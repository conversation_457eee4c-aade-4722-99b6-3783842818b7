#!/usr/bin/env node

/**
 * TempStick Tables Verification Script
 * Run this AFTER executing the manual SQL in Supabase SQL Editor
 * This script verifies all tables and views were created successfully
 */

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('   Please ensure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function verifyTables() {
  console.log('🌡️  TempStick Tables Verification');
  console.log('==================================\n');
  
  const tables = [
    { name: 'storage_areas', description: 'Physical storage locations' },
    { name: 'sensors', description: 'TempStick sensor devices' },
    { name: 'temperature_readings', description: 'Time-series temperature data' },
    { name: 'temperature_alerts', description: 'Temperature violation alerts' }
  ];
  
  const results = {};
  
  console.log('📋 Checking tables...\n');
  
  for (const table of tables) {
    try {
      console.log(`   Testing ${table.name}...`);
      
      // Test table access
      const { data, error } = await supabase
        .from(table.name)
        .select('*')
        .limit(1);
      
      if (error) {
        console.log(`   ❌ ${table.name}: ${error.message}`);
        results[table.name] = { exists: false, error: error.message };
      } else {
        console.log(`   ✅ ${table.name}: Accessible`);
        results[table.name] = { exists: true };
        
        // Get row count
        const { count, error: countError } = await supabase
          .from(table.name)
          .select('*', { count: 'exact', head: true });
        
        if (!countError) {
          console.log(`      Rows: ${count || 0}`);
          results[table.name].rowCount = count || 0;
        }
      }
    } catch (err) {
      console.log(`   ❌ ${table.name}: ${err.message}`);
      results[table.name] = { exists: false, error: err.message };
    }
  }
  
  return results;
}

async function verifyViews() {
  console.log('\n👁️  Checking dashboard views...\n');
  
  const views = [
    { name: 'sensor_status_dashboard', description: 'Real-time sensor status' },
    { name: 'haccp_compliance_dashboard', description: 'HACCP compliance summary' }
  ];
  
  const results = {};
  
  for (const view of views) {
    try {
      console.log(`   Testing ${view.name}...`);
      
      const { data, error } = await supabase
        .from(view.name)
        .select('*')
        .limit(1);
      
      if (error) {
        console.log(`   ❌ ${view.name}: ${error.message}`);
        results[view.name] = { exists: false, error: error.message };
      } else {
        console.log(`   ✅ ${view.name}: Accessible`);
        results[view.name] = { exists: true };
      }
    } catch (err) {
      console.log(`   ❌ ${view.name}: ${err.message}`);
      results[view.name] = { exists: false, error: err.message };
    }
  }
  
  return results;
}

async function testBasicOperations() {
  console.log('\n🧪 Testing basic CRUD operations...\n');
  
  try {
    // Test inserting a storage area (will need authentication)
    console.log('   Testing storage area creation...');
    const { data: storageData, error: storageError } = await supabase
      .from('storage_areas')
      .insert([
        {
          name: 'Test Walk-in Cooler',
          area_type: 'walk_in_cooler',
          temp_min_fahrenheit: 32,
          temp_max_fahrenheit: 40,
          haccp_required: true
        }
      ])
      .select();
    
    if (storageError) {
      console.log(`   ⚠️  Insert test: ${storageError.message}`);
      if (storageError.message.includes('RLS')) {
        console.log('      (This is expected - RLS policies require authentication)');
      }
    } else {
      console.log('   ✅ Storage area insert successful');
      
      // Clean up test data
      if (storageData && storageData[0]) {
        await supabase
          .from('storage_areas')
          .delete()
          .eq('id', storageData[0].id);
        console.log('   🧹 Test data cleaned up');
      }
    }
  } catch (err) {
    console.log(`   ❌ CRUD test error: ${err.message}`);
  }
}

async function checkIndexes() {
  console.log('\n📊 Checking database indexes...\n');
  
  try {
    // This query checks for some of our key indexes
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: `
        SELECT 
          schemaname, 
          tablename, 
          indexname 
        FROM pg_indexes 
        WHERE schemaname = 'public' 
        AND tablename IN ('storage_areas', 'sensors', 'temperature_readings', 'temperature_alerts')
        ORDER BY tablename, indexname;
      `
    });
    
    if (error) {
      console.log(`   ⚠️  Cannot check indexes: ${error.message}`);
    } else {
      console.log(`   ✅ Found ${data?.length || 0} indexes on TempStick tables`);
    }
  } catch (err) {
    console.log(`   ⚠️  Index check not available: ${err.message}`);
  }
}

async function generateSampleData() {
  console.log('\n📝 Sample data for testing...\n');
  
  console.log('Copy this SQL to create test data (replace USER_ID with your actual user ID):');
  console.log('================================================================================');
  console.log(`
-- Step 1: Create a test storage area
INSERT INTO storage_areas (user_id, name, area_type, temp_min_fahrenheit, temp_max_fahrenheit, haccp_required)
VALUES ('YOUR_USER_ID_HERE', 'Main Walk-in Cooler', 'walk_in_cooler', 32.0, 40.0, true);

-- Step 2: Create a test sensor (get storage_area_id from step 1)
INSERT INTO sensors (user_id, storage_area_id, sensor_id, device_name, name, is_online)
VALUES ('YOUR_USER_ID_HERE', 'STORAGE_AREA_ID_FROM_STEP_1', 'TEMP001', 'TempStick Pro', 'Cooler Sensor #1', true);

-- Step 3: Create sample temperature readings (get sensor_id from step 2)
INSERT INTO temperature_readings (user_id, sensor_id, storage_area_id, recorded_at, temp_celsius, temp_fahrenheit, within_safe_range)
VALUES 
  ('YOUR_USER_ID_HERE', 'SENSOR_ID_FROM_STEP_2', 'STORAGE_AREA_ID_FROM_STEP_1', NOW() - INTERVAL '1 hour', 3.0, 37.4, true),
  ('YOUR_USER_ID_HERE', 'SENSOR_ID_FROM_STEP_2', 'STORAGE_AREA_ID_FROM_STEP_1', NOW() - INTERVAL '30 minutes', 2.8, 37.0, true),
  ('YOUR_USER_ID_HERE', 'SENSOR_ID_FROM_STEP_2', 'STORAGE_AREA_ID_FROM_STEP_1', NOW(), 3.2, 37.8, true);

-- Step 4: Test the dashboard views
SELECT * FROM sensor_status_dashboard WHERE user_id = 'YOUR_USER_ID_HERE';
SELECT * FROM haccp_compliance_dashboard WHERE user_id = 'YOUR_USER_ID_HERE';
  `);
}

async function main() {
  const tableResults = await verifyTables();
  const viewResults = await verifyViews();
  await testBasicOperations();
  await checkIndexes();
  
  // Summary
  console.log('\n📊 Final Verification Summary:');
  console.log('==============================');
  
  const allTables = Object.keys(tableResults);
  const existingTables = allTables.filter(table => tableResults[table].exists);
  const missingTables = allTables.filter(table => !tableResults[table].exists);
  
  console.log(`✅ Tables created: ${existingTables.length}/${allTables.length}`);
  console.log(`❌ Tables missing: ${missingTables.length}/${allTables.length}`);
  
  if (existingTables.length > 0) {
    console.log(`\n✅ Successfully created tables:`);
    existingTables.forEach(table => {
      const count = tableResults[table].rowCount;
      console.log(`   - ${table}${count !== undefined ? ` (${count} rows)` : ''}`);
    });
  }
  
  if (missingTables.length > 0) {
    console.log(`\n❌ Missing tables:`);
    missingTables.forEach(table => {
      console.log(`   - ${table}: ${tableResults[table].error}`);
    });
  }
  
  const allViews = Object.keys(viewResults);
  const existingViews = allViews.filter(view => viewResults[view].exists);
  
  if (existingViews.length > 0) {
    console.log(`\n👁️  Dashboard views: ${existingViews.length}/${allViews.length} created`);
  }
  
  // Next steps
  console.log('\n🎯 Next Steps:');
  if (existingTables.length === allTables.length) {
    console.log('✅ All TempStick tables created successfully!');
    console.log('   1. Add sample data using the SQL above');
    console.log('   2. Test TempStick API integration');
    console.log('   3. Set up real-time temperature monitoring dashboard');
    console.log('   4. Configure temperature alerts and notifications');
    
    await generateSampleData();
  } else {
    console.log('⚠️  Some tables are still missing:');
    console.log('   1. Double-check the manual SQL was executed completely');
    console.log('   2. Check for any SQL execution errors in Supabase');
    console.log('   3. Verify your database permissions');
  }
  
  console.log('\n✅ Verification complete!');
}

main().catch(err => {
  console.error('❌ Verification failed:', err);
  process.exit(1);
});