#!/usr/bin/env node

/**
 * TempStick Migration Verification Script
 * Checks if TempStick tables exist and troubleshoots migration issues
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables:');
  console.error('   VITE_SUPABASE_URL:', !!supabaseUrl);
  console.error('   VITE_SUPABASE_SERVICE_ROLE_KEY:', !!process.env.VITE_SUPABASE_SERVICE_ROLE_KEY);
  console.error('   VITE_SUPABASE_ANON_KEY:', !!process.env.VITE_SUPABASE_ANON_KEY);
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkMigrationStatus() {
  console.log('🔍 Checking migration status...\n');
  
  try {
    // Check if schema_migrations table exists
    const { data: migrations, error: migrationError } = await supabase
      .from('schema_migrations')
      .select('*')
      .order('version', { ascending: false });
    
    if (migrationError) {
      console.log('⚠️  schema_migrations table not found or accessible');
      console.log('   This might be a remote database without migration tracking');
    } else {
      console.log('✅ Found schema_migrations table');
      console.log('📋 Applied migrations:');
      migrations?.forEach(m => {
        console.log(`   - ${m.version}`);
      });
      console.log();
    }
  } catch (err) {
    console.log('⚠️  Could not access schema_migrations:', err.message);
  }
}

async function checkTempStickTables() {
  console.log('🌡️  Checking TempStick tables...\n');
  
  const tables = ['storage_areas', 'sensors', 'temperature_readings', 'temperature_alerts'];
  const results = {};
  
  for (const table of tables) {
    try {
      console.log(`   Checking ${table}...`);
      
      // Try to query the table structure
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(0);
      
      if (error) {
        console.log(`   ❌ ${table}: ${error.message}`);
        results[table] = { exists: false, error: error.message };
      } else {
        console.log(`   ✅ ${table}: Table exists`);
        results[table] = { exists: true };
        
        // Get row count
        const { count } = await supabase
          .from(table)
          .select('*', { count: 'exact', head: true });
        
        console.log(`      Rows: ${count || 0}`);
        results[table].rowCount = count || 0;
      }
    } catch (err) {
      console.log(`   ❌ ${table}: ${err.message}`);
      results[table] = { exists: false, error: err.message };
    }
  }
  
  return results;
}

async function checkViews() {
  console.log('\n👁️  Checking TempStick views...\n');
  
  const views = ['sensor_status_dashboard', 'haccp_compliance_dashboard'];
  const results = {};
  
  for (const view of views) {
    try {
      console.log(`   Checking ${view}...`);
      
      const { data, error } = await supabase
        .from(view)
        .select('*')
        .limit(1);
      
      if (error) {
        console.log(`   ❌ ${view}: ${error.message}`);
        results[view] = { exists: false, error: error.message };
      } else {
        console.log(`   ✅ ${view}: View exists`);
        results[view] = { exists: true };
      }
    } catch (err) {
      console.log(`   ❌ ${view}: ${err.message}`);
      results[view] = { exists: false, error: err.message };
    }
  }
  
  return results;
}

async function runTempStickMigration() {
  console.log('\n🚀 Running TempStick migration manually...\n');
  
  try {
    // Read the migration file
    const migrationPath = path.join(__dirname, 'supabase/migrations/20250825_001_tempstick_sensor_integration_schema.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Split the SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`📄 Found ${statements.length} SQL statements to execute`);
    
    // Execute each statement
    let successCount = 0;
    let errorCount = 0;
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement.trim() === '') continue;
      
      try {
        console.log(`   Executing statement ${i + 1}/${statements.length}...`);
        
        const { error } = await supabase.rpc('exec_sql', { 
          sql: statement 
        });
        
        if (error) {
          console.log(`   ⚠️  Statement ${i + 1} warning: ${error.message}`);
          // Some errors are expected (like "already exists")
          if (error.message.includes('already exists')) {
            console.log('      (This is expected for existing objects)');
          } else {
            errorCount++;
          }
        } else {
          successCount++;
        }
      } catch (err) {
        console.log(`   ❌ Statement ${i + 1} error: ${err.message}`);
        errorCount++;
      }
    }
    
    console.log(`\n📊 Migration execution summary:`);
    console.log(`   ✅ Successful: ${successCount}`);
    console.log(`   ❌ Errors: ${errorCount}`);
    
    return { successCount, errorCount };
    
  } catch (err) {
    console.error('❌ Failed to run migration:', err.message);
    return { successCount: 0, errorCount: 1 };
  }
}

async function manualTableCreation() {
  console.log('\n🔧 Creating TempStick tables manually...\n');
  
  // Essential table creation SQL (simplified)
  const createStatements = [
    `CREATE TABLE IF NOT EXISTS storage_areas (
      id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
      user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
      name VARCHAR(255) NOT NULL,
      area_type VARCHAR(50) NOT NULL,
      temp_min_celsius DECIMAL(5,2),
      temp_max_celsius DECIMAL(5,2),
      created_at TIMESTAMPTZ DEFAULT NOW()
    )`,
    
    `CREATE TABLE IF NOT EXISTS sensors (
      id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
      user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
      storage_area_id UUID REFERENCES storage_areas(id) ON DELETE CASCADE,
      sensor_id VARCHAR(100) NOT NULL,
      device_name VARCHAR(255) NOT NULL,
      name VARCHAR(255) NOT NULL,
      is_online BOOLEAN DEFAULT true,
      created_at TIMESTAMPTZ DEFAULT NOW()
    )`,
    
    `CREATE TABLE IF NOT EXISTS temperature_readings (
      id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
      user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
      sensor_id UUID REFERENCES sensors(id) ON DELETE CASCADE NOT NULL,
      recorded_at TIMESTAMPTZ NOT NULL,
      temp_celsius DECIMAL(6,2) NOT NULL,
      temp_fahrenheit DECIMAL(6,2) NOT NULL,
      within_safe_range BOOLEAN NOT NULL DEFAULT true,
      created_at TIMESTAMPTZ DEFAULT NOW()
    )`,
    
    `CREATE TABLE IF NOT EXISTS temperature_alerts (
      id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
      user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
      sensor_id UUID REFERENCES sensors(id) ON DELETE CASCADE NOT NULL,
      alert_type VARCHAR(50) NOT NULL,
      severity VARCHAR(20) NOT NULL,
      title VARCHAR(255) NOT NULL,
      message TEXT NOT NULL,
      created_at TIMESTAMPTZ DEFAULT NOW()
    )`
  ];
  
  let successCount = 0;
  let errorCount = 0;
  
  for (let i = 0; i < createStatements.length; i++) {
    try {
      console.log(`   Creating table ${i + 1}/${createStatements.length}...`);
      
      const { error } = await supabase.rpc('exec_sql', { 
        sql: createStatements[i] 
      });
      
      if (error) {
        console.log(`   ⚠️  Error: ${error.message}`);
        if (error.message.includes('already exists')) {
          console.log('      (Table already exists)');
          successCount++;
        } else {
          errorCount++;
        }
      } else {
        console.log(`   ✅ Success`);
        successCount++;
      }
    } catch (err) {
      console.log(`   ❌ Error: ${err.message}`);
      errorCount++;
    }
  }
  
  return { successCount, errorCount };
}

async function main() {
  console.log('🌡️  TempStick Migration Verification Tool');
  console.log('==========================================\n');
  
  // Step 1: Check current migration status
  await checkMigrationStatus();
  
  // Step 2: Check if TempStick tables exist
  const tableResults = await checkTempStickTables();
  const missingTables = Object.entries(tableResults)
    .filter(([_, result]) => !result.exists)
    .map(([table, _]) => table);
  
  // Step 3: Check views
  const viewResults = await checkViews();
  
  // Step 4: If tables are missing, try to create them
  if (missingTables.length > 0) {
    console.log(`\n⚠️  Missing tables: ${missingTables.join(', ')}`);
    console.log('🔧 Attempting to create missing tables...\n');
    
    try {
      // Try running the full migration first
      const migrationResult = await runTempStickMigration();
      
      // If that fails, try manual table creation
      if (migrationResult.errorCount > 0) {
        console.log('\n🔄 Trying manual table creation...');
        await manualTableCreation();
      }
    } catch (err) {
      console.error('❌ Failed to create tables:', err.message);
      
      // Provide manual SQL for user to run
      console.log('\n📝 Manual SQL to run in Supabase SQL Editor:');
      console.log('========================================');
      console.log('-- Copy and paste this SQL into your Supabase SQL Editor:');
      console.log(`
CREATE TABLE IF NOT EXISTS storage_areas (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  name VARCHAR(255) NOT NULL,
  area_type VARCHAR(50) NOT NULL,
  temp_min_celsius DECIMAL(5,2),
  temp_max_celsius DECIMAL(5,2),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS sensors (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  storage_area_id UUID REFERENCES storage_areas(id) ON DELETE CASCADE,
  sensor_id VARCHAR(100) NOT NULL,
  device_name VARCHAR(255) NOT NULL,
  name VARCHAR(255) NOT NULL,
  is_online BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS temperature_readings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  sensor_id UUID REFERENCES sensors(id) ON DELETE CASCADE NOT NULL,
  recorded_at TIMESTAMPTZ NOT NULL,
  temp_celsius DECIMAL(6,2) NOT NULL,
  temp_fahrenheit DECIMAL(6,2) NOT NULL,
  within_safe_range BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS temperature_alerts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  sensor_id UUID REFERENCES sensors(id) ON DELETE CASCADE NOT NULL,
  alert_type VARCHAR(50) NOT NULL,
  severity VARCHAR(20) NOT NULL,
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
      `);
    }
    
    // Re-check tables after creation attempt
    console.log('\n🔄 Re-checking tables after creation...');
    await checkTempStickTables();
  } else {
    console.log('\n✅ All TempStick tables exist!');
  }
  
  // Step 5: Final summary
  console.log('\n📊 Final Status Summary:');
  console.log('========================');
  Object.entries(tableResults).forEach(([table, result]) => {
    const status = result.exists ? '✅' : '❌';
    const count = result.rowCount !== undefined ? ` (${result.rowCount} rows)` : '';
    console.log(`${status} ${table}${count}`);
  });
  
  console.log('\n🎯 Next Steps:');
  if (missingTables.length === 0) {
    console.log('✅ All tables exist! You can now:');
    console.log('   1. Test TempStick API integration');
    console.log('   2. Create sample temperature data');
    console.log('   3. Set up real-time temperature monitoring');
  } else {
    console.log('⚠️  Some tables are missing. Please:');
    console.log('   1. Run the manual SQL provided above in Supabase SQL Editor');
    console.log('   2. Check your database permissions');
    console.log('   3. Verify your Supabase service role key has admin access');
  }
  
  console.log('\n🔚 Migration check complete!');
}

// Handle uncaught errors
process.on('unhandledRejection', (err) => {
  console.error('❌ Unhandled error:', err);
  process.exit(1);
});

// Run the main function
main().catch(err => {
  console.error('❌ Script failed:', err);
  process.exit(1);
});