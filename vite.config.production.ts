import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  
  return {
    plugins: [
      react({
        babel: {
          plugins: [
            ['@babel/plugin-transform-runtime', {
              regenerator: false, // Reduce bundle size
              useESModules: true
            }]
          ]
        }
      })
    ],
    define: {
      'process.env': env,
      // Enable production optimizations
      __DEV__: false,
      'process.env.NODE_ENV': '"production"'
    },
    build: {
      target: 'es2020', // More compatible than esnext, better minification
      minify: 'esbuild',
      cssMinify: true,
      sourcemap: false,
      chunkSizeWarningLimit: 400, // Stricter limit to catch issues early
      assetsInlineLimit: 2048, // Reduce from 4KB to 2KB for better caching
      
      rollupOptions: {
        // External dependencies loaded via CDN (optional)
        // external: ['react', 'react-dom'],
        
        output: {
          // Advanced chunk splitting strategy
          manualChunks: (id) => {
            // Core React and router - highest priority
            if (id.includes('react') || id.includes('react-dom') || id.includes('react-router')) {
              return 'react-core';
            }
            
            // Voice processing - lazy loaded
            if (id.includes('openai') || 
                id.includes('react-speech-recognition') || 
                id.includes('regenerator-runtime') ||
                id.includes('/voice/') || 
                id.includes('voice-processor') ||
                id.includes('conversational-voice')) {
              return 'voice-processing';
            }
            
            // Heavy data processing - lazy loaded
            if (id.includes('papaparse') || id.includes('xlsx')) {
              return 'data-heavy';
            }
            
            // Charts and analytics - lazy loaded  
            if (id.includes('recharts')) {
              return 'charts';
            }
            
            // Supabase core
            if (id.includes('@supabase/supabase-js')) {
              return 'supabase-core';
            }
            
            // Supabase UI - lazy loaded
            if (id.includes('@supabase/auth-ui')) {
              return 'supabase-ui';
            }
            
            // Radix UI core components
            if (id.includes('@radix-ui/react-select') || 
                id.includes('@radix-ui/react-tabs') ||
                id.includes('@radix-ui/react-alert-dialog')) {
              return 'ui-core';
            }
            
            // Radix UI extended - lazy loaded
            if (id.includes('@radix-ui')) {
              return 'ui-extended';
            }
            
            // Form handling
            if (id.includes('react-hook-form') || 
                id.includes('@hookform/resolvers') || 
                id.includes('zod')) {
              return 'forms';
            }
            
            // Date utilities
            if (id.includes('date-fns')) {
              return 'date-utils';
            }
            
            // Icons and styling utilities  
            if (id.includes('lucide-react') || 
                id.includes('clsx') || 
                id.includes('tailwind-merge') ||
                id.includes('class-variance-authority')) {
              return 'ui-utils';
            }
            
            // HACCP and compliance - lazy loaded
            if (id.includes('/compliance/') || 
                id.includes('/haccp/') ||
                id.includes('monitoring/')) {
              return 'compliance';
            }
            
            // Import system - lazy loaded
            if (id.includes('/import/') || 
                id.includes('import-wizard') ||
                id.includes('import-sources')) {
              return 'import-system';
            }
            
            // Vendor management - lazy loaded
            if (id.includes('/vendors/') || 
                id.includes('vendor-api') ||
                id.includes('vendor-dashboard')) {
              return 'vendor-management';
            }
            
            // Analytics and reporting - lazy loaded
            if (id.includes('analytics') || 
                id.includes('monitoring') ||
                id.includes('performance')) {
              return 'analytics';
            }
            
            // All other vendor dependencies
            if (id.includes('node_modules')) {
              return 'vendor';
            }
          },
          
          // Optimized file naming
          chunkFileNames: (chunkInfo) => {
            // Use shorter names for better caching
            const facadeModuleId = chunkInfo.facadeModuleId ? 
              chunkInfo.facadeModuleId.split('/').pop() : 'chunk';
            return `assets/[name]-[hash:8].js`;
          },
          entryFileNames: 'assets/main-[hash:8].js',
          assetFileNames: (assetInfo) => {
            if (assetInfo.name && assetInfo.name.endsWith('.css')) {
              return 'assets/styles-[hash:8].css';
            }
            return 'assets/[name]-[hash:8].[ext]';
          }
        }
      },
      
      // Enhanced performance optimizations
      reportCompressedSize: true,
      
      // Aggressive tree shaking
      treeshake: {
        moduleSideEffects: false,
        propertyReadSideEffects: false,
        tryCatchDeoptimization: false,
        unknownGlobalSideEffects: false
      }
    },
    
    // Enhanced dependency optimization
    optimizeDeps: {
      include: [
        'react/jsx-runtime',
        'react',
        'react-dom',
        'react-dom/client',
        '@supabase/supabase-js',
        'clsx',
        'tailwind-merge'
      ],
      exclude: [
        '@vite/client', 
        '@vite/env',
        // Exclude heavy dependencies for lazy loading
        'openai',
        'papaparse',
        'xlsx',
        'recharts',
        'react-speech-recognition'
      ],
      esbuildOptions: {
        target: 'es2020',
        supported: {
          bigint: true,
          'top-level-await': true
        }
      }
    },
    
    // Enable experimental features
    esbuild: {
      legalComments: 'none', // Remove all comments
      minifyIdentifiers: true,
      minifySyntax: true,
      minifyWhitespace: true,
      treeShaking: true
    },
    
    server: {
      port: 5177,
      strictPort: true,
      host: true
    },
    
    preview: {
      port: 5177,
      strictPort: true,
      host: true
    }
  };
});