# Production Environment Configuration
NODE_ENV=production
VITE_APP_ENV=production

# Supabase Configuration (Production)
# These will be set in Vercel environment variables
VITE_SUPABASE_URL=https://your-project-ref.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key

# OpenAI Configuration (Production)
# This will be set in Vercel environment variables
VITE_OPENAI_API_KEY=your-openai-api-key

# Application Configuration
VITE_APP_NAME="Pacific Cloud Seafoods Manager"
VITE_APP_VERSION="1.0.0"
VITE_APP_DESCRIPTION="Enterprise Seafood Management System"

# Feature Flags
VITE_ENABLE_VOICE_FEATURES=true
VITE_ENABLE_ADVANCED_ANALYTICS=true
VITE_ENABLE_REAL_TIME_MONITORING=true
VITE_ENABLE_PERFORMANCE_TRACKING=true

# Security Configuration
VITE_ENABLE_CSP=true
VITE_ENABLE_SECURITY_HEADERS=true
VITE_ENABLE_RATE_LIMITING=true

# Performance Configuration
VITE_ENABLE_BUNDLE_ANALYSIS=true
VITE_ENABLE_LIGHTHOUSE_CI=true
VITE_PERFORMANCE_BUDGET_JS=1024
VITE_PERFORMANCE_BUDGET_CSS=50

# Monitoring Configuration
VITE_ENABLE_ERROR_TRACKING=true
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_ENABLE_USER_ANALYTICS=true

# Compliance Configuration
VITE_ENABLE_AUDIT_LOGGING=true
VITE_ENABLE_COMPLIANCE_TRACKING=true
VITE_ENABLE_GDPR_COMPLIANCE=true

# Cache Configuration
VITE_ENABLE_SERVICE_WORKER=true
VITE_CACHE_STRATEGY=cache-first
VITE_CACHE_VERSION=v1.0.0