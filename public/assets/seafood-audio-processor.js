/**
 * Advanced Audio Worklet Processor for Seafood Processing Environments
 * Optimized for noisy kitchens, freezers, and processing facilities
 */

class SeafoodAudioProcessor extends AudioWorkletProcessor {
  constructor(options) {
    super();
    
    this.options = options.processorOptions || {};
    this.targetLatency = this.options.targetLatency || 300;
    this.noiseReduction = this.options.noiseReduction || true;
    this.adaptiveGain = this.options.adaptiveGain || true;
    
    // Audio processing parameters
    this.sampleRate = 24000;
    this.bufferSize = 1024;
    this.hopSize = 512;
    
    // Voice Activity Detection
    this.vadThreshold = 0.01;
    this.vadHangover = 10; // frames to continue after voice detected
    this.vadCounter = 0;
    this.isVoiceActive = false;
    this.silenceFrames = 0;
    
    // Noise reduction parameters
    this.noiseFloor = 0.001;
    this.noiseGate = 0.005;
    this.noiseProfile = new Float32Array(this.bufferSize / 2);
    this.smoothingFactor = 0.95;
    
    // Adaptive gain control
    this.targetRMS = 0.1;
    this.currentGain = 1.0;
    this.gainSmoothingFactor = 0.999;
    this.maxGain = 8.0;
    this.minGain = 0.1;
    
    // Frequency domain processing
    this.window = this.createHannWindow(this.bufferSize);
    this.overlapBuffer = new Float32Array(this.bufferSize);
    this.frameBuffer = new Float32Array(this.bufferSize);
    this.outputBuffer = new Float32Array(this.bufferSize);
    
    // Initialize noise profile with kitchen/processing environment characteristics
    this.initializeSeafoodNoiseProfile();
    
    // Performance monitoring
    this.processedFrames = 0;
    this.lastLatencyCheck = 0;
    this.averageLatency = 0;
    
    console.log('🎤 Seafood Audio Processor initialized with optimizations for processing environments');
  }
  
  /**
   * Main audio processing loop
   */
  process(inputs, outputs, parameters) {
    const startTime = performance.now();
    
    const input = inputs[0];
    const output = outputs[0];
    
    if (!input || !input[0] || input[0].length === 0) {
      return true;
    }
    
    const inputChannel = input[0];
    const outputChannel = output[0];
    
    // Process audio frame
    const processedAudio = this.processAudioFrame(inputChannel);
    
    // Copy processed audio to output
    outputChannel.set(processedAudio);
    
    // Voice Activity Detection
    const voiceActivity = this.detectVoiceActivity(processedAudio);
    
    // Send processed audio and metadata to main thread
    if (voiceActivity) {
      this.port.postMessage({
        audioData: processedAudio,
        timestamp: currentTime,
        voiceActivity: true,
        rms: this.calculateRMS(processedAudio),
        gain: this.currentGain,
        latency: performance.now() - startTime
      });
    }
    
    // Update performance metrics
    this.updatePerformanceMetrics(performance.now() - startTime);
    
    return true;
  }
  
  /**
   * Process individual audio frame with seafood environment optimizations
   */
  processAudioFrame(inputData) {
    // Copy input to frame buffer
    this.frameBuffer.set(inputData);
    
    // Apply adaptive gain control first
    if (this.adaptiveGain) {
      this.applyAdaptiveGain(this.frameBuffer);
    }
    
    // Apply noise reduction for seafood processing environments
    if (this.noiseReduction) {
      this.applySeafoodNoiseReduction(this.frameBuffer);
    }
    
    // Apply window function and overlap-add for smooth processing
    this.applyWindowing(this.frameBuffer);
    
    return this.frameBuffer;
  }
  
  /**
   * Adaptive gain control optimized for varying seafood facility acoustics
   */
  applyAdaptiveGain(buffer) {
    const rms = this.calculateRMS(buffer);
    
    if (rms > 0) {
      // Calculate desired gain based on target RMS
      const desiredGain = this.targetRMS / rms;
      
      // Smooth gain changes to avoid artifacts
      const targetGain = Math.max(this.minGain, Math.min(this.maxGain, desiredGain));
      this.currentGain = (this.gainSmoothingFactor * this.currentGain) + 
                        ((1 - this.gainSmoothingFactor) * targetGain);
      
      // Apply gain to buffer
      for (let i = 0; i < buffer.length; i++) {
        buffer[i] *= this.currentGain;
      }
    }
  }
  
  /**
   * Advanced noise reduction specifically tuned for seafood processing environments
   */
  applySeafoodNoiseReduction(buffer) {
    // Identify and reduce common seafood facility noises:
    // - Ice machine operation (150-800 Hz)
    // - Refrigeration compressors (50-200 Hz)
    // - Water running/splashing (200-4000 Hz broadband)
    // - Equipment humming (60 Hz, 120 Hz, 180 Hz harmonics)
    // - Ventilation systems (100-500 Hz)
    
    // Apply spectral subtraction for noise reduction
    const spectrum = this.fft(buffer);
    
    // Update noise profile during silent periods
    if (!this.isVoiceActive) {
      this.updateNoiseProfile(spectrum);
    }
    
    // Apply noise reduction in frequency domain
    for (let i = 0; i < spectrum.length / 2; i++) {
      const magnitude = Math.sqrt(spectrum[i * 2] ** 2 + spectrum[i * 2 + 1] ** 2);
      const noiseMagnitude = this.noiseProfile[i];
      
      // Calculate spectral subtraction factor
      let reductionFactor = 1.0;
      if (magnitude > noiseMagnitude) {
        // More aggressive reduction for known seafood facility noise frequencies
        const frequency = (i * this.sampleRate) / this.bufferSize;
        const seafoodNoiseBoost = this.getSeafoodNoiseReduction(frequency);
        reductionFactor = Math.max(0.1, 1.0 - (noiseMagnitude / magnitude) * seafoodNoiseBoost);
      } else {
        reductionFactor = 0.1; // Heavy reduction for noise-dominated bins
      }
      
      // Apply reduction
      spectrum[i * 2] *= reductionFactor;
      spectrum[i * 2 + 1] *= reductionFactor;
    }
    
    // Convert back to time domain
    const cleanedBuffer = this.ifft(spectrum);
    buffer.set(cleanedBuffer);
  }
  
  /**
   * Get frequency-specific noise reduction for seafood environments
   */
  getSeafoodNoiseReduction(frequency) {
    // More aggressive reduction for common seafood facility noise frequencies
    if (frequency >= 50 && frequency <= 200) return 2.5;   // Compressor noise
    if (frequency >= 100 && frequency <= 500) return 2.0;  // Ventilation
    if (frequency >= 150 && frequency <= 800) return 2.5;  // Ice machines
    if (Math.abs(frequency - 60) < 5 || Math.abs(frequency - 120) < 5) return 3.0; // Electrical hum
    if (frequency >= 200 && frequency <= 4000) return 1.5; // Water/splashing
    return 1.0; // Standard reduction for other frequencies
  }
  
  /**
   * Voice Activity Detection optimized for seafood processing speech patterns
   */
  detectVoiceActivity(buffer) {
    const rms = this.calculateRMS(buffer);
    const spectralCentroid = this.calculateSpectralCentroid(buffer);
    const zeroCrossingRate = this.calculateZeroCrossingRate(buffer);
    
    // Voice activity decision based on multiple features
    const rmsThreshold = Math.max(this.vadThreshold, this.noiseFloor * 3);
    const isLoudEnough = rms > rmsThreshold;
    
    // Spectral centroid typically higher for voice (500-3000 Hz)
    const isVoiceSpectrum = spectralCentroid > 500 && spectralCentroid < 3000;
    
    // Zero crossing rate indicates voiced vs unvoiced speech
    const isVoiceZCR = zeroCrossingRate > 0.02 && zeroCrossingRate < 0.3;
    
    const currentlyActive = isLoudEnough && (isVoiceSpectrum || isVoiceZCR);
    
    // Apply hangover logic to avoid chopping speech
    if (currentlyActive) {
      this.vadCounter = this.vadHangover;
      this.isVoiceActive = true;
      this.silenceFrames = 0;
    } else {
      this.vadCounter = Math.max(0, this.vadCounter - 1);
      this.isVoiceActive = this.vadCounter > 0;
      this.silenceFrames++;
    }
    
    return this.isVoiceActive;
  }
  
  /**
   * Initialize noise profile for typical seafood processing environments
   */
  initializeSeafoodNoiseProfile() {
    // Pre-populate with typical seafood facility noise spectrum
    for (let i = 0; i < this.noiseProfile.length; i++) {
      const frequency = (i * this.sampleRate) / this.bufferSize;
      
      // Typical noise levels for different frequency ranges in seafood facilities
      if (frequency < 100) {
        this.noiseProfile[i] = 0.01; // Low frequency equipment noise
      } else if (frequency < 500) {
        this.noiseProfile[i] = 0.008; // Ventilation and compressors
      } else if (frequency < 2000) {
        this.noiseProfile[i] = 0.005; // Water and processing sounds
      } else {
        this.noiseProfile[i] = 0.003; // Higher frequency background
      }
    }
  }
  
  /**
   * Update noise profile during silent periods
   */
  updateNoiseProfile(spectrum) {
    for (let i = 0; i < this.noiseProfile.length; i++) {
      const magnitude = Math.sqrt(spectrum[i * 2] ** 2 + spectrum[i * 2 + 1] ** 2);
      this.noiseProfile[i] = (this.smoothingFactor * this.noiseProfile[i]) + 
                            ((1 - this.smoothingFactor) * magnitude);
    }
  }
  
  /**
   * Utility functions
   */
  calculateRMS(buffer) {
    let sum = 0;
    for (let i = 0; i < buffer.length; i++) {
      sum += buffer[i] * buffer[i];
    }
    return Math.sqrt(sum / buffer.length);
  }
  
  calculateSpectralCentroid(buffer) {
    const spectrum = this.fft(buffer);
    let numerator = 0;
    let denominator = 0;
    
    for (let i = 1; i < spectrum.length / 2; i++) {
      const magnitude = Math.sqrt(spectrum[i * 2] ** 2 + spectrum[i * 2 + 1] ** 2);
      const frequency = (i * this.sampleRate) / this.bufferSize;
      numerator += frequency * magnitude;
      denominator += magnitude;
    }
    
    return denominator > 0 ? numerator / denominator : 0;
  }
  
  calculateZeroCrossingRate(buffer) {
    let crossings = 0;
    for (let i = 1; i < buffer.length; i++) {
      if ((buffer[i] >= 0) !== (buffer[i - 1] >= 0)) {
        crossings++;
      }
    }
    return crossings / (buffer.length - 1);
  }
  
  createHannWindow(size) {
    const window = new Float32Array(size);
    for (let i = 0; i < size; i++) {
      window[i] = 0.5 * (1 - Math.cos(2 * Math.PI * i / (size - 1)));
    }
    return window;
  }
  
  applyWindowing(buffer) {
    for (let i = 0; i < buffer.length; i++) {
      buffer[i] *= this.window[i];
    }
  }
  
  updatePerformanceMetrics(latency) {
    this.processedFrames++;
    this.averageLatency = (this.averageLatency * 0.9) + (latency * 0.1);
    
    // Report performance every 1000 frames
    if (this.processedFrames % 1000 === 0) {
      this.port.postMessage({
        type: 'performance',
        averageLatency: this.averageLatency,
        processedFrames: this.processedFrames,
        currentGain: this.currentGain
      });
    }
  }
  
  /**
   * Simple FFT implementation for spectral processing
   * Note: In production, consider using a more optimized FFT library
   */
  fft(buffer) {
    const N = buffer.length;
    const output = new Float32Array(N * 2);
    
    // Copy real parts
    for (let i = 0; i < N; i++) {
      output[i * 2] = buffer[i];
      output[i * 2 + 1] = 0;
    }
    
    // Simple DFT (for demonstration - would use optimized FFT in production)
    const result = new Float32Array(N * 2);
    for (let k = 0; k < N; k++) {
      let realSum = 0, imagSum = 0;
      for (let n = 0; n < N; n++) {
        const angle = -2 * Math.PI * k * n / N;
        realSum += output[n * 2] * Math.cos(angle) - output[n * 2 + 1] * Math.sin(angle);
        imagSum += output[n * 2] * Math.sin(angle) + output[n * 2 + 1] * Math.cos(angle);
      }
      result[k * 2] = realSum;
      result[k * 2 + 1] = imagSum;
    }
    
    return result;
  }
  
  /**
   * Simple IFFT implementation
   */
  ifft(spectrum) {
    const N = spectrum.length / 2;
    const output = new Float32Array(N);
    
    // Simple inverse DFT
    for (let n = 0; n < N; n++) {
      let realSum = 0;
      for (let k = 0; k < N; k++) {
        const angle = 2 * Math.PI * k * n / N;
        realSum += spectrum[k * 2] * Math.cos(angle) - spectrum[k * 2 + 1] * Math.sin(angle);
      }
      output[n] = realSum / N;
    }
    
    return output;
  }
}

registerProcessor('seafood-audio-processor', SeafoodAudioProcessor);