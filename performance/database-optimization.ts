// Database Query Optimization for Seafood Manager
// Advanced query optimization and performance monitoring

export interface QueryMetrics {
  query: string;
  executionTime: number;
  rowsReturned: number;
  timestamp: number;
  userId?: string;
  cacheHit: boolean;
  indexUsed: boolean;
}

export interface OptimizationRule {
  name: string;
  pattern: RegExp;
  suggestion: string;
  optimization: (query: string) => string;
  priority: 'low' | 'medium' | 'high' | 'critical';
}

export class DatabaseOptimizer {
  private static instance: DatabaseOptimizer;
  private queryMetrics: QueryMetrics[] = [];
  private slowQueryThreshold = 1000; // 1 second
  private optimizationRules: OptimizationRule[] = [];

  static getInstance(): DatabaseOptimizer {
    if (!this.instance) {
      this.instance = new DatabaseOptimizer();
    }
    return this.instance;
  }

  constructor() {
    this.initializeOptimizationRules();
  }

  // Track query performance
  trackQuery(
    query: string,
    executionTime: number,
    rowsReturned: number,
    options: {
      userId?: string;
      cacheHit?: boolean;
      indexUsed?: boolean;
    } = {}
  ): void {
    const metric: QueryMetrics = {
      query: this.sanitizeQuery(query),
      executionTime,
      rowsReturned,
      timestamp: Date.now(),
      userId: options.userId,
      cacheHit: options.cacheHit || false,
      indexUsed: options.indexUsed || false
    };

    this.queryMetrics.push(metric);

    // Keep only last 1000 queries
    if (this.queryMetrics.length > 1000) {
      this.queryMetrics = this.queryMetrics.slice(-1000);
    }

    // Alert on slow queries
    if (executionTime > this.slowQueryThreshold) {
      this.handleSlowQuery(metric);
    }

    // Send metrics to monitoring
    this.sendMetricsToMonitoring(metric);
  }

  // Optimize Supabase queries
  optimizeSupabaseQuery(
    tableName: string,
    queryBuilder: any,
    options: {
      selectColumns?: string[];
      filterColumns?: string[];
      orderBy?: string;
      limit?: number;
      offset?: number;
      useIndex?: string;
    } = {}
  ): any {
    let optimizedQuery = queryBuilder;

    // Apply column selection optimization
    if (options.selectColumns && options.selectColumns.length > 0) {
      // Only select necessary columns
      optimizedQuery = optimizedQuery.select(options.selectColumns.join(', '));
    }

    // Apply filtering optimization
    if (options.filterColumns) {
      // Ensure filters use indexed columns first
      console.log(`Optimizing filters for indexed columns: ${options.filterColumns.join(', ')}`);
    }

    // Apply ordering optimization
    if (options.orderBy) {
      // Recommend composite indexes for complex ordering
      optimizedQuery = optimizedQuery.order(options.orderBy);
    }

    // Apply pagination optimization
    if (options.limit) {
      optimizedQuery = optimizedQuery.limit(options.limit);
      
      if (options.offset) {
        // Recommend cursor-based pagination for large offsets
        if (options.offset > 1000) {
          console.warn(`Large offset detected (${options.offset}). Consider cursor-based pagination.`);
        }
        optimizedQuery = optimizedQuery.range(options.offset, options.offset + options.limit - 1);
      }
    }

    return optimizedQuery;
  }

  // Seafood-specific query optimizations
  getOptimizedInventoryQuery(
    supabase: any,
    filters: {
      dateRange?: { start: string; end: string };
      productIds?: string[];
      eventTypes?: string[];
      userId?: string;
    } = {}
  ): any {
    let query = supabase
      .from('inventory_events')
      .select(`
        id,
        event_type,
        product_id,
        quantity,
        unit,
        batch_number,
        created_at,
        Products(id, name, category, unit_type)
      `);

    // Apply date range filter (indexed)
    if (filters.dateRange) {
      query = query
        .gte('created_at', filters.dateRange.start)
        .lte('created_at', filters.dateRange.end);
    }

    // Apply product filter (indexed)
    if (filters.productIds && filters.productIds.length > 0) {
      if (filters.productIds.length === 1) {
        query = query.eq('product_id', filters.productIds[0]);
      } else {
        query = query.in('product_id', filters.productIds);
      }
    }

    // Apply event type filter
    if (filters.eventTypes && filters.eventTypes.length > 0) {
      query = query.in('event_type', filters.eventTypes);
    }

    // Apply user filter if provided
    if (filters.userId) {
      query = query.eq('user_id', filters.userId);
    }

    // Order by created_at DESC (indexed) for recent events first
    query = query.order('created_at', { ascending: false });

    return query;
  }

  getOptimizedProductQuery(
    supabase: any,
    filters: {
      categories?: string[];
      active?: boolean;
      search?: string;
    } = {}
  ): any {
    let query = supabase
      .from('Products')
      .select(`
        id,
        name,
        category,
        unit_type,
        price,
        active,
        created_at
      `);

    // Apply active filter first (most selective)
    if (filters.active !== undefined) {
      query = query.eq('active', filters.active);
    }

    // Apply category filter (indexed)
    if (filters.categories && filters.categories.length > 0) {
      query = query.in('category', filters.categories);
    }

    // Apply text search (use full-text search if available)
    if (filters.search) {
      // For PostgreSQL full-text search
      query = query.or(`name.ilike.%${filters.search}%,category.ilike.%${filters.search}%`);
    }

    // Order by name for consistent results
    query = query.order('name', { ascending: true });

    return query;
  }

  getOptimizedHACCPQuery(
    supabase: any,
    filters: {
      dateRange?: { start: string; end: string };
      eventTypes?: string[];
      criticalOnly?: boolean;
    } = {}
  ): any {
    let query = supabase
      .from('haccp_events')
      .select(`
        id,
        event_type,
        temperature,
        location,
        product_id,
        compliance_status,
        created_at,
        Products(name, category)
      `);

    // Apply date range filter (indexed)
    if (filters.dateRange) {
      query = query
        .gte('created_at', filters.dateRange.start)
        .lte('created_at', filters.dateRange.end);
    }

    // Apply critical events filter
    if (filters.criticalOnly) {
      query = query.eq('compliance_status', 'non_compliant');
    }

    // Apply event type filter
    if (filters.eventTypes && filters.eventTypes.length > 0) {
      query = query.in('event_type', filters.eventTypes);
    }

    // Order by created_at DESC for recent events first
    query = query.order('created_at', { ascending: false });

    return query;
  }

  // Voice-specific optimizations
  getOptimizedVoiceQuery(
    supabase: any,
    filters: {
      dateRange?: { start: string; end: string };
      userId?: string;
      status?: string[];
    } = {}
  ): any {
    let query = supabase
      .from('voice_events')
      .select(`
        id,
        transcript,
        processed_data,
        confidence_score,
        status,
        created_at,
        user_id
      `);

    // Apply user filter first (most selective for voice data)
    if (filters.userId) {
      query = query.eq('user_id', filters.userId);
    }

    // Apply date range filter
    if (filters.dateRange) {
      query = query
        .gte('created_at', filters.dateRange.start)
        .lte('created_at', filters.dateRange.end);
    }

    // Apply status filter
    if (filters.status && filters.status.length > 0) {
      query = query.in('status', filters.status);
    }

    // Order by created_at DESC
    query = query.order('created_at', { ascending: false });

    return query;
  }

  // Query analysis and recommendations
  analyzeQueryPerformance(): {
    slowQueries: QueryMetrics[];
    recommendations: string[];
    averageExecutionTime: number;
    cacheHitRate: number;
    indexUsageRate: number;
  } {
    const slowQueries = this.queryMetrics.filter(q => q.executionTime > this.slowQueryThreshold);
    
    const totalQueries = this.queryMetrics.length;
    const totalExecutionTime = this.queryMetrics.reduce((sum, q) => sum + q.executionTime, 0);
    const cacheHits = this.queryMetrics.filter(q => q.cacheHit).length;
    const indexUsage = this.queryMetrics.filter(q => q.indexUsed).length;

    const recommendations = this.generateRecommendations();

    return {
      slowQueries: slowQueries.slice(-10), // Last 10 slow queries
      recommendations,
      averageExecutionTime: totalQueries > 0 ? totalExecutionTime / totalQueries : 0,
      cacheHitRate: totalQueries > 0 ? (cacheHits / totalQueries) * 100 : 0,
      indexUsageRate: totalQueries > 0 ? (indexUsage / totalQueries) * 100 : 0
    };
  }

  // Generate optimization recommendations
  private generateRecommendations(): string[] {
    const recommendations: string[] = [];
    const analysis = this.analyzePatterns();

    // Check for missing indexes
    if (analysis.nonIndexedQueries > analysis.indexedQueries * 0.5) {
      recommendations.push('Consider adding indexes for frequently queried columns');
    }

    // Check cache utilization
    if (analysis.cacheHitRate < 50) {
      recommendations.push('Low cache hit rate - review caching strategy');
    }

    // Check for N+1 query problems
    if (analysis.potentialNPlusOne > 0) {
      recommendations.push('Potential N+1 query pattern detected - use joins or includes');
    }

    // Check for large result sets
    if (analysis.largeResultSets > 0) {
      recommendations.push('Large result sets detected - implement pagination');
    }

    // Check for frequent full table scans
    if (analysis.fullTableScans > 0) {
      recommendations.push('Full table scans detected - add appropriate WHERE clauses');
    }

    return recommendations;
  }

  private analyzePatterns(): {
    nonIndexedQueries: number;
    indexedQueries: number;
    cacheHitRate: number;
    potentialNPlusOne: number;
    largeResultSets: number;
    fullTableScans: number;
  } {
    const patterns = {
      nonIndexedQueries: 0,
      indexedQueries: 0,
      cacheHitRate: 0,
      potentialNPlusOne: 0,
      largeResultSets: 0,
      fullTableScans: 0
    };

    this.queryMetrics.forEach(metric => {
      if (metric.indexUsed) {
        patterns.indexedQueries++;
      } else {
        patterns.nonIndexedQueries++;
      }

      if (metric.rowsReturned > 1000) {
        patterns.largeResultSets++;
      }

      // Simple heuristics for pattern detection
      if (metric.query.includes('SELECT *')) {
        patterns.fullTableScans++;
      }

      // Detect potential N+1 patterns (multiple similar queries in short time)
      const similarQueries = this.queryMetrics.filter(q => 
        q.query === metric.query && 
        Math.abs(q.timestamp - metric.timestamp) < 1000
      );
      
      if (similarQueries.length > 5) {
        patterns.potentialNPlusOne++;
      }
    });

    const totalQueries = this.queryMetrics.length;
    const cacheHits = this.queryMetrics.filter(q => q.cacheHit).length;
    patterns.cacheHitRate = totalQueries > 0 ? (cacheHits / totalQueries) * 100 : 0;

    return patterns;
  }

  // Initialize optimization rules
  private initializeOptimizationRules(): void {
    this.optimizationRules = [
      {
        name: 'Select All Columns',
        pattern: /SELECT \*/gi,
        suggestion: 'Specify only needed columns instead of SELECT *',
        optimization: (query) => query.replace(/SELECT \*/gi, 'SELECT id, name'), // Example
        priority: 'medium'
      },
      {
        name: 'Missing WHERE Clause',
        pattern: /FROM \w+ (?!WHERE)/gi,
        suggestion: 'Add WHERE clause to filter results',
        optimization: (query) => query,
        priority: 'high'
      },
      {
        name: 'No LIMIT Clause',
        pattern: /SELECT .* FROM .* (?!LIMIT)/gi,
        suggestion: 'Add LIMIT clause to prevent large result sets',
        optimization: (query) => query + ' LIMIT 100',
        priority: 'medium'
      },
      {
        name: 'ORDER BY without Index',
        pattern: /ORDER BY \w+/gi,
        suggestion: 'Ensure ORDER BY columns are indexed',
        optimization: (query) => query,
        priority: 'high'
      }
    ];
  }

  // Handle slow queries
  private handleSlowQuery(metric: QueryMetrics): void {
    console.warn('Slow query detected:', {
      query: metric.query,
      executionTime: metric.executionTime,
      rowsReturned: metric.rowsReturned
    });

    // Send alert for very slow queries
    if (metric.executionTime > 5000) { // 5 seconds
      this.sendSlowQueryAlert(metric);
    }

    // Analyze and suggest optimizations
    const suggestions = this.optimizationRules
      .filter(rule => rule.pattern.test(metric.query))
      .map(rule => rule.suggestion);

    if (suggestions.length > 0) {
      console.log('Optimization suggestions:', suggestions);
    }
  }

  // Send metrics to monitoring services
  private sendMetricsToMonitoring(metric: QueryMetrics): void {
    // Send to DataDog
    if (typeof window !== 'undefined' && window.DD_RUM) {
      window.DD_RUM.addTiming(`database.query.${this.getTableFromQuery(metric.query)}`, metric.executionTime);
      window.DD_RUM.addAttribute('query.rows_returned', metric.rowsReturned);
      window.DD_RUM.addAttribute('query.cache_hit', metric.cacheHit);
      window.DD_RUM.addAttribute('query.index_used', metric.indexUsed);
    }

    // Send to custom monitoring
    if (process.env.VITE_DATABASE_METRICS_ENDPOINT) {
      fetch(process.env.VITE_DATABASE_METRICS_ENDPOINT, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(metric)
      }).catch(error => {
        console.error('Failed to send database metrics:', error);
      });
    }
  }

  // Alert on slow queries
  private sendSlowQueryAlert(metric: QueryMetrics): void {
    if (typeof window !== 'undefined' && window.Sentry) {
      window.Sentry.captureMessage('Slow database query detected', {
        level: 'warning',
        tags: { slow_query: true },
        extra: metric
      });
    }

    // Send to Slack/Teams if configured
    if (process.env.VITE_SLOW_QUERY_WEBHOOK) {
      fetch(process.env.VITE_SLOW_QUERY_WEBHOOK, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          text: `⚠️ Slow Query Alert`,
          attachments: [{
            color: 'warning',
            title: 'Database Performance Issue',
            fields: [
              { title: 'Execution Time', value: `${metric.executionTime}ms`, short: true },
              { title: 'Rows Returned', value: metric.rowsReturned.toString(), short: true },
              { title: 'Query', value: metric.query.substring(0, 200), short: false }
            ]
          }]
        })
      }).catch(error => {
        console.error('Failed to send slow query alert:', error);
      });
    }
  }

  // Utility methods
  private sanitizeQuery(query: string): string {
    // Remove sensitive data from query for logging
    return query
      .replace(/password\s*=\s*'[^']*'/gi, "password='***'")
      .replace(/token\s*=\s*'[^']*'/gi, "token='***'")
      .replace(/secret\s*=\s*'[^']*'/gi, "secret='***'");
  }

  private getTableFromQuery(query: string): string {
    const match = query.match(/FROM\s+(\w+)/i);
    return match ? match[1] : 'unknown';
  }

  // Get performance statistics
  getPerformanceStats(): {
    totalQueries: number;
    slowQueries: number;
    averageExecutionTime: number;
    cacheHitRate: number;
    indexUsageRate: number;
    mostQueriedTables: Array<{ table: string; count: number }>;
  } {
    const analysis = this.analyzeQueryPerformance();
    
    // Count queries by table
    const tableQueries = new Map<string, number>();
    this.queryMetrics.forEach(metric => {
      const table = this.getTableFromQuery(metric.query);
      tableQueries.set(table, (tableQueries.get(table) || 0) + 1);
    });

    const mostQueriedTables = Array.from(tableQueries.entries())
      .map(([table, count]) => ({ table, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    return {
      totalQueries: this.queryMetrics.length,
      slowQueries: analysis.slowQueries.length,
      averageExecutionTime: analysis.averageExecutionTime,
      cacheHitRate: analysis.cacheHitRate,
      indexUsageRate: analysis.indexUsageRate,
      mostQueriedTables
    };
  }

  // Clear metrics
  clearMetrics(): void {
    this.queryMetrics = [];
  }
}

// Global database optimizer instance
export const dbOptimizer = DatabaseOptimizer.getInstance();

// Supabase query wrapper with automatic optimization
export async function optimizedSupabaseQuery<T>(
  queryBuilder: any,
  queryName: string,
  options: {
    userId?: string;
    enableCache?: boolean;
    cacheKey?: string;
    expectedRowCount?: number;
  } = {}
): Promise<T> {
  const startTime = performance.now();
  
  try {
    // Execute query
    const { data, error } = await queryBuilder;
    const executionTime = performance.now() - startTime;
    
    if (error) {
      throw new Error(error.message);
    }

    // Track query performance
    dbOptimizer.trackQuery(
      queryName,
      executionTime,
      data?.length || 0,
      {
        userId: options.userId,
        cacheHit: false, // Would be determined by cache layer
        indexUsed: true // Would be determined by query plan analysis
      }
    );

    return data;
  } catch (error) {
    const executionTime = performance.now() - startTime;
    
    // Track failed queries
    dbOptimizer.trackQuery(
      queryName,
      executionTime,
      0,
      { userId: options.userId, cacheHit: false, indexUsed: false }
    );
    
    throw error;
  }
}

// Helper function to create optimized Supabase queries
export function createOptimizedQuery(
  supabase: any,
  tableName: string,
  queryType: 'inventory' | 'product' | 'haccp' | 'voice',
  filters: any = {}
): any {
  const optimizer = DatabaseOptimizer.getInstance();
  
  switch (queryType) {
    case 'inventory':
      return optimizer.getOptimizedInventoryQuery(supabase, filters);
    case 'product':
      return optimizer.getOptimizedProductQuery(supabase, filters);
    case 'haccp':
      return optimizer.getOptimizedHACCPQuery(supabase, filters);
    case 'voice':
      return optimizer.getOptimizedVoiceQuery(supabase, filters);
    default:
      return supabase.from(tableName);
  }
}