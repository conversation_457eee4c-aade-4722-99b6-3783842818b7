// Advanced Caching Strategies for Seafood Manager
// Multi-layer caching system for optimal performance

export interface CacheConfig {
  ttl: number; // Time to live in milliseconds
  maxSize: number; // Maximum cache size
  strategy: 'lru' | 'lfu' | 'fifo' | 'ttl';
  persistToLocalStorage?: boolean;
  compressionEnabled?: boolean;
}

export interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  size: number;
  compressed?: boolean;
}

export class MultiLayerCache {
  private memoryCache = new Map<string, CacheItem<any>>();
  private localStoragePrefix = 'sm_cache_';
  private config: CacheConfig;
  private currentSize = 0;

  constructor(config: CacheConfig) {
    this.config = config;
    this.initCleanupTimer();
  }

  // Get item from cache with fallback chain
  async get<T>(key: string, fallbackFn?: () => Promise<T>): Promise<T | null> {
    // Try memory cache first (fastest)
    const memoryItem = this.getFromMemory<T>(key);
    if (memoryItem !== null) {
      return memoryItem;
    }

    // Try localStorage cache (medium speed)
    if (this.config.persistToLocalStorage) {
      const localStorageItem = await this.getFromLocalStorage<T>(key);
      if (localStorageItem !== null) {
        // Promote to memory cache
        this.setInMemory(key, localStorageItem, this.config.ttl);
        return localStorageItem;
      }
    }

    // Fallback to data source (slowest)
    if (fallbackFn) {
      try {
        const data = await fallbackFn();
        if (data !== null && data !== undefined) {
          await this.set(key, data, this.config.ttl);
        }
        return data;
      } catch (error) {
        console.error('Fallback function failed:', error);
        return null;
      }
    }

    return null;
  }

  // Set item in cache
  async set<T>(key: string, data: T, ttl?: number): Promise<void> {
    const effectiveTtl = ttl || this.config.ttl;
    
    // Set in memory cache
    this.setInMemory(key, data, effectiveTtl);

    // Set in localStorage if enabled
    if (this.config.persistToLocalStorage) {
      await this.setInLocalStorage(key, data, effectiveTtl);
    }
  }

  // Memory cache operations
  private getFromMemory<T>(key: string): T | null {
    const item = this.memoryCache.get(key);
    if (!item) return null;

    // Check TTL
    if (Date.now() > item.timestamp + item.ttl) {
      this.memoryCache.delete(key);
      this.currentSize -= item.size;
      return null;
    }

    // Update access count for LFU strategy
    item.accessCount++;
    
    return item.data;
  }

  private setInMemory<T>(key: string, data: T, ttl: number): void {
    const size = this.calculateSize(data);
    
    // Check if we need to evict items
    while (this.currentSize + size > this.config.maxSize && this.memoryCache.size > 0) {
      this.evictItem();
    }

    const item: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      ttl,
      accessCount: 1,
      size
    };

    // Remove existing item if present
    const existing = this.memoryCache.get(key);
    if (existing) {
      this.currentSize -= existing.size;
    }

    this.memoryCache.set(key, item);
    this.currentSize += size;
  }

  // localStorage operations with compression
  private async getFromLocalStorage<T>(key: string): Promise<T | null> {
    try {
      const storedItem = localStorage.getItem(this.localStoragePrefix + key);
      if (!storedItem) return null;

      const item: CacheItem<T> = JSON.parse(storedItem);
      
      // Check TTL
      if (Date.now() > item.timestamp + item.ttl) {
        localStorage.removeItem(this.localStoragePrefix + key);
        return null;
      }

      // Decompress if needed
      let data = item.data;
      if (item.compressed && this.config.compressionEnabled) {
        data = await this.decompress(data as string);
      }

      return data;
    } catch (error) {
      console.error('Failed to get from localStorage:', error);
      return null;
    }
  }

  private async setInLocalStorage<T>(key: string, data: T, ttl: number): Promise<void> {
    try {
      let finalData = data;
      let compressed = false;

      // Compress large data if enabled
      if (this.config.compressionEnabled && this.calculateSize(data) > 1024) {
        finalData = await this.compress(JSON.stringify(data)) as T;
        compressed = true;
      }

      const item: CacheItem<T> = {
        data: finalData,
        timestamp: Date.now(),
        ttl,
        accessCount: 1,
        size: this.calculateSize(finalData),
        compressed
      };

      localStorage.setItem(this.localStoragePrefix + key, JSON.stringify(item));
    } catch (error) {
      // Handle localStorage quota exceeded
      if (error instanceof DOMException && error.code === 22) {
        await this.clearOldLocalStorageItems();
        // Retry once
        try {
          localStorage.setItem(this.localStoragePrefix + key, JSON.stringify({
            data,
            timestamp: Date.now(),
            ttl,
            accessCount: 1,
            size: this.calculateSize(data)
          }));
        } catch (retryError) {
          console.error('Failed to set in localStorage after cleanup:', retryError);
        }
      } else {
        console.error('Failed to set in localStorage:', error);
      }
    }
  }

  // Cache eviction strategies
  private evictItem(): void {
    if (this.memoryCache.size === 0) return;

    let keyToEvict: string;

    switch (this.config.strategy) {
      case 'lru':
        keyToEvict = this.findLRUKey();
        break;
      case 'lfu':
        keyToEvict = this.findLFUKey();
        break;
      case 'fifo':
        keyToEvict = this.memoryCache.keys().next().value;
        break;
      case 'ttl':
        keyToEvict = this.findExpiredKey() || this.findLRUKey();
        break;
      default:
        keyToEvict = this.findLRUKey();
    }

    const item = this.memoryCache.get(keyToEvict);
    if (item) {
      this.currentSize -= item.size;
    }
    this.memoryCache.delete(keyToEvict);
  }

  private findLRUKey(): string {
    let oldestKey = '';
    let oldestTime = Date.now();

    for (const [key, item] of this.memoryCache.entries()) {
      if (item.timestamp < oldestTime) {
        oldestTime = item.timestamp;
        oldestKey = key;
      }
    }

    return oldestKey;
  }

  private findLFUKey(): string {
    let lfuKey = '';
    let minAccessCount = Infinity;

    for (const [key, item] of this.memoryCache.entries()) {
      if (item.accessCount < minAccessCount) {
        minAccessCount = item.accessCount;
        lfuKey = key;
      }
    }

    return lfuKey;
  }

  private findExpiredKey(): string | null {
    const now = Date.now();
    for (const [key, item] of this.memoryCache.entries()) {
      if (now > item.timestamp + item.ttl) {
        return key;
      }
    }
    return null;
  }

  // Compression utilities
  private async compress(data: string): Promise<string> {
    if (typeof CompressionStream !== 'undefined') {
      const stream = new CompressionStream('gzip');
      const writer = stream.writable.getWriter();
      const reader = stream.readable.getReader();
      
      writer.write(new TextEncoder().encode(data));
      writer.close();
      
      const chunks: Uint8Array[] = [];
      let result = await reader.read();
      
      while (!result.done) {
        chunks.push(result.value);
        result = await reader.read();
      }
      
      const compressed = new Uint8Array(chunks.reduce((acc, chunk) => acc + chunk.length, 0));
      let offset = 0;
      
      for (const chunk of chunks) {
        compressed.set(chunk, offset);
        offset += chunk.length;
      }
      
      return btoa(String.fromCharCode(...compressed));
    }
    
    // Fallback: simple compression simulation
    return data;
  }

  private async decompress(compressedData: string): Promise<any> {
    if (typeof DecompressionStream !== 'undefined') {
      try {
        const binary = atob(compressedData);
        const bytes = new Uint8Array(binary.length);
        
        for (let i = 0; i < binary.length; i++) {
          bytes[i] = binary.charCodeAt(i);
        }
        
        const stream = new DecompressionStream('gzip');
        const writer = stream.writable.getWriter();
        const reader = stream.readable.getReader();
        
        writer.write(bytes);
        writer.close();
        
        const chunks: Uint8Array[] = [];
        let result = await reader.read();
        
        while (!result.done) {
          chunks.push(result.value);
          result = await reader.read();
        }
        
        const decompressed = new Uint8Array(chunks.reduce((acc, chunk) => acc + chunk.length, 0));
        let offset = 0;
        
        for (const chunk of chunks) {
          decompressed.set(chunk, offset);
          offset += chunk.length;
        }
        
        const text = new TextDecoder().decode(decompressed);
        return JSON.parse(text);
      } catch (error) {
        console.error('Decompression failed:', error);
        return null;
      }
    }
    
    // Fallback
    return JSON.parse(compressedData);
  }

  // Utility methods
  private calculateSize(data: any): number {
    return JSON.stringify(data).length * 2; // Rough estimate in bytes
  }

  private async clearOldLocalStorageItems(): Promise<void> {
    const keys = Object.keys(localStorage).filter(key => key.startsWith(this.localStoragePrefix));
    const items = keys.map(key => ({
      key,
      item: JSON.parse(localStorage.getItem(key) || '{}')
    })).filter(({ item }) => item.timestamp);

    // Sort by timestamp and remove oldest 25%
    items.sort((a, b) => a.item.timestamp - b.item.timestamp);
    const toRemove = items.slice(0, Math.floor(items.length * 0.25));

    toRemove.forEach(({ key }) => {
      localStorage.removeItem(key);
    });
  }

  private initCleanupTimer(): void {
    // Clean up expired items every 5 minutes
    setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  private cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    // Clean memory cache
    for (const [key, item] of this.memoryCache.entries()) {
      if (now > item.timestamp + item.ttl) {
        keysToDelete.push(key);
        this.currentSize -= item.size;
      }
    }

    keysToDelete.forEach(key => this.memoryCache.delete(key));

    // Clean localStorage
    if (this.config.persistToLocalStorage) {
      const keys = Object.keys(localStorage).filter(key => key.startsWith(this.localStoragePrefix));
      keys.forEach(key => {
        try {
          const item = JSON.parse(localStorage.getItem(key) || '{}');
          if (item.timestamp && now > item.timestamp + item.ttl) {
            localStorage.removeItem(key);
          }
        } catch (error) {
          // Remove corrupted items
          localStorage.removeItem(key);
        }
      });
    }
  }

  // Cache statistics
  getStats(): {
    memoryItems: number;
    memorySize: number;
    hitRate: number;
    localStorageItems: number;
  } {
    const localStorageItems = Object.keys(localStorage)
      .filter(key => key.startsWith(this.localStoragePrefix)).length;

    return {
      memoryItems: this.memoryCache.size,
      memorySize: this.currentSize,
      hitRate: 0, // Would need hit/miss tracking
      localStorageItems
    };
  }

  // Clear all cache
  clear(): void {
    this.memoryCache.clear();
    this.currentSize = 0;

    if (this.config.persistToLocalStorage) {
      const keys = Object.keys(localStorage).filter(key => key.startsWith(this.localStoragePrefix));
      keys.forEach(key => localStorage.removeItem(key));
    }
  }
}

// Specialized cache instances for different data types
export class SeafoodCacheManager {
  private static instance: SeafoodCacheManager;
  
  // Different cache configurations for different data types
  private inventoryCache = new MultiLayerCache({
    ttl: 5 * 60 * 1000, // 5 minutes
    maxSize: 10 * 1024 * 1024, // 10MB
    strategy: 'lru',
    persistToLocalStorage: true,
    compressionEnabled: true
  });

  private productCache = new MultiLayerCache({
    ttl: 30 * 60 * 1000, // 30 minutes
    maxSize: 5 * 1024 * 1024, // 5MB
    strategy: 'lfu',
    persistToLocalStorage: true,
    compressionEnabled: false
  });

  private voiceCache = new MultiLayerCache({
    ttl: 2 * 60 * 1000, // 2 minutes
    maxSize: 2 * 1024 * 1024, // 2MB
    strategy: 'ttl',
    persistToLocalStorage: false,
    compressionEnabled: false
  });

  private haccpCache = new MultiLayerCache({
    ttl: 60 * 60 * 1000, // 1 hour
    maxSize: 15 * 1024 * 1024, // 15MB
    strategy: 'lru',
    persistToLocalStorage: true,
    compressionEnabled: true
  });

  private userCache = new MultiLayerCache({
    ttl: 15 * 60 * 1000, // 15 minutes
    maxSize: 1 * 1024 * 1024, // 1MB
    strategy: 'lru',
    persistToLocalStorage: false,
    compressionEnabled: false
  });

  static getInstance(): SeafoodCacheManager {
    if (!this.instance) {
      this.instance = new SeafoodCacheManager();
    }
    return this.instance;
  }

  // Inventory data caching
  async getInventoryData<T>(key: string, fetchFn?: () => Promise<T>): Promise<T | null> {
    return this.inventoryCache.get(`inventory_${key}`, fetchFn);
  }

  async setInventoryData<T>(key: string, data: T): Promise<void> {
    return this.inventoryCache.set(`inventory_${key}`, data);
  }

  // Product data caching
  async getProductData<T>(key: string, fetchFn?: () => Promise<T>): Promise<T | null> {
    return this.productCache.get(`product_${key}`, fetchFn);
  }

  async setProductData<T>(key: string, data: T): Promise<void> {
    return this.productCache.set(`product_${key}`, data);
  }

  // Voice processing caching
  async getVoiceData<T>(key: string, fetchFn?: () => Promise<T>): Promise<T | null> {
    return this.voiceCache.get(`voice_${key}`, fetchFn);
  }

  async setVoiceData<T>(key: string, data: T): Promise<void> {
    return this.voiceCache.set(`voice_${key}`, data);
  }

  // HACCP compliance caching
  async getHACCPData<T>(key: string, fetchFn?: () => Promise<T>): Promise<T | null> {
    return this.haccpCache.get(`haccp_${key}`, fetchFn);
  }

  async setHACCPData<T>(key: string, data: T): Promise<void> {
    return this.haccpCache.set(`haccp_${key}`, data);
  }

  // User data caching
  async getUserData<T>(key: string, fetchFn?: () => Promise<T>): Promise<T | null> {
    return this.userCache.get(`user_${key}`, fetchFn);
  }

  async setUserData<T>(key: string, data: T): Promise<void> {
    return this.userCache.set(`user_${key}`, data);
  }

  // Bulk operations
  async getMultiple<T>(
    cacheType: 'inventory' | 'product' | 'voice' | 'haccp' | 'user',
    keys: string[]
  ): Promise<Map<string, T | null>> {
    const cache = this.getCacheByType(cacheType);
    const results = new Map<string, T | null>();

    await Promise.all(
      keys.map(async (key) => {
        const data = await cache.get(`${cacheType}_${key}`);
        results.set(key, data);
      })
    );

    return results;
  }

  async setMultiple<T>(
    cacheType: 'inventory' | 'product' | 'voice' | 'haccp' | 'user',
    data: Map<string, T>
  ): Promise<void> {
    const cache = this.getCacheByType(cacheType);

    await Promise.all(
      Array.from(data.entries()).map(([key, value]) =>
        cache.set(`${cacheType}_${key}`, value)
      )
    );
  }

  private getCacheByType(type: string): MultiLayerCache {
    switch (type) {
      case 'inventory': return this.inventoryCache;
      case 'product': return this.productCache;
      case 'voice': return this.voiceCache;
      case 'haccp': return this.haccpCache;
      case 'user': return this.userCache;
      default: throw new Error(`Unknown cache type: ${type}`);
    }
  }

  // Cache warming for critical data
  async warmCache(): Promise<void> {
    console.log('Warming cache with critical data...');

    try {
      // Warm product cache with most accessed products
      const { createClient } = await import('@supabase/supabase-js');
      const supabase = createClient(
        process.env.VITE_SUPABASE_URL!,
        process.env.VITE_SUPABASE_ANON_KEY!
      );

      // Cache recent inventory events
      const { data: recentEvents } = await supabase
        .from('inventory_events')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(100);

      if (recentEvents) {
        await this.setInventoryData('recent_events', recentEvents);
      }

      // Cache active products
      const { data: activeProducts } = await supabase
        .from('Products')
        .select('*')
        .eq('active', true)
        .limit(50);

      if (activeProducts) {
        await this.setProductData('active_products', activeProducts);
      }

      console.log('Cache warming completed');
    } catch (error) {
      console.error('Cache warming failed:', error);
    }
  }

  // Cache performance monitoring
  getPerformanceStats(): {
    inventory: any;
    product: any;
    voice: any;
    haccp: any;
    user: any;
  } {
    return {
      inventory: this.inventoryCache.getStats(),
      product: this.productCache.getStats(),
      voice: this.voiceCache.getStats(),
      haccp: this.haccpCache.getStats(),
      user: this.userCache.getStats()
    };
  }

  // Clear specific cache type
  clearCache(type: 'inventory' | 'product' | 'voice' | 'haccp' | 'user' | 'all'): void {
    if (type === 'all') {
      this.inventoryCache.clear();
      this.productCache.clear();
      this.voiceCache.clear();
      this.haccpCache.clear();
      this.userCache.clear();
    } else {
      this.getCacheByType(type).clear();
    }
  }
}

// Global cache manager instance
export const cacheManager = SeafoodCacheManager.getInstance();

// Initialize cache warming on application start
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
  // Warm cache after a short delay to not block initial render
  setTimeout(() => {
    cacheManager.warmCache();
  }, 2000);
}