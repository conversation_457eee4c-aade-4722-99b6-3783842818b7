// Add sample data to test EventsTable component
import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

const supabaseUrl = process.env.VITE_SUPABASE_URL
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function addSampleData() {
  console.log('📊 Adding Sample Data for Testing')
  console.log('==================================\n')

  try {
    // First, add some sample products
    console.log('1. Adding sample products...')
    const sampleProducts = [
      { name: 'Wild Salmon', category: 'Fish' },
      { name: 'Dungeness Crab', category: 'Shellfish' },
      { name: 'Pacific Cod', category: 'Fish' },
      { name: '<PERSON>ib<PERSON>', category: 'Fish' },
      { name: 'Oysters', category: 'Shellfish' }
    ]

    const { data: products, error: productError } = await supabase
      .from('Products')
      .insert(sampleProducts)
      .select()

    if (productError) {
      console.error('❌ Error adding products:', productError.message)
      return
    }

    console.log(`✅ Added ${products.length} products`)

    // Then add sample inventory events
    console.log('\n2. Adding sample inventory events...')
    const sampleEvents = [
      {
        event_type: 'receiving',
        product_id: products[0].id,
        name: 'Wild Salmon',
        quantity: 50,
        unit_price: 12.50,
        total_amount: 625.00,
        notes: 'Fresh catch from Alaska',
        metadata: { 
          unit: 'lbs',
          batch_number: 'AS-001-2025',
          supplier: 'Alaska Seafood Co.'
        }
      },
      {
        event_type: 'receiving',
        product_id: products[1].id,
        name: 'Dungeness Crab',
        quantity: 25,
        unit_price: 18.00,
        total_amount: 450.00,
        notes: 'Live crab delivery',
        metadata: { 
          unit: 'lbs',
          batch_number: 'DC-002-2025',
          supplier: 'Pacific Crab Co.'
        }
      },
      {
        event_type: 'sale',
        product_id: products[0].id,
        name: 'Wild Salmon',
        quantity: -15,
        unit_price: 18.00,
        total_amount: 270.00,
        notes: 'Restaurant order',
        metadata: { 
          unit: 'lbs',
          customer: 'Ocean View Restaurant'
        }
      },
      {
        event_type: 'physical_count',
        product_id: products[2].id,
        name: 'Pacific Cod',
        quantity: 35,
        unit_price: 8.50,
        total_amount: 297.50,
        notes: 'Weekly inventory count',
        metadata: { 
          unit: 'lbs',
          count_date: new Date().toISOString()
        }
      },
      {
        event_type: 'disposal',
        product_id: products[3].id,
        name: 'Halibut',
        quantity: -3,
        unit_price: 0,
        total_amount: 0,
        notes: 'Past expiration date',
        metadata: { 
          unit: 'lbs',
          reason: 'expired',
          disposal_date: new Date().toISOString()
        }
      }
    ]

    const { data: events, error: eventError } = await supabase
      .from('inventory_events')
      .insert(sampleEvents)
      .select()

    if (eventError) {
      console.error('❌ Error adding events:', eventError.message)
      return
    }

    console.log(`✅ Added ${events.length} inventory events`)

    // Verify the data was added
    console.log('\n3. Verifying data...')
    const { data: allEvents, error: verifyError } = await supabase
      .from('inventory_events')
      .select(`
        id,
        event_type,
        name,
        quantity,
        total_amount,
        created_at
      `)
      .order('created_at', { ascending: false })

    if (verifyError) {
      console.error('❌ Verification failed:', verifyError.message)
      return
    }

    console.log('✅ Events in database:')
    allEvents.forEach(event => {
      console.log(`   - ${event.event_type}: ${event.name} (${event.quantity} units) - $${event.total_amount || 0}`)
    })

    console.log('\n🎉 Sample data added successfully!')
    console.log('   You can now test the EventsTable component')

  } catch (error) {
    console.error('💥 Error:', error)
  }
}

addSampleData().then(() => {
  console.log('\n🏁 Sample data setup complete')
  process.exit(0)
}).catch(error => {
  console.error('\n💥 Setup failed:', error)
  process.exit(1)
})