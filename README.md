# Pacific Cloud Seafoods Manager

A comprehensive seafood inventory management and compliance tracking system with voice-enabled data entry, real-time traceability, and HACCP compliance monitoring.

## Overview

The Pacific Cloud Seafoods Manager is a modern web application built to streamline seafood operations through intelligent inventory management, voice-enabled data entry, and comprehensive compliance tracking. The system provides real-time visibility into inventory levels, batch tracking, vendor performance, and regulatory compliance.

## Key Features

### 🎤 Voice-Enabled Operations
- Hands-free inventory updates using speech-to-text
- Real-time voice command processing
- Audio storage and playback for audit trails
- Quality review system for low-confidence transcriptions

### 📊 Inventory Management
- Real-time inventory tracking and updates
- Batch tracking and lot management
- Product catalog with detailed specifications
- Automated alerts for low stock and expiration dates

### 🛡️ Compliance & Traceability
- HACCP compliance monitoring and reporting
- GDST-compliant traceability tracking
- FDA regulatory compliance features
- Complete audit trail for all operations

### 👥 Vendor & Customer Management
- Vendor performance tracking and report cards
- Customer relationship management
- Order history and communication tracking
- Integration with external vendor APIs

### 📈 Analytics & Reporting
- Real-time dashboards and analytics
- Custom reporting capabilities
- Performance metrics and KPIs
- Export functionality for various formats

## Technology Stack

### Frontend
- **React 18** with TypeScript for type-safe development
- **Vite** for fast development and optimized builds
- **Tailwind CSS** with Radix <PERSON> for modern, accessible components
- **React Hook Form** with Zod validation for robust form handling

### Backend & Database
- **Supabase** for PostgreSQL database with real-time capabilities
- **Row Level Security (RLS)** for data access control
- **Supabase Storage** for audio files and document management
- **Real-time subscriptions** for live data updates

### Voice Processing
- **OpenAI Whisper API** for speech-to-text conversion
- **Custom confidence scoring** for quality assurance
- **Audio compression and optimization** for efficient storage
- **Real-time processing pipeline** for immediate feedback

### Infrastructure
- **Vercel** for serverless deployment and hosting
- **Serverless functions** for API endpoints and processing
- **Global CDN** for optimal performance
- **Automated CI/CD** with quality gates

## Project Structure

```
├── src/                    # Main application source code
│   ├── components/         # React components organized by feature
│   ├── services/          # Business logic and data access services
│   ├── lib/               # Core libraries and utilities
│   ├── types/             # TypeScript type definitions
│   ├── hooks/             # Custom React hooks
│   ├── test/              # Testing infrastructure and utilities
│   └── utils/             # Helper functions and utilities
├── api/                   # Serverless API endpoints
├── supabase/              # Database migrations and configuration
├── docs/                  # Project documentation
├── scripts/               # Utility scripts and automation
├── infrastructure/        # Infrastructure as code and deployment configs
└── e2e/                   # End-to-end tests
```

Each major directory contains its own README.md with detailed information about its purpose, structure, and usage.

## Quick Start

### Prerequisites
- Node.js 18+ 
- npm or yarn package manager
- Supabase account for database
- OpenAI API key for voice processing

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/paccloud/PCS-Seafood-Manager.git
   cd PCS-Seafood-Manager
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment setup**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Database setup**
   ```bash
   npm run db:migrate
   npm run seed:categories
   npm run seed:products
   ```

5. **Start development server**
   ```bash
   npm run dev
   ```

### Environment Variables

```bash
# Supabase Configuration
VITE_SUPABASE_URL=your-supabase-url
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key

# Application Configuration
NODE_ENV=development
VITE_APP_VERSION=1.0.0
```

## Development Workflow

### Code Quality
```bash
# Run all quality checks
npm run quality:check

# Fix formatting and linting issues
npm run quality:fix

# Type checking
npm run type-check
```

### Testing
```bash
# Run unit tests
npm run test

# Run tests with coverage
npm run test:coverage

# Run end-to-end tests
npm run test:e2e

# Run all tests
npm run test:all
```

### Database Operations
```bash
# Apply migrations
npm run db:migrate

# Reset database
npm run db:reset

# Generate TypeScript types
supabase gen types typescript > src/types/supabase.ts
```

## Deployment

### Production Build
```bash
# Build for production
npm run build:production

# Analyze bundle size
npm run build:analyze

# Check performance budgets
npm run performance:check
```

### Deploy to Vercel
```bash
# Deploy to staging
npm run deploy:staging

# Deploy to production
npm run deploy:production
```

## Voice Processing Features

### Voice Commands
The system supports natural language voice commands for common operations:

- **Inventory Updates**: "Add 50 pounds of salmon to freezer A"
- **Product Lookup**: "Show me the current stock of cod"
- **Batch Tracking**: "Move batch 12345 from processing to finished goods"
- **Quality Checks**: "Record temperature check for freezer B at 32 degrees"

### Quality Assurance
- Confidence scoring for all voice transcriptions
- Manual review workflow for low-confidence events
- Audio playback for verification
- Batch approval and correction capabilities

## Compliance Features

### HACCP Compliance
- Critical Control Point (CCP) monitoring
- Temperature logging and alerts
- Corrective action tracking
- Compliance reporting and documentation

### Traceability (GDST)
- Complete supply chain visibility
- Catch/harvest information tracking
- Processing history documentation
- Chain of custody maintenance

### Audit Trail
- Complete audit trail for all operations
- User attribution and timestamps
- Change tracking with before/after values
- Compliance reporting capabilities

## Performance & Monitoring

### Performance Budgets
- JavaScript bundle: < 1MB
- CSS bundle: < 50KB
- First Contentful Paint: < 2s
- Largest Contentful Paint: < 3s

### Monitoring
- Real-time error tracking
- Performance monitoring
- User analytics
- Custom business metrics

## Security

### Data Protection
- Row Level Security (RLS) for database access
- Encrypted data transmission (HTTPS)
- Secure audio file storage with signed URLs
- Input validation and sanitization

### Authentication & Authorization
- Supabase Auth for user management
- Role-based access control
- Session management
- API key security

## Contributing

### Development Guidelines
1. Follow TypeScript best practices
2. Write comprehensive tests for new features
3. Maintain accessibility standards (WCAG 2.1 AA)
4. Use semantic commit messages
5. Update documentation for significant changes

### Code Review Process
1. Create feature branch from `main`
2. Implement changes with tests
3. Run quality checks locally
4. Submit pull request with description
5. Address review feedback
6. Merge after approval

## Support & Documentation

### Documentation
- [API Documentation](./docs/)
- [Database Schema](./supabase/README.md)
- [Component Library](./src/components/README.md)
- [Deployment Guide](./docs/PRODUCTION_DEPLOYMENT_GUIDE.md)

### Getting Help
- Check existing documentation in each directory's README
- Review issue templates for bug reports and feature requests
- Contact the development team for technical support

## License

This project is proprietary software owned by Pacific Cloud Seafoods. All rights reserved.

## Changelog

See [CHANGELOG.md](./CHANGELOG.md) for a detailed history of changes and releases.

---

**Built with ❤️ for the seafood industry**