#!/usr/bin/env node

/**
 * Custom migration application script for remote Supabase
 * Applies migrations in correct dependency order for TempStick integration
 */

import { createClient } from '@supabase/supabase-js';
import { promises as fs } from 'fs';
import path from 'path';
import { config } from 'dotenv';
import { fileURLToPath } from 'url';

// Load environment variables
config();

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const serviceRoleKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !serviceRoleKey) {
  console.error('Missing required environment variables: VITE_SUPABASE_URL and VITE_SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Initialize Supabase client with service role key for admin operations
const supabase = createClient(supabaseUrl, serviceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Ordered list of migrations to apply (dependency-aware)
const migrationOrder = [
  // Core extensions and basic schema
  '20250813_000_enable_extensions.sql',
  
  // Enhanced traceability GDST - provides base traceability tables
  '20250813_enhanced_traceability_gdst.sql',
  
  // HACCP compliance system - provides HACCP tables needed for RLS policies
  '20250813_haccp_compliance_system.sql',
  
  // Voice event management - extends inventory_events with voice columns
  '20250815_001_voice_event_management_schema.sql',
  
  // Critical security fixes
  '20250819_001_CRITICAL_SECURITY_FIX_user_data_isolation.sql',
  '20250819_002_SECURITY_VERIFICATION_TESTS.sql',
  
  // Enhanced compliance system
  '20250821_001_enhanced_compliance_system.sql',
  
  // TempStick sensor integration schema - CRITICAL for temperature monitoring
  '20250825_001_tempstick_sensor_integration_schema.sql',
  
  // TempStick sample data - provides initial data for testing
  '20250825_002_tempstick_sample_data.sql',
  
  // Inventory temperature integration - links inventory with temperature data
  '20250825_003_inventory_temperature_integration.sql',
  
  // Vendor system
  '20250814_001_vendor_report_card_schema.sql',
  '20250814_002_vendor_rls_policies.sql',
  '20250814_003_vendor_metrics_automation.sql',
  
  // Inventory enhancements
  '20250814_004_inventory_integration.sql',
  '20250814_005_add_occurred_at_to_inventory_events.sql',
  '20250814_006_add_occurred_at_to_inventory_events.sql',
  
  // RLS policies - MUST be last after all tables are created
  '20250813_999_compliance_rls_policies.sql'
];

async function checkMigrationStatus() {
  console.log('🔍 Checking migration status...');
  
  const { data, error } = await supabase.rpc('exec', {
    sql: `
      SELECT version, applied_at 
      FROM supabase_migrations.schema_migrations 
      ORDER BY version;
    `
  });
  
  if (error && !error.message.includes('does not exist')) {
    console.error('Error checking migrations:', error);
    return [];
  }
  
  return data || [];
}

async function readMigrationFile(filename) {
  const migrationPath = path.join(__dirname, 'supabase', 'migrations', filename);
  
  try {
    const content = await fs.readFile(migrationPath, 'utf8');
    return content;
  } catch (error) {
    if (error.code === 'ENOENT') {
      console.warn(`⚠️  Migration file not found: ${filename}`);
      return null;
    }
    throw error;
  }
}

async function applyMigration(filename, sql) {
  console.log(`📝 Applying migration: ${filename}`);
  
  // Extract version from filename
  const version = filename.replace('.sql', '');
  
  try {
    // Execute the migration SQL
    const { error: execError } = await supabase.rpc('exec', { sql });
    
    if (execError) {
      console.error(`❌ Failed to apply ${filename}:`, execError.message);
      
      // For certain "already exists" errors, we can continue
      if (execError.message.includes('already exists') || 
          execError.message.includes('does not exist')) {
        console.log(`⚠️  Continuing despite non-critical error for ${filename}`);
        return true;
      }
      
      return false;
    }
    
    // Record the migration as applied
    const { error: recordError } = await supabase.rpc('exec', {
      sql: `
        INSERT INTO supabase_migrations.schema_migrations (version, applied_at, statements, name) 
        VALUES ('${version}', NOW(), 1, '${filename}')
        ON CONFLICT (version) DO NOTHING;
      `
    });
    
    if (recordError) {
      console.warn(`⚠️  Could not record migration status for ${filename}:`, recordError.message);
    }
    
    console.log(`✅ Successfully applied: ${filename}`);
    return true;
    
  } catch (error) {
    console.error(`💥 Unexpected error applying ${filename}:`, error);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting ordered migration application for remote Supabase...');
  console.log(`📍 Target: ${supabaseUrl}`);
  
  // Check current migration status
  const appliedMigrations = await checkMigrationStatus();
  console.log(`📊 Currently applied migrations: ${appliedMigrations.length}`);
  
  let successCount = 0;
  let skipCount = 0;
  let errorCount = 0;
  
  for (const filename of migrationOrder) {
    const version = filename.replace('.sql', '');
    
    // Check if migration is already applied
    const alreadyApplied = appliedMigrations.some(m => m.version === version);
    if (alreadyApplied) {
      console.log(`⏭️  Skipping already applied: ${filename}`);
      skipCount++;
      continue;
    }
    
    // Read migration file
    const sql = await readMigrationFile(filename);
    if (!sql) {
      console.log(`⏭️  Skipping missing file: ${filename}`);
      skipCount++;
      continue;
    }
    
    // Apply migration
    const success = await applyMigration(filename, sql);
    if (success) {
      successCount++;
    } else {
      errorCount++;
      
      // Stop on critical errors (non-RLS related)
      if (!filename.includes('rls_policies')) {
        console.error(`💀 Critical migration failed: ${filename}`);
        break;
      }
    }
    
    // Brief pause between migrations
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n📋 Migration Summary:');
  console.log(`✅ Successfully applied: ${successCount}`);
  console.log(`⏭️  Skipped: ${skipCount}`);
  console.log(`❌ Errors: ${errorCount}`);
  
  if (errorCount === 0) {
    console.log('\n🎉 All migrations applied successfully!');
    console.log('\n🔧 Next steps:');
    console.log('1. Verify voice columns are available');
    console.log('2. Check TempStick sensor tables exist');
    console.log('3. Fix Products table RLS policies');
    console.log('4. Test real TempStick API integration');
  } else {
    console.log('\n⚠️  Some migrations had issues. Please review the errors above.');
  }
}

main().catch(console.error);