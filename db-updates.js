// This script will make the necessary updates to the database schema
// including adding new tables and renaming the vendors table to suppliers
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkExistingTables() {
  try {
    console.log('Checking existing tables...');
    
    // We need to check if tables exist by attempting to query them
    const tables = [
      'products',
      'categories',
      'vendors',
      'customers',
      'batches',
      'events',
      'inventory',
      'inventory_snapshots'
    ];
    
    const existingTables = [];
    
    for (const table of tables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('count(*)', { count: 'exact' })
          .limit(1);
          
        if (!error) {
          existingTables.push(table);
          console.log(`✓ Table ${table} exists`);
        } else {
          console.log(`✗ Table ${table} does not exist or error: ${error.message}`);
        }
      } catch (error) {
        console.log(`✗ Error checking table ${table}: ${error.message}`);
      }
    }
    
    return existingTables;
  } catch (error) {
    console.error('Error checking existing tables:', error);
    return [];
  }
}

async function renameVendorsToSuppliers() {
  try {
    console.log('Renaming vendors table to suppliers...');
    
    // We need to use raw SQL for this operation
    const { data, error } = await supabase.rpc('execute_sql', {
      sql_query: 'ALTER TABLE vendors RENAME TO suppliers;'
    });
    
    if (error) {
      console.error('Error renaming vendors table:', error);
      return false;
    }
    
    console.log('Vendors table renamed to suppliers');
    return true;
  } catch (error) {
    console.error('Error renaming vendors table:', error);
    return false;
  }
}

async function createMissingTables() {
  try {
    console.log('Creating missing tables...');
    
    // SQL for creating COGS table
    const cogsSql = `
      CREATE TABLE IF NOT EXISTS cogs (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        batch_id UUID NOT NULL,
        raw_product_cost DECIMAL(10, 2) NOT NULL,
        shipping_cost DECIMAL(10, 2) DEFAULT 0,
        handling_cost DECIMAL(10, 2) DEFAULT 0,
        processing_cost DECIMAL(10, 2) DEFAULT 0,
        packaging_cost DECIMAL(10, 2) DEFAULT 0,
        labor_cost DECIMAL(10, 2) DEFAULT 0,
        other_costs DECIMAL(10, 2) DEFAULT 0,
        total_cost DECIMAL(10, 2),
        cost_per_unit DECIMAL(10, 2),
        notes TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    // SQL for creating Fulfillments table
    const fulfillmentsSql = `
      CREATE TABLE IF NOT EXISTS fulfillments (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        order_number VARCHAR(50) NOT NULL,
        square_transaction_id VARCHAR(100),
        dbp_order_id VARCHAR(100),
        batch_id UUID,
        product_id UUID NOT NULL,
        customer_id UUID,
        quantity DECIMAL(10, 2) NOT NULL,
        unit_price DECIMAL(10, 2) NOT NULL,
        total_price DECIMAL(10, 2) NOT NULL,
        fulfillment_date TIMESTAMP WITH TIME ZONE NOT NULL,
        fulfillment_status VARCHAR(50) NOT NULL,
        shipping_method VARCHAR(100),
        tracking_number VARCHAR(100),
        notes TEXT,
        metadata JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    // SQL for creating HACCP logs table
    const haccpLogsSql = `
      CREATE TABLE IF NOT EXISTS haccp_logs (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        batch_id UUID,
        log_type VARCHAR(50) NOT NULL,
        step_name VARCHAR(100) NOT NULL,
        critical_limit VARCHAR(255),
        monitoring_procedure TEXT,
        corrective_action TEXT,
        verification_procedure TEXT,
        record_keeping TEXT,
        temperature DECIMAL(5, 2),
        humidity DECIMAL(5, 2),
        ph_value DECIMAL(5, 2),
        compliance_status VARCHAR(50) NOT NULL,
        inspector_name VARCHAR(100),
        details TEXT,
        log_date TIMESTAMP WITH TIME ZONE NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    // SQL for creating HACCP audits table
    const haccpAuditsSql = `
      CREATE TABLE IF NOT EXISTS haccp_audits (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        audit_date TIMESTAMP WITH TIME ZONE NOT NULL,
        auditor_name VARCHAR(100) NOT NULL,
        audit_type VARCHAR(50) NOT NULL,
        findings TEXT,
        non_conformities TEXT,
        corrective_actions TEXT,
        compliance_status VARCHAR(50) NOT NULL,
        next_audit_date TIMESTAMP WITH TIME ZONE,
        attachments TEXT[],
        notes TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    // SQL for creating platform integrations table
    const platformIntegrationsSql = `
      CREATE TABLE IF NOT EXISTS platform_integrations (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        platform_name VARCHAR(50) NOT NULL,
        api_key TEXT,
        api_secret TEXT,
        access_token TEXT,
        refresh_token TEXT,
        token_expiry TIMESTAMP WITH TIME ZONE,
        webhook_url TEXT,
        webhook_secret TEXT,
        last_sync_time TIMESTAMP WITH TIME ZONE,
        sync_interval_minutes INTEGER DEFAULT 60,
        status VARCHAR(20) DEFAULT 'active',
        config JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    // SQL for creating sync logs table
    const syncLogsSql = `
      CREATE TABLE IF NOT EXISTS sync_logs (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        platform_id UUID,
        sync_start_time TIMESTAMP WITH TIME ZONE NOT NULL,
        sync_end_time TIMESTAMP WITH TIME ZONE,
        sync_type VARCHAR(50) NOT NULL,
        sync_direction VARCHAR(20) NOT NULL,
        items_processed INTEGER DEFAULT 0,
        items_created INTEGER DEFAULT 0,
        items_updated INTEGER DEFAULT 0,
        items_deleted INTEGER DEFAULT 0,
        items_failed INTEGER DEFAULT 0,
        status VARCHAR(20) NOT NULL,
        error_details JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
    `;
    
    // Execute SQL for each table creation
    const sqlStatements = [
      cogsSql,
      fulfillmentsSql,
      haccpLogsSql,
      haccpAuditsSql,
      platformIntegrationsSql,
      syncLogsSql
    ];
    
    for (const sql of sqlStatements) {
      const { error } = await supabase.rpc('execute_sql', {
        sql_query: sql
      });
      
      if (error) {
        console.error('Error creating table:', error);
      }
    }
    
    console.log('All missing tables created successfully');
    return true;
  } catch (error) {
    console.error('Error creating missing tables:', error);
    return false;
  }
}

async function updateSchema() {
  try {
    console.log('Starting database schema updates...');
    
    // 1. Check which tables already exist
    const existingTables = await checkExistingTables();
    
    // 2. Rename vendors to suppliers if needed
    if (existingTables.includes('vendors') && !existingTables.includes('suppliers')) {
      await renameVendorsToSuppliers();
    }
    
    // 3. Create any missing tables
    await createMissingTables();
    
    console.log('Database schema updates completed!');
  } catch (error) {
    console.error('Error updating database schema:', error);
  }
}

// Run the schema update
updateSchema();
