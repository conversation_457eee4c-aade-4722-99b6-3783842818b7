# Codebase Structure and Organization

## Root Directory Structure
```
/Users/<USER>/Dev/Seafood-Manager/
├── src/                    # Main application source code
├── supabase/              # Database schemas and migrations
├── public/                # Static assets
├── docs/                  # Documentation
├── scripts/               # Build and utility scripts
├── e2e/                   # End-to-end tests
├── monitoring/            # Performance monitoring configs
├── infrastructure/        # Deployment and infrastructure
├── docker/               # Docker configurations
└── security/             # Security policies and configs
```

## Source Code Structure (/src)
```
src/
├── components/
│   ├── ui/               # Radix UI + Tailwind components
│   ├── inventory/        # → Use ai-voice-processing-agent
│   ├── import/          # → Use import-export-data-processor
│   ├── haccp/           # → Use seafood-compliance-engineer
│   ├── dashboard/       # → Use performance-devops-specialist
│   └── sensors/         # → Use supabase-seafood-db-architect (NEW)
├── lib/
│   ├── supabase.ts      # → Use supabase-seafood-db-architect
│   ├── api.ts           # → Use code-quality-typescript-agent
│   ├── voice-processor.ts # → Use ai-voice-processing-agent
│   └── tempstick-service.ts # → Use supabase-seafood-db-architect (NEW)
├── types/
│   ├── schema.ts        # → Use supabase-seafood-db-architect
│   └── tempstick.ts     # → Use code-quality-typescript-agent (NEW)
├── hooks/               # → Use code-quality-typescript-agent
├── contexts/            # → Use react-testing-architect
└── test/               # Comprehensive test suites
```

## Database Structure (/supabase)
```
supabase/
├── migrations/          # Database migration files
│   ├── 20250819_001_CRITICAL_SECURITY_FIX_user_data_isolation.sql
│   ├── 20250815_001_voice_event_management_schema.sql
│   ├── 20250814_001_vendor_report_card_schema.sql
│   └── ... (many migration files)
├── Database Tables/     # CSV data files for reference
└── config.toml         # Supabase configuration
```

## Key Database Tables (Existing)
- **products** - Seafood product catalog with GDST fields
- **inventory_events** - Event-sourced inventory operations
- **calendar_events** - HACCP scheduling and compliance tracking
- **suppliers/vendors** - Vendor management with report cards
- **vendor_interactions** - Detailed vendor performance tracking
- **vendor_metrics** - Performance analytics and rankings
- **voice_events** - Voice-powered data entry system
- **haccp_logs** - HACCP compliance and monitoring

## Missing Tables (Need Implementation)
- **sensors** - TempStick sensor metadata and configuration
- **temperature_readings** - Time-series temperature data
- **temperature_alerts** - Temperature violation alerts
- **storage_areas** - Physical storage location management

## Key Features by Directory

### /components/inventory
- Voice-powered inventory entry
- Batch tracking and management
- Real-time inventory updates
- GDST traceability integration

### /components/haccp
- HACCP compliance dashboard
- Temperature monitoring (needs TempStick integration)
- Critical control point tracking
- Compliance reporting

### /components/import
- CSV import wizards
- Data validation and mapping
- Bulk operations processing
- Performance optimization for large files

### /lib
- **supabase.ts**: Database client and utilities
- **voice-processor.ts**: OpenAI integration for voice processing
- **api.ts**: REST API utilities and type safety
- **tempstick-service.ts**: (Needs implementation) TempStick API integration

## Configuration Files
- **package.json**: Dependencies and scripts
- **vite.config.ts**: Build configuration
- **tailwind.config.js**: Styling configuration
- **eslint.config.js**: Code quality rules
- **tsconfig.json**: TypeScript configuration
- **vitest.config.ts**: Testing configuration

## Testing Structure
- **Unit tests**: Component-specific testing
- **Integration tests**: Database and API integration
- **Performance tests**: Voice processing and import performance
- **E2E tests**: Complete user workflows
- **Voice tests**: Specialized voice processing validation

## Environment Configuration
- **.env.example**: Template for environment variables
- **.env.staging**: Staging environment config
- **.env.production**: Production environment config

## Development Tools
- **Prettier**: Code formatting
- **ESLint**: Code quality and style enforcement
- **TypeScript**: Type safety and developer experience
- **Vitest**: Unit and integration testing
- **Playwright**: End-to-end testing
- **Lighthouse CI**: Performance monitoring