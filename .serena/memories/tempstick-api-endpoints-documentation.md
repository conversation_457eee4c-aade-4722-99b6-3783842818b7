# TempStick API Integration Documentation

## API Configuration

**Base URL**: `https://api.tempstick.com/v1`
**Authentication**: API Key via `VITE_TEMPSTICK_API_KEY` environment variable
**Current API Key**: `03e99232a2794e2ea07fcd8f06ad356f35132396f02133c07a`

## Rate Limiting Configuration

- **Requests Per Minute**: 60
- **Burst Limit**: 10 requests
- **Retry Logic**: Exponential backoff (1s → 2s → 4s, max 30s)
- **Max Retries**: 3 attempts

## Available API Endpoints

### 1. Sensors Management

#### Get All Sensors
- **Endpoint**: `GET /sensors`
- **Response**: `{ sensors: TempStickSensor[] }`
- **Usage**: Retrieve all registered sensors with status information

#### Get Sensor Health
- **Endpoint**: `GET /sensors/{sensorId}/health`
- **Response**: `{ health: { online: boolean, batteryLevel: number, signalStrength: number } }`
- **Usage**: Check individual sensor health, battery, and connectivity

#### Health Check
- **Endpoint**: `GET /sensors?limit=1`
- **Usage**: Quick API connectivity test

### 2. Temperature Readings

#### Get Latest Readings
- **Endpoint**: `GET /sensors/{sensorId}/readings?limit={limit}`
- **Parameters**: 
  - `limit`: Number of readings (default: 100)
- **Response**: `{ readings: TempStickReading[] }`
- **Usage**: Get most recent temperature/humidity readings

#### Get Historical Readings
- **Endpoint**: `GET /sensors/{sensorId}/readings?start={start}&end={end}`
- **Parameters**:
  - `start`: ISO timestamp (start date)
  - `end`: ISO timestamp (end date)
- **Response**: `{ readings: TempStickReading[] }`
- **Usage**: Get readings for specific time range

## Data Structures

### TempStickSensor
```typescript
interface TempStickSensor {
  id: string;                    // Unique sensor identifier
  name?: string;                 // User-assigned sensor name
  location?: string;             // Physical location description
  battery_level?: number;        // Battery percentage (0-100)
  signal_strength?: number;      // Signal strength percentage
  last_reading?: string;         // ISO timestamp of last reading
  status: 'online' | 'offline' | 'low_battery';
  firmware_version?: string;     // Current firmware version
}
```

### TempStickReading
```typescript
interface TempStickReading {
  temperature: number;           // Temperature in configured units
  humidity: number;              // Humidity percentage
  timestamp: string;             // ISO timestamp of reading
  sensor_id: string;            // Reference to sensor
  battery_level?: number;        // Battery level at time of reading
  signal_strength?: number;      // Signal strength at reading time
}
```

### TempStickApiResponse
```typescript
interface TempStickApiResponse<T = any> {
  success: boolean;              // Request success status
  data?: T;                     // Response payload
  error?: string;               // Error message if failed
  message?: string;             // Additional info message
}
```

## Error Handling

### Error Types
- **TempStickApiError**: API-specific errors with retry capability
- **TempStickServiceError**: Service operation errors
- **Rate Limiting**: Automatic queue management with backoff
- **Network Issues**: Automatic retry with exponential backoff

### Error Categories
- **Retryable Errors**: Network timeouts, 5xx server errors, rate limits
- **Non-Retryable Errors**: 401 authentication, 404 not found, malformed requests
- **Rate Limited**: 429 responses trigger queue-based retry system

## Service Features

### Enhanced Capabilities
- **Mock Data Fallback**: Automatically uses mock data when API unavailable
- **Data Quality Validation**: Validates sensor readings for completeness
- **Health Monitoring**: Tracks API performance metrics and sensor status
- **Request Queuing**: Manages API rate limits transparently
- **Comprehensive Logging**: Detailed logging for debugging and monitoring

### Configuration Options
```typescript
// Service initialization
const service = new TempStickService({
  retry: {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 30000,
    backoffMultiplier: 2
  },
  rateLimit: {
    requestsPerMinute: 60,
    burstLimit: 10
  }
});
```

### Mock Data System
- **Environment Variable**: `VITE_USE_MOCK_TEMPSTICK_DATA=true`
- **Fallback Strategy**: Automatically enables when API unavailable
- **Realistic Data**: Generates 4 mock sensors with varied temperature profiles
- **Development Mode**: Always available for offline development

## Integration Points

### Database Synchronization
- **Sensor Registration**: Auto-creates database records for discovered sensors
- **Reading Storage**: Stores all readings in `temperature_readings` table
- **Alert Generation**: Automatically creates alerts based on thresholds
- **Storage Area Mapping**: Links sensors to specific storage areas

### Dashboard Integration
- **Real-time Updates**: 30-second refresh intervals
- **Health Indicators**: Battery, connectivity, and alert status
- **Temperature Trends**: Historical charts with configurable time ranges
- **Alert Management**: Visual indicators for temperature violations

### HACCP Compliance
- **Critical Control Points**: Monitors temperature-sensitive storage areas
- **Violation Detection**: Automatic alerts for threshold breaches
- **Audit Trail**: Complete record of temperature readings and responses
- **Reporting**: Automated compliance reports for regulatory requirements

## Usage Examples

### Basic Sensor Data Retrieval
```typescript
// Get all sensors
const sensors = await tempStickService.getSensors();

// Get latest readings for a sensor
const readings = await tempStickService.getLatestReadings('sensor-123', 50);

// Check sensor health
const health = await tempStickService.getSensorHealth('sensor-123');
```

### Synchronization Operations
```typescript
// Full sync of all sensors
const syncResult = await tempStickService.syncAllSensors();

// Sync specific sensors
const specificSync = await tempStickService.syncSensorData(['sensor-1', 'sensor-2']);

// Check system health
const healthStatus = await tempStickService.getSystemHealth();
```

## Monitoring and Metrics

### Performance Tracking
- **Request Counts**: Total, successful, failed, and rate-limited requests
- **Response Times**: Average API latency measurements
- **Error Rates**: Failure rate monitoring and alerting
- **Sync Status**: Last sync time and success status

### Health Checks
- **API Connectivity**: Regular health check requests
- **Sensor Status**: Online/offline monitoring
- **Battery Monitoring**: Low battery alerts and tracking
- **Signal Quality**: Connectivity strength monitoring

This documentation covers the complete TempStick API integration as implemented in the Seafood Manager temperature monitoring system.