# Task Completion Checklist

## Before Submitting Work

### Code Quality Verification
- [ ] Run `npm run type-check` - Fix all TypeScript errors
- [ ] Run `npm run lint:fix` - Fix all linting issues
- [ ] Run `npm run format` - Ensure consistent code formatting
- [ ] Run `npm run quality:check` - Verify all quality gates pass

### Testing Requirements
- [ ] Run relevant test suites based on changes made
- [ ] For voice features: `npm run test:voice:complete`
- [ ] For vendor features: `npm run test:vendor`
- [ ] For database changes: `npm run test:supabase`
- [ ] For UI changes: `npm run test:e2e`

### Database Changes
- [ ] **CRITICAL**: All database changes must go through `supabase-seafood-db-architect`
- [ ] Run migration files through proper review
- [ ] Test RLS policies for user data isolation
- [ ] Verify indexes for performance
- [ ] Backup before applying changes

### Security Review
- [ ] **MANDATORY**: Authentication/data changes require `security-audit-specialist` review
- [ ] Verify RLS policies prevent data leakage between users
- [ ] Check for SQL injection vulnerabilities
- [ ] Validate input sanitization
- [ ] Test authorization boundaries

### Performance Validation
- [ ] Run `npm run build:analyze` for bundle size impact
- [ ] Run `npm run performance:check` for performance budgets
- [ ] Test with large datasets if applicable
- [ ] Verify real-time features don't degrade performance

### Specialized Agent Reviews
Based on the type of work, ensure proper specialist review:
- **Voice processing**: `ai-voice-processing-agent`
- **HACCP compliance**: `seafood-compliance-engineer`
- **Import/export**: `import-export-data-processor`
- **React testing**: `react-testing-architect`
- **Production deployment**: `performance-devops-specialist`

### Documentation Updates
- [ ] Update component JSDoc comments for public interfaces
- [ ] Update type definitions and exports
- [ ] Update CLAUDE.md if new patterns/dependencies added
- [ ] Verify setup instructions in README remain accurate

### Git Workflow
- [ ] Commit messages follow conventional format
- [ ] Branch name follows naming conventions
- [ ] All changes are properly staged
- [ ] No sensitive data in commits

### Final Verification
- [ ] Test in development environment (`npm run dev`)
- [ ] Verify builds successfully (`npm run build`)
- [ ] Preview production build (`npm run preview`)
- [ ] No console errors or warnings
- [ ] Features work without JavaScript errors

## Deployment Checklist (Production)

### Pre-Deployment
- [ ] All tests pass in CI/CD pipeline
- [ ] Performance metrics within acceptable ranges
- [ ] Security audit completed if required
- [ ] Database migrations tested in staging
- [ ] Environment variables configured

### Post-Deployment
- [ ] Monitor application logs for errors
- [ ] Verify core functionality works
- [ ] Check database performance
- [ ] Validate user data isolation
- [ ] Monitor real-time features (voice, temperature sensors)

## Quality Gates

### Never Deploy If:
- TypeScript errors exist
- Critical security vulnerabilities found
- Performance budgets exceeded
- User data isolation compromised
- Core functionality broken
- Database migrations failed

### Always Required:
- Proper specialist agent review
- Comprehensive testing
- Security validation
- Performance verification
- Documentation updates