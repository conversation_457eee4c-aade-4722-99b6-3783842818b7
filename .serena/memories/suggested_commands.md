# Essential Commands for Seafood Manager Development

## Development Server
```bash
npm run dev              # Start development server on port 5177
npm run build           # Build for production
npm run preview         # Preview production build
```

## Code Quality & Testing
```bash
npm run type-check      # TypeScript type checking
npm run lint            # ESLint code quality check
npm run lint:fix        # Auto-fix linting issues
npm run format          # Format code with Prettier
npm run format:check    # Check code formatting
npm run quality:check   # Run all quality checks (type, lint, format)
npm run quality:fix     # Fix formatting and linting issues
```

## Testing (Comprehensive Test Suite Available)
```bash
npm test                # Run all tests
npm run test:watch      # Run tests in watch mode
npm run test:coverage   # Run tests with coverage
npm run test:voice      # Run voice-specific tests
npm run test:vendor     # Run vendor system tests
npm run test:e2e        # Run end-to-end tests
```

## Database Operations
```bash
npm run db:migrate      # Apply database migrations
npm run db:reset        # Reset database to clean state
npm run seed:categories # Seed product categories
npm run seed:products   # Seed sample products
```

## Voice System Testing
```bash
npm run test:voice:unit        # Unit tests for voice components
npm run test:voice:integration # Voice database integration tests
npm run test:voice:performance # Voice processing performance tests
npm run test:voice:complete    # Complete voice test suite
```

## Performance & Production
```bash
npm run build:production         # Production build with optimizations
npm run build:analyze           # Build with bundle analysis
npm run performance:check       # Check performance budgets
npm run performance:lighthouse  # Run Lighthouse CI
```

## Specialized Agent Commands
According to CLAUDE.md, delegate to specialized agents:
- **Database issues** → Use `supabase-seafood-db-architect`
- **TypeScript errors** → Use `code-quality-typescript-agent`
- **Voice processing** → Use `ai-voice-processing-agent`
- **HACCP compliance** → Use `seafood-compliance-engineer`
- **Import/export** → Use `import-export-data-processor`
- **Security audits** → Use `security-audit-specialist`
- **Testing** → Use `react-testing-architect`
- **Performance** → Use `performance-devops-specialist`

## System Commands (macOS/Darwin)
```bash
# Basic file operations
ls -la                  # List files with details
find . -name "*.ts"     # Find TypeScript files
grep -r "pattern" src/  # Search in source code
cd /path/to/directory   # Change directory

# Git operations
git status              # Check repository status
git log --oneline -10   # Recent commits
git branch -a           # List all branches

# Process management
ps aux | grep node      # Find Node.js processes
lsof -i :5177          # Check what's using port 5177
```