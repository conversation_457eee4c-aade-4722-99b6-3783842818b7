# Code Style and Conventions

## File Structure Limits
- **Components**: Maximum 300 lines - split into smaller focused components
- **Functions**: Maximum 50 lines with single responsibility
- **Custom hooks**: Maximum 100 lines with clear purpose
- **Line length**: Maximum 120 characters (Prettier/ESLint configured)

## TypeScript Standards
- **Strict typing**: No `any` types allowed - use proper TypeScript types
- **Type checking**: All TypeScript strict flags enabled
- **Interfaces**: Prefer interfaces over types for object shapes
- **Union types**: Use proper union types instead of string literals
- **Export types**: Always export types alongside components for reuse

## React Best Practices
- **Functional components**: Use hooks exclusively, no class components
- **Custom hooks**: Prefer custom hooks for complex state logic
- **Error boundaries**: Implement proper error boundaries for component trees
- **Performance**: Use React.memo() for expensive components
- **Component depth**: Avoid deeply nested structures (max 3-4 levels)

## Standard Component Structure
```typescript
// Component file structure template
interface ComponentProps {
  // Props with proper TypeScript typing
}

export const Component: React.FC<ComponentProps> = ({ prop1, prop2 }) => {
  // 1. Hooks (useState, useEffect, custom hooks)
  // 2. Event handlers
  // 3. Derived values/computations
  // 4. Early returns/loading states
  // 5. Main JSX return
};

export default Component;
```

## CSS/Styling Conventions
```css
/* TailwindCSS with seafood industry color scheme */
.inventory-status-low { @apply bg-red-100 text-red-800 border-red-200; }
.haccp-compliant { @apply bg-green-50 border-green-200 text-green-700; }
.voice-active { @apply bg-blue-100 border-blue-300 animate-pulse; }
```

## Naming Conventions
- **Components**: PascalCase (`VoiceAssistant`, `HACCPDashboard`)
- **Files**: kebab-case for components (`voice-assistant.tsx`)
- **Variables/Functions**: camelCase (`voiceConfidence`, `processTranscript`)
- **Constants**: SCREAMING_SNAKE_CASE (`API_ENDPOINTS`, `ERROR_MESSAGES`)
- **Types/Interfaces**: PascalCase (`VoiceEvent`, `InventoryData`)

## Import Organization
```typescript
// 1. React and external libraries
import React, { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';

// 2. Internal utilities and types
import { supabase } from '@/lib/supabase';
import type { VoiceEvent } from '@/types/schema';

// 3. Components (local first, then shared)
import { Button } from '@/components/ui/button';
import { VoiceInput } from './voice-input';
```

## Database Conventions
- **Table names**: snake_case (`inventory_events`, `vendor_metrics`)
- **Column names**: snake_case (`created_at`, `voice_confidence_score`)
- **UUIDs**: Use for all primary keys
- **Timestamps**: Always include `created_at` and `updated_at`
- **User isolation**: All user data must include `user_id` for RLS

## Error Handling
```typescript
// Domain-specific error types
export class VoiceProcessingError extends Error {
  constructor(message: string, public readonly code: string) {
    super(message);
    this.name = 'VoiceProcessingError';
  }
}

// Async error handling
const handleAsyncOperation = async () => {
  try {
    const result = await riskyOperation();
    return result;
  } catch (error) {
    console.error('Operation failed:', error);
    showUserFriendlyMessage('Operation failed. Please try again.');
    throw error; // Re-throw if needed for upper layers
  }
};
```

## Environment Configuration
```typescript
// lib/env.ts - Proper environment variable handling
const env = {
  VITE_SUPABASE_URL: process.env.VITE_SUPABASE_URL!,
  VITE_SUPABASE_ANON_KEY: process.env.VITE_SUPABASE_ANON_KEY!,
  VITE_OPENAI_API_KEY: process.env.VITE_OPENAI_API_KEY!,
  VITE_TEMPSTICK_API_KEY: process.env.VITE_TEMPSTICK_API_KEY!,
};
```