# Pacific Cloud Seafoods (PCS) Seafood Manager

## Project Purpose
A React/TypeScript web application for seafood inventory management, traceability, and HACCP compliance. The application features:
- Voice-based data entry with AI-powered processing
- TempStick sensor integration for temperature monitoring
- Import wizards for data processing
- Comprehensive HACCP compliance tracking
- GDST traceability standards implementation
- Vendor report card system
- Real-time temperature monitoring and automated reporting

## Tech Stack
- **Frontend**: React 18 + TypeScript + Vite
- **Database**: Supabase (PostgreSQL) with Row Level Security
- **Styling**: TailwindCSS + Radix UI components
- **AI Integration**: OpenAI API for voice processing
- **Authentication**: Supabase Auth
- **Forms**: react-hook-form + Zod validation
- **Voice**: react-speech-recognition + OpenAI Whisper
- **Temperature Monitoring**: TempStick API integration
- **Reporting**: PDF generation, Excel exports, Google Sheets API

## Architecture Principles
- Event-Driven Architecture: All inventory operations flow through `inventory_events`
- Single Source of Truth: Supabase as central data store
- Progressive Enhancement: Core functionality works without voice input
- <PERSON>ail Gracefully: Voice errors shouldn't break manual workflows
- KISS (Keep It Simple, Stupid) - Prioritize simple, maintainable solutions
- YAGNI (You Aren't Gonna Need It) - Build features incrementally

## Key Features
- Seafood inventory management with batch tracking
- Voice-powered data entry for hands-free operations
- HACCP compliance logging and monitoring
- GDST traceability for supply chain transparency
- Vendor performance tracking and report cards
- Temperature monitoring with TempStick sensors
- Real-time alerts and automated reporting
- Import/export capabilities for bulk data operations

## Development Environment
- Port: 5177 (fixed with strictPort: true)
- Package Manager: npm
- Node Version: >=18.0.0