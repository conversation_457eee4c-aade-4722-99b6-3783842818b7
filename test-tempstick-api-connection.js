/**
 * Test script to diagnose TempStick API connectivity issues
 * Based on the API documentation analysis
 */

const API_KEY = '03e99232a2794e2ea07fcd8f06ad356f35132396f02133c07a';

// Test 1: Basic API connectivity with correct headers
async function testBasicConnectivity() {
  console.log('\n=== Test 1: Basic API Connectivity ===');
  
  try {
    const response = await fetch('https://tempstickapi.com/api/v1/sensors/all', {
      method: 'GET',
      headers: {
        'X-API-KEY': API_KEY,
        'Accept': 'application/json',
        'User-Agent': 'Seafood-Manager/1.0',
        'Accept-Encoding': 'gzip' // API uses gzip compression
      }
    });

    console.log('Status:', response.status, response.statusText);
    console.log('Headers:', Object.fromEntries(response.headers));
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log('Error response:', errorText);
      return;
    }
    
    const data = await response.json();
    console.log('Success! Data:', JSON.stringify(data, null, 2));
    return data;
    
  } catch (error) {
    console.error('Connection failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Test 2: Test sensors endpoint (as documented)
async function testSensorsEndpoint() {
  console.log('\n=== Test 2: Sensors Endpoint (GET /api/v1/sensors/all) ===');
  
  try {
    const response = await fetch('https://tempstickapi.com/api/v1/sensors/all', {
      method: 'GET',
      headers: {
        'X-API-KEY': API_KEY,
        'Accept': 'application/json',
        'User-Agent': 'Seafood-Manager/1.0'
      }
    });

    console.log('Status:', response.status);
    
    if (!response.ok) {
      console.log('Failed to fetch sensors');
      const errorText = await response.text();
      console.log('Error:', errorText);
      return;
    }
    
    const data = await response.json();
    console.log('Sensors response:', JSON.stringify(data, null, 2));
    
    // If successful, try to get readings for the first sensor
    if (data.sensors && data.sensors.length > 0) {
      const firstSensor = data.sensors[0];
      console.log('\n--- Testing readings for first sensor ---');
      await testReadingsEndpoint(firstSensor.sensor_id);
    }
    
    return data;
    
  } catch (error) {
    console.error('Sensors endpoint failed:', error.message);
  }
}

// Test 3: Test readings endpoint for a specific sensor
async function testReadingsEndpoint(sensorId) {
  console.log(`\n=== Test 3: Readings Endpoint for sensor ${sensorId} ===`);
  
  try {
    const response = await fetch(`https://tempstickapi.com/api/v1/readings/${sensorId}/10`, {
      method: 'GET',
      headers: {
        'X-API-KEY': API_KEY,
        'Accept': 'application/json',
        'User-Agent': 'Seafood-Manager/1.0'
      }
    });

    console.log('Status:', response.status);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log('Error:', errorText);
      return;
    }
    
    const data = await response.json();
    console.log('Readings response:', JSON.stringify(data, null, 2));
    return data;
    
  } catch (error) {
    console.error('Readings endpoint failed:', error.message);
  }
}

// Test 4: Test with different User-Agent to see if it affects results
async function testUserAgentIssue() {
  console.log('\n=== Test 4: User-Agent Issue Test ===');
  
  try {
    // Test with curl-like user agent (should be blocked according to docs)
    const responseWithCurl = await fetch('https://tempstickapi.com/api/v1/sensors/all', {
      headers: {
        'X-API-KEY': API_KEY,
        'User-Agent': 'curl/7.68.0'
      }
    });
    
    console.log('With curl User-Agent:', responseWithCurl.status);
    
    // Test with empty user agent (allowed according to docs)
    const responseWithEmpty = await fetch('https://tempstickapi.com/api/v1/sensors/all', {
      headers: {
        'X-API-KEY': API_KEY,
        'User-Agent': ''
      }
    });
    
    console.log('With empty User-Agent:', responseWithEmpty.status);
    
    // Test with custom user agent
    const responseWithCustom = await fetch('https://tempstickapi.com/api/v1/sensors/all', {
      headers: {
        'X-API-KEY': API_KEY,
        'User-Agent': 'Seafood-Manager/1.0'
      }
    });
    
    console.log('With custom User-Agent:', responseWithCustom.status);
    
  } catch (error) {
    console.error('User-Agent test failed:', error.message);
  }
}

// Test 5: Validate API key format and test authentication
async function testAuthentication() {
  console.log('\n=== Test 5: Authentication Test ===');
  
  console.log('API Key format check:');
  console.log('- Length:', API_KEY.length);
  console.log('- First 8 chars:', API_KEY.substring(0, 8));
  console.log('- Last 8 chars:', API_KEY.substring(-8));
  console.log('- Contains only hex chars:', /^[a-f0-9]+$/.test(API_KEY));
  
  // Test with invalid key
  try {
    const response = await fetch('https://tempstickapi.com/api/v1/sensors/all', {
      headers: {
        'X-API-KEY': 'invalid-key-test',
        'User-Agent': 'Seafood-Manager/1.0'
      }
    });
    
    console.log('Invalid key response status:', response.status);
    
  } catch (error) {
    console.log('Invalid key test error:', error.message);
  }
  
  // Test without key
  try {
    const response = await fetch('https://tempstickapi.com/api/v1/sensors/all', {
      headers: {
        'User-Agent': 'Seafood-Manager/1.0'
      }
    });
    
    console.log('No key response status:', response.status);
    
  } catch (error) {
    console.log('No key test error:', error.message);
  }
}

// Run all tests
async function runAllTests() {
  console.log('🌡️ TempStick API Connection Diagnostic Tool');
  console.log('='.repeat(50));
  
  await testAuthentication();
  await testUserAgentIssue();
  await testBasicConnectivity();
  await testSensorsEndpoint();
  
  console.log('\n' + '='.repeat(50));
  console.log('✅ All tests completed');
}

// Run the tests
runAllTests().catch(console.error);