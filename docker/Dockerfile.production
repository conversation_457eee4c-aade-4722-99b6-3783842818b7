# Multi-stage Dockerfile for Production Deployment
# Optimized for Seafood Manager application

# Stage 1: Build stage
FROM node:18-alpine AS builder

LABEL maintainer="Pacific Cloud Seafoods <<EMAIL>>"
LABEL description="Seafood Manager - Production Build"

# Set working directory
WORKDIR /app

# Install dependencies for native modules
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    && ln -sf python3 /usr/bin/python

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./
COPY vite.config.ts ./
COPY vite.config.production.ts ./

# Install dependencies
RUN npm ci --only=production --frozen-lockfile && \
    npm cache clean --force

# Copy source code
COPY src/ ./src/
COPY public/ ./public/
COPY index.html ./

# Set build environment
ARG NODE_ENV=production
ARG VITE_SUPABASE_URL
ARG VITE_SUPABASE_ANON_KEY
ARG VITE_OPENAI_API_KEY

ENV NODE_ENV=${NODE_ENV}
ENV VITE_SUPABASE_URL=${VITE_SUPABASE_URL}
ENV VITE_SUPABASE_ANON_KEY=${VITE_SUPABASE_ANON_KEY}
ENV VITE_OPENAI_API_KEY=${VITE_OPENAI_API_KEY}

# Build the application using production config
RUN npm run build -- --config vite.config.production.ts

# Stage 2: Production runtime
FROM nginx:1.25-alpine AS production

# Install security updates
RUN apk update && apk upgrade && \
    apk add --no-cache \
    curl \
    ca-certificates && \
    rm -rf /var/cache/apk/*

# Create non-root user for security
RUN addgroup -g 1001 -S seafood && \
    adduser -S seafood -u 1001

# Copy built application
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY docker/nginx.conf /etc/nginx/nginx.conf
COPY docker/default.conf /etc/nginx/conf.d/default.conf

# Copy health check script
COPY docker/healthcheck.sh /usr/local/bin/healthcheck.sh
RUN chmod +x /usr/local/bin/healthcheck.sh

# Set proper permissions
RUN chown -R seafood:seafood /usr/share/nginx/html && \
    chown -R seafood:seafood /var/cache/nginx && \
    chown -R seafood:seafood /var/log/nginx && \
    chown -R seafood:seafood /etc/nginx/conf.d

# Create PID directory with proper permissions
RUN mkdir -p /var/run/nginx && \
    chown -R seafood:seafood /var/run/nginx

# Switch to non-root user
USER seafood

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD /usr/local/bin/healthcheck.sh

# Start nginx
CMD ["nginx", "-g", "daemon off;"]