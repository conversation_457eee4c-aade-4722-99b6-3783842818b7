#!/bin/sh

# Health Check Script for Seafood Manager Production Container
# Validates application availability and basic functionality

set -e

# Configuration
HEALTH_URL="http://localhost:8080/health"
APP_URL="http://localhost:8080"
TIMEOUT=10
MAX_RETRIES=3

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') [HEALTHCHECK] $1"
}

check_nginx() {
    log "Checking Nginx process..."
    if ! pgrep nginx > /dev/null; then
        log "${RED}ERROR: Nginx process not running${NC}"
        return 1
    fi
    log "${GREEN}✓ Nginx process is running${NC}"
    return 0
}

check_health_endpoint() {
    log "Checking health endpoint..."
    
    response=$(curl -f -s --max-time $TIMEOUT "$HEALTH_URL" 2>/dev/null || echo "")
    
    if [ "$response" = "healthy" ]; then
        log "${GREEN}✓ Health endpoint responding correctly${NC}"
        return 0
    else
        log "${RED}ERROR: Health endpoint not responding correctly${NC}"
        log "Response: $response"
        return 1
    fi
}

check_app_availability() {
    log "Checking application availability..."
    
    # Check if we can get the main page
    http_code=$(curl -o /dev/null -s -w "%{http_code}" --max-time $TIMEOUT "$APP_URL" 2>/dev/null || echo "000")
    
    if [ "$http_code" = "200" ]; then
        log "${GREEN}✓ Application is available (HTTP $http_code)${NC}"
        return 0
    else
        log "${RED}ERROR: Application not available (HTTP $http_code)${NC}"
        return 1
    fi
}

check_static_assets() {
    log "Checking static assets..."
    
    # Check if CSS and JS assets are available
    assets_url="$APP_URL/assets"
    http_code=$(curl -o /dev/null -s -w "%{http_code}" --max-time $TIMEOUT "$assets_url/" 2>/dev/null || echo "000")
    
    # 403 is acceptable for directory listing disabled
    if [ "$http_code" = "403" ] || [ "$http_code" = "404" ]; then
        log "${GREEN}✓ Assets directory is protected${NC}"
        return 0
    elif [ "$http_code" = "200" ]; then
        log "${GREEN}✓ Assets are available${NC}"
        return 0
    else
        log "${YELLOW}WARNING: Unexpected assets response (HTTP $http_code)${NC}"
        return 0  # Non-critical
    fi
}

check_memory_usage() {
    log "Checking memory usage..."
    
    # Get memory usage percentage
    mem_usage=$(awk '/MemAvailable/ { available=$2 } /MemTotal/ { total=$2 } END { used=(total-available); printf "%.1f", (used/total)*100 }' /proc/meminfo 2>/dev/null || echo "0")
    
    if [ "$(echo "$mem_usage > 90" | bc 2>/dev/null || echo 0)" = "1" ]; then
        log "${RED}ERROR: High memory usage: ${mem_usage}%${NC}"
        return 1
    elif [ "$(echo "$mem_usage > 75" | bc 2>/dev/null || echo 0)" = "1" ]; then
        log "${YELLOW}WARNING: Elevated memory usage: ${mem_usage}%${NC}"
    else
        log "${GREEN}✓ Memory usage normal: ${mem_usage}%${NC}"
    fi
    
    return 0
}

check_disk_space() {
    log "Checking disk space..."
    
    # Check root filesystem usage
    disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//' 2>/dev/null || echo "0")
    
    if [ "$disk_usage" -gt 90 ]; then
        log "${RED}ERROR: High disk usage: ${disk_usage}%${NC}"
        return 1
    elif [ "$disk_usage" -gt 75 ]; then
        log "${YELLOW}WARNING: Elevated disk usage: ${disk_usage}%${NC}"
    else
        log "${GREEN}✓ Disk usage normal: ${disk_usage}%${NC}"
    fi
    
    return 0
}

run_health_checks() {
    log "Starting health checks for Seafood Manager..."
    
    local failures=0
    
    # Critical checks
    check_nginx || failures=$((failures + 1))
    check_health_endpoint || failures=$((failures + 1))
    check_app_availability || failures=$((failures + 1))
    
    # Non-critical checks (warnings only)
    check_static_assets || true
    check_memory_usage || true
    check_disk_space || true
    
    if [ $failures -eq 0 ]; then
        log "${GREEN}✓ All critical health checks passed${NC}"
        return 0
    else
        log "${RED}✗ $failures critical health check(s) failed${NC}"
        return 1
    fi
}

# Main execution with retry logic
main() {
    local attempt=1
    
    while [ $attempt -le $MAX_RETRIES ]; do
        log "Health check attempt $attempt/$MAX_RETRIES"
        
        if run_health_checks; then
            log "${GREEN}Health check successful${NC}"
            exit 0
        fi
        
        if [ $attempt -lt $MAX_RETRIES ]; then
            log "${YELLOW}Health check failed, retrying in 5 seconds...${NC}"
            sleep 5
        fi
        
        attempt=$((attempt + 1))
    done
    
    log "${RED}Health check failed after $MAX_RETRIES attempts${NC}"
    exit 1
}

# Run health checks
main "$@"