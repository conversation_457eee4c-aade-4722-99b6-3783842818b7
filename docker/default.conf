# Nginx Default Site Configuration for Seafood Manager
server {
    listen 8080;
    server_name _;
    root /usr/share/nginx/html;
    index index.html;
    
    # Seafood Manager specific configuration
    # Optimized for SPA with API integration
    
    # Security and performance
    server_tokens off;
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";
    
    # Main application route
    location / {
        try_files $uri $uri/ /index.html;
        
        # Cache control for HTML
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
    
    # Static assets with aggressive caching
    location /assets/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options "nosniff";
        
        # Enable gzip for assets
        gzip_static on;
        
        # Security headers for assets
        add_header Cross-Origin-Resource-Policy "cross-origin";
    }
    
    # API proxy (if needed for backend)
    location /api/ {
        # Rate limiting for API endpoints
        limit_req zone=api burst=20 nodelay;
        
        # Proxy headers
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Proxy timeouts
        proxy_connect_timeout 5s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        
        # Disable caching for API responses
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # Metrics endpoint for monitoring
    location /metrics {
        access_log off;
        
        # Restrict access to monitoring systems
        allow 10.0.0.0/8;
        allow **********/12;
        allow ***********/16;
        deny all;
        
        # Return basic metrics
        return 200 "# HELP nginx_up Whether nginx is up\n# TYPE nginx_up gauge\nnginx_up 1\n";
        add_header Content-Type text/plain;
    }
    
    # Voice processing uploads (larger file size limit)
    location /upload/voice/ {
        client_max_body_size 10M;
        limit_req zone=api burst=5 nodelay;
        
        # Timeout settings for file uploads
        client_body_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # CSV import uploads (large file support)
    location /upload/csv/ {
        client_max_body_size 50M;
        limit_req zone=api burst=2 nodelay;
        
        # Extended timeouts for large CSV processing
        client_body_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }
    
    # Security: Deny access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Favicon handling
    location = /favicon.ico {
        log_not_found off;
        access_log off;
        expires 1M;
        add_header Cache-Control "public, immutable";
    }
    
    # Robots.txt
    location = /robots.txt {
        log_not_found off;
        access_log off;
        expires 1M;
        add_header Cache-Control "public";
    }
    
    # Font files with CORS headers
    location ~* \.(woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, OPTIONS";
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range";
    }
    
    # Image optimization
    location ~* \.(png|jpg|jpeg|gif|svg|webp|ico)$ {
        expires 1M;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
        
        # Enable WebP serving if supported
        location ~* \.(png|jpg|jpeg)$ {
            add_header Vary "Accept";
            try_files $uri.webp $uri =404;
        }
    }
    
    # Error pages
    error_page 404 /index.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /50x.html {
        root /usr/share/nginx/html;
        internal;
    }
}