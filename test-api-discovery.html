<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TempStick API Discovery</title>
    <style>
        body {
            font-family: monospace;
            padding: 20px;
            background: #1a1a1a;
            color: #0f0;
        }
        button {
            background: #333;
            color: #0f0;
            border: 1px solid #0f0;
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #444;
        }
        pre {
            background: #000;
            padding: 10px;
            border: 1px solid #333;
            overflow-x: auto;
        }
        .error { color: #f00; }
        .success { color: #0f0; }
        .warning { color: #ff0; }
    </style>
</head>
<body>
    <h1>TempStick API Discovery</h1>
    <p>Testing with corrected domain: api.tempstick.com</p>
    
    <h2>Through Proxy (localhost:5177)</h2>
    <div>
        <button onclick="testEndpoint('/api/tempstick/api/v1/sensors')">Test /api/tempstick/api/v1/sensors</button>
        <button onclick="testEndpoint('/api/tempstick/api/v1/sensors/all')">Test /api/tempstick/api/v1/sensors/all</button>
        <button onclick="testEndpoint('/api/tempstick/api/sensors')">Test /api/tempstick/api/sensors</button>
    </div>
    
    <h2>Direct API Calls</h2>
    <div>
        <button onclick="testDirect('https://api.tempstick.com/api/v1/sensors')">Direct: /api/v1/sensors</button>
        <button onclick="testDirect('https://api.tempstick.com/v1/sensors')">Direct: /v1/sensors</button>
        <button onclick="testDirect('https://api.tempstick.com/sensors')">Direct: /sensors</button>
    </div>
    
    <pre id="output"></pre>

    <script>
        const API_KEY = '03e99232a2794e2ea07fcd8f06ad356f35132396f02133c07a';
        
        async function testEndpoint(url) {
            const output = document.getElementById('output');
            output.innerHTML = `<span class="warning">Testing proxy endpoint: ${url}</span>\n\n`;
            
            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${API_KEY}`,
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });
                
                const statusClass = response.ok ? 'success' : 'error';
                output.innerHTML += `<span class="${statusClass}">Status: ${response.status} ${response.statusText}</span>\n`;
                
                const text = await response.text();
                try {
                    const json = JSON.parse(text);
                    output.innerHTML += `Response:\n${JSON.stringify(json, null, 2)}`;
                } catch {
                    output.innerHTML += `Response (text):\n${text.substring(0, 500)}${text.length > 500 ? '...' : ''}`;
                }
            } catch (error) {
                output.innerHTML += `<span class="error">Error: ${error.message}</span>`;
            }
        }
        
        async function testDirect(url) {
            const output = document.getElementById('output');
            output.innerHTML = `<span class="warning">Testing direct: ${url}</span>\n\n`;
            
            try {
                // Note: This will likely fail due to CORS, but we can see the error
                const response = await fetch(url, {
                    method: 'GET',
                    mode: 'cors',
                    headers: {
                        'Authorization': `Bearer ${API_KEY}`,
                        'X-API-KEY': API_KEY,
                        'Accept': 'application/json'
                    }
                });
                
                const statusClass = response.ok ? 'success' : 'error';
                output.innerHTML += `<span class="${statusClass}">Status: ${response.status} ${response.statusText}</span>\n`;
                
                const text = await response.text();
                try {
                    const json = JSON.parse(text);
                    output.innerHTML += `Response:\n${JSON.stringify(json, null, 2)}`;
                } catch {
                    output.innerHTML += `Response (text):\n${text.substring(0, 500)}${text.length > 500 ? '...' : ''}`;
                }
            } catch (error) {
                output.innerHTML += `<span class="error">Error: ${error.message}</span>\n`;
                if (error.message.includes('CORS')) {
                    output.innerHTML += `<span class="warning">Note: CORS error is expected for direct calls. Use proxy endpoints instead.</span>`;
                }
            }
        }
    </script>
</body>
</html>
