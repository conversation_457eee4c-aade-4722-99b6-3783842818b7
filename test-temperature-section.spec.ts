import { test, expect } from '@playwright/test';

test.describe('Temperature/TempStick Section Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('http://localhost:5177');

    // Wait for the page to load
    await page.waitForLoadState('networkidle');
  });

  test('should load the main application', async ({ page }) => {
    // Check if the application loads successfully
    await expect(page).toHaveTitle(/Seafood Manager|Pacific Cloud|Voice Seafood/i);
    console.log('✅ Application loaded successfully');
  });

  test('should find temperature/TempStick related elements', async ({ page }) => {
    // Look for temperature-related text or components
    const tempElements = await page.locator('text=/temperature|tempstick|sensor|TEMPERATURE|TEMPSTICK|SENSOR/i').all();

    if (tempElements.length > 0) {
      console.log(`✅ Found ${tempElements.length} temperature-related elements`);
      for (const element of tempElements) {
        const text = await element.textContent();
        console.log(`  - ${text?.trim()}`);
      }
    } else {
      console.log('ℹ️  No temperature-related elements found on main page');
    }
  });

  test('should check for TempStick sidebar navigation', async ({ page }) => {
    // Look for sidebar navigation items
    const sidebarItems = await page.locator('[data-testid="sidebar"] a, .sidebar a, nav a').all();

    let tempStickFound = false;
    for (const item of sidebarItems) {
      const text = await item.textContent();
      if (text && /tempstick|temperature|sensor|TEMPSTICK|TEMPERATURE|SENSOR/i.test(text)) {
        console.log(`✅ Found TempStick navigation: ${text.trim()}`);
        tempStickFound = true;

        // Try to click on the TempStick link
        await item.click();
        await page.waitForLoadState('networkidle');

        // Check if we're on a TempStick page
        const currentUrl = page.url();
        console.log(`📍 Navigated to: ${currentUrl}`);

        break;
      }
    }

    if (!tempStickFound) {
      console.log('ℹ️  No TempStick sidebar navigation found');
    }
  });

  test('should test TempStickDataSourceSelector component', async ({ page }) => {
    // Look for the TempStickDataSourceSelector component
    const selector = page.locator('[data-testid="tempstick-selector"], .tempstick-selector, [class*="TempStickDataSource"]').first();

    if (await selector.isVisible()) {
      console.log('✅ TempStickDataSourceSelector component found');

      // Take a screenshot for debugging
      await page.screenshot({ path: 'tempstick-selector.png', fullPage: true });

      // Look for buttons or interactive elements
      const buttons = await selector.locator('button').all();
      console.log(`📋 Found ${buttons.length} buttons in selector`);

      for (const button of buttons) {
        const buttonText = await button.textContent();
        console.log(`  - Button: ${buttonText?.trim()}`);
      }
    } else {
      console.log('ℹ️  TempStickDataSourceSelector component not visible');
    }
  });

  test('should check for temperature dashboard', async ({ page }) => {
    // Look for temperature dashboard components
    const dashboardSelectors = [
      '[data-testid="temperature-dashboard"]',
      '.temperature-dashboard',
      '[class*="TemperatureDashboard"]',
      '[data-testid="sensor-dashboard"]',
      '.sensor-dashboard',
      '[class*="SensorDashboard"]'
    ];

    let dashboardFound = false;
    for (const selector of dashboardSelectors) {
      const element = page.locator(selector).first();
      if (await element.isVisible()) {
        console.log(`✅ Temperature dashboard found: ${selector}`);
        dashboardFound = true;

        // Take a screenshot
        await page.screenshot({ path: 'temperature-dashboard.png', fullPage: true });

        break;
      }
    }

    if (!dashboardFound) {
      console.log('ℹ️  No temperature dashboard components visible');
    }
  });

  test('should test for error messages', async ({ page }) => {
    // Look for error messages related to TempStick
    const errorElements = await page.locator('text=/error|Error|failed|Failed|400|404|500/i').all();

    if (errorElements.length > 0) {
      console.log(`⚠️  Found ${errorElements.length} potential error messages:`);
      for (const error of errorElements) {
        const text = await error.textContent();
        console.log(`  - ${text?.trim()}`);
      }
    } else {
      console.log('✅ No error messages found');
    }
  });

  test('should check console for errors', async ({ page }) => {
    // Listen for console errors
    const errors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });

    // Wait a bit for any errors to appear
    await page.waitForTimeout(2000);

    if (errors.length > 0) {
      console.log(`⚠️  Console errors found:`);
      errors.forEach(error => console.log(`  - ${error}`));
    } else {
      console.log('✅ No console errors found');
    }
  });
});
