import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('Missing Supabase environment variables.');
}

const adminSupabase = createClient(supabaseUrl, supabaseServiceKey);

async function readTablesFromSupabase() {
  try {
    // Get all tables in the public schema
    const { data: tables, error: tablesError } = await adminSupabase.rpc('get_tables');
    
    if (tablesError) {
      console.error('Error fetching tables:', tablesError);
      
      // Try alternative approach with a direct SQL query
      const { data: tablesAlt, error: tablesAltError } = await adminSupabase.from('_tables').select('*');
      
      if (tablesAltError) {
        console.error('Error with alternative table fetch:', tablesAltError);
        
        // Last resort - try a direct SQL query to get tables
        const { data: tablesSql, error: tablesSqlError } = await adminSupabase.rpc('pg_catalog.pg_tables').select('*').eq('schemaname', 'public');
        
        if (tablesSqlError) {
          console.error('Could not fetch tables using any method:', tablesSqlError);
          return;
        }
        
        console.log('Tables (SQL method):', tablesSql);
        return;
      }
      
      console.log('Tables (alternative method):', tablesAlt);
      return;
    }
    
    console.log('Tables:', tables);
    
    // For each table, get its columns
    if (tables && tables.length > 0) {
      for (const table of tables) {
        const tableName = table.table_name || table.name;
        
        // Get columns for this table
        const { data: columns, error: columnsError } = await adminSupabase
          .from('_columns')
          .select('*')
          .eq('table_name', tableName);
          
        if (columnsError) {
          console.error(`Error fetching columns for table ${tableName}:`, columnsError);
          continue;
        }
        
        console.log(`Columns for table ${tableName}:`, columns);
        
        // Get a sample row to understand the data
        const { data: sampleData, error: sampleError } = await adminSupabase
          .from(tableName)
          .select('*')
          .limit(1);
          
        if (sampleError) {
          console.error(`Error fetching sample data for table ${tableName}:`, sampleError);
          continue;
        }
        
        console.log(`Sample data for table ${tableName}:`, sampleData);
      }
    }
    
  } catch (error) {
    console.error('Error reading from Supabase:', error);
  }
}

// Alternative approach using direct SQL
async function readTablesWithSQL() {
  try {
    const { data, error } = await adminSupabase.rpc('query_sql', {
      sql_query: `
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
      `
    });
    
    if (error) {
      console.error('Error executing SQL query:', error);
      return;
    }
    
    console.log('Tables from SQL query:', data);
    
    // For each table, get its columns
    if (data && data.length > 0) {
      for (const tableRow of data) {
        const tableName = tableRow.table_name;
        
        const { data: columnData, error: columnError } = await adminSupabase.rpc('query_sql', {
          sql_query: `
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns
            WHERE table_schema = 'public' AND table_name = '${tableName}'
          `
        });
        
        if (columnError) {
          console.error(`Error getting columns for ${tableName}:`, columnError);
          continue;
        }
        
        console.log(`Columns for ${tableName}:`, columnData);
      }
    }
    
  } catch (error) {
    console.error('Error with SQL approach:', error);
  }
}

// Try both approaches
async function fetchDatabaseStructure() {
  console.log('Attempting to read Supabase structure...');
  
  try {
    await readTablesFromSupabase();
  } catch (error) {
    console.error('First method failed:', error);
  }
  
  console.log('\nTrying SQL approach...');
  
  try {
    await readTablesWithSQL();
  } catch (error) {
    console.error('SQL approach failed:', error);
  }
}

fetchDatabaseStructure();
