<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TempStick API Discovery</title>
    <style>
        body {
            font-family: monospace;
            padding: 20px;
            background: #1a1a1a;
            color: #0f0;
        }
        .test-group {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
        }
        button {
            background: #333;
            color: #0f0;
            border: 1px solid #0f0;
            padding: 8px 15px;
            margin: 5px;
            cursor: pointer;
            font-size: 12px;
        }
        button:hover {
            background: #444;
        }
        pre {
            background: #000;
            padding: 10px;
            border: 1px solid #333;
            overflow-x: auto;
            max-height: 400px;
            overflow-y: auto;
        }
        .error { color: #f00; }
        .success { color: #0f0; }
        .warning { color: #ff0; }
        h3 { color: #ff0; }
    </style>
</head>
<body>
    <h1>TempStick API Endpoint Discovery</h1>
    <p>API Key: 03e99232a2794e2ea07fcd8f06ad356f35132396f02133c07a</p>
    
    <div class="test-group">
        <h3>1. Test Endpoint Patterns (with X-API-KEY only)</h3>
        <button onclick="testWithApiKey('/api/tempstick/sensor')">sensor (no version)</button>
        <button onclick="testWithApiKey('/api/tempstick/sensors')">sensors (no version)</button>
        <button onclick="testWithApiKey('/api/tempstick/v1/sensor')">v1/sensor</button>
        <button onclick="testWithApiKey('/api/tempstick/v1/sensors')">v1/sensors</button>
        <button onclick="testWithApiKey('/api/tempstick/api/sensor')">api/sensor</button>
        <button onclick="testWithApiKey('/api/tempstick/api/sensors')">api/sensors</button>
        <button onclick="testWithApiKey('/api/tempstick/api/v1/sensor')">api/v1/sensor</button>
        <button onclick="testWithApiKey('/api/tempstick/api/v1/sensors')">api/v1/sensors</button>
    </div>

    <div class="test-group">
        <h3>2. Test with Both Headers (X-API-KEY + Bearer)</h3>
        <button onclick="testWithBoth('/api/tempstick/sensor')">sensor</button>
        <button onclick="testWithBoth('/api/tempstick/sensors')">sensors</button>
        <button onclick="testWithBoth('/api/tempstick/v1/sensors')">v1/sensors</button>
        <button onclick="testWithBoth('/api/tempstick/api/v1/sensors')">api/v1/sensors</button>
    </div>

    <div class="test-group">
        <h3>3. Test Root Endpoints</h3>
        <button onclick="testWithApiKey('/api/tempstick/')">Root /</button>
        <button onclick="testWithApiKey('/api/tempstick/api/')">Root /api/</button>
        <button onclick="testWithApiKey('/api/tempstick/api/v1/')">Root /api/v1/</button>
    </div>

    <div class="test-group">
        <h3>4. Test Specific Sensor IDs</h3>
        <button onclick="testWithApiKey('/api/tempstick/api/v1/sensor/1')">sensor/1</button>
        <button onclick="testWithApiKey('/api/tempstick/api/v1/sensors/1')">sensors/1</button>
        <button onclick="testWithApiKey('/api/tempstick/api/sensor/1')">api/sensor/1</button>
    </div>

    <pre id="output"></pre>

    <script>
        const API_KEY = '03e99232a2794e2ea07fcd8f06ad356f35132396f02133c07a';
        
        async function testWithApiKey(url) {
            await testEndpoint(url, {
                'X-API-KEY': API_KEY,
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }, 'X-API-KEY only');
        }
        
        async function testWithBoth(url) {
            await testEndpoint(url, {
                'X-API-KEY': API_KEY,
                'Authorization': `Bearer ${API_KEY}`,
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }, 'X-API-KEY + Bearer');
        }
        
        async function testEndpoint(url, headers, headerType) {
            const output = document.getElementById('output');
            output.innerHTML = `<span class="warning">Testing: ${url}</span>\n`;
            output.innerHTML += `<span class="warning">Headers: ${headerType}</span>\n\n`;
            
            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: headers
                });
                
                const statusClass = response.ok ? 'success' : 'error';
                output.innerHTML += `<span class="${statusClass}">Status: ${response.status} ${response.statusText}</span>\n`;
                
                // Show relevant response headers
                const importantHeaders = ['content-type', 'x-limonade', 'server'];
                output.innerHTML += 'Response Headers:\n';
                importantHeaders.forEach(h => {
                    const value = response.headers.get(h);
                    if (value) {
                        output.innerHTML += `  ${h}: ${value}\n`;
                    }
                });
                output.innerHTML += '\n';
                
                const text = await response.text();
                try {
                    const json = JSON.parse(text);
                    output.innerHTML += `<span class="${json.type === 'error' ? 'error' : 'success'}">Response JSON:</span>\n`;
                    output.innerHTML += JSON.stringify(json, null, 2);
                } catch {
                    // If not JSON, show first 500 chars
                    const preview = text.substring(0, 500);
                    output.innerHTML += `Response (text preview):\n${preview}${text.length > 500 ? '...' : ''}`;
                }
            } catch (error) {
                output.innerHTML += `<span class="error">Error: ${error.message}</span>`;
            }
            
            output.innerHTML += '\n' + '='.repeat(80) + '\n\n';
        }
    </script>
</body>
</html>
