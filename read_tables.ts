import { createClient } from '@supabase/supabase-js';

async function readTables() {
  const supabaseUrl = 'https://puzjricwpsjusjlgrwen.supabase.co';
  const supabaseAnonKey =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB1empyaWN3cHNqdXNqbGdyd2VuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjUxMTUzODQsImV4cCI6MjA0MDY5MTM4NH0.Z-QOwukWPIEGbRl-mZck_4-Oa-7YKU93gxfIWy7xvgM';

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error(
      'Missing Supabase environment variables. Please check your .env file.'
    );
    return;
  }

  const supabase = createClient(supabaseUrl, supabaseAnonKey);

  const { data: tables, error: tablesError } = await supabase.from('pg_tables').select('tablename').eq('schemaname', 'public');

  if (tablesError) {
    console.error('Error fetching tables:', tablesError);
    return;
  }

  if (!tables) {
      console.log('No tables found in the public schema.');
      return;
  }

  for (const table of tables) {
    console.log(`\nTable: ${table.tablename}`);
    const { data, error } = await supabase.from(table.tablename).select('*');

    if (error) {
      console.error(`Error fetching data from ${table.tablename}:`, error);
      continue;
    }

    console.table(data);
  }
}

readTables();
