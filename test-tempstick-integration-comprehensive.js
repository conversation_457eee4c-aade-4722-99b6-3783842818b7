#!/usr/bin/env node

/**
 * Comprehensive TempStick Integration Test Script
 * 
 * Tests all aspects of the TempStick API integration including:
 * - API endpoint connectivity and authentication
 * - Sensor discovery and data retrieval
 * - Historical data processing
 * - Alert management functionality
 * - Export capabilities
 * - Error handling and edge cases
 * - Performance benchmarks
 */

import { config } from 'dotenv';
import { tempStickService } from './src/lib/tempstick-service.js';
import { temperatureExportService } from './src/lib/temperature-export-service.js';

// Load environment variables
config();

// Test configuration
const testConfig = {
  apiKey: process.env.VITE_TEMPSTICK_API_KEY,
  proxyUrl: process.env.VITE_TEMPSTICK_PROXY_URL || 'http://localhost:3001',
  testTimeout: 30000, // 30 seconds
  maxRetries: 3,
  verbose: true
};

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  total: 0,
  details: [],
  performance: {},
  errors: []
};

// Test utilities
class TestRunner {
  constructor(config) {
    this.config = config;
    this.startTime = Date.now();
  }

  log(message, level = 'info') {
    if (!this.config.verbose && level === 'debug') return;
    
    const timestamp = new Date().toISOString();
    const prefix = {
      info: '📋',
      success: '✅',
      error: '❌',
      warning: '⚠️',
      debug: '🔍'
    }[level] || '📋';
    
    console.log(`${prefix} [${timestamp}] ${message}`);
  }

  async runTest(testName, testFunction) {
    testResults.total++;
    const startTime = Date.now();
    
    try {
      this.log(`Starting test: ${testName}`, 'info');
      
      const result = await Promise.race([
        testFunction(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Test timeout')), this.config.testTimeout)
        )
      ]);
      
      const duration = Date.now() - startTime;
      testResults.passed++;
      testResults.performance[testName] = duration;
      
      testResults.details.push({
        name: testName,
        status: 'PASSED',
        duration,
        result
      });
      
      this.log(`✅ Test passed: ${testName} (${duration}ms)`, 'success');
      return result;
      
    } catch (error) {
      const duration = Date.now() - startTime;
      testResults.failed++;
      testResults.errors.push({ test: testName, error: error.message });
      
      testResults.details.push({
        name: testName,
        status: 'FAILED',
        duration,
        error: error.message
      });
      
      this.log(`❌ Test failed: ${testName} - ${error.message}`, 'error');
      throw error;
    }
  }

  async runTestSuite(suiteName, tests) {
    this.log(`\n🧪 Starting test suite: ${suiteName}`, 'info');
    this.log('='.repeat(50), 'info');
    
    const results = [];
    for (const [testName, testFunction] of Object.entries(tests)) {
      try {
        const result = await this.runTest(testName, testFunction);
        results.push(result);
      } catch (error) {
        // Continue with other tests even if one fails
        results.push(null);
      }
    }
    
    return results;
  }

  generateReport() {
    const totalDuration = Date.now() - this.startTime;
    
    this.log('\n📊 Test Results Summary', 'info');
    this.log('='.repeat(50), 'info');
    this.log(`Total Tests: ${testResults.total}`, 'info');
    this.log(`Passed: ${testResults.passed}`, 'success');
    this.log(`Failed: ${testResults.failed}`, testResults.failed > 0 ? 'error' : 'success');
    this.log(`Success Rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`, 'info');
    this.log(`Total Duration: ${totalDuration}ms`, 'info');
    
    if (testResults.failed > 0) {
      this.log('\n❌ Failed Tests:', 'error');
      testResults.errors.forEach(error => {
        this.log(`  - ${error.test}: ${error.error}`, 'error');
      });
    }
    
    this.log('\n⏱️  Performance Metrics:', 'info');
    Object.entries(testResults.performance).forEach(([test, duration]) => {
      const status = duration > 5000 ? '🐌' : duration > 2000 ? '⚡' : '🚀';
      this.log(`  ${status} ${test}: ${duration}ms`, 'info');
    });
    
    return testResults;
  }
}

// Initialize test runner
const testRunner = new TestRunner(testConfig);

/**
 * Pre-flight checks
 */
const preflightTests = {
  'Environment Variables': async () => {
    const requiredVars = ['VITE_TEMPSTICK_API_KEY'];
    const missingVars = requiredVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
      throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
    }
    
    return {
      apiKey: process.env.VITE_TEMPSTICK_API_KEY ? '✅ Present' : '❌ Missing',
      proxyUrl: testConfig.proxyUrl
    };
  },

  'Service Initialization': async () => {
    if (!tempStickService) {
      throw new Error('TempStick service not initialized');
    }
    
    const metrics = tempStickService.getServiceMetrics();
    return {
      serviceInitialized: true,
      metrics: metrics
    };
  }
};

/**
 * API connectivity tests
 */
const connectivityTests = {
  'API Authentication': async () => {
    const testResult = await tempStickService.apiClient?.testConnection();
    
    if (!testResult?.success) {
      throw new Error(`API connection failed: ${testResult?.error || 'Unknown error'}`);
    }
    
    return {
      connected: true,
      latency: testResult.latency,
      authenticated: testResult.success
    };
  },

  'Proxy Server Health': async () => {
    try {
      const response = await fetch(`${testConfig.proxyUrl}/health`);
      const isHealthy = response.ok;
      
      return {
        proxyUrl: testConfig.proxyUrl,
        status: response.status,
        healthy: isHealthy
      };
    } catch (error) {
      // Proxy health endpoint might not exist, try a basic request
      try {
        const response = await fetch(`${testConfig.proxyUrl}/api/v1/sensors/all`, {
          headers: {
            'X-API-KEY': testConfig.apiKey
          }
        });
        
        return {
          proxyUrl: testConfig.proxyUrl,
          status: response.status,
          healthy: response.status !== 404
        };
      } catch (proxyError) {
        return {
          proxyUrl: testConfig.proxyUrl,
          status: 'offline',
          healthy: false,
          error: proxyError.message
        };
      }
    }
  }
};

/**
 * Core functionality tests
 */
const functionalityTests = {
  'Get All Sensors': async () => {
    const sensors = await tempStickService.getAllSensors();
    
    if (!Array.isArray(sensors)) {
      throw new Error('Sensors result is not an array');
    }
    
    testRunner.log(`Found ${sensors.length} sensors`, 'debug');
    
    return {
      sensorCount: sensors.length,
      sensors: sensors.map(s => ({
        id: s.sensor_id,
        name: s.sensor_name,
        status: s.offline === '1' ? 'offline' : 'online'
      }))
    };
  },

  'Get Sensor Readings': async () => {
    const sensors = await tempStickService.getAllSensors();
    
    if (sensors.length === 0) {
      return { message: 'No sensors available for readings test' };
    }
    
    const testSensor = sensors[0];
    const readings = await tempStickService.getLatestReadings(testSensor.sensor_id, 10);
    
    if (!Array.isArray(readings)) {
      throw new Error('Readings result is not an array');
    }
    
    testRunner.log(`Retrieved ${readings.length} readings for sensor ${testSensor.sensor_name}`, 'debug');
    
    return {
      sensorId: testSensor.sensor_id,
      sensorName: testSensor.sensor_name,
      readingsCount: readings.length,
      latestReading: readings[0] || null
    };
  },

  'Batch Sensor Data': async () => {
    const sensors = await tempStickService.getAllSensors();
    
    if (sensors.length === 0) {
      return { message: 'No sensors available for batch test' };
    }
    
    const sensorIds = sensors.slice(0, 3).map(s => s.sensor_id); // Test first 3 sensors
    const batchResults = await tempStickService.apiClient?.getBatchLatestReadings(sensorIds);
    
    if (!batchResults || typeof batchResults !== 'object') {
      throw new Error('Batch results is not an object');
    }
    
    const totalReadings = Object.values(batchResults).reduce((sum, readings) => sum + readings.length, 0);
    
    return {
      sensorCount: sensorIds.length,
      totalReadings,
      results: Object.keys(batchResults).map(sensorId => ({
        sensorId,
        readingsCount: batchResults[sensorId].length
      }))
    };
  },

  'Historical Data Retrieval': async () => {
    const sensors = await tempStickService.getAllSensors();
    
    if (sensors.length === 0) {
      return { message: 'No sensors available for historical test' };
    }
    
    const testSensor = sensors[0];
    const endDate = new Date();
    const startDate = new Date(endDate.getTime() - 24 * 60 * 60 * 1000); // 24 hours ago
    
    try {
      const historicalReadings = await tempStickService.apiClient?.getReadingsForPeriod(
        testSensor.sensor_id,
        startDate,
        endDate
      );
      
      return {
        sensorId: testSensor.sensor_id,
        sensorName: testSensor.sensor_name,
        dateRange: {
          start: startDate.toISOString(),
          end: endDate.toISOString()
        },
        readingsCount: historicalReadings?.length || 0
      };
    } catch (error) {
      // Historical endpoint might not be available, return info
      return {
        sensorId: testSensor.sensor_id,
        sensorName: testSensor.sensor_name,
        error: error.message,
        fallbackToLatest: true
      };
    }
  }
};

/**
 * Advanced feature tests
 */
const advancedTests = {
  'Alerts Functionality': async () => {
    try {
      const alerts = await tempStickService.apiClient?.getAlerts();
      
      return {
        alertsCount: alerts?.length || 0,
        alertsAvailable: Array.isArray(alerts)
      };
    } catch (error) {
      return {
        alertsCount: 0,
        alertsAvailable: false,
        error: error.message
      };
    }
  },

  'Notifications Functionality': async () => {
    try {
      const notifications = await tempStickService.apiClient?.getNotifications();
      
      return {
        notificationsCount: notifications?.length || 0,
        notificationsAvailable: Array.isArray(notifications)
      };
    } catch (error) {
      return {
        notificationsCount: 0,
        notificationsAvailable: false,
        error: error.message
      };
    }
  },

  'System Health Check': async () => {
    const healthCheck = await tempStickService.performHealthCheck();
    
    return {
      apiStatus: healthCheck.tempstickApi.status,
      apiLatency: healthCheck.tempstickApi.latency,
      databaseStatus: healthCheck.database.status,
      sensorsCount: healthCheck.sensors.total,
      onlineSensors: healthCheck.sensors.online,
      activeAlerts: healthCheck.alerts.active
    };
  }
};

/**
 * Data processing and export tests
 */
const dataProcessingTests = {
  'Data Validation': async () => {
    const sensors = await tempStickService.getAllSensors();
    
    if (sensors.length === 0) {
      return { message: 'No sensors available for validation test' };
    }
    
    const testSensor = sensors[0];
    const readings = await tempStickService.getLatestReadings(testSensor.sensor_id, 5);
    
    // Validate data structure
    const validReadings = readings.filter(reading => {
      return (
        reading &&
        typeof reading.temperature === 'number' &&
        !isNaN(reading.temperature) &&
        reading.timestamp &&
        reading.sensor_id
      );
    });
    
    return {
      totalReadings: readings.length,
      validReadings: validReadings.length,
      validationRate: readings.length > 0 ? (validReadings.length / readings.length) * 100 : 0,
      sampleReading: validReadings[0] || null
    };
  },

  'Export Service Test': async () => {
    const sensors = await tempStickService.getAllSensors();
    
    if (sensors.length === 0) {
      return { message: 'No sensors available for export test' };
    }
    
    // Create mock data for export test
    const mockReadings = [
      {
        id: 'test-1',
        sensor_id: sensors[0].sensor_id,
        temperature: 35.5,
        humidity: 65.2,
        recorded_at: new Date().toISOString(),
        alert_triggered: false,
        created_at: new Date().toISOString()
      }
    ];
    
    const mockSensors = [
      {
        id: sensors[0].sensor_id,
        tempstick_sensor_id: sensors[0].sensor_id,
        name: sensors[0].sensor_name,
        location: 'Test Location',
        sensor_type: 'temperature_humidity' as const,
        temp_min_threshold: null,
        temp_max_threshold: null,
        humidity_min_threshold: null,
        humidity_max_threshold: null,
        storage_area_id: null,
        active: true,
        created_at: new Date().toISOString()
      }
    ];
    
    // Test CSV export functionality
    try {
      const exportResult = await temperatureExportService.exportData(
        mockReadings,
        mockSensors,
        [],
        undefined,
        {
          format: 'csv',
          includeCharts: false,
          includeStatistics: true,
          includeAlerts: false,
          includeHACCPData: true,
          sensorIds: [sensors[0].sensor_id],
          dateRange: {
            start: new Date(Date.now() - 24 * 60 * 60 * 1000),
            end: new Date()
          },
          temperatureUnit: 'fahrenheit',
          timezone: 'America/New_York'
        }
      );
      
      return {
        exportSuccessful: exportResult.success,
        filename: exportResult.filename,
        recordCount: exportResult.metadata.recordCount,
        error: exportResult.error
      };
    } catch (error) {
      return {
        exportSuccessful: false,
        error: error.message
      };
    }
  }
};

/**
 * Performance and stress tests
 */
const performanceTests = {
  'Concurrent Requests': async () => {
    const sensors = await tempStickService.getAllSensors();
    
    if (sensors.length === 0) {
      return { message: 'No sensors available for concurrency test' };
    }
    
    const testSensor = sensors[0];
    const concurrentRequests = 5;
    const startTime = Date.now();
    
    // Make concurrent requests
    const promises = Array(concurrentRequests).fill(null).map(() =>
      tempStickService.getLatestReadings(testSensor.sensor_id, 5)
    );
    
    const results = await Promise.allSettled(promises);
    const successful = results.filter(r => r.status === 'fulfilled').length;
    const failed = results.filter(r => r.status === 'rejected').length;
    const duration = Date.now() - startTime;
    
    return {
      concurrentRequests,
      successful,
      failed,
      duration,
      averageResponseTime: duration / concurrentRequests,
      successRate: (successful / concurrentRequests) * 100
    };
  },

  'Rate Limiting Behavior': async () => {
    const sensors = await tempStickService.getAllSensors();
    
    if (sensors.length === 0) {
      return { message: 'No sensors available for rate limiting test' };
    }
    
    const testSensor = sensors[0];
    const rapidRequests = 10;
    const requestTimes = [];
    
    // Make rapid requests to test rate limiting
    for (let i = 0; i < rapidRequests; i++) {
      const startTime = Date.now();
      try {
        await tempStickService.getLatestReadings(testSensor.sensor_id, 1);
        requestTimes.push(Date.now() - startTime);
      } catch (error) {
        if (error.message.includes('rate limit') || error.message.includes('429')) {
          return {
            rateLimitingActive: true,
            requestsBeforeLimit: i,
            averageResponseTime: requestTimes.length > 0 ? requestTimes.reduce((a, b) => a + b) / requestTimes.length : 0
          };
        }
        throw error;
      }
    }
    
    return {
      rateLimitingActive: false,
      completedRequests: rapidRequests,
      averageResponseTime: requestTimes.reduce((a, b) => a + b) / requestTimes.length,
      responseTimes: requestTimes
    };
  }
};

/**
 * Error handling tests
 */
const errorHandlingTests = {
  'Invalid API Key': async () => {
    // This test would require a separate service instance with invalid credentials
    // For now, we'll test error handling indirectly
    return {
      tested: false,
      message: 'Error handling test requires invalid credentials setup'
    };
  },

  'Network Timeout': async () => {
    // This test would require network simulation
    // For now, we'll check if timeout handling is configured
    const serviceMetrics = tempStickService.getServiceMetrics();
    
    return {
      timeoutHandling: 'configured',
      serviceMetrics: serviceMetrics.api || {}
    };
  },

  'Malformed Data Handling': async () => {
    const sensors = await tempStickService.getAllSensors();
    
    if (sensors.length === 0) {
      return { message: 'No sensors available for malformed data test' };
    }
    
    // Test with empty/invalid sensor ID
    try {
      await tempStickService.getLatestReadings('', 1);
      return { malformedDataHandled: false };
    } catch (error) {
      return {
        malformedDataHandled: true,
        errorMessage: error.message
      };
    }
  }
};

/**
 * Main test execution
 */
async function runAllTests() {
  try {
    testRunner.log('🚀 Starting comprehensive TempStick integration tests', 'info');
    testRunner.log(`Test configuration: ${JSON.stringify(testConfig, null, 2)}`, 'debug');
    
    // Run test suites
    await testRunner.runTestSuite('Pre-flight Checks', preflightTests);
    await testRunner.runTestSuite('API Connectivity', connectivityTests);
    await testRunner.runTestSuite('Core Functionality', functionalityTests);
    await testRunner.runTestSuite('Advanced Features', advancedTests);
    await testRunner.runTestSuite('Data Processing', dataProcessingTests);
    await testRunner.runTestSuite('Performance Tests', performanceTests);
    await testRunner.runTestSuite('Error Handling', errorHandlingTests);
    
    // Generate final report
    const report = testRunner.generateReport();
    
    // Save results to file
    const reportContent = JSON.stringify({
      summary: {
        passed: report.passed,
        failed: report.failed,
        total: report.total,
        successRate: `${((report.passed / report.total) * 100).toFixed(1)}%`
      },
      details: report.details,
      performance: report.performance,
      errors: report.errors,
      timestamp: new Date().toISOString()
    }, null, 2);
    
    // Write report to file
    const fs = await import('fs');
    const reportFilename = `tempstick-integration-report-${Date.now()}.json`;
    fs.writeFileSync(reportFilename, reportContent);
    
    testRunner.log(`📄 Full test report saved to: ${reportFilename}`, 'info');
    
    // Exit with appropriate code
    process.exit(report.failed > 0 ? 1 : 0);
    
  } catch (error) {
    testRunner.log(`💥 Test execution failed: ${error.message}`, 'error');
    process.exit(1);
  }
}

// Handle script termination
process.on('SIGINT', () => {
  testRunner.log('🛑 Tests interrupted by user', 'warning');
  testRunner.generateReport();
  process.exit(130);
});

process.on('unhandledRejection', (reason, promise) => {
  testRunner.log(`🚨 Unhandled rejection at ${promise}: ${reason}`, 'error');
  process.exit(1);
});

// Start tests
runAllTests();