// Apply just the voice columns to inventory_events table
import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

const supabaseUrl = process.env.VITE_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function addVoiceColumns() {
  console.log('🔧 Adding Voice Columns to inventory_events')
  console.log('==========================================\n')

  const alterCommands = [
    // Add voice processing confidence score (0.0 to 1.0)
    `ALTER TABLE inventory_events 
     ADD COLUMN IF NOT EXISTS voice_confidence_score DECIMAL(3,2) 
     CHECK (voice_confidence_score >= 0.0 AND voice_confidence_score <= 1.0)`,
    
    // Add detailed confidence breakdown as JSONB
    `ALTER TABLE inventory_events 
     ADD COLUMN IF NOT EXISTS voice_confidence_breakdown JSONB`,
    
    // Add raw transcript from voice processing
    `ALTER TABLE inventory_events 
     ADD COLUMN IF NOT EXISTS raw_transcript TEXT`,
    
    // Add URL to stored audio recording
    `ALTER TABLE inventory_events 
     ADD COLUMN IF NOT EXISTS audio_recording_url TEXT`,
    
    // Add flag to indicate if event was created via voice
    `ALTER TABLE inventory_events 
     ADD COLUMN IF NOT EXISTS created_by_voice BOOLEAN DEFAULT FALSE`,
     
    // Add missing unit column that VoiceEventService expects
    `ALTER TABLE inventory_events 
     ADD COLUMN IF NOT EXISTS unit TEXT DEFAULT 'lbs'`,
     
    // Add missing vendor/customer name columns
    `ALTER TABLE inventory_events 
     ADD COLUMN IF NOT EXISTS vendor_name TEXT`,
     
    `ALTER TABLE inventory_events 
     ADD COLUMN IF NOT EXISTS customer_name TEXT`,
     
    // Add missing condition/temperature columns  
    `ALTER TABLE inventory_events 
     ADD COLUMN IF NOT EXISTS condition_on_receipt TEXT`,
     
    `ALTER TABLE inventory_events 
     ADD COLUMN IF NOT EXISTS temperature_at_receipt DECIMAL(5,2)`,
     
    // Add occurred_at if it doesn't exist
    `ALTER TABLE inventory_events 
     ADD COLUMN IF NOT EXISTS occurred_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP`
  ]

  for (let i = 0; i < alterCommands.length; i++) {
    const command = alterCommands[i]
    console.log(`${i + 1}. Executing: ${command.split('\n')[0].trim()}...`)
    
    try {
      const { error } = await supabase.rpc('exec', { sql: command })
      
      if (error) {
        // Try direct query if rpc doesn't work
        const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${supabaseServiceKey}`,
            'apikey': supabaseServiceKey
          },
          body: JSON.stringify({ sql: command })
        })
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${await response.text()}`)
        }
      }
      
      console.log('   ✅ Success')
    } catch (error) {
      console.error(`   ❌ Failed: ${error.message}`)
      // Continue with other commands
    }
  }

  // Test that columns were added
  console.log('\n🔍 Verifying columns were added...')
  try {
    const { data, error } = await supabase
      .from('inventory_events')
      .select('voice_confidence_score, created_by_voice, unit, vendor_name')
      .limit(1)
    
    if (error) {
      console.error('❌ Verification failed:', error.message)
    } else {
      console.log('✅ Voice columns are accessible!')
    }
  } catch (error) {
    console.error('❌ Verification error:', error.message)
  }
}

addVoiceColumns().then(() => {
  console.log('\n🏁 Column addition complete')
  process.exit(0)
}).catch(error => {
  console.error('\n💥 Failed:', error)
  process.exit(1)
})