/**
 * End-to-End tests for complete vendor performance workflow
 * Tests the complete user journey from dashboard to detailed analysis
 */

import { test, expect, Page } from '@playwright/test';

// Mock data for E2E tests
const mockVendorData = {
  dashboardSummary: [
    {
      vendor_id: 'vendor-001',
      vendor_name: 'Pacific Seafood Supply',
      contact_name: '<PERSON>',
      email: '<EMAIL>',
      phone: '(*************',
      overall_letter_grade: 'A',
      completion_rate: 96.5,
      on_time_delivery_rate: 94.2,
      average_quality_score: 8.7,
      total_interactions: 45,
      active_alerts_count: 0,
      last_delivery_date: '2025-08-10T14:30:00Z',
      last_delivery_status: 'completed'
    },
    {
      vendor_id: 'vendor-002',
      vendor_name: 'Alaska Premium Fish',
      contact_name: '<PERSON>',
      email: '<EMAIL>',
      phone: '(*************',
      overall_letter_grade: 'B',
      completion_rate: 87.3,
      on_time_delivery_rate: 82.1,
      average_quality_score: 7.9,
      total_interactions: 32,
      active_alerts_count: 2,
      last_delivery_date: '2025-08-12T09:15:00Z',
      last_delivery_status: 'completed'
    },
    {
      vendor_id: 'vendor-003',
      vendor_name: 'Atlantic Imports LLC',
      contact_name: 'Emma <PERSON>',
      email: '<EMAIL>',
      phone: '(*************',
      overall_letter_grade: 'D',
      completion_rate: 65.8,
      on_time_delivery_rate: 58.3,
      average_quality_score: 6.2,
      total_interactions: 28,
      active_alerts_count: 5,
      last_delivery_date: '2025-08-08T16:45:00Z',
      last_delivery_status: 'partial'
    }
  ]
};

// Helper function to setup API mocking
async function setupApiMocks(page: Page) {
  // Mock Supabase API calls
  await page.route('**/rest/v1/rpc/get_vendor_dashboard_summary', async route => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify(mockVendorData.dashboardSummary)
    });
  });

  // Mock vendor details API
  await page.route('**/rest/v1/vendors*', async route => {
    const url = new URL(route.request().url());
    const vendorId = url.searchParams.get('id');
    
    const vendor = {
      id: vendorId,
      name: 'Pacific Seafood Supply',
      contact_person: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '(*************',
      created_at: '2025-01-01T00:00:00Z'
    };

    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify([vendor])
    });
  });

  // Mock other vendor API endpoints
  await page.route('**/rest/v1/vendor_metrics*', async route => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify([{
        id: 'metrics-001',
        vendor_id: 'vendor-001',
        completion_rate: 95.6,
        on_time_delivery_rate: 94.2,
        average_quality_score: 8.7,
        total_interactions: 45,
        overall_letter_grade: 'A'
      }])
    });
  });

  await page.route('**/rest/v1/vendor_ratings*', async route => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify([])
    });
  });

  await page.route('**/rest/v1/vendor_compliance*', async route => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify([{
        id: 'compliance-001',
        vendor_id: 'vendor-001',
        haccp_certified: true,
        compliance_score: 92,
        compliance_status: 'compliant'
      }])
    });
  });

  await page.route('**/rest/v1/vendor_performance_alerts*', async route => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify([])
    });
  });
}

test.describe('Vendor Performance Workflow', () => {
  test.beforeEach(async ({ page }) => {
    await setupApiMocks(page);
    
    // Navigate to vendors page
    await page.goto('/');
    
    // Navigate to vendors section (assuming it's in the main navigation)
    await page.click('[data-testid="vendors-nav"]').catch(() => {
      // If no test ID, try clicking on "Vendors" text
      page.click('text=Vendors').catch(() => {
        // If that fails, try finding vendor dashboard directly
        page.goto('/vendors');
      });
    });
  });

  // ===== DASHBOARD OVERVIEW TESTS =====

  test('displays vendor performance dashboard with all key metrics', async ({ page }) => {
    // Wait for page to load
    await expect(page.locator('text=Vendor Performance Dashboard')).toBeVisible();
    
    // Check that loading state is shown initially
    await expect(page.locator('text=Loading vendor dashboard')).toBeVisible();
    
    // Wait for data to load
    await expect(page.locator('text=Loading vendor dashboard')).not.toBeVisible({ timeout: 10000 });
    
    // Verify performance summary cards
    await expect(page.locator('text=Total Vendors')).toBeVisible();
    await expect(page.locator('text=A-Grade Vendors')).toBeVisible();
    await expect(page.locator('text=Active Alerts')).toBeVisible();
    await expect(page.locator('text=Avg Completion Rate')).toBeVisible();
    
    // Verify specific values
    await expect(page.locator('text=3').first()).toBeVisible(); // Total vendors
    await expect(page.locator('text=1').first()).toBeVisible(); // A-grade vendors
    
    // Verify vendor list
    await expect(page.locator('text=Pacific Seafood Supply')).toBeVisible();
    await expect(page.locator('text=Alaska Premium Fish')).toBeVisible();
    await expect(page.locator('text=Atlantic Imports LLC')).toBeVisible();
    
    // Verify grade distribution section
    await expect(page.locator('text=Grade Distribution')).toBeVisible();
  });

  test('filters and sorts vendors correctly', async ({ page }) => {
    await expect(page.locator('text=Vendor Performance Dashboard')).toBeVisible();
    await expect(page.locator('text=Pacific Seafood Supply')).toBeVisible();
    
    // Test search functionality
    await page.fill('input[placeholder="Search vendors..."]', 'Pacific');
    await expect(page.locator('text=Pacific Seafood Supply')).toBeVisible();
    await expect(page.locator('text=Alaska Premium Fish')).not.toBeVisible();
    
    // Clear search
    await page.fill('input[placeholder="Search vendors..."]', '');
    await expect(page.locator('text=Alaska Premium Fish')).toBeVisible();
    
    // Test grade filtering
    await page.selectOption('select >> nth=1', 'A'); // Filter by grade A
    await expect(page.locator('text=Pacific Seafood Supply')).toBeVisible();
    await expect(page.locator('text=Alaska Premium Fish')).not.toBeVisible();
    
    // Test sorting
    await page.selectOption('select >> nth=0', 'name'); // Sort by name
    await expect(page.locator('text=Pacific Seafood Supply')).toBeVisible();
    
    // Reset filters
    await page.selectOption('select >> nth=1', 'all');
    await expect(page.locator('text=Alaska Premium Fish')).toBeVisible();
  });

  test('displays alert indicators correctly', async ({ page }) => {
    await expect(page.locator('text=Vendor Performance Dashboard')).toBeVisible();
    await expect(page.locator('text=Loading vendor dashboard')).not.toBeVisible({ timeout: 10000 });
    
    // Vendors with alerts should show alert badges
    const alaskaVendorSection = page.locator('text=Alaska Premium Fish').locator('..').locator('..');
    await expect(alaskaVendorSection.locator('text=2 alerts')).toBeVisible();
    
    const atlanticVendorSection = page.locator('text=Atlantic Imports LLC').locator('..').locator('..');
    await expect(atlanticVendorSection.locator('text=5 alerts')).toBeVisible();
    
    // Vendor without alerts should not show alert badge
    const pacificVendorSection = page.locator('text=Pacific Seafood Supply').locator('..').locator('..');
    await expect(pacificVendorSection.locator('text*=alert')).not.toBeVisible();
  });

  // ===== VENDOR REPORT CARD TESTS =====

  test('navigates to detailed vendor report card', async ({ page }) => {
    await expect(page.locator('text=Vendor Performance Dashboard')).toBeVisible();
    await expect(page.locator('text=Loading vendor dashboard')).not.toBeVisible({ timeout: 10000 });
    
    // Click "View Report" for Pacific Seafood Supply
    const pacificVendorSection = page.locator('text=Pacific Seafood Supply').locator('..').locator('..');
    await pacificVendorSection.locator('button:has-text("View Report")').click();
    
    // Should navigate to report card
    await expect(page.locator('text=Loading vendor report card')).toBeVisible();
    await expect(page.locator('text=Loading vendor report card')).not.toBeVisible({ timeout: 10000 });
    
    // Verify report card content
    await expect(page.locator('text=Pacific Seafood Supply').first()).toBeVisible();
    await expect(page.locator('text=Sarah Johnson')).toBeVisible();
    await expect(page.locator('text=<EMAIL>')).toBeVisible();
    
    // Verify performance summary cards in report card
    await expect(page.locator('text=Completion Rate')).toBeVisible();
    await expect(page.locator('text=On-Time Delivery')).toBeVisible();
    await expect(page.locator('text=Avg Quality')).toBeVisible();
    await expect(page.locator('text=Total Orders')).toBeVisible();
    
    // Verify tabs are present
    await expect(page.locator('button:has-text("Overview")')).toBeVisible();
    await expect(page.locator('button:has-text("Ratings")')).toBeVisible();
    await expect(page.locator('button:has-text("Compliance")')).toBeVisible();
    await expect(page.locator('button:has-text("Alerts")')).toBeVisible();
  });

  test('navigates between report card tabs', async ({ page }) => {
    // Navigate to report card first
    await expect(page.locator('text=Vendor Performance Dashboard')).toBeVisible();
    await expect(page.locator('text=Loading vendor dashboard')).not.toBeVisible({ timeout: 10000 });
    
    const viewReportButton = page.locator('button:has-text("View Report")').first();
    await viewReportButton.click();
    
    await expect(page.locator('text=Loading vendor report card')).not.toBeVisible({ timeout: 10000 });
    
    // Overview tab should be active by default
    await expect(page.locator('text=Financial Performance')).toBeVisible();
    
    // Navigate to Ratings tab
    await page.click('button:has-text("Ratings")');
    await expect(page.locator('text=Recent Ratings')).toBeVisible();
    await expect(page.locator('button:has-text("Add New Rating")')).toBeVisible();
    
    // Navigate to Compliance tab
    await page.click('button:has-text("Compliance")');
    await expect(page.locator('text=Compliance Status')).toBeVisible();
    await expect(page.locator('text=HACCP Compliance')).toBeVisible();
    await expect(page.locator('text=GDST Traceability')).toBeVisible();
    
    // Navigate to Alerts tab
    await page.click('button:has-text("Alerts")');
    await expect(page.locator('text=Performance Alerts')).toBeVisible();
    
    // Should show "All Clear!" since no alerts in mock
    await expect(page.locator('text=All Clear!')).toBeVisible();
    
    // Navigate back to Overview
    await page.click('button:has-text("Overview")');
    await expect(page.locator('text=Financial Performance')).toBeVisible();
  });

  test('displays compliance information correctly', async ({ page }) => {
    // Navigate to report card
    await expect(page.locator('text=Loading vendor dashboard')).not.toBeVisible({ timeout: 10000 });
    await page.locator('button:has-text("View Report")').first().click();
    await expect(page.locator('text=Loading vendor report card')).not.toBeVisible({ timeout: 10000 });
    
    // Navigate to Compliance tab
    await page.click('button:has-text("Compliance")');
    
    // Verify HACCP compliance information
    await expect(page.locator('text=HACCP Compliance')).toBeVisible();
    await expect(page.locator('text=Certified')).toBeVisible();
    
    // Verify GDST traceability information
    await expect(page.locator('text=GDST Traceability')).toBeVisible();
    
    // Verify overall compliance score
    await expect(page.locator('text=Overall Compliance Score')).toBeVisible();
    await expect(page.locator('text=92/100')).toBeVisible();
  });

  test('returns to dashboard from report card', async ({ page }) => {
    // Navigate to report card
    await expect(page.locator('text=Loading vendor dashboard')).not.toBeVisible({ timeout: 10000 });
    await page.locator('button:has-text("View Report")').first().click();
    await expect(page.locator('text=Loading vendor report card')).not.toBeVisible({ timeout: 10000 });
    
    // Click close button
    await page.click('button:has-text("✕")');
    
    // Should return to dashboard
    await expect(page.locator('text=Vendor Performance Dashboard')).toBeVisible();
    await expect(page.locator('text=Pacific Seafood Supply')).toBeVisible();
    await expect(page.locator('text=Alaska Premium Fish')).toBeVisible();
  });

  // ===== REFRESH AND UPDATE TESTS =====

  test('refreshes dashboard data', async ({ page }) => {
    await expect(page.locator('text=Loading vendor dashboard')).not.toBeVisible({ timeout: 10000 });
    
    // Click refresh button
    await page.click('button:has-text("Refresh Data")');
    
    // Should maintain data display (in real app, might show loading indicator)
    await expect(page.locator('text=Pacific Seafood Supply')).toBeVisible();
  });

  test('refreshes metrics in report card', async ({ page }) => {
    // Navigate to report card
    await expect(page.locator('text=Loading vendor dashboard')).not.toBeVisible({ timeout: 10000 });
    await page.locator('button:has-text("View Report")').first().click();
    await expect(page.locator('text=Loading vendor report card')).not.toBeVisible({ timeout: 10000 });
    
    // Click refresh metrics button
    await page.click('button:has-text("Refresh Metrics")');
    
    // Should maintain display
    await expect(page.locator('text=Pacific Seafood Supply')).toBeVisible();
  });

  // ===== ERROR HANDLING TESTS =====

  test('handles API errors gracefully', async ({ page }) => {
    // Setup error response
    await page.route('**/rest/v1/rpc/get_vendor_dashboard_summary', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal server error' })
      });
    });
    
    await page.goto('/vendors');
    
    // Should show error state
    await expect(page.locator('text=Error Loading Dashboard')).toBeVisible();
    await expect(page.locator('button:has-text("Try Again")')).toBeVisible();
  });

  test('recovers from errors with retry', async ({ page }) => {
    // Start with error
    await page.route('**/rest/v1/rpc/get_vendor_dashboard_summary', route => {
      route.fulfill({
        status: 500,
        body: JSON.stringify({ error: 'Server error' })
      });
    });
    
    await page.goto('/vendors');
    await expect(page.locator('text=Error Loading Dashboard')).toBeVisible();
    
    // Fix the API
    await page.route('**/rest/v1/rpc/get_vendor_dashboard_summary', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockVendorData.dashboardSummary)
      });
    });
    
    // Click retry
    await page.click('button:has-text("Try Again")');
    
    // Should recover and show data
    await expect(page.locator('text=Pacific Seafood Supply')).toBeVisible({ timeout: 10000 });
  });

  // ===== RESPONSIVE DESIGN TESTS =====

  test('works correctly on mobile viewport', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    await expect(page.locator('text=Loading vendor dashboard')).not.toBeVisible({ timeout: 10000 });
    
    // Key elements should still be visible and functional
    await expect(page.locator('text=Vendor Performance Dashboard')).toBeVisible();
    await expect(page.locator('text=Pacific Seafood Supply')).toBeVisible();
    
    // Search should work
    await page.fill('input[placeholder="Search vendors..."]', 'Pacific');
    await expect(page.locator('text=Pacific Seafood Supply')).toBeVisible();
    await expect(page.locator('text=Alaska Premium Fish')).not.toBeVisible();
  });

  test('works correctly on tablet viewport', async ({ page }) => {
    // Set tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    
    await expect(page.locator('text=Loading vendor dashboard')).not.toBeVisible({ timeout: 10000 });
    
    // All functionality should work
    await expect(page.locator('text=Vendor Performance Dashboard')).toBeVisible();
    await expect(page.locator('text=Total Vendors')).toBeVisible();
    
    // Navigation to report card should work
    await page.locator('button:has-text("View Report")').first().click();
    await expect(page.locator('text=Loading vendor report card')).not.toBeVisible({ timeout: 10000 });
    await expect(page.locator('text=Pacific Seafood Supply')).toBeVisible();
  });

  // ===== ACCESSIBILITY TESTS =====

  test('supports keyboard navigation', async ({ page }) => {
    await expect(page.locator('text=Loading vendor dashboard')).not.toBeVisible({ timeout: 10000 });
    
    // Tab through key elements
    await page.keyboard.press('Tab'); // Should focus search input
    await expect(page.locator('input[placeholder="Search vendors..."]')).toBeFocused();
    
    await page.keyboard.press('Tab'); // Should focus sort select
    await page.keyboard.press('Tab'); // Should focus filter select
    await page.keyboard.press('Tab'); // Should focus refresh button
    
    // Should be able to activate refresh button with Enter
    await page.keyboard.press('Enter');
  });

  test('has proper ARIA labels and roles', async ({ page }) => {
    await expect(page.locator('text=Loading vendor dashboard')).not.toBeVisible({ timeout: 10000 });
    
    // Check for proper roles and labels
    await expect(page.locator('input[placeholder="Search vendors..."]')).toHaveAttribute('type', 'text');
    await expect(page.locator('button:has-text("Refresh Data")')).toHaveAttribute('type', 'button');
    await expect(page.locator('button:has-text("View Report")').first()).toHaveAttribute('type', 'button');
  });

  // ===== PERFORMANCE TESTS =====

  test('loads dashboard within acceptable time', async ({ page }) => {
    const startTime = Date.now();
    
    await expect(page.locator('text=Loading vendor dashboard')).not.toBeVisible({ timeout: 10000 });
    
    const loadTime = Date.now() - startTime;
    
    // Should load within 5 seconds
    expect(loadTime).toBeLessThan(5000);
    
    // All key elements should be visible
    await expect(page.locator('text=Pacific Seafood Supply')).toBeVisible();
    await expect(page.locator('text=Total Vendors')).toBeVisible();
  });
});