import { test, expect } from '@playwright/test';

/**
 * Performance Testing with Core Web Vitals
 */

test.describe('Core Web Vitals', () => {
  test.beforeEach(async ({ page }) => {
    // Mock auth for consistent testing
    await page.route('**/auth/**', route => route.fulfill({ 
      status: 200, 
      body: JSON.stringify({ user: { id: 'test' }, session: { access_token: 'token' } }) 
    }));

    // Mock fast API responses
    await page.route('**/rest/v1/**', route => route.fulfill({
      status: 200,
      body: JSON.stringify([]),
      headers: { 'content-type': 'application/json' }
    }));
  });

  test('should meet Core Web Vitals thresholds', async ({ page }) => {
    // Start performance measurement
    await page.goto('/', { waitUntil: 'networkidle' });
    
    // Measure Core Web Vitals
    const vitals = await page.evaluate(() => {
      return new Promise((resolve) => {
        const metrics = {
          LCP: 0,
          FID: 0,
          CLS: 0,
          FCP: 0,
          TTFB: 0
        };

        // Largest Contentful Paint
        new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          metrics.LCP = lastEntry.startTime;
        }).observe({ entryTypes: ['largest-contentful-paint'] });

        // First Input Delay
        new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            metrics.FID = entry.processingStart - entry.startTime;
          });
        }).observe({ entryTypes: ['first-input'] });

        // Cumulative Layout Shift
        let cumulativeScore = 0;
        new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              cumulativeScore += entry.value;
            }
          });
          metrics.CLS = cumulativeScore;
        }).observe({ entryTypes: ['layout-shift'] });

        // First Contentful Paint
        new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            if (entry.name === 'first-contentful-paint') {
              metrics.FCP = entry.startTime;
            }
          });
        }).observe({ entryTypes: ['paint'] });

        // Time to First Byte
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        metrics.TTFB = navigation.responseStart - navigation.requestStart;

        // Wait a bit for measurements
        setTimeout(() => resolve(metrics), 3000);
      });
    });

    // Assert Core Web Vitals thresholds
    expect(vitals.LCP).toBeLessThan(2500); // Good: < 2.5s
    expect(vitals.FID).toBeLessThan(100);  // Good: < 100ms
    expect(vitals.CLS).toBeLessThan(0.1);  // Good: < 0.1
    expect(vitals.FCP).toBeLessThan(1800); // Good: < 1.8s
    expect(vitals.TTFB).toBeLessThan(800); // Good: < 800ms
  });

  test('should measure JavaScript bundle size', async ({ page }) => {
    // Capture network activity
    const responses = [];
    page.on('response', response => {
      if (response.url().includes('.js')) {
        responses.push({
          url: response.url(),
          size: parseInt(response.headers()['content-length'] || '0')
        });
      }
    });

    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Calculate total JS bundle size
    const totalSize = responses.reduce((sum, response) => sum + response.size, 0);
    const totalSizeMB = totalSize / (1024 * 1024);

    // Assert bundle size is reasonable
    expect(totalSizeMB).toBeLessThan(2); // Should be under 2MB

    // Check for code splitting
    const uniqueBundles = new Set(responses.map(r => r.url.split('/').pop()));
    expect(uniqueBundles.size).toBeGreaterThan(1); // Should have multiple chunks
  });

  test('should measure memory usage', async ({ page }) => {
    await page.goto('/');
    
    // Get initial memory
    const initialMemory = await page.evaluate(() => {
      return (performance as any).memory?.usedJSHeapSize || 0;
    });

    // Navigate through the app to stress test memory
    const routes = ['/inventory', '/products', '/analytics', '/haccp'];
    
    for (const route of routes) {
      await page.goto(route);
      await page.waitForLoadState('networkidle');
    }

    // Check final memory usage
    const finalMemory = await page.evaluate(() => {
      return (performance as any).memory?.usedJSHeapSize || 0;
    });

    const memoryIncrease = (finalMemory - initialMemory) / (1024 * 1024); // MB

    // Memory increase should be reasonable
    expect(memoryIncrease).toBeLessThan(50); // Less than 50MB increase
  });

  test('should handle large datasets efficiently', async ({ page }) => {
    // Mock large dataset
    await page.route('**/rest/v1/inventory_events**', route => {
      const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
        id: i,
        product_name: `Product ${i}`,
        quantity: Math.random() * 100,
        created_at: new Date().toISOString()
      }));
      
      route.fulfill({
        status: 200,
        body: JSON.stringify(largeDataset)
      });
    });

    const startTime = Date.now();
    await page.goto('/inventory');
    
    // Wait for table to render
    await page.waitForSelector('[data-testid="inventory-table"]');
    const renderTime = Date.now() - startTime;

    // Should render large dataset quickly
    expect(renderTime).toBeLessThan(3000); // 3 seconds

    // Test scrolling performance
    const scrollStart = Date.now();
    await page.evaluate(() => {
      const table = document.querySelector('[data-testid="inventory-table"]');
      if (table) {
        table.scrollTop = table.scrollHeight;
      }
    });
    const scrollTime = Date.now() - scrollStart;

    expect(scrollTime).toBeLessThan(100); // Smooth scrolling
  });

  test('should optimize image loading', async ({ page }) => {
    let imageRequests = 0;
    page.on('request', request => {
      if (request.resourceType() === 'image') {
        imageRequests++;
      }
    });

    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Should implement lazy loading (minimal initial image requests)
    expect(imageRequests).toBeLessThan(10);

    // Test lazy loading by scrolling
    const initialRequests = imageRequests;
    await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
    await page.waitForTimeout(1000);

    // More images should load after scrolling
    expect(imageRequests).toBeGreaterThan(initialRequests);
  });
});

test.describe('Performance Monitoring', () => {
  test('should measure page load performance', async ({ page }) => {
    await page.goto('/');
    
    const performanceMetrics = await page.evaluate(() => {
      const nav = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      return {
        dns: nav.domainLookupEnd - nav.domainLookupStart,
        tcp: nav.connectEnd - nav.connectStart,
        request: nav.responseStart - nav.requestStart,
        response: nav.responseEnd - nav.responseStart,
        domProcessing: nav.domContentLoadedEventStart - nav.responseEnd,
        load: nav.loadEventEnd - nav.loadEventStart
      };
    });

    // Assert reasonable timing
    expect(performanceMetrics.dns).toBeLessThan(100);
    expect(performanceMetrics.tcp).toBeLessThan(100);
    expect(performanceMetrics.request).toBeLessThan(500);
    expect(performanceMetrics.response).toBeLessThan(1000);
    expect(performanceMetrics.domProcessing).toBeLessThan(2000);
  });

  test('should measure voice processing performance', async ({ page }) => {
    // Skip if voice not supported
    const hasVoiceSupport = await page.evaluate(() => {
      return 'SpeechRecognition' in window || 'webkitSpeechRecognition' in window;
    });
    
    test.skip(!hasVoiceSupport, 'Voice API not supported');

    await page.goto('/inventory');
    
    // Measure voice processing time
    const startTime = Date.now();
    await page.click('[data-testid="voice-input-button"]');
    
    // Simulate voice result
    await page.evaluate(() => {
      const event = new Event('result') as any;
      event.results = [{
        0: { transcript: 'Add ten pounds of salmon', confidence: 0.9 },
        isFinal: true
      }];
      window.dispatchEvent(event);
    });

    await page.waitForSelector('[data-testid="ai-processing-complete"]');
    const processingTime = Date.now() - startTime;

    // Voice processing should be responsive
    expect(processingTime).toBeLessThan(2000); // 2 seconds
  });

  test('should measure import processing performance', async ({ page }) => {
    await page.goto('/import');
    
    // Upload test file
    const startTime = Date.now();
    await page.setInputFiles('[data-testid="file-upload"]', {
      name: 'test.csv',
      mimeType: 'text/csv',
      buffer: Buffer.from(Array.from({ length: 100 }, (_, i) => 
        `Product ${i},${Math.random() * 100},lbs`
      ).join('\n'))
    });

    await page.waitForSelector('[data-testid="upload-complete"]');
    const uploadTime = Date.now() - startTime;

    // File processing should be efficient
    expect(uploadTime).toBeLessThan(5000); // 5 seconds for 100 rows
  });
});

test.describe('Resource Optimization', () => {
  test('should implement efficient caching', async ({ page }) => {
    let cacheHits = 0;
    let cacheMisses = 0;

    page.on('response', response => {
      const cacheHeader = response.headers()['cache-control'];
      if (cacheHeader && cacheHeader.includes('max-age')) {
        cacheHits++;
      } else {
        cacheMisses++;
      }
    });

    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Should have good cache hit ratio
    const totalRequests = cacheHits + cacheMisses;
    const cacheRatio = cacheHits / totalRequests;
    expect(cacheRatio).toBeGreaterThan(0.5); // 50% cache hit rate
  });

  test('should compress responses', async ({ page }) => {
    const responses = [];
    page.on('response', response => {
      if (response.url().includes('.js') || response.url().includes('.css')) {
        responses.push({
          url: response.url(),
          encoding: response.headers()['content-encoding']
        });
      }
    });

    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Most resources should be compressed
    const compressedResponses = responses.filter(r => 
      r.encoding === 'gzip' || r.encoding === 'br'
    );
    
    const compressionRatio = compressedResponses.length / responses.length;
    expect(compressionRatio).toBeGreaterThan(0.8); // 80% compressed
  });

  test('should minimize render blocking', async ({ page }) => {
    await page.goto('/');
    
    // Check for render-blocking resources
    const renderBlockingResources = await page.evaluate(() => {
      const links = document.querySelectorAll('link[rel="stylesheet"]');
      const scripts = document.querySelectorAll('script[src]:not([async]):not([defer])');
      return {
        css: links.length,
        js: scripts.length
      };
    });

    // Should minimize render-blocking resources
    expect(renderBlockingResources.css).toBeLessThan(3);
    expect(renderBlockingResources.js).toBeLessThan(2);
  });
});