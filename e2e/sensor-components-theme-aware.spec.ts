/**
 * Sensor Components Theme-Aware Rendering Tests
 * 
 * Tests for sensor components (SensorDashboard, SensorTile, ExportControls)
 * to ensure proper theme-aware styling in both light and dark modes.
 */

import { test, expect, type Page } from '@playwright/test';

test.describe('Sensor Components Theme-Aware Rendering', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForSelector('[data-testid="sidebar"]', { timeout: 10000 });
    
    // Navigate to the sensors/dashboard view if it exists
    // This assumes there's a way to reach sensor components
    const dashboardButton = page.locator('button', { hasText: 'Dashboard' });
    await dashboardButton.click();
    await page.waitForTimeout(500);
  });

  test.describe('SensorDashboard Theme Integration', () => {
    test('should render SensorDashboard with light theme styles', async ({ page }) => {
      // Set light theme
      await page.evaluate(() => {
        localStorage.setItem('seafood-manager-theme', 'light');
      });
      await page.reload();
      await page.waitForSelector('[data-testid="sidebar"]');
      
      // Look for sensor dashboard elements
      const sensorElements = page.locator('[data-testid*="sensor"], [class*="sensor"], .sensor-dashboard');
      
      if (await sensorElements.count() > 0) {
        // Check that sensor elements use light theme styling
        const firstSensor = sensorElements.first();
        
        const styles = await firstSensor.evaluate(el => {
          const computed = getComputedStyle(el);
          return {
            backgroundColor: computed.backgroundColor,
            color: computed.color,
            borderColor: computed.borderColor,
          };
        });
        
        // In light mode, should have light backgrounds
        expect(styles.backgroundColor).not.toBe('rgb(17, 24, 39)'); // Not dark gray-900
        
        // Take screenshot for visual validation
        await expect(firstSensor).toHaveScreenshot('sensor-dashboard-light-mode.png');
      }
    });

    test('should render SensorDashboard with dark theme styles', async ({ page }) => {
      // Set dark theme
      await page.evaluate(() => {
        localStorage.setItem('seafood-manager-theme', 'dark');
      });
      await page.reload();
      await page.waitForSelector('[data-testid="sidebar"]');
      
      // Verify dark theme is active
      await expect(page.locator('html')).toHaveClass(/dark/);
      
      // Look for sensor dashboard elements
      const sensorElements = page.locator('[data-testid*="sensor"], [class*="sensor"], .sensor-dashboard');
      
      if (await sensorElements.count() > 0) {
        const firstSensor = sensorElements.first();
        
        // Take screenshot for visual validation in dark mode
        await expect(firstSensor).toHaveScreenshot('sensor-dashboard-dark-mode.png');
      }
    });

    test('should update sensor styling when theme changes', async ({ page }) => {
      // Start with light theme
      await page.evaluate(() => {
        localStorage.setItem('seafood-manager-theme', 'light');
      });
      await page.reload();
      await page.waitForSelector('[data-testid="sidebar"]');
      
      // Look for sensor elements
      const sensorElements = page.locator('[data-testid*="sensor"], [class*="sensor"]');
      
      if (await sensorElements.count() > 0) {
        // Record initial styling
        const initialStyles = await sensorElements.first().evaluate(el => {
          return getComputedStyle(el).backgroundColor;
        });
        
        // Switch to dark theme
        const themeButton = page.locator('button', { hasText: /Theme:/ });
        await themeButton.click(); // Should cycle to dark
        await page.waitForTimeout(200);
        
        // Check if styles changed
        const newStyles = await sensorElements.first().evaluate(el => {
          return getComputedStyle(el).backgroundColor;
        });
        
        // Styles should be different (unless already dark)
        if (initialStyles === 'rgba(0, 0, 0, 0)' || initialStyles === 'rgb(255, 255, 255)') {
          expect(newStyles).toBeDefined();
        }
      }
    });
  });

  test.describe('SensorTile Theme Integration', () => {
    test('should render sensor status indicators with theme-appropriate colors', async ({ page }) => {
      // Set light theme first
      await page.evaluate(() => {
        localStorage.setItem('seafood-manager-theme', 'light');
      });
      await page.reload();
      await page.waitForSelector('[data-testid="sidebar"]');
      
      // Look for sensor tiles or status indicators
      const statusElements = page.locator(
        '[data-testid*="status"], [class*="status"], .sensor-online, .sensor-warning, .sensor-critical, .sensor-offline'
      );
      
      if (await statusElements.count() > 0) {
        // Check that status colors are appropriate for light mode
        const statusElement = statusElements.first();
        
        const styles = await statusElement.evaluate(el => {
          const computed = getComputedStyle(el);
          return {
            backgroundColor: computed.backgroundColor,
            color: computed.color,
          };
        });
        
        // Should have defined colors (not transparent)
        expect(styles.backgroundColor).not.toBe('rgba(0, 0, 0, 0)');
        expect(styles.color).not.toBe('rgba(0, 0, 0, 0)');
      }
    });

    test('should render sensor tiles correctly in dark mode', async ({ page }) => {
      // Set dark theme
      await page.evaluate(() => {
        localStorage.setItem('seafood-manager-theme', 'dark');
      });
      await page.reload();
      await page.waitForSelector('[data-testid="sidebar"]');
      
      // Look for sensor tiles
      const sensorTiles = page.locator('[data-testid*="sensor-tile"], [class*="sensor-tile"]');
      
      if (await sensorTiles.count() > 0) {
        // Check that tiles are visible and styled for dark mode
        const tile = sensorTiles.first();
        await expect(tile).toBeVisible();
        
        // Take screenshot for dark mode validation
        await expect(tile).toHaveScreenshot('sensor-tile-dark-mode.png');
      }
    });

    test('should maintain sensor tile functionality across theme changes', async ({ page }) => {
      const sensorTiles = page.locator('[data-testid*="sensor"], [class*="sensor"]');
      
      if (await sensorTiles.count() > 0) {
        const firstTile = sensorTiles.first();
        
        // Check initial visibility
        await expect(firstTile).toBeVisible();
        
        // Change theme
        const themeButton = page.locator('button', { hasText: /Theme:/ });
        await themeButton.click();
        await page.waitForTimeout(200);
        
        // Should still be visible and functional
        await expect(firstTile).toBeVisible();
        
        // Try clicking if it's interactive
        if (await firstTile.getAttribute('role') === 'button' || await firstTile.tagName() === 'BUTTON') {
          await firstTile.click();
          // Should not cause errors
        }
      }
    });
  });

  test.describe('ExportControls Theme Integration', () => {
    test('should render export controls with appropriate theme styling', async ({ page }) => {
      // Look for export controls (buttons, dropdowns, etc.)
      const exportElements = page.locator(
        '[data-testid*="export"], [class*="export"], button:has-text("Export"), button:has-text("Download")'
      );
      
      if (await exportElements.count() > 0) {
        // Set light theme
        await page.evaluate(() => {
          localStorage.setItem('seafood-manager-theme', 'light');
        });
        await page.reload();
        await page.waitForSelector('[data-testid="sidebar"]');
        
        const exportButton = exportElements.first();
        await expect(exportButton).toBeVisible();
        
        // Check button styling
        const buttonStyles = await exportButton.evaluate(el => {
          const computed = getComputedStyle(el);
          return {
            backgroundColor: computed.backgroundColor,
            color: computed.color,
            borderColor: computed.borderColor,
          };
        });
        
        expect(buttonStyles.color).not.toBe('rgba(0, 0, 0, 0)');
      }
    });

    test('should maintain export functionality in dark mode', async ({ page }) => {
      // Set dark theme
      await page.evaluate(() => {
        localStorage.setItem('seafood-manager-theme', 'dark');
      });
      await page.reload();
      await page.waitForSelector('[data-testid="sidebar"]');
      
      // Look for export controls
      const exportElements = page.locator(
        '[data-testid*="export"], [class*="export"], button:has-text("Export"), button:has-text("Download")'
      );
      
      if (await exportElements.count() > 0) {
        const exportButton = exportElements.first();
        
        // Should be visible in dark mode
        await expect(exportButton).toBeVisible();
        
        // Should be clickable
        await exportButton.hover();
        
        // Take screenshot for visual validation
        await expect(exportButton).toHaveScreenshot('export-controls-dark-mode.png');
      }
    });
  });

  test.describe('Temperature Chart Theme Integration', () => {
    test('should apply theme-aware chart styling', async ({ page }) => {
      // Check if CSS custom properties are properly applied
      const chartProperties = await page.evaluate(() => {
        const root = document.documentElement;
        return {
          chartBackground: getComputedStyle(root).getPropertyValue('--chart-background').trim(),
          chartText: getComputedStyle(root).getPropertyValue('--chart-text').trim(),
          chartGrid: getComputedStyle(root).getPropertyValue('--chart-grid').trim(),
          chartBorder: getComputedStyle(root).getPropertyValue('--chart-border').trim(),
        };
      });
      
      // Should have chart-specific CSS properties set
      expect(chartProperties.chartBackground).toBeTruthy();
      expect(chartProperties.chartText).toBeTruthy();
      expect(chartProperties.chartGrid).toBeTruthy();
      expect(chartProperties.chartBorder).toBeTruthy();
    });

    test('should update chart properties when theme changes', async ({ page }) => {
      // Start with light theme
      await page.evaluate(() => {
        localStorage.setItem('seafood-manager-theme', 'light');
      });
      await page.reload();
      await page.waitForSelector('[data-testid="sidebar"]');
      
      // Get initial chart properties
      const initialProps = await page.evaluate(() => {
        const root = document.documentElement;
        return getComputedStyle(root).getPropertyValue('--chart-background').trim();
      });
      
      expect(initialProps).toBe('#ffffff');
      
      // Switch to dark theme
      const themeButton = page.locator('button', { hasText: /Theme:/ });
      await themeButton.click();
      await page.waitForTimeout(200);
      
      // Get updated chart properties
      const updatedProps = await page.evaluate(() => {
        const root = document.documentElement;
        return getComputedStyle(root).getPropertyValue('--chart-background').trim();
      });
      
      // Should be different from light mode
      expect(updatedProps).not.toBe('#ffffff');
      expect(updatedProps).toBe('#1f2937'); // Dark mode background
    });
  });

  test.describe('HACCP Compliance Color Indicators', () => {
    test('should render HACCP status indicators with theme-appropriate colors', async ({ page }) => {
      // Navigate to HACCP view if available
      const haccpButton = page.locator('button:has-text("HACCP")');
      if (await haccpButton.count() > 0) {
        await haccpButton.click();
        await page.waitForTimeout(500);
      }
      
      // Look for HACCP status indicators
      const haccpElements = page.locator(
        '[data-testid*="haccp"], [class*="haccp"], .haccp-compliant, .haccp-violation, .haccp-warning'
      );
      
      if (await haccpElements.count() > 0) {
        // Test in light mode
        await page.evaluate(() => {
          localStorage.setItem('seafood-manager-theme', 'light');
        });
        await page.reload();
        await page.waitForSelector('[data-testid="sidebar"]');
        
        const haccpElement = haccpElements.first();
        await expect(haccpElement).toBeVisible();
        
        // Switch to dark mode and verify styling persists
        const themeButton = page.locator('button', { hasText: /Theme:/ });
        await themeButton.click();
        await page.waitForTimeout(200);
        
        await expect(haccpElement).toBeVisible();
      }
    });
  });

  test.describe('Responsive Sensor Components', () => {
    test('should maintain theme styling on mobile viewports', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      
      // Set dark theme
      await page.evaluate(() => {
        localStorage.setItem('seafood-manager-theme', 'dark');
      });
      await page.reload();
      await page.waitForSelector('[data-testid="sidebar"]');
      
      // Check that sensor components still render properly
      const sensorElements = page.locator('[data-testid*="sensor"], [class*="sensor"]');
      
      if (await sensorElements.count() > 0) {
        const firstSensor = sensorElements.first();
        await expect(firstSensor).toBeVisible();
        
        // Take mobile screenshot
        await expect(page).toHaveScreenshot('sensor-components-mobile-dark.png');
      }
    });

    test('should maintain theme styling on tablet viewports', async ({ page }) => {
      // Set tablet viewport
      await page.setViewportSize({ width: 768, height: 1024 });
      
      // Set light theme
      await page.evaluate(() => {
        localStorage.setItem('seafood-manager-theme', 'light');
      });
      await page.reload();
      await page.waitForSelector('[data-testid="sidebar"]');
      
      // Check sensor components on tablet
      const sensorElements = page.locator('[data-testid*="sensor"], [class*="sensor"]');
      
      if (await sensorElements.count() > 0) {
        const firstSensor = sensorElements.first();
        await expect(firstSensor).toBeVisible();
        
        // Take tablet screenshot
        await expect(page).toHaveScreenshot('sensor-components-tablet-light.png');
      }
    });
  });
});