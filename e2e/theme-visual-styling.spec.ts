/**
 * Theme Visual Styling Validation Tests
 * 
 * Tests for visual styling validation in both light and dark modes.
 * Focuses on theme-aware styling in sensor components and overall UI consistency.
 */

import { test, expect, type Page } from '@playwright/test';

test.describe('Theme Visual Styling Validation', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForSelector('[data-testid="sidebar"]', { timeout: 10000 });
  });

  test.describe('Light Mode Visual Validation', () => {
    test.beforeEach(async ({ page }) => {
      // Ensure light mode is active
      await page.evaluate(() => {
        localStorage.setItem('seafood-manager-theme', 'light');
      });
      await page.reload();
      await page.waitForSelector('[data-testid="sidebar"]');
    });

    test('should apply light mode styling to sidebar', async ({ page }) => {
      const sidebar = page.locator('[data-testid="sidebar"]');
      
      // Check background color (should be dark in light mode for contrast)
      const bgColor = await sidebar.evaluate(el => 
        getComputedStyle(el).backgroundColor
      );
      
      // Should be a dark background (gray-800)
      expect(bgColor).toBe('rgb(31, 41, 55)');
      
      // Check text color (should be white/light)
      const textColor = await sidebar.evaluate(el => 
        getComputedStyle(el).color
      );
      
      // Should be light text
      expect(textColor).toBe('rgb(255, 255, 255)');
    });

    test('should apply light mode styling to main content areas', async ({ page }) => {
      // Navigate to dashboard to see main content
      const dashboardButton = page.locator('button', { hasText: 'Dashboard' });
      await dashboardButton.click();
      
      // Check main content background
      const main = page.locator('main').or(page.locator('[data-testid="main-content"]'));
      if (await main.count() > 0) {
        const bgColor = await main.evaluate(el => 
          getComputedStyle(el).backgroundColor
        );
        
        // Should be light background in light mode
        expect(bgColor).toMatch(/rgb\(255, 255, 255\)|rgb\(249, 250, 251\)/);
      }
    });

    test('should display correct theme icon in light mode', async ({ page }) => {
      const themeButton = page.locator('button', { hasText: /Theme:/ });
      await expect(themeButton).toBeVisible();
      
      // Should show Sun icon for light mode
      const icon = themeButton.locator('svg').first();
      await expect(icon).toBeVisible();
      
      // Take screenshot for visual validation
      await expect(themeButton).toHaveScreenshot('light-mode-theme-button.png');
    });
  });

  test.describe('Dark Mode Visual Validation', () => {
    test.beforeEach(async ({ page }) => {
      // Set to dark mode
      await page.evaluate(() => {
        localStorage.setItem('seafood-manager-theme', 'dark');
      });
      await page.reload();
      await page.waitForSelector('[data-testid="sidebar"]');
    });

    test('should apply dark mode styling to HTML element', async ({ page }) => {
      const html = page.locator('html');
      await expect(html).toHaveClass(/dark/);
      
      // Check that light class is not present
      const classes = await html.getAttribute('class');
      expect(classes).not.toMatch(/\blight\b/);
    });

    test('should apply dark mode styling to sidebar', async ({ page }) => {
      const sidebar = page.locator('[data-testid="sidebar"]');
      
      // Sidebar should still be dark (consistent design)
      const bgColor = await sidebar.evaluate(el => 
        getComputedStyle(el).backgroundColor
      );
      
      expect(bgColor).toBe('rgb(31, 41, 55)');
    });

    test('should display correct theme icon in dark mode', async ({ page }) => {
      const themeButton = page.locator('button', { hasText: /Theme:/ });
      await expect(themeButton).toBeVisible();
      
      // Should show Moon icon for dark mode
      const icon = themeButton.locator('svg').first();
      await expect(icon).toBeVisible();
      
      // Take screenshot for visual validation
      await expect(themeButton).toHaveScreenshot('dark-mode-theme-button.png');
    });

    test('should apply dark theme classes to body/content', async ({ page }) => {
      // Check that dark mode affects overall page styling
      const bodyStyles = await page.evaluate(() => {
        const body = document.body;
        return {
          backgroundColor: getComputedStyle(body).backgroundColor,
          color: getComputedStyle(body).color,
        };
      });
      
      // In dark mode, should have darker backgrounds
      expect(bodyStyles.backgroundColor).not.toBe('rgb(255, 255, 255)');
    });
  });

  test.describe('System Mode Visual Validation', () => {
    test('should reflect system dark preference visually', async ({ page }) => {
      // Set browser to prefer dark mode
      await page.emulateMedia({ colorScheme: 'dark' });
      
      // Set theme to system
      await page.evaluate(() => {
        localStorage.setItem('seafood-manager-theme', 'system');
      });
      await page.reload();
      await page.waitForSelector('[data-testid="sidebar"]');
      
      // Should have dark theme applied
      await expect(page.locator('html')).toHaveClass(/dark/);
      
      // Should show Monitor icon for system mode
      const themeButton = page.locator('button', { hasText: /Theme:/ });
      await expect(themeButton).toContainText('Theme: system');
      
      const icon = themeButton.locator('svg').first();
      await expect(icon).toBeVisible();
    });

    test('should reflect system light preference visually', async ({ page }) => {
      // Set browser to prefer light mode
      await page.emulateMedia({ colorScheme: 'light' });
      
      // Set theme to system
      await page.evaluate(() => {
        localStorage.setItem('seafood-manager-theme', 'system');
      });
      await page.reload();
      await page.waitForSelector('[data-testid="sidebar"]');
      
      // Should have light theme applied
      await expect(page.locator('html')).toHaveClass(/light/);
      
      // Should show Monitor icon for system mode
      const themeButton = page.locator('button', { hasText: /Theme:/ });
      await expect(themeButton).toContainText('Theme: system');
    });
  });

  test.describe('Theme Transition Smoothness', () => {
    test('should smoothly transition between themes', async ({ page }) => {
      const themeButton = page.locator('button', { hasText: /Theme:/ });
      
      // Start with light theme
      await page.evaluate(() => {
        localStorage.setItem('seafood-manager-theme', 'light');
      });
      await page.reload();
      await page.waitForSelector('[data-testid="sidebar"]');
      
      // Record initial state
      const initialClass = await page.locator('html').getAttribute('class');
      
      // Click to change theme
      await themeButton.click();
      await page.waitForTimeout(100);
      
      // Check that theme changed
      const newClass = await page.locator('html').getAttribute('class');
      expect(newClass).not.toBe(initialClass);
    });

    test('should maintain layout during theme transitions', async ({ page }) => {
      const sidebar = page.locator('[data-testid="sidebar"]');
      const themeButton = page.locator('button', { hasText: /Theme:/ });
      
      // Record initial dimensions
      const initialBox = await sidebar.boundingBox();
      expect(initialBox).not.toBeNull();
      
      // Change theme
      await themeButton.click();
      await page.waitForTimeout(100);
      
      // Check dimensions haven't changed significantly
      const newBox = await sidebar.boundingBox();
      expect(newBox).not.toBeNull();
      
      if (initialBox && newBox) {
        expect(Math.abs(initialBox.width - newBox.width)).toBeLessThan(5);
        expect(Math.abs(initialBox.height - newBox.height)).toBeLessThan(5);
      }
    });
  });

  test.describe('Accessibility in Different Themes', () => {
    test('should maintain adequate contrast in light mode', async ({ page }) => {
      await page.evaluate(() => {
        localStorage.setItem('seafood-manager-theme', 'light');
      });
      await page.reload();
      await page.waitForSelector('[data-testid="sidebar"]');
      
      // Run accessibility check
      const a11yResults = await page.evaluate(async () => {
        // Simple contrast check for key elements
        const themeButton = document.querySelector('button[title*="theme"], button[aria-label*="theme"]');
        if (themeButton) {
          const styles = getComputedStyle(themeButton);
          return {
            color: styles.color,
            backgroundColor: styles.backgroundColor,
          };
        }
        return null;
      });
      
      expect(a11yResults).not.toBeNull();
    });

    test('should maintain adequate contrast in dark mode', async ({ page }) => {
      await page.evaluate(() => {
        localStorage.setItem('seafood-manager-theme', 'dark');
      });
      await page.reload();
      await page.waitForSelector('[data-testid="sidebar"]');
      
      // Check that dark mode maintains readable contrast
      const bodyStyles = await page.evaluate(() => {
        const body = document.body;
        const html = document.documentElement;
        return {
          bodyBg: getComputedStyle(body).backgroundColor,
          bodyColor: getComputedStyle(body).color,
          htmlClass: html.className,
        };
      });
      
      expect(bodyStyles.htmlClass).toContain('dark');
      
      // In dark mode, background should not be pure white
      expect(bodyStyles.bodyBg).not.toBe('rgb(255, 255, 255)');
    });
  });

  test.describe('Cross-Browser Theme Consistency', () => {
    test('should render consistently across different browsers', async ({ page, browserName }) => {
      // Set specific theme
      await page.evaluate(() => {
        localStorage.setItem('seafood-manager-theme', 'dark');
      });
      await page.reload();
      await page.waitForSelector('[data-testid="sidebar"]');
      
      // Take screenshot for each browser
      await expect(page).toHaveScreenshot(`theme-consistency-${browserName}.png`, {
        fullPage: false,
        clip: { x: 0, y: 0, width: 800, height: 600 }
      });
    });
  });
});