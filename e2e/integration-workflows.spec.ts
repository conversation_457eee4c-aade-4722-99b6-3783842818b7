import { test, expect } from '@playwright/test';

/**
 * Integration Testing for Cross-Feature Workflows
 */

test.describe('Cross-Feature Integration Workflows', () => {
  test.beforeEach(async ({ page }) => {
    // Mock auth
    await page.route('**/auth/**', route => route.fulfill({ 
      status: 200, 
      body: JSON.stringify({ user: { id: 'test' }, session: { access_token: 'token' } }) 
    }));

    // Mock APIs
    await page.route('**/rest/v1/**', route => {
      const url = route.request().url();
      
      if (url.includes('inventory_events')) {
        route.fulfill({
          status: 200,
          body: JSON.stringify([
            {
              id: '1',
              event_type: 'receiving',
              product_name: 'Atlantic Salmon',
              quantity: 25,
              unit: 'lbs',
              created_at: new Date().toISOString(),
              batch_number: 'BATCH001'
            }
          ])
        });
      } else if (url.includes('Products')) {
        route.fulfill({
          status: 200,
          body: JSON.stringify([
            { id: '1', name: 'Atlantic Salmon', category: 'Fresh Fish' }
          ])
        });
      } else {
        route.fulfill({ status: 200, body: JSON.stringify([]) });
      }
    });

    await page.goto('/');
  });

  test.describe('Voice + Forms Integration', () => {
    test('should populate form fields from voice input and submit', async ({ page }) => {
      // Navigate to inventory page
      await page.click('[data-testid="nav-inventory"]');
      await page.click('[data-testid="add-inventory-button"]');
      
      // Activate voice input
      await page.click('[data-testid="voice-input-button"]');
      
      // Simulate voice processing result
      await page.evaluate(() => {
        window.postMessage({
          type: 'VOICE_RESULT',
          payload: {
            action: 'add_inventory',
            product: 'Atlantic Salmon',
            quantity: 30,
            unit: 'lbs',
            batch_number: 'VOICE001'
          }
        }, '*');
      });
      
      // Verify form is populated
      await expect(page.locator('[data-testid="product-select"]')).toHaveValue('Atlantic Salmon');
      await expect(page.locator('[data-testid="quantity-input"]')).toHaveValue('30');
      await expect(page.locator('[data-testid="batch-input"]')).toHaveValue('VOICE001');
      
      // Submit form
      await page.click('[data-testid="submit-inventory"]');
      
      // Verify success and navigation back to list
      await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
      await expect(page.locator('[data-testid="inventory-table"]')).toBeVisible();
    });

    test('should handle voice errors and fallback to manual input', async ({ page }) => {
      await page.click('[data-testid="nav-inventory"]');
      await page.click('[data-testid="add-inventory-button"]');
      
      // Simulate voice error
      await page.click('[data-testid="voice-input-button"]');
      await page.evaluate(() => {
        window.postMessage({
          type: 'VOICE_ERROR',
          payload: { error: 'Speech recognition failed' }
        }, '*');
      });
      
      // Should show error and enable manual input
      await expect(page.locator('[data-testid="voice-error"]')).toBeVisible();
      await expect(page.locator('[data-testid="manual-input-enabled"]')).toBeVisible();
      
      // Continue with manual input
      await page.selectOption('[data-testid="product-select"]', 'Atlantic Salmon');
      await page.fill('[data-testid="quantity-input"]', '20');
      await page.click('[data-testid="submit-inventory"]');
      
      await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    });
  });

  test.describe('Import + HACCP Compliance Integration', () => {
    test('should import data and trigger HACCP compliance checks', async ({ page }) => {
      // Start import process
      await page.click('[data-testid="nav-import"]');
      await page.click('[data-testid="start-import-wizard"]');
      
      // Upload HACCP event data
      await page.setInputFiles('[data-testid="file-upload"]', {
        name: 'haccp-events.csv',
        mimeType: 'text/csv',
        buffer: Buffer.from(`
Event Type,Temperature,Location,Date
temperature_check,45,Receiving,${new Date().toISOString()}
temperature_check,38,Storage,${new Date().toISOString()}
        `.trim())
      });
      
      await page.click('[data-testid="next-step"]');
      await page.click('[data-testid="template-haccp-events"]');
      await page.click('[data-testid="next-step"]');
      
      // Map columns
      await expect(page.locator('[data-testid="mapping-temperature"]')).toHaveValue('Temperature');
      await page.click('[data-testid="next-step"]');
      
      // Validation should catch compliance issues
      await expect(page.locator('[data-testid="compliance-warning"]')).toBeVisible();
      await expect(page.locator('[data-testid="temperature-violation"]')).toContainText('45°F exceeds limit');
      
      // Proceed with import
      await page.click('[data-testid="next-step"]');
      await page.click('[data-testid="confirm-import"]');
      
      // Navigate to HACCP dashboard to verify alerts
      await page.click('[data-testid="nav-haccp"]');
      await expect(page.locator('[data-testid="critical-alert"]')).toBeVisible();
      await expect(page.locator('[data-testid="temperature-alert"]')).toContainText('Temperature exceeded');
    });

    test('should validate imported data against HACCP rules', async ({ page }) => {
      await page.click('[data-testid="nav-import"]');
      await page.click('[data-testid="start-import-wizard"]');
      
      // Upload data with HACCP violations
      await page.setInputFiles('[data-testid="file-upload"]', {
        name: 'violations.csv',
        mimeType: 'text/csv',
        buffer: Buffer.from(`
Product,Receiving Temperature,Storage Time
Salmon,50,8 hours
Cod,35,2 hours
        `.trim())
      });
      
      await page.click('[data-testid="next-step"]');
      await page.click('[data-testid="skip-template"]');
      await page.click('[data-testid="next-step"]');
      
      // Set up mappings
      await page.selectOption('[data-testid="map-receiving-temperature"]', 'temperature');
      await page.click('[data-testid="next-step"]');
      
      // Should show HACCP validation results
      await expect(page.locator('[data-testid="haccp-violations"]')).toBeVisible();
      await expect(page.locator('[data-testid="violation-salmon"]')).toContainText('Temperature too high');
      
      // Should offer corrective actions
      await expect(page.locator('[data-testid="corrective-actions"]')).toBeVisible();
    });
  });

  test.describe('Dashboard Real-time Updates Integration', () => {
    test('should update dashboard when inventory changes', async ({ page }) => {
      // Navigate to dashboard and get initial metrics
      await page.click('[data-testid="nav-dashboard"]');
      await page.waitForSelector('[data-testid="total-inventory"]');
      
      const initialCount = await page.locator('[data-testid="inventory-count"]').textContent();
      
      // Add new inventory
      await page.click('[data-testid="nav-inventory"]');
      await page.click('[data-testid="add-inventory-button"]');
      
      await page.selectOption('[data-testid="product-select"]', 'Atlantic Salmon');
      await page.fill('[data-testid="quantity-input"]', '50');
      await page.click('[data-testid="submit-inventory"]');
      
      // Return to dashboard
      await page.click('[data-testid="nav-dashboard"]');
      
      // Verify metrics updated
      await page.waitForFunction(
        (initial) => {
          const current = document.querySelector('[data-testid="inventory-count"]')?.textContent;
          return current !== initial;
        },
        initialCount
      );
      
      // Verify chart updates
      await expect(page.locator('[data-testid="chart-updated"]')).toBeVisible();
    });

    test('should show real-time HACCP alerts on dashboard', async ({ page }) => {
      await page.click('[data-testid="nav-dashboard"]');
      
      // Simulate HACCP alert
      await page.evaluate(() => {
        window.postMessage({
          type: 'HACCP_ALERT',
          payload: {
            type: 'temperature_violation',
            message: 'Cold storage temperature exceeded',
            severity: 'critical'
          }
        }, '*');
      });
      
      // Should show alert widget on dashboard
      await expect(page.locator('[data-testid="dashboard-alert"]')).toBeVisible();
      await expect(page.locator('[data-testid="alert-temperature"]')).toContainText('temperature exceeded');
      
      // Click alert should navigate to HACCP page
      await page.click('[data-testid="dashboard-alert"]');
      await expect(page).toHaveURL(/haccp/);
    });
  });

  test.describe('Cross-Component Data Flow', () => {
    test('should maintain state during navigation', async ({ page }) => {
      // Set filter on inventory page
      await page.click('[data-testid="nav-inventory"]');
      await page.selectOption('[data-testid="product-filter"]', 'Atlantic Salmon');
      
      // Navigate to analytics
      await page.click('[data-testid="nav-analytics"]');
      await page.waitForLoadState('networkidle');
      
      // Return to inventory
      await page.click('[data-testid="nav-inventory"]');
      
      // Filter should be preserved
      const filterValue = await page.locator('[data-testid="product-filter"]').inputValue();
      expect(filterValue).toBe('Atlantic Salmon');
    });

    test('should propagate batch information across features', async ({ page }) => {
      // Create inventory with batch number
      await page.click('[data-testid="nav-inventory"]');
      await page.click('[data-testid="add-inventory-button"]');
      
      await page.selectOption('[data-testid="product-select"]', 'Atlantic Salmon');
      await page.fill('[data-testid="quantity-input"]', '100');
      await page.fill('[data-testid="batch-input"]', 'BATCH2024001');
      await page.click('[data-testid="submit-inventory"]');
      
      // Navigate to batch tracking
      await page.click('[data-testid="nav-batch-tracking"]');
      
      // Should find the batch
      await page.fill('[data-testid="batch-search"]', 'BATCH2024001');
      await page.click('[data-testid="search-batch"]');
      
      await expect(page.locator('[data-testid="batch-details"]')).toBeVisible();
      await expect(page.locator('[data-testid="batch-product"]')).toContainText('Atlantic Salmon');
      await expect(page.locator('[data-testid="batch-quantity"]')).toContainText('100');
      
      // Navigate to HACCP and check batch association
      await page.click('[data-testid="nav-haccp"]');
      await page.click('[data-testid="events-by-batch"]');
      
      await page.fill('[data-testid="batch-filter"]', 'BATCH2024001');
      await expect(page.locator('[data-testid="batch-events"]')).toBeVisible();
    });
  });

  test.describe('Multi-User Scenarios', () => {
    test('should handle concurrent user interactions', async ({ page, context }) => {
      // Simulate multiple users by opening new pages
      const page2 = await context.newPage();
      
      // Setup page2 with same auth
      await page2.route('**/auth/**', route => route.fulfill({ 
        status: 200, 
        body: JSON.stringify({ user: { id: 'test2' }, session: { access_token: 'token2' } }) 
      }));
      
      await page2.goto('/');
      
      // User 1 adds inventory
      await page.click('[data-testid="nav-inventory"]');
      await page.click('[data-testid="add-inventory-button"]');
      await page.selectOption('[data-testid="product-select"]', 'Atlantic Salmon');
      await page.fill('[data-testid="quantity-input"]', '25');
      await page.click('[data-testid="submit-inventory"]');
      
      // User 2 should see updated data
      await page2.click('[data-testid="nav-inventory"]');
      await page2.reload();
      
      await expect(page2.locator('[data-testid="inventory-table"]')).toContainText('Atlantic Salmon');
    });

    test('should prevent data conflicts', async ({ page }) => {
      // Simulate editing same record from different sessions
      await page.click('[data-testid="nav-inventory"]');
      
      // Start editing a record
      await page.click('[data-testid="edit-inventory-1"]');
      
      // Simulate another user modifying the same record
      await page.evaluate(() => {
        window.postMessage({
          type: 'DATA_CONFLICT',
          payload: {
            record_id: '1',
            modified_by: 'other_user'
          }
        }, '*');
      });
      
      // Should show conflict warning
      await expect(page.locator('[data-testid="conflict-warning"]')).toBeVisible();
      await expect(page.locator('[data-testid="reload-data"]')).toBeVisible();
    });
  });

  test.describe('Error Recovery Workflows', () => {
    test('should recover from network failures', async ({ page }) => {
      await page.click('[data-testid="nav-inventory"]');
      
      // Simulate network failure
      await page.context().setOffline(true);
      
      await page.click('[data-testid="add-inventory-button"]');
      await page.selectOption('[data-testid="product-select"]', 'Atlantic Salmon');
      await page.fill('[data-testid="quantity-input"]', '30');
      await page.click('[data-testid="submit-inventory"]');
      
      // Should show offline message
      await expect(page.locator('[data-testid="offline-warning"]')).toBeVisible();
      await expect(page.locator('[data-testid="save-offline"]')).toBeVisible();
      
      // Save for later sync
      await page.click('[data-testid="save-offline"]');
      
      // Restore network
      await page.context().setOffline(false);
      
      // Should attempt sync
      await page.click('[data-testid="sync-data"]');
      await expect(page.locator('[data-testid="sync-success"]')).toBeVisible();
    });

    test('should handle partial failures gracefully', async ({ page }) => {
      // Mock partial API failure
      await page.route('**/rest/v1/inventory_events', route => {
        if (route.request().method() === 'POST') {
          route.fulfill({
            status: 500,
            body: JSON.stringify({ error: 'Database connection failed' })
          });
        } else {
          route.fulfill({
            status: 200,
            body: JSON.stringify([])
          });
        }
      });
      
      await page.click('[data-testid="nav-inventory"]');
      await page.click('[data-testid="add-inventory-button"]');
      
      await page.selectOption('[data-testid="product-select"]', 'Atlantic Salmon');
      await page.fill('[data-testid="quantity-input"]', '25');
      await page.click('[data-testid="submit-inventory"]');
      
      // Should show error with retry option
      await expect(page.locator('[data-testid="save-error"]')).toBeVisible();
      await expect(page.locator('[data-testid="retry-save"]')).toBeVisible();
      
      // Test retry functionality
      await page.route('**/rest/v1/inventory_events', route => {
        route.fulfill({
          status: 200,
          body: JSON.stringify({ id: '1', success: true })
        });
      });
      
      await page.click('[data-testid="retry-save"]');
      await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    });
  });
});