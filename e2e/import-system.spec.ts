import { test, expect } from '@playwright/test';

/**
 * Enterprise Import System Tests - Concise Version
 */

test.describe('Import System', () => {
  test.beforeEach(async ({ page }) => {
    // Mock auth and APIs
    await page.route('**/auth/**', route => route.fulfill({ 
      status: 200, 
      body: JSON.stringify({ user: { id: 'test' }, session: { access_token: 'token' } }) 
    }));
    
    await page.route('**/rest/v1/**', route => route.fulfill({ 
      status: 200, 
      body: JSON.stringify([]) 
    }));
    
    await page.goto('/');
  });

  test('should complete 6-step import wizard', async ({ page }) => {
    await page.click('[data-testid="nav-import"]');
    await page.click('[data-testid="start-import-wizard"]');

    // Step 1: Upload
    await page.setInputFiles('[data-testid="file-upload"]', {
      name: 'test.csv',
      mimeType: 'text/csv',
      buffer: Buffer.from('Product,Qty\nSalmon,25')
    });
    await page.click('[data-testid="next-step"]');

    // Step 2: Template
    await page.click('[data-testid="template-standard"]');
    await page.click('[data-testid="next-step"]');

    // Step 3: Mapping
    await expect(page.locator('[data-testid="column-mapping"]')).toBeVisible();
    await page.click('[data-testid="next-step"]');

    // Step 4: Validation
    await expect(page.locator('[data-testid="validation-results"]')).toBeVisible();
    await page.click('[data-testid="next-step"]');

    // Step 5: Preview
    await expect(page.locator('[data-testid="import-preview"]')).toBeVisible();
    await page.click('[data-testid="confirm-import"]');

    // Step 6: Processing
    await expect(page.locator('[data-testid="import-progress"]')).toBeVisible();
  });

  test('should handle large file uploads', async ({ page }) => {
    await page.click('[data-testid="nav-import"]');
    
    const largeContent = Array.from({length: 1000}, (_, i) => `Product${i},${i}`).join('\n');
    await page.setInputFiles('[data-testid="file-upload"]', {
      name: 'large.csv',
      mimeType: 'text/csv', 
      buffer: Buffer.from(largeContent)
    });

    await expect(page.locator('[data-testid="upload-progress"]')).toBeVisible();
    await expect(page.locator('[data-testid="file-info"]')).toContainText('1000');
  });

  test('should provide intelligent column mapping', async ({ page }) => {
    await page.click('[data-testid="nav-import"]');
    
    await page.setInputFiles('[data-testid="file-upload"]', {
      name: 'smart.csv',
      mimeType: 'text/csv',
      buffer: Buffer.from('Product_Name,Quantity,Unit\nSalmon,25,lbs')
    });

    await page.click('[data-testid="next-step"]');
    await page.click('[data-testid="template-standard"]');
    await page.click('[data-testid="next-step"]');

    // Verify auto-mapping
    await expect(page.locator('[data-testid="mapping-product"]')).toHaveValue('Product_Name');
    await expect(page.locator('[data-testid="mapping-quantity"]')).toHaveValue('Quantity');
  });
});