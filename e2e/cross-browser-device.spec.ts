import { test, expect, devices } from '@playwright/test';

/**
 * Cross-Browser and Device Testing
 */

// Test on multiple browsers
['chromium', 'firefox', 'webkit'].forEach(browserName => {
  test.describe(`${browserName} Browser Tests`, () => {
    test.use({ ...devices[browserName === 'webkit' ? 'Desktop Safari' : 'Desktop Chrome'] });

    test(`should load dashboard on ${browserName}`, async ({ page }) => {
      await page.route('**/auth/**', route => route.fulfill({ 
        status: 200, 
        body: JSON.stringify({ user: { id: 'test' }, session: { access_token: 'token' } }) 
      }));

      await page.goto('/');
      await expect(page.locator('[data-testid="dashboard"]')).toBeVisible();
      
      // Browser-specific checks
      if (browserName === 'firefox') {
        // Firefox specific validation
        await expect(page.locator('body')).toHaveCSS('font-family', /system-ui/);
      }
    });

    test(`should handle voice input on ${browserName}`, async ({ page }) => {
      // Skip voice tests on webkit (Safari) if WebRTC not supported
      test.skip(browserName === 'webkit', 'WebRTC support varies on Safari');
      
      await page.route('**/auth/**', route => route.fulfill({ 
        status: 200, 
        body: JSON.stringify({ user: { id: 'test' }, session: { access_token: 'token' } }) 
      }));

      await page.goto('/inventory');
      
      if (await page.locator('[data-testid="voice-input-button"]').isVisible()) {
        await page.click('[data-testid="voice-input-button"]');
        await expect(page.locator('[data-testid="voice-recording"]')).toBeVisible();
      }
    });
  });
});

// Mobile device tests
['iPhone 12', 'Pixel 5', 'iPad Pro'].forEach(deviceName => {
  test.describe(`${deviceName} Tests`, () => {
    test.use({ ...devices[deviceName] });

    test(`should be responsive on ${deviceName}`, async ({ page }) => {
      await page.route('**/auth/**', route => route.fulfill({ 
        status: 200, 
        body: JSON.stringify({ user: { id: 'test' }, session: { access_token: 'token' } }) 
      }));

      await page.goto('/');
      
      // Check mobile navigation
      if (deviceName.includes('iPhone') || deviceName.includes('Pixel')) {
        await expect(page.locator('[data-testid="mobile-menu-toggle"]')).toBeVisible();
        await page.click('[data-testid="mobile-menu-toggle"]');
        await expect(page.locator('[data-testid="mobile-nav-menu"]')).toBeVisible();
      }
      
      // Verify touch-friendly elements
      const buttons = page.locator('button');
      const firstButton = buttons.first();
      if (await firstButton.isVisible()) {
        const buttonSize = await firstButton.boundingBox();
        expect(buttonSize?.height).toBeGreaterThan(44); // iOS minimum touch target
      }
    });

    test(`should handle touch interactions on ${deviceName}`, async ({ page }) => {
      await page.route('**/auth/**', route => route.fulfill({ 
        status: 200, 
        body: JSON.stringify({ user: { id: 'test' }, session: { access_token: 'token' } }) 
      }));

      await page.goto('/inventory');
      
      // Test swipe gestures if applicable
      if (await page.locator('[data-testid="swipeable-list"]').isVisible()) {
        const listItem = page.locator('[data-testid="inventory-item"]').first();
        await listItem.swipeRight();
        await expect(page.locator('[data-testid="swipe-actions"]')).toBeVisible();
      }
      
      // Test long press
      if (await page.locator('[data-testid="long-press-item"]').isVisible()) {
        await page.locator('[data-testid="long-press-item"]').press();
        await expect(page.locator('[data-testid="context-menu"]')).toBeVisible();
      }
    });
  });
});

test.describe('Performance Across Devices', () => {
  ['Desktop Chrome', 'iPhone 12', 'Pixel 5'].forEach(device => {
    test(`should load quickly on ${device}`, async ({ page }) => {
      test.use({ ...devices[device] });
      
      await page.route('**/auth/**', route => route.fulfill({ 
        status: 200, 
        body: JSON.stringify({ user: { id: 'test' }, session: { access_token: 'token' } }) 
      }));

      const startTime = Date.now();
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      const loadTime = Date.now() - startTime;
      
      // Performance budgets by device type
      const budget = device.includes('iPhone') || device.includes('Pixel') ? 5000 : 3000;
      expect(loadTime).toBeLessThan(budget);
    });
  });
});

test.describe('Accessibility Across Browsers', () => {
  ['chromium', 'firefox'].forEach(browser => {
    test(`should be accessible on ${browser}`, async ({ page }) => {
      await page.route('**/auth/**', route => route.fulfill({ 
        status: 200, 
        body: JSON.stringify({ user: { id: 'test' }, session: { access_token: 'token' } }) 
      }));

      await page.goto('/');
      
      // Test keyboard navigation
      await page.keyboard.press('Tab');
      const focused = await page.evaluate(() => document.activeElement?.tagName);
      expect(['BUTTON', 'A', 'INPUT']).toContain(focused);
      
      // Test screen reader support
      const mainContent = page.locator('main, [role="main"]');
      await expect(mainContent).toBeVisible();
      
      // Check for ARIA labels
      const buttons = page.locator('button');
      const buttonCount = await buttons.count();
      if (buttonCount > 0) {
        const hasAriaLabel = await buttons.first().getAttribute('aria-label');
        const hasText = await buttons.first().textContent();
        expect(hasAriaLabel || hasText).toBeTruthy();
      }
    });
  });
});

test.describe('Feature Compatibility', () => {
  test('should gracefully degrade voice features', async ({ page, browserName }) => {
    await page.route('**/auth/**', route => route.fulfill({ 
      status: 200, 
      body: JSON.stringify({ user: { id: 'test' }, session: { access_token: 'token' } }) 
    }));

    // Disable speech recognition API
    await page.addInitScript(() => {
      delete (window as any).SpeechRecognition;
      delete (window as any).webkitSpeechRecognition;
    });

    await page.goto('/inventory');
    
    // Verify fallback UI is shown
    if (await page.locator('[data-testid="voice-not-supported"]').isVisible()) {
      await expect(page.locator('[data-testid="manual-input-option"]')).toBeVisible();
    }
  });

  test('should handle offline scenarios', async ({ page }) => {
    await page.route('**/auth/**', route => route.fulfill({ 
      status: 200, 
      body: JSON.stringify({ user: { id: 'test' }, session: { access_token: 'token' } }) 
    }));

    await page.goto('/');
    
    // Go offline
    await page.context().setOffline(true);
    
    // Try to navigate
    await page.click('[data-testid="nav-inventory"]');
    
    // Verify offline handling
    const offlineMessage = page.locator('[data-testid="offline-message"]');
    const errorMessage = page.locator('[data-testid="network-error"]');
    
    const hasOfflineHandling = await offlineMessage.isVisible() || await errorMessage.isVisible();
    expect(hasOfflineHandling).toBe(true);
  });
});