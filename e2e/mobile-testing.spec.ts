import { test, expect, devices } from '@playwright/test';

/**
 * Mobile Testing Strategy and Test Cases
 */

// Mobile device configurations
const mobileDevices = [
  { name: 'iPhone 12', device: devices['iPhone 12'] },
  { name: 'iPhone 12 Pro Max', device: devices['iPhone 12 Pro Max'] },
  { name: 'Pixel 5', device: devices['Pixel 5'] },
  { name: 'Samsung Galaxy S21', device: devices['Galaxy S21'] }
];

const tabletDevices = [
  { name: 'iPad Pro', device: devices['iPad Pro'] },
  { name: 'iPad Mini', device: devices['iPad Mini'] }
];

test.describe('Mobile Testing Strategy', () => {
  
  mobileDevices.forEach(({ name, device }) => {
    test.describe(`${name} Tests`, () => {
      test.use(device);

      test.beforeEach(async ({ page }) => {
        await page.route('**/auth/**', route => route.fulfill({ 
          status: 200, 
          body: JSON.stringify({ user: { id: 'test' }, session: { access_token: 'token' } }) 
        }));

        await page.route('**/rest/v1/**', route => route.fulfill({
          status: 200,
          body: JSON.stringify([])
        }));

        await page.goto('/');
      });

      test('should display mobile navigation', async ({ page }) => {
        // Mobile navigation should be visible
        await expect(page.locator('[data-testid="mobile-menu-toggle"]')).toBeVisible();
        
        // Desktop navigation should be hidden
        await expect(page.locator('[data-testid="desktop-nav"]')).not.toBeVisible();
        
        // Test hamburger menu
        await page.click('[data-testid="mobile-menu-toggle"]');
        await expect(page.locator('[data-testid="mobile-nav-menu"]')).toBeVisible();
        
        // Test navigation items
        await expect(page.locator('[data-testid="mobile-nav-dashboard"]')).toBeVisible();
        await expect(page.locator('[data-testid="mobile-nav-inventory"]')).toBeVisible();
      });

      test('should have touch-friendly interface elements', async ({ page }) => {
        // Check button sizes meet touch target requirements (44px minimum)
        const buttons = page.locator('button');
        const buttonCount = await buttons.count();
        
        for (let i = 0; i < Math.min(buttonCount, 5); i++) {
          const button = buttons.nth(i);
          if (await button.isVisible()) {
            const box = await button.boundingBox();
            expect(box?.height).toBeGreaterThanOrEqual(44);
            expect(box?.width).toBeGreaterThanOrEqual(44);
          }
        }
        
        // Check spacing between interactive elements
        const interactiveElements = page.locator('button, a, input');
        const elementCount = await interactiveElements.count();
        
        if (elementCount > 1) {
          const firstBox = await interactiveElements.first().boundingBox();
          const secondBox = await interactiveElements.nth(1).boundingBox();
          
          if (firstBox && secondBox) {
            const spacing = Math.abs(firstBox.y - secondBox.y);
            expect(spacing).toBeGreaterThanOrEqual(8); // Minimum 8px spacing
          }
        }
      });

      test('should support touch gestures', async ({ page }) => {
        await page.click('[data-testid="nav-inventory"]');
        
        // Test swipe gestures on lists
        const listItems = page.locator('[data-testid="inventory-item"]');
        const itemCount = await listItems.count();
        
        if (itemCount > 0) {
          const firstItem = listItems.first();
          
          // Swipe right to reveal actions
          await firstItem.swipeRight();
          
          // Should show swipe actions
          const swipeActions = page.locator('[data-testid="swipe-actions"]');
          if (await swipeActions.isVisible()) {
            await expect(swipeActions).toBeVisible();
          }
        }
        
        // Test pull-to-refresh
        await page.evaluate(() => {
          window.scrollTo(0, 0);
        });
        
        // Simulate pull down gesture
        await page.touchscreen.tap(200, 100);
        await page.touchscreen.swipe(200, 100, 200, 300);
        
        // Should trigger refresh indicator
        const refreshIndicator = page.locator('[data-testid="pull-refresh"]');
        if (await refreshIndicator.isVisible()) {
          await expect(refreshIndicator).toBeVisible();
        }
      });

      test('should handle voice input on mobile', async ({ page }) => {
        // Check if voice is supported on mobile
        const voiceSupported = await page.evaluate(() => {
          return 'SpeechRecognition' in window || 'webkitSpeechRecognition' in window;
        });
        
        test.skip(!voiceSupported, 'Voice API not supported on this mobile device');
        
        await page.click('[data-testid="nav-inventory"]');
        
        // Voice button should be prominent on mobile
        const voiceButton = page.locator('[data-testid="voice-input-button"]');
        await expect(voiceButton).toBeVisible();
        
        const buttonBox = await voiceButton.boundingBox();
        expect(buttonBox?.height).toBeGreaterThanOrEqual(56); // Larger on mobile
        
        // Test voice activation
        await voiceButton.click();
        await expect(page.locator('[data-testid="voice-recording"]')).toBeVisible();
        
        // Should show visual feedback optimized for mobile
        await expect(page.locator('[data-testid="mobile-voice-indicator"]')).toBeVisible();
      });

      test('should optimize forms for mobile input', async ({ page }) => {
        await page.click('[data-testid="nav-inventory"]');
        await page.click('[data-testid="add-inventory-button"]');
        
        // Form should be mobile-optimized
        const form = page.locator('[data-testid="inventory-form"]');
        await expect(form).toBeVisible();
        
        // Input fields should have appropriate types and keyboard hints
        const quantityInput = page.locator('[data-testid="quantity-input"]');
        const inputType = await quantityInput.getAttribute('type');
        expect(inputType).toBe('number');
        
        const inputMode = await quantityInput.getAttribute('inputmode');
        expect(inputMode).toBe('numeric');
        
        // Test keyboard behavior
        await quantityInput.focus();
        
        // On mobile, numeric keyboard should appear
        // This is device-specific behavior we can't directly test
        // but we can verify the attributes are set correctly
      });

      test('should handle device orientation changes', async ({ page }) => {
        // Test portrait mode (default)
        let viewport = page.viewportSize();
        expect(viewport?.height).toBeGreaterThan(viewport?.width);
        
        // Switch to landscape
        await page.setViewportSize({ width: 812, height: 375 });
        
        // Navigation should adapt
        await expect(page.locator('[data-testid="mobile-menu-toggle"]')).toBeVisible();
        
        // Content should remain accessible
        await expect(page.locator('[data-testid="dashboard"]')).toBeVisible();
        
        // Switch back to portrait
        await page.setViewportSize({ width: 375, height: 812 });
        
        await expect(page.locator('[data-testid="dashboard"]')).toBeVisible();
      });

      test('should handle network connectivity changes', async ({ page }) => {
        // Test with good connection
        await page.click('[data-testid="nav-inventory"]');
        await expect(page.locator('[data-testid="inventory-table"]')).toBeVisible();
        
        // Simulate poor network (offline)
        await page.context().setOffline(true);
        
        // Try to refresh
        await page.reload();
        
        // Should show offline indicator
        const offlineIndicator = page.locator('[data-testid="offline-indicator"]');
        if (await offlineIndicator.isVisible()) {
          await expect(offlineIndicator).toBeVisible();
        }
        
        // Should show cached content if available
        const cachedContent = page.locator('[data-testid="cached-content"]');
        if (await cachedContent.isVisible()) {
          await expect(cachedContent).toBeVisible();
        }
        
        // Restore connection
        await page.context().setOffline(false);
        await page.reload();
        
        await expect(page.locator('[data-testid="inventory-table"]')).toBeVisible();
      });
    });
  });

  tabletDevices.forEach(({ name, device }) => {
    test.describe(`${name} Tablet Tests`, () => {
      test.use(device);

      test.beforeEach(async ({ page }) => {
        await page.route('**/auth/**', route => route.fulfill({ 
          status: 200, 
          body: JSON.stringify({ user: { id: 'test' }, session: { access_token: 'token' } }) 
        }));

        await page.goto('/');
      });

      test('should use hybrid navigation on tablet', async ({ page }) => {
        const viewport = page.viewportSize();
        
        if (viewport && viewport.width >= 768) {
          // Tablet should show expanded navigation
          await expect(page.locator('[data-testid="tablet-nav"]')).toBeVisible();
          
          // Should have more space for content
          const contentArea = page.locator('[data-testid="main-content"]');
          const contentBox = await contentArea.boundingBox();
          expect(contentBox?.width).toBeGreaterThan(600);
        }
      });

      test('should support multi-column layouts', async ({ page }) => {
        await page.click('[data-testid="nav-dashboard"]');
        
        // Tablet should show multi-column dashboard
        const columns = page.locator('[data-testid="dashboard-column"]');
        const columnCount = await columns.count();
        expect(columnCount).toBeGreaterThanOrEqual(2);
        
        // Columns should be properly sized
        for (let i = 0; i < columnCount; i++) {
          const column = columns.nth(i);
          const box = await column.boundingBox();
          expect(box?.width).toBeGreaterThan(200);
        }
      });

      test('should optimize table views for tablet', async ({ page }) => {
        await page.click('[data-testid="nav-inventory"]');
        
        // Tablet should show full table with more columns
        const table = page.locator('[data-testid="inventory-table"]');
        await expect(table).toBeVisible();
        
        const headerCells = page.locator('th');
        const cellCount = await headerCells.count();
        expect(cellCount).toBeGreaterThanOrEqual(5); // More columns on tablet
        
        // Should support horizontal scrolling if needed
        await table.evaluate(element => {
          element.scrollLeft = 100;
        });
      });
    });
  });

  test.describe('Progressive Web App Features', () => {
    test.use(devices['iPhone 12']);

    test('should support PWA installation', async ({ page }) => {
      await page.goto('/');
      
      // Check for PWA manifest
      const manifest = page.locator('link[rel="manifest"]');
      await expect(manifest).toHaveAttribute('href');
      
      // Check for service worker registration
      const swRegistered = await page.evaluate(() => {
        return 'serviceWorker' in navigator;
      });
      expect(swRegistered).toBe(true);
      
      // Test add to home screen prompt (if supported)
      const installPrompt = page.locator('[data-testid="install-prompt"]');
      if (await installPrompt.isVisible()) {
        await expect(installPrompt).toBeVisible();
        
        await installPrompt.click();
        // Installation flow would continue here
      }
    });

    test('should work offline with service worker', async ({ page }) => {
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      // Go offline
      await page.context().setOffline(true);
      
      // Navigate to cached pages
      await page.click('[data-testid="nav-inventory"]');
      
      // Should still load from cache
      await expect(page.locator('[data-testid="inventory-page"]')).toBeVisible();
      
      // Should show offline indicator
      const offlineStatus = page.locator('[data-testid="connection-status"]');
      if (await offlineStatus.isVisible()) {
        await expect(offlineStatus).toContainText('offline');
      }
    });

    test('should support push notifications', async ({ page }) => {
      // Check notification permission
      const notificationSupported = await page.evaluate(() => {
        return 'Notification' in window;
      });
      
      test.skip(!notificationSupported, 'Notifications not supported');
      
      await page.goto('/');
      
      // Test notification permission request
      const permissionButton = page.locator('[data-testid="enable-notifications"]');
      if (await permissionButton.isVisible()) {
        await permissionButton.click();
        
        // Mock permission grant
        await page.evaluate(() => {
          Object.defineProperty(Notification, 'permission', {
            value: 'granted',
            writable: false
          });
        });
        
        await expect(page.locator('[data-testid="notifications-enabled"]')).toBeVisible();
      }
    });
  });

  test.describe('Mobile Performance', () => {
    test.use(devices['Pixel 5']);

    test('should load quickly on mobile networks', async ({ page }) => {
      // Simulate slow 3G connection
      await page.route('**/*', route => {
        setTimeout(() => route.continue(), 100); // Add 100ms delay
      });
      
      const startTime = Date.now();
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      const loadTime = Date.now() - startTime;
      
      // Should load within mobile performance budget
      expect(loadTime).toBeLessThan(5000); // 5 seconds on slow connection
    });

    test('should minimize resource usage', async ({ page }) => {
      await page.goto('/');
      
      // Check JavaScript bundle size for mobile
      const jsRequests = [];
      page.on('response', response => {
        if (response.url().includes('.js')) {
          jsRequests.push(parseInt(response.headers()['content-length'] || '0'));
        }
      });
      
      await page.waitForLoadState('networkidle');
      
      const totalJs = jsRequests.reduce((sum, size) => sum + size, 0);
      const totalJsMB = totalJs / (1024 * 1024);
      
      // Mobile bundle should be smaller
      expect(totalJsMB).toBeLessThan(1.5); // 1.5MB limit for mobile
    });

    test('should implement efficient scrolling', async ({ page }) => {
      await page.click('[data-testid="nav-inventory"]');
      
      // Test virtual scrolling for large lists
      const startTime = Date.now();
      
      await page.evaluate(() => {
        const container = document.querySelector('[data-testid="inventory-table"]');
        if (container) {
          container.scrollTop = container.scrollHeight;
        }
      });
      
      const scrollTime = Date.now() - startTime;
      expect(scrollTime).toBeLessThan(100); // Smooth scrolling
      
      // Check for virtual scrolling implementation
      const virtualContainer = page.locator('[data-testid="virtual-list"]');
      if (await virtualContainer.isVisible()) {
        // Should only render visible items
        const renderedItems = page.locator('[data-testid="list-item"]');
        const itemCount = await renderedItems.count();
        expect(itemCount).toBeLessThan(50); // Limited rendered items
      }
    });
  });

  test.describe('Mobile Accessibility', () => {
    test.use(devices['iPhone 12']);

    test('should support screen reader navigation', async ({ page }) => {
      await page.goto('/');
      
      // Check for proper heading structure
      const headings = await page.evaluate(() => {
        const headingElements = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
        return Array.from(headingElements).map(h => ({
          level: parseInt(h.tagName.charAt(1)),
          text: h.textContent
        }));
      });
      
      expect(headings.length).toBeGreaterThan(0);
      expect(headings[0].level).toBe(1); // Should start with h1
      
      // Check for landmarks
      await expect(page.locator('main, [role="main"]')).toBeVisible();
      await expect(page.locator('nav, [role="navigation"]')).toBeVisible();
    });

    test('should support voice control commands', async ({ page }) => {
      // Test common voice commands
      await page.goto('/');
      
      // Simulate voice command: "Go to inventory"
      await page.keyboard.press('Tab'); // Focus first element
      await page.keyboard.press('Enter'); // Activate
      
      // Should support voice navigation patterns
      const skipLink = page.locator('[href="#main"]');
      if (await skipLink.isVisible()) {
        await skipLink.click();
        
        const mainContent = page.locator('#main, [role="main"]');
        await expect(mainContent).toBeFocused();
      }
    });

    test('should provide haptic feedback cues', async ({ page }) => {
      await page.goto('/');
      
      // Test for haptic feedback triggers (vibration API)
      const hasVibration = await page.evaluate(() => {
        return 'vibrate' in navigator;
      });
      
      if (hasVibration) {
        // Simulate button press that should trigger haptic feedback
        await page.click('[data-testid="primary-action-button"]');
        
        // Verify vibration was called (mocked in testing)
        const vibrationCalled = await page.evaluate(() => {
          return (window as any).vibrationCalled;
        });
        
        // This would need to be implemented in the app
        // expect(vibrationCalled).toBe(true);
      }
    });
  });
});