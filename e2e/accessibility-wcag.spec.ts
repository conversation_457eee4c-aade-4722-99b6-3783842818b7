import { test, expect } from '@playwright/test';

/**
 * Accessibility Testing Suite (WCAG Compliance)
 */

test.describe('WCAG Accessibility Compliance', () => {
  test.beforeEach(async ({ page }) => {
    await page.route('**/auth/**', route => route.fulfill({ 
      status: 200, 
      body: JSON.stringify({ user: { id: 'test' }, session: { access_token: 'token' } }) 
    }));

    await page.route('**/rest/v1/**', route => route.fulfill({
      status: 200,
      body: JSON.stringify([])
    }));

    await page.goto('/');
  });

  test.describe('Keyboard Navigation', () => {
    test('should support full keyboard navigation', async ({ page }) => {
      // Test tab navigation
      await page.keyboard.press('Tab');
      let focusedElement = await page.evaluate(() => document.activeElement?.tagName);
      expect(['BUTTON', 'A', 'INPUT'].includes(focusedElement)).toBe(true);

      // Navigate through main elements
      const focusableElements = [];
      for (let i = 0; i < 10; i++) {
        await page.keyboard.press('Tab');
        const element = await page.evaluate(() => ({
          tag: document.activeElement?.tagName,
          id: document.activeElement?.id,
          role: document.activeElement?.getAttribute('role'),
          ariaLabel: document.activeElement?.getAttribute('aria-label')
        }));
        focusableElements.push(element);
      }

      // Should find focusable elements
      expect(focusableElements.length).toBeGreaterThan(0);
    });

    test('should handle Enter and Space key interactions', async ({ page }) => {
      // Find first button
      const button = page.locator('button').first();
      await button.focus();
      
      // Test Enter key
      await page.keyboard.press('Enter');
      // Verify button action (implementation specific)
      
      // Test Space key on buttons
      await page.keyboard.press('Space');
    });

    test('should support arrow key navigation in menus', async ({ page }) => {
      // Open dropdown/menu if exists
      const menuTrigger = page.locator('[role="button"][aria-haspopup]').first();
      if (await menuTrigger.isVisible()) {
        await menuTrigger.click();
        
        // Test arrow navigation
        await page.keyboard.press('ArrowDown');
        const focused = await page.evaluate(() => document.activeElement?.getAttribute('role'));
        expect(['menuitem', 'option'].includes(focused)).toBe(true);
      }
    });

    test('should trap focus in modals', async ({ page }) => {
      // Open modal if available
      const modalTrigger = page.locator('[data-testid*="add"], [data-testid*="create"]').first();
      if (await modalTrigger.isVisible()) {
        await modalTrigger.click();
        
        // Verify modal is open
        const modal = page.locator('[role="dialog"], [data-testid*="modal"]');
        await expect(modal).toBeVisible();
        
        // Test focus trap
        await page.keyboard.press('Tab');
        const focusedInModal = await page.evaluate(() => {
          const activeEl = document.activeElement;
          const modal = document.querySelector('[role="dialog"]');
          return modal?.contains(activeEl);
        });
        
        expect(focusedInModal).toBe(true);
      }
    });
  });

  test.describe('Screen Reader Support', () => {
    test('should have proper semantic HTML structure', async ({ page }) => {
      // Check for main landmarks
      await expect(page.locator('main, [role="main"]')).toBeVisible();
      await expect(page.locator('nav, [role="navigation"]')).toBeVisible();
      
      // Check heading hierarchy
      const headings = await page.evaluate(() => {
        const headings = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6'));
        return headings.map(h => ({
          level: parseInt(h.tagName.charAt(1)),
          text: h.textContent?.trim()
        }));
      });
      
      expect(headings.length).toBeGreaterThan(0);
      expect(headings[0].level).toBe(1); // Should start with h1
    });

    test('should have proper ARIA labels and descriptions', async ({ page }) => {
      // Check buttons have accessible names
      const buttons = page.locator('button');
      const buttonCount = await buttons.count();
      
      for (let i = 0; i < Math.min(buttonCount, 5); i++) {
        const button = buttons.nth(i);
        const accessibleName = await button.evaluate(el => {
          return el.getAttribute('aria-label') || 
                 el.getAttribute('aria-labelledby') ||
                 el.textContent?.trim();
        });
        expect(accessibleName).toBeTruthy();
      }

      // Check form inputs have labels
      const inputs = page.locator('input[type="text"], input[type="email"], input[type="number"]');
      const inputCount = await inputs.count();
      
      for (let i = 0; i < Math.min(inputCount, 3); i++) {
        const input = inputs.nth(i);
        const hasLabel = await input.evaluate(el => {
          const id = el.id;
          const label = document.querySelector(`label[for="${id}"]`);
          const ariaLabel = el.getAttribute('aria-label');
          const ariaLabelledby = el.getAttribute('aria-labelledby');
          
          return !!(label || ariaLabel || ariaLabelledby);
        });
        expect(hasLabel).toBe(true);
      }
    });

    test('should announce dynamic content changes', async ({ page }) => {
      // Check for live regions
      const liveRegions = page.locator('[aria-live]');
      const liveRegionCount = await liveRegions.count();
      
      if (liveRegionCount > 0) {
        // Test that status messages use live regions
        const statusRegion = liveRegions.first();
        await expect(statusRegion).toBeVisible();
        
        const ariaLive = await statusRegion.getAttribute('aria-live');
        expect(['polite', 'assertive'].includes(ariaLive)).toBe(true);
      }
    });

    test('should provide alternative text for images', async ({ page }) => {
      const images = page.locator('img');
      const imageCount = await images.count();
      
      for (let i = 0; i < Math.min(imageCount, 5); i++) {
        const img = images.nth(i);
        const alt = await img.getAttribute('alt');
        const role = await img.getAttribute('role');
        
        // Images should have alt text or be decorative
        expect(alt !== null || role === 'presentation').toBe(true);
      }
    });
  });

  test.describe('Color and Contrast', () => {
    test('should meet color contrast requirements', async ({ page }) => {
      // Get computed styles for text elements
      const textElements = page.locator('p, span, div, button, a').first();
      
      const contrast = await textElements.evaluate(el => {
        const style = window.getComputedStyle(el);
        return {
          color: style.color,
          backgroundColor: style.backgroundColor,
          fontSize: style.fontSize
        };
      });
      
      // Basic check that colors are defined
      expect(contrast.color).toBeTruthy();
      expect(contrast.backgroundColor).toBeTruthy();
    });

    test('should not rely solely on color for information', async ({ page }) => {
      // Check error states have non-color indicators
      const errorElements = page.locator('[class*="error"], [aria-invalid="true"]');
      const errorCount = await errorElements.count();
      
      if (errorCount > 0) {
        const errorElement = errorElements.first();
        
        // Check for text indicators
        const hasErrorText = await errorElement.evaluate(el => {
          const text = el.textContent?.toLowerCase() || '';
          return text.includes('error') || text.includes('invalid') || text.includes('required');
        });
        
        // Check for ARIA attributes
        const hasAriaInvalid = await errorElement.getAttribute('aria-invalid');
        const hasAriaDescribedby = await errorElement.getAttribute('aria-describedby');
        
        expect(hasErrorText || hasAriaInvalid || hasAriaDescribedby).toBe(true);
      }
    });
  });

  test.describe('Form Accessibility', () => {
    test('should associate labels with form controls', async ({ page }) => {
      // Navigate to a form
      const formPage = page.locator('form').first();
      if (await formPage.isVisible()) {
        
        const formControls = page.locator('input, select, textarea');
        const controlCount = await formControls.count();
        
        for (let i = 0; i < Math.min(controlCount, 3); i++) {
          const control = formControls.nth(i);
          
          const labelAssociation = await control.evaluate(el => {
            const id = el.id;
            const name = el.name;
            
            // Check for label element
            const label = document.querySelector(`label[for="${id}"]`);
            
            // Check for aria-label
            const ariaLabel = el.getAttribute('aria-label');
            
            // Check for aria-labelledby
            const ariaLabelledby = el.getAttribute('aria-labelledby');
            
            return !!(label || ariaLabel || ariaLabelledby);
          });
          
          expect(labelAssociation).toBe(true);
        }
      }
    });

    test('should provide helpful error messages', async ({ page }) => {
      // Try to find a form with validation
      const submitButton = page.locator('button[type="submit"]').first();
      if (await submitButton.isVisible()) {
        
        // Try to submit empty form
        await submitButton.click();
        
        // Check for error messages
        const errorMessages = page.locator('[role="alert"], [aria-live="assertive"], [class*="error"]');
        const errorCount = await errorMessages.count();
        
        if (errorCount > 0) {
          const errorMessage = errorMessages.first();
          const errorText = await errorMessage.textContent();
          
          // Error message should be descriptive
          expect(errorText?.length).toBeGreaterThan(5);
          expect(errorText).not.toBe('Error');
        }
      }
    });

    test('should group related form fields', async ({ page }) => {
      // Check for fieldsets
      const fieldsets = page.locator('fieldset');
      const fieldsetCount = await fieldsets.count();
      
      if (fieldsetCount > 0) {
        // Fieldsets should have legends
        const legend = fieldsets.first().locator('legend');
        await expect(legend).toBeVisible();
      }
      
      // Check for ARIA groups
      const groups = page.locator('[role="group"]');
      const groupCount = await groups.count();
      
      for (let i = 0; i < Math.min(groupCount, 2); i++) {
        const group = groups.nth(i);
        const hasLabel = await group.evaluate(el => {
          return el.getAttribute('aria-label') || el.getAttribute('aria-labelledby');
        });
        expect(hasLabel).toBeTruthy();
      }
    });
  });

  test.describe('Interactive Elements', () => {
    test('should provide clear focus indicators', async ({ page }) => {
      // Focus on interactive elements and check for visible focus
      const interactiveElements = page.locator('button, a, input, select');
      const elementCount = await interactiveElements.count();
      
      if (elementCount > 0) {
        const element = interactiveElements.first();
        await element.focus();
        
        // Check if focus styles are applied
        const focusStyles = await element.evaluate(el => {
          const style = window.getComputedStyle(el);
          return {
            outline: style.outline,
            outlineWidth: style.outlineWidth,
            boxShadow: style.boxShadow
          };
        });
        
        // Should have some form of focus indicator
        const hasFocusIndicator = 
          focusStyles.outline !== 'none' ||
          focusStyles.outlineWidth !== '0px' ||
          focusStyles.boxShadow !== 'none';
        
        expect(hasFocusIndicator).toBe(true);
      }
    });

    test('should have appropriate ARIA states', async ({ page }) => {
      // Check buttons with states
      const toggleButtons = page.locator('[aria-pressed], [aria-expanded]');
      const toggleCount = await toggleButtons.count();
      
      for (let i = 0; i < Math.min(toggleCount, 3); i++) {
        const button = toggleButtons.nth(i);
        
        const ariaPressed = await button.getAttribute('aria-pressed');
        const ariaExpanded = await button.getAttribute('aria-expanded');
        
        if (ariaPressed) {
          expect(['true', 'false'].includes(ariaPressed)).toBe(true);
        }
        
        if (ariaExpanded) {
          expect(['true', 'false'].includes(ariaExpanded)).toBe(true);
        }
      }
    });

    test('should handle disabled states properly', async ({ page }) => {
      const disabledElements = page.locator('[disabled], [aria-disabled="true"]');
      const disabledCount = await disabledElements.count();
      
      for (let i = 0; i < Math.min(disabledCount, 3); i++) {
        const element = disabledElements.nth(i);
        
        // Disabled elements should not be focusable
        await element.focus();
        const isFocused = await element.evaluate(el => el === document.activeElement);
        expect(isFocused).toBe(false);
      }
    });
  });

  test.describe('Table Accessibility', () => {
    test('should have proper table structure', async ({ page }) => {
      const tables = page.locator('table');
      const tableCount = await tables.count();
      
      for (let i = 0; i < Math.min(tableCount, 2); i++) {
        const table = tables.nth(i);
        
        // Check for table headers
        const headers = table.locator('th');
        const headerCount = await headers.count();
        expect(headerCount).toBeGreaterThan(0);
        
        // Check for scope attributes
        const headerWithScope = table.locator('th[scope]');
        const scopeCount = await headerWithScope.count();
        
        if (headerCount > 0) {
          expect(scopeCount).toBeGreaterThan(0);
        }
        
        // Check for caption or aria-label
        const caption = table.locator('caption');
        const ariaLabel = await table.getAttribute('aria-label');
        const ariaLabelledby = await table.getAttribute('aria-labelledby');
        
        const hasTableLabel = 
          await caption.count() > 0 || 
          ariaLabel || 
          ariaLabelledby;
        
        expect(hasTableLabel).toBe(true);
      }
    });
  });

  test.describe('Skip Links and Navigation', () => {
    test('should provide skip to main content link', async ({ page }) => {
      // Focus on first element (usually skip link)
      await page.keyboard.press('Tab');
      
      const skipLink = page.locator('a[href="#main"], a[href="#content"]').first();
      if (await skipLink.isVisible()) {
        const skipText = await skipLink.textContent();
        expect(skipText?.toLowerCase()).toContain('skip');
      }
    });

    test('should have consistent navigation', async ({ page }) => {
      // Check navigation landmarks
      const navElements = page.locator('nav, [role="navigation"]');
      const navCount = await navElements.count();
      
      expect(navCount).toBeGreaterThan(0);
      
      // Navigation should have accessible names
      for (let i = 0; i < Math.min(navCount, 2); i++) {
        const nav = navElements.nth(i);
        const hasLabel = await nav.evaluate(el => {
          return el.getAttribute('aria-label') || 
                 el.getAttribute('aria-labelledby') ||
                 el.querySelector('h1, h2, h3, h4, h5, h6');
        });
        expect(hasLabel).toBeTruthy();
      }
    });
  });
});