import { test, expect, Page, BrowserContext } from '@playwright/test';

/**
 * Voice Processing UI Test Suite
 * Tests voice input components, real-time feedback, mobile interface, and error handling
 */

test.describe('Voice Processing UI', () => {
  let page: Page;
  let context: BrowserContext;

  test.beforeEach(async ({ page: p, context: c }) => {
    page = p;
    context = c;

    // Grant microphone permissions
    await context.grantPermissions(['microphone']);

    // Mock Web Speech API
    await page.addInitScript(() => {
      // Mock SpeechRecognition
      class MockSpeechRecognition extends EventTarget {
        continuous = false;
        interimResults = false;
        lang = 'en-US';
        
        start() {
          setTimeout(() => {
            this.dispatchEvent(new Event('start'));
          }, 100);
        }
        
        stop() {
          setTimeout(() => {
            this.dispatchEvent(new Event('end'));
          }, 100);
        }
        
        abort() {
          this.dispatchEvent(new Event('end'));
        }

        // Method to simulate speech results
        simulateResult(transcript: string, isFinal: boolean = true) {
          const event = new Event('result') as any;
          event.results = [{
            0: { transcript, confidence: 0.9 },
            isFinal,
            length: 1
          }];
          event.resultIndex = 0;
          this.dispatchEvent(event);
        }

        simulateError(error: string = 'network') {
          const event = new Event('error') as any;
          event.error = error;
          this.dispatchEvent(event);
        }
      }

      // Mock MediaDevices.getUserMedia
      navigator.mediaDevices = navigator.mediaDevices || {};
      navigator.mediaDevices.getUserMedia = async () => {
        return new MediaStream();
      };

      // Make SpeechRecognition available globally
      (window as any).SpeechRecognition = MockSpeechRecognition;
      (window as any).webkitSpeechRecognition = MockSpeechRecognition;
      (window as any).mockSpeechRecognition = null;
    });

    // Mock authentication
    await page.route('**/auth/**', (route) => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: { id: 'test-user', email: '<EMAIL>' },
          session: { access_token: 'mock-token' }
        })
      });
    });

    // Mock OpenAI API for voice processing
    await page.route('**/openai/**', (route) => {
      const requestBody = route.request().postDataJSON();
      
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          choices: [{
            message: {
              content: JSON.stringify({
                action: 'add_inventory',
                product: 'Atlantic Salmon',
                quantity: 25,
                unit: 'lbs',
                confidence: 0.95
              })
            }
          }]
        })
      });
    });

    // Mock Supabase API calls
    await page.route('**/rest/v1/**', (route) => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([])
      });
    });

    await page.goto('/');
  });

  test.describe('Voice Input Components', () => {
    test('should activate voice recording with visual feedback', async () => {
      await page.click('[data-testid="nav-inventory"]');
      
      // Click voice input button
      await page.click('[data-testid="voice-input-button"]');
      
      // Verify visual indicators for recording state
      await expect(page.locator('[data-testid="voice-recording-indicator"]')).toBeVisible();
      await expect(page.locator('[data-testid="voice-input-button"]')).toHaveClass(/recording/);
      
      // Verify pulse animation
      const pulseElement = page.locator('[data-testid="voice-pulse-animation"]');
      await expect(pulseElement).toBeVisible();
      await expect(pulseElement).toHaveClass(/animate-pulse/);
      
      // Stop recording
      await page.click('[data-testid="voice-input-button"]');
      await expect(page.locator('[data-testid="voice-recording-indicator"]')).not.toBeVisible();
    });

    test('should display real-time transcription', async () => {
      await page.click('[data-testid="nav-inventory"]');
      await page.click('[data-testid="voice-input-button"]');
      
      // Simulate speech recognition results
      await page.evaluate(() => {
        const recognition = (window as any).mockSpeechRecognition;
        if (recognition) {
          recognition.simulateResult('Add twenty five pounds of salmon', false);
        }
      });
      
      // Verify interim transcription appears
      await expect(page.locator('[data-testid="interim-transcript"]')).toContainText('Add twenty five');
      
      // Simulate final result
      await page.evaluate(() => {
        const recognition = (window as any).mockSpeechRecognition;
        if (recognition) {
          recognition.simulateResult('Add twenty five pounds of salmon to inventory', true);
        }
      });
      
      // Verify final transcription
      await expect(page.locator('[data-testid="final-transcript"]')).toContainText('Add twenty five pounds of salmon');
    });

    test('should handle voice permission requests', async () => {
      // Deny microphone permission
      await context.clearPermissions();
      
      await page.click('[data-testid="nav-inventory"]');
      await page.click('[data-testid="voice-input-button"]');
      
      // Verify permission request message
      await expect(page.locator('[data-testid="microphone-permission-request"]')).toBeVisible();
      
      // Grant permission
      await context.grantPermissions(['microphone']);
      await page.click('[data-testid="grant-permission-button"]');
      
      // Verify voice input becomes available
      await expect(page.locator('[data-testid="voice-input-button"]')).toBeEnabled();
    });
  });

  test.describe('Seafood Terminology Recognition', () => {
    test('should accurately process seafood-specific terms', async () => {
      const seafoodTerms = [
        { input: 'dungeness crab', expected: 'Dungeness Crab' },
        { input: 'coho salmon', expected: 'Coho Salmon' },
        { input: 'pacific halibut', expected: 'Pacific Halibut' },
        { input: 'alaskan pollock', expected: 'Alaskan Pollock' },
        { input: 'blue mussels', expected: 'Blue Mussels' }
      ];

      await page.click('[data-testid="nav-inventory"]');

      for (const term of seafoodTerms) {
        await page.click('[data-testid="voice-input-button"]');
        
        // Simulate voice input
        await page.evaluate((transcript) => {
          const recognition = (window as any).mockSpeechRecognition;
          if (recognition) {
            recognition.simulateResult(`Add ten pounds of ${transcript}`, true);
          }
        }, term.input);

        // Verify AI processing converts to proper terminology
        await expect(page.locator('[data-testid="processed-product-name"]')).toContainText(term.expected);
        
        await page.click('[data-testid="voice-input-clear"]');
      }
    });

    test('should handle measurement units correctly', async () => {
      const measurements = [
        { input: 'twenty five pounds', expected: { quantity: '25', unit: 'lbs' } },
        { input: 'fifteen kilograms', expected: { quantity: '15', unit: 'kg' } },
        { input: 'fifty pieces', expected: { quantity: '50', unit: 'pieces' } },
        { input: 'three boxes', expected: { quantity: '3', unit: 'boxes' } }
      ];

      await page.click('[data-testid="nav-inventory"]');

      for (const measurement of measurements) {
        await page.click('[data-testid="voice-input-button"]');
        
        await page.evaluate((transcript) => {
          const recognition = (window as any).mockSpeechRecognition;
          if (recognition) {
            recognition.simulateResult(`Add ${transcript} of salmon`, true);
          }
        }, measurement.input);

        // Verify quantity extraction
        await expect(page.locator('[data-testid="processed-quantity"]')).toHaveValue(measurement.expected.quantity);
        await expect(page.locator('[data-testid="processed-unit"]')).toHaveValue(measurement.expected.unit);
        
        await page.click('[data-testid="voice-input-clear"]');
      }
    });
  });

  test.describe('Voice-to-Form Integration', () => {
    test('should populate inventory form from voice input', async () => {
      await page.click('[data-testid="nav-inventory"]');
      await page.click('[data-testid="add-inventory-button"]');
      
      // Activate voice input in form
      await page.click('[data-testid="form-voice-input"]');
      
      // Simulate comprehensive voice command
      await page.evaluate(() => {
        const recognition = (window as any).mockSpeechRecognition;
        if (recognition) {
          recognition.simulateResult(
            'Add thirty pounds of Atlantic salmon from batch SAL001 received today',
            true
          );
        }
      });

      // Wait for AI processing
      await page.waitForSelector('[data-testid="ai-processing-complete"]');
      
      // Verify form fields are populated
      await expect(page.locator('[data-testid="product-select"]')).toHaveValue('Atlantic Salmon');
      await expect(page.locator('[data-testid="quantity-input"]')).toHaveValue('30');
      await expect(page.locator('[data-testid="unit-select"]')).toHaveValue('lbs');
      await expect(page.locator('[data-testid="batch-number-input"]')).toHaveValue('SAL001');
      await expect(page.locator('[data-testid="event-type-select"]')).toHaveValue('receiving');
    });

    test('should handle ambiguous voice commands', async () => {
      await page.click('[data-testid="nav-inventory"]');
      await page.click('[data-testid="add-inventory-button"]');
      await page.click('[data-testid="form-voice-input"]');
      
      // Simulate ambiguous input
      await page.evaluate(() => {
        const recognition = (window as any).mockSpeechRecognition;
        if (recognition) {
          recognition.simulateResult('Add some fish to inventory', true);
        }
      });

      // Verify clarification dialog appears
      await expect(page.locator('[data-testid="voice-clarification-dialog"]')).toBeVisible();
      
      // Verify suggested options
      await expect(page.locator('[data-testid="product-suggestions"]')).toBeVisible();
      await expect(page.locator('[data-testid="quantity-clarification"]')).toBeVisible();
      
      // Select clarification option
      await page.click('[data-testid="suggest-salmon"]');
      await page.fill('[data-testid="clarify-quantity"]', '15');
      await page.click('[data-testid="apply-clarification"]');
      
      // Verify form is updated
      await expect(page.locator('[data-testid="product-select"]')).toHaveValue('Atlantic Salmon');
      await expect(page.locator('[data-testid="quantity-input"]')).toHaveValue('15');
    });

    test('should provide confidence indicators', async () => {
      await page.click('[data-testid="nav-inventory"]');
      await page.click('[data-testid="add-inventory-button"]');
      await page.click('[data-testid="form-voice-input"]');
      
      await page.evaluate(() => {
        const recognition = (window as any).mockSpeechRecognition;
        if (recognition) {
          recognition.simulateResult('Add twenty pounds of salmon', true);
        }
      });

      // Verify confidence indicators
      await expect(page.locator('[data-testid="product-confidence"]')).toBeVisible();
      await expect(page.locator('[data-testid="quantity-confidence"]')).toBeVisible();
      
      // High confidence should show green indicator
      const productConfidence = page.locator('[data-testid="product-confidence"]');
      await expect(productConfidence).toHaveClass(/confidence-high/);
      
      // Medium/low confidence should show warning colors
      if (await page.locator('[data-testid="quantity-confidence"].confidence-medium').isVisible()) {
        await expect(page.locator('[data-testid="confidence-warning"]')).toBeVisible();
      }
    });
  });

  test.describe('Error Handling and Recovery', () => {
    test('should handle microphone access errors', async () => {
      // Simulate microphone access denied
      await page.evaluate(() => {
        navigator.mediaDevices.getUserMedia = () => 
          Promise.reject(new Error('Permission denied'));
      });

      await page.click('[data-testid="nav-inventory"]');
      await page.click('[data-testid="voice-input-button"]');
      
      // Verify error message
      await expect(page.locator('[data-testid="microphone-error"]')).toBeVisible();
      await expect(page.locator('[data-testid="microphone-error"]')).toContainText('microphone access');
      
      // Verify fallback to manual input
      await expect(page.locator('[data-testid="manual-input-fallback"]')).toBeVisible();
    });

    test('should handle speech recognition errors', async () => {
      await page.click('[data-testid="nav-inventory"]');
      await page.click('[data-testid="voice-input-button"]');
      
      // Simulate speech recognition error
      await page.evaluate(() => {
        const recognition = (window as any).mockSpeechRecognition;
        if (recognition) {
          recognition.simulateError('network');
        }
      });

      // Verify error handling
      await expect(page.locator('[data-testid="speech-recognition-error"]')).toBeVisible();
      await expect(page.locator('[data-testid="retry-voice-input"]')).toBeVisible();
      
      // Test retry functionality
      await page.click('[data-testid="retry-voice-input"]');
      await expect(page.locator('[data-testid="voice-input-button"]')).toBeEnabled();
    });

    test('should handle AI processing failures', async () => {
      // Mock AI API failure
      await page.route('**/openai/**', (route) => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Service unavailable' })
        });
      });

      await page.click('[data-testid="nav-inventory"]');
      await page.click('[data-testid="voice-input-button"]');
      
      await page.evaluate(() => {
        const recognition = (window as any).mockSpeechRecognition;
        if (recognition) {
          recognition.simulateResult('Add salmon to inventory', true);
        }
      });

      // Verify error handling
      await expect(page.locator('[data-testid="ai-processing-error"]')).toBeVisible();
      
      // Verify fallback to raw transcript
      await expect(page.locator('[data-testid="raw-transcript"]')).toContainText('Add salmon to inventory');
      await expect(page.locator('[data-testid="manual-processing-option"]')).toBeVisible();
    });

    test('should handle network disconnection during voice processing', async () => {
      await page.click('[data-testid="nav-inventory"]');
      await page.click('[data-testid="voice-input-button"]');
      
      // Simulate network disconnection
      await context.setOffline(true);
      
      await page.evaluate(() => {
        const recognition = (window as any).mockSpeechRecognition;
        if (recognition) {
          recognition.simulateResult('Add twenty pounds of cod', true);
        }
      });

      // Verify offline handling
      await expect(page.locator('[data-testid="offline-voice-warning"]')).toBeVisible();
      await expect(page.locator('[data-testid="cache-transcript-button"]')).toBeVisible();
      
      // Cache transcript for later processing
      await page.click('[data-testid="cache-transcript-button"]');
      
      // Restore connection
      await context.setOffline(false);
      
      // Verify cached transcript can be processed
      if (await page.locator('[data-testid="process-cached-button"]').isVisible()) {
        await page.click('[data-testid="process-cached-button"]');
        await expect(page.locator('[data-testid="ai-processing-complete"]')).toBeVisible();
      }
    });
  });

  test.describe('Voice Analytics Dashboard', () => {
    test('should display accuracy metrics', async () => {
      await page.goto('/voice-analytics');
      
      // Verify analytics components
      await expect(page.locator('[data-testid="accuracy-chart"]')).toBeVisible();
      await expect(page.locator('[data-testid="recognition-success-rate"]')).toBeVisible();
      await expect(page.locator('[data-testid="average-confidence-score"]')).toBeVisible();
      
      // Verify metrics display actual values
      const successRate = await page.locator('[data-testid="recognition-success-rate"] .metric-value').textContent();
      expect(successRate).toMatch(/^\d+(\.\d+)?%$/);
    });

    test('should show processing time metrics', async () => {
      await page.goto('/voice-analytics');
      
      await expect(page.locator('[data-testid="processing-time-chart"]')).toBeVisible();
      await expect(page.locator('[data-testid="average-processing-time"]')).toBeVisible();
      
      // Verify time-based filtering
      await page.selectOption('[data-testid="time-range-filter"]', '7d');
      await page.waitForResponse(/.*analytics.*/);
      
      // Verify chart updates
      await expect(page.locator('[data-testid="processing-time-chart"] .recharts-line')).toBeVisible();
    });

    test('should track common voice commands', async () => {
      await page.goto('/voice-analytics');
      
      await expect(page.locator('[data-testid="common-commands-list"]')).toBeVisible();
      
      // Verify top commands are listed
      const commandItems = page.locator('[data-testid="command-item"]');
      const count = await commandItems.count();
      expect(count).toBeGreaterThan(0);
      
      // Verify command frequency data
      if (count > 0) {
        await expect(commandItems.first().locator('[data-testid="command-frequency"]')).toBeVisible();
      }
    });
  });

  test.describe('Performance and Optimization', () => {
    test('should handle rapid voice commands without lag', async () => {
      await page.click('[data-testid="nav-inventory"]');
      
      const commands = [
        'Add ten pounds of salmon',
        'Add five pounds of cod', 
        'Add twenty pounds of halibut'
      ];

      const startTime = Date.now();
      
      for (const command of commands) {
        await page.click('[data-testid="voice-input-button"]');
        
        await page.evaluate((transcript) => {
          const recognition = (window as any).mockSpeechRecognition;
          if (recognition) {
            recognition.simulateResult(transcript, true);
          }
        }, command);
        
        await page.waitForSelector('[data-testid="ai-processing-complete"]');
        await page.click('[data-testid="voice-input-clear"]');
      }
      
      const totalTime = Date.now() - startTime;
      
      // Should process all commands within reasonable time
      expect(totalTime).toBeLessThan(10000); // 10 seconds for 3 commands
    });

    test('should clean up resources after voice session', async () => {
      await page.click('[data-testid="nav-inventory"]');
      await page.click('[data-testid="voice-input-button"]');
      
      // Navigate away from page
      await page.click('[data-testid="nav-dashboard"]');
      
      // Verify voice session is properly cleaned up
      const memoryUsage = await page.evaluate(() => {
        return (performance as any).memory?.usedJSHeapSize || 0;
      });
      
      // Memory usage should be reasonable (this is a basic check)
      expect(memoryUsage).toBeLessThan(50 * 1024 * 1024); // 50MB threshold
    });
  });
});