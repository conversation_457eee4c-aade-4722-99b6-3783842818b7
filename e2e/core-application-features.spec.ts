import { test, expect, Page } from '@playwright/test';

/**
 * Core Application Features Test Suite
 * Tests dashboard, navigation, forms, data tables, and modal functionality
 */

test.describe('Core Application Features', () => {
  let page: Page;

  test.beforeEach(async ({ page: p }) => {
    page = p;
    
    // Mock authentication for testing
    await page.route('**/auth/**', (route) => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: { id: 'test-user', email: '<EMAIL>' },
          session: { access_token: 'mock-token' }
        })
      });
    });

    // Mock Supabase API calls
    await page.route('**/rest/v1/**', (route) => {
      const url = route.request().url();
      
      if (url.includes('inventory_events')) {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify([
            {
              id: '1',
              event_type: 'receiving',
              product_name: 'Atlantic Salmon',
              quantity: 100,
              unit: 'lbs',
              created_at: new Date().toISOString(),
              batch_number: 'BATCH001'
            }
          ])
        });
      } else if (url.includes('Products')) {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify([
            {
              id: '1',
              name: 'Atlantic Salmon',
              category: 'Fresh Fish',
              unit: 'lbs',
              current_stock: 150
            }
          ])
        });
      } else {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify([])
        });
      }
    });

    await page.goto('/');
  });

  test.describe('Dashboard Functionality', () => {
    test('should display real-time data widgets', async () => {
      // Wait for dashboard to load
      await page.waitForSelector('[data-testid="dashboard"]', { timeout: 10000 });
      
      // Check KPI widgets are present
      await expect(page.locator('[data-testid="total-inventory"]')).toBeVisible();
      await expect(page.locator('[data-testid="low-stock-alerts"]')).toBeVisible();
      await expect(page.locator('[data-testid="recent-events"]')).toBeVisible();
      
      // Verify charts are rendered
      const chartElements = page.locator('.recharts-wrapper');
      await expect(chartElements.first()).toBeVisible();
    });

    test('should update KPIs in real-time', async () => {
      await page.waitForSelector('[data-testid="dashboard"]');
      
      // Get initial inventory count
      const initialCount = await page.locator('[data-testid="total-inventory"] .metric-value').textContent();
      
      // Simulate real-time update by navigating and adding inventory
      await page.click('[data-testid="nav-inventory"]');
      await page.click('[data-testid="add-inventory-button"]');
      
      // Fill form and submit
      await page.fill('[data-testid="product-select"]', 'Atlantic Salmon');
      await page.fill('[data-testid="quantity-input"]', '25');
      await page.click('[data-testid="submit-inventory"]');
      
      // Navigate back to dashboard
      await page.click('[data-testid="nav-dashboard"]');
      
      // Verify KPI updated
      await page.waitForFunction(
        (initial) => {
          const current = document.querySelector('[data-testid="total-inventory"] .metric-value')?.textContent;
          return current !== initial;
        },
        initialCount,
        { timeout: 5000 }
      );
    });

    test('should handle chart interactions', async () => {
      await page.waitForSelector('[data-testid="dashboard"]');
      
      // Test chart hover interactions
      const chartArea = page.locator('.recharts-surface').first();
      await chartArea.hover();
      
      // Verify tooltip appears
      await expect(page.locator('.recharts-tooltip-wrapper')).toBeVisible();
      
      // Test chart filter controls
      if (await page.locator('[data-testid="chart-filter"]').isVisible()) {
        await page.selectOption('[data-testid="chart-filter"]', '7d');
        await page.waitForResponse(/.*inventory_events.*/);
      }
    });
  });

  test.describe('Navigation and Routing', () => {
    test('should navigate between all main routes', async () => {
      const routes = [
        { testId: 'nav-dashboard', path: '/', title: 'Dashboard' },
        { testId: 'nav-inventory', path: '/inventory', title: 'Inventory' },
        { testId: 'nav-products', path: '/products', title: 'Products' },
        { testId: 'nav-analytics', path: '/analytics', title: 'Analytics' },
        { testId: 'nav-haccp', path: '/haccp', title: 'HACCP' },
        { testId: 'nav-settings', path: '/settings', title: 'Settings' }
      ];

      for (const route of routes) {
        await page.click(`[data-testid="${route.testId}"]`);
        await expect(page).toHaveURL(new RegExp(route.path.replace('/', '')));
        
        // Verify page title or heading
        const heading = page.locator('h1, h2, [data-testid="page-title"]').first();
        if (await heading.isVisible()) {
          await expect(heading).toContainText(route.title);
        }
      }
    });

    test('should maintain state during navigation', async () => {
      // Navigate to inventory and set a filter
      await page.click('[data-testid="nav-inventory"]');
      await page.waitForSelector('[data-testid="inventory-table"]');
      
      if (await page.locator('[data-testid="product-filter"]').isVisible()) {
        await page.selectOption('[data-testid="product-filter"]', 'Fresh Fish');
      }
      
      // Navigate away and back
      await page.click('[data-testid="nav-dashboard"]');
      await page.click('[data-testid="nav-inventory"]');
      
      // Verify filter state is maintained (if implemented)
      const filterValue = await page.locator('[data-testid="product-filter"]').inputValue();
      expect(filterValue).toBeTruthy();
    });

    test('should handle browser back/forward navigation', async () => {
      // Navigate through pages
      await page.click('[data-testid="nav-inventory"]');
      await page.click('[data-testid="nav-products"]');
      
      // Use browser back
      await page.goBack();
      await expect(page).toHaveURL(/inventory/);
      
      // Use browser forward
      await page.goForward();
      await expect(page).toHaveURL(/products/);
    });
  });

  test.describe('Forms and Validation', () => {
    test('should validate required fields', async () => {
      await page.click('[data-testid="nav-inventory"]');
      await page.click('[data-testid="add-inventory-button"]');
      
      // Try to submit empty form
      await page.click('[data-testid="submit-inventory"]');
      
      // Check for validation errors
      await expect(page.locator('[data-testid="product-error"]')).toBeVisible();
      await expect(page.locator('[data-testid="quantity-error"]')).toBeVisible();
    });

    test('should show real-time validation feedback', async () => {
      await page.click('[data-testid="nav-inventory"]');
      await page.click('[data-testid="add-inventory-button"]');
      
      // Enter invalid quantity
      await page.fill('[data-testid="quantity-input"]', '-5');
      await page.blur('[data-testid="quantity-input"]');
      
      // Check for immediate validation feedback
      await expect(page.locator('[data-testid="quantity-error"]')).toContainText('must be positive');
      
      // Fix the error
      await page.fill('[data-testid="quantity-input"]', '25');
      await page.blur('[data-testid="quantity-input"]');
      
      // Verify error clears
      await expect(page.locator('[data-testid="quantity-error"]')).not.toBeVisible();
    });

    test('should handle form submission successfully', async () => {
      await page.click('[data-testid="nav-inventory"]');
      await page.click('[data-testid="add-inventory-button"]');
      
      // Fill valid form data
      await page.selectOption('[data-testid="product-select"]', 'Atlantic Salmon');
      await page.fill('[data-testid="quantity-input"]', '50');
      await page.selectOption('[data-testid="event-type-select"]', 'receiving');
      
      // Submit form
      await page.click('[data-testid="submit-inventory"]');
      
      // Verify success feedback
      await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
      
      // Verify form closes/resets
      await expect(page.locator('[data-testid="inventory-form"]')).not.toBeVisible();
    });
  });

  test.describe('Data Tables and Grids', () => {
    test('should display data with sorting functionality', async () => {
      await page.click('[data-testid="nav-inventory"]');
      await page.waitForSelector('[data-testid="inventory-table"]');
      
      // Test column sorting
      await page.click('[data-testid="sort-date"]');
      await page.waitForResponse(/.*inventory_events.*/);
      
      // Verify sort indicator
      await expect(page.locator('[data-testid="sort-date"] .sort-icon')).toBeVisible();
      
      // Test reverse sort
      await page.click('[data-testid="sort-date"]');
      await page.waitForResponse(/.*inventory_events.*/);
    });

    test('should filter data correctly', async () => {
      await page.click('[data-testid="nav-inventory"]');
      await page.waitForSelector('[data-testid="inventory-table"]');
      
      // Apply product filter
      if (await page.locator('[data-testid="product-filter"]').isVisible()) {
        await page.selectOption('[data-testid="product-filter"]', 'Atlantic Salmon');
        await page.waitForResponse(/.*inventory_events.*/);
        
        // Verify filtered results
        const rows = page.locator('[data-testid="inventory-row"]');
        const count = await rows.count();
        expect(count).toBeGreaterThan(0);
        
        // Verify all visible rows match filter
        for (let i = 0; i < Math.min(count, 5); i++) {
          await expect(rows.nth(i)).toContainText('Atlantic Salmon');
        }
      }
    });

    test('should paginate large datasets', async () => {
      await page.click('[data-testid="nav-inventory"]');
      await page.waitForSelector('[data-testid="inventory-table"]');
      
      // Check if pagination controls exist
      if (await page.locator('[data-testid="pagination"]').isVisible()) {
        const nextButton = page.locator('[data-testid="pagination-next"]');
        
        if (await nextButton.isEnabled()) {
          await nextButton.click();
          await page.waitForResponse(/.*inventory_events.*/);
          
          // Verify page changed
          await expect(page.locator('[data-testid="current-page"]')).toContainText('2');
        }
      }
    });
  });

  test.describe('Modal Dialogs and Overlays', () => {
    test('should open and close modals correctly', async () => {
      await page.click('[data-testid="nav-inventory"]');
      await page.click('[data-testid="add-inventory-button"]');
      
      // Verify modal opens
      await expect(page.locator('[data-testid="inventory-modal"]')).toBeVisible();
      
      // Test close button
      await page.click('[data-testid="modal-close"]');
      await expect(page.locator('[data-testid="inventory-modal"]')).not.toBeVisible();
      
      // Test ESC key
      await page.click('[data-testid="add-inventory-button"]');
      await page.keyboard.press('Escape');
      await expect(page.locator('[data-testid="inventory-modal"]')).not.toBeVisible();
    });

    test('should handle modal focus management', async () => {
      await page.click('[data-testid="nav-inventory"]');
      await page.click('[data-testid="add-inventory-button"]');
      
      // Verify focus is trapped in modal
      await page.keyboard.press('Tab');
      const focusedElement = await page.evaluate(() => document.activeElement?.getAttribute('data-testid'));
      expect(focusedElement).toBeTruthy();
      
      // Verify focus returns to trigger on close
      await page.keyboard.press('Escape');
      const finalFocus = await page.evaluate(() => document.activeElement?.getAttribute('data-testid'));
      expect(finalFocus).toBe('add-inventory-button');
    });

    test('should handle multiple modal layers', async () => {
      await page.click('[data-testid="nav-inventory"]');
      await page.click('[data-testid="add-inventory-button"]');
      
      // Open confirmation dialog if it exists
      if (await page.locator('[data-testid="open-confirmation"]').isVisible()) {
        await page.click('[data-testid="open-confirmation"]');
        
        // Verify both modals are visible with correct z-index
        await expect(page.locator('[data-testid="inventory-modal"]')).toBeVisible();
        await expect(page.locator('[data-testid="confirmation-dialog"]')).toBeVisible();
        
        // Close confirmation, verify main modal still visible
        await page.click('[data-testid="confirmation-cancel"]');
        await expect(page.locator('[data-testid="confirmation-dialog"]')).not.toBeVisible();
        await expect(page.locator('[data-testid="inventory-modal"]')).toBeVisible();
      }
    });
  });

  test.describe('Error Handling and Recovery', () => {
    test('should handle API errors gracefully', async () => {
      // Mock API error
      await page.route('**/rest/v1/inventory_events', (route) => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Internal server error' })
        });
      });

      await page.click('[data-testid="nav-inventory"]');
      
      // Verify error message is displayed
      await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
      await expect(page.locator('[data-testid="error-message"]')).toContainText('Failed to load');
      
      // Test retry functionality
      if (await page.locator('[data-testid="retry-button"]').isVisible()) {
        await page.click('[data-testid="retry-button"]');
        // Verify retry attempt is made
        await page.waitForResponse(/.*inventory_events.*/);
      }
    });

    test('should handle network disconnection', async () => {
      // Simulate offline
      await page.context().setOffline(true);
      
      await page.click('[data-testid="nav-products"]');
      
      // Verify offline indicator or error message
      const offlineIndicator = page.locator('[data-testid="offline-indicator"]');
      const errorMessage = page.locator('[data-testid="error-message"]');
      
      const isOfflineIndicatorVisible = await offlineIndicator.isVisible();
      const isErrorMessageVisible = await errorMessage.isVisible();
      
      expect(isOfflineIndicatorVisible || isErrorMessageVisible).toBe(true);
      
      // Restore connection
      await page.context().setOffline(false);
      
      // Verify app recovers
      if (await page.locator('[data-testid="retry-button"]').isVisible()) {
        await page.click('[data-testid="retry-button"]');
      }
      
      await page.waitForSelector('[data-testid="products-table"]', { timeout: 10000 });
    });
  });

  test.describe('Performance and Responsiveness', () => {
    test('should load pages within performance budget', async () => {
      const startTime = Date.now();
      await page.goto('/dashboard');
      await page.waitForLoadState('networkidle');
      const loadTime = Date.now() - startTime;
      
      // Should load within 3 seconds
      expect(loadTime).toBeLessThan(3000);
    });

    test('should handle large datasets without blocking UI', async () => {
      // Mock large dataset
      await page.route('**/rest/v1/inventory_events', (route) => {
        const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
          id: i.toString(),
          event_type: 'receiving',
          product_name: `Product ${i}`,
          quantity: Math.floor(Math.random() * 100),
          unit: 'lbs',
          created_at: new Date().toISOString()
        }));
        
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify(largeDataset)
        });
      });

      await page.click('[data-testid="nav-inventory"]');
      
      // Verify UI remains responsive during data loading
      const loadingStart = Date.now();
      await page.waitForSelector('[data-testid="inventory-table"]');
      
      // Test that UI interactions are still possible during loading
      await page.click('[data-testid="nav-dashboard"]');
      const interactionTime = Date.now() - loadingStart;
      
      // UI should remain responsive (interaction should complete quickly)
      expect(interactionTime).toBeLessThan(1000);
    });
  });
});