import { test, expect } from '@playwright/test';

test.describe('Critical Inventory Workflow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to application
    await page.goto('/');
    
    // Mock authentication for E2E tests
    await page.evaluate(() => {
      localStorage.setItem('supabase.auth.token', JSON.stringify({
        access_token: 'mock-token',
        refresh_token: 'mock-refresh',
        expires_at: Date.now() + 3600000,
        user: {
          id: 'test-user-id',
          email: '<EMAIL>'
        }
      }));
    });

    await page.reload();
  });

  test('complete receiving workflow', async ({ page }) => {
    // Navigate to inventory section
    await page.click('[aria-label="Main navigation"] button:has-text("Inventory")');
    
    // Verify we're on the inventory page
    await expect(page.locator('h1')).toContainText('Inventory');

    // Start adding new inventory item
    await page.click('button:has-text("Add Product")');

    // Fill out product form
    await page.fill('input[name="name"]', 'Test Atlantic Salmon');
    await page.selectOption('select[name="category"]', 'Fresh Fish');
    await page.fill('input[name="quantity"]', '100');
    await page.selectOption('select[name="unit"]', 'lbs');
    await page.fill('input[name="price"]', '18.50');

    // Submit form
    await page.click('button[type="submit"]');

    // Verify product was added
    await expect(page.locator('text=Test Atlantic Salmon')).toBeVisible();
    await expect(page.locator('text=100 lbs')).toBeVisible();
  });

  test('HACCP event recording workflow', async ({ page }) => {
    // Navigate to HACCP section
    await page.click('[aria-label="Main navigation"] button:has-text("HACCP")');
    
    // Open calendar view
    await page.click('button:has-text("Calendar")');

    // Click on today's date to add event
    const today = new Date();
    const todaySelector = `button:has-text("${today.getDate()}")`;
    await page.click(todaySelector);

    // Verify modal opened
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    await expect(page.locator('text=Add Event')).toBeVisible();

    // Fill out HACCP event form
    await page.selectOption('select[name="eventType"]', 'receiving');
    await page.selectOption('select[name="productId"]', { index: 1 }); // Select first available product
    await page.fill('input[name="quantity"]', '50');
    await page.selectOption('select[name="vendorId"]', { index: 1 }); // Select first vendor
    
    // Submit event
    await page.click('button:has-text("Save Event")');

    // Verify event was recorded
    await expect(page.locator('text=receiving')).toBeVisible();
  });

  test('voice input workflow', async ({ page }) => {
    // Mock speech recognition API
    await page.addInitScript(() => {
      // Mock SpeechRecognition
      window.SpeechRecognition = class MockSpeechRecognition {
        continuous = false;
        interimResults = false;
        lang = 'en-US';
        
        start() {
          // Simulate speech recognition result
          setTimeout(() => {
            const event = {
              results: [
                [{
                  transcript: 'Add fifty pounds of Atlantic salmon from Ocean Fresh',
                  confidence: 0.9,
                  isFinal: true
                }]
              ],
              resultIndex: 0
            };
            if (this.onresult) this.onresult(event);
          }, 1000);
        }
        
        stop() {}
        abort() {}
        addEventListener() {}
        removeEventListener() {}
      };
      
      window.webkitSpeechRecognition = window.SpeechRecognition;
    });

    // Navigate to inventory section
    await page.click('[aria-label="Main navigation"] button:has-text("Inventory")');

    // Find and click voice input button
    await page.click('button:has-text("Voice Input")');

    // Select category for voice input
    await page.selectOption('select', 'Fresh Fish');

    // Start voice recording
    await page.click('button:has-text("Start Recording")');

    // Wait for mock transcription to complete
    await page.waitForTimeout(2000);

    // Verify transcript appeared
    await expect(page.locator('text=Add fifty pounds of Atlantic salmon')).toBeVisible();

    // Stop recording
    await page.click('button:has-text("Stop Recording")');

    // Verify AI processing results
    await expect(page.locator('text=Atlantic salmon')).toBeVisible();
  });

  test('import workflow', async ({ page }) => {
    // Navigate to import section
    await page.click('[aria-label="Main navigation"] button:has-text("Import")');

    // Verify import wizard loads
    await expect(page.locator('text=Import Wizard')).toBeVisible();

    // Select import type
    await page.click('button:has-text("Products")');
    await page.click('button:has-text("Next")');

    // Upload CSV file
    const csvContent = `Product Name,Category,Quantity,Unit,Price
Atlantic Salmon,Fresh Fish,100,lbs,18.50
Dungeness Crab,Shellfish,50,lbs,25.00`;

    await page.setInputFiles('input[type="file"]', {
      name: 'test-products.csv',
      mimeType: 'text/csv',
      buffer: Buffer.from(csvContent)
    });

    // Wait for file processing
    await expect(page.locator('text=Detected Columns')).toBeVisible();
    await page.click('button:has-text("Next")');

    // Map fields
    await expect(page.locator('text=Map Fields')).toBeVisible();
    
    // Map product name field
    await page.selectOption('select:near(label:has-text("Name"))', 'Product Name');
    await page.selectOption('select:near(label:has-text("Category"))', 'Category');

    // Proceed to validation
    await page.click('button:has-text("Next")');

    // Verify validation passed
    await expect(page.locator('text=Mapping looks good')).toBeVisible();

    // Upload to database
    await page.click('button:has-text("Upload to Database")');

    // Verify success message
    await expect(page.locator('text=Import completed')).toBeVisible();
  });

  test('accessibility compliance', async ({ page }) => {
    // Check for proper heading structure
    const headings = await page.locator('h1, h2, h3, h4, h5, h6').all();
    expect(headings.length).toBeGreaterThan(0);

    // Check for alt text on images
    const images = await page.locator('img').all();
    for (const img of images) {
      const alt = await img.getAttribute('alt');
      expect(alt).toBeTruthy();
    }

    // Check for form labels
    const inputs = await page.locator('input, select, textarea').all();
    for (const input of inputs) {
      const id = await input.getAttribute('id');
      if (id) {
        const label = page.locator(`label[for="${id}"]`);
        await expect(label).toBeVisible();
      }
    }

    // Check for ARIA labels on buttons without text
    const buttons = await page.locator('button').all();
    for (const button of buttons) {
      const textContent = await button.textContent();
      if (!textContent?.trim()) {
        const ariaLabel = await button.getAttribute('aria-label');
        expect(ariaLabel).toBeTruthy();
      }
    }

    // Check color contrast (basic check)
    await page.addStyleTag({
      content: `
        * {
          background-color: white !important;
          color: black !important;
        }
      `
    });

    // Verify page is still functional with high contrast
    await expect(page.locator('body')).toBeVisible();
  });

  test('performance benchmarks', async ({ page }) => {
    // Measure page load time
    const startTime = Date.now();
    await page.goto('/');
    const loadTime = Date.now() - startTime;
    
    // Page should load within 3 seconds
    expect(loadTime).toBeLessThan(3000);

    // Test large data rendering performance
    await page.click('[aria-label="Main navigation"] button:has-text("Inventory")');
    
    // Measure time to render inventory list
    const renderStartTime = Date.now();
    await page.waitForSelector('[data-testid="inventory-list"], .inventory-grid', { timeout: 5000 });
    const renderTime = Date.now() - renderStartTime;
    
    // Inventory should render within 2 seconds
    expect(renderTime).toBeLessThan(2000);

    // Test search performance
    const searchInput = page.locator('input[placeholder*="Search"], input[type="search"]').first();
    if (await searchInput.isVisible()) {
      const searchStartTime = Date.now();
      await searchInput.fill('salmon');
      await page.waitForTimeout(500); // Wait for search debounce
      const searchTime = Date.now() - searchStartTime;
      
      // Search should respond within 1 second
      expect(searchTime).toBeLessThan(1000);
    }
  });

  test('error handling and recovery', async ({ page }) => {
    // Test network failure handling
    await page.route('**/api/**', route => route.abort());
    
    // Try to perform an action that requires API
    await page.click('[aria-label="Main navigation"] button:has-text("Inventory")');
    
    // Should show error message
    await expect(page.locator('text=Error, text=Failed, text=Unable')).toBeVisible({ timeout: 10000 });

    // Test recovery when network is restored
    await page.unroute('**/api/**');
    
    // Retry the action
    await page.reload();
    await page.click('[aria-label="Main navigation"] button:has-text("Inventory")');
    
    // Should work normally now
    await expect(page.locator('h1, h2')).toContainText('Inventory');
  });

  test('mobile responsiveness', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });

    // Check mobile navigation
    const mobileMenuButton = page.locator('button[aria-label*="menu"], button[aria-label*="Menu"], .mobile-menu-button');
    if (await mobileMenuButton.isVisible()) {
      await mobileMenuButton.click();
      await expect(page.locator('nav, .navigation-menu')).toBeVisible();
    }

    // Test form usability on mobile
    await page.click('button:has-text("Add Product")');
    
    // Forms should be usable on mobile
    const formInputs = page.locator('input, select, textarea');
    const firstInput = formInputs.first();
    
    if (await firstInput.isVisible()) {
      await firstInput.focus();
      await expect(firstInput).toBeFocused();
      
      // Input should not be cut off
      const inputBox = await firstInput.boundingBox();
      expect(inputBox?.width).toBeGreaterThan(0);
      expect(inputBox?.x).toBeGreaterThanOrEqual(0);
    }

    // Test touch targets are large enough (minimum 44px)
    const touchTargets = page.locator('button, a, input[type="checkbox"], input[type="radio"]');
    const targets = await touchTargets.all();
    
    for (const target of targets.slice(0, 5)) { // Test first 5 to avoid timeout
      if (await target.isVisible()) {
        const box = await target.boundingBox();
        if (box) {
          expect(Math.min(box.width, box.height)).toBeGreaterThanOrEqual(40); // Allow 4px tolerance
        }
      }
    }
  });
});