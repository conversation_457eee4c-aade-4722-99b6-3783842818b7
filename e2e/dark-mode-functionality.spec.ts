/**
 * Dark Mode Functionality E2E Tests
 * 
 * Comprehensive test suite for the dark mode implementation in Seafood Manager.
 * Tests theme context, toggle behavior, system detection, persistence, and styling.
 */

import { test, expect, type Page, type BrowserContext } from '@playwright/test';

test.describe('Dark Mode Functionality', () => {
  let page: Page;

  test.beforeEach(async ({ page: testPage, context }) => {
    page = testPage;
    
    // Navigate to the application
    await page.goto('/');
    
    // Wait for the app to load and ThemeContext to initialize
    await page.waitForSelector('[data-testid="sidebar"]', { timeout: 10000 });
  });

  test.describe('Theme Context Provider Initialization', () => {
    test('should initialize with system theme by default', async () => {
      // Check that the html element has a theme class applied
      const htmlElement = page.locator('html');
      const themeClass = await htmlElement.getAttribute('class');
      
      expect(themeClass).toMatch(/(?:light|dark)/);
      
      // Verify CSS custom properties are set
      const chartBackground = await page.evaluate(() => 
        getComputedStyle(document.documentElement).getPropertyValue('--chart-background')
      );
      expect(chartBackground).toBeTruthy();
    });

    test('should provide theme state through context', async () => {
      // Check that theme-aware components render correctly
      const sidebar = page.locator('[data-testid="sidebar"]');
      await expect(sidebar).toBeVisible();
      
      // Verify theme button is present with correct initial state
      const themeButton = page.locator('button', { hasText: /Theme:/ });
      await expect(themeButton).toBeVisible();
      
      const buttonText = await themeButton.textContent();
      expect(buttonText).toMatch(/Theme: (?:light|dark|system)/);
    });

    test('should handle missing localStorage gracefully', async () => {
      // Clear localStorage and reload
      await page.evaluate(() => localStorage.clear());
      await page.reload();
      
      // Should still initialize properly
      await page.waitForSelector('[data-testid="sidebar"]');
      const themeButton = page.locator('button', { hasText: /Theme:/ });
      await expect(themeButton).toBeVisible();
    });
  });

  test.describe('Theme Toggle Button Behavior', () => {
    test('should cycle through light -> dark -> system -> light', async () => {
      const themeButton = page.locator('button', { hasText: /Theme:/ });
      
      // Set initial state to light
      await page.evaluate(() => {
        localStorage.setItem('seafood-manager-theme', 'light');
      });
      await page.reload();
      await page.waitForSelector('[data-testid="sidebar"]');
      
      // Verify initial state is light
      await expect(themeButton).toContainText('Theme: light');
      await expect(page.locator('html')).toHaveClass(/light/);
      
      // Click to switch to dark
      await themeButton.click();
      await expect(themeButton).toContainText('Theme: dark');
      await expect(page.locator('html')).toHaveClass(/dark/);
      
      // Click to switch to system
      await themeButton.click();
      await expect(themeButton).toContainText('Theme: system');
      
      // Click to cycle back to light
      await themeButton.click();
      await expect(themeButton).toContainText('Theme: light');
      await expect(page.locator('html')).toHaveClass(/light/);
    });

    test('should update theme icons correctly', async () => {
      const themeButton = page.locator('button', { hasText: /Theme:/ });
      
      // Set to light theme
      await page.evaluate(() => {
        localStorage.setItem('seafood-manager-theme', 'light');
      });
      await page.reload();
      await page.waitForSelector('[data-testid="sidebar"]');
      
      // Check Sun icon for light theme
      const sunIcon = themeButton.locator('svg').first();
      await expect(sunIcon).toBeVisible();
      
      // Switch to dark
      await themeButton.click();
      await page.waitForTimeout(100); // Brief wait for icon update
      
      // Check Moon icon for dark theme
      const moonIcon = themeButton.locator('svg').first();
      await expect(moonIcon).toBeVisible();
      
      // Switch to system
      await themeButton.click();
      await page.waitForTimeout(100);
      
      // Check Monitor icon for system theme
      const monitorIcon = themeButton.locator('svg').first();
      await expect(monitorIcon).toBeVisible();
    });

    test('should maintain theme button functionality when sidebar is collapsed', async () => {
      // Collapse the sidebar
      const collapseButton = page.locator('button[aria-label*="Collapse sidebar"], button[aria-label*="Expand sidebar"]');
      await collapseButton.click();
      
      // Theme button should still be functional
      const themeButton = page.locator('button', { hasText: /Theme:/ }).or(
        page.locator('button[title*="Current theme"]')
      );
      
      await expect(themeButton).toBeVisible();
      
      // Should still cycle themes
      await themeButton.click();
      await page.waitForTimeout(100);
      
      const htmlClass = await page.locator('html').getAttribute('class');
      expect(htmlClass).toMatch(/(?:light|dark)/);
    });
  });

  test.describe('System Theme Detection', () => {
    test('should respect system dark mode preference', async () => {
      // Set system to prefer dark mode
      await page.emulateMedia({ colorScheme: 'dark' });
      
      // Set theme to system
      await page.evaluate(() => {
        localStorage.setItem('seafood-manager-theme', 'system');
      });
      await page.reload();
      await page.waitForSelector('[data-testid="sidebar"]');
      
      // Should apply dark theme
      await expect(page.locator('html')).toHaveClass(/dark/);
      
      const themeButton = page.locator('button', { hasText: /Theme:/ });
      await expect(themeButton).toContainText('Theme: system');
    });

    test('should respect system light mode preference', async () => {
      // Set system to prefer light mode
      await page.emulateMedia({ colorScheme: 'light' });
      
      // Set theme to system
      await page.evaluate(() => {
        localStorage.setItem('seafood-manager-theme', 'system');
      });
      await page.reload();
      await page.waitForSelector('[data-testid="sidebar"]');
      
      // Should apply light theme
      await expect(page.locator('html')).toHaveClass(/light/);
      
      const themeButton = page.locator('button', { hasText: /Theme:/ });
      await expect(themeButton).toContainText('Theme: system');
    });

    test('should update when system theme changes', async () => {
      // Set theme to system
      await page.evaluate(() => {
        localStorage.setItem('seafood-manager-theme', 'system');
      });
      await page.reload();
      await page.waitForSelector('[data-testid="sidebar"]');
      
      // Start with light system preference
      await page.emulateMedia({ colorScheme: 'light' });
      await page.waitForTimeout(100);
      await expect(page.locator('html')).toHaveClass(/light/);
      
      // Change to dark system preference
      await page.emulateMedia({ colorScheme: 'dark' });
      await page.waitForTimeout(100);
      await expect(page.locator('html')).toHaveClass(/dark/);
    });
  });

  test.describe('Theme Persistence with localStorage', () => {
    test('should save theme preference to localStorage', async () => {
      const themeButton = page.locator('button', { hasText: /Theme:/ });
      
      // Set to dark theme
      await themeButton.click();
      await page.waitForTimeout(100);
      
      // Check localStorage
      const savedTheme = await page.evaluate(() => 
        localStorage.getItem('seafood-manager-theme')
      );
      expect(['light', 'dark', 'system']).toContain(savedTheme);
    });

    test('should restore theme preference on reload', async () => {
      // Set dark theme manually
      await page.evaluate(() => {
        localStorage.setItem('seafood-manager-theme', 'dark');
      });
      
      // Reload page
      await page.reload();
      await page.waitForSelector('[data-testid="sidebar"]');
      
      // Should restore dark theme
      await expect(page.locator('html')).toHaveClass(/dark/);
      
      const themeButton = page.locator('button', { hasText: /Theme:/ });
      await expect(themeButton).toContainText('Theme: dark');
    });

    test('should handle corrupted localStorage data', async () => {
      // Set invalid theme value
      await page.evaluate(() => {
        localStorage.setItem('seafood-manager-theme', 'invalid-theme');
      });
      
      // Reload page
      await page.reload();
      await page.waitForSelector('[data-testid="sidebar"]');
      
      // Should fallback to default (system)
      const themeButton = page.locator('button', { hasText: /Theme:/ });
      await expect(themeButton).toContainText('Theme: system');
      
      // HTML should have a valid theme class
      const htmlClass = await page.locator('html').getAttribute('class');
      expect(htmlClass).toMatch(/(?:light|dark)/);
    });

    test('should persist theme across navigation', async () => {
      const themeButton = page.locator('button', { hasText: /Theme:/ });
      
      // Set to dark theme
      await themeButton.click();
      await page.waitForTimeout(100);
      await expect(page.locator('html')).toHaveClass(/dark/);
      
      // Navigate to different views
      const inventoryButton = page.locator('button', { hasText: 'Inventory' });
      await inventoryButton.click();
      await page.waitForTimeout(100);
      
      // Theme should persist
      await expect(page.locator('html')).toHaveClass(/dark/);
      
      // Navigate to another view
      const dashboardButton = page.locator('button', { hasText: 'Dashboard' });
      await dashboardButton.click();
      await page.waitForTimeout(100);
      
      // Theme should still persist
      await expect(page.locator('html')).toHaveClass(/dark/);
    });
  });

  test.describe('CSS Custom Properties Updates', () => {
    test('should update chart CSS custom properties for dark theme', async () => {
      // Set to dark theme
      const themeButton = page.locator('button', { hasText: /Theme:/ });
      await themeButton.click(); // Assuming this cycles to dark
      await page.waitForTimeout(100);
      
      if (await page.locator('html').getAttribute('class') !== 'dark') {
        await themeButton.click(); // Click again if not dark
        await page.waitForTimeout(100);
      }
      
      // Check dark theme CSS custom properties
      const cssProperties = await page.evaluate(() => {
        const root = document.documentElement;
        return {
          chartBackground: getComputedStyle(root).getPropertyValue('--chart-background').trim(),
          chartText: getComputedStyle(root).getPropertyValue('--chart-text').trim(),
          chartGrid: getComputedStyle(root).getPropertyValue('--chart-grid').trim(),
          chartBorder: getComputedStyle(root).getPropertyValue('--chart-border').trim(),
        };
      });
      
      expect(cssProperties.chartBackground).toBe('#1f2937');
      expect(cssProperties.chartText).toBe('#f3f4f6');
      expect(cssProperties.chartGrid).toBe('#374151');
      expect(cssProperties.chartBorder).toBe('#4b5563');
    });

    test('should update chart CSS custom properties for light theme', async () => {
      // Ensure light theme is active
      await page.evaluate(() => {
        localStorage.setItem('seafood-manager-theme', 'light');
      });
      await page.reload();
      await page.waitForSelector('[data-testid="sidebar"]');
      
      // Check light theme CSS custom properties
      const cssProperties = await page.evaluate(() => {
        const root = document.documentElement;
        return {
          chartBackground: getComputedStyle(root).getPropertyValue('--chart-background').trim(),
          chartText: getComputedStyle(root).getPropertyValue('--chart-text').trim(),
          chartGrid: getComputedStyle(root).getPropertyValue('--chart-grid').trim(),
          chartBorder: getComputedStyle(root).getPropertyValue('--chart-border').trim(),
        };
      });
      
      expect(cssProperties.chartBackground).toBe('#ffffff');
      expect(cssProperties.chartText).toBe('#374151');
      expect(cssProperties.chartGrid).toBe('#e5e7eb');
      expect(cssProperties.chartBorder).toBe('#d1d5db');
    });
  });
});