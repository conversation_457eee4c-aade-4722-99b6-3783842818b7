{"name": "pacific-cloud-seafoods", "private": true, "version": "1.0.0", "type": "module", "engines": {"node": ">=18.0.0"}, "scripts": {"dev": "vite", "build": "vite build", "build:production": "vite build --config vite.config.production.ts", "build:analyze": "npm run build:production && node scripts/analyze-bundle-performance.js", "build:analyze:detailed": "npm run build:production && npx vite-bundle-analyzer dist", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write 'src/**/*.{ts,tsx,js,jsx}'", "format:check": "prettier --check 'src/**/*.{ts,tsx,js,jsx}'", "type-check": "tsc --noEmit --project tsconfig.app.json", "quality:check": "npm run type-check && npm run lint && npm run format:check", "quality:fix": "npm run format && npm run lint:fix", "preview": "vite preview", "watch": "vite", "seed:categories": "node scripts/seed-categories.mjs", "seed:products": "node scripts/seed-products.mjs", "db:migrate": "npx supabase db push", "db:migrate:all": "npx supabase db push --include-all", "db:reset": "npx supabase db reset", "test": "vitest", "test:watch": "vitest --watch", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:unit": "vitest --run src/components src/lib src/hooks", "test:integration": "vitest --run src/test/integration", "test:performance": "vitest --run src/test/performance", "test:voice": "vitest --run --testPathPattern=\"voice.*test\"", "test:voice:unit": "vitest --run src/test/unit/voice-processing-components.test.ts", "test:voice:integration": "vitest --run src/test/integration/voice-database-comprehensive.test.ts", "test:voice:performance": "vitest --run src/test/performance/voice-processing-performance.test.ts", "test:voice:all": "npm run test:voice:unit && npm run test:voice:integration && npm run test:voice:performance", "test:supabase": "vitest --run --testPathPattern=\".*supabase.*test\"", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:voice": "playwright test src/test/e2e/voice-system-comprehensive.spec.ts", "test:e2e:voice:browsers": "playwright test src/test/e2e/voice-system-comprehensive.spec.ts --project=chromium --project=firefox --project=webkit", "test:all": "npm run test && npm run test:integration && npm run test:e2e", "test:voice:complete": "npm run test:voice:all && npm run test:e2e:voice", "test:ci": "npm run test:coverage && npm run test:integration && npm run test:performance && npm run test:voice:complete", "test:ci:voice": "npm run test:voice:unit && npm run test:voice:integration && npm run test:voice:performance", "test:vendor": "./scripts/test-vendor-system.sh", "test:vendor:unit": "vitest --run --testPathPattern=\"vendor.*component.*test\"", "test:vendor:api": "vitest --run --testPathPattern=\"vendor-api.test\"", "test:vendor:integration": "vitest --run --testPathPattern=\"vendor.*integration.*test\"", "test:vendor:performance": "vitest --run --testPathPattern=\"vendor.*performance.*test\"", "test:vendor:accessibility": "vitest --run --testPathPattern=\"vendor.*accessibility.*test\"", "test:vendor:e2e": "playwright test vendor-performance-workflow.spec.ts", "type-check:root": "tsc --noEmit", "performance:check": "node scripts/check-performance-budgets.js", "performance:lighthouse": "lhci autorun", "performance:analyze": "npm run build:production && npm run performance:check", "docker:build": "docker build -f docker/Dockerfile.production -t seafood-manager:latest .", "docker:run": "docker run -p 8080:8080 seafood-manager:latest", "monitoring:setup": "node scripts/setup-monitoring.js", "deploy:staging": "vercel --target staging", "deploy:production": "vercel --prod", "postbuild": "npm run performance:check"}, "dependencies": {"@executeautomation/playwright-mcp-server": "^1.0.6", "@hookform/resolvers": "^3.3.4", "@playwright/mcp": "^0.0.35", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@square/web-sdk": "^2.0.1", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.39.7", "@types/jspdf": "^1.3.3", "@types/papaparse": "^5.3.15", "@types/react-dom": "^18.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "cssesc": "^3.0.0", "date-fns": "^3.6.0", "dotenv": "^16.4.7", "express": "^5.1.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.469.0", "node-fetch": "^3.3.2", "openai": "^4.28.0", "papaparse": "^5.4.1", "react": "^18.2.0", "react-day-picker": "^9.9.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.51.0", "react-router-dom": "^7.8.1", "react-speech-recognition": "^3.10.0", "recharts": "^2.12.2", "regenerator-runtime": "^0.14.1", "rollup": "^3.29.4", "tailwind-merge": "^2.6.0", "xlsx": "^0.18.5", "zod": "^3.22.4"}, "devDependencies": {"@babel/plugin-transform-runtime": "^7.24.0", "@babel/runtime": "^7.26.0", "@lhci/cli": "^0.12.0", "@playwright/experimental-ct-react": "^1.49.1", "@playwright/test": "^1.49.1", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.9", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/react-speech-recognition": "^3.9.5", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^2.1.8", "@vitest/ui": "^2.1.8", "autoprefixer": "^10.4.18", "babel-plugin-polyfill-regenerator": "^0.6.3", "bundlesize": "^0.18.1", "date-fns": "^4.1.0", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "glob": "^10.3.10", "globals": "^15.9.0", "happy-dom": "^15.11.6", "jsdom": "^25.0.1", "msw": "^2.6.4", "parcel": "^2.13.2", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "terser": "^5.43.1", "typescript": "^5.7.3", "typescript-eslint": "^8.3.0", "undici": "^6.21.0", "vite": "^5.0.12", "vite-bundle-analyzer": "^0.7.0", "vite-plugin-commonjs": "^0.10.4", "vite-tsconfig-paths": "^5.1.3", "vitest": "^2.1.8"}, "bundlesize": [{"path": "dist/assets/*.js", "maxSize": "1MB"}, {"path": "dist/assets/*.css", "maxSize": "50kB"}]}