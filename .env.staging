# Staging Environment Configuration
NODE_ENV=development
VITE_APP_ENV=staging

# Supabase Configuration (Staging)
# These will be set in Vercel environment variables
VITE_SUPABASE_URL=https://your-staging-project-ref.supabase.co
VITE_SUPABASE_ANON_KEY=your-staging-anon-key

# OpenAI Configuration (Staging)
# This will be set in Vercel environment variables
VITE_OPENAI_API_KEY=your-staging-openai-api-key

# Application Configuration
VITE_APP_NAME="PCS Manager (Staging)"
VITE_APP_VERSION="1.0.0-staging"
VITE_APP_DESCRIPTION="Enterprise Seafood Management System - Staging"

# Feature Flags
VITE_ENABLE_VOICE_FEATURES=true
VITE_ENABLE_ADVANCED_ANALYTICS=true
VITE_ENABLE_REAL_TIME_MONITORING=true
VITE_ENABLE_PERFORMANCE_TRACKING=true
VITE_ENABLE_DEBUG_MODE=true

# Security Configuration
VITE_ENABLE_CSP=true
VITE_ENABLE_SECURITY_HEADERS=true
VITE_ENABLE_RATE_LIMITING=false

# Performance Configuration
VITE_ENABLE_BUNDLE_ANALYSIS=true
VITE_ENABLE_LIGHTHOUSE_CI=true
VITE_PERFORMANCE_BUDGET_JS=1024
VITE_PERFORMANCE_BUDGET_CSS=50

# Monitoring Configuration
VITE_ENABLE_ERROR_TRACKING=true
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_ENABLE_USER_ANALYTICS=false
VITE_ENABLE_VERBOSE_LOGGING=true

# Compliance Configuration
VITE_ENABLE_AUDIT_LOGGING=true
VITE_ENABLE_COMPLIANCE_TRACKING=true
VITE_ENABLE_GDPR_COMPLIANCE=true

# Cache Configuration
VITE_ENABLE_SERVICE_WORKER=false
VITE_CACHE_STRATEGY=network-first
VITE_CACHE_VERSION=v1.0.0-staging