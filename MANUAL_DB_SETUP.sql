-- ============================================================================
-- MANUAL DATABASE SETUP FOR TEMPSTICK SENSOR INTEGRATION
-- ============================================================================
-- Run this SQL in your Supabase SQL Editor to create the required tables
-- for temperature monitoring functionality
-- ============================================================================

-- Step 1: Create storage_areas table
-- ============================================================================
CREATE TABLE IF NOT EXISTS public.storage_areas (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  
  -- Area identification
  name VARCHAR(255) NOT NULL,
  description TEXT,
  area_code VARCHAR(50),
  location VARCHAR(255),
  
  -- Area classification
  area_type VARCHAR(50) NOT NULL DEFAULT 'other' CHECK (area_type IN (
    'walk_in_cooler', 'walk_in_freezer', 'reach_in_cooler', 'reach_in_freezer',
    'dry_storage', 'processing_area', 'shipping_dock', 'receiving_area',
    'blast_chiller', 'ice_storage', 'other'
  )),
  
  -- Status and metadata
  is_active BOOLEAN DEFAULT true,
  
  -- Audit timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(user_id, area_code)
);

-- Step 2: Create sensors table
-- ============================================================================
CREATE TABLE IF NOT EXISTS public.sensors (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  storage_area_id UUID REFERENCES storage_areas(id) ON DELETE SET NULL,
  
  -- TempStick API fields
  sensor_id VARCHAR(100) NOT NULL,
  name VARCHAR(255) NOT NULL,
  location VARCHAR(255),
  
  -- Connection status
  is_online BOOLEAN DEFAULT true,
  is_active BOOLEAN DEFAULT true,
  last_seen_at TIMESTAMPTZ,
  
  -- Hardware status
  battery_level INTEGER,
  
  -- Thresholds
  temp_min_threshold DECIMAL(5,2),
  temp_max_threshold DECIMAL(5,2),
  humidity_min_threshold DECIMAL(5,2),
  humidity_max_threshold DECIMAL(5,2),
  
  -- Maintenance
  calibration_date TIMESTAMPTZ,
  next_maintenance_due TIMESTAMPTZ,
  maintenance_notes TEXT,
  
  -- Audit timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(user_id, sensor_id)
);

-- Step 3: Create temperature_readings table
-- ============================================================================
CREATE TABLE IF NOT EXISTS public.temperature_readings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  sensor_id UUID REFERENCES sensors(id) ON DELETE CASCADE NOT NULL,
  storage_area_id UUID REFERENCES storage_areas(id) ON DELETE CASCADE,
  
  -- Reading data
  recorded_at TIMESTAMPTZ NOT NULL,
  temp_celsius DECIMAL(6,2) NOT NULL,
  temp_fahrenheit DECIMAL(6,2) NOT NULL,
  humidity DECIMAL(5,2),
  
  -- Compliance flags
  within_safe_range BOOLEAN DEFAULT true,
  temp_violation BOOLEAN DEFAULT false,
  humidity_violation BOOLEAN DEFAULT false,
  
  -- Metadata
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Step 4: Create temperature_alerts table
-- ============================================================================
CREATE TABLE IF NOT EXISTS public.temperature_alerts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  sensor_id UUID REFERENCES sensors(id) ON DELETE CASCADE NOT NULL,
  storage_area_id UUID REFERENCES storage_areas(id) ON DELETE CASCADE,
  reading_id UUID REFERENCES temperature_readings(id) ON DELETE SET NULL,
  
  -- Alert classification
  alert_type VARCHAR(50) NOT NULL CHECK (alert_type IN (
    'temp_high', 'temp_low', 'humidity_high', 'humidity_low',
    'sensor_offline', 'battery_low', 'calibration_due', 'maintenance_due'
  )),
  
  severity VARCHAR(20) NOT NULL CHECK (severity IN (
    'info', 'warning', 'critical', 'emergency'
  )),
  
  alert_status VARCHAR(20) DEFAULT 'active' CHECK (alert_status IN (
    'active', 'acknowledged', 'resolved', 'dismissed'
  )),
  
  -- Alert details
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  threshold_value DECIMAL(8,2),
  actual_value DECIMAL(8,2),
  
  -- Timing
  first_detected_at TIMESTAMPTZ NOT NULL,
  resolved_at TIMESTAMPTZ,
  
  -- Metadata
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Step 5: Enable Row Level Security
-- ============================================================================
ALTER TABLE public.storage_areas ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sensors ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.temperature_readings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.temperature_alerts ENABLE ROW LEVEL SECURITY;

-- Step 6: Create RLS Policies
-- ============================================================================

-- Storage Areas Policies
DROP POLICY IF EXISTS "Users can manage their own storage areas" ON public.storage_areas;
CREATE POLICY "Users can manage their own storage areas" 
  ON public.storage_areas FOR ALL 
  USING (auth.uid() = user_id) 
  WITH CHECK (auth.uid() = user_id);

-- Sensors Policies  
DROP POLICY IF EXISTS "Users can manage their own sensors" ON public.sensors;
CREATE POLICY "Users can manage their own sensors" 
  ON public.sensors FOR ALL 
  USING (auth.uid() = user_id) 
  WITH CHECK (auth.uid() = user_id);

-- Temperature Readings Policies
DROP POLICY IF EXISTS "Users can manage their own temperature readings" ON public.temperature_readings;
CREATE POLICY "Users can manage their own temperature readings" 
  ON public.temperature_readings FOR ALL 
  USING (auth.uid() = user_id) 
  WITH CHECK (auth.uid() = user_id);

-- Temperature Alerts Policies
DROP POLICY IF EXISTS "Users can manage their own temperature alerts" ON public.temperature_alerts;
CREATE POLICY "Users can manage their own temperature alerts" 
  ON public.temperature_alerts FOR ALL 
  USING (auth.uid() = user_id) 
  WITH CHECK (auth.uid() = user_id);

-- Step 7: Create Performance Indexes
-- ============================================================================

-- Storage areas indexes
CREATE INDEX IF NOT EXISTS idx_storage_areas_user_id ON public.storage_areas(user_id);
CREATE INDEX IF NOT EXISTS idx_storage_areas_active ON public.storage_areas(is_active) WHERE is_active = true;

-- Sensors indexes
CREATE INDEX IF NOT EXISTS idx_sensors_user_id ON public.sensors(user_id);
CREATE INDEX IF NOT EXISTS idx_sensors_storage_area_id ON public.sensors(storage_area_id);
CREATE INDEX IF NOT EXISTS idx_sensors_sensor_id ON public.sensors(sensor_id);
CREATE INDEX IF NOT EXISTS idx_sensors_active ON public.sensors(is_active) WHERE is_active = true;

-- Temperature readings indexes (optimized for time-series queries)
CREATE INDEX IF NOT EXISTS idx_temperature_readings_user_id ON public.temperature_readings(user_id);
CREATE INDEX IF NOT EXISTS idx_temperature_readings_sensor_id_time ON public.temperature_readings(sensor_id, recorded_at DESC);
CREATE INDEX IF NOT EXISTS idx_temperature_readings_recorded_at ON public.temperature_readings(recorded_at DESC);

-- Temperature alerts indexes
CREATE INDEX IF NOT EXISTS idx_temperature_alerts_user_id ON public.temperature_alerts(user_id);
CREATE INDEX IF NOT EXISTS idx_temperature_alerts_sensor_id ON public.temperature_alerts(sensor_id);
CREATE INDEX IF NOT EXISTS idx_temperature_alerts_status ON public.temperature_alerts(alert_status) WHERE alert_status = 'active';
CREATE INDEX IF NOT EXISTS idx_temperature_alerts_created_at ON public.temperature_alerts(created_at DESC);

-- Step 8: Insert Sample Data (Optional)
-- ============================================================================

-- Insert sample storage areas (this will use the authenticated user's ID)
INSERT INTO public.storage_areas (user_id, name, description, area_type, location)
SELECT 
  auth.uid(),
  'Walk-in Cooler #1',
  'Main seafood storage cooler',
  'walk_in_cooler',
  'Warehouse Section A'
WHERE auth.uid() IS NOT NULL
AND NOT EXISTS (
  SELECT 1 FROM public.storage_areas 
  WHERE user_id = auth.uid() AND name = 'Walk-in Cooler #1'
);

INSERT INTO public.storage_areas (user_id, name, description, area_type, location)
SELECT 
  auth.uid(),
  'Freezer Unit #1',
  'Main seafood freezer',
  'walk_in_freezer', 
  'Warehouse Section B'
WHERE auth.uid() IS NOT NULL
AND NOT EXISTS (
  SELECT 1 FROM public.storage_areas 
  WHERE user_id = auth.uid() AND name = 'Freezer Unit #1'
);

-- ============================================================================
-- SETUP COMPLETE!
-- ============================================================================
-- After running this SQL:
-- 1. Your TempStick temperature monitoring tables are created
-- 2. Row Level Security is enabled for multi-user access
-- 3. Performance indexes are in place
-- 4. Sample storage areas are added
-- 
-- Next steps:
-- 1. Test the temperature monitoring page in your app
-- 2. Use the Data Source Selector to switch between real/mock data
-- 3. Add sensors through the Sensor Management interface
-- ============================================================================