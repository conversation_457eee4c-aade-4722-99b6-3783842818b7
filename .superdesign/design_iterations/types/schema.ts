// Database schema types for Voice Seafood Manager
// Generated on 2024-03-11

export interface Supplier {
  id?: string;
  name: string;
  contact_name?: string;
  email?: string;
  phone?: string;
  address?: string;
  status?: 'active' | 'inactive';
  payment_terms?: string;
  credit_limit?: number;
  metadata?: Record<string, unknown>;
  created_at?: string;
  updated_at?: string;
}

export interface Customer {
  id?: string;
  name: string;
  contact_name?: string;
  email?: string;
  phone?: string;
  address?: string;
  channel_type: 'wholesale' | 'retail' | 'distributor';
  customer_source?: string;
  status: 'active' | 'inactive';
  payment_terms?: string;
  credit_limit?: number;
  metadata?: Record<string, unknown>;
  created_at?: string;
  updated_at?: string;
}

export interface Category {
  id?: string;
  name: string;
  description?: string;
  parent_id?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Product {
  id?: string;
  name: string;
  sku?: string;
  description?: string;
  category_id?: string;
  supplier_id?: string;
  unit?: string;
  price?: number;
  cost?: number;
  min_stock?: number;
  current_stock?: number;
  amount?: number;
  condition?: 'fresh' | 'frozen' | 'other';
  other_condition?: string;
  notes?: string;
  supplier?: string;
  images?: string[];
  metadata?: Record<string, unknown>;
  
  // GS1/GDST specific fields
  gtin?: string;
  scientific_name?: string;
  fao_species_code?: string;
  production_method?: string;
  species_details?: {
    scientificName?: string;
    habitat?: string;
    sustainabilityRating?: string;
  };
  created_at?: string;
  updated_at?: string;
}

export interface Batch {
  id?: string;
  product_id: string;
  batch_number: string;
  expiry_date?: string;
  production_date?: string;
  quantity: number;
  remaining_quantity: number;
  cost?: number;
  metadata?: Record<string, unknown>;
  created_at?: string;
  updated_at?: string;
}

export interface Event {
  id?: string;
  event_type: 'receiving' | 'sales' | 'disposal' | 'production' | 'adjustment';
  product_id: string;
  batch_id?: string;
  supplier_id?: string;
  customer_id?: string;
  quantity: number;
  unit: string;
  unit_price?: number;
  unit_cost?: number;
  total_amount?: number;
  quality_status?: string;
  notes?: string;
  metadata?: Record<string, unknown>;
  created_at?: string;
  updated_at?: string;
}

export interface InventorySnapshot {
  id?: string;
  product_id: string;
  batch_id?: string;
  quantity: number;
  unit_cost?: number;
  snapshot_date: string;
  created_at?: string;
}

// New interface for COGS tracking
export interface Cogs {
  id?: string;
  batch_id: string;
  raw_product_cost: number;
  shipping_cost?: number;
  handling_cost?: number;
  processing_cost?: number;
  packaging_cost?: number;
  labor_cost?: number;
  other_costs?: number;
  total_cost?: number; // Generated by database
  cost_per_unit?: number;
  notes?: string;
  created_at?: string;
  updated_at?: string;
}

// New interface for Fulfillment tracking
export interface Fulfillment {
  id?: string;
  order_number: string;
  square_transaction_id?: string;
  dbp_order_id?: string;
  batch_id?: string;
  product_id: string;
  customer_id?: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  fulfillment_date: string;
  fulfillment_status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  shipping_method?: string;
  tracking_number?: string;
  notes?: string;
  metadata?: Record<string, unknown>;
  created_at?: string;
  updated_at?: string;
}

// New interface for HACCP logs
export interface HaccpLog {
  id?: string;
  batch_id?: string;
  log_type: 'receiving' | 'processing' | 'storage' | 'shipping' | 'temperature' | 'sanitation' | 'other';
  step_name: string;
  critical_limit?: string;
  monitoring_procedure?: string;
  corrective_action?: string;
  verification_procedure?: string;
  record_keeping?: string;
  temperature?: number;
  humidity?: number;
  ph_value?: number;
  compliance_status: 'compliant' | 'non-compliant' | 'pending' | 'corrected';
  inspector_name?: string;
  details?: string;
  log_date: string;
  created_at?: string;
  updated_at?: string;
}

// New interface for