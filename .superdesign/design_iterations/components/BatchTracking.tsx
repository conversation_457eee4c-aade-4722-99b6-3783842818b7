import React, { useState } from 'react';
import { Plus } from 'lucide-react';
import ReceivingForm from './forms/ReceivingForm';

export default function BatchTracking() {
  const [showReceivingForm, setShowReceivingForm] = useState(false);

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Batch Tracking</h1>
        <button
          onClick={() => setShowReceivingForm(true)}
          className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="w-4 h-4" />
          New Receiving
        </button>
      </div>

      {showReceivingForm ? (
        <div className="bg-white rounded-lg shadow p-6">
          <ReceivingForm
            onSuccess={() => setShowReceivingForm(false)}
            onCancel={() => setShowReceivingForm(false)}
          />
        </div>
      ) : (
        <div className="text-center py-12 bg-white rounded-lg shadow">
          <p className="text-gray-500">No receiving records to display.</p>
          <p className="text-gray-500 mt-2">Click "New Receiving" to add a record.</p>
        </div>
      )}
    </div>
  );
}