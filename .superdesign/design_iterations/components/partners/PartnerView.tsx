import React, { useState } from 'react';
import { format } from 'date-fns';
import { 
  Building2, 
  Users, 
  Clock, 
  MessageSquare,
  Plus,
  AlertCircle,
  CheckCircle2,
  XCircle
} from 'lucide-react';
import type { Partner, Invoice, Transaction, Note } from '../../types';

// Mock data - replace with actual data from your backend
const mockPartner: Partner = {
  id: '1',
  type: 'vendor',
  name: 'Ocean Fresh Ltd',
  contact: '<PERSON>',
  email: '<EMAIL>',
  phone: '******-0123',
  address: '123 Harbor Drive, Seattle, WA 98101',
  since: '2023-01-15',
  status: 'active',
  creditLimit: 50000,
  paymentTerms: 'Net 30',
  notes: [
    {
      id: '1',
      date: '2024-03-01',
      author: '<PERSON>',
      content: 'Excellent quality in last shipment',
      type: 'general'
    },
    {
      id: '2',
      date: '2024-02-15',
      author: '<PERSON>',
      content: 'Late delivery on order #12345',
      type: 'warning'
    }
  ],
  tags: ['Premium', 'Salmon', 'Certified']
};

const mockInvoices: Invoice[] = [
  {
    id: '1',
    number: 'INV-2024-001',
    date: '2024-03-01',
    dueDate: '2024-03-31',
    amount: 12500,
    status: 'paid',
    items: [
      {
        description: 'Atlantic Salmon',
        quantity: 500,
        price: 25,
        total: 12500
      }
    ]
  },
  {
    id: '2',
    number: 'INV-2024-002',
    date: '2024-03-15',
    dueDate: '2024-04-14',
    amount: 8750,
    status: 'pending',
    items: [
      {
        description: 'King Salmon',
        quantity: 250,
        price: 35,
        total: 8750
      }
    ]
  }
];

const mockTransactions: Transaction[] = [
  {
    id: '1',
    type: 'purchase',
    date: '2024-03-01',
    amount: 12500,
    items: [
      {
        productId: '1',
        quantity: 500,
        price: 25
      }
    ],
    status: 'completed',
    paymentMethod: 'bank_transfer',
    reference: 'PO-2024-001'
  }
];

type Tab = 'overview' | 'invoices' | 'transactions' | 'notes';

export default function PartnerView({ partnerId }: { partnerId: string }) {
  const [activeTab, setActiveTab] = useState<Tab>('overview');
  // partnerId will be used to fetch actual partner data in future implementation
  const partner = mockPartner;
  console.log('Partner ID:', partnerId); // Temporary usage to avoid ESLint error

  const renderTab = () => {
    switch (activeTab) {
      case 'overview':
        return <OverviewTab partner={partner} />;
      case 'invoices':
        return <InvoicesTab invoices={mockInvoices} />;
      case 'transactions':
        return <TransactionsTab transactions={mockTransactions} />;
      case 'notes':
        return <NotesTab notes={partner.notes} />;
      default:
        return null;
    }
  };

  return (
    <div className="h-full flex flex-col">
      <header className="bg-white border-b p-6">
        <div className="flex justify-between items-start mb-4">
          <div>
            <div className="flex items-center gap-3">
              <h1 className="text-2xl font-bold text-gray-900">{partner.name}</h1>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                partner.status === 'active' 
                  ? 'bg-green-100 text-green-800'
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {partner.status.toUpperCase()}
              </span>
            </div>
            <p className="text-gray-500 mt-1">
              Partner since {format(new Date(partner.since), 'MMMM yyyy')}
            </p>
          </div>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            New Transaction
          </button>
        </div>

        <div className="flex gap-4">
          {(['overview', 'invoices', 'transactions', 'notes'] as const).map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`px-4 py-2 rounded-lg transition-colors ${
                activeTab === tab
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </button>
          ))}
        </div>
      </header>

      <div className="flex-1 overflow-y-auto p-6 bg-gray-50">
        {renderTab()}
      </div>
    </div>
  );
}

function OverviewTab({ partner }: { partner: Partner }) {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold mb-4">Contact Information</h2>
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Building2 className="w-5 h-5 text-gray-400" />
              <span>{partner.address}</span>
            </div>
            <div className="flex items-center gap-2">
              <Users className="w-5 h-5 text-gray-400" />
              <span>{partner.contact}</span>
            </div>
            <div className="flex items-center gap-2">
              <MessageSquare className="w-5 h-5 text-gray-400" />
              <span>{partner.email}</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold mb-4">Tags</h2>
          <div className="flex flex-wrap gap-2">
            {partner.tags.map((tag) => (
              <span
                key={tag}
                className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
              >
                {tag}
              </span>
            ))}
            <button className="px-3 py-1 border border-gray-300 rounded-full text-sm text-gray-600 hover:bg-gray-50">
              + Add Tag
            </button>
          </div>
        </div>
      </div>

      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold mb-4">Financial Overview</h2>
          <div className="grid grid-cols-2 gap-4">
            <div className="p-4 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-500">Credit Limit</p>
              <p className="text-xl font-semibold">${partner.creditLimit?.toLocaleString()}</p>
            </div>
            <div className="p-4 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-500">Payment Terms</p>
              <p className="text-xl font-semibold">{partner.paymentTerms}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold">Recent Notes</h2>
            <button className="text-blue-600 hover:text-blue-700">View All</button>
          </div>
          <div className="space-y-4">
            {partner.notes.slice(0, 3).map((note) => (
              <div
                key={note.id}
                className={`p-4 rounded-lg ${
                  note.type === 'warning'
                    ? 'bg-yellow-50 border-l-4 border-yellow-400'
                    : note.type === 'important'
                    ? 'bg-red-50 border-l-4 border-red-400'
                    : 'bg-gray-50'
                }`}
              >
                <div className="flex justify-between items-start mb-2">
                  <span className="text-sm font-medium">{note.author}</span>
                  <span className="text-sm text-gray-500">
                    {format(new Date(note.date), 'MMM d, yyyy')}
                  </span>
                </div>
                <p className="text-sm text-gray-600">{note.content}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

function InvoicesTab({ invoices }: { invoices: Invoice[] }) {
  return (
    <div className="bg-white rounded-lg shadow">
      <div className="p-6 border-b border-gray-200">
        <div className="flex justify-between items-center">
          <h2 className="text-lg font-semibold">Invoices</h2>
          <button className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            <Plus className="w-4 h-4" />
            New Invoice
          </button>
        </div>
      </div>
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Invoice #
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Date
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Due Date
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Amount
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {invoices.map((invoice) => (
              <tr key={invoice.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="text-sm font-medium text-gray-900">
                    {invoice.number}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="text-sm text-gray-500">
                    {format(new Date(invoice.date), 'MMM d, yyyy')}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="text-sm text-gray-500">
                    {format(new Date(invoice.dueDate), 'MMM d, yyyy')}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="text-sm font-medium text-gray-900">
                    ${invoice.amount.toLocaleString()}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                    invoice.status === 'paid'
                      ? 'bg-green-100 text-green-800'
                      : invoice.status === 'overdue'
                      ? 'bg-red-100 text-red-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {invoice.status.toUpperCase()}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <button className="text-blue-600 hover:text-blue-700">View</button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}

function TransactionsTab({ transactions }: { transactions: Transaction[] }) {
  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold">Transaction History</h2>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Reference
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {transactions.map((transaction) => (
                <tr key={transaction.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm text-gray-500">
                      {format(new Date(transaction.date), 'MMM d, yyyy')}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`text-sm font-medium ${
                      transaction.type === 'purchase'
                        ? 'text-purple-600'
                        : 'text-green-600'
                    }`}>
                      {transaction.type.toUpperCase()}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm text-gray-900">
                      {transaction.reference}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm font-medium text-gray-900">
                      ${transaction.amount.toLocaleString()}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
                      transaction.status === 'completed'
                        ? 'bg-green-100 text-green-800'
                        : transaction.status === 'cancelled'
                        ? 'bg-red-100 text-red-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {transaction.status === 'completed' && <CheckCircle2 className="w-3 h-3" />}
                      {transaction.status === 'cancelled' && <XCircle className="w-3 h-3" />}
                      {transaction.status === 'pending' && <Clock className="w-3 h-3" />}
                      {transaction.status.toUpperCase()}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

function NotesTab({ notes }: { notes: Note[] }) {
  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-lg font-semibold">Notes & Comments</h2>
          <button className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            <Plus className="w-4 h-4" />
            Add Note
          </button>
        </div>

        <div className="space-y-4">
          {notes.map((note) => (
            <div
              key={note.id}
              className={`p-4 rounded-lg ${
                note.type === 'warning'
                  ? 'bg-yellow-50 border-l-4 border-yellow-400'
                  : note.type === 'important'
                  ? 'bg-red-50 border-l-4 border-red-400'
                  : 'bg-gray-50'
              }`}
            >
              <div className="flex justify-between items-start mb-2">
                <div className="flex items-center gap-2">
                  {note.type === 'warning' && (
                    <AlertCircle className="w-4 h-4 text-yellow-500" />
                  )}
                  {note.type === 'important' && (
                    <AlertCircle className="w-4 h-4 text-red-500" />
                  )}
                  <span className="font-medium">{note.author}</span>
                </div>
                <span className="text-sm text-gray-500">
                  {format(new Date(note.date), 'MMM d, yyyy')}
                </span>
              </div>
              <p className="text-gray-600">{note.content}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}