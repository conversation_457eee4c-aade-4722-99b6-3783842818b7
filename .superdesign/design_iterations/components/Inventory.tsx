import { useState } from 'react';
import Products from './Products';
import { Plus } from 'lucide-react';
import VoiceInventory from './voice/VoiceInventory';
import { useNavigationContext } from '../contexts/NavigationContext';

export default function Inventory() {
  const [activeTab, setActiveTab] = useState('products');
  const { setActiveView } = useNavigationContext();

  return (
    <div className="relative">
      <div className="p-6 space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-900">Inventory</h1>
          <div className="flex space-x-4">
            <button
              onClick={() => setActiveTab('products')}
              className={`px-4 py-2 rounded-lg ${
                activeTab === 'products'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-800 hover:bg-gray-300'
              }`}
            >
              Products
            </button>
            <button
              onClick={() => setActiveTab('batchTracking')}
              className={`px-4 py-2 rounded-lg ${
                activeTab === 'batchTracking'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-800 hover:bg-gray-300'
              }`}
            >
              Batch Tracking
            </button>
            <button
              onClick={() => setActiveTab('voice')}
              className={`px-4 py-2 rounded-lg ${
                activeTab === 'voice'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-800 hover:bg-gray-300'
              }`}
            >
              Voice Input
            </button>
          </div>
        </div>

        {activeTab === 'products' && <Products />}
        {activeTab === 'voice' && <VoiceInventory />}

        {activeTab === 'batchTracking' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold text-gray-800">Batch Tracking</h2>
              <button
                onClick={() => setActiveView('HACCP Events')}
                className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Plus className="w-4 h-4" />
                New HACCP Event
              </button>
            </div>

            <div className="text-center py-12 bg-white rounded-lg shadow">
              <p className="text-gray-500">No receiving records to display.</p>
              <p className="text-gray-500 mt-2">Use the HACCP Events page to add a record.</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
