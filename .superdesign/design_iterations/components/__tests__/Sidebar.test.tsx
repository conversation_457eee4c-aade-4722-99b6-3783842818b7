import { test, expect } from '@playwright/experimental-ct-react';
import Sidebar, { SidebarProps } from '../Sidebar';

test.describe('Sidebar Component', () => {
  test.beforeEach(async ({ mount }) => {
    const props: SidebarProps = {
      activeView: 'Dashboard',
      setActiveView: () => {},
      onSignOut: () => {},
    };
    await mount(<Sidebar {...props} />);
  });

  test('should render all navigation links', async ({ page }) => {
    const links = await page.locator('nav a');
    await expect(links).toHaveCount(8); // Adjust count based on actual links
  });

  test('should highlight active link', async ({ page }) => {
    const activeLink = await page.locator('nav a[aria-current="page"]');
    await expect(activeLink).toBeVisible();
  });

  test('should be collapsible on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    const toggleButton = await page.locator('button[aria-label="Toggle sidebar"]');
    await expect(toggleButton).toBeVisible();
    
    // Test toggle functionality
    const initialSidebar = await page.locator('aside');
    await expect(initialSidebar).toHaveClass(/hidden/);
    
    await toggleButton.click();
    await expect(initialSidebar).not.toHaveClass(/hidden/);
  });

  test('should meet accessibility standards', async ({ page }) => {
    // Test keyboard navigation
    await page.keyboard.press('Tab');
    const firstFocusable = await page.locator('nav a').first();
    await expect(firstFocusable).toBeFocused();
    
    // Test ARIA attributes
    const nav = await page.locator('nav');
    await expect(nav).toHaveAttribute('aria-label', 'Main navigation');
  });
});

import React from 'react';
import {
  // ...existing imports...
} from 'lucide-react';

interface SidebarProps {
  activeView: string;
  setActiveView: (view: string) => void;
  onSignOut: () => void;
}

export default function Sidebar({ activeView, setActiveView }: Omit<SidebarProps, 'onSignOut'>) {
  const handleNavigation = (viewName: string) => {
    console.log('Navigating to:', viewName);
    setActiveView(viewName);
  };

  return (
    <div className="flex flex-col h-full bg-gray-900 text-white w-64 py-6">
      {/* ...existing header... */}
      <nav className="flex-1 px-4">
        <ul className="space-y-1">
          {navigation.map((item) => (
            <li key={item.name}>
              <button 
                onClick={() => handleNavigation(item.name)}
                className={`flex items-center gap-3 w-full px-3 py-2 rounded-lg transition-colors ${
                  activeView === item.name
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-300 hover:bg-gray-800 hover:text-white'
                }`}
              >
                <item.icon className="w-5 h-5" />
                {item.name}
              </button>
            </li>
          ))}
        </ul>
      </nav>
    </div>
  );
}
