import React, { useEffect, useState } from 'react';
import { useNavigationContext } from '../contexts/NavigationContext';
import { supabase } from '../lib/supabase';
import Sidebar from './Sidebar';
import Dashboard from './Dashboard';
import Inventory from './Inventory';
import Analytics from './Analytics';
import Messages from './Messages';
import Settings from './Settings';
import VendorsView from './vendors/VendorsView';
import CustomersView from './customers/CustomersView';
import ImportInventory from './import/ImportInventory';
import HACCPEventsView from './HACCPEventsView';
import BatchTracking from './BatchTracking';
import HACCPCalendar from './HACCPCalendar';

interface AppLayoutProps {
  children?: React.ReactNode;
}

function AppLayout({ children }: AppLayoutProps) {
  const { activeView, setActiveView } = useNavigationContext();
  const [userEmail, setUserEmail] = useState<string | null>(null);

  useEffect(() => {
    async function getSession() {
      const { data: { session } } = await supabase.auth.getSession();
      setUserEmail(session?.user?.email || null);
    }
    getSession();

    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setUserEmail(session?.user?.email || null);
    });

    return () => subscription.unsubscribe();
  }, []);

  const handleSignOut = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  let content;
  switch (activeView) {
    case 'Dashboard':
      content = <Dashboard />;
      break;
    case 'Inventory':
      content = <Inventory />;
      break;
    case 'Vendors':
      content = <VendorsView />;
      break;
    case 'Customers':
      content = <CustomersView />;
      break;
    case 'Analytics':
      content = <Analytics />;
      break;
    case 'Messages':
      content = <Messages />;
      break;
    case 'Settings':
      content = <Settings />;
      break;
    case 'Import':
      content = <ImportInventory />;
      break;
    case 'HACCP Events':
      content = <HACCPEventsView />;
      break;
    case 'HACCP: Events':
      content = <HACCPEventsView />;
      break;
    case 'HACCP: Batches':
      content = <BatchTracking />;
      break;
    case 'HACCP: Calendar':
      content = <HACCPCalendar />;
      break;
    default:
      content = <Dashboard />;
  }

  return (
    <div className="relative flex h-screen bg-gray-100">
      <Sidebar 
        activeView={activeView}
        setActiveView={setActiveView}
        onSignOut={handleSignOut}
        userEmail={userEmail || undefined}
        className="z-20"
      />
      <div className="relative flex-1 overflow-auto">
        <div className="relative z-0">
          {content}
          {children}
        </div>
      </div>
    </div>
  );
}

export default AppLayout;