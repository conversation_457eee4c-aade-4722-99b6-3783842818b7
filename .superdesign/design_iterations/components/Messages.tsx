import React from 'react';
import { Mail, Star, Archive } from 'lucide-react';

const messages = [
  {
    id: 1,
    sender: 'Ocean Fresh Ltd',
    subject: 'Order Confirmation #12345',
    preview: 'Your order for Atlantic Salmon (150kg) has been confirmed...',
    date: '2024-03-10T10:30:00',
    unread: true,
    important: true,
  },
  {
    id: 2,
    sender: 'Sea Harvest Co',
    subject: 'Price Update Notice',
    preview: 'Due to seasonal changes, we are updating our prices for...',
    date: '2024-03-09T15:45:00',
    unread: false,
    important: true,
  },
  {
    id: 3,
    sender: 'Quality Control',
    subject: 'Inspection Report',
    preview: 'Monthly quality inspection report for February 2024...',
    date: '2024-03-08T09:15:00',
    unread: false,
    important: false,
  },
];

export default function Messages() {
  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Messages</h1>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
          Compose
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="space-y-2">
          <button className="w-full flex items-center gap-3 px-4 py-2 bg-blue-600 text-white rounded-lg">
            <Mail className="w-5 h-5" />
            Inbox
          </button>
          <button className="w-full flex items-center gap-3 px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg">
            <Star className="w-5 h-5" />
            Important
          </button>
          <button className="w-full flex items-center gap-3 px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg">
            <Archive className="w-5 h-5" />
            Archived
          </button>
        </div>

        <div className="md:col-span-3 bg-white rounded-lg shadow divide-y">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`p-4 hover:bg-gray-50 cursor-pointer ${
                message.unread ? 'bg-blue-50' : ''
              }`}
            >
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-3">
                  <span className={`font-medium ${message.unread ? 'text-gray-900' : 'text-gray-700'}`}>
                    {message.sender}
                  </span>
                  {message.important && (
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                  )}
                </div>
                <span className="text-sm text-gray-500">
                  {new Date(message.date).toLocaleDateString()}
                </span>
              </div>
              <h3 className={`text-sm ${message.unread ? 'font-semibold' : ''}`}>
                {message.subject}
              </h3>
              <p className="text-sm text-gray-600 mt-1">{message.preview}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}