/*
  Type-aware mapping model for the Import Wizard (Phase 1–2)
  - Defines import targets, field catalogs, mapping rules, and validation helpers.
*/

export type ImportTarget = 'Products' | 'inventory_events';

export type FieldType = 'string' | 'number' | 'date' | 'boolean' | 'enum' | 'json';

export interface TargetField {
  key: string;
  label: string;
  type: FieldType;
  required?: boolean;
  enumValues?: string[]; // for type === 'enum'
  description?: string;
}

export interface SourceColumn {
  key: string; // header name from CSV
  label: string;
  inferredType?: FieldType;
}

export type MappingSourceType = 'column' | 'constant' | 'derived' | 'metadata';

export interface MappingRuleBase {
  targetKey: string; // matches TargetField.key
  sourceType: MappingSourceType;
}

export interface ColumnMappingRule extends MappingRuleBase {
  sourceType: 'column';
  columnKey: string; // name of CSV column
}

export interface ConstantMappingRule extends MappingRuleBase {
  sourceType: 'constant';
  value: string | number | boolean | null;
}

export interface DerivedMappingRule extends MappingRuleBase {
  sourceType: 'derived';
  // Placeholder for a future expression DSL; for now it will be informational only
  expression: string; // e.g. "qty * unit_price" or template
}

export interface MetadataMappingRule extends MappingRuleBase {
  sourceType: 'metadata';
  // Maps a CSV column directly into metadata[targetKey]
  columnKey: string;
}

export type MappingRule =
  | ColumnMappingRule
  | ConstantMappingRule
  | DerivedMappingRule
  | MetadataMappingRule;

export interface ImportMapping {
  target: ImportTarget;
  rules: MappingRule[];
}

// Field catalogs for each target
export const ProductFields: TargetField[] = [
  { key: 'name', label: 'Name', type: 'string', required: true },
  { key: 'sku', label: 'SKU', type: 'string', required: true },
  { key: 'category', label: 'Category', type: 'string', required: true },
  { key: 'unit', label: 'Default Unit', type: 'string', required: true },
  { key: 'price', label: 'Price', type: 'number' },
  { key: 'cost', label: 'Cost', type: 'number' },
  { key: 'description', label: 'Description', type: 'string' },
  { key: 'origin', label: 'Origin', type: 'string' },
  { key: 'notes', label: 'Notes', type: 'string' },
  { key: 'metadata', label: 'Metadata (JSON)', type: 'json' },
];

export const InventoryEventFields: TargetField[] = [
  {
    key: 'event_type',
    label: 'Event Type',
    type: 'enum',
    enumValues: ['receiving', 'sale', 'disposal', 'production', 'adjustment'],
    required: true,
    description: 'Unified events design',
  },
  {
    key: 'product_id',
    label: 'Product (FK)',
    type: 'string', // FK wiring later (Phase 3)
    description: 'Link to Products; will support lookup/creation',
  },
  { key: 'name', label: 'Name', type: 'string' },
  { key: 'quantity', label: 'Quantity', type: 'number', required: true },
  { key: 'unit_price', label: 'Unit Price', type: 'number' },
  { key: 'total_amount', label: 'Total Amount', type: 'number' },
  { key: 'timestamp', label: 'Timestamp', type: 'date' },
  { key: 'category', label: 'Category', type: 'string' },
  { key: 'notes', label: 'Notes', type: 'string' },
  { key: 'images', label: 'Images (Array)', type: 'json' },
  { key: 'metadata', label: 'Metadata (JSON)', type: 'json' },
];

export function getFieldCatalog(target: ImportTarget): TargetField[] {
  return target === 'Products' ? ProductFields : InventoryEventFields;
}

// Simple type inference for CSV samples
export function inferFieldType(samples: string[]): FieldType {
  let numeric = true;
  let dates = true;
  for (const s of samples) {
    const v = s?.trim();
    if (!v) continue;
    if (numeric && isNaN(Number(v))) numeric = false;
    if (dates && isNaN(Date.parse(v))) dates = false;
  }
  if (numeric) return 'number';
  if (dates) return 'date';
  return 'string';
}

export interface SourceSchema {
  columns: SourceColumn[];
}

export function buildSourceSchema(rows: Record<string, unknown>[], sampleCount = 10): SourceSchema {
  if (!rows?.length) return { columns: [] };
  const headers = Object.keys(rows[0] || {});
  const columns: SourceColumn[] = headers.map((key) => {
    const samples = rows.slice(0, sampleCount).map((r) => String((r as Record<string, unknown>)[key] ?? ''));
    return { key, label: key, inferredType: inferFieldType(samples) };
  });
  return { columns };
}

export interface ValidationIssue {
  field: string;
  message: string;
}

export function validateMapping(mapping: ImportMapping, targetFields: TargetField[]): ValidationIssue[] {
  const issues: ValidationIssue[] = [];
  const mapByTarget = new Map<string, MappingRule>();
  for (const rule of mapping.rules) {
    mapByTarget.set(rule.targetKey, rule);
  }
  for (const f of targetFields) {
    if (f.required && !mapByTarget.get(f.key)) {
      issues.push({ field: f.key, message: `Required field not mapped: ${f.label}` });
    }
  }
  return issues;
}

// Apply mapping to rows to build target-shaped objects + metadata
export function applyMapping(
  rows: Array<Record<string, unknown>>,
  mapping: ImportMapping,
  targetFields: TargetField[]
): Array<Record<string, unknown>> {
  const outputs: Array<Record<string, unknown>> = [];
  for (const row of rows) {
    const out: Record<string, unknown> = {};
    const metadata: Record<string, unknown> = {};
    for (const rule of mapping.rules) {
      const f = targetFields.find((x) => x.key === rule.targetKey);
      if (!f) continue;
      switch (rule.sourceType) {
        case 'column': {
          const value = (row as Record<string, unknown>)[(rule as ColumnMappingRule).columnKey];
          // store as-is; downstream sanitizers will coerce types as needed
          out[f.key] = value as unknown;
          break;
        }
        case 'constant': {
          out[f.key] = (rule as ConstantMappingRule).value as unknown;
          break;
        }
        case 'derived': {
          // Phase 1–2: placeholders only; keep expression as note in metadata
          metadata[`derived:${f.key}`] = (rule as DerivedMappingRule).expression;
          break;
        }
        case 'metadata': {
          const col = (rule as MetadataMappingRule).columnKey;
          metadata[rule.targetKey] = (row as Record<string, unknown>)[col] as unknown;
          break;
        }
      }
    }
    if ('metadata' in out && typeof out['metadata'] === 'object') {
      out['metadata'] = { ...(out['metadata'] as Record<string, unknown>), ...metadata };
    } else if (Object.keys(metadata).length > 0) {
      out['metadata'] = metadata;
    }
    outputs.push(out);
  }
  return outputs;
}
