export interface ImportRow {
  rowNumber?: number;
  source?: 'voice' | 'file' | 'manual';
  date: string;
  name: string;
  description?: string;
  sku: string;
  category: string;
  sub_category?: string;
  stock: number;
  unit: string;
  min_stock?: number;
  price?: number;
  cost?: number;
  gross_amount?: number;
  net_amount?: number;
  supplier_id?: string;
  vendor?: string;
  expiry_date?: string;
  storage_temp?: string;
  handling_instructions?: string;
  seasonal_availability?: string[];
  origin?: string;
  species_details?: {
    scientificName?: string;
    habitat?: string;
    sustainabilityRating?: string;
  };
  event_type?: 'receiving' | 'sales' | 'disposal' | 'production' | 'adjustment';
  customer?: string;
  channel?: string;
  total_amount?: number;
  unit_price?: number;
  unit_cost?: number;
  quality_status?: string;
  batch_number?: string;
  created_at?: string;
  updated_at?: string;
  notes?: string;
}

export interface ValidationError {
  row: number;
  message: string;
}

export interface ColumnMapping {
  sourceColumn: string;
  targetField: keyof ImportRow;
}

export interface ImportResult {
  data: ImportRow[];
  errors: ValidationError[];
}

export interface ImportConfig {
  requiredFields: Array<keyof ImportRow>;
  optionalFields: Array<keyof ImportRow>;
  defaultValues?: Partial<ImportRow>;
  validators?: {
    [K in keyof ImportRow]?: (value: ImportRow[K]) => string | null;
  };
}

export interface ProcessedVoiceData {
  data: ImportRow[];
  errors: ValidationError[];
}
