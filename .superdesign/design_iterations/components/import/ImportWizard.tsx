import React, { useMemo, useState } from 'react';
import <PERSON> from 'papa<PERSON><PERSON>';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { supabase } from '../../lib/supabase';
import { useToast } from '../ui/use-toast';
import {
  ImportTarget,
  getFieldCatalog,
  buildSourceSchema,
  applyMapping,
  validateMapping,
  ProductFields,
  InventoryEventFields,
  type ImportMapping,
  type MappingSourceType,
  type ColumnMappingRule,
  type ConstantMappingRule,
  type DerivedMappingRule,
  type MetadataMappingRule,
} from './mapping';

// Simple step labels
const STEPS = ['Choose Type', 'Load Data', 'Map Fields', 'Validate & Preview'];

// Wizard scaffolding only (Phase 1–2):
// - Lets user pick target (Products or inventory_events)
// - Load CSV via file upload or paste
// - Build simple mapping rules (column or constant; derived/metadata placeholders)
// - Validate required fields and show a preview
// - Does NOT persist or wire FKs yet

export default function ImportWizard() {
  const [step, setStep] = useState<number>(0);
  const [target, setTarget] = useState<ImportTarget>('Products');
  const [csvRows, setCsvRows] = useState<Record<string, string>[]>([]);
  const [rawCsvText, setRawCsvText] = useState<string>('');
  const [mapping, setMapping] = useState<ImportMapping>({ target: 'Products', rules: [] });
  const [error, setError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const { toast } = useToast();

  const fields = useMemo(() => getFieldCatalog(target), [target]);
  const sourceSchema = useMemo(() => buildSourceSchema(csvRows), [csvRows]);

  // Reset mapping when target changes
  React.useEffect(() => {
    setMapping((m) => ({ target, rules: m.rules.filter((r) => fields.some((f) => f.key === r.targetKey)) }));
  }, [target, fields]);

  function next() {
    setStep((s) => Math.min(s + 1, STEPS.length - 1));
  }
  function prev() {
    setStep((s) => Math.max(s - 1, 0));
  }

  function handleFileUpload(e: React.ChangeEvent<HTMLInputElement>) {
    const file = e.target.files?.[0];
    if (!file) return;
    setError(null);
    file.text()
      .then((text) => parseCsv(text))
      .catch((err) => setError(String(err?.message || err)));
  }

  function parseCsv(text: string) {
    setRawCsvText(text);
    const result = Papa.parse<Record<string, string>>(text, { header: true, skipEmptyLines: true, dynamicTyping: false });
    if (result.errors?.length) {
      setError(`CSV parse error: ${result.errors[0].message}`);
      return;
    }
    setCsvRows(result.data || []);
  }

  function setColumnRule(targetKey: string, columnKey: string | '') {
    setMapping((m) => {
      // Remove any existing rule for this targetKey
      const others = m.rules.filter((r) => r.targetKey !== targetKey);
      if (!columnKey) return { ...m, rules: others };
      const newRule: ColumnMappingRule = { targetKey, sourceType: 'column', columnKey };
      return { ...m, rules: [...others, newRule] };
    });
  }

  function setConstantRule(targetKey: string, value: string) {
    setMapping((m) => {
      const others = m.rules.filter((r) => r.targetKey !== targetKey);
      // Treat empty string as removing the constant rule
      if (value === '') {
        return { ...m, rules: others };
      }
      const newRule: ConstantMappingRule = { targetKey, sourceType: 'constant', value };
      return { ...m, rules: [...others, newRule] };
    });
  }

  function addPlaceholderRule(targetKey: string, type: MappingSourceType) {
    if (type === 'derived') {
      setMapping((m) => {
        const newRule: DerivedMappingRule = { targetKey, sourceType: 'derived', expression: 'TODO: define' };
        return {
          ...m,
          rules: [...m.rules.filter((r) => r.targetKey !== targetKey), newRule],
        };
      });
    } else if (type === 'metadata') {
      // default to mapping a selected column later; keep placeholder
      setMapping((m) => {
        const newRule: MetadataMappingRule = { targetKey, sourceType: 'metadata', columnKey: '' };
        return {
          ...m,
          rules: [...m.rules.filter((r) => r.targetKey !== targetKey), newRule],
        };
      });
    }
  }

  function getColumnRuleValue(targetKey: string): string {
    const r = mapping.rules.find((r) => r.targetKey === targetKey && r.sourceType === 'column') as ColumnMappingRule | undefined;
    return r?.columnKey ?? '';
  }

  function getConstantRuleValue(targetKey: string): string {
    const r = mapping.rules.find((r) => r.targetKey === targetKey && r.sourceType === 'constant') as ConstantMappingRule | undefined;
    // Normalize to string for input value; show empty when not set
    const v = r?.value;
    if (v === null || v === undefined) return '';
    return String(v);
  }

  const issues = useMemo(() => validateMapping(mapping, fields), [mapping, fields]);
  const missingRequired = useMemo(
    () => fields.filter((f) => f.required && !mapping.rules.some((r) => r.targetKey === f.key)),
    [fields, mapping]
  );
  const preview = useMemo(() => applyMapping(csvRows.slice(0, 5), mapping, fields), [csvRows, mapping, fields]);

  // Helpers
  function chunkArray<T>(arr: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < arr.length; i += size) chunks.push(arr.slice(i, i + size));
    return chunks;
  }

  function normalizeEventType(et: string | undefined): string | undefined {
    if (!et) return et;
    const v = String(et).toLowerCase();
    if (v === 'sales') return 'sale';
    return v;
  }

  function sanitizeProducts(rows: Array<Record<string, unknown>>): Array<Record<string, unknown>> {
    const allowed = new Set(['name','sku','category','unit','price','cost','description','origin','notes','metadata']);
    return rows.map((r) => {
      const out: Record<string, unknown> = {};
      for (const k of Object.keys(r || {})) {
        if (allowed.has(k)) out[k] = (r as Record<string, unknown>)[k];
      }
      return out;
    });
  }

  function sanitizeInventoryEvents(rows: Array<Record<string, unknown>>): Array<Record<string, unknown>> {
    return rows.map((r) => {
      const out: Record<string, unknown> = {};
      // map allowed fields only
      if ((r as Record<string, unknown>)['event_type']) out['event_type'] = normalizeEventType((r as Record<string, unknown>)['event_type'] as string | undefined);
      if ((r as Record<string, unknown>)['product_id']) out['product_id'] = (r as Record<string, unknown>)['product_id']; // assumes UUID provided
      if ((r as Record<string, unknown>)['name']) out['name'] = (r as Record<string, unknown>)['name'];
      if ((r as Record<string, unknown>)['quantity'] != null) out['quantity'] = Number((r as Record<string, unknown>)['quantity'] as unknown as string);
      if ((r as Record<string, unknown>)['unit_price'] != null) out['unit_price'] = Number((r as Record<string, unknown>)['unit_price'] as unknown as string);
      if ((r as Record<string, unknown>)['total_amount'] != null) out['total_amount'] = Number((r as Record<string, unknown>)['total_amount'] as unknown as string);
      if ((r as Record<string, unknown>)['category']) out['category'] = (r as Record<string, unknown>)['category'];
      if ((r as Record<string, unknown>)['images']) {
        const val = (r as Record<string, unknown>)['images'];
        if (Array.isArray(val)) {
          out['images'] = val as unknown[];
        } else if (typeof val === 'string') {
          const arr = (val as string)
            .split(',')
            .map((s) => s.trim())
            .filter((s) => s.length > 0);
          out['images'] = arr;
        }
      }
      if ((r as Record<string, unknown>)['notes']) out['notes'] = (r as Record<string, unknown>)['notes'];
      if ((r as Record<string, unknown>)['metadata']) out['metadata'] = (r as Record<string, unknown>)['metadata'];
      // optional timestamp mapped to created_at if provided
      if ((r as Record<string, unknown>)['timestamp']) out['created_at'] = (r as Record<string, unknown>)['timestamp'];
      return out;
    });
  }

  async function handleSubmit() {
    try {
      setError(null);
      if (!csvRows.length) {
        setError('No rows to import.');
        return;
      }
      if (issues.length > 0) {
        setError('Please resolve validation issues before submitting.');
        return;
      }
      setSubmitting(true);
      const all = applyMapping(csvRows, mapping, fields);

      let inserted = 0;
      if (target === 'Products') {
        const payload = sanitizeProducts(all);
        for (const batch of chunkArray(payload, 500)) {
          const { error: insErr, count } = await supabase
            .from('Products')
            .insert(batch, { count: 'exact' });
          if (insErr) throw insErr;
          inserted += count || batch.length;
        }
      } else if (target === 'inventory_events') {
        const payload = sanitizeInventoryEvents(all);
        for (const batch of chunkArray(payload, 500)) {
          const { error: insErr, count } = await supabase
            .from('inventory_events')
            .insert(batch, { count: 'exact' });
          if (insErr) throw insErr;
          inserted += count || batch.length;
        }
      }

      toast({ title: 'Import completed', description: `Inserted ${inserted} ${target} row(s).` });
    } catch (e: unknown) {
      const msg = e instanceof Error ? e.message : String(e);
      setError(msg);
      toast({ title: 'Import failed', description: msg });
    } finally {
      setSubmitting(false);
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Import Wizard (Beta)</CardTitle>
        <CardDescription>Type-aware, extensible mapping. Phase 1–2 scaffolding only.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Stepper */}
        <div className="flex items-center gap-2 text-sm">
          {STEPS.map((label, i) => (
            <div key={label} className="flex items-center gap-2">
              <div className={`h-6 w-6 rounded-full text-center leading-6 ${i <= step ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'}`}>{i + 1}</div>
              <span className={i === step ? 'font-medium' : ''}>{label}</span>
              {i < STEPS.length - 1 && <span className="text-gray-400">›</span>}
            </div>
          ))}
        </div>

        {/* Step 1: Choose Type */}
        {step === 0 && (
          <div className="space-y-3">
            <p className="text-sm text-gray-600">Select what you are importing.</p>
            <div className="flex gap-2">
              <Button variant={target === 'Products' ? 'default' : 'secondary'} onClick={() => setTarget('Products')}>Products</Button>
              <Button variant={target === 'inventory_events' ? 'default' : 'secondary'} onClick={() => setTarget('inventory_events')}>Inventory Events</Button>
            </div>
            <div className="text-xs text-gray-500">
              {target === 'Products' ? 'Fields: ' + ProductFields.map((f) => f.label).join(', ') : 'Fields: ' + InventoryEventFields.map((f) => f.label).join(', ')}
            </div>
          </div>
        )}

        {/* Step 2: Load Data */}
        {step === 1 && (
          <div className="space-y-3">
            <div className="space-y-2">
              <label className="text-sm font-medium" htmlFor="wizard-file">Upload CSV</label>
              <input id="wizard-file" type="file" accept=".csv" onChange={handleFileUpload} />
            </div>
            <div className="space-y-1">
              <label className="text-sm font-medium" htmlFor="wizard-paste">Or paste CSV</label>
              <textarea id="wizard-paste" className="w-full min-h-[120px] rounded-md border p-2 text-sm" value={rawCsvText} onChange={(e) => setRawCsvText(e.target.value)} />
              <div>
                <Button variant="secondary" onClick={() => parseCsv(rawCsvText)}>Parse</Button>
              </div>
            </div>
            {csvRows.length > 0 && (
              <div className="rounded-md border p-2 text-xs">
                <div className="font-medium">Detected Columns</div>
                <div className="mt-1 flex flex-wrap gap-2">
                  {sourceSchema.columns.map((c) => (
                    <span key={c.key} className="rounded border bg-muted px-2 py-0.5">{c.label}{c.inferredType ? ` (${c.inferredType})` : ''}</span>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Step 3: Map Fields */}
        {step === 2 && (
          <div className="space-y-3">
            {fields.length === 0 && <p className="text-sm text-gray-500">Select a type and load data first.</p>}
            {fields.length > 0 && (
              <>
                {missingRequired.length > 0 && (
                  <div className="rounded-md border border-yellow-300 bg-yellow-50 p-2 text-xs text-yellow-800">
                    <div className="font-medium">Missing required fields</div>
                    <div className="mt-1">
                      Provide a constant value below for any required field your CSV does not contain.
                    </div>
                    <ul className="mt-1 list-disc pl-5">
                      {missingRequired.map((f) => (
                        <li key={f.key}>{f.label}</li>
                      ))}
                    </ul>
                  </div>
                )}
                <div className="grid gap-3 sm:grid-cols-2">
                  {fields.map((f) => (
                    <div key={f.key} className="flex items-center gap-2">
                      <label className="w-40 text-sm">{f.label}{f.required ? ' *' : ''}</label>
                      <Select value={getColumnRuleValue(f.key)} onValueChange={(v) => setColumnRule(f.key, v)}>
                        <SelectTrigger className="flex-1">
                          <SelectValue placeholder="(Ignore)" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">(Ignore)</SelectItem>
                          {sourceSchema.columns.map((c) => (
                            <SelectItem key={c.key} value={c.key}>{c.label}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <input
                        placeholder="Constant"
                        className="w-32 rounded-md border p-2 text-xs"
                        value={getConstantRuleValue(f.key)}
                        onChange={(e) => setConstantRule(f.key, e.target.value)}
                      />
                      <Button variant="ghost" size="sm" onClick={() => addPlaceholderRule(f.key, 'derived')}>+ Derived</Button>
                      <Button variant="ghost" size="sm" onClick={() => addPlaceholderRule(f.key, 'metadata')}>+ Meta</Button>
                    </div>
                  ))}
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-500">Target table: {target}</span>
                  <Button onClick={handleSubmit} disabled={submitting || issues.length > 0 || csvRows.length === 0}>
                    {submitting ? 'Uploading...' : 'Upload to Database'}
                  </Button>
                </div>
              </>
            )}
          </div>
        )}

        {/* Step 4: Validate & Preview */}
        {step === 3 && (
          <div className="space-y-3">
            <div className="rounded-md border p-2 text-sm">
              <div className="font-medium">Validation</div>
              {issues.length === 0 ? (
                <div className="text-green-600">Mapping looks good.</div>
              ) : (
                <ul className="list-disc pl-5 text-red-600">
                  {issues.map((i) => (
                    <li key={i.field}>{i.message}</li>
                  ))}
                </ul>
              )}
            </div>
            <div className="rounded-md border p-2">
              <div className="text-sm font-medium">Preview (first 5 rows)</div>
              <pre className="max-h-64 overflow-auto rounded bg-muted p-2 text-xs">{JSON.stringify(preview, null, 2)}</pre>
            </div>
            <div className="text-xs text-gray-500">
              Note: Import action, FK wiring, and templates are deferred to next phases.
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-500">Target table: {target}</span>
              <Button onClick={handleSubmit} disabled={submitting || issues.length > 0 || csvRows.length === 0}>
                {submitting ? 'Uploading...' : 'Upload to Database'}
              </Button>
            </div>
          </div>
        )}

        {/* Navigation */}
        <div className="flex items-center justify-between">
          <Button variant="secondary" onClick={prev} disabled={step === 0}>Back</Button>
          <div className="flex items-center gap-2">
            {error && <span className="text-xs text-red-600">{error}</span>}
            <Button onClick={next} disabled={(step === 1 && csvRows.length === 0) || (step === 2 && issues.length > 0)}>
              {step < STEPS.length - 1 ? 'Next' : 'Done'}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
