import { useState, useCallback } from 'react';
import { useSpeechRecognition } from 'react-speech-recognition';
import { Button } from '../ui/button';
import { Textarea } from '../ui/textarea';
import { useToast } from '../ui/use-toast';
import { validateImportData } from './validation';
import { processVoiceInput } from '../../lib/ai';
import { supabase } from '../../lib/supabase';
import type { ValidationError } from './types';

const MAX_RETRIES = 3;
const INITIAL_DELAY = 1000;

function ValidationErrors({ errors }: { errors: ValidationError[] }) {
  return (
    <div className="space-y-2">
      {errors.map((error, i) => (
        <div key={i} className="text-sm text-red-500">
          Row {error.row}: {error.message}
        </div>
      ))}
    </div>
  );
}

async function withTransaction(callback: () => Promise<void>) {
  const { error } = await supabase.rpc('begin');
  if (error) throw error;

  try {
    await callback();
    await supabase.rpc('commit');
  } catch (err) {
    await supabase.rpc('rollback');
    throw err;
  }
}

export function VoiceInput() {
  const [text, setText] = useState('');
  const [loading, setLoading] = useState(false);
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([]);
  const { toast } = useToast();
  const { transcript, listening, startListening, stopListening } = useSpeechRecognition();

  const handleSubmit = useCallback(async () => {
    setLoading(true);
    setValidationErrors([]);
    
    let retries = 0;
    let delay = INITIAL_DELAY;

    while (retries < MAX_RETRIES) {
      try {
        // Process voice input with OpenAI
        const processedData = await processVoiceInput(transcript);
        
        // Validate against database schema
        const validationErrors = await validateImportData(processedData);
        
        if (validationErrors.length > 0) {
          setValidationErrors(validationErrors);
          throw new Error('Validation failed');
        }

        // Transform data for events table
        const eventData = processedData.map((item, index) => ({
          event_type: 'receiving',
          product_id: item.sku,
          quantity: item.stock,
          unit: item.unit,
          unit_price: item.price || 0,
          unit_cost: item.cost || 0,
          total_amount: (item.stock || 0) * (item.price || 0),
          notes: `Voice import: ${item.description}`,
          metadata: {
            source: 'voice',
            raw_input: transcript,
            row_number: index + 1
          }
        }));

        // Execute in transaction
        await withTransaction(async () => {
          const { error } = await supabase
            .from('events')
            .insert(eventData);
          if (error) throw error;
        });

        toast({
          title: 'Success',
          description: 'Data imported successfully',
        });
        return; // Success - exit retry loop
      } catch (error) {
        if (retries === MAX_RETRIES - 1) {
          toast({
            title: 'Error',
            description: error.message,
            variant: 'destructive',
          });
          return;
        }
        
        // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, delay));
        delay *= 2;
        retries++;
      }
    }
  }, [transcript, toast]);

  return (
    <div className="space-y-4">
      <Textarea
        value={listening ? transcript : text}
        onChange={(e) => setText(e.target.value)}
        placeholder="Speak or type your import data..."
      />
      {validationErrors.length > 0 && (
        <ValidationErrors errors={validationErrors} />
      )}
      <div className="flex gap-2">
        <Button
          variant={listening ? 'destructive' : 'default'}
          onClick={listening ? stopListening : startListening}
          disabled={loading}
        >
          {listening ? 'Stop Recording' : 'Start Recording'}
        </Button>
        <Button onClick={handleSubmit} disabled={loading || !text}>
          {loading ? 'Processing...' : 'Submit'}
        </Button>
      </div>
    </div>
  );
}
