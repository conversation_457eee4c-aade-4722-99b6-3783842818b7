import type { ImportRow, ValidationError } from './types';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: import.meta.env.VITE_OPENAI_API_KEY,
  dangerouslyAllowBrowser: true
});

export async function validateWithAI(data: ImportRow[]): Promise<ValidationError[]> {
  const errors: ValidationError[] = [];
  
  try {
    // Validate voice transcription quality
    for (const row of data) {
      if (row.source === 'voice') {
        const transcriptionErrors = await validateTranscriptionQuality(row);
        errors.push(...transcriptionErrors);
      }
    }

    // Validate data consistency across rows
    const consistencyErrors = await validateDataConsistency(data);
    errors.push(...consistencyErrors);

    // Validate business rules and anomalies
    const anomalyErrors = await validateBusinessRules(data);
    errors.push(...anomalyErrors);

  } catch (error) {
    console.error('AI validation error:', error);
    errors.push({ row: 0, message: 'AI validation service unavailable' });
  }

  return errors;
}

async function validateTranscriptionQuality(row: ImportRow): Promise<ValidationError[]> {
  const errors: ValidationError[] = [];
  const prompt = `Analyze this seafood inventory transcription for quality issues:
  
  Product: ${row.name}
  Details: ${row.description || ''}
  
  Identify any:
  1. Unclear or ambiguous terms
  2. Incomplete information
  3. Potential misinterpretations
  4. Formatting issues`;

  try {
    const response = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.2,
    });

    const analysis = response.choices[0].message.content;
    if (analysis?.toLowerCase().includes('issues detected')) {
      errors.push({
        row: row.rowNumber || 0,
        message: `Transcription quality issues detected: ${analysis}`
      });
    }
  } catch (error) {
    console.error('Transcription validation error:', error);
  }

  return errors;
}

async function validateDataConsistency(data: ImportRow[]): Promise<ValidationError[]> {
  const errors: ValidationError[] = [];
  const prompt = `Analyze this seafood inventory data for consistency issues:
  
  ${JSON.stringify(data, null, 2)}
  
  Identify any:
  1. Inconsistent units of measurement
  2. Missing required fields
  3. Data format inconsistencies
  4. Logical inconsistencies between rows`;

  try {
    const response = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.2,
    });

    const analysis = response.choices[0].message.content;
    if (analysis?.toLowerCase().includes('inconsistencies')) {
      errors.push({
        row: 0,
        message: `Data consistency issues detected: ${analysis}`
      });
    }
  } catch (error) {
    console.error('Consistency validation error:', error);
  }

  return errors;
}

async function validateBusinessRules(data: ImportRow[]): Promise<ValidationError[]> {
  const errors: ValidationError[] = [];
  const prompt = `Analyze this seafood inventory data for business rule violations:
  
  ${JSON.stringify(data, null, 2)}
  
  Check for:
  1. Unrealistic pricing (too high/low)
  2. Impossible stock quantities
  3. Invalid category assignments
  4. Expired products
  5. Regulatory compliance issues`;

  try {
    const response = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.2,
    });

    const analysis = response.choices[0].message.content;
    if (analysis?.toLowerCase().includes('violations')) {
      errors.push({
        row: 0,
        message: `Business rule violations detected: ${analysis}`
      });
    }
  } catch (error) {
    console.error('Business rule validation error:', error);
  }

  return errors;
}
