import { useEffect, useMemo, useState } from 'react';
import { supabase } from '../lib/supabase';
import Modal from './modals/Modal';
import HACCPEventForm from './forms/HACCPEventForm';

type InventoryEvent = {
  id: string;
  event_type: string;
  product_id: string | null;
  quantity: number | null;
  notes: string | null;
  created_at: string; // ISO
};

const EVENT_COLORS: Record<string, string> = {
  receiving: 'bg-green-100 text-green-800 border-green-200',
  disposal: 'bg-red-100 text-red-800 border-red-200',
  physical_count: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  sale: 'bg-blue-100 text-blue-800 border-blue-200',
  default: 'bg-gray-100 text-gray-800 border-gray-200',
};

function formatISODate(d: Date) {
  const tzOff = d.getTimezoneOffset() * 60000;
  return new Date(d.getTime() - tzOff).toISOString().slice(0, 10);
}

function startOfMonth(date: Date) {
  return new Date(date.getFullYear(), date.getMonth(), 1);
}
function endOfMonth(date: Date) {
  return new Date(date.getFullYear(), date.getMonth() + 1, 0);
}
function startOfWeek(date: Date) {
  const d = new Date(date);
  const day = d.getDay(); // 0 Sun - 6 Sat
  d.setDate(d.getDate() - day);
  d.setHours(0, 0, 0, 0);
  return d;
}
function endOfWeek(date: Date) {
  const d = new Date(date);
  const day = d.getDay(); // 0 Sun - 6 Sat
  d.setDate(d.getDate() + (6 - day));
  d.setHours(23, 59, 59, 999);
  return d;
}

export default function HACCPCalendar() {
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [events, setEvents] = useState<InventoryEvent[]>([]);
  const [selectedDate, setSelectedDate] = useState<string | null>(null); // YYYY-MM-DD
  const [activeTypes, setActiveTypes] = useState<Record<string, boolean>>({
    receiving: true,
    disposal: true,
    physical_count: true,
    sale: true,
  });
  const [isAddOpen, setIsAddOpen] = useState(false);
  const [dateForAdd, setDateForAdd] = useState<string | null>(null);
  const [reloadKey, setReloadKey] = useState(0);

  const monthStart = useMemo(() => startOfMonth(currentMonth), [currentMonth]);
  const monthEnd = useMemo(() => endOfMonth(currentMonth), [currentMonth]);
  const gridStart = useMemo(() => startOfWeek(monthStart), [monthStart]);
  const gridEnd = useMemo(() => endOfWeek(monthEnd), [monthEnd]);

  const days = useMemo(() => {
    const arr: Date[] = [];
    const cur = new Date(gridStart);
    while (cur <= gridEnd) {
      arr.push(new Date(cur));
      cur.setDate(cur.getDate() + 1);
    }
    return arr;
  }, [gridStart, gridEnd]);

  useEffect(() => {
    let cancelled = false;
    const load = async () => {
      setLoading(true);
      setError(null);
      try {
        const startISO = new Date(gridStart).toISOString();
        const endISO = new Date(gridEnd).toISOString();
        const { data, error } = await supabase
          .from('inventory_events')
          .select('id, event_type, product_id, quantity, notes, created_at')
          .gte('created_at', startISO)
          .lte('created_at', endISO)
          .order('created_at', { ascending: true });
        if (error) throw error;
        if (!cancelled) setEvents(data || []);
      } catch (e: unknown) {
        const msg = e instanceof Error ? e.message : 'Failed to load events';
        if (!cancelled) setError(msg);
      } finally {
        if (!cancelled) setLoading(false);
      }
    };
    load();
    return () => { cancelled = true; };
  }, [gridStart, gridEnd, reloadKey]);

  const typesInData = useMemo(() => {
    const set = new Set<string>();
    events.forEach(ev => set.add(ev.event_type));
    return Array.from(set);
  }, [events]);

  const eventsByDay = useMemo(() => {
    const map: Record<string, InventoryEvent[]> = {};
    for (const ev of events) {
      const enabled = Object.prototype.hasOwnProperty.call(activeTypes, ev.event_type)
        ? Boolean(activeTypes[ev.event_type as keyof typeof activeTypes])
        : true;
      if (!enabled) continue;
      const d = new Date(ev.created_at);
      const key = formatISODate(d);
      if (!map[key]) map[key] = [];
      map[key].push(ev);
    }
    return map;
  }, [events, activeTypes]);

  const todayKey = formatISODate(new Date());

  const goPrev = () => setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1));
  const goNext = () => setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1));
  const goToday = () => setCurrentMonth(new Date());

  const openAddForDay = (key: string) => {
    setSelectedDate(key);
    setDateForAdd(key);
    setIsAddOpen(true);
  };

  const handleAddSuccess = () => {
    setIsAddOpen(false);
    setDateForAdd(null);
    setReloadKey(k => k + 1);
  };

  const handleAddCancel = () => {
    setIsAddOpen(false);
    setDateForAdd(null);
  };

  return (
    <div className="bg-white rounded-lg shadow p-4 md:p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <button aria-label="Previous month" className="px-2 py-1 rounded border hover:bg-gray-50" onClick={goPrev}>&lt;</button>
          <button aria-label="Today" className="px-2 py-1 rounded border hover:bg-gray-50" onClick={goToday}>Today</button>
          <button aria-label="Next month" className="px-2 py-1 rounded border hover:bg-gray-50" onClick={goNext}>&gt;</button>
        </div>
        <h2 className="text-lg font-semibold text-gray-900">
          {currentMonth.toLocaleString(undefined, { month: 'long', year: 'numeric' })}
        </h2>
        <div className="flex items-center gap-2">
          {typesInData.length === 0 && <span className="text-xs text-gray-500">No events</span>}
          {['receiving','disposal','physical_count','sale', ...typesInData.filter(t => !['receiving','disposal','physical_count','sale'].includes(t))].map(t => (
            <label key={t} className="flex items-center gap-1 text-xs border rounded-full px-2 py-1 cursor-pointer select-none">
              <input
                type="checkbox"
                className="accent-blue-600"
                checked={activeTypes[t as keyof typeof activeTypes] ?? true}
                onChange={(e) => setActiveTypes(prev => ({ ...prev, [t]: e.target.checked }))}
              />
              <span className="capitalize">{t.replace('_',' ')}</span>
            </label>
          ))}
        </div>
      </div>

      {error && <div className="mb-3 text-sm text-red-700 bg-red-50 border border-red-200 rounded p-2">{error}</div>}
      {loading && !error && (
        <div className="mb-3 text-sm text-gray-600 bg-gray-50 border border-gray-200 rounded p-2">Loading events…</div>
      )}

      <div className="grid grid-cols-7 gap-2" aria-label="HACCP Events Calendar">
        {['Sun','Mon','Tue','Wed','Thu','Fri','Sat'].map(d => (
          <div key={d} className="text-xs font-medium text-gray-500 text-center py-1">{d}</div>
        ))}
        {days.map((d) => {
          const key = formatISODate(d);
          const inMonth = d.getMonth() === currentMonth.getMonth();
          const isToday = key === todayKey;
          const dayEvents = eventsByDay[key] || [];
          return (
            <button
              key={key}
              data-selected={selectedDate === key ? 'true' : 'false'}
              onClick={() => openAddForDay(key)}
              className={`min-h-[88px] rounded border p-1 text-left focus:outline-none focus:ring-2 focus:ring-blue-500 ${inMonth ? '' : 'bg-gray-50'} ${isToday ? 'border-blue-500' : 'border-gray-200'}`}
            >
              <div className="flex items-center justify-between mb-1">
                <span className={`text-xs ${inMonth ? 'text-gray-700' : 'text-gray-400'}`}>{d.getDate()}</span>
                {isToday && <span className="text-[10px] text-blue-600">Today</span>}
              </div>
              <div className="flex flex-wrap gap-1">
                {dayEvents.slice(0, 3).map(ev => (
                  <span
                    key={ev.id}
                    className={`border rounded px-1 py-0.5 text-[10px] ${EVENT_COLORS[ev.event_type] || EVENT_COLORS.default}`}
                    title={`${ev.event_type} • ${ev.quantity ?? ''}`.trim()}
                  >
                    {ev.event_type.replace('_',' ')}
                  </span>
                ))}
                {dayEvents.length > 3 && (
                  <span className="text-[10px] text-gray-600">+{dayEvents.length - 3} more</span>
                )}
              </div>
            </button>
          );
        })}
      </div>

      <div className="mt-4">
        {selectedDate ? (
          <>
            <h3 className="text-sm font-semibold text-gray-900 mb-2">Events on {selectedDate}</h3>
            <ul className="space-y-2 max-h-64 overflow-auto">
              {(eventsByDay[selectedDate] || []).map(ev => (
                <li key={ev.id} className="border rounded p-2">
                  <div className="flex items-center justify-between">
                    <span className={`inline-flex items-center gap-1 text-xs px-2 py-0.5 border rounded-full ${EVENT_COLORS[ev.event_type] || EVENT_COLORS.default}`}>
                      {ev.event_type.replace('_',' ')}
                    </span>
                    <span className="text-xs text-gray-500">{new Date(ev.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</span>
                  </div>
                  <div className="text-sm text-gray-800 mt-1">
                    Qty: {ev.quantity ?? '—'}
                  </div>
                  {ev.notes && <div className="text-xs text-gray-600 mt-1">{ev.notes}</div>}
                </li>
              ))}
              {(!eventsByDay[selectedDate] || eventsByDay[selectedDate].length === 0) && (
                <li className="text-sm text-gray-500">No events</li>
              )}
            </ul>
          </>
        ) : (
          <p className="text-sm text-gray-500">Select a day to view details.</p>
        )}
      </div>

      <Modal isOpen={isAddOpen} onClose={handleAddCancel} title={`Add Event – ${dateForAdd ?? ''}`}>
        <HACCPEventForm onSuccess={handleAddSuccess} onCancel={handleAddCancel} dateOverride={dateForAdd ?? undefined} />
      </Modal>
    </div>
  );
}
