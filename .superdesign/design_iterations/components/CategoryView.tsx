import React, { useState, useEffect } from 'react';
import type { Product } from '../types';
import { getProductImageUrl } from '../lib/api';

interface CategoryViewProps {
  category: { id: string; name: string };
  products: Product[];
  onProductSelect: (product: Product) => void;
}

function CategoryView({ category, products, onProductSelect }: CategoryViewProps) {
  const [imageErrors, setImageErrors] = useState<Record<string, boolean>>({});
  const [signedUrls, setSignedUrls] = useState<Record<string, string>>({});

  useEffect(() => {
    const handleImageError = (productId: string) => {
      setImageErrors(prev => ({ ...prev, [productId]: true }));
    };

    const loadSignedUrls = async () => {
      setSignedUrls({});
      
      const urlPromises = products
        .filter(product => product.images?.[0])
        .map(async product => {
          if (!product.id || !product.images?.[0]) return null;
          try {
            const signedUrl = await getProductImageUrl(product.images[0]);
            console.log(`Got signed URL for product ${product.id}:`, signedUrl);
            return { id: product.id, url: signedUrl };
          } catch (error) {
            console.error(`Error getting signed URL for product ${product.id}:`, error);
            if (product.id) {
              handleImageError(product.id);
            }
            return null;
          }
        });

      try {
        const results = await Promise.all(urlPromises);
        const newUrls = results.reduce((acc, result) => {
          if (result) {
            acc[result.id] = result.url;
          }
          return acc;
        }, {} as Record<string, string>);

        console.log('Setting new signed URLs:', newUrls);
        setSignedUrls(newUrls);
      } catch (error) {
        console.error('Error loading signed URLs:', error);
      }
    };

    loadSignedUrls();
  }, [products]);

  const handleImageError = (productId: string) => {
    setImageErrors(prev => ({ ...prev, [productId]: true }));
  };

  if (!products?.length) {
    return (
      <div className="flex-1 p-6">
        <h2 className="text-2xl font-bold mb-4">{category?.name}</h2>
        <div className="text-center py-12 bg-white rounded-lg shadow">
          <p className="text-gray-500">No products in this category.</p>
          <p className="text-gray-500 mt-2">Click "Add Product" to add a new product.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 p-6">
      <h2 className="text-2xl font-bold mb-4">{category?.name}</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {products.map((product) => (
          <div
            key={product.id}
            className="bg-white p-4 border rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => onProductSelect(product)}
          >
            <div className="relative pb-[75%] mb-4">
              {product.id && signedUrls[product.id] && !imageErrors[product.id] ? (
                <img
                  src={signedUrls[product.id]}
                  alt={product.name}
                  className="absolute inset-0 w-full h-full object-cover rounded-md"
                  onError={() => product.id && handleImageError(product.id)}
                />
              ) : (
                <div className="absolute inset-0 bg-gray-100 rounded-md flex items-center justify-center">
                  <span className="text-gray-400">No image</span>
                </div>
              )}
            </div>
            <h3 className="font-semibold text-gray-900">{product.name}</h3>
            <div className="mt-2 space-y-1">
              <p className="text-sm text-gray-600">
                Amount: {product.amount} {product.amount === 1 ? 'unit' : 'units'}
              </p>
              <p className="text-sm text-gray-600">
                Condition: {product.condition}
              </p>
              {product.supplier && (
                <p className="text-sm text-gray-600">
                  Supplier: {product.supplier}
                </p>
              )}
              {product.price !== null && product.price !== undefined && (
                <p className="mt-2 text-lg font-medium text-gray-900">
                  ${product.price.toFixed(2)}
                </p>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default CategoryView;