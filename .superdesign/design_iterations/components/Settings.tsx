import React from 'react';
import { User, <PERSON>, Database } from 'lucide-react';

const settings = [
  {
    category: 'Account',
    icon: User,
    items: [
      { name: 'Profile Information', description: 'Update your account details and preferences' },
      { name: 'Password', description: 'Change your password and security settings' },
      { name: 'Two-Factor Authentication', description: 'Add an extra layer of security' },
    ],
  },
  {
    category: 'Notifications',
    icon: Bell,
    items: [
      { name: 'Email Notifications', description: 'Configure email alert preferences' },
      { name: 'Stock Alerts', description: 'Set threshold for low stock warnings' },
      { name: 'Price Updates', description: 'Get notified about market price changes' },
    ],
  },
  {
    category: 'System',
    icon: Database,
    items: [
      { name: 'Data Export', description: 'Export inventory and sales data' },
      { name: 'Backup Settings', description: 'Configure automatic backups' },
      { name: 'Print Settings', description: 'Configure label and report printing' },
    ],
  },
];

export default function Settings() {
  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold text-gray-900">Settings</h1>

      <div className="space-y-6">
        {settings.map((section) => (
          <div key={section.category} className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center gap-3">
                <section.icon className="w-5 h-5 text-gray-500" />
                <h2 className="text-lg font-semibold text-gray-900">{section.category}</h2>
              </div>
            </div>
            <div className="divide-y divide-gray-200">
              {section.items.map((item) => (
                <div key={item.name} className="px-6 py-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-sm font-medium text-gray-900">{item.name}</h3>
                      <p className="text-sm text-gray-500 mt-1">{item.description}</p>
                    </div>
                    <button className="text-sm text-blue-600 hover:text-blue-700">
                      Configure
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}