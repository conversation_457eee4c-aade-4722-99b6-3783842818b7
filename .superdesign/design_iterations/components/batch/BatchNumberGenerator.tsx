import { useCallback, useMemo, useState } from 'react';
import { supabase } from '../../lib/supabase';
import { useEffect } from 'react';

export type BatchPattern = 'YYYYMMDD-PROD-SEQ2' | 'YYDOY-PROD-SEQ2' | 'YYYYMMDD-VEND-PROD-SEQ2';

interface BatchNumberGeneratorProps {
  date?: string; // YYYY-MM-DD
  productId?: string;
  vendorId?: string;
  productNameById?: Record<string, string>;
  vendorNameById?: Record<string, string>;
  pattern?: BatchPattern;
  onGenerate: (value: string) => void;
  disabled?: boolean;
}

// Minimal row type for selecting only `metadata` from `inventory_events`
type MetaRow = { metadata?: Record<string, unknown> | null };
type LotRow = { tlc?: string | null };

function pad(num: number, size: number) {
  let s = String(num);
  while (s.length < size) s = '0' + s;
  return s;
}

function toYYYYMMDD(dateStr?: string) {
  if (!dateStr) return '';
  return dateStr.replace(/-/g, '');
}

function toYYDOY(dateStr?: string) {
  if (!dateStr) return '';
  const d = new Date(dateStr + 'T12:00:00');
  const year = String(d.getFullYear()).slice(-2);
  const start = new Date(d.getFullYear(), 0, 0);
  const diff = d.getTime() - start.getTime();
  const oneDay = 1000 * 60 * 60 * 24;
  const doy = Math.floor(diff / oneDay);
  return `${year}${pad(doy, 3)}`;
}

function codeFromName(name?: string, len = 4) {
  if (!name) return '';
  const alnum = name.replace(/[^a-zA-Z0-9]/g, '');
  return alnum.toUpperCase().slice(0, len);
}

export default function BatchNumberGenerator({
  date,
  productId,
  vendorId,
  productNameById,
  vendorNameById,
  pattern = 'YYYYMMDD-PROD-SEQ2',
  onGenerate,
  disabled,
}: BatchNumberGeneratorProps) {
  const [currentPattern, setCurrentPattern] = useState<BatchPattern>(pattern);
  const [loading, setLoading] = useState(false);
  const [preview, setPreview] = useState('');
  const [error, setError] = useState<string | null>(null);

  const productCode = useMemo(() => codeFromName(productNameById?.[productId ?? '']), [productId, productNameById]);
  const vendorCode = useMemo(() => codeFromName(vendorNameById?.[vendorId ?? ''], 3), [vendorId, vendorNameById]);

  // Clear stale preview whenever inputs affecting the pattern change
  useEffect(() => {
    setPreview('');
  }, [date, productId, vendorId, currentPattern]);

  const buildBase = useCallback((seq: number) => {
    const seq2 = pad(seq, 2);
    switch (currentPattern) {
      case 'YYDOY-PROD-SEQ2':
        return `${toYYDOY(date)}-${productCode}-${seq2}`;
      case 'YYYYMMDD-VEND-PROD-SEQ2':
        return `${toYYYYMMDD(date)}-${vendorCode}-${productCode}-${seq2}`;
      case 'YYYYMMDD-PROD-SEQ2':
      default:
        return `${toYYYYMMDD(date)}-${productCode}-${seq2}`;
    }
  }, [currentPattern, date, productCode, vendorCode]);

  const buildPrefix = useCallback(() => {
    const result = (() => {
      switch (currentPattern) {
        case 'YYDOY-PROD-SEQ2':
          return `${toYYDOY(date)}-${productCode}-`;
        case 'YYYYMMDD-VEND-PROD-SEQ2':
          return `${toYYYYMMDD(date)}-${vendorCode}-${productCode}-`;
        case 'YYYYMMDD-PROD-SEQ2':
        default:
          return `${toYYYYMMDD(date)}-${productCode}-`;
      }
    })();
    console.log('buildPrefix:', { currentPattern, date, productCode, vendorCode, result });
    return result;
  }, [currentPattern, date, productCode, vendorCode]);

  const estimateNextSeq = useCallback(async (): Promise<number> => {
    if (!date) return 1;
    const prefix = buildPrefix();
    if (!prefix) return 1;

    // Query existing TLC and batch_number values that start with the prefix
    const [tlcRes, batchRes, lotsRes] = await Promise.all([
      supabase.from('inventory_events').select('metadata').ilike('metadata->>tlc', `${prefix}%`).limit(200),
      supabase.from('inventory_events').select('metadata').ilike('metadata->>batch_number', `${prefix}%`).limit(200),
      supabase.from('lots').select('tlc').ilike('tlc', `${prefix}%`).limit(200),
    ]);

    const parseMax = (rows?: MetaRow[], key?: 'tlc' | 'batch_number') => {
      let max = 0;
      if (!rows) return 0;
      for (const r of rows) {
        const v = (r?.metadata as Record<string, unknown> | undefined | null)?.[key ?? 'tlc'];
        if (typeof v === 'string' && v.startsWith(prefix)) {
          const num = parseInt(v.slice(prefix.length), 10);
          if (!Number.isNaN(num) && num > max) max = num;
        }
      }
      return max;
    };

    if (tlcRes.error && batchRes.error && lotsRes.error) {
      // Fallback to old heuristic: count receiving events on date
      const start = new Date(date + 'T00:00:00');
      const end = new Date(date + 'T23:59:59.999');
      const { count } = await supabase
        .from('inventory_events')
        .select('id', { count: 'exact', head: true })
        .eq('event_type', 'receiving')
        .gte('created_at', start.toISOString())
        .lte('created_at', end.toISOString());
      return (count ?? 0) + 1;
    }

    const tlcRows: MetaRow[] = (tlcRes.error ? [] : ((tlcRes.data as unknown) as MetaRow[])) ?? [];
    const batchRows: MetaRow[] = (batchRes.error ? [] : ((batchRes.data as unknown) as MetaRow[])) ?? [];
    const lotRows: LotRow[] = (lotsRes.error ? [] : ((lotsRes.data as unknown) as LotRow[])) ?? [];

    // Parse max from lots (tlc string)
    let lotMax = 0;
    for (const r of lotRows) {
      const v = r?.tlc ?? undefined;
      if (typeof v === 'string' && v.startsWith(prefix)) {
        const num = parseInt(v.slice(prefix.length), 10);
        if (!Number.isNaN(num) && num > lotMax) lotMax = num;
      }
    }

    const maxSeq = Math.max(parseMax(tlcRows, 'tlc'), parseMax(batchRows, 'batch_number'), lotMax);
    console.log('estimateNextSeq:', { prefix, maxSeq, tlcCount: tlcRows.length, batchCount: batchRows.length, lotCount: lotRows.length });

    // Ensure we return at least 1, and cap at 99 for SEQ2 patterns
    const nextSeq = Math.max(1, maxSeq + 1);
    return Math.min(nextSeq, 99);
  }, [date, buildPrefix]);

  const checkUnique = useCallback(async (candidate: string) => {
    // Use JSONB containment which is well supported in PostgREST/Supabase
    const byTlc = await supabase
      .from('inventory_events')
      .select('id')
      .contains('metadata', { tlc: candidate })
      .limit(1);
    if (!byTlc.error && byTlc.data && byTlc.data.length > 0) return false;

    const byBatch = await supabase
      .from('inventory_events')
      .select('id')
      .contains('metadata', { batch_number: candidate })
      .limit(1);
    if (!byBatch.error && byBatch.data && byBatch.data.length > 0) return false;

    // Also ensure uniqueness across lots table (TLCs)
    const byLots = await supabase
      .from('lots')
      .select('id')
      .eq('tlc', candidate)
      .limit(1);
    if (!byLots.error && byLots.data && byLots.data.length > 0) return false;

    // If checks error out, default to allowing generation; creation path still stores TLC to prevent future duplicates
    return true;
  }, []);

  const doGenerate = useCallback(async () => {
    setError(null);
    // Basic preconditions to avoid generating invalid/ambiguous patterns
    if (!date) {
      setError('Please select a date.');
      return;
    }
    if (!productCode) {
      setError('Please select a product.');
      return;
    }
    if (currentPattern === 'YYYYMMDD-VEND-PROD-SEQ2' && !vendorCode) {
      setError('Please select a vendor for this pattern.');
      return;
    }
    setLoading(true);
    try {
      const seq = await estimateNextSeq();
      console.log('Generated sequence:', seq, 'for date:', date, 'product:', productCode, 'vendor:', vendorCode);
      
      // Guard against exceeding 2 digits for SEQ2 patterns
      if (seq >= 100) {
        setError('Sequence exceeds 2 digits for this pattern/date. Consider changing date or pattern.');
        return;
      }
      let n = seq;
      let candidate = buildBase(n);
      console.log('Built candidate:', candidate, 'from sequence:', n);
      
      let unique = await checkUnique(candidate);
      // Try incrementing until a unique candidate is found or we hit 99
      while (!unique && n < 99) {
        n += 1;
        candidate = buildBase(n);
        unique = await checkUnique(candidate);
      }
      if (!unique) {
        setError('Unable to find a unique batch for this pattern/date. Try a different date or pattern.');
        return;
      }
      setPreview(candidate);
    } catch (e: unknown) {
      console.error('Batch generation error:', e);
      setError(e instanceof Error ? e.message : 'Failed to generate batch number');
    } finally {
      setLoading(false);
    }
  }, [estimateNextSeq, buildBase, checkUnique, date, productCode, vendorCode, currentPattern]);

  return (
    <div className="mt-2 rounded-md border border-gray-200 p-3 bg-gray-50">
      <div className="flex flex-col gap-2">
        <div className="flex items-center gap-2">
          <label htmlFor="batchPattern" className="text-sm font-medium text-gray-700">Pattern</label>
          <select
            id="batchPattern"
            value={currentPattern}
            onChange={(e) => setCurrentPattern(e.target.value as BatchPattern)}
            className="rounded-md border-gray-300 text-sm"
            disabled={disabled}
          >
            <option value="YYYYMMDD-PROD-SEQ2">YYYYMMDD-PROD-SEQ</option>
            <option value="YYDOY-PROD-SEQ2">YYDOY-PROD-SEQ</option>
            <option value="YYYYMMDD-VEND-PROD-SEQ2">YYYYMMDD-VEND-PROD-SEQ</option>
          </select>
          <button
            type="button"
            onClick={doGenerate}
            disabled={disabled || loading}
            className="ml-auto rounded-md bg-blue-600 px-3 py-1.5 text-white text-sm hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Generating…' : 'Generate'}
          </button>
        </div>
        {error && <p className="text-sm text-red-600">{error}</p>}
        <div className="flex items-center gap-2">
          <input
            type="text"
            readOnly
            value={preview}
            placeholder="Generated batch will appear here"
            className="flex-1 rounded-md border-gray-300 text-sm"
          />
          <button
            type="button"
            onClick={() => preview && onGenerate(preview)}
            disabled={!preview || disabled}
            className="rounded-md bg-gray-800 px-3 py-1.5 text-white text-sm hover:bg-gray-900 disabled:opacity-50"
          >
            Use
          </button>
        </div>
      </div>
    </div>
  );
}
