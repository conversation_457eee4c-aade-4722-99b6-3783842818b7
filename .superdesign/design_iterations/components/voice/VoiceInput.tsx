import React, { useEffect, useState } from 'react';
import SpeechR<PERSON>ognition, { useSpeechRecognition } from 'react-speech-recognition';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>2, AlertTriangle } from 'lucide-react';
import { format } from 'date-fns';
import { processVoiceInput } from '../../lib/ai';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../ui/select';
import { getProductCategories } from '../../lib/setupDatabase';

export interface TranscriptionData {
  product?: string;
  quantity?: number;
  category?: string;
  vendor?: string;
  timestamp: string;
}

interface VoiceInputProps {
  onTranscriptionComplete: (data: TranscriptionData) => void;
  onError: (error: string) => void;
}

export default function VoiceInput({ onTranscriptionComplete, onError }: VoiceInputProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [isUsingFallback, setIsUsingFallback] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [categories, setCategories] = useState<Array<{value: string, label: string, description?: string}>>([]);
  const [retryCount, setRetryCount] = useState(0);
  const MAX_RETRIES = 3;
  const {
    transcript,
    listening,
    resetTranscript,
    browserSupportsSpeechRecognition
  } = useSpeechRecognition();

  useEffect(() => {
    if (!browserSupportsSpeechRecognition) {
      onError('Browser does not support speech recognition.');
    }
  }, [browserSupportsSpeechRecognition, onError]);

  // Cleanup speech recognition on unmount
  useEffect(() => {
    return () => {
      SpeechRecognition.stopListening();
      resetTranscript();
    };
  }, [resetTranscript]);

  // Fetch categories with retry mechanism
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const { data, error } = await getProductCategories();
        if (error) throw error;
        if (data) {
          setCategories(data);
          setRetryCount(0); // Reset retry count on success
        }
      } catch {
        if (retryCount < MAX_RETRIES) {
          setTimeout(() => {
            setRetryCount(prev => prev + 1);
          }, Math.pow(2, retryCount) * 1000); // Exponential backoff
        } else {
          onError('Failed to load product categories after multiple attempts');
        }
      }
    };
    fetchCategories();
  }, [onError, retryCount]);

  const handleStartListening = () => {
    resetTranscript();
    setIsUsingFallback(false);
    SpeechRecognition.startListening({ continuous: true });
  };

  const handleStopListening = async () => {
    SpeechRecognition.stopListening();
    if (transcript) {
      setIsProcessing(true);
      try {
        const processedData = await processVoiceInput(transcript);
        if (!processedData.product && !processedData.quantity) {
          setIsUsingFallback(true);
        }
        const timestamp = format(new Date(), 'yyyy-MM-dd HH:mm');
        
        // Include selected category in transcription data
        const categoryName = categories.find(cat => cat.value === selectedCategory)?.label;
        onTranscriptionComplete({
          ...processedData,
          category: categoryName || processedData.category, // Use selected category or fallback to processed category
          timestamp
        });
      } catch (error) {
        setIsUsingFallback(true);
        onError(error instanceof Error ? error.message : 'Failed to process voice input');
      } finally {
        setIsProcessing(false);
        resetTranscript();
      }
    }
  };

  return (
    <div className="space-y-4">
      <div className="grid gap-4">
        <Select onValueChange={setSelectedCategory} value={selectedCategory}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select category" />
          </SelectTrigger>
          <SelectContent>
            {categories.map((category) => (
              <SelectItem key={category.value} value={category.value}>
                {category.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {!selectedCategory && (
          <p className="text-sm text-yellow-600">Please select a category before recording</p>
        )}
      </div>
      <div className="flex items-center gap-4">
        <button
          onClick={listening ? handleStopListening : handleStartListening}
          disabled={isProcessing || !selectedCategory}
          className={`flex items-center gap-2 px-4 py-2 rounded-lg ${
            listening
              ? 'bg-red-600 hover:bg-red-700'
              : 'bg-blue-600 hover:bg-blue-700'
          } text-white transition-colors`}
        >
          {isProcessing ? (
            <Loader2 className="w-5 h-5 animate-spin" />
          ) : listening ? (
            <MicOff className="w-5 h-5" />
          ) : (
            <Mic className="w-5 h-5" />
          )}
          {listening ? 'Stop Recording' : 'Start Recording'}
        </button>
        {listening && (
          <span className="text-sm text-gray-600">
            Listening...
          </span>
        )}
      </div>

      {isUsingFallback && (
        <div className="flex items-center gap-2 text-yellow-700 bg-yellow-50 px-4 py-2 rounded-lg">
          <AlertTriangle className="w-5 h-5" />
          <span className="text-sm">Using basic voice processing due to AI service limitations.</span>
        </div>
      )}

      {transcript && (
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-gray-700 mb-2">Transcript:</h3>
          <p className="text-gray-600">{transcript}</p>
        </div>
      )}

      <div className="text-sm text-gray-600">
        <p className="font-medium mb-2">Voice Command Examples:</p>
        <ul className="list-disc list-inside space-y-1">
          <li>"Received 50 lbs of Atlantic Salmon from Ocean Fresh Ltd"</li>
          <li>"New shipment of Tiger Prawns at $25 per lb"</li>
          <li>"Add 100 pounds of Tuna from Global Fish"</li>
        </ul>
        <div className="mt-4 bg-blue-50 p-4 rounded-lg">
          <p className="font-medium mb-2">Available Categories:</p>
          <ul className="list-disc list-inside space-y-1">
            {categories.map((cat) => (
              <li key={cat.value} className="text-sm">
                {cat.label} - {cat.description}
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
}
