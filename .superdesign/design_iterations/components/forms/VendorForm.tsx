import { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { vendorSchema } from '../../lib/validation';
import { createVendor, updateVendor } from '../../lib/api';
import type { Vendor } from '../../types';
import { supabase } from '../../lib/supabase';
import Modal from '../modals/Modal';
import ProductForm from './ProductForm';

interface VendorFormProps {
  vendor?: Vendor;
  onSuccess: () => void;
  onCancel: () => void;
}

type ApiResult<T> = { success: true; data: T } | { success: false; error: { message: string } };

export default function VendorForm({ vendor, onSuccess, onCancel }: VendorFormProps) {
  const [products, setProducts] = useState<Array<{ id: string; name: string; category?: string | null }>>([]);
  const [selectedProductIds, setSelectedProductIds] = useState<string[]>([]);
  const [categoryOptions, setCategoryOptions] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [isProductModalOpen, setIsProductModalOpen] = useState(false);

  const { register, handleSubmit, formState: { errors, isSubmitting } } = useForm<Vendor>({
    resolver: zodResolver(vendorSchema),
    defaultValues: vendor || {
      status: 'active',
    }
  });

  // Derived map for quick product lookup
  const productById = useMemo(() => {
    const map = new Map<string, { id: string; name: string; category?: string | null }>();
    products.forEach(p => map.set(p.id, p));
    return map;
  }, [products]);

  // Load products and existing associations
  useEffect(() => {
    const load = async () => {
      type ProductRow = { id: string; name: string; category?: string | null; supplier_id?: string | null };
      const { data: prods, error } = await supabase
        .from('Products')
        .select('id, name, category, supplier_id')
        .order('name', { ascending: true });
      if (!error && prods) {
        const prodsRows = (prods ?? []) as ProductRow[];
        setProducts(prodsRows);
        // Build category options from product_categories if available, else from products
        try {
          type CategoryRow = { name: string };
          const { data: pcats, error: pcErr } = await supabase
            .from('product_categories')
            .select('name')
            .order('name', { ascending: true });
          if (!pcErr && pcats && pcats.length) {
            const catRows = (pcats ?? []) as CategoryRow[];
            const names = catRows
              .map((c) => c.name)
              .filter((n): n is string => typeof n === 'string' && n.length > 0);
            setCategoryOptions(Array.from(new Set(names)));
          } else {
            const cats = prodsRows
              .map((p) => p.category)
              .filter((c): c is string => typeof c === 'string' && c.length > 0);
            setCategoryOptions(Array.from(new Set(cats)));
          }
        } catch {
          const cats = prodsRows
            .map((p) => p.category)
            .filter((c): c is string => typeof c === 'string' && c.length > 0);
          setCategoryOptions(Array.from(new Set(cats)));
        }

        // Preselect products already associated with this vendor
        if (vendor?.id) {
          const assigned = prodsRows
            .filter((p) => p.supplier_id === vendor.id)
            .map((p) => p.id);
          setSelectedProductIds(assigned);
        }
      }
    };
    load();
  }, [vendor?.id]);

  // Auto-fill category when selecting a single product that has a category and category not chosen yet
  useEffect(() => {
    if (!selectedCategory && selectedProductIds.length === 1) {
      const prod = productById.get(selectedProductIds[0]);
      if (prod?.category) {
        setSelectedCategory(prod.category);
      }
    }
  }, [selectedProductIds, selectedCategory, productById]);

  const onSubmit = async (data: Partial<Vendor>) => {
    try {
      // Merge selected category into metadata to avoid schema/DB changes
      const payload: Partial<Vendor> = {
        ...data,
        metadata: {
          ...(data?.metadata || {}),
          category: selectedCategory || (data?.metadata?.category ?? undefined),
        },
      };

      let savedVendorId = vendor?.id as string | undefined;
      if (vendor?.id) {
        const result = await updateVendor(vendor.id, payload) as ApiResult<{ id: string }>;
        if (!result.success) throw new Error(result.error.message || 'Failed to update vendor');
        savedVendorId = result.data.id ?? vendor.id;
      } else {
        const result = await createVendor(payload) as ApiResult<{ id: string }>;
        if (!result.success) throw new Error(result.error.message || 'Failed to create vendor');
        savedVendorId = result.data.id;
      }

      // Associate selected products to this vendor by setting supplier_id
      if (savedVendorId) {
        // Fetch current associations for diffing
        const { data: currentProds } = await supabase
          .from('Products')
          .select('id, supplier_id')
          .eq('supplier_id', savedVendorId);
        const currentRows = (currentProds ?? []) as { id: string; supplier_id: string | null }[];
        const currentlyAssigned = new Set<string>(currentRows.map((p) => p.id));

        const toAdd = selectedProductIds.filter(id => !currentlyAssigned.has(id));
        const toRemove = Array.from(currentlyAssigned).filter(id => !selectedProductIds.includes(id));

        if (toAdd.length) {
          await supabase
            .from('Products')
            .update({ supplier_id: savedVendorId })
            .in('id', toAdd);
        }
        if (toRemove.length) {
          await supabase
            .from('Products')
            .update({ supplier_id: null })
            .in('id', toRemove);
        }
      }

      onSuccess();
    } catch (error) {
      console.error('Error saving vendor:', error);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700">Name</label>
          <input
            type="text"
            {...register('name')}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-600">{errors.name.message as string}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Contact Name</label>
          <input
            type="text"
            {...register('contactName')}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          />
          {errors.contactName && (
            <p className="mt-1 text-sm text-red-600">{errors.contactName.message as string}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Email</label>
          <input
            type="email"
            {...register('email')}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          />
          {errors.email && (
            <p className="mt-1 text-sm text-red-600">{errors.email.message as string}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Phone</label>
          <input
            type="tel"
            {...register('phone')}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          />
          {errors.phone && (
            <p className="mt-1 text-sm text-red-600">{errors.phone.message as string}</p>
          )}
        </div>

        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700">Address</label>
          <input
            type="text"
            {...register('address')}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          />
          {errors.address && (
            <p className="mt-1 text-sm text-red-600">{errors.address.message as string}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Status</label>
          <select
            {...register('status')}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          >
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Payment Terms</label>
          <input
            type="text"
            {...register('paymentTerms')}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Credit Limit</label>
          <input
            type="number"
            {...register('creditLimit', { setValueAs: (v) => v === '' || v == null ? undefined : Number(v) })}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            placeholder="Optional"
          />
        </div>

        {/* Products association */}
        <div className="md:col-span-2">
          <div className="flex items-center justify-between">
            <label htmlFor="vendor-products" className="block text-sm font-medium text-gray-700">Products</label>
            <button
              type="button"
              onClick={() => setIsProductModalOpen(true)}
              className="text-sm text-blue-600 hover:text-blue-700"
            >
              Add Product
            </button>
          </div>
          {products.length ? (
            <select
              id="vendor-products"
              multiple
              value={selectedProductIds}
              onChange={(e) => {
                const ids = Array.from(e.target.selectedOptions).map(o => o.value);
                setSelectedProductIds(ids);
              }}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 min-h-[120px]"
            >
              {products.map(p => (
                <option key={p.id} value={p.id}>
                  {p.name}{p.category ? ` — ${p.category}` : ''}
                </option>
              ))}
            </select>
          ) : (
            <div className="mt-1 text-sm text-gray-600">No products found. Click "Add Product" to create one.</div>
          )}
        </div>

        {/* Category dropdown (auto-fills from selected product) */}
        <div>
          <label htmlFor="vendor-category" className="block text-sm font-medium text-gray-700">Category</label>
          <select
            id="vendor-category"
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          >
            <option value="">Select a category</option>
            {categoryOptions.map(cat => (
              <option key={cat} value={cat}>{cat}</option>
            ))}
          </select>
        </div>
      </div>

      

    <div className="flex justify-end gap-4">
      <button
        type="button"
        onClick={onCancel}
        className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        Cancel
      </button>
      <button
        type="submit"
        disabled={isSubmitting}
        className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
      >
        {isSubmitting ? 'Saving...' : vendor ? 'Update Vendor' : 'Create Vendor'}
      </button>
    </div>

    {/* Inline Product Modal */}
    <Modal
      isOpen={isProductModalOpen}
      onClose={() => setIsProductModalOpen(false)}
      title="Add Product"
    >
      <ProductForm
        onCancel={() => setIsProductModalOpen(false)}
        onSuccess={async () => {
          setIsProductModalOpen(false);
          // Reload product list after adding
          type ProductRow = { id: string; name: string; category?: string | null; supplier_id?: string | null };
          const { data: prods } = await supabase
            .from('Products')
            .select('id, name, category, supplier_id')
            .order('name', { ascending: true });
          if (prods) {
            const rows = (prods ?? []) as ProductRow[];
            setProducts(rows);
            // update category options as well
            const cats = rows
              .map((p) => p.category)
              .filter((c): c is string => typeof c === 'string' && c.length > 0);
            setCategoryOptions(prev => Array.from(new Set([
              ...prev,
              ...cats
            ])));
          }
        }}
      />
    </Modal>
  </form>
);
}