import { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { supabase } from '../../lib/supabase';
import { createReceivingWithLot } from '../../lib/api';
import BatchNumberGenerator from '../batch/BatchNumberGenerator';

// Event type options mapped to internal values used across the app
const EVENT_OPTIONS = [
  { label: 'Receiving', value: 'receiving' },
  { label: 'Disposal', value: 'disposal' },
  { label: 'Physical Count', value: 'physical_count' },
  { label: 'Sales', value: 'sale' },
] as const;

const UNIT_OPTIONS = [
  { label: 'Pounds (lbs)', value: 'lbs' },
  { label: 'Kilograms (kg)', value: 'kg' },
  { label: 'Cases', value: 'cases' },
  { label: 'Units', value: 'units' },
] as const;

const haccpEventSchema = z.object({
  eventType: z.enum(['receiving', 'disposal', 'physical_count', 'sale']),
  productId: z.string().uuid({ message: 'Select a product' }),
  vendorId: z.string().uuid().optional().or(z.literal('')),
  customerId: z.string().uuid().optional().or(z.literal('')),
  quantity: z.number({ invalid_type_error: 'Quantity is required' }).positive('Enter a positive quantity'),
  unit: z.enum(['lbs', 'kg', 'cases', 'units']).default('lbs'),
  receivingDate: z.string().optional(),
  condition: z.enum(['Excellent', 'Good', 'Fair', 'Poor', 'Damaged']).optional(),
  unitPrice: z.number().min(0, 'Must be >= 0').optional(),
  notes: z.string().max(500, 'Max 500 chars').optional(),
  // Batch number creation for Receiving
  batchNumberMode: z.enum(['auto', 'manual']).default('auto'),
  manualBatchNumber: z.string().max(64).optional(),
}).refine((data) => {
  if (data.eventType === 'receiving') return !!data.vendorId && !!data.receivingDate;
  if (data.eventType === 'sale') return !!data.customerId;
  return true;
}, {
  message: 'Missing required fields for selected event',
  path: ['eventType']
}).refine((data) => {
  if (data.eventType === 'receiving' && data.batchNumberMode === 'manual') {
    return !!data.manualBatchNumber && data.manualBatchNumber.trim().length > 0;
  }
  return true;
}, {
  message: 'Batch number is required when using Manual entry',
  path: ['manualBatchNumber']
});

export type HACCPEventFormData = z.infer<typeof haccpEventSchema>;

interface HACCPEventFormProps {
  onSuccess: () => void;
  onCancel: () => void;
  // Optional external controls
  eventTypeOverride?: 'receiving' | 'disposal' | 'physical_count' | 'sale';
  useExternalBatchControls?: boolean;
  batchNumberModeOverride?: 'auto' | 'manual';
  manualBatchNumberOverride?: string;
  // When provided (YYYY-MM-DD), the form will prefill the date and use it for created_at
  dateOverride?: string;
}

export default function HACCPEventForm({ onSuccess, onCancel, eventTypeOverride, useExternalBatchControls, batchNumberModeOverride, manualBatchNumberOverride, dateOverride }: HACCPEventFormProps) {
  const { register, handleSubmit, watch, setValue, formState: { errors, isSubmitting } } = useForm<HACCPEventFormData>({
    resolver: zodResolver(haccpEventSchema),
    defaultValues: {
      eventType: 'receiving',
      unit: 'lbs',
      receivingDate: new Date().toISOString().split('T')[0],
      batchNumberMode: 'auto',
    }
  });

  const eventType = watch('eventType');
  const batchNumberMode = watch('batchNumberMode');
  const productId = watch('productId');
  const vendorId = watch('vendorId');
  const receivingDate = watch('receivingDate');

  const [products, setProducts] = useState<Array<{ id: string; name: string }>>([]);
  const [vendors, setVendors] = useState<Array<{ id: string; name: string }>>([]);
  const [customers, setCustomers] = useState<Array<{ id: string; name: string }>>([]);
  const [submitError, setSubmitError] = useState<string | null>(null);

  useEffect(() => {
    const load = async () => {
      const [prodRes, vendRes, custRes] = await Promise.all([
        supabase.from('Products').select('id, name').order('name', { ascending: true }),
        supabase.from('vendors').select('id, name').order('name', { ascending: true }),
        supabase.from('customers').select('id, name').order('name', { ascending: true }),
      ]);
      if (!prodRes.error && prodRes.data) setProducts(prodRes.data);
      if (!vendRes.error && vendRes.data) setVendors(vendRes.data);
      if (!custRes.error && custRes.data) setCustomers(custRes.data);
    };
    load();
  }, []);

  const productNameById = useMemo(() => Object.fromEntries(products.map(p => [p.id, p.name])), [products]);
  const vendorNameById = useMemo(() => Object.fromEntries(vendors.map(v => [v.id, v.name])), [vendors]);

  // Apply external overrides
  useEffect(() => {
    if (eventTypeOverride) setValue('eventType', eventTypeOverride);
  }, [eventTypeOverride, setValue]);

  useEffect(() => {
    if (batchNumberModeOverride) setValue('batchNumberMode', batchNumberModeOverride);
  }, [batchNumberModeOverride, setValue]);

  useEffect(() => {
    if (typeof manualBatchNumberOverride === 'string') setValue('manualBatchNumber', manualBatchNumberOverride);
  }, [manualBatchNumberOverride, setValue]);

  // Apply external date override for receiving date (YYYY-MM-DD)
  useEffect(() => {
    if (dateOverride && /\d{4}-\d{2}-\d{2}/.test(dateOverride)) {
      setValue('receivingDate', dateOverride);
    }
  }, [dateOverride, setValue]);

  const onSubmit = async (data: HACCPEventFormData) => {
    setSubmitError(null);
    try {
      const { eventType, productId, quantity, unit, unitPrice, notes } = data;
      let batchMeta: { batch_number?: string; lot_id?: string; tlc?: string } = {};

      // Resolve created_at using dateOverride if provided (set to local noon to avoid TZ shift)
      const createdAtISO = (() => {
        if (dateOverride && /\d{4}-\d{2}-\d{2}/.test(dateOverride)) {
          const [y, m, d] = dateOverride.split('-').map(Number);
          const dt = new Date(y, (m as number) - 1, d as number, 12, 0, 0, 0);
          return dt.toISOString();
        }
        return new Date().toISOString();
      })();

      // Receiving: prefer traceability path (partners/lots). If unavailable (e.g., 404 tables),
      // gracefully fall back to inserting into inventory_events only so the form still works.
      if (eventType === 'receiving') {
        const vendorId = data.vendorId as string;
        const vendorName = vendorNameById[vendorId];
        const productName = productNameById[productId];
        const receivingDate = data.receivingDate;

        if (!vendorName || !productName || !receivingDate) {
          throw new Error('Missing product, vendor, or date');
        }

        try {
          const result = await createReceivingWithLot({
            productName,
            quantity,
            unit,
            vendorName,
            receivingDate,
            condition: data.condition,
            notes,
            tlc: data.batchNumberMode === 'manual' ? data.manualBatchNumber : undefined,
          });
          if (!result.success) throw new Error(result.error?.message || 'Failed to create receiving/lot');
          batchMeta = { batch_number: result.data?.tlc, lot_id: result.data?.lot_id, tlc: result.data?.tlc };

          // Also log to unified inventory_events for dashboards/reporting
          const totalAmount = unitPrice ? Number((quantity * unitPrice).toFixed(2)) : null;
          const { error: invErr } = await supabase.from('inventory_events').insert({
            event_type: 'receiving',
            product_id: productId,
            quantity,
            unit_price: unitPrice ?? null,
            total_amount: totalAmount,
            notes: notes || null,
            metadata: {
              source: 'haccp-form',
              ...batchMeta,
              batch_number_mode: data.batchNumberMode,
              unit,
              vendor_id: vendorId,
              vendor_name: vendorName,
            },
            created_at: createdAtISO,
          });
          if (invErr) throw invErr;
        } catch (traceErr) {
          console.warn('Traceability path failed, falling back to inventory_events only:', traceErr);
          const totalAmount = unitPrice ? Number((quantity * unitPrice).toFixed(2)) : null;
          const fallbackMeta: Record<string, unknown> = {
            source: 'haccp-form',
            batch_number_mode: data.batchNumberMode,
          };
          // If user used the generator (switches to manual and sets manualBatchNumber), persist it as TLC
          if (data.batchNumberMode === 'manual' && data.manualBatchNumber) {
            fallbackMeta['tlc'] = data.manualBatchNumber;
            fallbackMeta['batch_number'] = data.manualBatchNumber;
          }
          // Persist unit and vendor context in metadata for dashboards/reporting
          fallbackMeta['unit'] = unit;
          fallbackMeta['vendor_id'] = vendorId;
          fallbackMeta['vendor_name'] = vendorName;
          const { error: invErr } = await supabase.from('inventory_events').insert({
            event_type: 'receiving',
            product_id: productId,
            quantity,
            unit_price: unitPrice ?? null,
            total_amount: totalAmount,
            notes: notes || null,
            metadata: fallbackMeta,
            created_at: createdAtISO,
          });
          if (invErr) throw invErr;
        }
      }

      // Disposal / Physical Count / Sales: insert into inventory_events only
      if (eventType !== 'receiving') {
        const payload: Record<string, unknown> = {
          event_type: eventType,
          product_id: productId,
          quantity,
          notes: notes || null,
          metadata: { source: 'haccp-form', unit },
          created_at: createdAtISO,
        };
        if (eventType === 'sale') {
          payload.customer_id = data.customerId || null;
          if (typeof data.unitPrice === 'number') {
            payload.unit_price = data.unitPrice;
            payload.total_amount = Number((quantity * data.unitPrice).toFixed(2));
          }
        }
        const { error } = await supabase.from('inventory_events').insert(payload);
        if (error) throw error;
      }

      onSuccess();
    } catch (err: unknown) {
      console.error('HACCP submit failed:', err);
      setSubmitError(err instanceof Error ? err.message : 'Failed to submit');
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Event Type */}
        <div>
          <label htmlFor="eventType" className="block text-sm font-medium text-gray-700">Event Type</label>
          <select id="eventType" {...register('eventType')} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            {EVENT_OPTIONS.map(opt => (
              <option key={opt.value} value={opt.value}>{opt.label}</option>
            ))}
          </select>
          {errors.eventType && <p className="mt-1 text-sm text-red-600">{errors.eventType.message}</p>}
        </div>

        {/* Product */}
        <div>
          <label htmlFor="productId" className="block text-sm font-medium text-gray-700">Product</label>
          <select id="productId" {...register('productId')} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            <option value="">Select a product</option>
            {products.map(p => <option key={p.id} value={p.id}>{p.name}</option>)}
          </select>
          {errors.productId && <p className="mt-1 text-sm text-red-600">{errors.productId.message}</p>}
        </div>

        {/* Quantity */}
        <div>
          <label htmlFor="quantity" className="block text-sm font-medium text-gray-700">Quantity</label>
          <input id="quantity" type="number" step="0.01" {...register('quantity', { valueAsNumber: true })} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" />
          {errors.quantity && <p className="mt-1 text-sm text-red-600">{errors.quantity.message}</p>}
        </div>

        {/* Unit */}
        <div>
          <label htmlFor="unit" className="block text-sm font-medium text-gray-700">Unit</label>
          <select id="unit" {...register('unit')} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            {UNIT_OPTIONS.map(u => <option key={u.value} value={u.value}>{u.label}</option>)}
          </select>
          {errors.unit && <p className="mt-1 text-sm text-red-600">{errors.unit.message as string}</p>}
        </div>

        {/* Receiving-only fields */}
        {eventType === 'receiving' && (
          <>
            <div>
              <label htmlFor="vendorId" className="block text-sm font-medium text-gray-700">Vendor</label>
              <select id="vendorId" {...register('vendorId')} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                <option value="">Select a vendor</option>
                {vendors.map(v => <option key={v.id} value={v.id}>{v.name}</option>)}
              </select>
              {errors.vendorId && <p className="mt-1 text-sm text-red-600">Vendor is required</p>}
            </div>
            <div>
              <label htmlFor="receivingDate" className="block text-sm font-medium text-gray-700">Receiving Date</label>
              <input id="receivingDate" type="date" {...register('receivingDate')} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" />
            </div>
            {!useExternalBatchControls && (
              <div>
                <label htmlFor="batchNumberMode" className="block text-sm font-medium text-gray-700">Batch Number</label>
                <select id="batchNumberMode" {...register('batchNumberMode')} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                  <option value="auto">Auto-generate (recommended)</option>
                  <option value="manual">Manual entry</option>
                </select>
                {/* Generator helper - can fill manual and switch mode */}
                <BatchNumberGenerator
                  date={receivingDate}
                  productId={productId}
                  vendorId={vendorId}
                  productNameById={productNameById}
                  vendorNameById={vendorNameById}
                  onGenerate={(val) => {
                    setValue('manualBatchNumber', val, { shouldDirty: true, shouldValidate: true });
                    setValue('batchNumberMode', 'manual', { shouldDirty: true, shouldValidate: true });
                  }}
                  disabled={isSubmitting}
                />
                {batchNumberMode === 'manual' && (
                  <input
                    id="manualBatchNumber"
                    placeholder="Enter batch number (TLC)"
                    type="text"
                    {...register('manualBatchNumber')}
                    className="mt-2 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  />
                )}
                {errors.manualBatchNumber && (
                  <p className="mt-1 text-sm text-red-600">{errors.manualBatchNumber.message as string}</p>
                )}
              </div>
            )}
            <div>
              <label htmlFor="condition" className="block text-sm font-medium text-gray-700">Condition</label>
              <select id="condition" {...register('condition')} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                {['Excellent','Good','Fair','Poor','Damaged'].map(c => <option key={c} value={c}>{c}</option>)}
              </select>
            </div>
            <div>
              <label htmlFor="unitPrice" className="block text-sm font-medium text-gray-700">Cost per Unit</label>
              <input id="unitPrice" type="number" step="0.01" min="0" {...register('unitPrice', { valueAsNumber: true })} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" />
            </div>
          </>
        )}

        {/* Sales-only fields */}
        {eventType === 'sale' && (
          <>
            <div>
              <label htmlFor="customerId" className="block text-sm font-medium text-gray-700">Customer</label>
              <select id="customerId" {...register('customerId')} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                <option value="">Select a customer</option>
                {customers.map(c => <option key={c.id} value={c.id}>{c.name}</option>)}
              </select>
              {errors.customerId && <p className="mt-1 text-sm text-red-600">Customer is required</p>}
            </div>
            <div>
              <label htmlFor="unitPrice" className="block text-sm font-medium text-gray-700">Unit Price</label>
              <input id="unitPrice" type="number" step="0.01" min="0" {...register('unitPrice', { valueAsNumber: true })} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" />
            </div>
          </>
        )}

        {/* Notes */}
        <div className={eventType === 'receiving' ? 'md:col-span-2' : ''}>
          <label htmlFor="notes" className="block text-sm font-medium text-gray-700">Notes</label>
          <textarea id="notes" rows={3} {...register('notes')} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" />
          {errors.notes && <p className="mt-1 text-sm text-red-600">{errors.notes.message}</p>}
        </div>
      </div>

      {submitError && (
        <div className="bg-red-50 border border-red-200 rounded p-3 text-sm text-red-700">{submitError}</div>
      )}

      <div className="flex justify-end gap-3">
        <button type="button" onClick={onCancel} className="px-4 py-2 rounded-lg bg-gray-200 text-gray-800 hover:bg-gray-300">Cancel</button>
        <button type="submit" disabled={isSubmitting} className="px-4 py-2 rounded-lg bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50">
          {isSubmitting ? 'Saving…' : 'Save Event'}
        </button>
      </div>
    </form>
  );
}
