import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import type { FieldError, FieldErrorsImpl, Merge } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { productSchema } from '../../lib/validation';
import { uploadProductImage, deleteProductImage, getProductImageUrl } from '../../lib/api';
import { Upload, X } from 'lucide-react';
import type { Product } from '../../types';
import { supabase } from '../../lib/supabase';

interface ProductFormProps {
  product?: Product;
  onSuccess: () => void;
  onCancel: () => void;
}

export default function ProductForm({ product, onSuccess, onCancel }: ProductFormProps) {
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [imageError, setImageError] = useState(false);

  const renderError = (err?: FieldError | Merge<FieldError, FieldErrorsImpl<Record<string, unknown>>>) =>
    typeof err?.message === 'string' ? err.message : null;

  useEffect(() => {
    const loadProductImage = async () => {
      if (product?.images?.[0]) {
        try {
          const url = await getProductImageUrl(product.images[0]);
          setImagePreview(url);
          setImageError(false);
        } catch (error) {
          console.error('Error loading product image:', error);
          setImageError(true);
        }
      }
    };

    loadProductImage();
  }, [product?.images]);

  const { register, handleSubmit, watch, formState: { errors } } = useForm<Product>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      ...product,
      date: product?.date || new Date().toISOString().split('T')[0],
      condition: product?.condition || 'frozen',
      amount: product?.amount || 0,
      price: product?.price === null ? undefined : product?.price,
      category: product?.category || '',
      supplier: product?.supplier || '',
      notes: product?.notes || ''
    }
  });

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        alert('File size must be less than 5MB');
        return;
      }
      setImageFile(file);
      const previewUrl = URL.createObjectURL(file);
      setImagePreview(previewUrl);
      setImageError(false);
    }
  };

  const handleRemoveImage = async () => {
    if (imageFile) {
      URL.revokeObjectURL(imagePreview || '');
    }
    setImageFile(null);
    setImagePreview(null);
    if (product?.images?.[0]) {
      try {
        await deleteProductImage(product.images[0]);
      } catch (error) {
        console.error('Error deleting image:', error);
      }
    }
  };

  const onSubmit = async (data: Product) => {
    console.log('Form submitted with data:', data);
    console.log('Current image file:', imageFile);
    console.log('Current product images:', product?.images);
    setSubmitError(null);
    setIsSubmitting(true);
    
    try {
      let imagePath = null;
      
      if (imageFile) {
        console.log('Uploading new image file:', imageFile.name);
        // Delete old image if it exists
        if (product?.images?.[0]) {
          try {
            console.log('Deleting old image:', product.images[0]);
            await deleteProductImage(product.images[0]);
          } catch (error) {
            console.error('Error deleting old image:', error);
          }
        }
        // Upload new image and get the path
        imagePath = await uploadProductImage(imageFile);
        console.log('New image uploaded successfully, path:', imagePath);
      } else {
        console.log('No new image file to upload');
      }

      // Keep existing images if no new image is uploaded and no image was deleted
      const existingImages = (!imageFile && product?.images) || [];
      const images = imagePath ? [imagePath] : existingImages;
      console.log('Final images array:', images);

      const formattedData = {
        name: data.name,
        date: data.date,
        category: data.category || '',
        sub_category: data.subCategory || '',
        amount: data.amount,
        min_stock: 0,
        price: data.price === null || data.price === 0 ? null : data.price,
        cost: null,
        supplier_id: null,
        expiry_date: null,
        storage_temp: null,
        handling_instructions: null,
        origin: null,
        species_details: null,
        market_price_history: [],
        metadata: {},
        images,
        condition: data.condition || 'frozen',
        notes: data.notes || '',
        other_condition: data.otherCondition || '',
        supplier: data.supplier || '',
      };

      console.log('Sending formatted data to Supabase:', formattedData);

      if (product?.id) {
        console.log('Updating existing product:', product.id);
        const { data: updatedProduct, error } = await supabase
          .from('Products')
          .update(formattedData)
          .eq('id', product.id)
          .select()
          .single();

        if (error) throw error;
        console.log('Product updated successfully:', updatedProduct);
      } else {
        console.log('Creating new product');
        const { data: newProduct, error } = await supabase
          .from('Products')
          .insert([formattedData])
          .select()
          .single();

        if (error) throw error;
        console.log('Product created successfully:', newProduct);
      }

      onSuccess();
    } catch (error) {
      console.error('Error saving product:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      setSubmitError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div>
        <label htmlFor="date" className="block text-sm font-medium text-gray-700">
          Date *
        </label>
        <input
          type="date"
          id="date"
          {...register('date')}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          aria-invalid={errors.date ? 'true' : undefined}
        />
        {renderError(errors.date) && (
          <p className="mt-1 text-sm text-red-600" role="alert">{renderError(errors.date)}</p>
        )}
      </div>

      {/* Image Upload */}
      <div className="space-y-2">
        <label htmlFor="product-image" className="block text-sm font-medium text-gray-700">
          Product Image
        </label>
        <div className="flex items-center space-x-4">
          <div className="relative flex-shrink-0 h-32 w-32 border rounded-lg overflow-hidden bg-gray-100">
            {imagePreview && !imageError ? (
              <>
                <img
                  src={imagePreview}
                  alt="Product preview"
                  className="h-full w-full object-cover"
                  onError={() => setImageError(true)}
                />
                <button
                  type="button"
                  onClick={handleRemoveImage}
                  className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600"
                  title="Remove image"
                  aria-label="Remove product image"
                >
                  <X size={16} />
                </button>
              </>
            ) : (
              <div className="h-full w-full flex flex-col items-center justify-center text-gray-400 gap-2">
                <Upload className="h-8 w-8" aria-hidden="true" />
                {imageError && (
                  <span className="text-xs text-red-500 text-center px-2">
                    Error loading image
                  </span>
                )}
              </div>
            )}
          </div>
          <div className="flex-1">
            <input
              id="product-image"
              type="file"
              accept="image/*"
              onChange={handleImageChange}
              className="block w-full text-sm text-gray-500
                file:mr-4 file:py-2 file:px-4
                file:rounded-md file:border-0
                file:text-sm file:font-medium
                file:bg-blue-50 file:text-blue-700
                hover:file:bg-blue-100"
              title="Choose product image"
              aria-label="Choose product image"
            />
            <p className="mt-2 text-sm text-gray-500">
              Upload a product image (PNG, JPG up to 5MB)
            </p>
          </div>
        </div>
      </div>

      {/* Product Details */}
      <div className="space-y-4">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700">
            Product Name *
          </label>
          <input
            type="text"
            id="name"
            {...register('name')}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            aria-invalid={errors.name ? 'true' : undefined}
          />
          {renderError(errors.name) && (
            <p className="mt-1 text-sm text-red-600" role="alert">{renderError(errors.name)}</p>
          )}
        </div>

        <div>
          <label htmlFor="amount" className="block text-sm font-medium text-gray-700">
            Amount *
          </label>
          <input
            type="number"
            id="amount"
            {...register('amount', { valueAsNumber: true })}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            aria-invalid={errors.amount ? 'true' : undefined}
          />
          {renderError(errors.amount) && (
            <p className="mt-1 text-sm text-red-600" role="alert">{renderError(errors.amount)}</p>
          )}
        </div>

        <div>
          <label htmlFor="price" className="block text-sm font-medium text-gray-700">
            Price
          </label>
          <input
            type="number"
            id="price"
            step="0.01"
            {...register('price', { valueAsNumber: true })}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            aria-invalid={errors.price ? 'true' : undefined}
          />
          {renderError(errors.price) && (
            <p className="mt-1 text-sm text-red-600" role="alert">{renderError(errors.price)}</p>
          )}
        </div>

        <div>
          <label htmlFor="condition" className="block text-sm font-medium text-gray-700">
            Condition *
          </label>
          <select
            id="condition"
            {...register('condition')}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            aria-invalid={errors.condition ? 'true' : undefined}
          >
            <option value="fresh">Fresh</option>
            <option value="frozen">Frozen</option>
            <option value="other">Other</option>
          </select>
          {renderError(errors.condition) && (
            <p className="mt-1 text-sm text-red-600" role="alert">{renderError(errors.condition)}</p>
          )}
        </div>

        {watch('condition') === 'other' && (
          <div>
            <label htmlFor="otherCondition" className="block text-sm font-medium text-gray-700">
              Specify Other Condition
            </label>
            <input
              type="text"
              id="otherCondition"
              {...register('otherCondition')}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              aria-invalid={errors.otherCondition ? 'true' : undefined}
            />
            {renderError(errors.otherCondition) && (
              <p className="mt-1 text-sm text-red-600" role="alert">{renderError(errors.otherCondition)}</p>
            )}
          </div>
        )}

        <div>
          <label htmlFor="category" className="block text-sm font-medium text-gray-700">
            Category
          </label>
          <input
            type="text"
            id="category"
            {...register('category')}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            aria-invalid={errors.category ? 'true' : undefined}
          />
          {renderError(errors.category) && (
            <p className="mt-1 text-sm text-red-600" role="alert">{renderError(errors.category)}</p>
          )}
        </div>

        <div>
          <label htmlFor="supplier" className="block text-sm font-medium text-gray-700">
            Supplier
          </label>
          <input
            type="text"
            id="supplier"
            {...register('supplier')}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            aria-invalid={errors.supplier ? 'true' : undefined}
          />
          {renderError(errors.supplier) && (
            <p className="mt-1 text-sm text-red-600" role="alert">{renderError(errors.supplier)}</p>
          )}
        </div>

        <div>
          <label htmlFor="notes" className="block text-sm font-medium text-gray-700">
            Notes
          </label>
          <textarea
            id="notes"
            {...register('notes')}
            rows={3}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            aria-invalid={errors.notes ? 'true' : undefined}
          />
          {renderError(errors.notes) && (
            <p className="mt-1 text-sm text-red-600" role="alert">{renderError(errors.notes)}</p>
          )}
        </div>
      </div>

      {submitError && (
        <div className="text-red-600 text-sm" role="alert">{submitError}</div>
      )}

      <div className="flex justify-end gap-4">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isSubmitting}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
        >
          {isSubmitting ? 'Saving...' : 'Save Product'}
        </button>
      </div>
    </form>
  );
}
