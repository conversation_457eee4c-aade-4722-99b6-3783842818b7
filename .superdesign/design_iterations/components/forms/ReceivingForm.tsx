import React, { useState, useEffect, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { receivingSchema } from '../../lib/validation';
import { createReceivingWithLot } from '../../lib/api';
import { 
  Calendar, Save, Loader2, 
  Scan, HelpCircle, Check 
} from 'lucide-react';
import Modal from '../modals/Modal';

interface ReceivingFormProps {
  onSuccess: () => void;
  onCancel: () => void;
}

export type ReceivingFormData = {
  productName: string;
  receivingDate: string;
  quantity: number;
  vendorName: string;
  condition: 'Excellent' | 'Good' | 'Fair' | 'Poor' | 'Damaged';
  daysInTransit?: number;
  costPerUnit?: number;
  notes?: string;
};

export default function ReceivingForm({ onSuccess, onCancel }: ReceivingFormProps) {
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [draftSaved, setDraftSaved] = useState<Date | null>(null);
  const formRef = useRef<HTMLFormElement>(null);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [lastTLC, setLastTLC] = useState<string | null>(null);
  
  const { 
    register, 
    handleSubmit, 
    watch, 
    setValue,
    formState: { errors, isDirty }
  } = useForm<ReceivingFormData>({
    resolver: zodResolver(receivingSchema),
    defaultValues: {
      receivingDate: new Date().toISOString().split('T')[0],
      condition: 'Good'
    }
  });

  const watchedFields = {
    productName: watch('productName'),
    quantity: watch('quantity'),
    vendorName: watch('vendorName'),
    condition: watch('condition')
  };

  // Auto-save draft
  useEffect(() => {
    const subscription = watch((value, { type }) => {
      if (isDirty && type === 'change') {
        const timeoutId = setTimeout(() => {
          localStorage.setItem('receiving-draft', JSON.stringify(value));
          setDraftSaved(new Date());
        }, 30000);
        return () => clearTimeout(timeoutId);
      }
    });
    return () => subscription.unsubscribe();
  }, [watch, isDirty]);

  // Load draft on mount
  useEffect(() => {
    const draft = localStorage.getItem('receiving-draft');
    if (draft) {
      try {
        const data = JSON.parse(draft) as Partial<ReceivingFormData>;
        Object.entries(data).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            setValue(key as keyof ReceivingFormData, value as ReceivingFormData[keyof ReceivingFormData]);
          }
        });
      } catch (error) {
        console.error('Error loading draft:', error);
      }
    }
  }, [setValue]);

  const handleBarcodeScan = async () => {
    try {
      // Implement barcode scanning logic here
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      // Process stream for barcode scanning here...
      // For now, immediately stop to free camera and avoid leaks
      stream.getTracks().forEach(t => t.stop());
    } catch (error) {
      console.error('Barcode scanning error:', error);
    }
  };

  const onSubmit = async (data: ReceivingFormData) => {
    setSubmitError(null);
    setIsSaving(true);
    try {
      const result = await createReceivingWithLot({
        productName: data.productName,
        quantity: data.quantity,
        vendorName: data.vendorName,
        receivingDate: data.receivingDate,
        condition: data.condition,
        notes: data.notes,
      });

      if (result.success) {
        setLastTLC(result.data?.tlc ?? null);
        localStorage.removeItem('receiving-draft');
        setShowSuccess(true);
        onSuccess();
      } else {
        const errorMessage = result.error?.message || 'Failed to save receiving record';
        setSubmitError(errorMessage);
      }
    } catch (error) {
      console.error('Error saving receiving record:', error);
      setSubmitError('An unexpected error occurred. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  if (showSuccess) {
    return (
      <div className="text-center py-8">
        <div className="mb-4">
          <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
            <Check className="w-6 h-6 text-green-600" />
          </div>
        </div>
        <h3 className="text-lg font-semibold mb-2">Receiving Record Saved!</h3>
        {lastTLC && (
          <p className="text-sm text-gray-600">
            Traceability Lot Code (TLC): <span className="font-mono">{lastTLC}</span>
          </p>
        )}
        <div className="space-x-4 mt-6">
          <button
            onClick={() => {
              setShowSuccess(false);
              formRef.current?.reset();
            }}
            className="text-blue-600 hover:text-blue-700"
          >
            Add Another Item
          </button>
          <button
            onClick={onSuccess}
            className="text-blue-600 hover:text-blue-700"
          >
            View Record
          </button>
          <button
            onClick={onCancel}
            className="text-gray-600 hover:text-gray-700"
          >
            Return to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <form ref={formRef} onSubmit={handleSubmit(() => setShowConfirmation(true))} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Product Name */}
        <div className="relative">
          <label className="block text-sm font-medium text-gray-700">
            Product Name <span className="text-red-500">*</span>
          </label>
          <div className="mt-1 relative">
            <input
              type="text"
              maxLength={100}
              {...register('productName')}
              className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
            <button
              type="button"
              onClick={handleBarcodeScan}
              title="Scan Barcode"
              className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              <Scan className="w-5 h-5" />
            </button>
          </div>
          {errors.productName && (
            <p className="mt-1 text-sm text-red-600">{errors.productName.message}</p>
          )}
        </div>

        {/* Receiving Date */}
        <div>
          <label className="block text-sm font-medium text-gray-700">
            Receiving Date <span className="text-red-500">*</span>
          </label>
          <div className="mt-1 relative">
            <input
              type="date"
              {...register('receivingDate')}
              className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
            <Calendar className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400" />
          </div>
          {errors.receivingDate && (
            <p className="mt-1 text-sm text-red-600">{errors.receivingDate.message}</p>
          )}
        </div>

        {/* Quantity */}
        <div>
          <label className="block text-sm font-medium text-gray-700">
            Quantity Received <span className="text-red-500">*</span>
          </label>
          <input
            type="number"
            min="1"
            step="1"
            {...register('quantity', { valueAsNumber: true })}
            className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          />
          {errors.quantity && (
            <p className="mt-1 text-sm text-red-600">{errors.quantity.message}</p>
          )}
        </div>

        {/* Vendor */}
        <div>
          <label className="block text-sm font-medium text-gray-700">
            Vendor/Supplier <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            {...register('vendorName')}
            className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          />
          {errors.vendorName && (
            <p className="mt-1 text-sm text-red-600">{errors.vendorName.message}</p>
          )}
        </div>

        {/* Condition */}
        <div>
          <label className="block text-sm font-medium text-gray-700">
            Product Condition <span className="text-red-500">*</span>
          </label>
          <select
            {...register('condition')}
            className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          >
            <option value="Excellent">Excellent</option>
            <option value="Good">Good</option>
            <option value="Fair">Fair</option>
            <option value="Poor">Poor</option>
            <option value="Damaged">Damaged</option>
          </select>
          {errors.condition && (
            <p className="mt-1 text-sm text-red-600">{errors.condition.message}</p>
          )}
        </div>

        {/* Days in Transit */}
        <div>
          <label className="block text-sm font-medium text-gray-700">
            Days in Transit
            <button
              type="button"
              className="ml-1 text-gray-400 hover:text-gray-600"
              title="Number of days the product was in transit"
            >
              <HelpCircle className="w-4 h-4 inline" />
            </button>
          </label>
          <input
            type="number"
            min="0"
            step="1"
            {...register('daysInTransit', { valueAsNumber: true })}
            className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          />
        </div>

        {/* Cost per Unit */}
        <div>
          <label className="block text-sm font-medium text-gray-700">
            Cost per Unit
          </label>
          <div className="mt-1 relative">
            <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">$</span>
            <input
              type="number"
              step="0.01"
              min="0"
              {...register('costPerUnit', { valueAsNumber: true })}
              className="w-full pl-8 rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Notes */}
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700">
            Notes/Comments
          </label>
          <textarea
            {...register('notes')}
            rows={3}
            className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          />
        </div>
      </div>

      {draftSaved && (
        <div className="flex items-center gap-2 text-sm text-gray-500">
          <Save className="w-4 h-4" />
          Draft saved at {draftSaved.toLocaleTimeString()}
        </div>
      )}

      <div className="flex justify-end gap-4">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isSaving}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
        >
          {isSaving ? (
            <>
              <Loader2 className="w-4 h-4 animate-spin" />
              Saving...
            </>
          ) : (
            'Submit'
          )}
        </button>
      </div>

      <Modal
        isOpen={showConfirmation}
        onClose={() => setShowConfirmation(false)}
        title="Confirm Submission"
      >
        <div className="space-y-4">
          <p>Please review the receiving details before submitting:</p>
          <div className="bg-gray-50 p-4 rounded-lg space-y-2">
            <p><strong>Product:</strong> {watchedFields.productName}</p>
            <p><strong>Quantity:</strong> {watchedFields.quantity}</p>
            <p><strong>Vendor:</strong> {watchedFields.vendorName}</p>
            <p><strong>Condition:</strong> {watchedFields.condition}</p>
          </div>
          <div className="flex justify-end gap-4">
            <button
              type="button"
              onClick={() => setShowConfirmation(false)}
              className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
            >
              Review Changes
            </button>
            <button
              type="button"
              onClick={handleSubmit(onSubmit)}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Confirm & Submit
            </button>
          </div>
        </div>
      </Modal>

      {submitError && (
        <div className="text-red-600 text-sm mt-2">
          {submitError}
        </div>
      )}
    </form>
  );
}
