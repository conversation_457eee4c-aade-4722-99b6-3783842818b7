import { useState, type ReactNode } from 'react';
import type { ActiveView } from '../contexts/NavigationContext';
import {
  LayoutDashboard,
  Package,
  BarChart2,
  MessageSquare,
  Settings,
  ChevronLeft,
  ChevronRight,
  LogOut,
  Users,
  Building2,
  Upload,
  User,
  ClipboardCheck,
  Calendar
} from 'lucide-react';

interface SidebarProps {
  activeView: ActiveView;
  setActiveView: (view: ActiveView) => void;
  onSignOut: () => void;
  userEmail?: string;
  className?: string;
}

export default function Sidebar({ activeView, setActiveView, onSignOut, userEmail, className }: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [haccpOpen, setHaccpOpen] = useState(true);

  const navItems: Array<{ icon: ReactNode; label: string; view: ActiveView }> = [
    { icon: <LayoutDashboard size={24} />, label: 'Dashboard', view: 'Dashboard' },
    { icon: <Package size={24} />, label: 'Inventory', view: 'Inventory' },
    { icon: <Building2 size={24} />, label: 'Vendors', view: 'Vendors' },
    { icon: <Users size={24} />, label: 'Customers', view: 'Customers' },
    { icon: <BarChart2 size={24} />, label: 'Analytics', view: 'Analytics' },
    { icon: <MessageSquare size={24} />, label: 'Messages', view: 'Messages' },
    { icon: <Settings size={24} />, label: 'Settings', view: 'Settings' },
    { icon: <Upload size={24} />, label: 'Import', view: 'Import' }
  ];

  return (
    <nav className={`h-screen bg-gray-800 text-white transition-all duration-300 ${isCollapsed ? 'w-20' : 'w-64'} ${className || ''}`}>
      <div className="flex flex-col h-full p-4">
        <div className="mb-6">
          {!isCollapsed && (
            <div>
              <h1 className="text-lg font-bold">Pacific Cloud</h1>
              <h2 className="text-sm text-gray-400">Seafoods</h2>
              <p className="text-xs text-gray-500">Inventory Manager</p>
            </div>
          )}
        </div>

        <button
          className="flex items-center justify-end cursor-pointer mb-6"
          onClick={() => setIsCollapsed(!isCollapsed)}
          aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        >
          {isCollapsed ? <ChevronRight size={24} /> : <ChevronLeft size={24} />}
        </button>

        <div className="flex-1 space-y-2">
          {navItems.map((item) => (
            <button
              key={item.view}
              className={`w-full flex items-center space-x-2 p-3 rounded-lg transition-colors
                ${activeView === item.view ? 'bg-gray-700' : 'hover:bg-gray-700'}`}
              onClick={() => setActiveView(item.view)}
              aria-current={activeView === item.view ? 'page' : undefined}
            >
              <span className="min-w-[24px]">{item.icon}</span>
              {!isCollapsed && <span>{item.label}</span>}
            </button>
          ))}

          {/* HACCP group */}
          <div className="space-y-1">
            <button
              className={`w-full flex items-center justify-between p-3 rounded-lg transition-colors hover:bg-gray-700`}
              onClick={() => setHaccpOpen((v) => !v)}
              aria-expanded={haccpOpen ? 'true' : 'false'}
              aria-controls="haccp-subnav"
            >
              <div className="flex items-center space-x-2">
                <span className="min-w-[24px]"><ClipboardCheck size={24} /></span>
                {!isCollapsed && <span>HACCP Events</span>}
              </div>
              {!isCollapsed && (
                <span className="text-gray-300">{haccpOpen ? <ChevronLeft size={18} className="rotate-90" /> : <ChevronRight size={18} />}</span>
              )}
            </button>

            {!isCollapsed && haccpOpen && (
              <div id="haccp-subnav" className="pl-10 space-y-1">
                <button
                  className={`w-full flex items-center space-x-2 p-2 rounded-lg transition-colors ${activeView === 'HACCP: Batches' ? 'bg-gray-700' : 'hover:bg-gray-700'}`}
                  onClick={() => setActiveView('HACCP: Batches')}
                  aria-current={activeView === 'HACCP: Batches' ? 'page' : undefined}
                >
                  <span className="min-w-[20px]"><Package size={20} /></span>
                  <span className="text-sm">Batches</span>
                </button>
                <button
                  className={`w-full flex items-center space-x-2 p-2 rounded-lg transition-colors ${activeView === 'HACCP: Events' || activeView === 'HACCP Events' ? 'bg-gray-700' : 'hover:bg-gray-700'}`}
                  onClick={() => setActiveView('HACCP: Events')}
                  aria-current={activeView === 'HACCP: Events' ? 'page' : undefined}
                >
                  <span className="min-w-[20px]"><ClipboardCheck size={20} /></span>
                  <span className="text-sm">Events</span>
                </button>
                <button
                  className={`w-full flex items-center space-x-2 p-2 rounded-lg transition-colors ${activeView === 'HACCP: Calendar' ? 'bg-gray-700' : 'hover:bg-gray-700'}`}
                  onClick={() => setActiveView('HACCP: Calendar')}
                  aria-current={activeView === 'HACCP: Calendar' ? 'page' : undefined}
                >
                  <span className="min-w-[20px]"><Calendar size={20} /></span>
                  <span className="text-sm">Calendar</span>
                </button>
              </div>
            )}
          </div>
        </div>

        {userEmail && (
          <div className={`flex items-center gap-2 mb-4 ${isCollapsed ? 'justify-center' : ''}`}>
            <div className="bg-gray-700 p-2 rounded-full">
              <User size={20} />
            </div>
            {!isCollapsed && (
              <div className="text-sm text-gray-400 truncate">
                {userEmail}
              </div>
            )}
          </div>
        )}

        <button
          className="w-full flex items-center space-x-2 p-3 rounded-lg transition-colors hover:bg-gray-700"
          onClick={onSignOut}
        >
          <span className="min-w-[24px]"><LogOut size={24} /></span>
          {!isCollapsed && <span>Sign Out</span>}
        </button>
      </div>
    </nav>
  );
}
