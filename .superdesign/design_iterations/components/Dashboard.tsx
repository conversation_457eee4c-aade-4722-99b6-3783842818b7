import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { BarChart3, Package, DollarSign, TrendingUp } from 'lucide-react';
import { format, startOfMonth, endOfDay, subMonths, subDays, startOfYear, parseISO } from 'date-fns';
import { supabase } from '../lib/supabase';
import { EVENT_TYPES } from '../lib/setupDatabase';

interface PostgrestError {
  message: string;
  details: string;
  hint?: string;
  code: string;
}

interface SalesData {
  date: string;
  revenue: number;
  profit: number;
}

interface DashboardStats {
  totalProducts: number;
  monthlyRevenue: number;
  avgDailySales: number;
  totalProfit: number;
  profitMargin: number;
  avgPricePerOrder: number;
  sellThroughRate: number;
}

 

interface FilterOptions {
  startDate: string;
  endDate: string;
  categoryId: string;
}

interface DatePreset {
  label: string;
  getValue: () => { startDate: string; endDate: string };
}

interface PieChartData {
  name: string;
  value: number;
  secondaryValue?: number;
}

type PieChartMetric = 'products' | 'categories' | 'vendors' | 'customers' | 'channels';
type PieChartValueType = 'quantity' | 'revenue';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

interface InventoryProduct {
  id: string;
  name: string;
  category_id: string;
  category: {
    id: string;
    name: string;
  };
  vendor: {
    id: string;
    name: string;
  };
}

interface InventoryEvent {
  id: string;
  created_at: string;
  event_type: string;
  quantity: number;
  unit_price: number;
  total_amount: number;
  unit_cost?: number;
  metadata: {
    gross_amount?: number;
    cost?: number;
    customer?: string;
    channel?: string;
    [key: string]: unknown;
  };
  product: InventoryProduct;
}

export default function Dashboard() {
  const [salesData, setSalesData] = useState<SalesData[]>([]);
  const [stats, setStats] = useState<DashboardStats>({
    totalProducts: 0,
    monthlyRevenue: 0,
    avgDailySales: 0,
    totalProfit: 0,
    profitMargin: 0,
    avgPricePerOrder: 0,
    sellThroughRate: 0
  });
  const [loading, setLoading] = useState(true);
  const [pieChartData, setPieChartData] = useState<PieChartData[]>([]);
  const [selectedPieMetric, setSelectedPieMetric] = useState<PieChartMetric>('products');
  const [filters, setFilters] = useState<FilterOptions>({
    startDate: startOfMonth(new Date()).toISOString(),
    endDate: endOfDay(new Date()).toISOString(),
    categoryId: ''
  });
  // Extend filters to include selected event types (default to sales and received for meaningful metrics)
  const [selectedEventTypes, setSelectedEventTypes] = useState<string[]>(['sale', 'received']);
  const [pieValueType, setPieValueType] = useState<PieChartValueType>('quantity');
  const [customerData, setCustomerData] = useState<PieChartData[]>([]);
  const [channelData, setChannelData] = useState<PieChartData[]>([]);

  const datePresets: DatePreset[] = [
    {
      label: 'Today',
      getValue: () => ({
        startDate: new Date().toISOString(),
        endDate: endOfDay(new Date()).toISOString()
      })
    },
    {
      label: 'Last 7 Days',
      getValue: () => ({
        startDate: subDays(new Date(), 6).toISOString(),
        endDate: endOfDay(new Date()).toISOString()
      })
    },
    {
      label: 'This Month',
      getValue: () => ({
        startDate: startOfMonth(new Date()).toISOString(),
        endDate: endOfDay(new Date()).toISOString()
      })
    },
    {
      label: 'Last 3 Months',
      getValue: () => ({
        startDate: subMonths(new Date(), 3).toISOString(),
        endDate: endOfDay(new Date()).toISOString()
      })
    },
    {
      label: 'This Year',
      getValue: () => ({
        startDate: startOfYear(new Date()).toISOString(),
        endDate: endOfDay(new Date()).toISOString()
      })
    }
  ];

  // Data fetchers
  const fetchPieChartData = useCallback(async () => {
    if (!selectedEventTypes || selectedEventTypes.length === 0) {
      setPieChartData([]);
      return;
    }
    try {
      let data: PieChartData[] = [];
      
      // Fetch events data (robust: avoid nested relationship selects that require FKs)
      let query = supabase
        .from('inventory_events')
        .select(`
          id,
          created_at,
          event_type,
          quantity,
          unit_price,
          total_amount,
          metadata,
          product_id
        `)
        .gte('created_at', filters.startDate)
        .lte('created_at', filters.endDate);

      if (selectedEventTypes && selectedEventTypes.length > 0) {
        // Map canonical types to include legacy synonyms in data (e.g., 'receiving', 'sales')
        const expandedTypes = selectedEventTypes.flatMap(t => {
          if (t === 'received') return ['received', 'receiving'];
          if (t === 'sale') return ['sale', 'sales'];
          return [t];
        });
        query = query.in('event_type', expandedTypes);
      }

      const { data: eventsData, error } = await query as { data: Array<InventoryEvent & { product_id: string }> | null; error: PostgrestError | null };

      if (error) {
        console.error('Error fetching pie chart data:', error);
        return;
      }

      // Optionally resolve product names (best-effort)
      let productNameById: Record<string, string> = {};
      if (eventsData && eventsData.length > 0) {
        const ids = Array.from(new Set(eventsData.map(e => e.product_id).filter(Boolean)));
        if (ids.length > 0) {
          const { data: products, error: prodErr } = await supabase
            .from('Products')
            .select('id,name')
            .in('id', ids);
          if (!prodErr && products) {
            productNameById = Object.fromEntries(products.map((p: { id: string; name: string }) => [p.id, p.name]));
          }
        }
      }

      if (eventsData) {
        switch (selectedPieMetric) {
          case 'products': {
            data = eventsData.reduce((acc: PieChartData[], event) => {
              const productName = productNameById[event.product_id] || event.product_id || 'Unknown Product';
              const quantity = Math.abs(event.quantity);
              const revenue = event.total_amount || 0;
              
              const existingProduct = acc.find(p => p.name === productName);
              if (existingProduct) {
                existingProduct.value += quantity;
                existingProduct.secondaryValue = (existingProduct.secondaryValue || 0) + revenue;
              } else {
                acc.push({ 
                  name: productName, 
                  value: quantity,
                  secondaryValue: revenue
                });
              }
              return acc;
            }, []);
            break;
          }

          case 'categories': {
            // Without relationships, we cannot reliably resolve category names; fallback to Unknown
            data = [];
            break;
          }

          case 'vendors': {
            // Without relationships, we cannot reliably resolve vendor names; fallback to Unknown
            data = [];
            break;
          }
        }
      }

      // Sort by the selected value type
      data.sort((a, b) => {
        const aValue = pieValueType === 'revenue' ? (a.secondaryValue || 0) : a.value;
        const bValue = pieValueType === 'revenue' ? (b.secondaryValue || 0) : b.value;
        return bValue - aValue;
      });

      setPieChartData(data.slice(0, 6));
    } catch (error) {
      console.error('Error fetching pie chart data:', error);
    }
  }, [filters.startDate, filters.endDate, selectedEventTypes, selectedPieMetric, pieValueType]);

  const fetchDashboardData = useCallback(async () => {
    if (!selectedEventTypes || selectedEventTypes.length === 0) {
      // When no event types are selected, show empty datasets and zero event-derived stats
      setCustomerData([]);
      setChannelData([]);
      setSalesData([]);
      setStats(prev => ({
        ...prev,
        monthlyRevenue: 0,
        avgDailySales: 0,
        totalProfit: 0,
        profitMargin: 0,
        avgPricePerOrder: 0,
        sellThroughRate: 0
      }));
      setLoading(false);
      return;
    }
    try {
      setLoading(true);
      
      // Fetch total products and inventory
      const { count: totalProducts } = await supabase
        .from('Products')
        .select('*', { count: 'exact' });

      // Fetch events data for sales and revenue
      let eventsQuery = supabase
        .from('inventory_events')
        .select(`
          id,
          created_at,
          event_type,
          quantity,
          unit_price,
          unit_cost,
          total_amount,
          metadata,
          product_id
        `)
        .gte('created_at', filters.startDate)
        .lte('created_at', filters.endDate)
        .order('created_at');

      if (selectedEventTypes && selectedEventTypes.length > 0) {
        const expandedTypes = selectedEventTypes.flatMap(t => {
          if (t === 'received') return ['received', 'receiving'];
          if (t === 'sale') return ['sale', 'sales'];
          return [t];
        });
        eventsQuery = eventsQuery.in('event_type', expandedTypes);
      }

      const { data: eventsData, error } = await eventsQuery as { data: Array<InventoryEvent & { product_id: string }> | null; error: PostgrestError | null };

      if (error) {
        console.error('Error fetching events data:', error);
        return;
      }


      // Process sales data (support legacy synonyms: 'sales', 'receiving')
      const salesEvents = (eventsData || []).filter(event => event.event_type === 'sale' || event.event_type === 'sales');
      const receivingEvents = (eventsData || []).filter(event => event.event_type === 'received' || event.event_type === 'receiving');

      // Calculate sell-through rate
      const totalReceived = receivingEvents.reduce((sum, event) => sum + Math.abs(event.quantity), 0);
      const totalSold = salesEvents.reduce((sum, event) => sum + Math.abs(event.quantity), 0);
      const sellThroughRate = totalReceived > 0 ? (totalSold / totalReceived) * 100 : 0;

      // Process sales by customer
      const customerSales = salesEvents.reduce((acc: { [key: string]: { orders: number; revenue: number } }, event) => {
        const customer = event.metadata?.customer || 'Unknown';
        if (!acc[customer]) {
          acc[customer] = { orders: 0, revenue: 0 };
        }
        acc[customer].orders++;
        acc[customer].revenue += event.total_amount || 0;
        return acc;
      }, {});

      const customerChartData = Object.entries(customerSales).map(([name, data]) => ({
        name,
        value: data.orders,
        secondaryValue: data.revenue
      }));

      // Process sales by channel
      const channelSales = salesEvents.reduce((acc: { [key: string]: { orders: number; revenue: number } }, event) => {
        const channel = event.metadata?.channel || 'Unknown';
        if (!acc[channel]) {
          acc[channel] = { orders: 0, revenue: 0 };
        }
        acc[channel].orders++;
        acc[channel].revenue += event.total_amount || 0;
        return acc;
      }, {});

      const channelChartData = Object.entries(channelSales).map(([name, data]) => ({
        name,
        value: data.orders,
        secondaryValue: data.revenue
      }));

      // Calculate average price per order
      const totalOrders = salesEvents.length;
      const orderRevenue = salesEvents.reduce((sum, event) => sum + (event.total_amount || 0), 0);
      const avgPricePerOrder = totalOrders > 0 ? orderRevenue / totalOrders : 0;

      // Update state
      setCustomerData(customerChartData);
      setChannelData(channelChartData);
      setStats(prev => ({
        ...prev,
        avgPricePerOrder,
        sellThroughRate
      }));

      // Process events data with category filter and profit calculation
      const processedSalesData = salesEvents.reduce((acc: { [key: string]: { revenue: number; profit: number } }, event) => {
        const date = format(parseISO(event.created_at), 'yyyy-MM-dd');
        
        if (filters.categoryId && event.product.category_id !== filters.categoryId) {
          return acc;
        }

        const quantity = Math.abs(event.quantity);
        const revenue = event.total_amount || 0;
        const cost = quantity * (event.unit_cost || 0);
        const profit = revenue - cost;
        
        acc[date] = {
          revenue: (acc[date]?.revenue || 0) + revenue,
          profit: (acc[date]?.profit || 0) + profit
        };
        
        return acc;
      }, {});

      const formattedSalesData = Object.entries(processedSalesData)
        .map(([date, data]) => ({
          date,
          revenue: data.revenue,
          profit: data.profit
        }))
        .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

      // Calculate totals
      const periodRevenue = formattedSalesData.reduce((sum, day) => sum + day.revenue, 0);
      const totalProfit = formattedSalesData.reduce((sum, day) => sum + day.profit, 0);
      const avgDailySales = periodRevenue / (formattedSalesData.length || 1);
      const profitMargin = periodRevenue > 0 ? (totalProfit / periodRevenue) * 100 : 0;

      // Update state
      setSalesData(formattedSalesData);
      setStats({
        totalProducts: totalProducts || 0,
        monthlyRevenue: periodRevenue,
        avgDailySales,
        totalProfit,
        profitMargin,
        avgPricePerOrder,
        sellThroughRate
      });
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  }, [filters.startDate, filters.endDate, filters.categoryId, selectedEventTypes]);

  // Fetch dashboard and pie data when dependencies change
  useEffect(() => {
    fetchDashboardData();
    fetchPieChartData();
  }, [fetchDashboardData, fetchPieChartData]);
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  

  

  const handlePresetClick = (preset: DatePreset) => {
    const { startDate, endDate } = preset.getValue();
    setFilters(prev => ({ ...prev, startDate, endDate }));
  };

  // Event type filter helpers
  const formatEventLabel = (type: string) => {
    if (type === 'physical_count') return 'Physical Count';
    return type.charAt(0).toUpperCase() + type.slice(1);
  };

  const toggleEventType = (type: string) => {
    setSelectedEventTypes(prev =>
      prev.includes(type)
        ? prev.filter(t => t !== type)
        : [...prev, type]
    );
  };

  const selectAllEventTypes = () => {
    setSelectedEventTypes([...EVENT_TYPES]);
  };


  if (loading) {
    return <div className="p-6">Loading dashboard data...</div>;
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-start flex-col lg:flex-row gap-4">
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <div className="w-full lg:w-auto">
          <div className="bg-white p-4 rounded-lg shadow space-y-4">
            <div className="flex flex-wrap gap-2">
              {datePresets.map((preset, index) => (
                <button
                  key={index}
                  onClick={() => handlePresetClick(preset)}
                  className="px-3 py-1 text-sm rounded-full bg-gray-100 hover:bg-blue-50 hover:text-blue-600 transition-colors"
                >
                  {preset.label}
                </button>
              ))}
            </div>
            <div className="flex flex-wrap items-center gap-2">
              <span className="text-sm text-gray-600">Event Types:</span>
              {EVENT_TYPES.map((type) => (
                <label key={type} className="flex items-center gap-1 text-sm">
                  <input
                    type="checkbox"
                    checked={selectedEventTypes.includes(type)}
                    onChange={() => toggleEventType(type)}
                  />
                  {formatEventLabel(type)}
                </label>
              ))}
              <button
                type="button"
                onClick={selectAllEventTypes}
                className="px-2 py-1 text-xs rounded bg-gray-100 hover:bg-gray-200"
              >
                All
              </button>
              <button
                type="button"
                onClick={() => setSelectedEventTypes([])}
                className="px-2 py-1 text-xs rounded bg-gray-100 hover:bg-gray-200"
              >
                None
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard
          icon={<Package className="w-6 h-6 text-blue-500" />}
          title="Total Products"
          value={stats.totalProducts.toString()}
          trend="--"
        />
        <StatCard
          icon={<DollarSign className="w-6 h-6 text-green-500" />}
          title="Average Order Value"
          value={`$${Math.round(stats.avgPricePerOrder).toLocaleString()}`}
          trend="--"
        />
        <StatCard
          icon={<TrendingUp className="w-6 h-6 text-purple-500" />}
          title="Sell Through Rate"
          value={`${stats.sellThroughRate.toFixed(1)}%`}
          trend="--"
        />
        <StatCard
          icon={<BarChart3 className="w-6 h-6 text-orange-500" />}
          title="Profit"
          value={`$${Math.round(stats.totalProfit).toLocaleString()}`}
          secondaryValue={`${stats.profitMargin.toFixed(1)}% margin`}
          trend="--"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold mb-4">Revenue & Profit Trend</h2>
          <div className="h-[300px]">
            {salesData.length > 0 ? (
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={salesData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="date" 
                    tickFormatter={(date) => format(new Date(date), 'MMM d')}
                  />
                  <YAxis />
                  <Tooltip 
                    labelFormatter={(date) => format(new Date(date), 'MMM d, yyyy')}
                    formatter={(value, name) => [`$${value.toLocaleString()}`, name === 'revenue' ? 'Revenue' : 'Profit']}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="revenue" 
                    stroke="#3B82F6" 
                    strokeWidth={2}
                    dot={false}
                    name="Revenue"
                  />
                  <Line 
                    type="monotone" 
                    dataKey="profit" 
                    stroke="#10B981" 
                    strokeWidth={2}
                    dot={false}
                    name="Profit"
                  />
                </LineChart>
              </ResponsiveContainer>
            ) : (
              <div className="h-full flex items-center justify-center text-gray-500">
                No sales data available for this period
              </div>
            )}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">Distribution</h2>
            <div className="flex items-center gap-2">
              <select
                value={pieValueType}
                onChange={(e) => setPieValueType(e.target.value as PieChartValueType)}
                className="border rounded px-2 py-1 text-sm"
                aria-label="Select distribution value type"
              >
                <option value="quantity">By Quantity</option>
                <option value="revenue">By Revenue</option>
              </select>
              <select
                value={selectedPieMetric}
                onChange={(e) => setSelectedPieMetric(e.target.value as PieChartMetric)}
                className="border rounded px-2 py-1 text-sm"
                aria-label="Select distribution metric"
              >
                <option value="products">Top Products</option>
                <option value="categories">Categories</option>
                <option value="vendors">Vendors</option>
                <option value="customers">Customers</option>
                <option value="channels">Sales Channels</option>
              </select>
            </div>
          </div>
          <div className="h-[300px]">
            {pieChartData.length > 0 ? (
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={
                      selectedPieMetric === 'customers' ? customerData :
                      selectedPieMetric === 'channels' ? channelData :
                      pieChartData
                    }
                    dataKey={pieValueType === 'revenue' ? 'secondaryValue' : 'value'}
                    nameKey="name"
                    cx="50%"
                    cy="50%"
                    outerRadius={100}
                    label={({ name, percent }) => `${name} (${(percent * 100).toFixed(0)}%)`}
                  >
                    {pieChartData.map((_, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip 
                    formatter={(value) => [
                      pieValueType === 'revenue' ? `$${Number(value).toLocaleString()}` : value,
                      pieValueType === 'revenue' ? 'Revenue' : 'Quantity'
                    ]} 
                  />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              <div className="h-full flex items-center justify-center text-gray-500">
                No data available
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

function StatCard({ icon, title, value, secondaryValue, trend }: { 
  icon: React.ReactNode;
  title: string;
  value: string;
  secondaryValue?: string;
  trend: string;
}) {
  const isPositive = trend.startsWith('+');
  
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="p-2 bg-gray-50 rounded-lg">{icon}</div>
        <span className={`text-sm font-medium ${
          isPositive ? 'text-green-600' : 'text-red-600'
        }`}>
          {trend}
        </span>
      </div>
      <h3 className="text-sm font-medium text-gray-500">{title}</h3>
      <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
      {secondaryValue && <p className="text-sm text-gray-500 mt-1">{secondaryValue}</p>}
    </div>
  );
}
