import HACCPEventForm from './forms/HACCPEventForm';
import { useState } from 'react';

export default function HACCPEventsView() {
  const [selectedEventType, setSelectedEventType] = useState<'receiving' | 'disposal' | 'physical_count' | 'sale'>('receiving');
  const [batchNumberMode, setBatchNumberMode] = useState<'auto' | 'manual'>('auto');
  const [manualBatchNumber, setManualBatchNumber] = useState<string>('');

  const EVENT_OPTIONS = [
    { label: 'Receiving', value: 'receiving' },
    { label: 'Disposal', value: 'disposal' },
    { label: 'Physical Count', value: 'physical_count' },
    { label: 'Sales', value: 'sale' },
  ] as const;

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">HACCP Events</h1>
        {/* Controls dropdown lives on Events view */}
        <details className="relative">
          <summary className="list-none cursor-pointer select-none flex items-center gap-2 text-sm text-gray-700 border rounded px-3 py-1.5 hover:bg-gray-50">
            <span className="font-medium">HACCP Controls</span>
            <span className="text-gray-500">(Event, Batch, Form)</span>
          </summary>
          <div className="absolute right-0 mt-2 w-[360px] bg-white border rounded-lg shadow-lg p-4 z-10">
            <div className="space-y-3">
              <div>
                <label htmlFor="haccpEventType" className="block text-xs font-medium text-gray-700">Event Type</label>
                <select
                  id="haccpEventType"
                  value={selectedEventType}
                  onChange={(e) => setSelectedEventType(e.target.value as typeof selectedEventType)}
                  className="mt-1 w-full rounded border-gray-300 text-sm"
                >
                  {EVENT_OPTIONS.map(opt => (
                    <option key={opt.value} value={opt.value}>{opt.label}</option>
                  ))}
                </select>
              </div>
              <div>
                <label htmlFor="batchMode" className="block text-xs font-medium text-gray-700">Batch Tracking</label>
                <select
                  id="batchMode"
                  value={batchNumberMode}
                  onChange={(e) => setBatchNumberMode(e.target.value as 'auto' | 'manual')}
                  className="mt-1 w-full rounded border-gray-300 text-sm"
                >
                  <option value="auto">Auto-generate (recommended)</option>
                  <option value="manual">Manual entry</option>
                </select>
                {batchNumberMode === 'manual' && (
                  <input
                    id="manualTLC"
                    type="text"
                    placeholder="Enter batch number (TLC)"
                    value={manualBatchNumber}
                    onChange={(e) => setManualBatchNumber(e.target.value)}
                    className="mt-2 w-full rounded border-gray-300 text-sm"
                  />
                )}
              </div>
              {/* Form-only view; dashboard moved to its own page */}
            </div>
          </div>
        </details>
      </div>

      <div className="bg-white rounded-lg shadow p-4">
        <HACCPEventForm
          onSuccess={() => { /* TODO: add toast */ }}
          onCancel={() => { /* no-op on page view */ }}
          eventTypeOverride={selectedEventType}
          useExternalBatchControls
          batchNumberModeOverride={batchNumberMode}
          manualBatchNumberOverride={batchNumberMode === 'manual' ? manualBatchNumber : ''}
        />
      </div>
    </div>
  );
}
