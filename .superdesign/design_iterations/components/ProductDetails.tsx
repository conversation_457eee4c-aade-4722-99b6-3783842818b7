import React, { useEffect, useState } from 'react';
import { ChevronLeft, Image as ImageIcon } from 'lucide-react';
import type { Product } from '../types';
import { getProductImageUrl } from '../lib/api';

interface ProductDetailsProps {
  product: Product;
  onBack: () => void;
}

export default function ProductDetails({ product, onBack }: ProductDetailsProps) {
  const [imageUrl, setImageUrl] = useState<string>('');
  const [imageError, setImageError] = useState(false);

  useEffect(() => {
    const loadImage = async () => {
      if (product.images?.[0]) {
        try {
          const signedUrl = await getProductImageUrl(product.images[0]);
          console.log('Got signed URL for product detail:', signedUrl);
          setImageUrl(signedUrl);
          setImageError(false);
        } catch (error) {
          console.error('Error loading product image:', error);
          setImageError(true);
        }
      } else {
        console.log('No image found for product:', product.id);
        setImageError(true);
      }
    };

    loadImage();
  }, [product.images, product.id]);

  return (
    <div className="space-y-6">
      <button
        onClick={onBack}
        className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
      >
        <ChevronLeft className="w-4 h-4" />
        Back to Products
      </button>

      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="relative h-96 bg-gray-100 rounded-lg mb-6">
          {imageUrl && !imageError ? (
            <img
              src={imageUrl}
              alt={product.name}
              className="w-full h-full object-cover rounded-lg"
              onError={() => setImageError(true)}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <ImageIcon className="w-16 h-16 text-gray-400" />
            </div>
          )}
        </div>

        <div className="flex justify-between items-start mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{product.name}</h1>
            <p className="text-gray-500">
              {product.category} {product.subCategory ? `- ${product.subCategory}` : ''}
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
          <div className="space-y-4">
            <div className="p-4 bg-gray-50 rounded-lg">
              <h3 className="font-medium text-gray-900 mb-2">Product Details</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-500">Amount:</span>
                  <span className="text-gray-900">{product.amount}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Condition:</span>
                  <span className="text-gray-900">{product.condition}</span>
                </div>
                {product.price && (
                  <div className="flex justify-between">
                    <span className="text-gray-500">Price:</span>
                    <span className="text-gray-900">${product.price}</span>
                  </div>
                )}
              </div>
            </div>

            {product.supplier && (
              <div className="p-4 bg-gray-50 rounded-lg">
                <h3 className="font-medium text-gray-900 mb-2">Supplier Information</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-500">Supplier:</span>
                    <span className="text-gray-900">{product.supplier}</span>
                  </div>
                </div>
              </div>
            )}
          </div>

          {product.notes && (
            <div className="p-4 bg-gray-50 rounded-lg">
              <h3 className="font-medium text-gray-900 mb-2">Notes</h3>
              <p className="text-gray-600">{product.notes}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}