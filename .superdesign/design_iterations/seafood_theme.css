:root {
  --background: oklch(0.9900 0.0050 220.0000);
  --foreground: oklch(0.1500 0.0200 220.0000);
  --card: oklch(1.0000 0 0);
  --card-foreground: oklch(0.1500 0.0200 220.0000);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.1500 0.0200 220.0000);
  --primary: oklch(0.5500 0.1800 220.0000);
  --primary-foreground: oklch(0.9900 0.0050 220.0000);
  --secondary: oklch(0.9200 0.0300 180.0000);
  --secondary-foreground: oklch(0.2000 0.0400 180.0000);
  --muted: oklch(0.9500 0.0200 220.0000);
  --muted-foreground: oklch(0.5000 0.0300 220.0000);
  --accent: oklch(0.6500 0.1500 180.0000);
  --accent-foreground: oklch(0.1500 0.0200 220.0000);
  --destructive: oklch(0.6200 0.2000 15.0000);
  --destructive-foreground: oklch(0.9900 0.0050 220.0000);
  --border: oklch(0.8800 0.0200 220.0000);
  --input: oklch(0.9200 0.0200 220.0000);
  --ring: oklch(0.5500 0.1800 220.0000);
  --chart-1: oklch(0.5500 0.1800 220.0000);
  --chart-2: oklch(0.6500 0.1500 180.0000);
  --chart-3: oklch(0.7000 0.1200 160.0000);
  --chart-4: oklch(0.6000 0.1600 200.0000);
  --chart-5: oklch(0.5800 0.1400 240.0000);
  --sidebar: oklch(0.9700 0.0100 220.0000);
  --sidebar-foreground: oklch(0.1500 0.0200 220.0000);
  --sidebar-primary: oklch(0.5500 0.1800 220.0000);
  --sidebar-primary-foreground: oklch(0.9900 0.0050 220.0000);
  --sidebar-accent: oklch(0.6500 0.1500 180.0000);
  --sidebar-accent-foreground: oklch(0.1500 0.0200 220.0000);
  --sidebar-border: oklch(0.8800 0.0200 220.0000);
  --sidebar-ring: oklch(0.5500 0.1800 220.0000);
  --font-sans: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: JetBrains Mono, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --radius: 0.75rem;
  --shadow-2xs: 0 1px 2px 0px hsl(220 20% 0% / 0.03);
  --shadow-xs: 0 1px 3px 0px hsl(220 20% 0% / 0.06);
  --shadow-sm: 0 2px 4px -1px hsl(220 20% 0% / 0.08), 0 1px 2px -1px hsl(220 20% 0% / 0.06);
  --shadow: 0 4px 6px -1px hsl(220 20% 0% / 0.10), 0 2px 4px -2px hsl(220 20% 0% / 0.06);
  --shadow-md: 0 6px 12px -2px hsl(220 20% 0% / 0.12), 0 4px 8px -4px hsl(220 20% 0% / 0.08);
  --shadow-lg: 0 10px 20px -4px hsl(220 20% 0% / 0.15), 0 6px 12px -6px hsl(220 20% 0% / 0.10);
  --shadow-xl: 0 16px 32px -8px hsl(220 20% 0% / 0.18), 0 10px 20px -10px hsl(220 20% 0% / 0.12);
  --shadow-2xl: 0 24px 48px -12px hsl(220 20% 0% / 0.25);
  --tracking-normal: 0em;
  --spacing: 0.25rem;

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  /* Custom Seafood Manager Variables */
  --status-active: oklch(0.7000 0.1500 140.0000);
  --status-pending: oklch(0.7500 0.1200 60.0000);
  --status-inactive: oklch(0.6500 0.1300 20.0000);
  --rating-gold: oklch(0.7500 0.1400 50.0000);
  --ocean-blue: oklch(0.5500 0.1800 220.0000);
  --seafoam-green: oklch(0.6500 0.1500 180.0000);
  --coral-accent: oklch(0.6800 0.1600 30.0000);
  --pearl-white: oklch(0.9800 0.0100 220.0000);
}