# Terraform Configuration for Seafood Manager Production Infrastructure
# Multi-tenant scalable architecture with monitoring and security

terraform {
  required_version = ">= 1.0"
  required_providers {
    vercel = {
      source  = "vercel/vercel"
      version = "~> 1.0"
    }
    datadog = {
      source  = "datadog/datadog"
      version = "~> 3.0"
    }
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
  
  backend "s3" {
    bucket = "seafood-manager-terraform-state"
    key    = "production/terraform.tfstate"
    region = "us-west-2"
    
    dynamodb_table = "seafood-manager-terraform-locks"
    encrypt        = true
  }
}

# Variables
variable "environment" {
  description = "Environment name"
  type        = string
  default     = "production"
}

variable "project_name" {
  description = "Project name"
  type        = string
  default     = "seafood-manager"
}

variable "supabase_project_url" {
  description = "Supabase project URL"
  type        = string
  sensitive   = true
}

variable "supabase_anon_key" {
  description = "Supabase anonymous key"
  type        = string
  sensitive   = true
}

variable "openai_api_key" {
  description = "OpenAI API key"
  type        = string
  sensitive   = true
}

variable "datadog_api_key" {
  description = "DataDog API key"
  type        = string
  sensitive   = true
}

variable "datadog_app_key" {
  description = "DataDog application key"
  type        = string
  sensitive   = true
}

# Local values
locals {
  common_tags = {
    Project     = var.project_name
    Environment = var.environment
    ManagedBy   = "terraform"
    Application = "seafood-inventory-management"
  }
}

# Vercel Project Configuration
resource "vercel_project" "seafood_manager" {
  name      = "${var.project_name}-${var.environment}"
  framework = "vite"
  
  git_repository = {
    type = "github"
    repo = "ryanhorwath/Seafood-Manager"
  }
  
  build_command    = "npm run build"
  output_directory = "dist"
  install_command  = "npm ci"
  
  environment = [
    {
      key    = "NODE_ENV"
      value  = var.environment
      target = ["production", "preview"]
    },
    {
      key    = "VITE_SUPABASE_URL"
      value  = var.supabase_project_url
      target = ["production", "preview"]
    },
    {
      key    = "VITE_SUPABASE_ANON_KEY"
      value  = var.supabase_anon_key
      target = ["production", "preview"]
    },
    {
      key    = "VITE_OPENAI_API_KEY"
      value  = var.openai_api_key
      target = ["production", "preview"]
    },
    {
      key    = "VITE_DATADOG_CLIENT_TOKEN"
      value  = datadog_rum_application.seafood_manager.client_token
      target = ["production"]
    },
    {
      key    = "VITE_DATADOG_APPLICATION_ID"
      value  = datadog_rum_application.seafood_manager.id
      target = ["production"]
    }
  ]
}

# Vercel Domains
resource "vercel_project_domain" "seafood_manager_production" {
  project_id = vercel_project.seafood_manager.id
  domain     = "seafoodmanager.app"
}

resource "vercel_project_domain" "seafood_manager_staging" {
  project_id = vercel_project.seafood_manager.id
  domain     = "staging.seafoodmanager.app"
}

# DataDog RUM Application
resource "datadog_rum_application" "seafood_manager" {
  name = "${var.project_name}-${var.environment}"
  type = "browser"
  
  tags = [
    "env:${var.environment}",
    "service:${var.project_name}",
    "team:platform"
  ]
}

# DataDog Monitors
resource "datadog_monitor" "high_error_rate" {
  name               = "Seafood Manager - High Error Rate"
  type               = "metric alert"
  message            = "Error rate is above acceptable threshold. @slack-alerts @pagerduty"
  escalation_message = "Error rate is critically high. @oncall-engineer"
  
  query = "avg(last_5m):sum:rum.error_count{service:${var.project_name},env:${var.environment}}.as_rate() > 0.05"
  
  monitor_thresholds {
    warning  = 0.02
    critical = 0.05
  }
  
  notify_no_data    = false
  renotify_interval = 60
  notify_audit      = false
  timeout_h         = 60
  include_tags      = true
  
  tags = ["service:${var.project_name}", "env:${var.environment}", "team:platform"]
}

resource "datadog_monitor" "slow_page_load" {
  name               = "Seafood Manager - Slow Page Load"
  type               = "metric alert"
  message            = "Page load time is above threshold. Check performance. @slack-alerts"
  escalation_message = "Page load time is critically slow. @performance-team"
  
  query = "avg(last_10m):avg:browser.page.load_time{service:${var.project_name},env:${var.environment}} > 3000"
  
  monitor_thresholds {
    warning  = 2000
    critical = 3000
  }
  
  notify_no_data    = false
  renotify_interval = 60
  
  tags = ["service:${var.project_name}", "env:${var.environment}", "team:platform"]
}

resource "datadog_monitor" "haccp_compliance_violations" {
  name               = "Seafood Manager - HACCP Compliance Violations"
  type               = "metric alert"
  message            = "CRITICAL: HACCP compliance violations detected. @haccp-team @compliance-officer"
  escalation_message = "URGENT: Multiple HACCP failures. @food-safety-emergency"
  
  query = "sum(last_30m):sum:seafood.haccp.compliance{status:fail,env:${var.environment}}.as_count() > 3"
  
  monitor_thresholds {
    warning  = 1
    critical = 3
  }
  
  priority = 1  # High priority
  
  notify_no_data    = false
  renotify_interval = 15  # Renotify every 15 minutes for compliance issues
  
  tags = ["service:${var.project_name}", "env:${var.environment}", "team:compliance", "critical:haccp"]
}

# AWS CloudFront Distribution for Global CDN
resource "aws_cloudfront_distribution" "seafood_manager_cdn" {
  origin {
    domain_name = vercel_project_domain.seafood_manager_production.domain
    origin_id   = "vercel-${var.project_name}"
    
    custom_origin_config {
      http_port              = 80
      https_port             = 443
      origin_protocol_policy = "https-only"
      origin_ssl_protocols   = ["TLSv1.2"]
    }
  }
  
  enabled             = true
  is_ipv6_enabled     = true
  comment             = "CDN for Seafood Manager application"
  default_root_object = "index.html"
  
  default_cache_behavior {
    allowed_methods        = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
    cached_methods         = ["GET", "HEAD"]
    target_origin_id       = "vercel-${var.project_name}"
    compress              = true
    viewer_protocol_policy = "redirect-to-https"
    
    forwarded_values {
      query_string = false
      headers     = ["Origin", "Access-Control-Request-Headers", "Access-Control-Request-Method"]
      
      cookies {
        forward = "none"
      }
    }
    
    min_ttl     = 0
    default_ttl = 3600   # 1 hour
    max_ttl     = 86400  # 24 hours
  }
  
  # Cache behavior for static assets
  ordered_cache_behavior {
    path_pattern     = "/assets/*"
    allowed_methods  = ["GET", "HEAD", "OPTIONS"]
    cached_methods   = ["GET", "HEAD"]
    target_origin_id = "vercel-${var.project_name}"
    compress         = true
    
    forwarded_values {
      query_string = false
      
      cookies {
        forward = "none"
      }
    }
    
    min_ttl                = 86400   # 24 hours
    default_ttl            = 2592000 # 30 days
    max_ttl                = 31536000 # 1 year
    viewer_protocol_policy = "redirect-to-https"
  }
  
  # Geographic distribution for seafood industry
  price_class = "PriceClass_100"  # US, Canada, Europe
  
  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }
  
  viewer_certificate {
    cloudfront_default_certificate = true
  }
  
  tags = local.common_tags
}

# AWS WAF for Security
resource "aws_wafv2_web_acl" "seafood_manager_waf" {
  name  = "${var.project_name}-${var.environment}-waf"
  scope = "CLOUDFRONT"
  
  default_action {
    allow {}
  }
  
  # Rate limiting rule
  rule {
    name     = "RateLimitRule"
    priority = 1
    
    override_action {
      none {}
    }
    
    statement {
      rate_based_statement {
        limit              = 2000
        aggregate_key_type = "IP"
      }
    }
    
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "RateLimitRule"
      sampled_requests_enabled   = true
    }
    
    action {
      block {}
    }
  }
  
  # Known bad inputs
  rule {
    name     = "AWSManagedRulesKnownBadInputsRuleSet"
    priority = 2
    
    override_action {
      none {}
    }
    
    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesKnownBadInputsRuleSet"
        vendor_name = "AWS"
      }
    }
    
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "KnownBadInputsRuleSet"
      sampled_requests_enabled   = true
    }
  }
  
  tags = local.common_tags
  
  visibility_config {
    cloudwatch_metrics_enabled = true
    metric_name                = "${var.project_name}-${var.environment}-waf"
    sampled_requests_enabled   = true
  }
}

# Associate WAF with CloudFront
resource "aws_wafv2_web_acl_association" "seafood_manager_waf_association" {
  resource_arn = aws_cloudfront_distribution.seafood_manager_cdn.arn
  web_acl_arn  = aws_wafv2_web_acl.seafood_manager_waf.arn
}

# Outputs
output "vercel_project_url" {
  description = "Vercel project URL"
  value       = "https://${vercel_project_domain.seafood_manager_production.domain}"
}

output "cloudfront_distribution_id" {
  description = "CloudFront distribution ID"
  value       = aws_cloudfront_distribution.seafood_manager_cdn.id
}

output "cloudfront_domain_name" {
  description = "CloudFront distribution domain name"
  value       = aws_cloudfront_distribution.seafood_manager_cdn.domain_name
}

output "datadog_rum_application_id" {
  description = "DataDog RUM application ID"
  value       = datadog_rum_application.seafood_manager.id
}

output "waf_web_acl_id" {
  description = "WAF Web ACL ID"
  value       = aws_wafv2_web_acl.seafood_manager_waf.id
}