# Infrastructure Directory

This directory contains infrastructure-as-code configurations, deployment scripts, and cloud resource definitions for the Pacific Cloud Seafoods Manager application.

## Overview

The infrastructure directory manages all cloud resources, deployment configurations, and infrastructure automation for the application. It includes configurations for hosting, databases, monitoring, security, and CI/CD pipelines.

## Infrastructure Architecture

### Cloud Platform
- **Primary Platform**: Vercel for application hosting and serverless functions
- **Database**: Supabase (managed PostgreSQL with real-time capabilities)
- **Storage**: Supabase Storage for audio files and documents
- **CDN**: Vercel Edge Network for global content delivery
- **Monitoring**: Integrated monitoring and analytics

### Key Components

#### Application Hosting
- Serverless deployment on Vercel platform
- Automatic scaling based on demand
- Global edge network for optimal performance
- Built-in SSL/TLS certificates
- Custom domain configuration

#### Database Infrastructure
- Managed PostgreSQL database via Supabase
- Real-time subscriptions for live updates
- Row Level Security (RLS) for data protection
- Automated backups and point-in-time recovery
- Connection pooling for optimal performance

#### Storage Solutions
- Supabase Storage for file management
- Audio file storage with compression
- Secure file access with signed URLs
- Automatic cleanup and lifecycle management
- CDN integration for fast file delivery

## Configuration Files

### Deployment Configuration

#### `vercel.json`
**Purpose**: Vercel platform configuration for deployment and routing.

**Key Features**:
- Serverless function configuration
- Custom routing rules
- Environment variable management
- Build optimization settings
- Security headers configuration

**Example Configuration**:
```json
{
  "functions": {
    "api/voice-process.js": {
      "maxDuration": 30
    }
  },
  "headers": [
    {
      "source": "/api/(.*)",
      "headers": [
        {
          "key": "Access-Control-Allow-Origin",
          "value": "*"
        }
      ]
    }
  ],
  "rewrites": [
    {
      "source": "/api/(.*)",
      "destination": "/api/$1"
    }
  ]
}
```

### Environment Management

#### Environment Variables
Centralized environment variable management:

```bash
# Production Environment
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
OPENAI_API_KEY=your-openai-key
NODE_ENV=production

# Development Environment
VITE_SUPABASE_URL=http://localhost:54321
VITE_SUPABASE_ANON_KEY=local-anon-key
SUPABASE_SERVICE_ROLE_KEY=local-service-role-key
OPENAI_API_KEY=your-openai-key
NODE_ENV=development
```

#### Environment-Specific Configurations
Different configurations for each environment:

```typescript
// config/environments.ts
export const environments = {
  development: {
    api: {
      baseUrl: 'http://localhost:3000',
      timeout: 10000
    },
    database: {
      url: 'http://localhost:54321',
      poolSize: 5
    },
    features: {
      debugMode: true,
      mockExternalAPIs: true
    }
  },
  
  staging: {
    api: {
      baseUrl: 'https://staging.example.com',
      timeout: 15000
    },
    database: {
      url: process.env.STAGING_DATABASE_URL,
      poolSize: 10
    },
    features: {
      debugMode: false,
      mockExternalAPIs: false
    }
  },
  
  production: {
    api: {
      baseUrl: 'https://app.example.com',
      timeout: 20000
    },
    database: {
      url: process.env.PRODUCTION_DATABASE_URL,
      poolSize: 20
    },
    features: {
      debugMode: false,
      mockExternalAPIs: false
    }
  }
};
```

## Deployment Strategies

### Continuous Deployment
Automated deployment pipeline with quality gates:

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test:all
      - run: npm run quality:check

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run build:production
      - uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          vercel-args: '--prod'
```

### Blue-Green Deployment
Zero-downtime deployment strategy:

```bash
#!/bin/bash
# scripts/blue-green-deploy.sh

# Deploy to staging environment (green)
echo "Deploying to staging environment..."
vercel --target staging

# Run health checks
echo "Running health checks..."
./scripts/health-check.sh staging

# Promote to production if healthy
if [ $? -eq 0 ]; then
  echo "Health checks passed. Promoting to production..."
  vercel --prod
else
  echo "Health checks failed. Deployment aborted."
  exit 1
fi
```

### Rollback Procedures
Quick rollback capabilities for production issues:

```bash
#!/bin/bash
# scripts/rollback.sh

PREVIOUS_DEPLOYMENT_ID=$1

if [ -z "$PREVIOUS_DEPLOYMENT_ID" ]; then
  echo "Usage: ./rollback.sh <deployment-id>"
  exit 1
fi

echo "Rolling back to deployment: $PREVIOUS_DEPLOYMENT_ID"
vercel rollback $PREVIOUS_DEPLOYMENT_ID --prod

echo "Rollback completed. Running health checks..."
./scripts/health-check.sh production
```

## Security Configuration

### SSL/TLS Configuration
Automatic SSL certificate management:

```json
{
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "Strict-Transport-Security",
          "value": "max-age=31536000; includeSubDomains"
        },
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "Referrer-Policy",
          "value": "strict-origin-when-cross-origin"
        }
      ]
    }
  ]
}
```

### API Security
Rate limiting and security headers for API endpoints:

```javascript
// middleware/security.js
export function securityMiddleware(req, res, next) {
  // Rate limiting
  const rateLimiter = new RateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100 // limit each IP to 100 requests per windowMs
  });
  
  // Security headers
  res.setHeader('X-API-Version', '1.0');
  res.setHeader('X-RateLimit-Limit', '100');
  res.setHeader('X-RateLimit-Remaining', rateLimiter.remaining);
  
  return rateLimiter(req, res, next);
}
```

### Database Security
Row Level Security (RLS) policies and access control:

```sql
-- Enable RLS on all tables
ALTER TABLE inventory_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE voice_event_audit ENABLE ROW LEVEL SECURITY;

-- User access policies
CREATE POLICY "Users can access their own data" ON inventory_events
  FOR ALL USING (auth.uid() = user_id);

-- Admin access policies
CREATE POLICY "Admins can access all data" ON inventory_events
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_roles 
      WHERE user_id = auth.uid() 
      AND role = 'admin'
    )
  );
```

## Monitoring and Observability

### Application Monitoring
Comprehensive monitoring setup:

```typescript
// monitoring/setup.ts
export const monitoringConfig = {
  // Performance monitoring
  performance: {
    enabled: true,
    sampleRate: 1.0,
    trackWebVitals: true,
    trackUserInteractions: true
  },
  
  // Error tracking
  errorTracking: {
    enabled: true,
    captureUnhandledRejections: true,
    captureConsoleErrors: true,
    filterSensitiveData: true
  },
  
  // Custom metrics
  customMetrics: {
    voiceProcessingTime: true,
    databaseQueryTime: true,
    apiResponseTime: true,
    userEngagement: true
  }
};
```

### Health Checks
Automated health monitoring:

```typescript
// monitoring/health-checks.ts
export const healthChecks = {
  database: async () => {
    const { data, error } = await supabase
      .from('products')
      .select('count')
      .limit(1);
    
    return { healthy: !error, details: error?.message };
  },
  
  voiceProcessing: async () => {
    try {
      const response = await fetch('/api/voice-realtime-check');
      const data = await response.json();
      return { healthy: data.success, details: data };
    } catch (error) {
      return { healthy: false, details: error.message };
    }
  },
  
  storage: async () => {
    try {
      const { data, error } = await supabase.storage
        .from('voice-recordings')
        .list('', { limit: 1 });
      
      return { healthy: !error, details: error?.message };
    } catch (error) {
      return { healthy: false, details: error.message };
    }
  }
};
```

### Alerting Configuration
Automated alerting for critical issues:

```typescript
// monitoring/alerts.ts
export const alertConfig = {
  errorRate: {
    threshold: 5, // 5% error rate
    window: '5m',
    severity: 'critical'
  },
  
  responseTime: {
    threshold: 2000, // 2 seconds
    window: '5m',
    severity: 'warning'
  },
  
  databaseConnections: {
    threshold: 80, // 80% of pool
    window: '1m',
    severity: 'warning'
  },
  
  voiceProcessingQueue: {
    threshold: 100, // 100 pending items
    window: '2m',
    severity: 'critical'
  }
};
```

## Performance Optimization

### CDN Configuration
Content delivery network optimization:

```json
{
  "headers": [
    {
      "source": "/static/(.*)",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "public, max-age=31536000, immutable"
        }
      ]
    },
    {
      "source": "/api/(.*)",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "no-cache, no-store, must-revalidate"
        }
      ]
    }
  ]
}
```

### Build Optimization
Production build optimization:

```typescript
// vite.config.production.ts
export default defineConfig({
  build: {
    target: 'es2020',
    minify: 'terser',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          supabase: ['@supabase/supabase-js'],
          ui: ['@radix-ui/react-select', '@radix-ui/react-toast']
        }
      }
    }
  },
  
  define: {
    'process.env.NODE_ENV': '"production"'
  }
});
```

### Database Optimization
Database performance tuning:

```sql
-- Indexes for voice event queries
CREATE INDEX CONCURRENTLY idx_inventory_events_voice_search 
ON inventory_events USING gin(to_tsvector('english', transcription))
WHERE transcription IS NOT NULL;

CREATE INDEX CONCURRENTLY idx_inventory_events_user_created 
ON inventory_events (user_id, created_at DESC);

CREATE INDEX CONCURRENTLY idx_inventory_events_confidence 
ON inventory_events (confidence_score DESC)
WHERE confidence_score IS NOT NULL;

-- Connection pooling configuration
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
```

## Disaster Recovery

### Backup Strategy
Comprehensive backup and recovery procedures:

```bash
#!/bin/bash
# scripts/backup.sh

# Database backup
echo "Creating database backup..."
supabase db dump > "backups/db-$(date +%Y%m%d-%H%M%S).sql"

# Storage backup
echo "Backing up storage files..."
supabase storage download --recursive voice-recordings "backups/storage-$(date +%Y%m%d-%H%M%S)/"

# Configuration backup
echo "Backing up configuration..."
cp -r infrastructure/ "backups/config-$(date +%Y%m%d-%H%M%S)/"

echo "Backup completed successfully"
```

### Recovery Procedures
Step-by-step recovery process:

```bash
#!/bin/bash
# scripts/recover.sh

BACKUP_DATE=$1

if [ -z "$BACKUP_DATE" ]; then
  echo "Usage: ./recover.sh <backup-date>"
  exit 1
fi

echo "Starting recovery from backup: $BACKUP_DATE"

# Restore database
echo "Restoring database..."
supabase db reset
psql -f "backups/db-$BACKUP_DATE.sql"

# Restore storage
echo "Restoring storage files..."
supabase storage upload --recursive "backups/storage-$BACKUP_DATE/" voice-recordings/

# Verify recovery
echo "Verifying recovery..."
./scripts/health-check.sh

echo "Recovery completed successfully"
```

## Cost Optimization

### Resource Monitoring
Track and optimize cloud resource costs:

```typescript
// monitoring/cost-tracking.ts
export const costMetrics = {
  // Vercel usage tracking
  vercelUsage: {
    functions: 'track execution time and invocations',
    bandwidth: 'monitor data transfer',
    builds: 'track build minutes'
  },
  
  // Supabase usage tracking
  supabaseUsage: {
    database: 'monitor connection hours',
    storage: 'track file storage usage',
    bandwidth: 'monitor data transfer'
  },
  
  // OpenAI API usage
  openaiUsage: {
    whisper: 'track audio processing minutes',
    tokens: 'monitor token usage'
  }
};
```

### Optimization Strategies
Cost reduction techniques:

```typescript
// optimization/cost-reduction.ts
export const optimizations = {
  // Function optimization
  functions: {
    coldStartReduction: 'Keep functions warm during peak hours',
    executionOptimization: 'Optimize function execution time',
    memoryTuning: 'Right-size function memory allocation'
  },
  
  // Storage optimization
  storage: {
    compression: 'Compress audio files before storage',
    lifecycle: 'Implement file lifecycle policies',
    cleanup: 'Regular cleanup of expired files'
  },
  
  // API optimization
  api: {
    caching: 'Cache frequently accessed data',
    batching: 'Batch API requests where possible',
    rateOptimization: 'Optimize API call frequency'
  }
};
```

## Future Infrastructure Plans

### Scalability Enhancements
- Multi-region deployment for global performance
- Advanced caching strategies with Redis
- Message queue implementation for async processing
- Microservices architecture for complex features

### Security Improvements
- Advanced threat detection and prevention
- Enhanced audit logging and compliance
- Zero-trust security model implementation
- Advanced encryption for sensitive data

### Monitoring Enhancements
- Advanced analytics and business intelligence
- Predictive monitoring and alerting
- Custom dashboards for stakeholders
- Integration with external monitoring tools

### Cost Optimization
- Reserved capacity planning
- Advanced resource scheduling
- Multi-cloud cost comparison
- Automated cost optimization recommendations