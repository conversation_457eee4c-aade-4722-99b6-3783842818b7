const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY
);

async function createViews() {
  console.log('🔧 Creating TempStick Dashboard Views...');
  
  try {
    // Create sensor status dashboard view
    console.log('📊 Creating sensor_status_dashboard view...');
    const sensorViewSQL = `
      CREATE OR REPLACE VIEW sensor_status_dashboard AS
      SELECT 
        s.id,
        s.sensor_id,
        s.name,
        s.device_name,
        s.location_description,
        s.is_online,
        s.last_seen_at,
        s.connection_status,
        s.battery_level,
        s.signal_strength,
        sa.name as storage_area_name,
        sa.area_type,
        sa.location as storage_area_location,
        -- Latest reading
        tr.temp_celsius as latest_temp_celsius,
        tr.temp_fahrenheit as latest_temp_fahrenheit,
        tr.humidity as latest_humidity,
        tr.recorded_at as latest_reading_at,
        tr.within_safe_range,
        -- Alert counts
        (SELECT COUNT(*) FROM temperature_alerts ta 
         WHERE ta.sensor_id = s.id AND ta.alert_status = 'active') as active_alerts,
        s.user_id
      FROM sensors s
      LEFT JOIN storage_areas sa ON s.storage_area_id = sa.id
      LEFT JOIN LATERAL (
        SELECT temp_celsius, temp_fahrenheit, humidity, recorded_at, within_safe_range
        FROM temperature_readings tr2 
        WHERE tr2.sensor_id = s.id 
        ORDER BY tr2.recorded_at DESC 
        LIMIT 1
      ) tr ON true;
    `;
    
    const { error: sensorError } = await supabase.rpc('exec', { sql: sensorViewSQL });
    if (sensorError) {
      console.log('❌ Sensor view creation failed:', sensorError.message);
    } else {
      console.log('✅ sensor_status_dashboard view created');
    }
    
    // Create HACCP compliance dashboard view
    console.log('📊 Creating haccp_compliance_dashboard view...');
    const haccpViewSQL = `
      CREATE OR REPLACE VIEW haccp_compliance_dashboard AS
      SELECT 
        sa.id as storage_area_id,
        sa.name as storage_area_name,
        sa.area_type,
        sa.location,
        sa.haccp_required,
        sa.haccp_ccp_number,
        sa.temp_min_celsius,
        sa.temp_max_celsius,
        sa.temp_min_fahrenheit,
        sa.temp_max_fahrenheit,
        -- Compliance status
        CASE 
          WHEN sa.haccp_required = false THEN 'not_required'
          WHEN COUNT(s.id) = 0 THEN 'no_sensors'
          WHEN COUNT(CASE WHEN s.is_online = true THEN 1 END) = 0 THEN 'offline'
          WHEN COUNT(CASE WHEN ta.id IS NOT NULL AND ta.haccp_violation = true THEN 1 END) > 0 THEN 'violation'
          ELSE 'compliant'
        END as compliance_status,
        -- Sensor counts
        COUNT(s.id) as total_sensors,
        COUNT(CASE WHEN s.is_online = true THEN 1 END) as online_sensors,
        -- Alert counts
        COUNT(CASE WHEN ta.id IS NOT NULL AND ta.alert_status = 'active' THEN 1 END) as active_alerts,
        COUNT(CASE WHEN ta.id IS NOT NULL AND ta.haccp_violation = true THEN 1 END) as haccp_violations,
        -- Latest readings
        MAX(tr.recorded_at) as latest_reading_at,
        AVG(tr.temp_celsius) as avg_temp_celsius,
        MIN(tr.temp_celsius) as min_temp_celsius,
        MAX(tr.temp_celsius) as max_temp_celsius,
        sa.user_id
      FROM storage_areas sa
      LEFT JOIN sensors s ON sa.id = s.storage_area_id
      LEFT JOIN temperature_readings tr ON s.id = tr.sensor_id 
        AND tr.recorded_at > NOW() - INTERVAL '24 hours'
      LEFT JOIN temperature_alerts ta ON s.id = ta.sensor_id 
        AND ta.alert_status = 'active'
      GROUP BY sa.id, sa.name, sa.area_type, sa.location, sa.haccp_required, 
               sa.haccp_ccp_number, sa.temp_min_celsius, sa.temp_max_celsius,
               sa.temp_min_fahrenheit, sa.temp_max_fahrenheit, sa.user_id;
    `;
    
    const { error: haccpError } = await supabase.rpc('exec', { sql: haccpViewSQL });
    if (haccpError) {
      console.log('❌ HACCP view creation failed:', haccpError.message);
    } else {
      console.log('✅ haccp_compliance_dashboard view created');
    }
    
    // Grant permissions
    console.log('🔐 Granting view permissions...');
    const permissionsSQL = `
      GRANT SELECT ON sensor_status_dashboard TO authenticated;
      GRANT SELECT ON haccp_compliance_dashboard TO authenticated;
    `;
    
    const { error: permError } = await supabase.rpc('exec', { sql: permissionsSQL });
    if (permError) {
      console.log('❌ Permission grant failed:', permError.message);
    } else {
      console.log('✅ View permissions granted');
    }
    
    // Test views
    console.log('\n🧪 Testing views...');
    
    const { data: sensorData, error: sensorTestError } = await supabase
      .from('sensor_status_dashboard')
      .select('*')
      .limit(1);
    
    if (sensorTestError) {
      console.log('❌ Sensor view test failed:', sensorTestError.message);
    } else {
      console.log('✅ sensor_status_dashboard view accessible');
    }
    
    const { data: haccpData, error: haccpTestError } = await supabase
      .from('haccp_compliance_dashboard')
      .select('*')
      .limit(1);
    
    if (haccpTestError) {
      console.log('❌ HACCP view test failed:', haccpTestError.message);
    } else {
      console.log('✅ haccp_compliance_dashboard view accessible');
    }
    
    console.log('\n🎉 TempStick dashboard views created successfully!');
    
  } catch (error) {
    console.error('❌ Error creating views:', error.message);
  }
}

createViews();
