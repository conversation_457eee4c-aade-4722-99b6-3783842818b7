const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function updateSchema() {
  try {
    console.log('Reading schema update SQL file...');
    
    // Read the SQL migration file
    const sqlFilePath = './migrations/016_add_fulfillment_cogs_haccp_tables.sql';
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf-8');
    
    // Split the SQL file into individual statements (roughly)
    const statements = sqlContent
      .replace(/\/\*[\s\S]*?\*\//g, '') // Remove multi-line comments
      .replace(/--.*$/gm, '') // Remove single line comments
      .split(';') // Split on semicolons
      .map(stmt => stmt.trim()) // Trim each statement
      .filter(stmt => stmt.length > 0); // Remove empty statements
    
    console.log(`Found ${statements.length} SQL statements to execute.`);
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(`Executing statement ${i + 1}/${statements.length}...`);
      
      // Log a preview of the statement (first 100 chars)
      console.log(`Preview: ${statement.substring(0, 100)}...`);
      
      try {
        // Execute the statement using rpc
        const { data, error } = await supabase.rpc('execute_sql', {
          sql: statement
        });
        
        if (error) {
          console.error(`Error executing statement ${i + 1}:`, error);
          continue;
        }
        
        console.log(`Statement ${i + 1} executed successfully.`);
      } catch (stmtError) {
        console.error(`Error executing statement ${i + 1}:`, stmtError);
      }
    }
    
    console.log('Schema update completed!');
  } catch (error) {
    console.error('Error updating schema:', error);
  }
}

// Execute the schema update
updateSchema().catch(error => {
  console.error('Unhandled error:', error);
});
