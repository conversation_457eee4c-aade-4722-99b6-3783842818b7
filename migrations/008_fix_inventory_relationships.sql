-- Drop existing relationships if any
ALTER TABLE IF EXISTS inventory DROP CONSTRAINT IF EXISTS inventory_product_id_fkey;

-- Add Foreign Key relationships
ALTER TABLE inventory 
ADD CONSTRAINT inventory_product_id_fkey 
FOREIGN KEY (product_id) REFERENCES products(id);

-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_inventory_product_id ON inventory(product_id);

COMMENT ON CONSTRAINT inventory_product_id_fkey ON inventory IS 'Links inventory events to products';
