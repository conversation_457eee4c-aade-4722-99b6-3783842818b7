-- Create categories table if it doesn't exist
CREATE TABLE IF NOT EXISTS categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create trigger for updated_at
CREATE TRIGGER update_categories_updated_at
    BEFORE UPDATE ON categories
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add unique constraint on name
ALTER TABLE categories
ADD CONSTRAINT categories_name_key UNIQUE (name);

-- Insert required categories
INSERT INTO categories (name)
VALUES 
  ('Receiving'),
  ('Disposal'),
  ('Physical Count'),
  ('Re-processing')
ON CONFLICT (name) DO NOTHING;
