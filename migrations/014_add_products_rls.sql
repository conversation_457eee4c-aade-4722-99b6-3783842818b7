-- Enable RLS on products table
ALTER TABLE products ENABLE ROW LEVEL SECURITY;

-- Create policies for products table
CREATE POLICY "Enable read access for all users" ON products
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Enable insert for authenticated users" ON products
    FOR INSERT
    TO authenticated
    WITH CHECK (true);

CREATE POLICY "Enable update for authenticated users" ON products
    FOR UPDATE
    TO authenticated
    USING (true)
    WITH CHECK (true);

CREATE POLICY "Enable delete for authenticated users" ON products
    FOR DELETE
    TO authenticated
    USING (true);

-- Also add RLS policies for vendors table since it's referenced by products
ALTER TABLE vendors ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Enable read access for all users" ON vendors
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Enable insert for authenticated users" ON vendors
    FOR INSERT
    TO authenticated
    WITH CHECK (true);

CREATE POLICY "Enable update for authenticated users" ON vendors
    FOR UPDATE
    TO authenticated
    USING (true)
    WITH CHECK (true);
