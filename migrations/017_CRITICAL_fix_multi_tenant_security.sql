-- CRITICAL SECURITY FIX: Multi-tenant data isolation
-- This migration addresses the complete failure of RLS policies
-- All existing policies allow ANY authenticated user to access ALL data
-- 
-- CHANGES:
-- 1. Add user_id/created_by columns to all user-sensitive tables
-- 2. Remove the anonymous user security vulnerability
-- 3. Replace broken RLS policies with proper user-based isolation
-- 4. Add indexes for RLS performance
-- 
-- WARNING: This will break existing data access patterns
-- Run in maintenance mode with user notification

BEGIN;

-- =============================================
-- STEP 1: Remove Anonymous User Vulnerability
-- =============================================

-- Remove dangerous anonymous user account
DELETE FROM auth.users WHERE id = '********-0000-0000-0000-************';

-- Drop anonymous access policies
DROP POLICY IF EXISTS "Allow anonymous read access" ON categories;
DROP POLICY IF EXISTS "Allow anonymous read access" ON products;
DROP POLICY IF EXISTS "Allow anonymous read access" ON inventory;

-- =============================================
-- STEP 2: Add User Ownership Columns
-- =============================================

-- Add user_id to core tables for proper isolation
ALTER TABLE vendors ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES auth.users(id);
ALTER TABLE customers ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES auth.users(id);
ALTER TABLE products ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES auth.users(id);
ALTER TABLE batches ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES auth.users(id);
ALTER TABLE events ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES auth.users(id);

-- Add user_id to inventory system tables
ALTER TABLE inventory_events ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES auth.users(id);
ALTER TABLE inventory_transactions ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES auth.users(id);
ALTER TABLE inventory_snapshots ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES auth.users(id);

-- Add user_id to HACCP and compliance tables
ALTER TABLE haccp_logs ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES auth.users(id);
ALTER TABLE haccp_audits ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES auth.users(id);
ALTER TABLE cogs ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES auth.users(id);
ALTER TABLE fulfillments ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES auth.users(id);

-- Add user_id to traceability tables
ALTER TABLE gdst_traceability ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES auth.users(id);
ALTER TABLE partners ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES auth.users(id);
ALTER TABLE locations ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES auth.users(id);
ALTER TABLE lots ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES auth.users(id);
ALTER TABLE traceability_events ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES auth.users(id);

-- Add user_id to calendar system
ALTER TABLE calendar_events ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES auth.users(id);

-- =============================================
-- STEP 3: Drop All Broken RLS Policies
-- =============================================

-- Drop all broken policies that use USING (true)
DROP POLICY IF EXISTS "Enable read access for all users" ON categories;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON categories;
DROP POLICY IF EXISTS "Enable update for authenticated users" ON categories;

DROP POLICY IF EXISTS "Enable read access for all users" ON inventory;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON inventory;
DROP POLICY IF EXISTS "Enable update for authenticated users" ON inventory;

DROP POLICY IF EXISTS "Enable read access for all users" ON products;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON products;
DROP POLICY IF EXISTS "Enable update for authenticated users" ON products;
DROP POLICY IF EXISTS "Enable delete for authenticated users" ON products;

DROP POLICY IF EXISTS "Enable read access for all users" ON vendors;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON vendors;
DROP POLICY IF EXISTS "Enable update for authenticated users" ON vendors;
DROP POLICY IF EXISTS "Enable delete for authenticated users" ON vendors;

DROP POLICY IF EXISTS "Enable read access for authenticated users" ON vendors;
DROP POLICY IF EXISTS "Enable insert access for authenticated users" ON vendors;
DROP POLICY IF EXISTS "Enable update access for authenticated users" ON vendors;
DROP POLICY IF EXISTS "Enable delete access for authenticated users" ON vendors;

DROP POLICY IF EXISTS "Enable read access for authenticated users" ON customers;
DROP POLICY IF EXISTS "Enable insert access for authenticated users" ON customers;
DROP POLICY IF EXISTS "Enable update access for authenticated users" ON customers;
DROP POLICY IF EXISTS "Enable delete access for authenticated users" ON customers;

DROP POLICY IF EXISTS "Enable read access for authenticated users" ON events;
DROP POLICY IF EXISTS "Enable insert access for authenticated users" ON events;
DROP POLICY IF EXISTS "Enable update access for authenticated users" ON events;
DROP POLICY IF EXISTS "Enable delete access for authenticated users" ON events;

-- =============================================
-- STEP 4: Create Proper User-Based RLS Policies
-- =============================================

-- Vendors/Suppliers Policies
CREATE POLICY "Users can only view their own vendors" ON vendors
    FOR SELECT USING (created_by = auth.uid());

CREATE POLICY "Users can only insert their own vendors" ON vendors
    FOR INSERT WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can only update their own vendors" ON vendors
    FOR UPDATE USING (created_by = auth.uid()) WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can only delete their own vendors" ON vendors
    FOR DELETE USING (created_by = auth.uid());

-- Customers Policies
CREATE POLICY "Users can only view their own customers" ON customers
    FOR SELECT USING (created_by = auth.uid());

CREATE POLICY "Users can only insert their own customers" ON customers
    FOR INSERT WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can only update their own customers" ON customers
    FOR UPDATE USING (created_by = auth.uid()) WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can only delete their own customers" ON customers
    FOR DELETE USING (created_by = auth.uid());

-- Products Policies
CREATE POLICY "Users can only view their own products" ON products
    FOR SELECT USING (created_by = auth.uid());

CREATE POLICY "Users can only insert their own products" ON products
    FOR INSERT WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can only update their own products" ON products
    FOR UPDATE USING (created_by = auth.uid()) WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can only delete their own products" ON products
    FOR DELETE USING (created_by = auth.uid());

-- Batches Policies
CREATE POLICY "Users can only view their own batches" ON batches
    FOR SELECT USING (created_by = auth.uid());

CREATE POLICY "Users can only insert their own batches" ON batches
    FOR INSERT WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can only update their own batches" ON batches
    FOR UPDATE USING (created_by = auth.uid()) WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can only delete their own batches" ON batches
    FOR DELETE USING (created_by = auth.uid());

-- Events/Inventory Events Policies
CREATE POLICY "Users can only view their own events" ON events
    FOR SELECT USING (created_by = auth.uid());

CREATE POLICY "Users can only insert their own events" ON events
    FOR INSERT WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can only update their own events" ON events
    FOR UPDATE USING (created_by = auth.uid()) WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can only delete their own events" ON events
    FOR DELETE USING (created_by = auth.uid());

-- =============================================
-- STEP 5: Add RLS to Tables Missing It
-- =============================================

-- Enable RLS on tables that don't have it yet
ALTER TABLE inventory_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory_snapshots ENABLE ROW LEVEL SECURITY;
ALTER TABLE haccp_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE haccp_audits ENABLE ROW LEVEL SECURITY;
ALTER TABLE cogs ENABLE ROW LEVEL SECURITY;
ALTER TABLE fulfillments ENABLE ROW LEVEL SECURITY;
ALTER TABLE gdst_traceability ENABLE ROW LEVEL SECURITY;
ALTER TABLE partners ENABLE ROW LEVEL SECURITY;
ALTER TABLE locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE lots ENABLE ROW LEVEL SECURITY;
ALTER TABLE traceability_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE event_lots ENABLE ROW LEVEL SECURITY;
ALTER TABLE calendar_events ENABLE ROW LEVEL SECURITY;

-- =============================================
-- STEP 6: Create RLS Policies for All Tables
-- =============================================

-- Inventory Events Policies
CREATE POLICY "Users can only view their own inventory_events" ON inventory_events
    FOR SELECT USING (created_by = auth.uid());

CREATE POLICY "Users can only insert their own inventory_events" ON inventory_events
    FOR INSERT WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can only update their own inventory_events" ON inventory_events
    FOR UPDATE USING (created_by = auth.uid()) WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can only delete their own inventory_events" ON inventory_events
    FOR DELETE USING (created_by = auth.uid());

-- Inventory Transactions Policies
CREATE POLICY "Users can only view their own inventory_transactions" ON inventory_transactions
    FOR SELECT USING (created_by = auth.uid());

CREATE POLICY "Users can only insert their own inventory_transactions" ON inventory_transactions
    FOR INSERT WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can only update their own inventory_transactions" ON inventory_transactions
    FOR UPDATE USING (created_by = auth.uid()) WITH CHECK (created_by = auth.uid());

-- HACCP Logs Policies
CREATE POLICY "Users can only view their own haccp_logs" ON haccp_logs
    FOR SELECT USING (created_by = auth.uid());

CREATE POLICY "Users can only insert their own haccp_logs" ON haccp_logs
    FOR INSERT WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can only update their own haccp_logs" ON haccp_logs
    FOR UPDATE USING (created_by = auth.uid()) WITH CHECK (created_by = auth.uid());

-- HACCP Audits Policies
CREATE POLICY "Users can only view their own haccp_audits" ON haccp_audits
    FOR SELECT USING (created_by = auth.uid());

CREATE POLICY "Users can only insert their own haccp_audits" ON haccp_audits
    FOR INSERT WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can only update their own haccp_audits" ON haccp_audits
    FOR UPDATE USING (created_by = auth.uid()) WITH CHECK (created_by = auth.uid());

-- COGS Policies
CREATE POLICY "Users can only view their own cogs" ON cogs
    FOR SELECT USING (created_by = auth.uid());

CREATE POLICY "Users can only insert their own cogs" ON cogs
    FOR INSERT WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can only update their own cogs" ON cogs
    FOR UPDATE USING (created_by = auth.uid()) WITH CHECK (created_by = auth.uid());

-- Fulfillments Policies
CREATE POLICY "Users can only view their own fulfillments" ON fulfillments
    FOR SELECT USING (created_by = auth.uid());

CREATE POLICY "Users can only insert their own fulfillments" ON fulfillments
    FOR INSERT WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can only update their own fulfillments" ON fulfillments
    FOR UPDATE USING (created_by = auth.uid()) WITH CHECK (created_by = auth.uid());

-- GDST Traceability Policies
CREATE POLICY "Users can only view their own gdst_traceability" ON gdst_traceability
    FOR SELECT USING (created_by = auth.uid());

CREATE POLICY "Users can only insert their own gdst_traceability" ON gdst_traceability
    FOR INSERT WITH CHECK (created_by = auth.uid());

-- Partners Policies
CREATE POLICY "Users can only view their own partners" ON partners
    FOR SELECT USING (created_by = auth.uid());

CREATE POLICY "Users can only insert their own partners" ON partners
    FOR INSERT WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can only update their own partners" ON partners
    FOR UPDATE USING (created_by = auth.uid()) WITH CHECK (created_by = auth.uid());

-- Locations Policies (inherits from partner ownership)
CREATE POLICY "Users can only view locations of their partners" ON locations
    FOR SELECT USING (
        partner_id IN (SELECT id FROM partners WHERE created_by = auth.uid())
    );

CREATE POLICY "Users can only insert locations for their partners" ON locations
    FOR INSERT WITH CHECK (
        partner_id IN (SELECT id FROM partners WHERE created_by = auth.uid())
    );

CREATE POLICY "Users can only update locations of their partners" ON locations
    FOR UPDATE USING (
        partner_id IN (SELECT id FROM partners WHERE created_by = auth.uid())
    );

-- Lots Policies
CREATE POLICY "Users can only view their own lots" ON lots
    FOR SELECT USING (created_by = auth.uid());

CREATE POLICY "Users can only insert their own lots" ON lots
    FOR INSERT WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can only update their own lots" ON lots
    FOR UPDATE USING (created_by = auth.uid()) WITH CHECK (created_by = auth.uid());

-- Traceability Events Policies
CREATE POLICY "Users can only view their own traceability_events" ON traceability_events
    FOR SELECT USING (created_by = auth.uid());

CREATE POLICY "Users can only insert their own traceability_events" ON traceability_events
    FOR INSERT WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can only update their own traceability_events" ON traceability_events
    FOR UPDATE USING (created_by = auth.uid()) WITH CHECK (created_by = auth.uid());

-- Event Lots Policies (inherits from event ownership)
CREATE POLICY "Users can only view event_lots for their events" ON event_lots
    FOR SELECT USING (
        event_id IN (SELECT id FROM traceability_events WHERE created_by = auth.uid())
    );

CREATE POLICY "Users can only insert event_lots for their events" ON event_lots
    FOR INSERT WITH CHECK (
        event_id IN (SELECT id FROM traceability_events WHERE created_by = auth.uid())
    );

-- Calendar Events Policies
CREATE POLICY "Users can only view their own calendar_events" ON calendar_events
    FOR SELECT USING (created_by = auth.uid());

CREATE POLICY "Users can only insert their own calendar_events" ON calendar_events
    FOR INSERT WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can only update their own calendar_events" ON calendar_events
    FOR UPDATE USING (created_by = auth.uid()) WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can only delete their own calendar_events" ON calendar_events
    FOR DELETE USING (created_by = auth.uid());

-- =============================================
-- STEP 7: Categories - Special Shared Resource
-- =============================================

-- Categories can be shared system-wide or user-specific
-- Add both system and user categories support
ALTER TABLE categories ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES auth.users(id);
ALTER TABLE categories ADD COLUMN IF NOT EXISTS is_system BOOLEAN DEFAULT false;

-- Categories policies: Users can see system categories + their own
CREATE POLICY "Users can view system categories and their own" ON categories
    FOR SELECT USING (is_system = true OR created_by = auth.uid());

CREATE POLICY "Users can only insert their own categories" ON categories
    FOR INSERT WITH CHECK (created_by = auth.uid() AND is_system = false);

CREATE POLICY "Users can only update their own categories" ON categories
    FOR UPDATE USING (created_by = auth.uid() AND is_system = false) 
    WITH CHECK (created_by = auth.uid() AND is_system = false);

-- =============================================
-- STEP 8: Performance Indexes for RLS
-- =============================================

-- Add indexes on created_by columns for RLS performance
CREATE INDEX IF NOT EXISTS idx_vendors_created_by ON vendors(created_by);
CREATE INDEX IF NOT EXISTS idx_customers_created_by ON customers(created_by);
CREATE INDEX IF NOT EXISTS idx_products_created_by ON products(created_by);
CREATE INDEX IF NOT EXISTS idx_batches_created_by ON batches(created_by);
CREATE INDEX IF NOT EXISTS idx_events_created_by ON events(created_by);
CREATE INDEX IF NOT EXISTS idx_inventory_events_created_by ON inventory_events(created_by);
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_created_by ON inventory_transactions(created_by);
CREATE INDEX IF NOT EXISTS idx_haccp_logs_created_by ON haccp_logs(created_by);
CREATE INDEX IF NOT EXISTS idx_haccp_audits_created_by ON haccp_audits(created_by);
CREATE INDEX IF NOT EXISTS idx_cogs_created_by ON cogs(created_by);
CREATE INDEX IF NOT EXISTS idx_fulfillments_created_by ON fulfillments(created_by);
CREATE INDEX IF NOT EXISTS idx_gdst_traceability_created_by ON gdst_traceability(created_by);
CREATE INDEX IF NOT EXISTS idx_partners_created_by ON partners(created_by);
CREATE INDEX IF NOT EXISTS idx_lots_created_by ON lots(created_by);
CREATE INDEX IF NOT EXISTS idx_traceability_events_created_by ON traceability_events(created_by);
CREATE INDEX IF NOT EXISTS idx_calendar_events_created_by ON calendar_events(created_by);
CREATE INDEX IF NOT EXISTS idx_categories_created_by ON categories(created_by);

-- =============================================
-- STEP 9: Update Triggers for User Assignment
-- =============================================

-- Create function to automatically set created_by
CREATE OR REPLACE FUNCTION set_created_by()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.created_by IS NULL THEN
        NEW.created_by := auth.uid();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add triggers to automatically set created_by on INSERT
CREATE TRIGGER set_vendors_created_by
    BEFORE INSERT ON vendors
    FOR EACH ROW EXECUTE FUNCTION set_created_by();

CREATE TRIGGER set_customers_created_by
    BEFORE INSERT ON customers
    FOR EACH ROW EXECUTE FUNCTION set_created_by();

CREATE TRIGGER set_products_created_by
    BEFORE INSERT ON products
    FOR EACH ROW EXECUTE FUNCTION set_created_by();

CREATE TRIGGER set_batches_created_by
    BEFORE INSERT ON batches
    FOR EACH ROW EXECUTE FUNCTION set_created_by();

CREATE TRIGGER set_events_created_by
    BEFORE INSERT ON events
    FOR EACH ROW EXECUTE FUNCTION set_created_by();

CREATE TRIGGER set_inventory_events_created_by
    BEFORE INSERT ON inventory_events
    FOR EACH ROW EXECUTE FUNCTION set_created_by();

CREATE TRIGGER set_inventory_transactions_created_by
    BEFORE INSERT ON inventory_transactions
    FOR EACH ROW EXECUTE FUNCTION set_created_by();

CREATE TRIGGER set_haccp_logs_created_by
    BEFORE INSERT ON haccp_logs
    FOR EACH ROW EXECUTE FUNCTION set_created_by();

CREATE TRIGGER set_haccp_audits_created_by
    BEFORE INSERT ON haccp_audits
    FOR EACH ROW EXECUTE FUNCTION set_created_by();

CREATE TRIGGER set_cogs_created_by
    BEFORE INSERT ON cogs
    FOR EACH ROW EXECUTE FUNCTION set_created_by();

CREATE TRIGGER set_fulfillments_created_by
    BEFORE INSERT ON fulfillments
    FOR EACH ROW EXECUTE FUNCTION set_created_by();

CREATE TRIGGER set_gdst_traceability_created_by
    BEFORE INSERT ON gdst_traceability
    FOR EACH ROW EXECUTE FUNCTION set_created_by();

CREATE TRIGGER set_partners_created_by
    BEFORE INSERT ON partners
    FOR EACH ROW EXECUTE FUNCTION set_created_by();

CREATE TRIGGER set_lots_created_by
    BEFORE INSERT ON lots
    FOR EACH ROW EXECUTE FUNCTION set_created_by();

CREATE TRIGGER set_traceability_events_created_by
    BEFORE INSERT ON traceability_events
    FOR EACH ROW EXECUTE FUNCTION set_created_by();

CREATE TRIGGER set_calendar_events_created_by
    BEFORE INSERT ON calendar_events
    FOR EACH ROW EXECUTE FUNCTION set_created_by();

-- =============================================
-- STEP 10: Fix Calendar Events Sync Function
-- =============================================

-- Update calendar sync function to respect user ownership
CREATE OR REPLACE FUNCTION sync_calendar_from_inventory_events()
RETURNS TRIGGER AS $$
DECLARE
  v_title text;
  v_desc text;
BEGIN
  IF (TG_OP = 'INSERT') THEN
    v_title := coalesce(NEW.event_type, 'event');
    v_desc := NULLIF(NEW.notes, '');
    INSERT INTO calendar_events (
      title, description, start_at, end_at, all_day,
      source, source_id, inventory_event_id,
      event_type, product_id, metadata, created_by
    ) VALUES (
      v_title, v_desc, NEW.created_at, NULL, false,
      'inventory_events', NEW.id, NEW.id,
      NEW.event_type, NEW.product_id,
      coalesce(NEW.metadata, '{}'::jsonb) || jsonb_build_object(
        'quantity', NEW.quantity,
        'unit_price', NEW.unit_price,
        'total_amount', NEW.total_amount,
        'images', coalesce(NEW.images, ARRAY[]::text[])
      ),
      NEW.created_by  -- ✅ Respect user ownership
    );
    RETURN NEW;
  ELSIF (TG_OP = 'UPDATE') THEN
    v_title := coalesce(NEW.event_type, 'event');
    v_desc := NULLIF(NEW.notes, '');
    UPDATE calendar_events SET
      title = v_title,
      description = v_desc,
      start_at = NEW.created_at,
      end_at = NULL,
      all_day = false,
      event_type = NEW.event_type,
      product_id = NEW.product_id,
      metadata = coalesce(NEW.metadata, '{}'::jsonb) || jsonb_build_object(
        'quantity', NEW.quantity,
        'unit_price', NEW.unit_price,
        'total_amount', NEW.total_amount,
        'images', coalesce(NEW.images, ARRAY[]::text[])
      ),
      updated_at = now()
    WHERE inventory_event_id = NEW.id;
    RETURN NEW;
  ELSIF (TG_OP = 'DELETE') THEN
    DELETE FROM calendar_events WHERE inventory_event_id = OLD.id;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- STEP 11: Mark System Categories
-- =============================================

-- Mark existing categories as system categories (visible to all users)
UPDATE categories SET is_system = true, created_by = NULL WHERE created_by IS NULL;

COMMIT;

-- =============================================
-- VALIDATION QUERIES (Run after migration)
-- =============================================

-- These queries should return 0 rows if RLS is working properly:
-- 
-- SELECT COUNT(*) FROM products WHERE created_by != auth.uid();
-- SELECT COUNT(*) FROM customers WHERE created_by != auth.uid();
-- SELECT COUNT(*) FROM vendors WHERE created_by != auth.uid();
-- SELECT COUNT(*) FROM events WHERE created_by != auth.uid();
-- 
-- System categories should still be visible:
-- SELECT COUNT(*) FROM categories WHERE is_system = true;