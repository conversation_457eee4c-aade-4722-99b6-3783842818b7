-- <PERSON><PERSON><PERSON><PERSON><PERSON>K MIGRATION for 017_CRITICAL_fix_multi_tenant_security.sql
-- Use this ONLY if the security fix causes critical application failures
-- WARNING: This will restore the security vulnerability!

BEGIN;

-- =============================================
-- STEP 1: Drop User-Based RLS Policies
-- =============================================

-- Drop vendor policies
DROP POLICY IF EXISTS "Users can only view their own vendors" ON vendors;
DROP POLICY IF EXISTS "Users can only insert their own vendors" ON vendors;
DROP POLICY IF EXISTS "Users can only update their own vendors" ON vendors;
DROP POLICY IF EXISTS "Users can only delete their own vendors" ON vendors;

-- Drop customer policies
DROP POLICY IF EXISTS "Users can only view their own customers" ON customers;
DROP POLICY IF EXISTS "Users can only insert their own customers" ON customers;
DROP POLICY IF EXISTS "Users can only update their own customers" ON customers;
DROP POLICY IF EXISTS "Users can only delete their own customers" ON customers;

-- Drop product policies
DROP POLICY IF EXISTS "Users can only view their own products" ON products;
DROP POLICY IF EXISTS "Users can only insert their own products" ON products;
DROP POLICY IF EXISTS "Users can only update their own products" ON products;
DROP POLICY IF EXISTS "Users can only delete their own products" ON products;

-- Drop all other user-based policies
DROP POLICY IF EXISTS "Users can only view their own batches" ON batches;
DROP POLICY IF EXISTS "Users can only insert their own batches" ON batches;
DROP POLICY IF EXISTS "Users can only update their own batches" ON batches;
DROP POLICY IF EXISTS "Users can only delete their own batches" ON batches;

DROP POLICY IF EXISTS "Users can only view their own events" ON events;
DROP POLICY IF EXISTS "Users can only insert their own events" ON events;
DROP POLICY IF EXISTS "Users can only update their own events" ON events;
DROP POLICY IF EXISTS "Users can only delete their own events" ON events;

DROP POLICY IF EXISTS "Users can only view their own inventory_events" ON inventory_events;
DROP POLICY IF EXISTS "Users can only insert their own inventory_events" ON inventory_events;
DROP POLICY IF EXISTS "Users can only update their own inventory_events" ON inventory_events;
DROP POLICY IF EXISTS "Users can only delete their own inventory_events" ON inventory_events;

-- Drop all other table policies
DROP POLICY IF EXISTS "Users can only view their own inventory_transactions" ON inventory_transactions;
DROP POLICY IF EXISTS "Users can only insert their own inventory_transactions" ON inventory_transactions;
DROP POLICY IF EXISTS "Users can only update their own inventory_transactions" ON inventory_transactions;

DROP POLICY IF EXISTS "Users can only view their own haccp_logs" ON haccp_logs;
DROP POLICY IF EXISTS "Users can only insert their own haccp_logs" ON haccp_logs;
DROP POLICY IF EXISTS "Users can only update their own haccp_logs" ON haccp_logs;

DROP POLICY IF EXISTS "Users can only view their own haccp_audits" ON haccp_audits;
DROP POLICY IF EXISTS "Users can only insert their own haccp_audits" ON haccp_audits;
DROP POLICY IF EXISTS "Users can only update their own haccp_audits" ON haccp_audits;

DROP POLICY IF EXISTS "Users can only view their own cogs" ON cogs;
DROP POLICY IF EXISTS "Users can only insert their own cogs" ON cogs;
DROP POLICY IF EXISTS "Users can only update their own cogs" ON cogs;

DROP POLICY IF EXISTS "Users can only view their own fulfillments" ON fulfillments;
DROP POLICY IF EXISTS "Users can only insert their own fulfillments" ON fulfillments;
DROP POLICY IF EXISTS "Users can only update their own fulfillments" ON fulfillments;

DROP POLICY IF EXISTS "Users can only view their own gdst_traceability" ON gdst_traceability;
DROP POLICY IF EXISTS "Users can only insert their own gdst_traceability" ON gdst_traceability;

DROP POLICY IF EXISTS "Users can only view their own partners" ON partners;
DROP POLICY IF EXISTS "Users can only insert their own partners" ON partners;
DROP POLICY IF EXISTS "Users can only update their own partners" ON partners;

DROP POLICY IF EXISTS "Users can only view locations of their partners" ON locations;
DROP POLICY IF EXISTS "Users can only insert locations for their partners" ON locations;
DROP POLICY IF EXISTS "Users can only update locations of their partners" ON locations;

DROP POLICY IF EXISTS "Users can only view their own lots" ON lots;
DROP POLICY IF EXISTS "Users can only insert their own lots" ON lots;
DROP POLICY IF EXISTS "Users can only update their own lots" ON lots;

DROP POLICY IF EXISTS "Users can only view their own traceability_events" ON traceability_events;
DROP POLICY IF EXISTS "Users can only insert their own traceability_events" ON traceability_events;
DROP POLICY IF EXISTS "Users can only update their own traceability_events" ON traceability_events;

DROP POLICY IF EXISTS "Users can only view event_lots for their events" ON event_lots;
DROP POLICY IF EXISTS "Users can only insert event_lots for their events" ON event_lots;

DROP POLICY IF EXISTS "Users can only view their own calendar_events" ON calendar_events;
DROP POLICY IF EXISTS "Users can only insert their own calendar_events" ON calendar_events;
DROP POLICY IF EXISTS "Users can only update their own calendar_events" ON calendar_events;
DROP POLICY IF EXISTS "Users can only delete their own calendar_events" ON calendar_events;

-- Drop category policies
DROP POLICY IF EXISTS "Users can view system categories and their own" ON categories;
DROP POLICY IF EXISTS "Users can only insert their own categories" ON categories;
DROP POLICY IF EXISTS "Users can only update their own categories" ON categories;

-- =============================================
-- STEP 2: Restore Original Broken Policies
-- =============================================

-- WARNING: These policies are insecure and allow cross-user data access!

-- Categories policies (original broken ones)
CREATE POLICY "Enable read access for all users" ON categories
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Enable insert for authenticated users" ON categories
    FOR INSERT
    TO authenticated
    WITH CHECK (true);

CREATE POLICY "Enable update for authenticated users" ON categories
    FOR UPDATE
    TO authenticated
    USING (true)
    WITH CHECK (true);

-- Inventory policies (original broken ones)
CREATE POLICY "Enable read access for all users" ON inventory
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Enable insert for authenticated users" ON inventory
    FOR INSERT
    TO authenticated
    WITH CHECK (true);

CREATE POLICY "Enable update for authenticated users" ON inventory
    FOR UPDATE
    TO authenticated
    USING (true)
    WITH CHECK (true);

-- Products policies (original broken ones)
CREATE POLICY "Enable read access for all users" ON products
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Enable insert for authenticated users" ON products
    FOR INSERT
    TO authenticated
    WITH CHECK (true);

CREATE POLICY "Enable update for authenticated users" ON products
    FOR UPDATE
    TO authenticated
    USING (true)
    WITH CHECK (true);

CREATE POLICY "Enable delete for authenticated users" ON products
    FOR DELETE
    TO authenticated
    USING (true);

-- Vendors policies (original broken ones)
CREATE POLICY "Enable read access for all users" ON vendors
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Enable insert for authenticated users" ON vendors
    FOR INSERT
    TO authenticated
    WITH CHECK (true);

CREATE POLICY "Enable update for authenticated users" ON vendors
    FOR UPDATE
    TO authenticated
    USING (true)
    WITH CHECK (true);

-- =============================================
-- STEP 3: Drop User Tracking Infrastructure
-- =============================================

-- Drop triggers
DROP TRIGGER IF EXISTS set_vendors_created_by ON vendors;
DROP TRIGGER IF EXISTS set_customers_created_by ON customers;
DROP TRIGGER IF EXISTS set_products_created_by ON products;
DROP TRIGGER IF EXISTS set_batches_created_by ON batches;
DROP TRIGGER IF EXISTS set_events_created_by ON events;
DROP TRIGGER IF EXISTS set_inventory_events_created_by ON inventory_events;
DROP TRIGGER IF EXISTS set_inventory_transactions_created_by ON inventory_transactions;
DROP TRIGGER IF EXISTS set_haccp_logs_created_by ON haccp_logs;
DROP TRIGGER IF EXISTS set_haccp_audits_created_by ON haccp_audits;
DROP TRIGGER IF EXISTS set_cogs_created_by ON cogs;
DROP TRIGGER IF EXISTS set_fulfillments_created_by ON fulfillments;
DROP TRIGGER IF EXISTS set_gdst_traceability_created_by ON gdst_traceability;
DROP TRIGGER IF EXISTS set_partners_created_by ON partners;
DROP TRIGGER IF EXISTS set_lots_created_by ON lots;
DROP TRIGGER IF EXISTS set_traceability_events_created_by ON traceability_events;
DROP TRIGGER IF EXISTS set_calendar_events_created_by ON calendar_events;

-- Drop the trigger function
DROP FUNCTION IF EXISTS set_created_by();

-- Drop indexes (optional - they don't hurt performance but not needed)
DROP INDEX IF EXISTS idx_vendors_created_by;
DROP INDEX IF EXISTS idx_customers_created_by;
DROP INDEX IF EXISTS idx_products_created_by;
DROP INDEX IF EXISTS idx_batches_created_by;
DROP INDEX IF EXISTS idx_events_created_by;
DROP INDEX IF EXISTS idx_inventory_events_created_by;
DROP INDEX IF EXISTS idx_inventory_transactions_created_by;
DROP INDEX IF EXISTS idx_haccp_logs_created_by;
DROP INDEX IF EXISTS idx_haccp_audits_created_by;
DROP INDEX IF EXISTS idx_cogs_created_by;
DROP INDEX IF EXISTS idx_fulfillments_created_by;
DROP INDEX IF EXISTS idx_gdst_traceability_created_by;
DROP INDEX IF EXISTS idx_partners_created_by;
DROP INDEX IF EXISTS idx_lots_created_by;
DROP INDEX IF EXISTS idx_traceability_events_created_by;
DROP INDEX IF EXISTS idx_calendar_events_created_by;
DROP INDEX IF EXISTS idx_categories_created_by;

-- =============================================
-- STEP 4: Optionally Remove User Columns
-- =============================================

-- Uncomment these if you want to completely remove the user tracking columns
-- WARNING: This will lose all user ownership data!

-- ALTER TABLE vendors DROP COLUMN IF EXISTS created_by;
-- ALTER TABLE customers DROP COLUMN IF EXISTS created_by;
-- ALTER TABLE products DROP COLUMN IF EXISTS created_by;
-- ALTER TABLE batches DROP COLUMN IF EXISTS created_by;
-- ALTER TABLE events DROP COLUMN IF EXISTS created_by;
-- ALTER TABLE inventory_events DROP COLUMN IF EXISTS created_by;
-- ALTER TABLE inventory_transactions DROP COLUMN IF EXISTS created_by;
-- ALTER TABLE inventory_snapshots DROP COLUMN IF EXISTS created_by;
-- ALTER TABLE haccp_logs DROP COLUMN IF EXISTS created_by;
-- ALTER TABLE haccp_audits DROP COLUMN IF EXISTS created_by;
-- ALTER TABLE cogs DROP COLUMN IF EXISTS created_by;
-- ALTER TABLE fulfillments DROP COLUMN IF EXISTS created_by;
-- ALTER TABLE gdst_traceability DROP COLUMN IF EXISTS created_by;
-- ALTER TABLE partners DROP COLUMN IF EXISTS created_by;
-- ALTER TABLE locations DROP COLUMN IF EXISTS created_by;
-- ALTER TABLE lots DROP COLUMN IF EXISTS created_by;
-- ALTER TABLE traceability_events DROP COLUMN IF EXISTS created_by;
-- ALTER TABLE calendar_events DROP COLUMN IF EXISTS created_by;
-- ALTER TABLE categories DROP COLUMN IF EXISTS created_by;
-- ALTER TABLE categories DROP COLUMN IF EXISTS is_system;

COMMIT;

-- =============================================
-- WARNING MESSAGE
-- =============================================

-- This rollback has restored the SECURITY VULNERABILITY
-- ALL authenticated users can now access ALL data from ALL other users
-- This should only be used temporarily for critical fixes
-- Re-apply the security fix as soon as possible!