-- Migration script to add new tables and modify existing ones
-- Author: Claude
-- Date: 2024-03-11

-- Rename vendors table to suppliers
DO $$
BEGIN
    IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'vendors') THEN
        ALTER TABLE public.vendors RENAME TO suppliers;
    END IF;
END $$;

-- Update foreign key constraints pointing to vendors
DO $$
BEGIN
    IF EXISTS (SELECT FROM pg_constraint WHERE conname = 'products_vendor_id_fkey') THEN
        ALTER TABLE public.products 
        DROP CONSTRAINT products_vendor_id_fkey,
        ADD CONSTRAINT products_supplier_id_fkey FOREIGN KEY (vendor_id) REFERENCES public.suppliers(id);
        
        -- Rename the column for consistency
        ALTER TABLE public.products RENAME COLUMN vendor_id TO supplier_id;
    END IF;
    
    IF EXISTS (SELECT FROM pg_constraint WHERE conname = 'events_vendor_id_fkey') THEN
        ALTER TABLE public.events
        DROP CONSTRAINT events_vendor_id_fkey,
        ADD CONSTRAINT events_supplier_id_fkey FOREIGN KEY (vendor_id) REFERENCES public.suppliers(id);
        
        -- Rename the column for consistency
        ALTER TABLE public.events RENAME COLUMN vendor_id TO supplier_id;
    END IF;
END $$;

-- Update indexes
DO $$
BEGIN
    IF EXISTS (SELECT FROM pg_indexes WHERE indexname = 'idx_products_vendor') THEN
        ALTER INDEX idx_products_vendor RENAME TO idx_products_supplier;
    END IF;
    
    IF EXISTS (SELECT FROM pg_indexes WHERE indexname = 'idx_events_vendor') THEN
        ALTER INDEX idx_events_vendor RENAME TO idx_events_supplier;
    END IF;
END $$;

-- Create COGS table (Cost of Goods Sold)
CREATE TABLE IF NOT EXISTS public.cogs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    batch_id UUID NOT NULL REFERENCES public.batches(id),
    raw_product_cost DECIMAL(10, 2) NOT NULL,
    shipping_cost DECIMAL(10, 2) DEFAULT 0,
    handling_cost DECIMAL(10, 2) DEFAULT 0,
    processing_cost DECIMAL(10, 2) DEFAULT 0,
    packaging_cost DECIMAL(10, 2) DEFAULT 0,
    labor_cost DECIMAL(10, 2) DEFAULT 0,
    other_costs DECIMAL(10, 2) DEFAULT 0,
    total_cost DECIMAL(10, 2) GENERATED ALWAYS AS (
        raw_product_cost + shipping_cost + handling_cost + 
        processing_cost + packaging_cost + labor_cost + other_costs
    ) STORED,
    cost_per_unit DECIMAL(10, 2), -- Will be calculated based on batch quantity
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create Fulfillment table
CREATE TABLE IF NOT EXISTS public.fulfillments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_number VARCHAR(50) NOT NULL,
    square_transaction_id VARCHAR(100),
    dbp_order_id VARCHAR(100),
    batch_id UUID REFERENCES public.batches(id),
    product_id UUID REFERENCES public.products(id),
    customer_id UUID REFERENCES public.customers(id),
    quantity DECIMAL(10, 2) NOT NULL,
    unit_price DECIMAL(10, 2) NOT NULL,
    total_price DECIMAL(10, 2) NOT NULL,
    fulfillment_date TIMESTAMP WITH TIME ZONE NOT NULL,
    fulfillment_status VARCHAR(50) NOT NULL CHECK (
        fulfillment_status IN ('pending', 'processing', 'shipped', 'delivered', 'cancelled')
    ),
    shipping_method VARCHAR(100),
    tracking_number VARCHAR(100),
    notes TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create HACCP Logs table
CREATE TABLE IF NOT EXISTS public.haccp_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    batch_id UUID REFERENCES public.batches(id),
    log_type VARCHAR(50) NOT NULL CHECK (
        log_type IN ('receiving', 'processing', 'storage', 'shipping', 'temperature', 'sanitation', 'other')
    ),
    step_name VARCHAR(100) NOT NULL,
    critical_limit VARCHAR(255),
    monitoring_procedure TEXT,
    corrective_action TEXT,
    verification_procedure TEXT,
    record_keeping TEXT,
    temperature DECIMAL(5, 2),
    humidity DECIMAL(5, 2),
    ph_value DECIMAL(5, 2),
    compliance_status VARCHAR(50) NOT NULL CHECK (
        compliance_status IN ('compliant', 'non-compliant', 'pending', 'corrected')
    ),
    inspector_name VARCHAR(100),
    details TEXT,
    log_date TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create HACCP Audits table
CREATE TABLE IF NOT EXISTS public.haccp_audits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    audit_date TIMESTAMP WITH TIME ZONE NOT NULL,
    auditor_name VARCHAR(100) NOT NULL,
    audit_type VARCHAR(50) NOT NULL CHECK (
        audit_type IN ('internal', 'external', 'regulatory', 'certification')
    ),
    findings TEXT,
    non_conformities TEXT,
    corrective_actions TEXT,
    compliance_status VARCHAR(50) NOT NULL CHECK (
        compliance_status IN ('compliant', 'minor-non-compliant', 'major-non-compliant', 'critical-non-compliant')
    ),
    next_audit_date TIMESTAMP WITH TIME ZONE,
    attachments TEXT[],
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create enhanced Inventory tracking table
CREATE TABLE IF NOT EXISTS public.inventory_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES public.products(id),
    batch_id UUID REFERENCES public.batches(id),
    transaction_type VARCHAR(50) NOT NULL CHECK (
        transaction_type IN ('receiving', 'sale', 'adjustment', 'transfer', 'disposal', 'return')
    ),
    quantity DECIMAL(10, 2) NOT NULL,
    unit_cost DECIMAL(10, 2),
    total_cost DECIMAL(10, 2),
    previous_quantity DECIMAL(10, 2) NOT NULL,
    new_quantity DECIMAL(10, 2) NOT NULL,
    location VARCHAR(100),
    reference_id UUID, -- Can link to fulfillment, event, etc.
    reference_type VARCHAR(50), -- Type of reference (fulfillment, event, etc.)
    performed_by VARCHAR(100),
    notes TEXT,
    transaction_date TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_cogs_batch ON public.cogs(batch_id);
CREATE INDEX IF NOT EXISTS idx_fulfillments_batch ON public.fulfillments(batch_id);
CREATE INDEX IF NOT EXISTS idx_fulfillments_product ON public.fulfillments(product_id);
CREATE INDEX IF NOT EXISTS idx_fulfillments_customer ON public.fulfillments(customer_id);
CREATE INDEX IF NOT EXISTS idx_fulfillments_order_number ON public.fulfillments(order_number);
CREATE INDEX IF NOT EXISTS idx_haccp_logs_batch ON public.haccp_logs(batch_id);
CREATE INDEX IF NOT EXISTS idx_haccp_logs_date ON public.haccp_logs(log_date);
CREATE INDEX IF NOT EXISTS idx_haccp_audits_date ON public.haccp_audits(audit_date);
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_product ON public.inventory_transactions(product_id);
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_batch ON public.inventory_transactions(batch_id);
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_date ON public.inventory_transactions(transaction_date);

-- Create triggers for updated_at timestamps
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_cogs_updated_at') THEN
        CREATE TRIGGER update_cogs_updated_at
            BEFORE UPDATE ON public.cogs
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_fulfillments_updated_at') THEN
        CREATE TRIGGER update_fulfillments_updated_at
            BEFORE UPDATE ON public.fulfillments
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_haccp_logs_updated_at') THEN
        CREATE TRIGGER update_haccp_logs_updated_at
            BEFORE UPDATE ON public.haccp_logs
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_haccp_audits_updated_at') THEN
        CREATE TRIGGER update_haccp_audits_updated_at
            BEFORE UPDATE ON public.haccp_audits
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_inventory_transactions_updated_at') THEN
        CREATE TRIGGER update_inventory_transactions_updated_at
            BEFORE UPDATE ON public.inventory_transactions
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- Create a specialized GS1/GDST traceability table
CREATE TABLE IF NOT EXISTS public.gdst_traceability (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    batch_id UUID REFERENCES public.batches(id),
    product_id UUID REFERENCES public.products(id),
    event_type VARCHAR(50) NOT NULL CHECK (
        event_type IN ('fishing', 'landing', 'transshipment', 'processing', 'aggregation', 'disaggregation', 'shipping', 'receiving')
    ),
    event_time TIMESTAMP WITH TIME ZONE NOT NULL,
    location_id VARCHAR(100),
    location_name VARCHAR(100),
    vessel_name VARCHAR(100),
    vessel_id VARCHAR(50),
    vessel_flag_state VARCHAR(3),
    catch_area VARCHAR(20),
    fishing_gear VARCHAR(20),
    production_method VARCHAR(2),
    first_freeze_date TIMESTAMP WITH TIME ZONE,
    business_step VARCHAR(50),
    disposition VARCHAR(50),
    gdst_data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_gdst_batch ON public.gdst_traceability(batch_id);
CREATE INDEX IF NOT EXISTS idx_gdst_product ON public.gdst_traceability(product_id);
CREATE INDEX IF NOT EXISTS idx_gdst_event_type ON public.gdst_traceability(event_type);
CREATE INDEX IF NOT EXISTS idx_gdst_event_time ON public.gdst_traceability(event_time);

-- Add GS1 identifiers to products table if they don't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'products' AND column_name = 'gtin') THEN
        ALTER TABLE public.products ADD COLUMN gtin VARCHAR(14);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'products' AND column_name = 'scientific_name') THEN
        ALTER TABLE public.products ADD COLUMN scientific_name VARCHAR(100);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'products' AND column_name = 'fao_species_code') THEN
        ALTER TABLE public.products ADD COLUMN fao_species_code VARCHAR(3);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'products' AND column_name = 'production_method') THEN
        ALTER TABLE public.products ADD COLUMN production_method VARCHAR(2);
    END IF;
END $$;

-- Create a table for syncing with external platforms (Square, DBP)
CREATE TABLE IF NOT EXISTS public.platform_integrations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    platform_name VARCHAR(50) NOT NULL CHECK (
        platform_name IN ('square', 'dbp', 'shopify', 'woocommerce', 'other')
    ),
    api_key TEXT,
    api_secret TEXT,
    access_token TEXT,
    refresh_token TEXT,
    token_expiry TIMESTAMP WITH TIME ZONE,
    webhook_url TEXT,
    webhook_secret TEXT,
    last_sync_time TIMESTAMP WITH TIME ZONE,
    sync_interval_minutes INTEGER DEFAULT 60,
    status VARCHAR(20) DEFAULT 'active',
    config JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS public.sync_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    platform_id UUID REFERENCES public.platform_integrations(id),
    sync_start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    sync_end_time TIMESTAMP WITH TIME ZONE,
    sync_type VARCHAR(50) NOT NULL CHECK (
        sync_type IN ('orders', 'inventory', 'products', 'customers', 'fulfillments', 'full')
    ),
    sync_direction VARCHAR(20) NOT NULL CHECK (
        sync_direction IN ('import', 'export', 'bidirectional')
    ),
    items_processed INTEGER DEFAULT 0,
    items_created INTEGER DEFAULT 0,
    items_updated INTEGER DEFAULT 0,
    items_deleted INTEGER DEFAULT 0,
    items_failed INTEGER DEFAULT 0,
    status VARCHAR(20) NOT NULL CHECK (
        status IN ('pending', 'running', 'completed', 'failed', 'partially_completed')
    ),
    error_details JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_platform_integrations_platform ON public.platform_integrations(platform_name);
CREATE INDEX IF NOT EXISTS idx_sync_logs_platform ON public.sync_logs(platform_id);
CREATE INDEX IF NOT EXISTS idx_sync_logs_time ON public.sync_logs(sync_start_time);
CREATE INDEX IF NOT EXISTS idx_sync_logs_status ON public.sync_logs(status);

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_platform_integrations_updated_at') THEN
        CREATE TRIGGER update_platform_integrations_updated_at
            BEFORE UPDATE ON public.platform_integrations
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;
