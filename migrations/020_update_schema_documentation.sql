-- Updated schema documentation after security fix
-- This documents the current state after applying migration 017

-- =============================================
-- MULTI-TENANT SECURITY MODEL
-- =============================================

COMMENT ON SCHEMA public IS 'Pacific Cloud Seafoods Manager - Multi-tenant secured schema with proper RLS isolation';

-- =============================================
-- USER ISOLATION PATTERN
-- =============================================

-- All user-sensitive tables follow this pattern:
-- 1. created_by UUID REFERENCES auth.users(id) - Tracks ownership
-- 2. RLS policies using auth.uid() = created_by
-- 3. Automatic user assignment via triggers
-- 4. Indexes on created_by for performance

-- =============================================
-- CORE BUSINESS TABLES
-- =============================================

COMMENT ON TABLE vendors IS 'Supplier/vendor master data - isolated per user via created_by column';
COMMENT ON COLUMN vendors.created_by IS 'User who created this vendor - enforces RLS isolation';

COMMENT ON TABLE customers IS 'Customer master data - isolated per user via created_by column';  
COMMENT ON COLUMN customers.created_by IS 'User who created this customer - enforces RLS isolation';

COMMENT ON TABLE products IS 'Product catalog - isolated per user via created_by column';
COMMENT ON COLUMN products.created_by IS 'User who created this product - enforces RLS isolation';

COMMENT ON TABLE batches IS 'Product batch/lot tracking - isolated per user via created_by column';
COMMENT ON COLUMN batches.created_by IS 'User who created this batch - enforces RLS isolation';

COMMENT ON TABLE events IS 'Business events (receiving, sales, etc.) - isolated per user via created_by column';
COMMENT ON COLUMN events.created_by IS 'User who created this event - enforces RLS isolation';

-- =============================================
-- INVENTORY SYSTEM TABLES  
-- =============================================

COMMENT ON TABLE inventory_events IS 'Core inventory transaction log - isolated per user via created_by column';
COMMENT ON COLUMN inventory_events.created_by IS 'User who created this inventory event - enforces RLS isolation';

COMMENT ON TABLE inventory_transactions IS 'Detailed inventory movement tracking - isolated per user via created_by column';
COMMENT ON COLUMN inventory_transactions.created_by IS 'User who performed this transaction - enforces RLS isolation';

COMMENT ON TABLE inventory_snapshots IS 'Point-in-time inventory states - isolated per user via created_by column';
COMMENT ON COLUMN inventory_snapshots.created_by IS 'User who owns this snapshot data - enforces RLS isolation';

-- =============================================
-- HACCP COMPLIANCE TABLES
-- =============================================

COMMENT ON TABLE haccp_logs IS 'HACCP compliance monitoring logs - isolated per user/organization via created_by column';
COMMENT ON COLUMN haccp_logs.created_by IS 'User/organization responsible for this HACCP log - enforces RLS isolation';

COMMENT ON TABLE haccp_audits IS 'HACCP audit records - isolated per user/organization via created_by column';
COMMENT ON COLUMN haccp_audits.created_by IS 'User/organization responsible for this audit - enforces RLS isolation';

-- =============================================
-- FINANCIAL TRACKING TABLES
-- =============================================

COMMENT ON TABLE cogs IS 'Cost of Goods Sold tracking - isolated per user via created_by column';
COMMENT ON COLUMN cogs.created_by IS 'User who owns this cost data - enforces RLS isolation';

COMMENT ON TABLE fulfillments IS 'Order fulfillment tracking - isolated per user via created_by column';
COMMENT ON COLUMN fulfillments.created_by IS 'User who processed this fulfillment - enforces RLS isolation';

-- =============================================
-- TRACEABILITY SYSTEM TABLES
-- =============================================

COMMENT ON TABLE gdst_traceability IS 'GDST/GS1 traceability events - isolated per user/organization via created_by column';
COMMENT ON COLUMN gdst_traceability.created_by IS 'User/organization responsible for this traceability data - enforces RLS isolation';

COMMENT ON TABLE partners IS 'Business partners for traceability - isolated per user via created_by column';
COMMENT ON COLUMN partners.created_by IS 'User who created this partner record - enforces RLS isolation';

COMMENT ON TABLE locations IS 'Partner locations - access controlled via partner ownership';

COMMENT ON TABLE lots IS 'Traceability lots with TLC codes - isolated per user via created_by column';
COMMENT ON COLUMN lots.created_by IS 'User who created this lot - enforces RLS isolation';

COMMENT ON TABLE traceability_events IS 'FSMA traceability events - isolated per user via created_by column';
COMMENT ON COLUMN traceability_events.created_by IS 'User who recorded this traceability event - enforces RLS isolation';

COMMENT ON TABLE event_lots IS 'Lot inputs/outputs for traceability events - access controlled via event ownership';

-- =============================================
-- CALENDAR SYSTEM TABLES
-- =============================================

COMMENT ON TABLE calendar_events IS 'Calendar events synced from inventory operations - isolated per user via created_by column';
COMMENT ON COLUMN calendar_events.created_by IS 'User who owns this calendar event - enforces RLS isolation';

-- =============================================
-- SHARED SYSTEM TABLES
-- =============================================

COMMENT ON TABLE categories IS 'Product categories - supports both system-wide and user-specific categories';
COMMENT ON COLUMN categories.created_by IS 'User who created this category - NULL for system categories';
COMMENT ON COLUMN categories.is_system IS 'TRUE for system categories visible to all users, FALSE for user-specific';

-- =============================================
-- RLS POLICY DOCUMENTATION  
-- =============================================

-- Standard RLS Policy Pattern:
-- 1. SELECT: WHERE created_by = auth.uid()
-- 2. INSERT: WITH CHECK (created_by = auth.uid()) 
-- 3. UPDATE: USING (created_by = auth.uid()) WITH CHECK (created_by = auth.uid())
-- 4. DELETE: USING (created_by = auth.uid())

-- Special Cases:
-- - categories: Users see system categories (is_system=true) + own categories
-- - locations: Access controlled via partner ownership  
-- - event_lots: Access controlled via event ownership

-- =============================================
-- PERFORMANCE INDEXES FOR RLS
-- =============================================

-- All user-sensitive tables have indexes on created_by for RLS performance:
-- CREATE INDEX idx_[table]_created_by ON [table](created_by);

-- These indexes ensure RLS filtering is efficient even with large datasets

-- =============================================
-- SECURITY VALIDATION
-- =============================================

-- The following queries should return 0 rows when RLS is working correctly:
-- (Replace [user_id] with actual user ID being tested)

-- SELECT COUNT(*) FROM products WHERE created_by != '[user_id]';
-- SELECT COUNT(*) FROM customers WHERE created_by != '[user_id]';  
-- SELECT COUNT(*) FROM vendors WHERE created_by != '[user_id]';
-- SELECT COUNT(*) FROM inventory_events WHERE created_by != '[user_id]';
-- SELECT COUNT(*) FROM haccp_logs WHERE created_by != '[user_id]';

-- System categories should be visible to all users:
-- SELECT COUNT(*) FROM categories WHERE is_system = true; -- Should return > 0

-- =============================================
-- MIGRATION HISTORY
-- =============================================

-- Migration 017: CRITICAL security fix - added user isolation to all tables
-- Migration 018: Rollback script for migration 017 (emergency use only)  
-- Migration 019: Validation script for security fix
-- Migration 020: Updated schema documentation (this file)

-- =============================================
-- COMPLIANCE NOTES
-- =============================================

-- HACCP Compliance: User isolation ensures proper segregation of HACCP data 
-- per organization/facility, meeting regulatory requirements

-- GDST Traceability: User isolation maintains proper chain of custody boundaries
-- while allowing traceability data to be shared within organizational boundaries

-- Data Privacy: User isolation ensures customer and business data privacy
-- in multi-tenant environment

-- =============================================
-- DEVELOPMENT NOTES
-- =============================================

-- When adding new user-sensitive tables:
-- 1. Add created_by UUID REFERENCES auth.users(id) column
-- 2. Add RLS policies following standard pattern
-- 3. Add trigger for automatic user assignment
-- 4. Add index on created_by for performance
-- 5. Update this documentation

-- When querying from application:
-- - RLS automatically filters results by current user
-- - No need to manually add WHERE created_by = auth.uid()
-- - System categories are automatically included for all users

-- For administrative/reporting queries:
-- - May need to use SECURITY DEFINER functions with appropriate access controls
-- - Consider row-level permissions for cross-user data access when needed