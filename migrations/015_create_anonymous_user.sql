-- Create anonymous user with limited permissions
INSERT INTO auth.users (
    instance_id,
    id,
    aud,
    role,
    email,
    encrypted_password,
    email_confirmed_at,
    created_at,
    updated_at,
    last_sign_in_at
)
VALUES (
    '00000000-0000-0000-0000-000000000000',
    '00000000-0000-0000-0000-000000000000',
    'authenticated',
    'authenticated',
    '<EMAIL>',
    crypt('anonymous', gen_salt('bf')),
    now(),
    now(),
    now(),
    now()
)
ON CONFLICT (id) DO NOTHING;

-- Enable anonymous access
UPDATE auth.users
SET raw_app_meta_data = '{"provider":"email","providers":["email"]}'::jsonb,
    raw_user_meta_data = '{}'::jsonb,
    is_super_admin = false,
    confirmed_at = now()
WHERE email = '<EMAIL>';

-- Create RLS policy for anonymous user
CREATE POLICY "Allow anonymous read access" ON categories
    FOR SELECT
    TO authenticated
    USING (auth.uid() = '00000000-0000-0000-0000-000000000000');

CREATE POLICY "Allow anonymous read access" ON products
    FOR SELECT
    TO authenticated
    USING (auth.uid() = '00000000-0000-0000-0000-000000000000');

CREATE POLICY "Allow anonymous read access" ON inventory
    FOR SELECT
    TO authenticated
    USING (auth.uid() = '00000000-0000-0000-0000-000000000000');
