-- Create temporary table to store products data
CREATE TABLE products_temp AS SELECT * FROM products;

-- Drop existing table
DROP TABLE IF EXISTS products CASCADE;

-- Recreate products table
CREATE TABLE IF NOT EXISTS products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    sku VARCHAR(100) UNIQUE,
    description TEXT,
    category_id UUID REFERENCES categories(id),
    vendor_id UUID REFERENCES vendors(id),
    unit VARCHAR(50) NOT NULL,
    price DECIMAL(10,2),
    cost DECIMAL(10,2),
    min_stock DECIMAL(10,2),
    current_stock DECIMAL(10,2) DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Restore data
INSERT INTO products 
SELECT * FROM products_temp;

-- Drop temporary table
DROP TABLE products_temp;

-- Recreate indexes
CREATE INDEX idx_products_category ON products(category_id);
CREATE INDEX idx_products_vendor ON products(vendor_id);

-- Recreate trigger
CREATE TRIGGER update_products_updated_at
    BEFORE UPDATE ON products
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();