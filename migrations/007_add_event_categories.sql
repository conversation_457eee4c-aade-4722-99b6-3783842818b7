-- First, ensure categories table exists
CREATE TABLE IF NOT EXISTS categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    parent_id UUID REFERENCES categories(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Insert required categories based on event types
INSERT INTO categories (name, description) VALUES
('Receiving', 'Items received into inventory'),
('Disposal', 'Items removed from inventory due to damage, spoilage, or other losses'),
('Physical Count', 'Inventory adjustments based on physical count'),
('Re-processing', 'Items being reprocessed or transformed')
ON CONFLICT (name) DO UPDATE SET
    description = EXCLUDED.description,
    updated_at = CURRENT_TIMESTAMP;