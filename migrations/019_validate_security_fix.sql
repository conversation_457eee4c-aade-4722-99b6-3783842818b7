-- SECURITY VALIDATION SCRIPT
-- Run this after applying migration 017_CRITICAL_fix_multi_tenant_security.sql
-- This script creates test data with different users to validate RLS policies work correctly

BEGIN;

-- Create test function to validate multi-tenant isolation
CREATE OR REPLACE FUNCTION validate_multi_tenant_security()
RETURNS TABLE (
    test_name TEXT,
    expected_result TEXT,
    actual_result TEXT,
    status TEXT
) AS $$
DECLARE
    user_a_id UUID := '11111111-1111-1111-1111-111111111111';
    user_b_id UUID := '*************-2222-2222-************';
    test_product_a_id UUID := '*************-3333-3333-************';
    test_product_b_id UUID := '*************-4444-4444-************';
    count_result INTEGER;
BEGIN
    -- Create test users in auth.users (simulate)
    INSERT INTO auth.users (id, email, encrypted_password, email_confirmed_at, created_at, updated_at)
    VALUES 
        (user_a_id, '<EMAIL>', crypt('password', gen_salt('bf')), now(), now(), now()),
        (user_b_id, '<EMAIL>', crypt('password', gen_salt('bf')), now(), now(), now())
    ON CONFLICT (id) DO NOTHING;
    
    -- Insert test data for User A
    INSERT INTO products (id, name, created_by) 
    VALUES (test_product_a_id, 'User A Product', user_a_id)
    ON CONFLICT (id) DO NOTHING;
    
    INSERT INTO customers (id, name, created_by, channel_type, status) 
    VALUES ('*************-5555-5555-************', 'User A Customer', user_a_id, 'wholesale', 'active')
    ON CONFLICT (id) DO NOTHING;
    
    -- Insert test data for User B
    INSERT INTO products (id, name, created_by) 
    VALUES (test_product_b_id, 'User B Product', user_b_id)
    ON CONFLICT (id) DO NOTHING;
    
    INSERT INTO customers (id, name, created_by, channel_type, status) 
    VALUES ('66666666-6666-6666-6666-666666666666', 'User B Customer', user_b_id, 'retail', 'active')
    ON CONFLICT (id) DO NOTHING;
    
    -- Test 1: User A should only see their own products
    SET LOCAL "request.jwt.claim.sub" = user_a_id::text;
    SELECT COUNT(*) INTO count_result FROM products WHERE name LIKE 'User % Product';
    
    RETURN QUERY SELECT 
        'User A Products Isolation'::TEXT,
        '1'::TEXT,
        count_result::TEXT,
        CASE WHEN count_result = 1 THEN 'PASS' ELSE 'FAIL' END::TEXT;
    
    -- Test 2: User B should only see their own products
    SET LOCAL "request.jwt.claim.sub" = user_b_id::text;
    SELECT COUNT(*) INTO count_result FROM products WHERE name LIKE 'User % Product';
    
    RETURN QUERY SELECT 
        'User B Products Isolation'::TEXT,
        '1'::TEXT,
        count_result::TEXT,
        CASE WHEN count_result = 1 THEN 'PASS' ELSE 'FAIL' END::TEXT;
    
    -- Test 3: User A should only see their own customers
    SET LOCAL "request.jwt.claim.sub" = user_a_id::text;
    SELECT COUNT(*) INTO count_result FROM customers WHERE name LIKE 'User % Customer';
    
    RETURN QUERY SELECT 
        'User A Customers Isolation'::TEXT,
        '1'::TEXT,
        count_result::TEXT,
        CASE WHEN count_result = 1 THEN 'PASS' ELSE 'FAIL' END::TEXT;
    
    -- Test 4: User B should only see their own customers
    SET LOCAL "request.jwt.claim.sub" = user_b_id::text;
    SELECT COUNT(*) INTO count_result FROM customers WHERE name LIKE 'User % Customer';
    
    RETURN QUERY SELECT 
        'User B Customers Isolation'::TEXT,
        '1'::TEXT,
        count_result::TEXT,
        CASE WHEN count_result = 1 THEN 'PASS' ELSE 'FAIL' END::TEXT;
    
    -- Test 5: Both users should see system categories
    SET LOCAL "request.jwt.claim.sub" = user_a_id::text;
    SELECT COUNT(*) INTO count_result FROM categories WHERE is_system = true;
    
    RETURN QUERY SELECT 
        'System Categories Visible to User A'::TEXT,
        '> 0'::TEXT,
        count_result::TEXT,
        CASE WHEN count_result > 0 THEN 'PASS' ELSE 'FAIL' END::TEXT;
    
    SET LOCAL "request.jwt.claim.sub" = user_b_id::text;
    SELECT COUNT(*) INTO count_result FROM categories WHERE is_system = true;
    
    RETURN QUERY SELECT 
        'System Categories Visible to User B'::TEXT,
        '> 0'::TEXT,
        count_result::TEXT,
        CASE WHEN count_result > 0 THEN 'PASS' ELSE 'FAIL' END::TEXT;
    
    -- Test 6: Anonymous user should not exist
    SELECT COUNT(*) INTO count_result FROM auth.users WHERE id = '00000000-0000-0000-0000-000000000000';
    
    RETURN QUERY SELECT 
        'Anonymous User Removed'::TEXT,
        '0'::TEXT,
        count_result::TEXT,
        CASE WHEN count_result = 0 THEN 'PASS' ELSE 'FAIL' END::TEXT;
    
    -- Clean up test data
    DELETE FROM products WHERE id IN (test_product_a_id, test_product_b_id);
    DELETE FROM customers WHERE name LIKE 'User % Customer';
    DELETE FROM auth.users WHERE id IN (user_a_id, user_b_id);
END;
$$ LANGUAGE plpgsql;

-- Run the validation
SELECT * FROM validate_multi_tenant_security();

-- Drop the test function
DROP FUNCTION IF EXISTS validate_multi_tenant_security();

COMMIT;

-- =============================================
-- MANUAL VERIFICATION QUERIES
-- =============================================

-- Run these queries to manually verify RLS is working:

-- 1. Check all tables have RLS enabled:
-- SELECT schemaname, tablename, rowsecurity 
-- FROM pg_tables 
-- WHERE schemaname = 'public' 
-- AND tablename IN ('products', 'customers', 'vendors', 'events', 'inventory_events', 'haccp_logs');

-- 2. Check all user-sensitive tables have created_by columns:
-- SELECT table_name, column_name 
-- FROM information_schema.columns 
-- WHERE table_schema = 'public' 
-- AND column_name = 'created_by'
-- ORDER BY table_name;

-- 3. Check RLS policies exist:
-- SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
-- FROM pg_policies 
-- WHERE schemaname = 'public'
-- ORDER BY tablename, policyname;

-- 4. Verify anonymous user is removed:
-- SELECT COUNT(*) as anonymous_user_count 
-- FROM auth.users 
-- WHERE id = '00000000-0000-0000-0000-000000000000';

-- 5. Check system categories are marked correctly:
-- SELECT name, is_system, created_by 
-- FROM categories 
-- ORDER BY is_system DESC, name;

-- =============================================
-- EXPECTED RESULTS FOR SECURITY SUCCESS:
-- =============================================
-- 
-- ✅ All validation tests should return 'PASS'
-- ✅ rowsecurity = true for all sensitive tables
-- ✅ created_by columns exist on all user tables
-- ✅ RLS policies use auth.uid() not 'true'
-- ✅ anonymous_user_count = 0
-- ✅ System categories have is_system = true