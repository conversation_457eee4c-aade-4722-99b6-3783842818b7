-- Create inventory table for tracking stock movements
CREATE TABLE IF NOT EXISTS inventory (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(id),
    quantity DECIMAL(10,2) NOT NULL,
    event_type VARCHAR(50) NOT NULL CHECK (event_type IN ('received', 'disposal', 'physical_count', 'sale')),
    notes TEXT,
    images TEXT[],
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index for faster queries
CREATE INDEX idx_inventory_product ON inventory(product_id);
CREATE INDEX idx_inventory_event_type ON inventory(event_type);
CREATE INDEX idx_inventory_created_at ON inventory(created_at);

-- Create trigger for updated_at
CREATE TRIGGER update_inventory_updated_at
    BEFORE UPDATE ON inventory
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();