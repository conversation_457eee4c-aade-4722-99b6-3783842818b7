-- Drop existing relationships if any
ALTER TABLE IF EXISTS events DROP CONSTRAINT IF EXISTS events_product_id_fkey;
ALTER TABLE IF EXISTS products DROP CONSTRAINT IF EXISTS products_category_id_fkey;
ALTER TABLE IF EXISTS products DROP CONSTRAINT IF EXISTS products_vendor_id_fkey;

-- Add Foreign Key relationships
ALTER TABLE events 
ADD CONSTRAINT events_product_id_fkey 
FOREIGN KEY (product_id) REFERENCES products(id);

ALTER TABLE products 
ADD CONSTRAINT products_category_id_fkey 
FOREIGN KEY (category_id) REFERENCES categories(id);

ALTER TABLE products 
ADD CONSTRAINT products_vendor_id_fkey 
FOREIGN KEY (vendor_id) REFERENCES vendors(id);

-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_events_product_id ON events(product_id);
CREATE INDEX IF NOT EXISTS idx_products_category_id ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_products_vendor_id ON products(vendor_id);

COMMENT ON CONSTRAINT events_product_id_fkey ON events IS 'Links events to products';
COMMENT ON CONSTRAINT products_category_id_fkey ON products IS 'Links products to categories';
COMMENT ON CONSTRAINT products_vendor_id_fkey ON products IS 'Links products to vendors';