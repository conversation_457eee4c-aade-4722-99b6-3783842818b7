@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    font-family: Inter, system-ui, -apple-system, sans-serif;
  }
}

@layer utilities {
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  /* Defensive CSS to prevent white box overlay issues */
  .sidebar-protected {
    position: relative;
    z-index: 100 !important;
  }

  /* Ensure overlays don't interfere with sidebar */
  .modal-backdrop {
    left: var(--sidebar-width, 0) !important;
  }

  /* Prevent voice assistant from overflowing */
  .voice-panel-container {
    max-width: calc(100vw - var(--sidebar-width, 280px) - 3rem) !important;
    max-height: calc(100vh - 8rem) !important;
    overflow: hidden !important;
  }

  /* Force proper z-index hierarchy */
  nav[data-testid="sidebar"] {
    z-index: 100 !important;
    position: relative !important;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}