# Types Directory

This directory contains TypeScript type definitions, interfaces, and schemas that provide type safety throughout the Pacific Cloud Seafoods Manager application.

## Overview

The types directory serves as the central location for all TypeScript type definitions, ensuring consistent typing across the application and providing compile-time safety for data structures, API contracts, and component interfaces.

## Type Organization

### Core Type Files

#### `schema.ts`
**Purpose**: Database schema types generated from Supabase and custom application types.

**Key Features**:
- Auto-generated types from Supabase database schema
- Custom type extensions for application-specific needs
- Voice event types and interfaces
- Audit trail and compliance types
- Vendor and customer relationship types

**Key Type Categories**:
```typescript
// Database table types
export type InventoryEvent = Database['public']['Tables']['inventory_events']['Row'];
export type VoiceEvent = InventoryEvent & VoiceEventExtensions;

// Voice-specific types
export interface VoiceEventMetadata {
  audioPath?: string;
  transcription?: string;
  confidence_score?: number;
  processing_status: 'pending' | 'processing' | 'completed' | 'failed';
}

// Audit and compliance types
export interface AuditTrail {
  id: string;
  table_name: string;
  operation: 'INSERT' | 'UPDATE' | 'DELETE';
  old_values?: Record<string, any>;
  new_values?: Record<string, any>;
  changed_by: string;
  changed_at: string;
}
```

#### `index.ts`
**Purpose**: Central export point for all application types and re-exports from other type files.

**Key Features**:
- Consolidated type exports
- Re-exports from schema.ts and other type files
- Application-wide type aliases
- Utility type definitions

**Usage Pattern**:
```typescript
// Single import point for all types
import { VoiceEvent, InventoryEvent, User } from '../types';
```

#### `speech.d.ts`
**Purpose**: TypeScript declarations for speech recognition and voice processing APIs.

**Key Features**:
- Web Speech API type definitions
- Custom speech recognition interfaces
- Voice command type definitions
- Audio processing type declarations

**Key Declarations**:
```typescript
declare global {
  interface Window {
    SpeechRecognition: typeof SpeechRecognition;
    webkitSpeechRecognition: typeof SpeechRecognition;
  }
}

export interface VoiceCommand {
  command: string;
  confidence: number;
  timestamp: Date;
  metadata?: Record<string, any>;
}
```

## Type Categories

### Database Types

#### Table Types
Generated from Supabase schema with extensions:
- `inventory_events` - Core inventory tracking
- `voice_event_audit` - Voice event audit trail
- `products` - Product catalog
- `vendors` - Vendor information
- `customers` - Customer data
- `compliance_events` - HACCP and regulatory compliance

#### Relationship Types
Types for database relationships and joins:
```typescript
export interface InventoryEventWithProduct extends InventoryEvent {
  product: Product;
  vendor?: Vendor;
  customer?: Customer;
}
```

### Voice Processing Types

#### Voice Event Types
```typescript
export interface VoiceEventInput {
  audio_blob: Blob;
  metadata: VoiceEventMetadata;
  user_id: string;
}

export interface ProcessedVoiceEvent {
  transcription: string;
  confidence_score: number;
  processing_time: number;
  audio_path: string;
  status: ProcessingStatus;
}
```

#### Audio Processing Types
```typescript
export interface AudioProcessingOptions {
  format: 'wav' | 'mp3' | 'ogg';
  quality: 'low' | 'medium' | 'high';
  compression: boolean;
  noise_reduction: boolean;
}
```

### Component Types

#### Props Interfaces
Standardized prop interfaces for components:
```typescript
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
  testId?: string;
}

export interface DataTableProps<T> extends BaseComponentProps {
  data: T[];
  columns: ColumnDefinition<T>[];
  loading?: boolean;
  error?: string;
  onRowClick?: (row: T) => void;
}
```

#### Form Types
Form-related type definitions:
```typescript
export interface FormFieldProps {
  name: string;
  label: string;
  required?: boolean;
  validation?: ValidationSchema;
  error?: string;
}
```

### API Types

#### Request/Response Types
```typescript
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  errors?: string[];
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
```

#### Service Types
```typescript
export interface ServiceOptions {
  timeout?: number;
  retries?: number;
  cache?: boolean;
}

export interface VoiceServiceConfig extends ServiceOptions {
  apiKey: string;
  model: 'whisper-1' | 'whisper-large';
  language?: string;
}
```

### Utility Types

#### Generic Utility Types
```typescript
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};
```

#### Domain-Specific Utilities
```typescript
export type VoiceEventStatus = 'pending' | 'processing' | 'completed' | 'failed';
export type ComplianceLevel = 'compliant' | 'warning' | 'violation' | 'critical';
export type UserRole = 'admin' | 'manager' | 'operator' | 'viewer';
```

## Type Generation

### Supabase Type Generation
Types are generated from the Supabase database schema:
```bash
npx supabase gen types typescript --project-id <project-id> > src/types/supabase.ts
```

### Custom Type Extensions
Custom types extend generated types:
```typescript
// Extend generated types with application-specific fields
export interface ExtendedInventoryEvent extends Database['public']['Tables']['inventory_events']['Row'] {
  // Voice-specific extensions
  audio_path?: string;
  transcription?: string;
  confidence_score?: number;
  
  // Computed fields
  formatted_date?: string;
  status_display?: string;
}
```

## Type Safety Patterns

### Type Guards
```typescript
export function isVoiceEvent(event: InventoryEvent): event is VoiceEvent {
  return 'transcription' in event && event.transcription !== null;
}

export function hasAudioPath(event: VoiceEvent): event is VoiceEvent & { audio_path: string } {
  return event.audio_path !== null && event.audio_path !== undefined;
}
```

### Validation Schemas
Integration with Zod for runtime validation:
```typescript
import { z } from 'zod';

export const VoiceEventSchema = z.object({
  transcription: z.string().min(1),
  confidence_score: z.number().min(0).max(1),
  audio_path: z.string().optional(),
  processing_status: z.enum(['pending', 'processing', 'completed', 'failed'])
});

export type VoiceEventValidated = z.infer<typeof VoiceEventSchema>;
```

### Branded Types
For additional type safety:
```typescript
export type UserId = string & { readonly brand: unique symbol };
export type EventId = string & { readonly brand: unique symbol };
export type AudioPath = string & { readonly brand: unique symbol };
```

## Integration with Components

### Component Type Safety
```typescript
interface VoiceEventListProps {
  events: VoiceEvent[];
  onEventSelect: (event: VoiceEvent) => void;
  loading?: boolean;
  error?: string;
}

export const VoiceEventList: React.FC<VoiceEventListProps> = ({
  events,
  onEventSelect,
  loading = false,
  error
}) => {
  // Component implementation with full type safety
};
```

### Hook Types
```typescript
export interface UseVoiceCommandsReturn {
  isListening: boolean;
  transcript: string;
  confidence: number;
  startListening: () => void;
  stopListening: () => void;
  error: string | null;
}
```

## Testing Types

### Mock Types
```typescript
export interface MockVoiceEvent extends Partial<VoiceEvent> {
  id: string;
  transcription: string;
}

export const createMockVoiceEvent = (overrides?: Partial<VoiceEvent>): VoiceEvent => ({
  // Default mock values
  id: 'mock-id',
  transcription: 'Mock transcription',
  confidence_score: 0.95,
  ...overrides
});
```

## Development Guidelines

1. **Naming Conventions**: Use PascalCase for types and interfaces
2. **Export Strategy**: Export all types from index.ts for centralized imports
3. **Documentation**: Document complex types with JSDoc comments
4. **Validation**: Pair TypeScript types with Zod schemas for runtime validation
5. **Generics**: Use generics for reusable type patterns
6. **Strict Typing**: Avoid `any` type, use `unknown` when necessary
7. **Branded Types**: Use branded types for additional type safety where appropriate

## Migration Strategy

### Legacy Type Migration
Gradually migrating from `/src/types.ts` to organized type files:
1. Identify type categories in legacy file
2. Create appropriate type files in `/types` directory
3. Move types to new files
4. Update imports throughout application
5. Remove legacy types file

### Type Maintenance
- Regular updates from database schema changes
- Version control for type definitions
- Breaking change documentation
- Migration guides for type updates

## Future Enhancements

- Advanced generic type utilities
- Runtime type validation integration
- Automatic API type generation
- Enhanced error type definitions
- Performance-optimized type definitions
- Advanced branded type patterns