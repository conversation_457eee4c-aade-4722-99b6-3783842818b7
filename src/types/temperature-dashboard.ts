/**
 * Historical Dashboard Types
 * 
 * Type definitions for the historical temperature dashboard components
 */

export interface DateRange {
  start: Date;
  end: Date;
}

export interface DateRangePreset {
  label: string;
  value: DateRange;
  key: string;
}

export interface HistoricalDataPoint {
  timestamp: Date;
  sensorId: string;
  sensorName?: string;
  temperature: number;
  humidity: number;
  dewpoint?: number;
  heatIndex?: number;
  batteryLevel?: number;
  signalStrength?: number;
}

export interface SensorInfo {
  id: string;
  name: string;
  location: string;
  isOnline: boolean;
  batteryLevel?: number;
  currentTemperature?: number;
  currentHumidity?: number;
  signalStrength?: number;
  dewpoint?: number;
  heatIndex?: number;
  lastReading?: Date;
}

export interface SensorStatus extends SensorInfo {
  lastReading: Date;
  currentTemperature: number;
  currentHumidity: number;
  batteryLevel: number;
  signalStrength: number;
  dewpoint: number;
  heatIndex: number;
}

export interface SensorStatistics {
  sensorId: string;
  period: DateRange;
  temperature: {
    min: number;
    max: number;
    average: number;
    minTimestamp: Date;
    maxTimestamp: Date;
  };
  humidity: {
    min: number;
    max: number;
    average: number;
  };
}

export interface ChartConfig {
  showTemperature: boolean;
  showHumidity: boolean;
  temperatureUnit: 'F' | 'C';
  chartType: 'line' | 'area';
  showDataPoints: boolean;
  smoothCurves: boolean;
}

export interface ExportFormat {
  type: 'csv' | 'excel' | 'pdf';
  includeMetadata: boolean;
  includeStatistics: boolean;
  dateFormat?: string;
}

export interface CSVExportData {
  timestamp: string;
  sensorName: string;
  location: string;
  temperature: number;
  humidity: number;
  dewpoint: number;
  heatIndex: number;
  batteryLevel?: number;
  signalStrength?: number;
}

export interface HistoricalDataRequest {
  sensorIds: string[];
  startDate: Date;
  endDate: Date;
  dateRange?: DateRange;
  interval?: 'minute' | 'hour' | 'day';
  limit?: number;
  includeStatistics?: boolean;
}

export interface HistoricalDataResponse {
  data: HistoricalDataPoint[];
  statistics: SensorStatistics[];
  totalPoints: number;
  hasMore: boolean;
}

// Predefined date range presets
export const DATE_RANGE_PRESETS: DateRangePreset[] = [
  {
    key: 'last-24h',
    label: 'Last 24 Hours',
    value: {
      start: new Date(Date.now() - 24 * 60 * 60 * 1000),
      end: new Date()
    }
  },
  {
    key: 'last-7d',
    label: 'Last 7 Days',
    value: {
      start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      end: new Date()
    }
  },
  {
    key: 'last-30d',
    label: 'Last 30 Days',
    value: {
      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      end: new Date()
    }
  },
  {
    key: 'last-3m',
    label: 'Last 3 Months',
    value: {
      start: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
      end: new Date()
    }
  },
  {
    key: 'last-6m',
    label: 'Last 6 Months',
    value: {
      start: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000),
      end: new Date()
    }
  },
  {
    key: 'last-year',
    label: 'Last Year',
    value: {
      start: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000),
      end: new Date()
    }
  }
];

// Chart color schemes
export const CHART_COLORS = {
  temperature: {
    primary: '#ef4444', // red-500
    secondary: '#fca5a5', // red-300
    gradient: ['#ef4444', '#fca5a5']
  },
  humidity: {
    primary: '#3b82f6', // blue-500
    secondary: '#93c5fd', // blue-300
    gradient: ['#3b82f6', '#93c5fd']
  }
};

// Temperature thresholds for different storage types
export const TEMPERATURE_THRESHOLDS = {
  freezer: { min: -10, max: 10 },
  refrigerator: { min: 32, max: 40 },
  ambient: { min: 60, max: 80 }
};