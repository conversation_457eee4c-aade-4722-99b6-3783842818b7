// Database schema types for Voice Seafood Manager
// Updated on 2025-08-14 - Added Vendor Report Card System

export interface Supplier {
  id?: string;
  name: string;
  contact_name?: string;
  email?: string;
  phone?: string;
  address?: string;
  status?: 'active' | 'inactive';
  payment_terms?: string;
  credit_limit?: number;
  metadata?: Record<string, unknown>;
  created_at?: string;
  updated_at?: string;
}

export interface Customer {
  id?: string;
  name: string;
  contact_name?: string;
  email?: string;
  phone?: string;
  address?: string;
  channel_type: 'wholesale' | 'retail' | 'distributor';
  customer_source?: string;
  status: 'active' | 'inactive';
  payment_terms?: string;
  credit_limit?: number;
  metadata?: Record<string, unknown>;
  created_at?: string;
  updated_at?: string;
}

export interface Category {
  id?: string;
  name: string;
  description?: string;
  parent_id?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Product {
  id?: string;
  name: string;
  sku?: string;
  description?: string;
  category_id?: string;
  supplier_id?: string;
  unit?: string;
  price?: number;
  cost?: number;
  min_stock?: number;
  current_stock?: number;
  amount?: number;
  condition?: 'fresh' | 'frozen' | 'other';
  other_condition?: string;
  notes?: string;
  supplier?: string;
  images?: string[];
  metadata?: Record<string, unknown>;
  
  // GS1/GDST specific fields
  gtin?: string;
  scientific_name?: string;
  fao_species_code?: string;
  production_method?: string;
  species_details?: {
    scientificName?: string;
    habitat?: string;
    sustainabilityRating?: string;
  };
  created_at?: string;
  updated_at?: string;
}

export interface Batch {
  id?: string;
  product_id: string;
  batch_number: string;
  expiry_date?: string;
  production_date?: string;
  quantity: number;
  remaining_quantity: number;
  cost?: number;
  metadata?: Record<string, unknown>;
  created_at?: string;
  updated_at?: string;
}

export interface Event {
  id?: string;
  event_type: 'receiving' | 'sales' | 'disposal' | 'production' | 'adjustment';
  product_id: string;
  batch_id?: string;
  supplier_id?: string;
  customer_id?: string;
  quantity: number;
  unit: string;
  unit_price?: number;
  unit_cost?: number;
  total_amount?: number;
  quality_status?: string;
  notes?: string;
  metadata?: Record<string, unknown>;
  occurred_at?: string; // When the event actually happened (vs when it was recorded)
  created_at?: string;
  updated_at?: string;
  
  // Voice-specific fields for voice event management
  voice_confidence_score?: number; // 0.0 to 1.0
  voice_confidence_breakdown?: {
    product_match: number;
    quantity_extraction: number;
    vendor_match: number;
    overall: number;
  };
  raw_transcript?: string;
  audio_recording_url?: string;
  created_by_voice?: boolean;
}

export interface InventorySnapshot {
  id?: string;
  product_id: string;
  batch_id?: string;
  quantity: number;
  unit_cost?: number;
  snapshot_date: string;
  created_at?: string;
}

// New interface for COGS tracking
export interface Cogs {
  id?: string;
  batch_id: string;
  raw_product_cost: number;
  shipping_cost?: number;
  handling_cost?: number;
  processing_cost?: number;
  packaging_cost?: number;
  labor_cost?: number;
  other_costs?: number;
  total_cost?: number; // Generated by database
  cost_per_unit?: number;
  notes?: string;
  created_at?: string;
  updated_at?: string;
}

// New interface for Fulfillment tracking
export interface Fulfillment {
  id?: string;
  order_number: string;
  square_transaction_id?: string;
  dbp_order_id?: string;
  batch_id?: string;
  product_id: string;
  customer_id?: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  fulfillment_date: string;
  fulfillment_status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  shipping_method?: string;
  tracking_number?: string;
  notes?: string;
  metadata?: Record<string, unknown>;
  created_at?: string;
  updated_at?: string;
}

// New interface for HACCP logs
export interface HaccpLog {
  id?: string;
  batch_id?: string;
  log_type: 'receiving' | 'processing' | 'storage' | 'shipping' | 'temperature' | 'sanitation' | 'other';
  step_name: string;
  critical_limit?: string;
  monitoring_procedure?: string;
  corrective_action?: string;
  verification_procedure?: string;
  record_keeping?: string;
  temperature?: number;
  humidity?: number;
  ph_value?: number;
  compliance_status: 'compliant' | 'non-compliant' | 'pending' | 'corrected';
  inspector_name?: string;
  details?: string;
  log_date: string;
  created_at?: string;
  updated_at?: string;
}

// ================================================================
// VENDOR REPORT CARD SYSTEM - TYPESCRIPT INTERFACES
// ================================================================

export interface VendorInteraction {
  id?: string;
  vendor_id: string;
  
  // Interaction Details - Updated to match database schema
  interaction_type: 'delivery' | 'order' | 'quality_check' | 'issue_resolution' | 'communication';
  
  // Purchase Order Details
  po_number?: string;
  
  // Order/Delivery Information
  order_date?: string;
  expected_delivery_date?: string;
  actual_delivery_date?: string;
  delivery_window_start?: string;
  delivery_window_end?: string;
  
  // Products and Quantities
  products_ordered?: Array<{
    product_id: string;
    quantity_ordered: number;
    unit_price: number;
  }>;
  products_delivered?: Array<{
    product_id: string;
    product_name?: string;
    quantity_delivered: number;
    unit_price?: number;
    condition: 'fresh' | 'frozen' | 'damaged' | 'expired' | 'good' | 'poor';
    batch_number?: string;
    lot_number?: string;
    temperature?: number;
    expiry_date?: string;
  }>;
  
  // Financial Information
  order_total_amount?: number;
  delivered_amount?: number;
  order_amount?: number;
  delivery_amount?: number;
  discrepancy_amount?: number; // Generated
  
  // Compliance and Quality
  temperature_compliant?: boolean;
  documentation_complete?: boolean;
  product_condition?: 'fresh' | 'frozen' | 'damaged' | 'expired' | 'good' | 'poor';
  
  // Status and Notes
  delivery_status: 'pending' | 'partial' | 'complete' | 'rejected';
  notes?: string;
  internal_notes?: string;
  
  // Automated Flags
  is_on_time?: boolean; // Generated
  is_complete_delivery?: boolean; // Generated
  
  // Metadata
  metadata?: Record<string, unknown>;
  created_at?: string;
  updated_at?: string;
  created_by?: string;
}

export interface VendorRating {
  id?: string;
  vendor_interaction_id: string;
  vendor_id: string;
  
  // Performance Ratings (1-10 scale)
  quality_score?: number;
  delivery_timeliness_score?: number;
  order_accuracy_score?: number;
  communication_score?: number;
  price_competitiveness_score?: number;
  issue_resolution_score?: number;
  
  // Overall Satisfaction
  overall_satisfaction?: number;
  
  // Detailed Feedback
  quality_notes?: string;
  delivery_notes?: string;
  accuracy_notes?: string;
  communication_notes?: string;
  pricing_notes?: string;
  resolution_notes?: string;
  general_feedback?: string;
  
  // Recommendations
  would_reorder?: boolean;
  would_recommend?: boolean;
  
  // Rating metadata
  rated_by?: string;
  rating_date?: string;
  created_at?: string;
  updated_at?: string;
  created_by?: string;
}

export interface VendorMetrics {
  id?: string;
  vendor_id: string;
  
  // Time Period
  calculation_period: 'last_30_days' | 'last_90_days' | 'last_year' | 'all_time';
  period_type: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly' | 'all_time';
  period_start: string;
  period_end: string;
  
  // Delivery Performance Metrics
  total_orders: number;
  completed_orders: number;
  partial_orders: number;
  rejected_orders: number;
  on_time_deliveries: number;
  late_deliveries: number;
  
  // Calculated Performance Percentages (Generated)
  completion_rate?: number;
  on_time_delivery_rate?: number;
  on_time_rate?: number;
  
  // Financial Metrics
  total_order_value: number;
  total_delivered_value: number;
  average_order_value?: number;
  total_discrepancy_value: number;
  
  // Quality Metrics (Averaged ratings)
  avg_quality_score?: number;
  avg_delivery_score?: number;
  avg_accuracy_score?: number;
  avg_communication_score?: number;
  avg_pricing_score?: number;
  avg_resolution_score?: number;
  avg_overall_satisfaction?: number;
  
  // Reliability Metrics
  defect_rate?: number;
  return_rate?: number;
  issue_count: number;
  issue_resolution_rate?: number;
  average_resolution_time_hours?: number;
  resolution_time_avg?: string; // Interval as string
  
  // Ranking and Comparison
  overall_rank?: number;
  category_rank?: number;
  performance_grade?: 'A' | 'B' | 'C' | 'D' | 'F';
  overall_letter_grade?: 'A' | 'B' | 'C' | 'D' | 'F';
  
  // Additional Metrics
  total_interactions?: number;
  average_quality_score?: number;
  
  // Metadata
  last_interaction_date?: string;
  metadata?: Record<string, unknown>;
  created_at?: string;
  updated_at?: string;
  calculated_at?: string;
}

export interface VendorCompliance {
  id?: string;
  vendor_id: string;
  vendor_interaction_id?: string;
  
  // HACCP Compliance
  haccp_certified: boolean;
  haccp_cert_number?: string;
  haccp_cert_expiry_date?: string;
  haccp_cert_expiry?: string;
  temperature_compliant?: boolean;
  temperature_compliance?: boolean;
  temperature_logs_complete: boolean;
  temperature_log_provided: boolean;
  critical_control_points_met: boolean;
  temperature_at_delivery?: number;
  temperature_required_min?: number;
  temperature_required_max?: number;
  
  // Traceability Compliance (GDST standards)
  traceability_complete: boolean;
  catch_certificate_provided: boolean;
  chain_of_custody_complete: boolean;
  species_verification_done: boolean;
  species_verification: boolean;
  origin_documentation: boolean;
  processing_documentation: boolean;
  
  // Compliance Score and Status
  compliance_score?: number;
  compliance_status?: string;
  
  // Documentation Quality Score (1-10)
  documentation_completeness_score?: number;
  documentation_accuracy_score?: number;
  
  // Compliance Issues and Actions
  compliance_issues?: string[];
  corrective_actions_taken?: string[];
  follow_up_required: boolean;
  next_audit_date?: string;
  
  // Assessment metadata
  assessed_date?: string;
  
  // Audit Information
  audited_by?: string;
  audit_date?: string;
  audit_notes?: string;
  
  // Metadata
  metadata?: Record<string, unknown>;
  created_at?: string;
  updated_at?: string;
  created_by?: string;
}

export interface VendorPerformanceAlert {
  id?: string;
  vendor_id: string;
  
  // Alert Details
  alert_type: 'quality_decline' | 'delivery_delay' | 'compliance_issue' | 'cost_increase';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description?: string;
  
  // Trigger Information
  triggered_by_interaction_id?: string;
  triggered_by_metric?: string;
  trigger_condition?: string;
  threshold_value?: number;
  actual_value?: number;
  
  // Status and Resolution
  status: 'active' | 'open' | 'acknowledged' | 'investigating' | 'resolved' | 'dismissed';
  acknowledged_by?: string;
  acknowledged_at?: string;
  resolved_by?: string;
  resolved_at?: string;
  resolution_notes?: string;
  
  // Escalation
  escalated: boolean;
  escalated_to?: string;
  escalated_at?: string;
  
  // Auto-resolve settings
  auto_resolve_after?: string; // Interval as string
  auto_resolve_at?: string;
  
  created_at?: string;
  updated_at?: string;
  created_by?: string;
}

// Enhanced vendor interface with report card data
export interface VendorWithMetrics extends Supplier {
  // Current metrics
  current_metrics?: VendorMetrics;
  lifetime_metrics?: VendorMetrics;
  
  // Recent performance data
  recent_interactions?: VendorInteraction[];
  recent_ratings?: VendorRating[];
  compliance_status?: VendorCompliance;
  
  // Alert information
  open_alerts?: VendorPerformanceAlert[];
  alert_count?: number;
  
  // Dashboard summary fields
  current_month_orders?: number;
  current_month_completion_rate?: number;
  current_month_on_time_rate?: number;
  current_month_satisfaction?: number;
  current_month_grade?: 'A' | 'B' | 'C' | 'D' | 'F';
  current_rank?: number;
  
  lifetime_satisfaction?: number;
  lifetime_completion_rate?: number;
  lifetime_grade?: 'A' | 'B' | 'C' | 'D' | 'F';
  
  last_interaction_date?: string;
  haccp_compliant_deliveries?: number;
  temperature_violations?: number;
}

// View interfaces for dashboard data
export interface VendorInventoryPerformance {
  inventory_event_id: string;
  event_type: string;
  event_date: string;
  quantity: number;
  total_amount: number;
  unit_price: number;
  batch_number?: string;
  condition_on_receipt?: string;
  temperature_at_receipt?: number;
  
  vendor_id: string;
  vendor_name: string;
  vendor_status: string;
  
  product_name: string;
  product_category: string;
  
  vendor_interaction_id?: string;
  delivery_status?: string;
  is_on_time?: boolean;
  is_complete_delivery?: boolean;
  
  overall_satisfaction?: number;
  quality_score?: number;
  
  temperature_compliance?: boolean;
  traceability_complete?: boolean;
  haccp_certified?: boolean;
}

export interface VendorDashboardSummary {
  vendor_id: string;
  vendor_name: string;
  vendor_status: string;
  contact_name?: string;
  email?: string;
  phone?: string;
  
  // Performance metrics
  completion_rate?: number;
  on_time_delivery_rate?: number;
  total_interactions?: number;
  overall_letter_grade?: 'A' | 'B' | 'C' | 'D' | 'F';
  
  // Current month metrics
  current_month_orders?: number;
  current_month_completion_rate?: number;
  current_month_on_time_rate?: number;
  current_month_satisfaction?: number;
  current_month_grade?: 'A' | 'B' | 'C' | 'D' | 'F';
  current_rank?: number;
  
  // Lifetime metrics
  total_lifetime_orders?: number;
  lifetime_completion_rate?: number;
  lifetime_satisfaction?: number;
  lifetime_grade?: 'A' | 'B' | 'C' | 'D' | 'F';
  
  // Recent activity
  last_interaction_date?: string;
  last_delivery_date?: string;
  last_delivery_status?: string;
  
  // Alert counts
  active_alerts_count?: number;
  open_alerts?: number;
  
  // Compliance status
  haccp_compliant_deliveries?: number;
  temperature_violations?: number;
}

// Request/Response types for API
export interface VendorMetricsRequest {
  vendor_id: string;
  period_type?: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly' | 'all_time';
  start_date?: string;
  end_date?: string;
}

export interface VendorRankingRequest {
  period_type?: 'monthly' | 'quarterly' | 'yearly';
  limit?: number;
  sort_by?: 'completion_rate' | 'on_time_rate' | 'overall_satisfaction' | 'performance_grade';
}

export interface VendorComparisonRequest {
  vendor_ids: string[];
  period_type?: 'monthly' | 'quarterly' | 'yearly' | 'all_time';
  metrics?: string[];
}

// Enhanced Vendor type with optional ID field for Supabase compatibility
export interface Vendor extends Supplier {
  // Ensure id is properly typed for existing components
  id?: string;
}

// ================================================================
// VOICE EVENT MANAGEMENT SYSTEM - TYPESCRIPT INTERFACES
// ================================================================

export interface VoiceEvent {
  id?: string;
  event_type: 'receiving' | 'disposal' | 'physical_count' | 'sale';
  product_name: string;
  quantity: number;
  unit: 'lbs' | 'kg' | 'cases' | 'units';
  vendor_name?: string;
  customer_name?: string;
  condition?: 'Excellent' | 'Good' | 'Fair' | 'Poor' | 'Damaged';
  occurred_at: string;
  temperature?: number;
  temperature_unit?: 'fahrenheit' | 'celsius';
  processing_method?: string;
  quality_grade?: string;
  market_form?: string;
  notes?: string;
  
  // Voice-specific fields
  voice_confidence_score: number;
  voice_confidence_breakdown: {
    product_match: number;
    quantity_extraction: number;
    vendor_match: number;
    overall: number;
  };
  raw_transcript: string;
  audio_recording_url?: string;
  created_by_voice: boolean;
  
  // Metadata
  created_at?: string;
  updated_at?: string;
  created_by?: string;
  last_modified_by?: string;
  session_id?: string;
}

export interface EventAudit {
  id?: string;
  event_id: string;
  field_name: string;
  old_value: string | number | boolean | null;
  new_value: string | number | boolean | null;
  changed_by?: string;
  changed_at?: string;
  change_reason?: string;
  change_source?: 'manual' | 'voice' | 'api' | 'system';
  session_id?: string;
}

export interface VoiceEventFilters {
  dateRange?: {
    start: string;
    end: string;
  };
  eventType?: string[];
  confidenceThreshold?: number;
  createdBy?: string;
  searchQuery?: string;
}

export interface VoiceEventResult {
  eventData: VoiceEvent;
  confidence: number;
  requiresConfirmation: boolean;
  audioConfirmation?: string;
}

export interface VoiceEventData {
  event_type: 'receiving' | 'disposal' | 'physical_count' | 'sale';
  product_name: string;
  quantity: number;
  unit: 'lbs' | 'kg' | 'cases' | 'units';
  vendor_name?: string;
  customer_name?: string;
  condition?: 'Excellent' | 'Good' | 'Fair' | 'Poor' | 'Damaged';
  temperature?: number;
  temperature_unit?: 'fahrenheit' | 'celsius';
  processing_method?: string;
  quality_grade?: string;
  market_form?: string;
  notes?: string;
  occurred_at?: string;
  
  // Voice processing fields (when creating from voice input)
  voice_confidence_score?: number;
  voice_confidence_breakdown?: {
    product_match: number;
    quantity_extraction: number;
    vendor_match: number;
    overall: number;
  };
  raw_transcript?: string;
  audio_recording_url?: string;
  session_id?: string;
}

// ================================================================
// TEMPSTICK SENSOR INTEGRATION - TYPESCRIPT INTERFACES
// ================================================================

export interface StorageArea {
  id?: string;
  user_id: string;
  
  // Area identification
  name: string;
  description?: string;
  area_code?: string;
  location?: string;
  
  // Area classification
  area_type: 'walk_in_cooler' | 'walk_in_freezer' | 'reach_in_cooler' | 'reach_in_freezer' | 
             'dry_storage' | 'processing_area' | 'shipping_dock' | 'receiving_area' | 
             'blast_chiller' | 'ice_storage' | 'custom';
  custom_area_type?: string;
  
  // HACCP requirements
  haccp_required?: boolean;
  haccp_ccp_number?: string;
  
  // Temperature requirements
  temp_min_celsius?: number;
  temp_max_celsius?: number;
  temp_min_fahrenheit?: number;
  temp_max_fahrenheit?: number;
  temp_unit?: 'celsius' | 'fahrenheit';
  
  // Humidity requirements
  humidity_min?: number;
  humidity_max?: number;
  
  // Alert configuration
  alert_enabled?: boolean;
  alert_threshold_minutes?: number;
  escalation_minutes?: number;
  
  // Compliance settings
  monitoring_frequency_minutes?: number;
  record_retention_days?: number;
  
  // Status and metadata
  is_active?: boolean;
  capacity_info?: Record<string, unknown>;
  compliance_notes?: string;
  metadata?: Record<string, unknown>;
  
  // Audit timestamps
  created_at?: string;
  updated_at?: string;
  created_by?: string;
}

export interface TempStickSensor {
  id?: string;
  user_id: string;
  storage_area_id?: string;
  
  // TempStick API fields
  sensor_id: string; // TempStick device ID from API
  device_name: string;
  device_type?: string;
  firmware_version?: string;
  
  // Sensor configuration
  name: string;
  description?: string;
  location_description?: string;
  
  // Connection status
  is_online?: boolean;
  last_seen_at?: string;
  connection_status?: 'online' | 'offline' | 'maintenance' | 'error';
  
  // Battery and hardware status
  battery_level?: number; // 0-100 percentage
  battery_voltage?: number;
  signal_strength?: number;
  
  // Calibration settings
  temp_offset_celsius?: number;
  temp_offset_fahrenheit?: number;
  humidity_offset?: number;
  last_calibrated_at?: string;
  calibrated_by?: string;
  
  // Custom alert thresholds (override storage area defaults)
  custom_temp_min_celsius?: number;
  custom_temp_max_celsius?: number;
  custom_temp_min_fahrenheit?: number;
  custom_temp_max_fahrenheit?: number;
  custom_humidity_min?: number;
  custom_humidity_max?: number;
  
  // Data collection settings
  reading_interval_minutes?: number;
  data_sync_enabled?: boolean;
  
  // Status and metadata
  is_active?: boolean;
  installation_date?: string;
  last_maintenance_date?: string;
  next_maintenance_due?: string;
  maintenance_notes?: string;
  metadata?: Record<string, unknown>;
  
  // Audit timestamps
  created_at?: string;
  updated_at?: string;
  created_by?: string;
}

export interface TemperatureReading {
  id?: string;
  user_id: string;
  sensor_id: string;
  storage_area_id?: string;
  
  // Reading data
  recorded_at: string;
  temp_celsius: number;
  temp_fahrenheit: number;
  humidity?: number; // Percentage
  
  // Data quality indicators
  reading_quality?: 'good' | 'acceptable' | 'poor' | 'error';
  signal_strength?: number;
  battery_level?: number;
  
  // HACCP compliance flags
  within_safe_range?: boolean;
  temp_violation?: boolean;
  humidity_violation?: boolean;
  
  // TempStick API metadata
  api_reading_id?: string;
  sync_status?: 'pending' | 'synced' | 'error' | 'manual';
  
  // Data source tracking
  data_source?: 'tempstick_api' | 'manual_entry' | 'calibration' | 'estimated';
  
  // Metadata
  metadata?: Record<string, unknown>;
  created_at?: string;
}

export interface TemperatureAlert {
  id?: string;
  user_id: string;
  sensor_id: string;
  storage_area_id?: string;
  reading_id?: string;
  
  // Alert classification
  alert_type: 'temp_high' | 'temp_low' | 'humidity_high' | 'humidity_low' | 
              'sensor_offline' | 'battery_low' | 'calibration_due' | 'maintenance_due' |
              'data_gap' | 'system_error';
  
  // Alert severity and status
  severity: 'info' | 'warning' | 'critical' | 'emergency';
  alert_status?: 'active' | 'acknowledged' | 'investigating' | 'resolved' | 'dismissed';
  
  // Alert details
  title: string;
  message: string;
  threshold_value?: number;
  actual_value?: number;
  deviation?: number;
  
  // Timing information
  first_detected_at: string;
  last_detected_at?: string;
  duration_minutes?: number;
  
  // HACCP compliance impact
  haccp_violation?: boolean;
  regulatory_notification_required?: boolean;
  product_safety_risk?: 'none' | 'low' | 'medium' | 'high' | 'critical';
  
  // Response tracking
  acknowledged_at?: string;
  acknowledged_by?: string;
  resolved_at?: string;
  resolved_by?: string;
  resolution_notes?: string;
  corrective_actions_taken?: string;
  
  // Escalation tracking
  escalated?: boolean;
  escalated_at?: string;
  escalated_to?: string;
  escalation_level?: number;
  
  // Notification tracking
  email_sent?: boolean;
  email_sent_at?: string;
  sms_sent?: boolean;
  sms_sent_at?: string;
  notification_attempts?: number;
  
  // Auto-resolution settings
  auto_resolve_enabled?: boolean;
  auto_resolve_after_minutes?: number;
  
  // Affected inventory tracking
  affected_products?: Record<string, unknown>[];
  inventory_impact_assessed?: boolean;
  
  // Metadata and audit
  metadata?: Record<string, unknown>;
  created_at?: string;
  updated_at?: string;
}

// Dashboard view interfaces
export interface SensorStatusDashboard {
  id: string;
  user_id: string;
  sensor_id: string;
  name: string;
  device_name: string;
  connection_status: string;
  is_online: boolean;
  battery_level?: number;
  last_seen_at?: string;
  
  // Storage area info
  storage_area_name?: string;
  area_type?: string;
  haccp_required?: boolean;
  
  // Latest reading
  last_reading_at?: string;
  temp_celsius?: number;
  temp_fahrenheit?: number;
  humidity?: number;
  within_safe_range?: boolean;
  temp_violation?: boolean;
  humidity_violation?: boolean;
  
  // Active alerts count
  active_alerts_count: number;
  critical_alerts_count: number;
}

export interface HACCPComplianceDashboard {
  storage_area_id: string;
  user_id: string;
  storage_area_name: string;
  area_type: string;
  haccp_ccp_number?: string;
  temp_min_celsius?: number;
  temp_max_celsius?: number;
  temp_min_fahrenheit?: number;
  temp_max_fahrenheit?: number;
  
  // Sensor count
  total_sensors?: number;
  online_sensors?: number;
  offline_sensors?: number;
  
  // Violation summary (last 24 hours)
  total_readings?: number;
  violation_readings?: number;
  violation_percentage?: number;
  last_violation_at?: string;
  
  // Current compliance status
  compliance_status: 'compliant' | 'marginal' | 'non_compliant' | 'sensors_offline';
}

// API request/response types for TempStick integration
export interface TempStickApiReading {
  id: string;
  deviceId: string;
  timestamp: string;
  temperature: number; // Fahrenheit
  humidity?: number;
  batteryLevel?: number;
  signalStrength?: number;
}

export interface TempStickDevice {
  id: string;
  name: string;
  type: string;
  firmwareVersion?: string;
  lastSeen: string;
  isOnline: boolean;
  batteryLevel?: number;
  signalStrength?: number;
}

export interface TemperatureDataRequest {
  sensor_id?: string;
  storage_area_id?: string;
  start_date: string;
  end_date: string;
  interval?: 'minute' | 'hour' | 'day';
  include_violations_only?: boolean;
}

export interface TemperatureDataResponse {
  readings: TemperatureReading[];
  summary: {
    total_readings: number;
    violation_count: number;
    violation_percentage: number;
    avg_temperature: number;
    min_temperature: number;
    max_temperature: number;
    avg_humidity?: number;
  };
}

export interface AlertNotificationSettings {
  email_enabled: boolean;
  sms_enabled: boolean;
  email_addresses: string[];
  phone_numbers: string[];
  escalation_delay_minutes: number;
  auto_resolve_enabled: boolean;
  auto_resolve_minutes: number;
}

// Enhanced sensor interface with real-time data
export interface SensorWithRealtimeData extends TempStickSensor {
  latest_reading?: TemperatureReading;
  active_alerts?: TemperatureAlert[];
  storage_area?: StorageArea;
  compliance_status?: 'compliant' | 'violation' | 'offline';
  violation_count_24h?: number;
  last_violation_at?: string;
}

// ================================================================
// INVENTORY-TEMPERATURE INTEGRATION INTERFACES
// ================================================================

export interface ProductStorageRequirements {
  id?: string;
  user_id: string;
  product_id: string;
  
  // Temperature requirements
  required_temp_min_celsius?: number;
  required_temp_max_celsius?: number;
  required_temp_min_fahrenheit?: number;
  required_temp_max_fahrenheit?: number;
  temp_unit?: 'celsius' | 'fahrenheit';
  
  // Humidity requirements
  required_humidity_min?: number;
  required_humidity_max?: number;
  
  // Cold chain requirements
  cold_chain_critical?: boolean;
  max_temp_excursion_minutes?: number;
  
  // HACCP requirements
  haccp_monitoring_required?: boolean;
  ccp_designation?: string;
  
  // Storage preferences
  preferred_storage_areas?: string[];
  prohibited_storage_areas?: string[];
  
  // Shelf life impact
  temp_affects_shelf_life?: boolean;
  shelf_life_temp_factor?: number;
  
  // Metadata
  special_handling_notes?: string;
  regulatory_requirements?: string;
  metadata?: Record<string, unknown>;
  
  created_at?: string;
  updated_at?: string;
  created_by?: string;
}

export interface TemperatureEvent {
  id?: string;
  user_id: string;
  
  // Links
  inventory_event_id?: string;
  sensor_id?: string;
  storage_area_id?: string;
  product_id?: string;
  
  // Event details
  event_type: 'receiving_temp_check' | 'storage_temp_verification' | 'pre_sale_temp_check' |
              'cold_chain_verification' | 'haccp_monitoring' | 'quality_assessment';
  
  // Temperature data
  recorded_at: string;
  temp_celsius: number;
  temp_fahrenheit: number;
  humidity?: number;
  
  // Compliance assessment
  within_product_requirements: boolean;
  within_storage_requirements: boolean;
  cold_chain_maintained?: boolean;
  
  // Requirements vs actual
  required_min_temp?: number;
  required_max_temp?: number;
  temp_deviation?: number;
  
  // Quality impact
  quality_impact?: 'none' | 'minimal' | 'moderate' | 'significant' | 'severe';
  shelf_life_impact_hours?: number;
  corrective_action_required?: boolean;
  corrective_action_taken?: string;
  
  // Personnel and documentation
  checked_by?: string;
  verification_method?: string;
  documentation_complete?: boolean;
  
  // Notes and metadata
  notes?: string;
  metadata?: Record<string, unknown>;
  
  created_at?: string;
  created_by?: string;
}

// Enhanced inventory event with temperature data
export interface InventoryEventWithTemperature extends Event {
  // Temperature fields
  storage_area_id?: string;
  temp_at_event_celsius?: number;
  temp_at_event_fahrenheit?: number;
  humidity_at_event?: number;
  temp_compliant?: boolean;
  temp_sensor_id?: string;
  temp_reading_id?: string;
  cold_chain_maintained?: boolean;
  temp_violation_notes?: string;
  
  // Joined data from view
  storage_area_name?: string;
  area_type?: string;
  temp_min_fahrenheit?: number;
  temp_max_fahrenheit?: number;
  haccp_required?: boolean;
  required_temp_min_fahrenheit?: number;
  required_temp_max_fahrenheit?: number;
  cold_chain_critical?: boolean;
  haccp_monitoring_required?: boolean;
  current_storage_temp?: number;
  current_storage_humidity?: number;
  last_temp_reading_at?: string;
  storage_temp_compliant?: boolean;
  total_temp_events?: number;
  non_compliant_events?: number;
  last_temp_check_at?: string;
  corrective_actions_required?: number;
  temperature_compliance_status?: 'compliant' | 'temp_violation' | 'cold_chain_broken' | 
                                  'temp_non_compliant' | 'monitoring_gap';
}

// Enhanced HACCP compliance dashboard with inventory data
export interface EnhancedHACCPComplianceDashboard extends HACCPComplianceDashboard {
  // Inventory temperature compliance (last 7 days)
  total_inventory_events?: number;
  temp_non_compliant_events?: number;
  cold_chain_violations?: number;
  last_temp_event_at?: string;
}

// Temperature compliance summary for products
export interface ProductTemperatureCompliance {
  product_id: string;
  product_name: string;
  
  // Storage requirements
  has_storage_requirements: boolean;
  cold_chain_critical: boolean;
  haccp_monitoring_required: boolean;
  
  // Current storage status
  current_storage_areas: Array<{
    storage_area_id: string;
    storage_area_name: string;
    current_temp: number;
    temp_compliant: boolean;
    last_reading_at: string;
  }>;
  
  // Compliance metrics (last 30 days)
  total_inventory_events: number;
  temp_compliant_events: number;
  cold_chain_violations: number;
  compliance_percentage: number;
  
  // Risk assessment
  current_risk_level: 'low' | 'medium' | 'high' | 'critical';
  risk_factors: string[];
  
  last_temp_check_at?: string;
  next_required_check?: string;
}

// Temperature trend data for charts and analytics
export interface TemperatureTrendData {
  timestamp: string;
  sensor_id: string;
  sensor_name: string;
  storage_area_name: string;
  temp_fahrenheit: number;
  temp_celsius: number;
  humidity?: number;
  within_safe_range: boolean;
  safe_range_min: number;
  safe_range_max: number;
  violation_type?: 'temp_high' | 'temp_low' | 'humidity_high' | 'humidity_low';
}

// Alert summary for dashboard widgets
export interface TemperatureAlertSummary {
  total_active_alerts: number;
  critical_alerts: number;
  warning_alerts: number;
  info_alerts: number;
  
  haccp_violations: number;
  cold_chain_breaks: number;
  sensor_offline_count: number;
  battery_low_count: number;
  
  most_recent_alert?: {
    id: string;
    title: string;
    severity: string;
    created_at: string;
    storage_area_name: string;
  };
  
  alerts_by_area: Array<{
    storage_area_id: string;
    storage_area_name: string;
    active_count: number;
    critical_count: number;
  }>;
}
