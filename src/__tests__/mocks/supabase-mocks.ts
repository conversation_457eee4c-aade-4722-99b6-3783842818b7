/**
 * Supabase Mocks for Sensor Dashboard Testing
 * 
 * Provides comprehensive mocking for Supabase client operations
 * including real-time subscriptions, database queries, and error scenarios.
 */

import { vi } from 'vitest';
import type { 
  SupabaseClient, 
  PostgrestFilterBuilder,
  PostgrestQueryBuilder,
  RealtimeChannel,
  RealtimePostgresChangesPayload
} from '@supabase/supabase-js';

// Mock data types
interface MockSupabaseResponse<T> {
  data: T;
  error: null;
}

interface MockSupabaseError {
  data: null;
  error: {
    message: string;
    code?: string;
    details?: string;
  };
}

type MockResponse<T> = MockSupabaseResponse<T> | MockSupabaseError;

// Mock query builder that supports method chaining
class MockPostgrestQueryBuilder {
  private tableName: string;
  private mockData: any[] = [];
  private mockError: any = null;
  private filters: any[] = [];

  constructor(tableName: string) {
    this.tableName = tableName;
  }

  // Set mock data for this query
  setMockData(data: any[]): this {
    this.mockData = data;
    return this;
  }

  setMockError(error: any): this {
    this.mockError = error;
    return this;
  }

  // Query methods
  select(columns?: string) {
    return this;
  }

  insert(data: any) {
    return this;
  }

  update(data: any) {
    return this;
  }

  delete() {
    return this;
  }

  // Filter methods
  eq(column: string, value: any) {
    this.filters.push({ type: 'eq', column, value });
    return this;
  }

  neq(column: string, value: any) {
    this.filters.push({ type: 'neq', column, value });
    return this;
  }

  gt(column: string, value: any) {
    this.filters.push({ type: 'gt', column, value });
    return this;
  }

  gte(column: string, value: any) {
    this.filters.push({ type: 'gte', column, value });
    return this;
  }

  lt(column: string, value: any) {
    this.filters.push({ type: 'lt', column, value });
    return this;
  }

  lte(column: string, value: any) {
    this.filters.push({ type: 'lte', column, value });
    return this;
  }

  like(column: string, pattern: string) {
    this.filters.push({ type: 'like', column, pattern });
    return this;
  }

  in(column: string, values: any[]) {
    this.filters.push({ type: 'in', column, values });
    return this;
  }

  // Ordering and pagination
  order(column: string, options?: { ascending?: boolean }) {
    return this;
  }

  limit(count: number) {
    return this;
  }

  range(from: number, to: number) {
    return this;
  }

  // Execute query
  async then(resolve: (value: any) => void, reject?: (reason: any) => void) {
    return new Promise((res, rej) => {
      setTimeout(() => {
        if (this.mockError) {
          const errorResult = { data: null, error: this.mockError };
          reject ? rej(errorResult) : res(errorResult);
        } else {
          let filteredData = [...this.mockData];
          
          // Apply filters
          for (const filter of this.filters) {
            filteredData = this.applyFilter(filteredData, filter);
          }
          
          const result = { data: filteredData, error: null };
          res(result);
        }
      }, 10); // Simulate async delay
    }).then(resolve, reject);
  }

  private applyFilter(data: any[], filter: any): any[] {
    const { type, column, value, pattern, values } = filter;
    
    return data.filter(item => {
      const itemValue = item[column];
      
      switch (type) {
        case 'eq':
          return itemValue === value;
        case 'neq':
          return itemValue !== value;
        case 'gt':
          return itemValue > value;
        case 'gte':
          return itemValue >= value;
        case 'lt':
          return itemValue < value;
        case 'lte':
          return itemValue <= value;
        case 'like':
          return String(itemValue).includes(pattern.replace(/%/g, ''));
        case 'in':
          return values.includes(itemValue);
        default:
          return true;
      }
    });
  }
}

// Mock realtime channel
class MockRealtimeChannel {
  private channelName: string;
  private callbacks: Map<string, Function[]> = new Map();

  constructor(channelName: string) {
    this.channelName = channelName;
  }

  on(event: string, callback: Function) {
    const callbacks = this.callbacks.get(event) || [];
    callbacks.push(callback);
    this.callbacks.set(event, callbacks);
    return this;
  }

  subscribe(callback?: (status: string) => void) {
    setTimeout(() => callback?.('SUBSCRIBED'), 10);
    return this;
  }

  unsubscribe() {
    this.callbacks.clear();
    return Promise.resolve({ error: null });
  }

  // Test helper to trigger events
  triggerEvent(event: string, payload: any) {
    const callbacks = this.callbacks.get(event) || [];
    callbacks.forEach(callback => {
      try {
        callback(payload);
      } catch (error) {
        console.warn('Mock realtime callback error:', error);
      }
    });
  }
}

// Mock Supabase client
export const createMockSupabaseClient = () => {
  const mockQueryBuilders: Map<string, MockPostgrestQueryBuilder> = new Map();
  const mockChannels: Map<string, MockRealtimeChannel> = new Map();

  const mockClient = {
    // Database operations
    from(tableName: string) {
      let queryBuilder = mockQueryBuilders.get(tableName);
      if (!queryBuilder) {
        queryBuilder = new MockPostgrestQueryBuilder(tableName);
        mockQueryBuilders.set(tableName, queryBuilder);
      }
      return queryBuilder;
    },

    // Realtime operations
    channel(channelName: string) {
      let channel = mockChannels.get(channelName);
      if (!channel) {
        channel = new MockRealtimeChannel(channelName);
        mockChannels.set(channelName, channel);
      }
      return channel;
    },

    // Test helpers
    __setMockData(tableName: string, data: any[]) {
      const queryBuilder = this.from(tableName) as MockPostgrestQueryBuilder;
      queryBuilder.setMockData(data);
    },

    __setMockError(tableName: string, error: any) {
      const queryBuilder = this.from(tableName) as MockPostgrestQueryBuilder;
      queryBuilder.setMockError(error);
    },

    __triggerRealtimeEvent(channelName: string, event: string, payload: any) {
      const channel = mockChannels.get(channelName);
      if (channel) {
        channel.triggerEvent(event, payload);
      }
    },

    __getChannel(channelName: string) {
      return mockChannels.get(channelName);
    },

    __clearMocks() {
      mockQueryBuilders.clear();
      mockChannels.clear();
    }
  };

  return mockClient;
};

// Pre-configured mock scenarios
export const mockScenarios = {
  // Normal operation with sample sensor data
  normalOperation: (mockClient: ReturnType<typeof createMockSupabaseClient>) => {
    mockClient.__setMockData('sensors', [
      {
        id: 'sensor-001',
        name: 'Freezer Unit A',
        location: 'Cold Storage',
        is_online: true,
        battery_level: 85,
        last_reading_at: new Date().toISOString(),
      },
      {
        id: 'sensor-002', 
        name: 'Cooler Unit B',
        location: 'Display Area',
        is_online: true,
        battery_level: 92,
        last_reading_at: new Date().toISOString(),
      }
    ]);

    mockClient.__setMockData('temperature_readings', [
      {
        id: 'reading-001',
        sensor_id: 'sensor-001',
        temperature: -18.5,
        humidity: 65.2,
        recorded_at: new Date().toISOString(),
      },
      {
        id: 'reading-002',
        sensor_id: 'sensor-002',
        temperature: 4.2,
        humidity: 70.1,
        recorded_at: new Date().toISOString(),
      }
    ]);

    mockClient.__setMockData('temperature_alerts', []);
  },

  // Alert scenario with high temperature
  temperatureAlert: (mockClient: ReturnType<typeof createMockSupabaseClient>) => {
    mockClient.__setMockData('sensors', [
      {
        id: 'sensor-001',
        name: 'Freezer Unit A',
        location: 'Cold Storage',
        is_online: true,
        battery_level: 85,
        last_reading_at: new Date().toISOString(),
      }
    ]);

    mockClient.__setMockData('temperature_readings', [
      {
        id: 'reading-001',
        sensor_id: 'sensor-001', 
        temperature: -5.0, // Too high for freezer
        humidity: 65.2,
        recorded_at: new Date().toISOString(),
      }
    ]);

    mockClient.__setMockData('temperature_alerts', [
      {
        id: 'alert-001',
        sensor_id: 'sensor-001',
        alert_type: 'high_temp',
        severity: 'critical',
        message: 'Temperature exceeded critical threshold',
        created_at: new Date().toISOString(),
        is_resolved: false,
      }
    ]);
  },

  // Offline sensor scenario
  offlineSensor: (mockClient: ReturnType<typeof createMockSupabaseClient>) => {
    mockClient.__setMockData('sensors', [
      {
        id: 'sensor-001',
        name: 'Freezer Unit A',
        location: 'Cold Storage',
        is_online: false,
        battery_level: 15, // Low battery
        last_reading_at: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
      }
    ]);

    mockClient.__setMockData('temperature_readings', []);

    mockClient.__setMockData('temperature_alerts', [
      {
        id: 'alert-001',
        sensor_id: 'sensor-001',
        alert_type: 'sensor_offline',
        severity: 'high',
        message: 'Sensor has been offline for more than 30 minutes',
        created_at: new Date().toISOString(),
        is_resolved: false,
      }
    ]);
  },

  // Network error scenario
  networkError: (mockClient: ReturnType<typeof createMockSupabaseClient>) => {
    const networkError = {
      message: 'Network request failed',
      code: 'NETWORK_ERROR',
      details: 'Unable to connect to temperature monitoring service'
    };

    mockClient.__setMockError('sensors', networkError);
    mockClient.__setMockError('temperature_readings', networkError);
    mockClient.__setMockError('temperature_alerts', networkError);
  },

  // Large dataset for performance testing
  largeDataset: (mockClient: ReturnType<typeof createMockSupabaseClient>) => {
    const sensors = Array.from({ length: 100 }, (_, i) => ({
      id: `sensor-${i.toString().padStart(3, '0')}`,
      name: `Sensor ${i + 1}`,
      location: `Location ${Math.floor(i / 10) + 1}`,
      is_online: Math.random() > 0.1,
      battery_level: Math.floor(Math.random() * 100),
      last_reading_at: new Date(Date.now() - Math.random() * 86400000).toISOString(),
    }));

    const readings = sensors.map(sensor => ({
      id: `reading-${sensor.id}`,
      sensor_id: sensor.id,
      temperature: -20 + Math.random() * 40,
      humidity: 30 + Math.random() * 40,
      recorded_at: new Date().toISOString(),
    }));

    const alerts = sensors
      .filter(() => Math.random() > 0.8) // 20% chance of alert
      .map(sensor => ({
        id: `alert-${sensor.id}`,
        sensor_id: sensor.id,
        alert_type: ['high_temp', 'low_temp', 'sensor_offline'][Math.floor(Math.random() * 3)],
        severity: ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)],
        message: 'Simulated alert for testing',
        created_at: new Date().toISOString(),
        is_resolved: Math.random() > 0.5,
      }));

    mockClient.__setMockData('sensors', sensors);
    mockClient.__setMockData('temperature_readings', readings);
    mockClient.__setMockData('temperature_alerts', alerts);
  },
};

// Global mock setup for tests
export const setupSupabaseMocks = () => {
  const mockClient = createMockSupabaseClient();
  
  // Mock the Supabase module
  vi.mock('../../lib/supabase', () => ({
    supabase: mockClient,
  }));

  return mockClient;
};