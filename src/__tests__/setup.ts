/**
 * Test Setup Configuration
 * 
 * Global test setup for Vitest including DOM environment, mocks, and utilities.
 */

import { vi } from 'vitest';
import '@testing-library/jest-dom';

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn(() => ({
  disconnect: vi.fn(),
  observe: vi.fn(),
  unobserve: vi.fn(),
}));

// Mock ResizeObserver
global.ResizeObserver = vi.fn(() => ({
  disconnect: vi.fn(),
  observe: vi.fn(),
  unobserve: vi.fn(),
}));

// Mock HTMLElement.scrollIntoView
global.HTMLElement.prototype.scrollIntoView = vi.fn();

// Mock window.getComputedStyle for CSS testing
const mockGetComputedStyle = vi.fn(() => ({
  getPropertyValue: vi.fn((prop: string) => {
    // Return appropriate values for CSS custom properties used in tests
    const customProps: Record<string, string> = {
      '--chart-background': '#ffffff',
      '--chart-text': '#374151',
      '--chart-grid': '#e5e7eb',
      '--chart-border': '#d1d5db',
    };
    return customProps[prop] || '';
  }),
  backgroundColor: 'rgb(255, 255, 255)',
  color: 'rgb(0, 0, 0)',
  borderColor: 'rgb(0, 0, 0)',
}));

Object.defineProperty(window, 'getComputedStyle', {
  value: mockGetComputedStyle,
});

// Setup default data-testids for components that might be missing them
beforeAll(() => {
  // Add data-testid to sidebar if it doesn't exist
  const originalQuerySelector = document.querySelector;
  document.querySelector = function(selector: string) {
    const result = originalQuerySelector.call(this, selector);
    
    // Auto-add data-testid for common elements during tests
    if (selector.includes('[data-testid="sidebar"]') && !result) {
      const sidebar = this.querySelector('nav');
      if (sidebar && !sidebar.getAttribute('data-testid')) {
        sidebar.setAttribute('data-testid', 'sidebar');
      }
      return sidebar;
    }
    
    return result;
  };
});