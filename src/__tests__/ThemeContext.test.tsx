/**
 * ThemeContext Unit Tests
 * 
 * Comprehensive unit tests for the ThemeContext, ThemeProvider, and useTheme hook.
 * Tests theme state management, persistence, system preference detection, and utility hooks.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { act } from '@testing-library/react';
import React from 'react';

import { ThemeProvider, useTheme, useThemeAwareStyles, type Theme } from '../contexts/ThemeContext';

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock matchMedia
const mockMatchMedia = vi.fn();
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: mockMatchMedia,
});

// Test component that uses the theme context
function TestThemeConsumer() {
  const { theme, resolvedTheme, setTheme, toggleTheme } = useTheme();
  const styles = useThemeAwareStyles();
  
  return (
    <div data-testid="theme-consumer">
      <div data-testid="current-theme">{theme}</div>
      <div data-testid="resolved-theme">{resolvedTheme}</div>
      <div data-testid="is-dark">{styles.isDark ? 'dark' : 'light'}</div>
      <button data-testid="set-light" onClick={() => setTheme('light')}>
        Set Light
      </button>
      <button data-testid="set-dark" onClick={() => setTheme('dark')}>
        Set Dark
      </button>
      <button data-testid="set-system" onClick={() => setTheme('system')}>
        Set System
      </button>
      <button data-testid="toggle-theme" onClick={toggleTheme}>
        Toggle Theme
      </button>
      <div data-testid="bg-primary" className={styles.bg.primary}>Background</div>
      <div data-testid="text-primary" className={styles.text.primary}>Text</div>
    </div>
  );
}

describe('ThemeContext', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
    
    // Mock a default matchMedia that doesn't prefer dark
    mockMatchMedia.mockImplementation((query) => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    }));
    
    // Clear document classes
    document.documentElement.className = '';
  });

  afterEach(() => {
    document.documentElement.className = '';
    document.documentElement.style.cssText = '';
  });

  describe('ThemeProvider Initialization', () => {
    it('should initialize with system theme by default', () => {
      render(
        <ThemeProvider>
          <TestThemeConsumer />
        </ThemeProvider>
      );
      
      expect(screen.getByTestId('current-theme')).toHaveTextContent('system');
      expect(screen.getByTestId('resolved-theme')).toHaveTextContent('light');
    });

    it('should initialize with custom default theme', () => {
      render(
        <ThemeProvider defaultTheme="dark">
          <TestThemeConsumer />
        </ThemeProvider>
      );
      
      expect(screen.getByTestId('current-theme')).toHaveTextContent('dark');
      expect(screen.getByTestId('resolved-theme')).toHaveTextContent('dark');
    });

    it('should load saved theme from localStorage', () => {
      localStorageMock.getItem.mockReturnValue('dark');
      
      render(
        <ThemeProvider>
          <TestThemeConsumer />
        </ThemeProvider>
      );
      
      expect(localStorageMock.getItem).toHaveBeenCalledWith('seafood-manager-theme');
      expect(screen.getByTestId('current-theme')).toHaveTextContent('dark');
    });

    it('should handle invalid localStorage values', () => {
      localStorageMock.getItem.mockReturnValue('invalid-theme');
      
      render(
        <ThemeProvider>
          <TestThemeConsumer />
        </ThemeProvider>
      );
      
      // Should fallback to default system theme
      expect(screen.getByTestId('current-theme')).toHaveTextContent('system');
    });

    it('should handle localStorage access errors', () => {
      localStorageMock.getItem.mockImplementation(() => {
        throw new Error('localStorage not available');
      });
      
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      
      render(
        <ThemeProvider>
          <TestThemeConsumer />
        </ThemeProvider>
      );
      
      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to load theme preference:',
        expect.any(Error)
      );
      
      consoleSpy.mockRestore();
    });
  });

  describe('System Theme Detection', () => {
    it('should resolve system theme to light when media query does not match', () => {
      mockMatchMedia.mockImplementation((query) => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }));
      
      render(
        <ThemeProvider defaultTheme="system">
          <TestThemeConsumer />
        </ThemeProvider>
      );
      
      expect(screen.getByTestId('resolved-theme')).toHaveTextContent('light');
    });

    it('should resolve system theme to dark when media query matches', () => {
      mockMatchMedia.mockImplementation((query) => ({
        matches: query === '(prefers-color-scheme: dark)',
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }));
      
      render(
        <ThemeProvider defaultTheme="system">
          <TestThemeConsumer />
        </ThemeProvider>
      );
      
      expect(screen.getByTestId('resolved-theme')).toHaveTextContent('dark');
    });

    it('should listen for system theme changes', () => {
      const mockAddListener = vi.fn();
      const mockAddEventListener = vi.fn();
      
      mockMatchMedia.mockImplementation(() => ({
        matches: false,
        media: '(prefers-color-scheme: dark)',
        onchange: null,
        addListener: mockAddListener,
        removeListener: vi.fn(),
        addEventListener: mockAddEventListener,
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }));
      
      render(
        <ThemeProvider defaultTheme="system">
          <TestThemeConsumer />
        </ThemeProvider>
      );
      
      // Should set up listeners for system theme changes
      expect(mockAddListener).toHaveBeenCalled();
      expect(mockAddEventListener).toHaveBeenCalledWith('change', expect.any(Function));
    });
  });

  describe('Theme Setting and Persistence', () => {
    it('should set light theme correctly', async () => {
      render(
        <ThemeProvider>
          <TestThemeConsumer />
        </ThemeProvider>
      );
      
      await act(async () => {
        fireEvent.click(screen.getByTestId('set-light'));
      });
      
      expect(screen.getByTestId('current-theme')).toHaveTextContent('light');
      expect(screen.getByTestId('resolved-theme')).toHaveTextContent('light');
      expect(localStorageMock.setItem).toHaveBeenCalledWith('seafood-manager-theme', 'light');
    });

    it('should set dark theme correctly', async () => {
      render(
        <ThemeProvider>
          <TestThemeConsumer />
        </ThemeProvider>
      );
      
      await act(async () => {
        fireEvent.click(screen.getByTestId('set-dark'));
      });
      
      expect(screen.getByTestId('current-theme')).toHaveTextContent('dark');
      expect(screen.getByTestId('resolved-theme')).toHaveTextContent('dark');
      expect(localStorageMock.setItem).toHaveBeenCalledWith('seafood-manager-theme', 'dark');
    });

    it('should set system theme correctly', async () => {
      render(
        <ThemeProvider>
          <TestThemeConsumer />
        </ThemeProvider>
      );
      
      await act(async () => {
        fireEvent.click(screen.getByTestId('set-system'));
      });
      
      expect(screen.getByTestId('current-theme')).toHaveTextContent('system');
      expect(localStorageMock.setItem).toHaveBeenCalledWith('seafood-manager-theme', 'system');
    });

    it('should handle localStorage write errors', async () => {
      localStorageMock.setItem.mockImplementation(() => {
        throw new Error('localStorage write failed');
      });
      
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      
      render(
        <ThemeProvider>
          <TestThemeConsumer />
        </ThemeProvider>
      );
      
      await act(async () => {
        fireEvent.click(screen.getByTestId('set-dark'));
      });
      
      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to save theme preference:',
        expect.any(Error)
      );
      
      consoleSpy.mockRestore();
    });
  });

  describe('DOM Updates', () => {
    it('should add light class to html element for light theme', async () => {
      render(
        <ThemeProvider>
          <TestThemeConsumer />
        </ThemeProvider>
      );
      
      await act(async () => {
        fireEvent.click(screen.getByTestId('set-light'));
      });
      
      expect(document.documentElement.classList.contains('light')).toBe(true);
      expect(document.documentElement.classList.contains('dark')).toBe(false);
    });

    it('should add dark class to html element for dark theme', async () => {
      render(
        <ThemeProvider>
          <TestThemeConsumer />
        </ThemeProvider>
      );
      
      await act(async () => {
        fireEvent.click(screen.getByTestId('set-dark'));
      });
      
      expect(document.documentElement.classList.contains('dark')).toBe(true);
      expect(document.documentElement.classList.contains('light')).toBe(false);
    });

    it('should update CSS custom properties for light theme', async () => {
      render(
        <ThemeProvider>
          <TestThemeConsumer />
        </ThemeProvider>
      );
      
      await act(async () => {
        fireEvent.click(screen.getByTestId('set-light'));
      });
      
      const style = document.documentElement.style;
      expect(style.getPropertyValue('--chart-background')).toBe('#ffffff');
      expect(style.getPropertyValue('--chart-text')).toBe('#374151');
      expect(style.getPropertyValue('--chart-grid')).toBe('#e5e7eb');
      expect(style.getPropertyValue('--chart-border')).toBe('#d1d5db');
    });

    it('should update CSS custom properties for dark theme', async () => {
      render(
        <ThemeProvider>
          <TestThemeConsumer />
        </ThemeProvider>
      );
      
      await act(async () => {
        fireEvent.click(screen.getByTestId('set-dark'));
      });
      
      const style = document.documentElement.style;
      expect(style.getPropertyValue('--chart-background')).toBe('#1f2937');
      expect(style.getPropertyValue('--chart-text')).toBe('#f3f4f6');
      expect(style.getPropertyValue('--chart-grid')).toBe('#374151');
      expect(style.getPropertyValue('--chart-border')).toBe('#4b5563');
    });
  });

  describe('Toggle Theme Functionality', () => {
    it('should toggle from system to opposite of system preference', async () => {
      // Mock system preference as light
      mockMatchMedia.mockImplementation((query) => ({
        matches: false, // light preference
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }));
      
      render(
        <ThemeProvider defaultTheme="system">
          <TestThemeConsumer />
        </ThemeProvider>
      );
      
      expect(screen.getByTestId('current-theme')).toHaveTextContent('system');
      expect(screen.getByTestId('resolved-theme')).toHaveTextContent('light');
      
      await act(async () => {
        fireEvent.click(screen.getByTestId('toggle-theme'));
      });
      
      // Should toggle to dark (opposite of light system preference)
      expect(screen.getByTestId('current-theme')).toHaveTextContent('dark');
      expect(screen.getByTestId('resolved-theme')).toHaveTextContent('dark');
    });

    it('should toggle from light to dark', async () => {
      render(
        <ThemeProvider defaultTheme="light">
          <TestThemeConsumer />
        </ThemeProvider>
      );
      
      await act(async () => {
        fireEvent.click(screen.getByTestId('toggle-theme'));
      });
      
      expect(screen.getByTestId('current-theme')).toHaveTextContent('dark');
    });

    it('should toggle from dark to light', async () => {
      render(
        <ThemeProvider defaultTheme="dark">
          <TestThemeConsumer />
        </ThemeProvider>
      );
      
      await act(async () => {
        fireEvent.click(screen.getByTestId('toggle-theme'));
      });
      
      expect(screen.getByTestId('current-theme')).toHaveTextContent('light');
    });
  });

  describe('useThemeAwareStyles Hook', () => {
    it('should provide correct styles for light theme', async () => {
      render(
        <ThemeProvider defaultTheme="light">
          <TestThemeConsumer />
        </ThemeProvider>
      );
      
      expect(screen.getByTestId('is-dark')).toHaveTextContent('light');
      
      const bgElement = screen.getByTestId('bg-primary');
      expect(bgElement).toHaveClass('bg-white');
      
      const textElement = screen.getByTestId('text-primary');
      expect(textElement).toHaveClass('text-gray-900');
    });

    it('should provide correct styles for dark theme', async () => {
      render(
        <ThemeProvider defaultTheme="dark">
          <TestThemeConsumer />
        </ThemeProvider>
      );
      
      expect(screen.getByTestId('is-dark')).toHaveTextContent('dark');
      
      const bgElement = screen.getByTestId('bg-primary');
      expect(bgElement).toHaveClass('bg-gray-900');
      
      const textElement = screen.getByTestId('text-primary');
      expect(textElement).toHaveClass('text-gray-100');
    });

    it('should update styles when theme changes', async () => {
      render(
        <ThemeProvider defaultTheme="light">
          <TestThemeConsumer />
        </ThemeProvider>
      );
      
      // Start with light theme styles
      expect(screen.getByTestId('is-dark')).toHaveTextContent('light');
      
      // Switch to dark
      await act(async () => {
        fireEvent.click(screen.getByTestId('set-dark'));
      });
      
      // Should now have dark theme styles
      expect(screen.getByTestId('is-dark')).toHaveTextContent('dark');
    });

    it('should provide sensor-specific colors', () => {
      const { container } = render(
        <ThemeProvider defaultTheme="dark">
          <TestThemeConsumer />
        </ThemeProvider>
      );
      
      // This tests that the hook provides the expected structure
      // Actual sensor color testing would be in components that use these colors
      expect(container).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('should throw error when useTheme is used outside ThemeProvider', () => {
      // Mock console.error to avoid test noise
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      expect(() => {
        render(<TestThemeConsumer />);
      }).toThrow('useTheme must be used within a ThemeProvider');
      
      consoleSpy.mockRestore();
    });

    it('should handle SSR environment gracefully', () => {
      // This test verifies that the component can handle SSR scenarios
      // In this case, we'll test that it initializes properly without window
      const TestComponent = () => {
        const { theme } = useTheme();
        return <div data-testid="ssr-theme">{theme}</div>;
      };
      
      // The theme context should work in a DOM environment (happy-dom)
      render(
        <ThemeProvider>
          <TestComponent />
        </ThemeProvider>
      );
      
      expect(screen.getByTestId('ssr-theme')).toHaveTextContent('system');
    });
  });

  describe('Cleanup', () => {
    it('should clean up event listeners on unmount', async () => {
      const mockRemoveListener = vi.fn();
      const mockRemoveEventListener = vi.fn();
      
      mockMatchMedia.mockImplementation(() => ({
        matches: false,
        media: '(prefers-color-scheme: dark)',
        onchange: null,
        addListener: vi.fn(),
        removeListener: mockRemoveListener,
        addEventListener: vi.fn(),
        removeEventListener: mockRemoveEventListener,
        dispatchEvent: vi.fn(),
      }));
      
      const { unmount } = render(
        <ThemeProvider>
          <TestThemeConsumer />
        </ThemeProvider>
      );
      
      // Wait for any async operations to complete
      await act(async () => {
        unmount();
      });
      
      // Verify cleanup was called (the actual implementation uses both methods)
      expect(mockRemoveListener).toHaveBeenCalled();
      expect(mockRemoveEventListener).toHaveBeenCalledWith('change', expect.any(Function));
    });
  });
});