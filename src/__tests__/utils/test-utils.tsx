/**
 * Test Utilities for Sensor Dashboard Components
 * 
 * Provides comprehensive testing utilities including theme providers,
 * mocked hooks, and helper functions for sensor component testing.
 */

import React, { ReactElement } from 'react';
import { render, RenderOptions, screen } from '@testing-library/react';
import { vi } from 'vitest';
import { ThemeProvider, Theme } from '../../contexts/ThemeContext';

// Mock sensor data types
export interface MockSensor {
  id: string;
  name: string;
  location: string;
  isOnline: boolean;
  battery_level: number;
  last_reading_at: string;
}

export interface MockSensorStatus {
  sensor: MockSensor;
  latest_reading: {
    temperature: number;
    humidity: number;
    recorded_at: string;
  } | null;
  alert_status: 'normal' | 'warning' | 'critical';
  haccp_status: 'compliant' | 'violation' | 'warning';
}

export interface MockTemperatureAlert {
  id: string;
  sensor_id: string;
  alert_type: 'high_temp' | 'low_temp' | 'sensor_offline' | 'battery_low';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  created_at: string;
  resolved_at?: string;
  is_resolved: boolean;
}

// Mock data factories
export const createMockSensor = (overrides: Partial<MockSensor> = {}): MockSensor => ({
  id: `sensor-${Math.random().toString(36).substr(2, 9)}`,
  name: 'Freezer Unit A',
  location: 'Cold Storage',
  isOnline: true,
  battery_level: 85,
  last_reading_at: new Date().toISOString(),
  ...overrides,
});

export const createMockSensorStatus = (overrides: Partial<MockSensorStatus> = {}): MockSensorStatus => ({
  sensor: createMockSensor(),
  latest_reading: {
    temperature: -18.5,
    humidity: 65.2,
    recorded_at: new Date().toISOString(),
  },
  alert_status: 'normal',
  haccp_status: 'compliant',
  ...overrides,
});

export const createMockTemperatureAlert = (overrides: Partial<MockTemperatureAlert> = {}): MockTemperatureAlert => ({
  id: `alert-${Math.random().toString(36).substr(2, 9)}`,
  sensor_id: 'sensor-123',
  alert_type: 'high_temp',
  severity: 'medium',
  message: 'Temperature exceeded threshold',
  created_at: new Date().toISOString(),
  is_resolved: false,
  ...overrides,
});

// Large dataset generators for performance testing
export const createMockSensorDataset = (count: number): MockSensorStatus[] => {
  return Array.from({ length: count }, (_, index) => 
    createMockSensorStatus({
      sensor: createMockSensor({
        id: `sensor-${index.toString().padStart(3, '0')}`,
        name: `Sensor ${index + 1}`,
        location: `Location ${Math.floor(index / 10) + 1}`,
        isOnline: Math.random() > 0.1, // 90% online rate
        battery_level: Math.floor(Math.random() * 100),
      }),
      latest_reading: {
        temperature: -20 + Math.random() * 40, // -20°F to 20°F range
        humidity: 30 + Math.random() * 40, // 30% to 70% range
        recorded_at: new Date(Date.now() - Math.random() * 86400000).toISOString(),
      },
      alert_status: ['normal', 'warning', 'critical'][Math.floor(Math.random() * 3)] as 'normal' | 'warning' | 'critical',
      haccp_status: ['compliant', 'violation', 'warning'][Math.floor(Math.random() * 3)] as 'compliant' | 'violation' | 'warning',
    })
  );
};

// Theme testing wrapper
interface ThemeWrapperProps {
  children: React.ReactNode;
  theme?: Theme;
}

const ThemeWrapper: React.FC<ThemeWrapperProps> = ({ 
  children, 
  theme = 'light' 
}) => (
  <ThemeProvider defaultTheme={theme}>
    {children}
  </ThemeProvider>
);

// Enhanced render function with theme support
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  theme?: Theme;
  wrapper?: React.ComponentType<any>;
}

export const renderWithTheme = (
  ui: ReactElement,
  options: CustomRenderOptions = {}
) => {
  const { theme = 'light', wrapper, ...renderOptions } = options;
  
  const Wrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const ThemeWrapperComponent = wrapper ? 
      wrapper : 
      ({ children }: { children: React.ReactNode }) => (
        <ThemeWrapper theme={theme}>{children}</ThemeWrapper>
      );
    
    return <ThemeWrapperComponent>{children}</ThemeWrapperComponent>;
  };

  return {
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
    // Helper to re-render with different theme
    rerenderWithTheme: (newTheme: Theme) => {
      return render(ui, {
        wrapper: ({ children }) => (
          <ThemeWrapper theme={newTheme}>{children}</ThemeWrapper>
        ),
        ...renderOptions
      });
    }
  };
};

// Mock hook factories
export const createMockUseSensorStatuses = (sensorStatuses: MockSensorStatus[] = []) => {
  const summary = {
    totalSensors: sensorStatuses.length,
    onlineSensors: sensorStatuses.filter(s => s.sensor.isOnline).length,
    activeAlerts: sensorStatuses.filter(s => s.alert_status !== 'normal').length,
    criticalAlerts: sensorStatuses.filter(s => s.alert_status === 'critical').length,
    averageTemperature: sensorStatuses.length > 0 
      ? sensorStatuses.reduce((sum, s) => sum + (s.latest_reading?.temperature || 0), 0) / sensorStatuses.length 
      : null,
    temperatureRange: sensorStatuses.length > 0 ? {
      min: Math.min(...sensorStatuses.map(s => s.latest_reading?.temperature || 0)),
      max: Math.max(...sensorStatuses.map(s => s.latest_reading?.temperature || 0)),
    } : null,
  };

  return vi.fn().mockReturnValue({
    sensorStatuses,
    summary,
    loading: false,
    error: null,
    refetch: vi.fn(),
  });
};

export const createMockUseTemperatureAlerts = (alerts: MockTemperatureAlert[] = []) => {
  return vi.fn().mockReturnValue({
    alerts,
    unreadCount: alerts.filter(a => !a.is_resolved).length,
    loading: false,
    error: null,
    dismissAlert: vi.fn(),
    resolveAlert: vi.fn(),
    refetch: vi.fn(),
  });
};

export const createMockUseTemperatureSync = (overrides = {}) => {
  return vi.fn().mockReturnValue({
    sync: vi.fn(),
    syncing: false,
    lastSyncTime: new Date(),
    error: null,
    ...overrides,
  });
};

// CSS custom property testing
export const expectThemeStyles = (element: HTMLElement, theme: 'light' | 'dark') => {
  const computedStyles = window.getComputedStyle(element);
  
  if (theme === 'dark') {
    expect(element).toHaveClass('dark');
  } else {
    expect(element).toHaveClass('light');
  }
};

// Wait for async operations in tests
export const waitForNextTick = () => new Promise(resolve => setTimeout(resolve, 0));

// Performance testing helpers
export const measureRenderTime = async (renderFn: () => void): Promise<number> => {
  const start = performance.now();
  renderFn();
  await waitForNextTick();
  return performance.now() - start;
};

// Custom matchers for temperature values
export const expectTemperatureFormat = (element: HTMLElement, temperature: number) => {
  const expectedText = `${temperature.toFixed(1)}°F`;
  expect(element).toHaveTextContent(expectedText);
};

// Mock Supabase real-time subscription
export const createMockRealtimeSubscription = () => {
  const callbacks: Array<(payload: any) => void> = [];
  
  return {
    subscribe: vi.fn((callback: (payload: any) => void) => {
      callbacks.push(callback);
      return {
        unsubscribe: vi.fn(() => {
          const index = callbacks.indexOf(callback);
          if (index > -1) callbacks.splice(index, 1);
        }),
      };
    }),
    // Test helper to trigger callbacks
    triggerUpdate: (payload: any) => {
      callbacks.forEach(callback => callback(payload));
    },
    callbacks, // For inspection in tests
  };
};

// Error simulation utilities
export const simulateNetworkError = () => {
  return Promise.reject(new Error('Network request failed'));
};

export const simulateOfflineMode = () => {
  Object.defineProperty(navigator, 'onLine', {
    writable: true,
    value: false,
  });
  
  // Cleanup function
  return () => {
    Object.defineProperty(navigator, 'onLine', {
      writable: true,
      value: true,
    });
  };
};

// Accessibility testing helpers
export const expectProperAriaLabels = (element: HTMLElement) => {
  const interactiveElements = element.querySelectorAll('button, input, select, a[href]');
  
  interactiveElements.forEach(el => {
    const hasLabel = el.hasAttribute('aria-label') || 
                    el.hasAttribute('aria-labelledby') ||
                    (el.tagName === 'INPUT' && el.hasAttribute('id') && document.querySelector(`label[for="${el.id}"]`));
    
    if (!hasLabel) {
      console.warn('Interactive element missing aria-label:', el);
    }
  });
};

export const expectKeyboardNavigation = async (element: HTMLElement) => {
  const focusableElements = element.querySelectorAll(
    'button:not([disabled]), input:not([disabled]), select:not([disabled]), a[href], [tabindex]:not([tabindex="-1"])'
  );
  
  expect(focusableElements.length).toBeGreaterThan(0);
  
  // Check that elements are focusable
  for (const el of Array.from(focusableElements)) {
    (el as HTMLElement).focus();
    expect(document.activeElement).toBe(el);
  }
};