/**
 * Theme Context for Dark Mode Support
 * 
 * Provides theme state management and persistence for the Seafood Manager app.
 * Supports system preference detection and manual theme switching.
 */

import React, { createContext, useContext, useEffect, useState } from 'react';

export type Theme = 'light' | 'dark' | 'system';
export type ResolvedTheme = 'light' | 'dark';

interface ThemeContextType {
  theme: Theme;
  resolvedTheme: ResolvedTheme;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: Theme;
}

export function ThemeProvider({ children, defaultTheme = 'system' }: ThemeProviderProps) {
  const [theme, setThemeState] = useState<Theme>(defaultTheme);
  const [resolvedTheme, setResolvedTheme] = useState<ResolvedTheme>('light');

  // Get system theme preference
  const getSystemTheme = (): ResolvedTheme => {
    if (typeof window === 'undefined') return 'light';
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  };

  // Resolve theme based on current setting and system preference
  const resolveTheme = (currentTheme: Theme): ResolvedTheme => {
    if (currentTheme === 'system') {
      return getSystemTheme();
    }
    return currentTheme as ResolvedTheme;
  };

  // Update DOM classes and localStorage
  const updateTheme = (newTheme: Theme) => {
    const resolved = resolveTheme(newTheme);
    
    // Update DOM
    const root = document.documentElement;
    root.classList.remove('light', 'dark');
    root.classList.add(resolved);
    
    // Update CSS custom properties for charts and dynamic content
    if (resolved === 'dark') {
      root.style.setProperty('--chart-background', '#1f2937');
      root.style.setProperty('--chart-text', '#f3f4f6');
      root.style.setProperty('--chart-grid', '#374151');
      root.style.setProperty('--chart-border', '#4b5563');
    } else {
      root.style.setProperty('--chart-background', '#ffffff');
      root.style.setProperty('--chart-text', '#374151');
      root.style.setProperty('--chart-grid', '#e5e7eb');
      root.style.setProperty('--chart-border', '#d1d5db');
    }
    
    setResolvedTheme(resolved);
    
    // Persist to localStorage
    try {
      localStorage.setItem('seafood-manager-theme', newTheme);
    } catch (error) {
      console.warn('Failed to save theme preference:', error);
    }
  };

  // Set theme with persistence
  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
    updateTheme(newTheme);
  };

  // Toggle between light and dark (skips system)
  const toggleTheme = () => {
    if (theme === 'system') {
      const systemTheme = getSystemTheme();
      setTheme(systemTheme === 'dark' ? 'light' : 'dark');
    } else {
      setTheme(resolvedTheme === 'dark' ? 'light' : 'dark');
    }
  };

  // Initialize theme on mount
  useEffect(() => {
    // Load saved theme preference
    let savedTheme = defaultTheme;
    try {
      const saved = localStorage.getItem('seafood-manager-theme') as Theme;
      if (saved && ['light', 'dark', 'system'].includes(saved)) {
        savedTheme = saved;
      }
    } catch (error) {
      console.warn('Failed to load theme preference:', error);
    }
    
    setThemeState(savedTheme);
    updateTheme(savedTheme);

    // Listen for system theme changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleSystemThemeChange = () => {
      if (savedTheme === 'system') {
        updateTheme('system');
      }
    };

    mediaQuery.addListener?.(handleSystemThemeChange);
    mediaQuery.addEventListener?.('change', handleSystemThemeChange);

    return () => {
      mediaQuery.removeListener?.(handleSystemThemeChange);
      mediaQuery.removeEventListener?.('change', handleSystemThemeChange);
    };
  }, [defaultTheme]);

  // Update resolved theme when theme changes
  useEffect(() => {
    updateTheme(theme);
  }, [theme]);

  const value: ThemeContextType = {
    theme,
    resolvedTheme,
    setTheme,
    toggleTheme
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

/**
 * Hook for components that need theme-aware styling
 */
export function useThemeAwareStyles() {
  const { resolvedTheme } = useTheme();

  return {
    isDark: resolvedTheme === 'dark',
    
    // Background colors
    bg: {
      primary: resolvedTheme === 'dark' ? 'bg-gray-900' : 'bg-white',
      secondary: resolvedTheme === 'dark' ? 'bg-gray-800' : 'bg-gray-50',
      tertiary: resolvedTheme === 'dark' ? 'bg-gray-700' : 'bg-gray-100',
      card: resolvedTheme === 'dark' ? 'bg-gray-800' : 'bg-white',
      overlay: resolvedTheme === 'dark' ? 'bg-gray-900/95' : 'bg-white/95',
    },
    
    // Text colors
    text: {
      primary: resolvedTheme === 'dark' ? 'text-gray-100' : 'text-gray-900',
      secondary: resolvedTheme === 'dark' ? 'text-gray-300' : 'text-gray-600',
      tertiary: resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-500',
      muted: resolvedTheme === 'dark' ? 'text-gray-500' : 'text-gray-400',
    },
    
    // Border colors
    border: {
      primary: resolvedTheme === 'dark' ? 'border-gray-700' : 'border-gray-200',
      secondary: resolvedTheme === 'dark' ? 'border-gray-600' : 'border-gray-300',
      light: resolvedTheme === 'dark' ? 'border-gray-800' : 'border-gray-100',
    },
    
    // Sensor-specific colors (temperature monitoring)
    sensor: {
      online: resolvedTheme === 'dark' ? 'bg-green-900 text-green-200 border-green-800' : 'bg-green-100 text-green-800 border-green-200',
      warning: resolvedTheme === 'dark' ? 'bg-yellow-900 text-yellow-200 border-yellow-800' : 'bg-yellow-100 text-yellow-800 border-yellow-200',
      critical: resolvedTheme === 'dark' ? 'bg-red-900 text-red-200 border-red-800' : 'bg-red-100 text-red-800 border-red-200',
      offline: resolvedTheme === 'dark' ? 'bg-gray-800 text-gray-300 border-gray-700' : 'bg-gray-100 text-gray-800 border-gray-200',
    },
    
    // HACCP compliance colors
    haccp: {
      compliant: resolvedTheme === 'dark' ? 'bg-green-900 text-green-200' : 'bg-green-50 text-green-700',
      violation: resolvedTheme === 'dark' ? 'bg-red-900 text-red-200' : 'bg-red-50 text-red-700',
      warning: resolvedTheme === 'dark' ? 'bg-yellow-900 text-yellow-200' : 'bg-yellow-50 text-yellow-700',
    },
    
    // Chart colors optimized for both themes
    chart: {
      background: resolvedTheme === 'dark' ? '#1f2937' : '#ffffff',
      text: resolvedTheme === 'dark' ? '#f3f4f6' : '#374151',
      grid: resolvedTheme === 'dark' ? '#374151' : '#e5e7eb',
      border: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db',
      
      // Temperature colors that work in both themes
      colors: {
        temperature: resolvedTheme === 'dark' ? '#60a5fa' : '#3b82f6',
        humidity: resolvedTheme === 'dark' ? '#34d399' : '#10b981',
        alert: resolvedTheme === 'dark' ? '#f87171' : '#ef4444',
        warning: resolvedTheme === 'dark' ? '#fbbf24' : '#f59e0b',
      }
    }
  };
}