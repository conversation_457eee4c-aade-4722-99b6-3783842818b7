import { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export type ActiveView = 
  | 'Dashboard' 
  | 'Inventory' 
  | 'Vendors'
  | 'Customers'
  | 'Analytics' 
  | 'Messages' 
  | 'Settings'
  | 'Import'
  | 'Voice Input'
  | 'Voice Test'
  | 'Voice Management'
  | 'Events'               // dedicated events table view
  | 'HACCP Events'        // legacy entry point to HACCP Events (maps to Events)
  | 'HACCP: Batches'
  | 'HACCP: Events'
  | 'HACCP: Calendar'
  | 'Temperature Monitoring'  // TempStick dashboard
  | 'Sensor Management'       // Sensor configuration and management
  | 'Temperature: Dashboard'  // Alternative naming for submenu
  | 'Temperature: Sensors'    // Alternative naming for submenu
  | 'Temperature: Historical' // Historical temperature data view
  | 'Temperature: Sync'       // Real-time sync interface
  | 'Temperature: Calibration' // Calibration management
  | 'Real-Time Sync'          // Alternative naming for temperature sync
  | 'TempStick Test';         // TempStick API testing interface

interface NavigationContextType {
  activeView: ActiveView;
  setActiveView: (view: ActiveView) => void;
  viewFilters: Record<string, unknown>;
  setViewFilter: (key: string, value: unknown) => void;
  clearViewFilters: () => void;
}

const NavigationContext = createContext<NavigationContextType | undefined>(undefined);

// Helper function to get view from URL
function getViewFromPath(pathname: string): ActiveView {
  if (pathname.startsWith('/temperature/dashboard')) return 'Temperature: Dashboard';
  if (pathname.startsWith('/temperature/sensors')) return 'Temperature: Sensors';
  if (pathname.startsWith('/temperature')) return 'Temperature: Dashboard';
  if (pathname.startsWith('/inventory')) return 'Inventory';
  if (pathname.startsWith('/vendors')) return 'Vendors';
  if (pathname.startsWith('/customers')) return 'Customers';
  if (pathname.startsWith('/analytics')) return 'Analytics';
  if (pathname.startsWith('/messages')) return 'Messages';
  if (pathname.startsWith('/settings')) return 'Settings';
  if (pathname.startsWith('/import')) return 'Import';
  if (pathname.startsWith('/events')) return 'Events';
  if (pathname.startsWith('/haccp/batches')) return 'HACCP: Batches';
  if (pathname.startsWith('/haccp/events')) return 'HACCP: Events';
  if (pathname.startsWith('/haccp/calendar')) return 'HACCP: Calendar';
  if (pathname.startsWith('/voice')) return 'Voice Management';
  return 'Dashboard';
}

export function NavigationProvider({ children }: { children: ReactNode }) {
  const [activeView, setActiveView] = useState<ActiveView>(() => 
    getViewFromPath(window.location.pathname)
  );
  const [viewFilters, setViewFilters] = useState<Record<string, unknown>>({});

  // Listen for URL changes and update activeView accordingly
  useEffect(() => {
    const handlePopState = () => {
      setActiveView(getViewFromPath(window.location.pathname));
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, []);

  const setViewFilter = (key: string, value: unknown) => {
    setViewFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearViewFilters = () => {
    setViewFilters({});
  };

  return (
    <NavigationContext.Provider value={{ 
      activeView, 
      setActiveView, 
      viewFilters, 
      setViewFilter, 
      clearViewFilters 
    }}>
      {children}
    </NavigationContext.Provider>
  );
}

// Create a separate file for this hook: src/hooks/useNavigation.ts
export function useNavigationContext() {
  const context = useContext(NavigationContext);
  if (context === undefined) {
    throw new Error('useNavigationContext must be used within a NavigationProvider');
  }
  return context;
} 