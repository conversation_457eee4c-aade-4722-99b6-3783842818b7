/**
 * High-Performance CSV Processing Web Worker
 * Handles streaming processing of large CSV files with memory optimization
 * Optimized for seafood industry datasets up to 500MB
 */

import Papa from 'papaparse';

export interface ProcessingConfig {
  chunkSize: number;
  hasHeader: boolean;
  delimiter: string;
  encoding: string;
  skipEmptyLines: boolean;
}

export interface ProcessingProgress {
  stage: 'parsing' | 'validating' | 'transforming' | 'complete';
  rowsProcessed: number;
  totalRows: number;
  currentChunk: number;
  totalChunks: number;
  bytesProcessed: number;
  totalBytes: number;
  percentage: number;
  estimatedTimeRemaining: number;
  currentMemoryUsage: number;
  errorsFound: number;
  warningsFound: number;
}

export interface ProcessingResult {
  success: boolean;
  totalRows: number;
  processedRows: number;
  data: Record<string, unknown>[];
  headers: string[];
  errors: ProcessingError[];
  warnings: ProcessingWarning[];
  metadata: {
    originalFileName: string;
    fileSize: number;
    processingStartTime: number;
    processingEndTime: number;
    chunksProcessed: number;
    memoryPeak: number;
    averageChunkTime: number;
  };
  memoryUsage: number;
  performanceMetrics?: {
    totalProcessingTime: number;
    rowsPerSecond: number;
    bytesPerSecond: number;
    memoryEfficiency: string;
    errorRate: number;
    recommendedForSimilarFiles: number;
  };
}

export interface ProcessingError {
  row: number;
  column?: string;
  message: string;
  severity: 'critical' | 'error' | 'warning';
  suggestedFix?: string;
  dataValue?: unknown;
}

export interface ProcessingWarning {
  row: number;
  column?: string;
  message: string;
  dataValue?: unknown;
}

export interface WorkerMessage {
  type: 'start' | 'progress' | 'complete' | 'error' | 'cancel' | 'ready';
  data?: unknown;
}

class CSVProcessor {
  private shouldCancel = false;
  private startTime = 0;
  private totalBytes = 0;
  private processedBytes = 0;
  private memoryPeak = 0;
  private chunkTimes: number[] = [];

  async processFile(file: File, config: ProcessingConfig): Promise<ProcessingResult> {
    this.shouldCancel = false;
    this.startTime = Date.now();
    this.totalBytes = file.size;
    this.processedBytes = 0;
    this.memoryPeak = 0;
    this.chunkTimes = [];

    const result: ProcessingResult = {
      success: false,
      totalRows: 0,
      processedRows: 0,
      data: [],
      headers: [],
      errors: [],
      warnings: [],
      metadata: {
        originalFileName: file.name,
        fileSize: file.size,
        processingStartTime: this.startTime,
        processingEndTime: 0,
        chunksProcessed: 0,
        memoryPeak: 0,
        averageChunkTime: 0
      },
      memoryUsage: 0
    };

    try {
      // Check if we should use streaming for large files
      if (file.size > 10 * 1024 * 1024) { // 10MB threshold
        return await this.processLargeFile(file, config, result);
      } else {
        return await this.processSmallFile(file, config, result);
      }
    } catch (error) {
      result.success = false;
      result.errors.push({
        row: 0,
        message: error instanceof Error ? error.message : 'Unknown processing error',
        severity: 'critical'
      });
      return result;
    } finally {
      this.shouldCancel = false;
    }
  }

  private async processLargeFile(file: File, config: ProcessingConfig, result: ProcessingResult): Promise<ProcessingResult> {
    return new Promise((resolve) => {
      let headers: string[] = [];
      let rowCount = 0;
      let chunkCount = 0;
      const allData: Record<string, unknown>[] = [];
      
      // Send initial progress
      this.sendProgress({
        stage: 'parsing',
        rowsProcessed: 0,
        totalRows: 0,
        currentChunk: 0,
        totalChunks: 0,
        bytesProcessed: 0,
        totalBytes: this.totalBytes,
        percentage: 0,
        estimatedTimeRemaining: 0,
        currentMemoryUsage: this.getCurrentMemoryUsage(),
        errorsFound: 0,
        warningsFound: 0
      });

      Papa.parse(file, {
        header: config.hasHeader,
        delimiter: config.delimiter,
        skipEmptyLines: config.skipEmptyLines,
        encoding: config.encoding,
        chunk: (chunk, parser) => {
          if (this.shouldCancel) {
            parser.abort();
            return;
          }

          const chunkStartTime = Date.now();
          chunkCount++;

          // Store headers from first chunk if using header mode
          if (config.hasHeader && headers.length === 0) {
            headers = Object.keys(chunk.data[0] || {});
            result.headers = headers;
          }

          // Process chunk data
          const chunkData = this.processChunk(chunk.data as Record<string, unknown>[], headers, rowCount, result);
          allData.push(...chunkData);
          
          rowCount += chunk.data.length;
          this.processedBytes += this.estimateChunkSize(chunk.data as Record<string, unknown>[]);
          
          // Track performance
          const chunkTime = Date.now() - chunkStartTime;
          this.chunkTimes.push(chunkTime);
          this.updateMemoryPeak();

          // Send progress update
          const percentage = (this.processedBytes / this.totalBytes) * 100;
          const estimatedTimeRemaining = ((this.totalBytes - this.processedBytes) / this.processedBytes) * (Date.now() - this.startTime);

          this.sendProgress({
            stage: 'parsing',
            rowsProcessed: rowCount,
            totalRows: 0, // Unknown until complete
            currentChunk: chunkCount,
            totalChunks: 0, // Unknown until complete
            bytesProcessed: this.processedBytes,
            totalBytes: this.totalBytes,
            percentage,
            estimatedTimeRemaining,
            currentMemoryUsage: this.getCurrentMemoryUsage(),
            errorsFound: result.errors.length,
            warningsFound: result.warnings.length
          });

          // Throttle processing to prevent memory issues
          if (chunkCount % 100 === 0) {
            this.performGarbageCollection();
          }
        },
        complete: () => {
          result.success = true;
          result.totalRows = rowCount;
          result.processedRows = rowCount;
          result.data = allData;
          result.metadata.processingEndTime = Date.now();
          result.metadata.chunksProcessed = chunkCount;
          result.metadata.memoryPeak = this.memoryPeak;
          result.metadata.averageChunkTime = this.chunkTimes.reduce((a, b) => a + b, 0) / this.chunkTimes.length;
          result.memoryUsage = this.getCurrentMemoryUsage();

          this.sendProgress({
            stage: 'complete',
            rowsProcessed: rowCount,
            totalRows: rowCount,
            currentChunk: chunkCount,
            totalChunks: chunkCount,
            bytesProcessed: this.totalBytes,
            totalBytes: this.totalBytes,
            percentage: 100,
            estimatedTimeRemaining: 0,
            currentMemoryUsage: this.getCurrentMemoryUsage(),
            errorsFound: result.errors.length,
            warningsFound: result.warnings.length
          });

          resolve(result);
        },
        error: (error) => {
          result.errors.push({
            row: rowCount,
            message: error.message,
            severity: 'critical'
          });
          resolve(result);
        }
      });
    });
  }

  private async processSmallFile(file: File, config: ProcessingConfig, result: ProcessingResult): Promise<ProcessingResult> {
    return new Promise((resolve) => {
      const fileText = file.text();
      
      fileText.then((text) => {
        const parseResult = Papa.parse<Record<string, unknown>>(text, {
          header: config.hasHeader,
          delimiter: config.delimiter,
          skipEmptyLines: config.skipEmptyLines,
          dynamicTyping: false
        });

        if (parseResult.errors.length > 0) {
          result.errors.push(...parseResult.errors.map(error => ({
            row: error.row ?? 0,
            message: error.message,
            severity: 'error' as const,
            column: error.code === 'TooManyFields' ? 'multiple' : undefined
          })));
        }

        result.success = parseResult.errors.length === 0;
        result.data = parseResult.data || [];
        result.totalRows = result.data.length;
        result.processedRows = result.data.length;
        result.headers = config.hasHeader ? Object.keys(result.data[0] || {}) : [];
        result.metadata.processingEndTime = Date.now();
        result.metadata.chunksProcessed = 1;
        result.metadata.memoryPeak = this.getCurrentMemoryUsage();
        result.metadata.averageChunkTime = result.metadata.processingEndTime - this.startTime;
        result.memoryUsage = this.getCurrentMemoryUsage();

        // Send final progress
        this.sendProgress({
          stage: 'complete',
          rowsProcessed: result.totalRows,
          totalRows: result.totalRows,
          currentChunk: 1,
          totalChunks: 1,
          bytesProcessed: this.totalBytes,
          totalBytes: this.totalBytes,
          percentage: 100,
          estimatedTimeRemaining: 0,
          currentMemoryUsage: this.getCurrentMemoryUsage(),
          errorsFound: result.errors.length,
          warningsFound: result.warnings.length
        });

        resolve(result);
      }).catch((error) => {
        result.errors.push({
          row: 0,
          message: error.message,
          severity: 'critical'
        });
        resolve(result);
      });
    });
  }

  private processChunk(
    chunkData: Record<string, unknown>[],
    headers: string[],
    startRowIndex: number,
    result: ProcessingResult
  ): Record<string, unknown>[] {
    const processedData: Record<string, unknown>[] = [];

    for (let i = 0; i < chunkData.length; i++) {
      const row = chunkData[i];
      const rowIndex = startRowIndex + i;

      try {
        // Basic data validation and cleaning
        const cleanRow = this.cleanRowData(row, headers, rowIndex, result);
        processedData.push(cleanRow);
      } catch (error) {
        result.errors.push({
          row: rowIndex,
          message: error instanceof Error ? error.message : 'Row processing error',
          severity: 'error',
          dataValue: row
        });
      }
    }

    return processedData;
  }

  private cleanRowData(
    row: Record<string, unknown>,
    headers: string[],
    rowIndex: number,
    result: ProcessingResult
  ): Record<string, unknown> {
    const cleanRow: Record<string, unknown> = {};

    for (const header of headers) {
      const value = row[header];
      
      // Handle null, undefined, and empty string values
      if (value === null || value === undefined || value === '') {
        cleanRow[header] = null;
        continue;
      }

      // Convert to string for processing
      const stringValue = String(value).trim();
      
      // Basic data type inference and conversion
      if (stringValue === '') {
        cleanRow[header] = null;
      } else if (this.isNumeric(stringValue)) {
        cleanRow[header] = this.parseNumber(stringValue);
      } else if (this.isDate(stringValue)) {
        cleanRow[header] = this.parseDate(stringValue, rowIndex, header, result);
      } else if (this.isBoolean(stringValue)) {
        cleanRow[header] = this.parseBoolean(stringValue);
      } else {
        cleanRow[header] = stringValue;
      }
    }

    return cleanRow;
  }

  private isNumeric(value: string): boolean {
    // Check for numbers, including currency and percentages
    return /^-?\$?\d{1,3}(,\d{3})*\.?\d*%?$/.test(value.replace(/\s/g, ''));
  }

  private parseNumber(value: string): number {
    // Remove currency symbols, commas, and percentages
    const cleaned = value.replace(/[$,\s%]/g, '');
    const num = parseFloat(cleaned);
    
    // Handle percentages
    if (value.includes('%')) {
      return num / 100;
    }
    
    return num;
  }

  private isDate(value: string): boolean {
    // Basic date pattern detection
    return /^\d{1,2}[/-]\d{1,2}[/-]\d{2,4}$/.test(value) ||
           /^\d{4}-\d{2}-\d{2}$/.test(value) ||
           /^\d{2}\/\d{2}\/\d{4}$/.test(value);
  }

  private parseDate(value: string, rowIndex: number, column: string, result: ProcessingResult): string {
    try {
      const date = new Date(value);
      if (isNaN(date.getTime())) {
        result.warnings.push({
          row: rowIndex,
          column,
          message: `Invalid date format: ${value}`,
          dataValue: value
        });
        return value; // Return original value if can't parse
      }
      return date.toISOString().split('T')[0]; // Return YYYY-MM-DD format
    } catch {
      result.warnings.push({
        row: rowIndex,
        column,
        message: `Date parsing failed: ${value}`,
        dataValue: value
      });
      return value;
    }
  }

  private isBoolean(value: string): boolean {
    const lowercased = value.toLowerCase();
    return ['true', 'false', 'yes', 'no', '1', '0', 'y', 'n'].includes(lowercased);
  }

  private parseBoolean(value: string): boolean {
    const lowercased = value.toLowerCase();
    return ['true', 'yes', '1', 'y'].includes(lowercased);
  }

  private estimateChunkSize(data: Record<string, unknown>[]): number {
    // Rough estimation of chunk size in bytes
    return JSON.stringify(data).length * 2; // Factor for UTF-16 encoding
  }

  private getCurrentMemoryUsage(): number {
    // Approximate memory usage (not available in all browsers)
    const performanceWithMemory = performance as Performance & {
      memory?: { usedJSHeapSize: number; totalJSHeapSize: number; jsHeapSizeLimit: number };
    };
    if ('memory' in performance && performanceWithMemory.memory) {
      return performanceWithMemory.memory.usedJSHeapSize;
    }
    return 0;
  }

  private updateMemoryPeak(): void {
    const current = this.getCurrentMemoryUsage();
    if (current > this.memoryPeak) {
      this.memoryPeak = current;
    }
  }

  private performGarbageCollection(): void {
    // Force garbage collection if available (Chrome)
    const windowWithGc = window as Window & { gc?: () => void };
    if ('gc' in window && typeof windowWithGc.gc === 'function') {
      try {
        windowWithGc.gc();
      } catch {
        // Ignore if not available
      }
    }
  }

  private sendProgress(progress: ProcessingProgress): void {
    self.postMessage({
      type: 'progress',
      data: progress
    } as WorkerMessage);
  }

  cancel(): void {
    this.shouldCancel = true;
  }
}

// Worker instance
const processor = new CSVProcessor();

// Handle messages from main thread
self.addEventListener('message', async (event: MessageEvent<WorkerMessage>) => {
  const { type, data } = event.data;

  try {
    switch (type) {
      case 'start':
        if (data && typeof data === 'object' && 'file' in data && 'config' in data) {
          const { file, config } = data as { file: File; config: ProcessingConfig };
          const result = await processor.processFile(file, config);
          
          self.postMessage({
            type: 'complete',
            data: result
          } as WorkerMessage);
        } else {
          // Test message for worker initialization
          self.postMessage({
            type: 'ready',
            data: null
          } as WorkerMessage);
        }
        break;

      case 'cancel':
        processor.cancel();
        self.postMessage({
          type: 'cancel',
          data: null
        } as WorkerMessage);
        break;

      default:
        console.warn('Unknown message type:', type);
    }
  } catch (error) {
    self.postMessage({
      type: 'error',
      data: {
        message: error instanceof Error ? error.message : 'Unknown worker error'
      }
    } as WorkerMessage);
  }
});

// Types are exported above with interface declarations