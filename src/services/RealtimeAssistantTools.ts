import { supabase } from '../lib/supabase';
import { VoiceEventData } from '../types/schema';
import { voiceEventService } from '../modules/voice-event-storage';

/**
 * Tool calling functions for OpenAI Realtime Assistant
 * Provides database connectivity for Supabase and optional Zep integration
 */

// Zep client placeholder - install with: npm install @getzep/zep-js to enable memory features
// For now, memory functionality is disabled
const zepClient: unknown = null;

// Interface definitions for tool parameters
interface CreateInventoryEventParams {
  event_type: 'receiving' | 'disposal' | 'physical_count' | 'sale';
  quantity: number;
  unit?: 'lbs' | 'kg' | 'cases' | 'units';
  notes?: string;
  occurred_at?: string;
  voice_confidence_score?: number;
  voice_confidence_breakdown?: {
    product_match: number;
    quantity_extraction: number;
    vendor_match: number;
    overall: number;
  };
  raw_transcript?: string;
  product_name?: string;
  vendor_name?: string;
  customer_name?: string;
  condition?: 'Excellent' | 'Good' | 'Fair' | 'Poor' | 'Damaged';
  temperature?: number;
  temperature_unit?: 'fahrenheit' | 'celsius';
  processing_method?: string;
  quality_grade?: string;
  market_form?: string;
}

interface ToolExecutionResult {
  success: boolean;
  data?: unknown;
  error?: string;
  message?: string;
  event_id?: string;
}

console.warn('Zep client disabled - install @getzep/zep-js to enable memory features');

// Tool function definitions for OpenAI function calling
export const REALTIME_ASSISTANT_TOOLS = [
  {
    type: "function",
    function: {
      name: "create_inventory_event",
      description: "Create a new inventory event (receiving, sale, disposal, physical_count) in the Supabase database",
      parameters: {
        type: "object",
        properties: {
          event_type: {
            type: "string",
            enum: ["receiving", "sale", "disposal", "physical_count"],
            description: "Type of inventory event"
          },
          product_name: {
            type: "string",
            description: "Name of the seafood product (species and form)"
          },
          quantity: {
            type: "number",
            description: "Quantity of product"
          },
          unit: {
            type: "string",
            enum: ["lbs", "kg", "cases", "units"],
            description: "Unit of measurement"
          },
          vendor_name: {
            type: "string",
            description: "Name of vendor/supplier (for receiving events)"
          },
          customer_name: {
            type: "string", 
            description: "Name of customer (for sale events)"
          },
          condition: {
            type: "string",
            enum: ["Excellent", "Good", "Fair", "Poor", "Damaged"],
            description: "Product condition"
          },
          temperature: {
            type: "number",
            description: "Temperature reading if mentioned"
          },
          temperature_unit: {
            type: "string",
            enum: ["fahrenheit", "celsius"],
            description: "Temperature unit"
          },
          processing_method: {
            type: "string",
            description: "Processing method (Fresh, Frozen, IQF, etc.)"
          },
          quality_grade: {
            type: "string", 
            description: "Quality grade (Premium, Grade A, Sashimi Grade, etc.)"
          },
          market_form: {
            type: "string",
            description: "Market form (Whole, Fillets, H&G, etc.)"
          },
          notes: {
            type: "string",
            description: "Additional notes or context"
          },
          occurred_at: {
            type: "string",
            description: "When the event occurred (ISO date string)"
          },
          voice_confidence_score: {
            type: "number",
            description: "Confidence score from voice processing (0.0-1.0)"
          },
          raw_transcript: {
            type: "string",
            description: "Original voice transcript"
          }
        },
        required: ["event_type", "product_name", "quantity", "unit", "voice_confidence_score", "raw_transcript"]
      }
    }
  },
  {
    type: "function", 
    function: {
      name: "search_products",
      description: "Search for existing products in the Supabase database",
      parameters: {
        type: "object",
        properties: {
          query: {
            type: "string",
            description: "Search query (product name, species, etc.)"
          },
          limit: {
            type: "number",
            description: "Maximum number of results to return",
            default: 10
          }
        },
        required: ["query"]
      }
    }
  },
  {
    type: "function",
    function: {
      name: "get_inventory_status", 
      description: "Get current inventory levels for a specific product",
      parameters: {
        type: "object",
        properties: {
          product_name: {
            type: "string",
            description: "Name of the product to check inventory for"
          }
        },
        required: ["product_name"]
      }
    }
  },
  {
    type: "function",
    function: {
      name: "search_vendors",
      description: "Search for vendors/suppliers in the Supabase database",
      parameters: {
        type: "object", 
        properties: {
          query: {
            type: "string",
            description: "Vendor name or partial name to search for"
          },
          limit: {
            type: "number", 
            description: "Maximum number of results",
            default: 5
          }
        },
        required: ["query"]
      }
    }
  },
  {
    type: "function",
    function: {
      name: "search_customers",
      description: "Search for customers in the Supabase database", 
      parameters: {
        type: "object",
        properties: {
          query: {
            type: "string",
            description: "Customer name or partial name to search for"
          },
          limit: {
            type: "number",
            description: "Maximum number of results", 
            default: 5
          }
        },
        required: ["query"]
      }
    }
  },
  // NOTE: Memory tools disabled - install @getzep/zep-js to enable
  // {
  //   type: "function",
  //   function: {
  //     name: "store_conversation_memory",
  //     description: "Store conversation context and user preferences in Zep memory",
  //     ...
  //   }
  // },
  {
    type: "function",
    function: {
      name: "get_recent_events",
      description: "Get recent inventory events from Supabase database",
      parameters: {
        type: "object",
        properties: {
          event_type: {
            type: "string",
            enum: ["receiving", "sale", "disposal", "physical_count", "all"],
            description: "Filter by event type or 'all' for all types"
          },
          limit: {
            type: "number",
            description: "Number of recent events to retrieve",
            default: 10
          },
          hours: {
            type: "number", 
            description: "Number of hours back to search",
            default: 24
          }
        }
      }
    }
  }
];

/**
 * Implementation functions for tool calling
 */
export class RealtimeAssistantToolHandler {
  
  /**
   * Create a new inventory event
   */
  static async createInventoryEvent(params: CreateInventoryEventParams): Promise<ToolExecutionResult> {
    try {
      // Validate required parameters
      if (!params.product_name || !params.quantity || !params.voice_confidence_score || !params.raw_transcript) {
        return {
          success: false,
          error: 'Missing required parameters: product_name, quantity, voice_confidence_score, and raw_transcript are required',
          message: 'Failed to create inventory event due to missing parameters'
        };
      }

      // Create voice event data from parameters
      const voiceEventData: VoiceEventData = {
        event_type: params.event_type,
        product_name: params.product_name,
        quantity: params.quantity,
        unit: params.unit || 'lbs',
        vendor_name: params.vendor_name,
        customer_name: params.customer_name,
        condition: params.condition,
        temperature: params.temperature,
        temperature_unit: params.temperature_unit,
        processing_method: params.processing_method,
        quality_grade: params.quality_grade,
        market_form: params.market_form,
        notes: params.notes || '',
        occurred_at: params.occurred_at || new Date().toISOString(),
        
        // Voice-specific fields
        voice_confidence_score: params.voice_confidence_score,
        voice_confidence_breakdown: params.voice_confidence_breakdown || {
          product_match: params.voice_confidence_score,
          quantity_extraction: params.voice_confidence_score,
          vendor_match: params.vendor_name ? params.voice_confidence_score : 0,
          overall: params.voice_confidence_score
        },
        raw_transcript: params.raw_transcript
      };

      // Use the voice event service to create the event
      const createdEvent = await voiceEventService.createVoiceEvent(voiceEventData);

      return {
        success: true,
        event_id: createdEvent.id,
        message: `Successfully recorded ${params.event_type} event for ${params.quantity} ${params.unit} of ${params.product_name}`,
        data: createdEvent
      };

    } catch (error) {
      console.error('Error creating inventory event:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        message: 'Failed to create inventory event'
      };
    }
  }

  /**
   * Search for products
   */
  static async searchProducts(params: { query: string; limit?: number }): Promise<any> {
    try {
      const { data, error } = await supabase
        .from('Products')
        .select('id, name, category, sub_category, current_stock, unit, condition, supplier')
        .or(`name.ilike.%${params.query}%,category.ilike.%${params.query}%,sub_category.ilike.%${params.query}%`)
        .limit(params.limit || 10);

      if (error) {
        throw new Error(`Search error: ${error.message}`);
      }

      return {
        success: true,
        products: data || [],
        count: data?.length || 0,
        message: `Found ${data?.length || 0} products matching "${params.query}"`
      };

    } catch (error) {
      console.error('Error searching products:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Search failed',
        products: [],
        count: 0
      };
    }
  }

  /**
   * Get inventory status for a product
   */
  static async getInventoryStatus(params: { product_name: string }): Promise<any> {
    try {
      // Search for the product first
      const { data: products, error: productError } = await supabase
        .from('Products')
        .select('id, name, current_stock, unit, min_stock')
        .ilike('name', `%${params.product_name}%`)
        .limit(5);

      if (productError) {
        throw new Error(`Product search error: ${productError.message}`);
      }

      if (!products || products.length === 0) {
        return {
          success: false,
          message: `No products found matching "${params.product_name}"`,
          inventory: []
        };
      }

      // Get recent events for these products
      const productIds = products.map(p => p.id);
      const { data: events, error: eventsError } = await supabase
        .from('inventory_events')
        .select('product_id, event_type, quantity, created_at')
        .in('product_id', productIds)
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()) // Last 30 days
        .order('created_at', { ascending: false });

      if (eventsError) {
        console.warn('Error fetching recent events:', eventsError);
      }

      return {
        success: true,
        inventory: products.map(product => ({
          ...product,
          recent_activity: events?.filter(e => e.product_id === product.id).slice(0, 5) || []
        })),
        message: `Found inventory status for ${products.length} products matching "${params.product_name}"`
      };

    } catch (error) {
      console.error('Error getting inventory status:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Inventory check failed',
        inventory: []
      };
    }
  }

  /**
   * Search for vendors/suppliers
   */
  static async searchVendors(params: { query: string; limit?: number }): Promise<any> {
    try {
      const { data, error } = await supabase
        .from('Suppliers')
        .select('id, name, contact_name, email, phone, status')
        .ilike('name', `%${params.query}%`)
        .eq('status', 'active')
        .limit(params.limit || 5);

      if (error) {
        throw new Error(`Vendor search error: ${error.message}`);
      }

      return {
        success: true,
        vendors: data || [],
        count: data?.length || 0,
        message: `Found ${data?.length || 0} vendors matching "${params.query}"`
      };

    } catch (error) {
      console.error('Error searching vendors:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Vendor search failed',
        vendors: [],
        count: 0
      };
    }
  }

  /**
   * Search for customers
   */
  static async searchCustomers(params: { query: string; limit?: number }): Promise<any> {
    try {
      const { data, error } = await supabase
        .from('Customers')
        .select('id, name, contact_name, email, phone, channel_type, status')
        .ilike('name', `%${params.query}%`)
        .eq('status', 'active')
        .limit(params.limit || 5);

      if (error) {
        throw new Error(`Customer search error: ${error.message}`);
      }

      return {
        success: true,
        customers: data || [],
        count: data?.length || 0,
        message: `Found ${data?.length || 0} customers matching "${params.query}"`
      };

    } catch (error) {
      console.error('Error searching customers:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Customer search failed',
        customers: [],
        count: 0
      };
    }
  }

  /**
   * Store conversation memory in Zep
   */
  static async storeConversationMemory(params: {
    user_id: string;
    session_id: string;
    message: string;
    metadata?: Record<string, unknown>;
  }): Promise<ToolExecutionResult> {
    try {
      if (!zepClient) {
        return {
          success: false,
          error: 'Zep client not available',
          message: 'Memory storage is disabled - install @getzep/zep-js to enable'
        };
      }

      await (zepClient as any).memory.addMemory(params.session_id, {
        messages: [{
          role: 'user',
          content: params.message,
          metadata: {
            user_id: params.user_id,
            timestamp: new Date().toISOString(),
            ...params.metadata
          }
        }]
      });

      return {
        success: true,
        message: 'Conversation memory stored successfully'
      };

    } catch (error) {
      console.error('Error storing conversation memory:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Memory storage failed'
      };
    }
  }

  /**
   * Retrieve conversation memory from Zep
   */
  static async retrieveConversationMemory(params: {
    user_id: string;
    session_id: string;
    query?: string;
    limit?: number;
  }): Promise<any> {
    try {
      if (!zepClient) {
        return {
          success: false,
          error: 'Zep client not available',
          message: 'Memory retrieval is disabled - install @getzep/zep-js to enable',
          memories: [],
          count: 0
        };
      }

      let memories;
      if (params.query) {
        // Search memories
        memories = await (zepClient as any).memory.searchMemory(params.session_id, {
          text: params.query,
          limit: params.limit || 10
        });
      } else {
        // Get recent memory
        const memoryResult = await (zepClient as any).memory.getMemory(params.session_id);
        memories = {
          results: memoryResult.messages?.slice(-(params.limit || 10)) || []
        };
      }

      return {
        success: true,
        memories: memories.results || [],
        count: memories.results?.length || 0,
        message: `Retrieved ${memories.results?.length || 0} relevant memories`
      };

    } catch (error) {
      console.error('Error retrieving conversation memory:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Memory retrieval failed',
        memories: [],
        count: 0
      };
    }
  }

  /**
   * Get recent inventory events
   */
  static async getRecentEvents(params: {
    event_type?: string;
    limit?: number;
    hours?: number;
  }): Promise<any> {
    try {
      const hoursBack = params.hours || 24;
      const since = new Date(Date.now() - hoursBack * 60 * 60 * 1000).toISOString();

      let query = supabase
        .from('inventory_events')
        .select(`
          id,
          event_type,
          quantity,
          unit,
          created_at,
          occurred_at,
          notes,
          created_by_voice,
          voice_confidence_score,
          metadata
        `)
        .gte('created_at', since)
        .order('created_at', { ascending: false })
        .limit(params.limit || 10);

      if (params.event_type && params.event_type !== 'all') {
        query = query.eq('event_type', params.event_type);
      }

      const { data, error } = await query;

      if (error) {
        throw new Error(`Events query error: ${error.message}`);
      }

      return {
        success: true,
        events: data || [],
        count: data?.length || 0,
        message: `Found ${data?.length || 0} recent events in the last ${hoursBack} hours`
      };

    } catch (error) {
      console.error('Error getting recent events:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Events retrieval failed',
        events: [],
        count: 0
      };
    }
  }
}

/**
 * Tool dispatcher for OpenAI function calling
 */
export async function executeRealtimeTool(toolName: string, params: Record<string, unknown>): Promise<ToolExecutionResult> {
  console.log(`Executing tool: ${toolName}`, params);

  switch (toolName) {
    case 'create_inventory_event':
      return await RealtimeAssistantToolHandler.createInventoryEvent(params as any);
    
    case 'search_products':
      return await RealtimeAssistantToolHandler.searchProducts(params as { query: string; limit?: number });
    
    case 'get_inventory_status':
      return await RealtimeAssistantToolHandler.getInventoryStatus(params as { product_name: string });
    
    case 'search_vendors':
      return await RealtimeAssistantToolHandler.searchVendors(params as { query: string; limit?: number });
    
    case 'search_customers':
      return await RealtimeAssistantToolHandler.searchCustomers(params as { query: string; limit?: number });
    
    case 'store_conversation_memory':
      return {
        success: false,
        error: 'Memory functionality disabled',
        message: 'Install @getzep/zep-js to enable memory features'
      };
    
    case 'retrieve_conversation_memory':
      return {
        success: false,
        error: 'Memory functionality disabled',
        message: 'Install @getzep/zep-js to enable memory features'
      };
    
    case 'get_recent_events':
      return await RealtimeAssistantToolHandler.getRecentEvents(params);
    
    default:
      return {
        success: false,
        error: `Unknown tool: ${toolName}`,
        message: 'Tool not found'
      };
  }
}