import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { VoiceErrorHandler, VoiceErrorType } from '../VoiceErrorHandler';

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

// Mock navigator.onLine
Object.defineProperty(navigator, 'onLine', {
  writable: true,
  value: true
});

// Mock crypto.randomUUID
Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: vi.fn(() => 'test-uuid-123')
  }
});

describe('VoiceErrorHandler', () => {
  let errorHandler: VoiceErrorHandler;
  let mockAudioBlob: Blob;

  beforeEach(() => {
    vi.clearAllMocks();
    errorHandler = new VoiceErrorHandler();
    mockAudioBlob = new Blob(['test audio'], { type: 'audio/wav' });
    
    // Reset navigator.onLine
    Object.defineProperty(navigator, 'onLine', { value: true });
  });

  afterEach(() => {
    errorHandler.clearQueue();
  });

  describe('handleVoiceError', () => {
    it('should handle network errors correctly', () => {
      const networkError = new TypeError('fetch failed');
      const result = errorHandler.handleVoiceError(networkError);

      expect(result.type).toBe(VoiceErrorType.NETWORK_ERROR);
      expect(result.recoverable).toBe(true);
      expect(result.userMessage).toContain('Unable to connect');
      expect(result.actionSteps).toContain('Check your internet connection');
    });

    it('should handle audio processing errors correctly', () => {
      const audioError = new Error('audio processing failed');
      const result = errorHandler.handleVoiceError(audioError);

      expect(result.type).toBe(VoiceErrorType.AUDIO_PROCESSING_ERROR);
      expect(result.recoverable).toBe(true);
      expect(result.userMessage).toContain('Voice input could not be processed');
      expect(result.actionSteps).toContain('Check your microphone permissions');
    });

    it('should handle transcription errors correctly', () => {
      const transcriptionError = new Error('transcription failed');
      const result = errorHandler.handleVoiceError(transcriptionError);

      expect(result.type).toBe(VoiceErrorType.TRANSCRIPTION_ERROR);
      expect(result.recoverable).toBe(true);
      expect(result.userMessage).toContain('Could not understand your voice input');
      expect(result.actionSteps).toContain('Try speaking more clearly');
    });

    it('should handle database errors correctly', () => {
      const dbError = new Error('database connection failed');
      const result = errorHandler.handleVoiceError(dbError);

      expect(result.type).toBe(VoiceErrorType.DATABASE_ERROR);
      expect(result.recoverable).toBe(true);
      expect(result.retryAfter).toBe(10);
    });

    it('should handle permission errors correctly', () => {
      const permissionError = new Error('permission denied');
      const result = errorHandler.handleVoiceError(permissionError);

      expect(result.type).toBe(VoiceErrorType.PERMISSION_ERROR);
      expect(result.recoverable).toBe(false);
      expect(result.actionSteps).toContain('Click the microphone icon');
    });

    it('should handle timeout errors correctly', () => {
      const timeoutError = new Error('timeout exceeded');
      const result = errorHandler.handleVoiceError(timeoutError);

      expect(result.type).toBe(VoiceErrorType.TIMEOUT_ERROR);
      expect(result.recoverable).toBe(true);
      expect(result.retryAfter).toBe(5);
    });

    it('should handle unknown errors correctly', () => {
      const unknownError = new Error('something unexpected');
      const result = errorHandler.handleVoiceError(unknownError);

      expect(result.type).toBe(VoiceErrorType.UNKNOWN_ERROR);
      expect(result.recoverable).toBe(true);
      expect(result.userMessage).toContain('unexpected error');
    });
  });

  describe('queueOfflineEvent', () => {
    it('should queue events for offline processing', async () => {
      const eventId = await errorHandler.queueOfflineEvent(mockAudioBlob, 'user123');
      
      expect(eventId).toBe('test-uuid-123');
      
      const status = errorHandler.getQueueStatus();
      expect(status.queuedEvents).toBe(1);
    });

    it('should limit queue size', async () => {
      // Create a handler with small queue size for testing
      const smallQueueHandler = new VoiceErrorHandler();
      smallQueueHandler['maxQueueSize'] = 2;

      await smallQueueHandler.queueOfflineEvent(mockAudioBlob, 'user1');
      await smallQueueHandler.queueOfflineEvent(mockAudioBlob, 'user2');
      await smallQueueHandler.queueOfflineEvent(mockAudioBlob, 'user3');

      const status = smallQueueHandler.getQueueStatus();
      expect(status.queuedEvents).toBe(2); // Should only keep 2 events
    });
  });

  describe('getQueueStatus', () => {
    it('should return correct queue status', () => {
      const status = errorHandler.getQueueStatus();
      
      expect(status).toHaveProperty('isOnline');
      expect(status).toHaveProperty('queuedEvents');
      expect(status).toHaveProperty('failedEvents');
      expect(typeof status.isOnline).toBe('boolean');
      expect(typeof status.queuedEvents).toBe('number');
      expect(typeof status.failedEvents).toBe('number');
    });
  });

  describe('clearQueue', () => {
    it('should clear all queued events', async () => {
      await errorHandler.queueOfflineEvent(mockAudioBlob, 'user1');
      await errorHandler.queueOfflineEvent(mockAudioBlob, 'user2');
      
      let status = errorHandler.getQueueStatus();
      expect(status.queuedEvents).toBe(2);
      
      errorHandler.clearQueue();
      
      status = errorHandler.getQueueStatus();
      expect(status.queuedEvents).toBe(0);
    });
  });

  describe('error utility methods', () => {
    it('should format error messages correctly', () => {
      const error = errorHandler.handleVoiceError(new Error('test error'));
      const message = errorHandler.getErrorMessage(error);
      
      expect(message).toContain(error.userMessage);
      expect(message).toContain(error.actionSteps[0]);
    });

    it('should correctly identify recoverable errors', () => {
      const recoverableError = errorHandler.handleVoiceError(new TypeError('fetch failed'));
      const nonRecoverableError = errorHandler.handleVoiceError(new Error('permission denied'));
      
      expect(errorHandler.isRecoverable(recoverableError)).toBe(true);
      expect(errorHandler.isRecoverable(nonRecoverableError)).toBe(false);
    });
  });

  describe('offline/online handling', () => {
    it('should update online status when connection changes', () => {
      // Simulate going offline
      Object.defineProperty(navigator, 'onLine', { value: false });
      window.dispatchEvent(new Event('offline'));
      
      let status = errorHandler.getQueueStatus();
      expect(status.isOnline).toBe(false);
      
      // Simulate going online
      Object.defineProperty(navigator, 'onLine', { value: true });
      window.dispatchEvent(new Event('online'));
      
      status = errorHandler.getQueueStatus();
      expect(status.isOnline).toBe(true);
    });
  });

  describe('retry scheduling', () => {
    it('should schedule retries with proper delay', () => {
      vi.useFakeTimers();
      
      const retryFunction = vi.fn().mockResolvedValue(undefined);
      errorHandler.scheduleRetry('test-event', retryFunction, 1000);
      
      expect(retryFunction).not.toHaveBeenCalled();
      
      vi.advanceTimersByTime(1000);
      
      expect(retryFunction).toHaveBeenCalledOnce();
      
      vi.useRealTimers();
    });

    it('should clear existing timeouts when scheduling new retry', () => {
      vi.useFakeTimers();
      
      const retryFunction1 = vi.fn().mockResolvedValue(undefined);
      const retryFunction2 = vi.fn().mockResolvedValue(undefined);
      
      errorHandler.scheduleRetry('test-event', retryFunction1, 1000);
      errorHandler.scheduleRetry('test-event', retryFunction2, 500);
      
      vi.advanceTimersByTime(500);
      expect(retryFunction2).toHaveBeenCalledOnce();
      expect(retryFunction1).not.toHaveBeenCalled();
      
      vi.advanceTimersByTime(500);
      expect(retryFunction1).not.toHaveBeenCalled();
      
      vi.useRealTimers();
    });
  });
});