import { describe, it, expect, vi, beforeEach } from 'vitest';
import { VoiceAnalyticsService } from '../VoiceAnalyticsService';
import { supabase } from '../../lib/supabase';

// Mock Supabase
vi.mock('../../lib/supabase', () => ({
  supabase: {
    from: vi.fn().mockReturnThis(),
    select: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    gte: vi.fn().mockReturnThis(),
    lte: vi.fn().mockReturnThis(),
    not: vi.fn().mockReturnThis(),
    order: vi.fn().mockReturnThis()
  }
}));

const mockSupabase = vi.mocked(supabase);

describe('VoiceAnalyticsService', () => {
  let service: VoiceAnalyticsService;

  beforeEach(() => {
    vi.clearAllMocks();
    service = new VoiceAnalyticsService();
  });

  describe('getAccuracyTrends', () => {
    it('should return stable trend when no events exist', async () => {
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        gte: vi.fn().mockReturnThis(),
        lte: vi.fn().mockReturnThis(),
        not: vi.fn().mockReturnThis(),
        order: vi.fn().mockResolvedValue({ data: [], error: null })
      } as any);

      const result = await service.getAccuracyTrends(30);

      expect(result).toEqual({
        trend: 'stable',
        currentAverage: 0,
        previousAverage: 0,
        changePercentage: 0,
        dailyAverages: []
      });
    });

    it('should calculate upward trend correctly', async () => {
      const mockEvents = [
        { created_at: '2024-01-01T10:00:00Z', voice_confidence_score: 0.7 },
        { created_at: '2024-01-01T11:00:00Z', voice_confidence_score: 0.75 },
        { created_at: '2024-01-02T10:00:00Z', voice_confidence_score: 0.85 },
        { created_at: '2024-01-02T11:00:00Z', voice_confidence_score: 0.9 }
      ];

      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        gte: vi.fn().mockReturnThis(),
        lte: vi.fn().mockReturnThis(),
        not: vi.fn().mockReturnThis(),
        order: vi.fn().mockResolvedValue({ data: mockEvents, error: null })
      } as any);

      const result = await service.getAccuracyTrends(30);

      expect(result.trend).toBe('up');
      expect(result.dailyAverages).toHaveLength(2);
      expect(result.dailyAverages[0].date).toBe('2024-01-01');
      expect(result.dailyAverages[0].average).toBe(0.725); // (0.7 + 0.75) / 2
      expect(result.dailyAverages[1].date).toBe('2024-01-02');
      expect(result.dailyAverages[1].average).toBe(0.875); // (0.85 + 0.9) / 2
    });

    it('should handle database errors gracefully', async () => {
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        gte: vi.fn().mockReturnThis(),
        lte: vi.fn().mockReturnThis(),
        not: vi.fn().mockReturnThis(),
        order: vi.fn().mockResolvedValue({ 
          data: null, 
          error: { message: 'Database connection failed' } 
        })
      } as any);

      await expect(service.getAccuracyTrends(30)).rejects.toThrow(
        'Failed to fetch voice events for analytics: Database connection failed'
      );
    });
  });

  describe('getReviewStatistics', () => {
    it('should return zero statistics when no reviewed events exist', async () => {
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        gte: vi.fn().mockReturnThis(),
        lte: vi.fn().mockReturnThis(),
        not: vi.fn().mockReturnThis(),
        order: vi.fn().mockResolvedValue({ data: [], error: null })
      } as any);

      const result = await service.getReviewStatistics(1);

      expect(result).toEqual({
        totalReviewed: 0,
        approved: 0,
        rejected: 0,
        approvalRate: 0,
        commonRejectionReasons: []
      });
    });

    it('should calculate review statistics correctly', async () => {
      const mockEvents = [
        { metadata: { review_status: 'approved' } },
        { metadata: { review_status: 'approved' } },
        { metadata: { review_status: 'rejected', rejection_reason: 'Incorrect product' } },
        { metadata: { review_status: 'rejected', rejection_reason: 'Incorrect product' } },
        { metadata: { review_status: 'rejected', rejection_reason: 'Poor audio quality' } }
      ];

      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        gte: vi.fn().mockReturnThis(),
        lte: vi.fn().mockReturnThis(),
        not: vi.fn().mockReturnThis(),
        order: vi.fn().mockResolvedValue({ data: mockEvents, error: null })
      } as any);

      const result = await service.getReviewStatistics(1);

      expect(result.totalReviewed).toBe(5);
      expect(result.approved).toBe(2);
      expect(result.rejected).toBe(3);
      expect(result.approvalRate).toBe(40); // 2/5 * 100
      expect(result.commonRejectionReasons).toEqual([
        { reason: 'Incorrect product', count: 2 },
        { reason: 'Poor audio quality', count: 1 }
      ]);
    });
  });

  describe('getConfidenceDistribution', () => {
    it('should return zero distribution when no events exist', async () => {
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        gte: vi.fn().mockReturnThis(),
        lte: vi.fn().mockReturnThis(),
        not: vi.fn().mockReturnThis(),
        order: vi.fn().mockResolvedValue({ data: [], error: null })
      } as any);

      const result = await service.getConfidenceDistribution(30);

      expect(result).toEqual({
        high: 0,
        medium: 0,
        low: 0,
        distribution: []
      });
    });

    it('should calculate confidence distribution correctly', async () => {
      const mockEvents = [
        { voice_confidence_score: 0.95 }, // high
        { voice_confidence_score: 0.92 }, // high
        { voice_confidence_score: 0.85 }, // medium
        { voice_confidence_score: 0.75 }, // medium
        { voice_confidence_score: 0.65 }, // low
        { voice_confidence_score: 0.55 }  // low
      ];

      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        gte: vi.fn().mockReturnThis(),
        lte: vi.fn().mockReturnThis(),
        not: vi.fn().mockReturnThis(),
        order: vi.fn().mockResolvedValue({ data: mockEvents, error: null })
      } as any);

      const result = await service.getConfidenceDistribution(30);

      expect(result.high).toBe(2);
      expect(result.medium).toBe(2);
      expect(result.low).toBe(2);
      expect(result.distribution).toEqual([
        { range: 'High (90-100%)', count: 2, percentage: 33.333333333333336 },
        { range: 'Medium (70-89%)', count: 2, percentage: 33.333333333333336 },
        { range: 'Low (<70%)', count: 2, percentage: 33.333333333333336 }
      ]);
    });
  });

  describe('getProcessingMetrics', () => {
    it('should return mock processing metrics', async () => {
      const result = await service.getProcessingMetrics(7);

      expect(result).toEqual({
        averageProcessingTime: 1.2,
        totalProcessed: 0,
        successRate: 95.5,
        peakHours: []
      });
    });
  });

  it('should handle service errors gracefully', async () => {
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    
    mockSupabase.from.mockImplementation(() => {
      throw new Error('Service unavailable');
    });

    await expect(service.getAccuracyTrends(30)).rejects.toThrow('Service unavailable');
    
    expect(consoleErrorSpy).toHaveBeenCalledWith(
      'VoiceAnalyticsService.getAccuracyTrends error:',
      expect.any(Error)
    );

    consoleErrorSpy.mkRestore();
  });
});