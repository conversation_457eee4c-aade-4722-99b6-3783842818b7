import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { AudioStorageService } from '../AudioStorageService';
import { supabase } from '../../lib/supabase';

// Mock Supabase
vi.mock('../../lib/supabase', () => ({
  supabase: {
    storage: {
      listBuckets: vi.fn(),
      createBucket: vi.fn(),
      from: vi.fn(() => ({
        upload: vi.fn(),
        getPublicUrl: vi.fn(),
        createSignedUrl: vi.fn(),
        list: vi.fn(),
        remove: vi.fn()
      }))
    }
  }
}));

// Mock AudioContext
global.AudioContext = vi.fn().mockImplementation(() => ({
  createBuffer: vi.fn(),
  decodeAudioData: vi.fn(),
  close: vi.fn()
}));

(global as any).webkitAudioContext = global.AudioContext;

describe('AudioStorageService', () => {
  let service: AudioStorageService;
  let mockSupabase: any;

  beforeEach(() => {
    service = new AudioStorageService();
    mockSupabase = supabase as any;
    vi.clearAllMocks();
  });

  describe('uploadAudioRecording', () => {
    it('should upload audio recording successfully', async () => {
      const mockBlob = new Blob(['audio data'], { type: 'audio/webm' });
      const eventId = 'test-event-id';
      const userId = 'test-user-id';

      const mockUpload = vi.fn().mockResolvedValue({
        data: { path: 'events/test-event-id/2025-08-15T10-00-00-000Z.webm' },
        error: null
      });

      const mockGetPublicUrl = vi.fn().mockReturnValue({
        data: { publicUrl: 'https://example.com/audio.webm' }
      });

      mockSupabase.storage.from.mockReturnValue({
        upload: mockUpload,
        getPublicUrl: mockGetPublicUrl
      });

      const result = await service.uploadAudioRecording(mockBlob, eventId, userId);

      expect(mockSupabase.storage.from).toHaveBeenCalledWith('voice-recordings');
      expect(mockUpload).toHaveBeenCalledWith(
        expect.stringMatching(/^events\/test-event-id\/\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}-\d{3}Z\.webm$/),
        mockBlob,
        expect.objectContaining({
          cacheControl: '3600',
          upsert: false,
          metadata: expect.objectContaining({
            eventId,
            userId,
            originalSize: mockBlob.size.toString(),
            compressedSize: mockBlob.size.toString()
          })
        })
      );
      expect(result).toBe('https://example.com/audio.webm');
    });

    it('should reject files that are too large', async () => {
      const largeBlob = new Blob([new ArrayBuffer(11 * 1024 * 1024)], { type: 'audio/webm' }); // 11MB
      const eventId = 'test-event-id';

      const result = await service.uploadAudioRecording(largeBlob, eventId);

      expect(result).toBeNull();
      expect(mockSupabase.storage.from).not.toHaveBeenCalled();
    });

    it('should handle upload errors gracefully', async () => {
      const mockBlob = new Blob(['audio data'], { type: 'audio/webm' });
      const eventId = 'test-event-id';

      const mockUpload = vi.fn().mockResolvedValue({
        data: null,
        error: { message: 'Upload failed' }
      });

      mockSupabase.storage.from.mockReturnValue({
        upload: mockUpload,
        getPublicUrl: vi.fn()
      });

      const result = await service.uploadAudioRecording(mockBlob, eventId);

      expect(result).toBeNull();
    });

    it('should warn about unsupported MIME types but still attempt upload', async () => {
      const mockBlob = new Blob(['audio data'], { type: 'audio/unsupported' });
      const eventId = 'test-event-id';

      const mockUpload = vi.fn().mockResolvedValue({
        data: { path: 'events/test-event-id/audio.webm' },
        error: null
      });

      const mockGetPublicUrl = vi.fn().mockReturnValue({
        data: { publicUrl: 'https://example.com/audio.webm' }
      });

      mockSupabase.storage.from.mockReturnValue({
        upload: mockUpload,
        getPublicUrl: mockGetPublicUrl
      });

      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      const result = await service.uploadAudioRecording(mockBlob, eventId);

      expect(consoleSpy).toHaveBeenCalledWith('Unsupported audio type: audio/unsupported. Attempting to upload anyway.');
      expect(result).toBe('https://example.com/audio.webm');

      consoleSpy.mockRestore();
    });
  });

  describe('getAudioRecordingUrl', () => {
    it('should get signed URL for audio recording', async () => {
      const audioPath = 'events/test-event-id/audio.webm';
      const mockSignedUrl = 'https://example.com/signed-url';

      const mockCreateSignedUrl = vi.fn().mockResolvedValue({
        data: { signedUrl: mockSignedUrl },
        error: null
      });

      mockSupabase.storage.from.mockReturnValue({
        createSignedUrl: mockCreateSignedUrl
      });

      const result = await service.getAudioRecordingUrl(audioPath);

      expect(mockSupabase.storage.from).toHaveBeenCalledWith('voice-recordings');
      expect(mockCreateSignedUrl).toHaveBeenCalledWith(audioPath, 3600);
      expect(result).toBe(mockSignedUrl);
    });

    it('should handle signed URL errors', async () => {
      const audioPath = 'events/test-event-id/audio.webm';

      const mockCreateSignedUrl = vi.fn().mockResolvedValue({
        data: null,
        error: { message: 'Access denied' }
      });

      mockSupabase.storage.from.mockReturnValue({
        createSignedUrl: mockCreateSignedUrl
      });

      const result = await service.getAudioRecordingUrl(audioPath);

      expect(result).toBeNull();
    });

    it('should extract file path from full URL', async () => {
      const fullUrl = 'https://example.com/storage/v1/object/public/voice-recordings/events/test-event-id/audio.webm';
      const expectedPath = 'events/test-event-id/audio.webm';

      const mockCreateSignedUrl = vi.fn().mockResolvedValue({
        data: { signedUrl: 'https://example.com/signed-url' },
        error: null
      });

      mockSupabase.storage.from.mockReturnValue({
        createSignedUrl: mockCreateSignedUrl
      });

      await service.getAudioRecordingUrl(fullUrl);

      expect(mockCreateSignedUrl).toHaveBeenCalledWith(expectedPath, 3600);
    });
  });

  describe('deleteAudioRecording', () => {
    it('should delete all audio files for an event', async () => {
      const eventId = 'test-event-id';
      const mockFiles = [
        { name: 'audio1.webm' },
        { name: 'audio2.webm' }
      ];

      const mockList = vi.fn().mockResolvedValue({
        data: mockFiles,
        error: null
      });

      const mockRemove = vi.fn().mockResolvedValue({
        error: null
      });

      mockSupabase.storage.from.mockReturnValue({
        list: mockList,
        remove: mockRemove
      });

      const result = await service.deleteAudioRecording(eventId);

      expect(mockList).toHaveBeenCalledWith(`events/${eventId}`);
      expect(mockRemove).toHaveBeenCalledWith([
        `events/${eventId}/audio1.webm`,
        `events/${eventId}/audio2.webm`
      ]);
      expect(result).toBe(true);
    });

    it('should return true when no files exist', async () => {
      const eventId = 'test-event-id';

      const mockList = vi.fn().mockResolvedValue({
        data: [],
        error: null
      });

      mockSupabase.storage.from.mockReturnValue({
        list: mockList,
        remove: vi.fn()
      });

      const result = await service.deleteAudioRecording(eventId);

      expect(result).toBe(true);
    });

    it('should handle delete errors', async () => {
      const eventId = 'test-event-id';
      const mockFiles = [{ name: 'audio1.webm' }];

      const mockList = vi.fn().mockResolvedValue({
        data: mockFiles,
        error: null
      });

      const mockRemove = vi.fn().mockResolvedValue({
        error: { message: 'Delete failed' }
      });

      mockSupabase.storage.from.mockReturnValue({
        list: mockList,
        remove: mockRemove
      });

      const result = await service.deleteAudioRecording(eventId);

      expect(result).toBe(false);
    });
  });

  describe('getStorageStats', () => {
    it('should return storage statistics', async () => {
      const mockFiles = [
        { name: 'audio1.webm', metadata: { size: 1000 }, created_at: '2025-08-15T10:00:00Z' },
        { name: 'audio2.webm', metadata: { size: 2000 }, created_at: '2025-08-14T10:00:00Z' }
      ];

      const mockList = vi.fn().mockResolvedValue({
        data: mockFiles,
        error: null
      });

      mockSupabase.storage.from.mockReturnValue({
        list: mockList
      });

      const result = await service.getStorageStats();

      expect(result).toEqual({
        totalFiles: 2,
        totalSize: 3000,
        newestFile: 'audio1.webm',
        oldestFile: 'audio2.webm'
      });
    });

    it('should handle empty storage', async () => {
      const mockList = vi.fn().mockResolvedValue({
        data: [],
        error: null
      });

      mockSupabase.storage.from.mockReturnValue({
        list: mockList
      });

      const result = await service.getStorageStats();

      expect(result).toEqual({
        totalFiles: 0,
        totalSize: 0
      });
    });
  });

  describe('cleanupOldRecordings', () => {
    it('should delete old recordings', async () => {
      const oldDate = new Date();
      oldDate.setDate(oldDate.getDate() - 100); // 100 days old

      const recentDate = new Date();
      recentDate.setDate(recentDate.getDate() - 30); // 30 days old

      const mockFiles = [
        { name: 'old-audio.webm', created_at: oldDate.toISOString() },
        { name: 'recent-audio.webm', created_at: recentDate.toISOString() }
      ];

      const mockList = vi.fn().mockResolvedValue({
        data: mockFiles,
        error: null
      });

      const mockRemove = vi.fn().mockResolvedValue({
        error: null
      });

      mockSupabase.storage.from.mockReturnValue({
        list: mockList,
        remove: mockRemove
      });

      const result = await service.cleanupOldRecordings(90); // Delete files older than 90 days

      expect(mockRemove).toHaveBeenCalledWith(['events/old-audio.webm']);
      expect(result).toBe(1);
    });

    it('should return 0 when no old files exist', async () => {
      const recentDate = new Date();
      recentDate.setDate(recentDate.getDate() - 30);

      const mockFiles = [
        { name: 'recent-audio.webm', created_at: recentDate.toISOString() }
      ];

      const mockList = vi.fn().mockResolvedValue({
        data: mockFiles,
        error: null
      });

      mockSupabase.storage.from.mockReturnValue({
        list: mockList,
        remove: vi.fn()
      });

      const result = await service.cleanupOldRecordings(90);

      expect(result).toBe(0);
    });
  });
});