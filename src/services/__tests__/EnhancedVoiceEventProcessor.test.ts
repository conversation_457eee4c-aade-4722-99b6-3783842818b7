import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { EnhancedVoiceEventProcessor } from '../EnhancedVoiceEventProcessor';
import VoiceProcessor, { VoiceCommand } from '../../lib/voice-processor';
import { voiceEventService } from '../VoiceEventService';

// Mock the VoiceProcessor
vi.mock('../../lib/voice-processor', () => ({
  default: vi.fn().mockImplementation(() => ({
    processAudioBlob: vi.fn(),
    getProcessingStatus: vi.fn().mockReturnValue(false)
  }))
}));

// Mock the VoiceEventService
vi.mock('../VoiceEventService', () => ({
  voiceEventService: {
    createVoiceEvent: vi.fn()
  }
}));

// Mock Web Speech API
Object.defineProperty(window, 'speechSynthesis', {
  writable: true,
  value: {
    speak: vi.fn(),
    cancel: vi.fn(),
    pause: vi.fn(),
    resume: vi.fn(),
    getVoices: vi.fn().mockReturnValue([])
  }
});

global.SpeechSynthesisUtterance = vi.fn().mockImplementation((text) => ({
  text,
  rate: 1,
  pitch: 1,
  volume: 1,
  onend: null,
  onerror: null
}));

// Mock URL.createObjectURL
global.URL.createObjectURL = vi.fn().mockReturnValue('blob:mock-url');

describe('EnhancedVoiceEventProcessor', () => {
  let processor: EnhancedVoiceEventProcessor;
  let mockVoiceProcessor: any;
  let mockVoiceEventService: any;

  beforeEach(() => {
    processor = new EnhancedVoiceEventProcessor();
    mockVoiceProcessor = new (VoiceProcessor as any)();
    mockVoiceEventService = voiceEventService as any;
    vi.clearAllMocks();
  });

  describe('processVoiceEvent', () => {
    it('should process voice event successfully with high confidence', async () => {
      const mockAudioBlob = new Blob(['test audio'], { type: 'audio/webm' });
      const mockVoiceCommand: VoiceCommand = {
        action_type: 'create_event',
        event_type: 'receiving',
        product_name: 'Salmon Fillet',
        quantity: 25,
        unit: 'lbs',
        vendor_name: 'Pacific Seafoods',
        confidence_score: 0.9,
        confidence_breakdown: {
          product_match: 0.95,
          quantity_extraction: 0.85,
          vendor_match: 0.9,
          overall: 0.9
        },
        raw_transcript: 'Received 25 pounds of salmon fillet from Pacific Seafoods'
      };

      const mockSavedEvent = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        product_name: 'Salmon Fillet',
        quantity: 25,
        unit: 'lbs',
        vendor_name: 'Pacific Seafoods',
        voice_confidence_score: 0.9,
        event_type: 'receiving'
      };

      mockVoiceProcessor.processAudioBlob.mockResolvedValue(mockVoiceCommand);
      mockVoiceEventService.createVoiceEvent.mockResolvedValue(mockSavedEvent);

      const result = await processor.processVoiceEvent(mockAudioBlob, 'user123');

      expect(mockVoiceProcessor.processAudioBlob).toHaveBeenCalledWith(mockAudioBlob);
      expect(mockVoiceEventService.createVoiceEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          event_type: 'receiving',
          product_name: 'Salmon Fillet',
          quantity: 25,
          unit: 'lbs',
          vendor_name: 'Pacific Seafoods',
          voice_confidence_score: 0.9,
          raw_transcript: 'Received 25 pounds of salmon fillet from Pacific Seafoods'
        })
      );
      expect(result.confidence).toBe(0.9);
      expect(result.requiresConfirmation).toBe(false);
      expect(result.eventData).toEqual(mockSavedEvent);
    });

    it('should flag low confidence events for review', async () => {
      const mockAudioBlob = new Blob(['test audio'], { type: 'audio/webm' });
      const mockVoiceCommand: VoiceCommand = {
        action_type: 'create_event',
        event_type: 'receiving',
        product_name: 'Unknown Product',
        quantity: 10,
        unit: 'lbs',
        confidence_score: 0.5, // Low confidence
        raw_transcript: 'Unclear audio transcript'
      };

      const mockSavedEvent = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        product_name: 'Unknown Product',
        quantity: 10,
        voice_confidence_score: 0.5
      };

      mockVoiceProcessor.processAudioBlob.mockResolvedValue(mockVoiceCommand);
      mockVoiceEventService.createVoiceEvent.mockResolvedValue(mockSavedEvent);

      const result = await processor.processVoiceEvent(mockAudioBlob);

      expect(result.confidence).toBe(0.5);
      expect(result.requiresConfirmation).toBe(true);
    });

    it('should handle voice processing errors', async () => {
      const mockAudioBlob = new Blob(['test audio'], { type: 'audio/webm' });
      const mockError = new Error('Voice processing failed');

      mockVoiceProcessor.processAudioBlob.mockRejectedValue(mockError);

      await expect(processor.processVoiceEvent(mockAudioBlob)).rejects.toThrow('Voice processing failed');
    });
  });

  describe('processVoiceEventWithRetry', () => {
    it('should retry on failure and succeed on second attempt', async () => {
      const mockAudioBlob = new Blob(['test audio'], { type: 'audio/webm' });
      const mockVoiceCommand: VoiceCommand = {
        action_type: 'create_event',
        event_type: 'receiving',
        product_name: 'Salmon Fillet',
        quantity: 25,
        unit: 'lbs',
        confidence_score: 0.8,
        raw_transcript: 'Test transcript'
      };

      const mockSavedEvent = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        product_name: 'Salmon Fillet',
        voice_confidence_score: 0.8
      };

      // First call fails, second succeeds
      mockVoiceProcessor.processAudioBlob
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce(mockVoiceCommand);
      
      mockVoiceEventService.createVoiceEvent.mockResolvedValue(mockSavedEvent);

      const result = await processor.processVoiceEventWithRetry(mockAudioBlob);

      expect(mockVoiceProcessor.processAudioBlob).toHaveBeenCalledTimes(2);
      expect(result.confidence).toBe(0.8);
    });

    it('should fail after max retries', async () => {
      const mockAudioBlob = new Blob(['test audio'], { type: 'audio/webm' });
      const mockError = new Error('Persistent error');

      mockVoiceProcessor.processAudioBlob.mockRejectedValue(mockError);

      await expect(processor.processVoiceEventWithRetry(mockAudioBlob)).rejects.toThrow('Persistent error');
      expect(mockVoiceProcessor.processAudioBlob).toHaveBeenCalledTimes(4); // Initial + 3 retries
    });
  });

  describe('confidence threshold management', () => {
    it('should check confidence threshold correctly', () => {
      expect(processor.checkConfidenceThreshold(0.8)).toBe(true);
      expect(processor.checkConfidenceThreshold(0.6)).toBe(false);
    });

    it('should set and get confidence threshold', () => {
      processor.setConfidenceThreshold(0.8);
      expect(processor.getConfidenceThreshold()).toBe(0.8);
    });

    it('should validate confidence threshold range', () => {
      expect(() => processor.setConfidenceThreshold(-0.1)).toThrow('Confidence threshold must be between 0.0 and 1.0');
      expect(() => processor.setConfidenceThreshold(1.1)).toThrow('Confidence threshold must be between 0.0 and 1.0');
    });
  });

  describe('audio confirmation', () => {
    it('should generate appropriate confirmation text for receiving event', async () => {
      const mockAudioBlob = new Blob(['test audio'], { type: 'audio/webm' });
      const mockVoiceCommand: VoiceCommand = {
        action_type: 'create_event',
        event_type: 'receiving',
        product_name: 'Salmon Fillet',
        quantity: 25,
        unit: 'lbs',
        vendor_name: 'Pacific Seafoods',
        confidence_score: 0.9,
        raw_transcript: 'Test transcript'
      };

      const mockSavedEvent = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        event_type: 'receiving',
        product_name: 'Salmon Fillet',
        quantity: 25,
        unit: 'lbs',
        vendor_name: 'Pacific Seafoods',
        voice_confidence_score: 0.9
      };

      mockVoiceProcessor.processAudioBlob.mockResolvedValue(mockVoiceCommand);
      mockVoiceEventService.createVoiceEvent.mockResolvedValue(mockSavedEvent);

      const result = await processor.processVoiceEvent(mockAudioBlob);

      expect(result.audioConfirmation).toContain('Successfully recorded receiving');
      expect(result.audioConfirmation).toContain('25 lbs of Salmon Fillet');
      expect(result.audioConfirmation).toContain('from Pacific Seafoods');
    });

    it('should generate appropriate confirmation text for sale event', async () => {
      const mockAudioBlob = new Blob(['test audio'], { type: 'audio/webm' });
      const mockVoiceCommand: VoiceCommand = {
        action_type: 'create_event',
        event_type: 'sale',
        product_name: 'Crab Legs',
        quantity: 10,
        unit: 'lbs',
        customer_name: 'Restaurant ABC',
        confidence_score: 0.85,
        raw_transcript: 'Test transcript'
      };

      const mockSavedEvent = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        event_type: 'sale',
        product_name: 'Crab Legs',
        quantity: 10,
        unit: 'lbs',
        customer_name: 'Restaurant ABC',
        voice_confidence_score: 0.85
      };

      mockVoiceProcessor.processAudioBlob.mockResolvedValue(mockVoiceCommand);
      mockVoiceEventService.createVoiceEvent.mockResolvedValue(mockSavedEvent);

      const result = await processor.processVoiceEvent(mockAudioBlob);

      expect(result.audioConfirmation).toContain('Successfully recorded sale');
      expect(result.audioConfirmation).toContain('10 lbs of Crab Legs');
      expect(result.audioConfirmation).toContain('to Restaurant ABC');
    });
  });

  describe('error handling', () => {
    it('should provide user-friendly error messages for API key errors', () => {
      const error = new Error('API key not found');
      const message = processor.handleVoiceProcessingError(error);
      expect(message).toBe('Voice processing is not configured. Please check your API settings.');
    });

    it('should provide user-friendly error messages for network errors', () => {
      const error = new Error('Network fetch failed');
      const message = processor.handleVoiceProcessingError(error);
      expect(message).toBe('Network error. Please check your internet connection and try again.');
    });

    it('should provide user-friendly error messages for audio errors', () => {
      const error = new Error('Audio processing failed');
      const message = processor.handleVoiceProcessingError(error);
      expect(message).toBe('Audio processing failed. Please ensure your microphone is working and try again.');
    });

    it('should provide generic error message for unknown errors', () => {
      const error = new Error('Unknown error');
      const message = processor.handleVoiceProcessingError(error);
      expect(message).toBe('Voice processing failed. Please try again or enter the information manually.');
    });
  });

  describe('processTextAsVoice', () => {
    it('should process text input as mock voice event', async () => {
      const testText = 'Received 20 pounds of cod from Ocean Fresh';
      
      const mockSavedEvent = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        product_name: 'Test Product',
        quantity: 1,
        unit: 'lbs',
        voice_confidence_score: 0.8
      };

      mockVoiceEventService.createVoiceEvent.mockResolvedValue(mockSavedEvent);

      const result = await processor.processTextAsVoice(testText, 'user123');

      expect(mockVoiceEventService.createVoiceEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          raw_transcript: testText,
          voice_confidence_score: 0.8
        })
      );
      expect(result.confidence).toBe(0.8);
      expect(result.eventData).toEqual(mockSavedEvent);
    });
  });

  describe('processing status', () => {
    it('should return processing status from underlying VoiceProcessor', () => {
      mockVoiceProcessor.getProcessingStatus.mockReturnValue(true);
      expect(processor.isProcessing()).toBe(true);

      mockVoiceProcessor.getProcessingStatus.mockReturnValue(false);
      expect(processor.isProcessing()).toBe(false);
    });
  });
});