import { supabase } from '../lib/supabase';

/**
 * Service for managing audio recording storage using Supabase Storage
 * Handles upload, retrieval, compression, and cleanup of voice event audio files
 */
export class AudioStorageService {
  private readonly bucketName = 'voice-recordings';
  private readonly maxFileSize = 10 * 1024 * 1024; // 10MB
  private readonly allowedMimeTypes = ['audio/webm', 'audio/wav', 'audio/mp3', 'audio/ogg'];

  constructor() {
    this.initializeBucket();
  }

  /**
   * Initialize the storage bucket if it doesn't exist
   */
  private async initializeBucket(): Promise<void> {
    try {
      const { data: buckets } = await supabase.storage.listBuckets();
      const bucketExists = buckets?.some(bucket => bucket.name === this.bucketName);

      if (!bucketExists) {
        const { error } = await supabase.storage.createBucket(this.bucketName, {
          public: false,
          allowedMimeTypes: this.allowedMimeTypes,
          fileSizeLimit: this.maxFileSize
        });

        if (error) {
          console.error('Error creating audio storage bucket:', error);
        } else {
          console.log('Audio storage bucket created successfully');
        }
      }
    } catch (error) {
      console.error('Error initializing audio storage bucket:', error);
    }
  }

  /**
   * Upload audio recording for a voice event
   * @param audioBlob - The audio blob to upload
   * @param eventId - ID of the voice event
   * @param userId - ID of the user uploading
   * @returns Promise<string | null> - URL of uploaded audio or null if failed
   */
  async uploadAudioRecording(
    audioBlob: Blob, 
    eventId: string, 
    userId?: string
  ): Promise<string | null> {
    try {
      // Validate file size
      if (audioBlob.size > this.maxFileSize) {
        throw new Error(`Audio file too large. Maximum size is ${this.maxFileSize / 1024 / 1024}MB`);
      }

      // Validate MIME type
      if (!this.allowedMimeTypes.includes(audioBlob.type)) {
        console.warn(`Unsupported audio type: ${audioBlob.type}. Attempting to upload anyway.`);
      }

      // Compress audio if needed
      const compressedBlob = await this.compressAudio(audioBlob);

      // Generate file path
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileExtension = this.getFileExtension(compressedBlob.type);
      const filePath = `events/${eventId}/${timestamp}${fileExtension}`;

      // Upload to Supabase Storage
      const { data, error } = await supabase.storage
        .from(this.bucketName)
        .upload(filePath, compressedBlob, {
          cacheControl: '3600',
          upsert: false,
          metadata: {
            eventId,
            userId: userId || 'unknown',
            originalSize: audioBlob.size.toString(),
            compressedSize: compressedBlob.size.toString(),
            uploadedAt: new Date().toISOString()
          }
        });

      if (error) {
        console.error('Error uploading audio:', error);
        return null;
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from(this.bucketName)
        .getPublicUrl(filePath);

      return urlData.publicUrl;
    } catch (error) {
      console.error('AudioStorageService.uploadAudioRecording error:', error);
      return null;
    }
  }

  /**
   * Retrieve audio recording URL for a voice event
   * @param audioPath - Path to the audio file
   * @returns Promise<string | null> - Signed URL for audio access
   */
  async getAudioRecordingUrl(audioPath: string): Promise<string | null> {
    try {
      // Extract file path from full URL if needed
      const filePath = this.extractFilePathFromUrl(audioPath);

      // Get signed URL for private access
      const { data, error } = await supabase.storage
        .from(this.bucketName)
        .createSignedUrl(filePath, 3600); // 1 hour expiry

      if (error) {
        console.error('Error getting signed URL:', error);
        return null;
      }

      return data.signedUrl;
    } catch (error) {
      console.error('AudioStorageService.getAudioRecordingUrl error:', error);
      return null;
    }
  }

  /**
   * Delete audio recording for a voice event
   * @param eventId - ID of the voice event
   * @returns Promise<boolean> - Success status
   */
  async deleteAudioRecording(eventId: string): Promise<boolean> {
    try {
      // List all files for this event
      const { data: files, error: listError } = await supabase.storage
        .from(this.bucketName)
        .list(`events/${eventId}`);

      if (listError) {
        console.error('Error listing audio files:', listError);
        return false;
      }

      if (!files || files.length === 0) {
        return true; // No files to delete
      }

      // Delete all files for this event
      const filePaths = files.map(file => `events/${eventId}/${file.name}`);
      const { error: deleteError } = await supabase.storage
        .from(this.bucketName)
        .remove(filePaths);

      if (deleteError) {
        console.error('Error deleting audio files:', deleteError);
        return false;
      }

      console.log(`Deleted ${filePaths.length} audio files for event ${eventId}`);
      return true;
    } catch (error) {
      console.error('AudioStorageService.deleteAudioRecording error:', error);
      return false;
    }
  }

  /**
   * Compress audio blob to reduce file size
   * @param audioBlob - Original audio blob
   * @returns Promise<Blob> - Compressed audio blob
   */
  private async compressAudio(audioBlob: Blob): Promise<Blob> {
    try {
      // If file is already small enough, return as-is
      if (audioBlob.size < 1024 * 1024) { // Less than 1MB
        return audioBlob;
      }

      // For now, we'll implement basic compression by converting to a more efficient format
      // In a production environment, you might want to use a more sophisticated audio compression library
      
      // Create audio context for processing
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      
      try {
        // Convert blob to array buffer
        const arrayBuffer = await audioBlob.arrayBuffer();
        
        // Decode audio data
        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
        
        // Reduce sample rate for compression (if original is high)
        const targetSampleRate = Math.min(audioBuffer.sampleRate, 22050); // Max 22kHz
        const compressionRatio = targetSampleRate / audioBuffer.sampleRate;
        
        if (compressionRatio < 1) {
          // Create new buffer with reduced sample rate
          const compressedBuffer = audioContext.createBuffer(
            audioBuffer.numberOfChannels,
            Math.floor(audioBuffer.length * compressionRatio),
            targetSampleRate
          );

          // Copy and downsample audio data
          for (let channel = 0; channel < audioBuffer.numberOfChannels; channel++) {
            const originalData = audioBuffer.getChannelData(channel);
            const compressedData = compressedBuffer.getChannelData(channel);
            
            for (let i = 0; i < compressedData.length; i++) {
              const originalIndex = Math.floor(i / compressionRatio);
              compressedData[i] = originalData[originalIndex] || 0;
            }
          }

          // Convert back to blob (this is a simplified approach)
          // In production, you'd use a proper audio encoder
          console.log(`Audio compressed from ${audioBlob.size} to estimated ${Math.floor(audioBlob.size * compressionRatio)} bytes`);
        }
      } catch (decodeError) {
        console.warn('Audio compression failed, using original:', decodeError);
      } finally {
        audioContext.close();
      }

      // For now, return original blob since proper compression requires additional libraries
      return audioBlob;
    } catch (error) {
      console.warn('Audio compression failed, using original:', error);
      return audioBlob;
    }
  }

  /**
   * Get file extension based on MIME type
   * @param mimeType - MIME type of the audio file
   * @returns string - File extension
   */
  private getFileExtension(mimeType: string): string {
    const extensions: { [key: string]: string } = {
      'audio/webm': '.webm',
      'audio/wav': '.wav',
      'audio/mp3': '.mp3',
      'audio/mpeg': '.mp3',
      'audio/ogg': '.ogg',
      'audio/x-wav': '.wav'
    };

    return extensions[mimeType] || '.webm';
  }

  /**
   * Extract file path from full Supabase Storage URL
   * @param url - Full URL or file path
   * @returns string - File path for storage operations
   */
  private extractFilePathFromUrl(url: string): string {
    if (url.includes('/storage/v1/object/public/')) {
      // Extract path from public URL
      const parts = url.split(`/storage/v1/object/public/${  this.bucketName  }/`);
      return parts[1] || url;
    }
    
    if (url.includes('/storage/v1/object/sign/')) {
      // Extract path from signed URL
      const parts = url.split(`/storage/v1/object/sign/${  this.bucketName  }/`);
      const pathWithQuery = parts[1] || url;
      return pathWithQuery.split('?')[0]; // Remove query parameters
    }

    // Assume it's already a file path
    return url;
  }

  /**
   * Get storage statistics for monitoring
   * @returns Promise<object> - Storage usage statistics
   */
  async getStorageStats(): Promise<{
    totalFiles: number;
    totalSize: number;
    oldestFile?: string;
    newestFile?: string;
  }> {
    try {
      const { data: files, error } = await supabase.storage
        .from(this.bucketName)
        .list('events', {
          limit: 1000,
          sortBy: { column: 'created_at', order: 'desc' }
        });

      if (error) {
        console.error('Error getting storage stats:', error);
        return { totalFiles: 0, totalSize: 0 };
      }

      const totalFiles = files?.length || 0;
      const totalSize = files?.reduce((sum, file) => sum + (file.metadata?.size || 0), 0) || 0;
      const newestFile = files?.[0]?.name;
      const oldestFile = files?.[files.length - 1]?.name;

      return {
        totalFiles,
        totalSize,
        oldestFile,
        newestFile
      };
    } catch (error) {
      console.error('AudioStorageService.getStorageStats error:', error);
      return { totalFiles: 0, totalSize: 0 };
    }
  }

  /**
   * Clean up old audio recordings (older than specified days)
   * @param daysOld - Number of days after which to delete recordings
   * @returns Promise<number> - Number of files deleted
   */
  async cleanupOldRecordings(daysOld: number = 90): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      const { data: files, error } = await supabase.storage
        .from(this.bucketName)
        .list('events', {
          limit: 1000,
          sortBy: { column: 'created_at', order: 'asc' }
        });

      if (error) {
        console.error('Error listing files for cleanup:', error);
        return 0;
      }

      const filesToDelete = files?.filter(file => {
        const fileDate = new Date(file.created_at);
        return fileDate < cutoffDate;
      }) || [];

      if (filesToDelete.length === 0) {
        return 0;
      }

      const filePaths = filesToDelete.map(file => `events/${file.name}`);
      const { error: deleteError } = await supabase.storage
        .from(this.bucketName)
        .remove(filePaths);

      if (deleteError) {
        console.error('Error during cleanup:', deleteError);
        return 0;
      }

      console.log(`Cleaned up ${filesToDelete.length} old audio recordings`);
      return filesToDelete.length;
    } catch (error) {
      console.error('AudioStorageService.cleanupOldRecordings error:', error);
      return 0;
    }
  }
}

// Export singleton instance
export const audioStorageService = new AudioStorageService();