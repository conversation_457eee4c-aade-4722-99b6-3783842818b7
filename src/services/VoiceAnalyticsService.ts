import { supabase } from '../lib/supabase';

/**
 * Service for voice recognition analytics and trend analysis
 */
export class VoiceAnalyticsService {
  
  /**
   * Get voice recognition accuracy trends over time
   * @param days - Number of days to analyze (default: 30)
   * @returns Promise with trend data
   */
  async getAccuracyTrends(days: number = 30): Promise<{
    trend: 'up' | 'down' | 'stable';
    currentAverage: number;
    previousAverage: number;
    changePercentage: number;
    dailyAverages: Array<{ date: string; average: number; count: number }>;
  }> {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      // Get voice events with confidence scores for the period
      const { data: events, error } = await supabase
        .from('inventory_events')
        .select('created_at, voice_confidence_score')
        .eq('created_by_voice', true)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString())
        .not('voice_confidence_score', 'is', null)
        .order('created_at');

      if (error) {
        throw new Error(`Failed to fetch voice events for analytics: ${error.message}`);
      }

      if (!events || events.length === 0) {
        return {
          trend: 'stable',
          currentAverage: 0,
          previousAverage: 0,
          changePercentage: 0,
          dailyAverages: []
        };
      }

      // Group events by day and calculate daily averages
      const dailyGroups: { [key: string]: number[] } = {};
      
      events.forEach(event => {
        const date = new Date(event.created_at).toISOString().split('T')[0];
        if (!dailyGroups[date]) {
          dailyGroups[date] = [];
        }
        dailyGroups[date].push(event.voice_confidence_score);
      });

      // Calculate daily averages
      const dailyAverages = Object.entries(dailyGroups)
        .map(([date, scores]) => ({
          date,
          average: scores.reduce((sum, score) => sum + score, 0) / scores.length,
          count: scores.length
        }))
        .sort((a, b) => a.date.localeCompare(b.date));

      if (dailyAverages.length < 2) {
        return {
          trend: 'stable',
          currentAverage: dailyAverages[0]?.average || 0,
          previousAverage: dailyAverages[0]?.average || 0,
          changePercentage: 0,
          dailyAverages
        };
      }

      // Calculate trend by comparing first half vs second half of the period
      const midPoint = Math.floor(dailyAverages.length / 2);
      const firstHalf = dailyAverages.slice(0, midPoint);
      const secondHalf = dailyAverages.slice(midPoint);

      const previousAverage = firstHalf.reduce((sum, day) => sum + day.average, 0) / firstHalf.length;
      const currentAverage = secondHalf.reduce((sum, day) => sum + day.average, 0) / secondHalf.length;

      const changePercentage = previousAverage > 0 
        ? ((currentAverage - previousAverage) / previousAverage) * 100 
        : 0;

      let trend: 'up' | 'down' | 'stable' = 'stable';
      if (Math.abs(changePercentage) > 2) { // 2% threshold for significant change
        trend = changePercentage > 0 ? 'up' : 'down';
      }

      return {
        trend,
        currentAverage,
        previousAverage,
        changePercentage,
        dailyAverages
      };
    } catch (error) {
      console.error('VoiceAnalyticsService.getAccuracyTrends error:', error);
      throw error;
    }
  }

  /**
   * Get review statistics for a specific time period
   * @param days - Number of days to analyze (default: 1 for today)
   * @returns Promise with review statistics
   */
  async getReviewStatistics(days: number = 1): Promise<{
    totalReviewed: number;
    approved: number;
    rejected: number;
    approvalRate: number;
    commonRejectionReasons: Array<{ reason: string; count: number }>;
  }> {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      // Get reviewed events for the period
      const { data: events, error } = await supabase
        .from('inventory_events')
        .select('metadata')
        .eq('created_by_voice', true)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString())
        .not('metadata->review_status', 'is', null);

      if (error) {
        throw new Error(`Failed to fetch review statistics: ${error.message}`);
      }

      if (!events || events.length === 0) {
        return {
          totalReviewed: 0,
          approved: 0,
          rejected: 0,
          approvalRate: 0,
          commonRejectionReasons: []
        };
      }

      let approved = 0;
      let rejected = 0;
      const rejectionReasons: { [key: string]: number } = {};

      events.forEach(event => {
        const reviewStatus = event.metadata?.review_status;
        if (reviewStatus === 'approved') {
          approved++;
        } else if (reviewStatus === 'rejected') {
          rejected++;
          const reason = event.metadata?.rejection_reason || 'No reason provided';
          rejectionReasons[reason] = (rejectionReasons[reason] || 0) + 1;
        }
      });

      const totalReviewed = approved + rejected;
      const approvalRate = totalReviewed > 0 ? (approved / totalReviewed) * 100 : 0;

      const commonRejectionReasons = Object.entries(rejectionReasons)
        .map(([reason, count]) => ({ reason, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5); // Top 5 reasons

      return {
        totalReviewed,
        approved,
        rejected,
        approvalRate,
        commonRejectionReasons
      };
    } catch (error) {
      console.error('VoiceAnalyticsService.getReviewStatistics error:', error);
      throw error;
    }
  }

  /**
   * Get confidence score distribution
   * @param days - Number of days to analyze (default: 30)
   * @returns Promise with confidence distribution data
   */
  async getConfidenceDistribution(days: number = 30): Promise<{
    high: number; // >= 0.9
    medium: number; // 0.7 - 0.89
    low: number; // < 0.7
    distribution: Array<{ range: string; count: number; percentage: number }>;
  }> {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const { data: events, error } = await supabase
        .from('inventory_events')
        .select('voice_confidence_score')
        .eq('created_by_voice', true)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString())
        .not('voice_confidence_score', 'is', null);

      if (error) {
        throw new Error(`Failed to fetch confidence distribution: ${error.message}`);
      }

      if (!events || events.length === 0) {
        return {
          high: 0,
          medium: 0,
          low: 0,
          distribution: []
        };
      }

      let high = 0;
      let medium = 0;
      let low = 0;

      events.forEach(event => {
        const score = event.voice_confidence_score;
        if (score >= 0.9) {
          high++;
        } else if (score >= 0.7) {
          medium++;
        } else {
          low++;
        }
      });

      const total = events.length;
      const distribution = [
        {
          range: 'High (90-100%)',
          count: high,
          percentage: (high / total) * 100
        },
        {
          range: 'Medium (70-89%)',
          count: medium,
          percentage: (medium / total) * 100
        },
        {
          range: 'Low (<70%)',
          count: low,
          percentage: (low / total) * 100
        }
      ];

      return {
        high,
        medium,
        low,
        distribution
      };
    } catch (error) {
      console.error('VoiceAnalyticsService.getConfidenceDistribution error:', error);
      throw error;
    }
  }

  /**
   * Get processing performance metrics
   * @param days - Number of days to analyze (default: 7)
   * @returns Promise with performance metrics
   */
  async getProcessingMetrics(days: number = 7): Promise<{
    averageProcessingTime: number;
    totalProcessed: number;
    successRate: number;
    peakHours: Array<{ hour: number; count: number }>;
  }> {
    try {
      const _endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      // This would require additional metadata about processing times
      // For now, return mock data structure
      return {
        averageProcessingTime: 1.2, // seconds
        totalProcessed: 0,
        successRate: 95.5, // percentage
        peakHours: []
      };
    } catch (error) {
      console.error('VoiceAnalyticsService.getProcessingMetrics error:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const voiceAnalyticsService = new VoiceAnalyticsService();