import VoiceProcessor, { VoiceCommand } from '../lib/voice-processor';
import { voiceEventService } from './VoiceEventService';
import { VoiceEventData, VoiceEventResult } from '../types/schema';
import { voiceErrorHandler, VoiceErrorType } from './VoiceErrorHandler';

/**
 * Enhanced Voice Event Processor that extends VoiceProcessor
 * with database persistence and audio confirmation capabilities
 */
export class EnhancedVoiceEventProcessor {
  private voiceProcessor: VoiceProcessor;
  private confidenceThreshold: number = 0.7;
  private maxRetries: number = 3;

  constructor() {
    this.voiceProcessor = new VoiceProcessor();
  }

  /**
   * Process voice input and automatically save to database
   * @param audioBlob - Audio blob from microphone
   * @param userId - ID of the user creating the event
   * @returns Promise<VoiceEventResult> - Processing result with confirmation
   */
  async processVoiceEvent(audioBlob: Blob, userId?: string): Promise<VoiceEventResult> {
    try {
      // Step 1: Process audio with existing VoiceProcessor
      const voiceCommand = await this.voiceProcessor.processAudioBlob(audioBlob);
      
      // Step 2: Check confidence threshold
      const requiresConfirmation = voiceCommand.confidence_score < this.confidenceThreshold;
      
      // Step 3: Convert VoiceCommand to VoiceEventData
      const eventData = this.convertVoiceCommandToEventData(voiceCommand);
      
      // Step 4: Save to database
      const savedEvent = await voiceEventService.createVoiceEvent({
        ...eventData,
        voice_confidence_score: voiceCommand.confidence_score,
        voice_confidence_breakdown: voiceCommand.confidence_breakdown || {
          product_match: voiceCommand.confidence_score,
          quantity_extraction: voiceCommand.confidence_score,
          vendor_match: voiceCommand.confidence_score,
          overall: voiceCommand.confidence_score
        },
        raw_transcript: voiceCommand.raw_transcript,
        audio_recording_url: await this.storeAudioRecording(audioBlob, savedEvent?.id)
      });

      // Step 5: Generate audio confirmation
      const audioConfirmation = await this.generateAudioConfirmation(savedEvent);

      // Step 6: Play audio confirmation
      if (audioConfirmation && !requiresConfirmation) {
        await this.playAudioConfirmation(audioConfirmation);
      }

      return {
        eventData: savedEvent,
        confidence: voiceCommand.confidence_score,
        requiresConfirmation,
        audioConfirmation
      };

    } catch (error) {
      console.error('EnhancedVoiceEventProcessor.processVoiceEvent error:', error);
      
      // Handle error with comprehensive error handler
      const voiceError = voiceErrorHandler.handleVoiceError(error, {
        audioBlob,
        userId,
        operation: 'processVoiceEvent'
      });

      // Queue for offline processing if network-related and recoverable
      if (voiceError.type === VoiceErrorType.NETWORK_ERROR || 
          voiceError.type === VoiceErrorType.DATABASE_ERROR) {
        try {
          const queueId = await voiceErrorHandler.queueOfflineEvent(audioBlob, userId);
          console.log(`Queued voice event ${queueId} due to ${voiceError.type}`);
          
          // Return a result indicating the event was queued
          return {
            eventData: {} as any, // Will be processed later
            confidence: 0,
            requiresConfirmation: false,
            audioConfirmation: 'Your voice input has been saved and will be processed when connection is restored.'
          };
        } catch (queueError) {
          console.error('Failed to queue voice event:', queueError);
        }
      }
      
      throw voiceError;
    }
  }

  /**
   * Process voice event with retry logic for failed attempts
   * @param audioBlob - Audio blob from microphone
   * @param userId - ID of the user creating the event
   * @param retryCount - Current retry attempt
   * @returns Promise<VoiceEventResult> - Processing result
   */
  async processVoiceEventWithRetry(
    audioBlob: Blob, 
    userId?: string, 
    retryCount: number = 0
  ): Promise<VoiceEventResult> {
    try {
      return await this.processVoiceEvent(audioBlob, userId);
    } catch (error) {
      // Use error handler to determine if retry is appropriate
      const voiceError = voiceErrorHandler.handleVoiceError(error, {
        audioBlob,
        userId,
        retryCount,
        operation: 'processVoiceEventWithRetry'
      });

      // Only retry if error is recoverable and we haven't exceeded max retries
      if (voiceError.recoverable && retryCount < this.maxRetries) {
        console.warn(`Voice processing failed, retrying (${retryCount + 1}/${this.maxRetries})...`);
        
        // Use exponential backoff with jitter
        const baseDelay = Math.pow(2, retryCount) * 1000;
        const jitter = Math.random() * 1000; // Add randomness to prevent thundering herd
        const delay = baseDelay + jitter;
        
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.processVoiceEventWithRetry(audioBlob, userId, retryCount + 1);
      }
      
      // If not recoverable or max retries exceeded, queue for offline if appropriate
      if (voiceError.type === VoiceErrorType.NETWORK_ERROR || 
          voiceError.type === VoiceErrorType.DATABASE_ERROR) {
        try {
          const queueId = await voiceErrorHandler.queueOfflineEvent(audioBlob, userId);
          console.log(`Max retries exceeded, queued voice event ${queueId}`);
          
          return {
            eventData: {} as any,
            confidence: 0,
            requiresConfirmation: false,
            audioConfirmation: 'Maximum retry attempts reached. Your voice input has been saved and will be processed later.'
          };
        } catch (queueError) {
          console.error('Failed to queue voice event after max retries:', queueError);
        }
      }
      
      throw voiceError;
    }
  }

  /**
   * Check if an event meets confidence threshold for automatic processing
   * @param confidence - Confidence score from voice processing
   * @returns boolean - Whether event needs manual review
   */
  checkConfidenceThreshold(confidence: number): boolean {
    return confidence >= this.confidenceThreshold;
  }

  /**
   * Set the confidence threshold for automatic processing
   * @param threshold - New threshold (0.0 to 1.0)
   */
  setConfidenceThreshold(threshold: number): void {
    if (threshold < 0 || threshold > 1) {
      throw new Error('Confidence threshold must be between 0.0 and 1.0');
    }
    this.confidenceThreshold = threshold;
  }

  /**
   * Get current confidence threshold
   * @returns number - Current threshold
   */
  getConfidenceThreshold(): number {
    return this.confidenceThreshold;
  }

  /**
   * Generate audio confirmation message for processed event
   * @param eventData - The processed voice event
   * @returns Promise<string> - Audio confirmation message
   */
  private async generateAudioConfirmation(eventData: any): Promise<string> {
    try {
      const confirmationText = this.buildConfirmationText(eventData);
      return confirmationText;
    } catch (error) {
      console.error('Error generating audio confirmation:', error);
      return 'Event successfully recorded.';
    }
  }

  /**
   * Build confirmation text based on event data
   * @param eventData - The processed voice event
   * @returns string - Confirmation message
   */
  private buildConfirmationText(eventData: any): string {
    const { event_type, product_name, quantity, unit, vendor_name, customer_name } = eventData;
    
    let confirmation = `Successfully recorded ${event_type}`;
    
    if (quantity && product_name) {
      confirmation += ` of ${quantity} ${unit || 'units'} of ${product_name}`;
    } else if (product_name) {
      confirmation += ` for ${product_name}`;
    }
    
    if (event_type === 'receiving' && vendor_name) {
      confirmation += ` from ${vendor_name}`;
    } else if (event_type === 'sale' && customer_name) {
      confirmation += ` to ${customer_name}`;
    }
    
    confirmation += '.';
    
    // Add confidence note for medium confidence events
    if (eventData.voice_confidence_score < 0.9 && eventData.voice_confidence_score >= 0.7) {
      confirmation += ' Please verify the details are correct.';
    }
    
    return confirmation;
  }

  /**
   * Play audio confirmation using Web Speech API
   * @param text - Text to speak
   * @returns Promise<void>
   */
  private async playAudioConfirmation(text: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!('speechSynthesis' in window)) {
        console.warn('Speech synthesis not supported');
        resolve();
        return;
      }

      const utterance = new SpeechSynthesisUtterance(text);
      utterance.rate = 0.9;
      utterance.pitch = 1.0;
      utterance.volume = 0.8;
      
      utterance.onend = () => resolve();
      utterance.onerror = (event) => {
        console.error('Speech synthesis error:', event);
        resolve(); // Don't fail the whole process for speech errors
      };

      window.speechSynthesis.speak(utterance);
    });
  }

  /**
   * Store audio recording for later review
   * @param audioBlob - Original audio blob
   * @param eventId - ID of the created event
   * @returns Promise<string | undefined> - URL to stored audio
   */
  private async storeAudioRecording(audioBlob: Blob, eventId?: string): Promise<string | undefined> {
    try {
      if (!eventId) return undefined;

      // For now, we'll create a blob URL. In production, this should upload to Supabase Storage
      const audioUrl = URL.createObjectURL(audioBlob);
      
      // TODO: Implement actual storage to Supabase Storage
      // const { data, error } = await supabase.storage
      //   .from('voice-recordings')
      //   .upload(`events/${eventId}/audio.webm`, audioBlob);
      
      return audioUrl;
    } catch (error) {
      console.error('Error storing audio recording:', error);
      return undefined;
    }
  }

  /**
   * Convert VoiceCommand to VoiceEventData format
   * @param voiceCommand - Command from VoiceProcessor
   * @returns VoiceEventData - Formatted event data
   */
  private convertVoiceCommandToEventData(voiceCommand: VoiceCommand): VoiceEventData {
    return {
      event_type: voiceCommand.event_type || 'receiving',
      product_name: voiceCommand.product_name || 'Unknown Product',
      quantity: voiceCommand.quantity || 0,
      unit: voiceCommand.unit || 'lbs',
      vendor_name: voiceCommand.vendor_name,
      customer_name: voiceCommand.customer_name,
      condition: voiceCommand.condition,
      temperature: voiceCommand.temperature,
      temperature_unit: voiceCommand.temperature_unit || 'fahrenheit',
      notes: voiceCommand.notes,
      occurred_at: voiceCommand.occurred_at || new Date().toISOString()
    };
  }

  /**
   * Handle voice processing errors with user-friendly messages
   * @param error - The error that occurred
   * @returns string - User-friendly error message
   */
  handleVoiceProcessingError(error: any): string {
    if (error.message?.includes('API key')) {
      return 'Voice processing is not configured. Please check your API settings.';
    }
    
    if (error.message?.includes('network') || error.message?.includes('fetch')) {
      return 'Network error. Please check your internet connection and try again.';
    }
    
    if (error.message?.includes('audio')) {
      return 'Audio processing failed. Please ensure your microphone is working and try again.';
    }
    
    if (error.message?.includes('transcription')) {
      return 'Could not understand the audio. Please speak clearly and try again.';
    }
    
    return 'Voice processing failed. Please try again or enter the information manually.';
  }

  /**
   * Get processing status from underlying VoiceProcessor
   * @returns boolean - Whether processing is in progress
   */
  isProcessing(): boolean {
    return this.voiceProcessor.getProcessingStatus();
  }

  /**
   * Process text input as if it were voice (for testing/fallback)
   * @param text - Text to process
   * @param userId - ID of the user creating the event
   * @returns Promise<VoiceEventResult> - Processing result
   */
  async processTextAsVoice(text: string, userId?: string): Promise<VoiceEventResult> {
    try {
      // Create a mock audio blob from text (for testing purposes)
      const mockAudioBlob = new Blob([text], { type: 'text/plain' });
      
      // For now, we'll simulate voice processing by directly parsing the text
      // In a real implementation, this might use text-to-speech then speech-to-text
      const mockVoiceCommand: VoiceCommand = {
        action_type: 'create_event',
        event_type: 'receiving',
        product_name: 'Test Product',
        quantity: 1,
        unit: 'lbs',
        confidence_score: 0.8,
        raw_transcript: text
      };

      const eventData = this.convertVoiceCommandToEventData(mockVoiceCommand);
      
      const savedEvent = await voiceEventService.createVoiceEvent({
        ...eventData,
        voice_confidence_score: mockVoiceCommand.confidence_score,
        voice_confidence_breakdown: {
          product_match: 0.8,
          quantity_extraction: 0.8,
          vendor_match: 0.8,
          overall: 0.8
        },
        raw_transcript: text
      });

      return {
        eventData: savedEvent,
        confidence: mockVoiceCommand.confidence_score,
        requiresConfirmation: mockVoiceCommand.confidence_score < this.confidenceThreshold,
        audioConfirmation: await this.generateAudioConfirmation(savedEvent)
      };
    } catch (error) {
      console.error('EnhancedVoiceEventProcessor.processTextAsVoice error:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const enhancedVoiceEventProcessor = new EnhancedVoiceEventProcessor();