# Services Directory

This directory contains the business logic services for the Pacific Cloud Seafoods Manager application. Services provide a clean abstraction layer between the UI components and data persistence/external APIs.

## Overview

Services implement the business logic and data access patterns for the application. They handle complex operations, data transformations, and integrate with external systems while maintaining separation of concerns from the presentation layer.

## Service Architecture

### Design Principles

1. **Single Responsibility**: Each service handles one specific domain
2. **Dependency Injection**: Services can be easily mocked and tested
3. **Error Handling**: Consistent error handling and logging
4. **Type Safety**: Full TypeScript integration with proper typing
5. **Async Operations**: Promise-based APIs for all async operations

## Current Services

### `VoiceEventService.ts`
**Purpose**: Manages voice event data lifecycle including CRUD operations, filtering, and statistics.

**Key Features**:
- Complete CRUD operations for voice events
- Advanced filtering and search capabilities
- Audit trail management
- Statistics and analytics
- Batch operations for bulk updates

**Database Integration**:
- Connects to `inventory_events` table with voice-specific columns
- Implements audit trail in `voice_event_audit` table
- Uses Row Level Security (RLS) for data access control

**Key Methods**:
```typescript
- createVoiceEvent(event: VoiceEventInput): Promise<VoiceEvent>
- getVoiceEvents(filters?: VoiceEventFilters): Promise<VoiceEvent[]>
- updateVoiceEvent(id: string, updates: Partial<VoiceEvent>): Promise<VoiceEvent>
- deleteVoiceEvent(id: string): Promise<void>
- getVoiceEventStatistics(): Promise<VoiceEventStats>
- batchUpdateConfidence(updates: ConfidenceUpdate[]): Promise<void>
```

**Integration Points**:
- Used by voice UI components for data display and editing
- Integrates with `EnhancedVoiceEventProcessor` for event processing
- Connects to `AudioStorageService` for audio file management

### `EnhancedVoiceEventProcessor.ts`
**Purpose**: Processes voice input, converts speech to text, and persists voice events to the database.

**Key Features**:
- Speech-to-text processing using OpenAI Whisper
- Confidence scoring and quality assessment
- Database persistence with audit trails
- Error handling and retry logic
- Performance optimization

**Processing Pipeline**:
1. Audio input validation
2. Speech-to-text conversion via OpenAI API
3. Confidence scoring and quality assessment
4. Database persistence with audit trail
5. Error handling and logging

**Key Methods**:
```typescript
- processVoiceEvent(audioBlob: Blob, metadata: VoiceEventMetadata): Promise<ProcessedVoiceEvent>
- validateAudioInput(audioBlob: Blob): Promise<boolean>
- transcribeAudio(audioBlob: Blob): Promise<TranscriptionResult>
- calculateConfidenceScore(transcription: string): number
- persistVoiceEvent(event: VoiceEventData): Promise<VoiceEvent>
```

**Integration Points**:
- Called by voice input components for real-time processing
- Uses `VoiceEventService` for database operations
- Integrates with `AudioStorageService` for audio file storage
- Connects to OpenAI API for speech processing

### `AudioStorageService.ts`
**Purpose**: Manages audio file storage, compression, and retrieval using Supabase Storage.

**Key Features**:
- Secure audio file upload to Supabase Storage
- Audio compression and optimization
- Signed URL generation for secure access
- File cleanup and lifecycle management
- Download and streaming capabilities

**Storage Operations**:
- Upload audio files with compression
- Generate signed URLs for secure access
- Download audio files
- Delete audio files with cleanup
- List and manage audio file metadata

**Key Methods**:
```typescript
- uploadAudio(audioBlob: Blob, eventId: string): Promise<string>
- getSignedUrl(audioPath: string): Promise<string>
- downloadAudio(audioPath: string): Promise<Blob>
- deleteAudio(audioPath: string): Promise<void>
- compressAudio(audioBlob: Blob): Promise<Blob>
- cleanupExpiredFiles(): Promise<void>
```

**Integration Points**:
- Used by voice components for audio playback
- Integrates with `VoiceEventService` for audio metadata
- Connects to Supabase Storage for file operations
- Used by `EnhancedVoiceEventProcessor` for audio persistence

## Service Integration Patterns

### Service Composition
Services can be composed together for complex operations:
```typescript
class VoiceWorkflowService {
  constructor(
    private voiceProcessor: EnhancedVoiceEventProcessor,
    private voiceEventService: VoiceEventService,
    private audioStorage: AudioStorageService
  ) {}

  async processCompleteVoiceWorkflow(audioBlob: Blob) {
    // Orchestrate multiple services
  }
}
```

### Error Handling
All services implement consistent error handling:
```typescript
try {
  const result = await service.operation();
  return result;
} catch (error) {
  logger.error('Service operation failed', { error, context });
  throw new ServiceError('Operation failed', error);
}
```

### Testing Strategy
Services are designed for testability:
- Dependency injection for easy mocking
- Pure functions where possible
- Comprehensive unit test coverage
- Integration tests with database

## Database Integration

### Connection Management
Services use the centralized Supabase client:
```typescript
import { supabase } from '../lib/supabase';
```

### Type Safety
All database operations use generated types:
```typescript
import { Database } from '../types/schema';
type VoiceEvent = Database['public']['Tables']['inventory_events']['Row'];
```

### Transaction Support
Services support database transactions for complex operations:
```typescript
const { data, error } = await supabase.rpc('transaction_function', params);
```

## Performance Considerations

### Caching Strategy
- In-memory caching for frequently accessed data
- Cache invalidation on data updates
- TTL-based cache expiration

### Batch Operations
Services support batch operations to reduce database round trips:
```typescript
await voiceEventService.batchUpdateConfidence(updates);
```

### Connection Pooling
Supabase handles connection pooling automatically, but services are designed to be connection-efficient.

## Security Implementation

### Row Level Security (RLS)
All services respect RLS policies defined in the database:
- User-based data access control
- Role-based permissions
- Audit trail for security events

### Input Validation
Services validate all inputs using Zod schemas:
```typescript
const validatedInput = VoiceEventSchema.parse(input);
```

### API Security
External API calls include proper authentication and rate limiting.

## Monitoring and Logging

### Performance Monitoring
Services include performance tracking:
- Operation timing
- Error rates
- Resource usage

### Logging
Structured logging for debugging and monitoring:
```typescript
logger.info('Voice event processed', {
  eventId,
  processingTime,
  confidenceScore
});
```

## Development Guidelines

1. **Service Interface**: Define clear interfaces for all services
2. **Error Handling**: Implement comprehensive error handling
3. **Testing**: Write unit and integration tests
4. **Documentation**: Document all public methods
5. **Performance**: Consider performance implications
6. **Security**: Validate inputs and handle sensitive data properly

## Future Enhancements

- Additional voice processing services
- Real-time synchronization services
- Advanced analytics services
- Integration services for external APIs
- Caching and optimization services