/**
 * Performance monitoring system for voice events
 * Tracks latency, throughput, and system performance metrics
 */

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  tags?: Record<string, string>;
}

interface PerformanceStats {
  averageLatency: number;
  p95Latency: number;
  p99Latency: number;
  throughput: number;
  errorRate: number;
  successRate: number;
  totalOperations: number;
}

interface DashboardData {
  voiceProcessing: PerformanceStats;
  transcription: PerformanceStats;
  aiProcessing: PerformanceStats;
  database: PerformanceStats;
  cacheHitRate: number;
  alertCount: number;
  systemHealth: number;
}

interface VoiceProcessingMetrics {
  transcriptionLatency: number;
  aiProcessingLatency: number;
  databaseLatency: number;
  totalLatency: number;
  confidenceScore: number;
  success: boolean;
}

export class VoicePerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private maxMetrics: number;
  private timers = new Map<string, number>();

  constructor(maxMetrics: number = 10000) {
    this.maxMetrics = maxMetrics;
    
    // Cleanup old metrics every 5 minutes
    setInterval(() => this.cleanup(), 5 * 60 * 1000);
  }

  /**
   * Start timing an operation
   */
  startTimer(operationId: string): void {
    this.timers.set(operationId, performance.now());
  }

  /**
   * End timing and record metric
   */
  endTimer(operationId: string, metricName: string, tags?: Record<string, string>): number {
    const startTime = this.timers.get(operationId);
    if (!startTime) {
      console.warn(`No start time found for operation: ${operationId}`);
      return 0;
    }

    const duration = performance.now() - startTime;
    this.timers.delete(operationId);

    this.recordMetric(metricName, duration, tags);
    return duration;
  }

  /**
   * Record a performance metric
   */
  recordMetric(name: string, value: number, tags?: Record<string, string>): void {
    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      tags
    };

    this.metrics.push(metric);

    // Trim if we exceed max metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }

    // Log performance issues
    this.checkPerformanceThresholds(metric);
  }

  /**
   * Record voice processing metrics
   */
  recordVoiceProcessing(metrics: VoiceProcessingMetrics): void {
    const tags = {
      success: metrics.success.toString(),
      confidence: this.getConfidenceLevel(metrics.confidenceScore)
    };

    this.recordMetric('voice.transcription.latency', metrics.transcriptionLatency, tags);
    this.recordMetric('voice.ai_processing.latency', metrics.aiProcessingLatency, tags);
    this.recordMetric('voice.database.latency', metrics.databaseLatency, tags);
    this.recordMetric('voice.total.latency', metrics.totalLatency, tags);
    this.recordMetric('voice.confidence.score', metrics.confidenceScore, tags);
    
    if (metrics.success) {
      this.recordMetric('voice.operation.success', 1, tags);
    } else {
      this.recordMetric('voice.operation.error', 1, tags);
    }
  }

  /**
   * Get confidence level category
   */
  private getConfidenceLevel(score: number): string {
    if (score >= 0.9) return 'high';
    if (score >= 0.7) return 'medium';
    return 'low';
  }

  /**
   * Check if metrics exceed performance thresholds
   */
  private checkPerformanceThresholds(metric: PerformanceMetric): void {
    const thresholds = {
      'voice.total.latency': 2000, // 2 seconds
      'voice.transcription.latency': 1000, // 1 second
      'voice.ai_processing.latency': 800, // 800ms
      'voice.database.latency': 200, // 200ms
      'database.query.latency': 100, // 100ms
      'cache.lookup.latency': 10 // 10ms
    };

    const threshold = thresholds[metric.name];
    if (threshold && metric.value > threshold) {
      console.warn(`Performance threshold exceeded for ${metric.name}: ${metric.value}ms > ${threshold}ms`, metric.tags);
      
      // Record performance alert
      this.recordMetric('performance.alert', 1, {
        metric: metric.name,
        value: metric.value.toString(),
        threshold: threshold.toString(),
        ...metric.tags
      });
    }
  }

  /**
   * Get performance statistics for a metric
   */
  getStats(metricName: string, timeWindowMs: number = 5 * 60 * 1000): PerformanceStats {
    const cutoff = Date.now() - timeWindowMs;
    const relevantMetrics = this.metrics.filter(m => 
      m.name === metricName && m.timestamp > cutoff
    );

    if (relevantMetrics.length === 0) {
      return {
        averageLatency: 0,
        p95Latency: 0,
        p99Latency: 0,
        throughput: 0,
        errorRate: 0,
        successRate: 0,
        totalOperations: 0
      };
    }

    const values = relevantMetrics.map(m => m.value).sort((a, b) => a - b);
    const successes = relevantMetrics.filter(m => m.tags?.success === 'true').length;
    const errors = relevantMetrics.filter(m => m.tags?.success === 'false').length;

    return {
      averageLatency: values.reduce((sum, val) => sum + val, 0) / values.length,
      p95Latency: this.percentile(values, 0.95),
      p99Latency: this.percentile(values, 0.99),
      throughput: relevantMetrics.length / (timeWindowMs / 1000), // operations per second
      errorRate: errors / relevantMetrics.length,
      successRate: successes / relevantMetrics.length,
      totalOperations: relevantMetrics.length
    };
  }

  /**
   * Calculate percentile
   */
  private percentile(values: number[], p: number): number {
    if (values.length === 0) return 0;
    const index = Math.ceil(values.length * p) - 1;
    return values[Math.max(0, Math.min(index, values.length - 1))];
  }

  /**
   * Get real-time performance dashboard data
   */
  getDashboardData(): DashboardData {
    const timeWindow = 5 * 60 * 1000; // 5 minutes
    
    return {
      voiceProcessing: this.getStats('voice.total.latency', timeWindow),
      transcription: this.getStats('voice.transcription.latency', timeWindow),
      aiProcessing: this.getStats('voice.ai_processing.latency', timeWindow),
      database: this.getStats('voice.database.latency', timeWindow),
      cacheHitRate: this.getCacheHitRate(timeWindow),
      alertCount: this.getAlertCount(timeWindow),
      systemHealth: this.getSystemHealth()
    };
  }

  /**
   * Get cache hit rate
   */
  private getCacheHitRate(timeWindowMs: number): number {
    const cutoff = Date.now() - timeWindowMs;
    const cacheMetrics = this.metrics.filter(m => 
      (m.name === 'cache.hit' || m.name === 'cache.miss') && m.timestamp > cutoff
    );

    if (cacheMetrics.length === 0) return 0;

    const hits = cacheMetrics.filter(m => m.name === 'cache.hit').length;
    return hits / cacheMetrics.length;
  }

  /**
   * Get alert count
   */
  private getAlertCount(timeWindowMs: number): number {
    const cutoff = Date.now() - timeWindowMs;
    return this.metrics.filter(m => 
      m.name === 'performance.alert' && m.timestamp > cutoff
    ).length;
  }

  /**
   * Get overall system health score
   */
  private getSystemHealth(): number {
    const stats = this.getStats('voice.total.latency');
    const cacheHitRate = this.getCacheHitRate(5 * 60 * 1000);
    const alertCount = this.getAlertCount(5 * 60 * 1000);

    // Health score based on multiple factors
    let health = 100;
    
    // Penalize for high latency
    if (stats.averageLatency > 2000) health -= 30;
    else if (stats.averageLatency > 1000) health -= 15;
    
    // Penalize for low success rate
    if (stats.successRate < 0.9) health -= 20;
    else if (stats.successRate < 0.95) health -= 10;
    
    // Penalize for low cache hit rate
    if (cacheHitRate < 0.5) health -= 15;
    else if (cacheHitRate < 0.7) health -= 8;
    
    // Penalize for alerts
    health -= Math.min(alertCount * 5, 25);

    return Math.max(0, health);
  }

  /**
   * Start monitoring session
   */
  startSession(sessionId: string): void {
    this.recordMetric('session.started', 1, { sessionId });
  }

  /**
   * End monitoring session
   */
  endSession(sessionId: string, duration: number): void {
    this.recordMetric('session.duration', duration, { sessionId });
    this.recordMetric('session.ended', 1, { sessionId });
  }

  /**
   * Monitor database query performance
   */
  async monitorDatabaseQuery<T>(
    queryName: string,
    queryFn: () => Promise<T>,
    tags?: Record<string, string>
  ): Promise<T> {
    const operationId = `db_${Date.now()}_${Math.random()}`;
    this.startTimer(operationId);

    try {
      const result = await queryFn();
      this.endTimer(operationId, 'database.query.latency', { query: queryName, success: 'true', ...tags });
      this.recordMetric('database.query.success', 1, { query: queryName, ...tags });
      return result;
    } catch (error) {
      this.endTimer(operationId, 'database.query.latency', { query: queryName, success: 'false', ...tags });
      this.recordMetric('database.query.error', 1, { query: queryName, ...tags });
      throw error;
    }
  }

  /**
   * Monitor cache operations
   */
  monitorCacheOperation(operation: 'hit' | 'miss', cacheType: string, key?: string): void {
    this.recordMetric(`cache.${operation}`, 1, { 
      type: cacheType, 
      key: key ? this.hashKey(key) : undefined 
    });
  }

  /**
   * Hash cache key for privacy
   */
  private hashKey(key: string): string {
    // Simple hash for logging purposes
    let hash = 0;
    for (let i = 0; i < key.length; i++) {
      const char = key.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Clean up old metrics
   */
  private cleanup(): void {
    const cutoff = Date.now() - (30 * 60 * 1000); // Keep 30 minutes of data
    const originalLength = this.metrics.length;
    
    this.metrics = this.metrics.filter(m => m.timestamp > cutoff);
    
    if (originalLength !== this.metrics.length) {
      console.log(`Performance monitor cleanup: removed ${originalLength - this.metrics.length} old metrics`);
    }
  }

  /**
   * Export metrics for external monitoring systems
   */
  exportMetrics(format: 'json' | 'prometheus' = 'json'): string {
    if (format === 'prometheus') {
      return this.exportPrometheusFormat();
    }
    
    return JSON.stringify({
      timestamp: Date.now(),
      metrics: this.metrics.slice(-1000), // Last 1000 metrics
      stats: this.getDashboardData()
    });
  }

  /**
   * Export in Prometheus format
   */
  private exportPrometheusFormat(): string {
    const stats = this.getDashboardData();
    const lines: string[] = [];

    // Voice processing metrics
    lines.push(`# HELP voice_processing_latency_seconds Voice processing latency`);
    lines.push(`# TYPE voice_processing_latency_seconds histogram`);
    lines.push(`voice_processing_latency_seconds_avg ${stats.voiceProcessing.averageLatency / 1000}`);
    lines.push(`voice_processing_latency_seconds_p95 ${stats.voiceProcessing.p95Latency / 1000}`);
    lines.push(`voice_processing_latency_seconds_p99 ${stats.voiceProcessing.p99Latency / 1000}`);

    // Success rate
    lines.push(`# HELP voice_processing_success_rate Voice processing success rate`);
    lines.push(`# TYPE voice_processing_success_rate gauge`);
    lines.push(`voice_processing_success_rate ${stats.voiceProcessing.successRate}`);

    // Cache hit rate
    lines.push(`# HELP cache_hit_rate Cache hit rate`);
    lines.push(`# TYPE cache_hit_rate gauge`);
    lines.push(`cache_hit_rate ${stats.cacheHitRate}`);

    // System health
    lines.push(`# HELP system_health_score Overall system health score`);
    lines.push(`# TYPE system_health_score gauge`);
    lines.push(`system_health_score ${stats.systemHealth}`);

    return lines.join('\n');
  }

  /**
   * Get current metrics count
   */
  getMetricsCount(): number {
    return this.metrics.length;
  }

  /**
   * Clear all metrics
   */
  clear(): void {
    this.metrics = [];
    this.timers.clear();
  }
}

// Global performance monitor instance
export const voicePerformanceMonitor = new VoicePerformanceMonitor();