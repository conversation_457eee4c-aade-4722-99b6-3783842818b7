import { useState, useEffect, useCallback } from 'react';
import { voiceError<PERSON>and<PERSON>, VoiceError, VoiceErrorType } from '../services/VoiceErrorHandler';
// Dynamic import to avoid bundling issue with VoiceErrorHandler
// import { enhancedVoiceEventProcessor } from '../services/EnhancedVoiceEventProcessor';

interface UseVoiceErrorHandlingReturn {
  processVoiceWithErrorHandling: (audioBlob: Blob, userId?: string) => Promise<any>;
  currentError: VoiceError | null;
  isRetrying: boolean;
  retryCount: number;
  clearError: () => void;
  retryLastOperation: () => Promise<void>;
  queueStatus: {
    isOnline: boolean;
    queuedEvents: number;
    failedEvents: number;
  };
}

export const useVoiceErrorHandling = (): UseVoiceErrorHandlingReturn => {
  const [currentError, setCurrentError] = useState<VoiceError | null>(null);
  const [isRetrying, setIsRetrying] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [lastOperation, setLastOperation] = useState<{
    audioBlob: Blob;
    userId?: string;
  } | null>(null);

  const [queueStatus, setQueueStatus] = useState({
    isOnline: true,
    queuedEvents: 0,
    failedEvents: 0
  });

  // Update queue status periodically
  useEffect(() => {
    const updateQueueStatus = () => {
      setQueueStatus(voiceErrorHandler.getQueueStatus());
    };

    updateQueueStatus();
    const interval = setInterval(updateQueueStatus, 3000);

    return () => clearInterval(interval);
  }, []);

  const processVoiceWithErrorHandling = useCallback(async (audioBlob: Blob, userId?: string) => {
    setCurrentError(null);
    setIsRetrying(false);
    setRetryCount(0);
    setLastOperation({ audioBlob, userId });

    try {
      const { enhancedVoiceEventProcessor } = await import('../services/EnhancedVoiceEventProcessor');
      const result = await enhancedVoiceEventProcessor.processVoiceEventWithRetry(audioBlob, userId);
      return result;
    } catch (error) {
      let voiceError: VoiceError;
      
      if (error && typeof error === 'object' && 'type' in error) {
        // Already a VoiceError
        voiceError = error as VoiceError;
      } else {
        // Handle raw error
        voiceError = voiceErrorHandler.handleVoiceError(error, {
          audioBlob,
          userId,
          operation: 'processVoiceWithErrorHandling'
        });
      }

      setCurrentError(voiceError);
      
      // Automatically retry certain types of errors
      if (voiceError.recoverable && voiceError.retryAfter) {
        setTimeout(() => {
          retryLastOperation();
        }, voiceError.retryAfter * 1000);
      }

      throw voiceError;
    }
  }, []);

  const retryLastOperation = useCallback(async () => {
    if (!lastOperation) {
      throw new Error('No operation to retry');
    }

    setIsRetrying(true);
    setRetryCount(prev => prev + 1);

    try {
      const { enhancedVoiceEventProcessor } = await import('../services/EnhancedVoiceEventProcessor');
      const result = await enhancedVoiceEventProcessor.processVoiceEventWithRetry(
        lastOperation.audioBlob,
        lastOperation.userId,
        retryCount
      );
      
      setCurrentError(null);
      setIsRetrying(false);
      return result;
    } catch (error) {
      let voiceError: VoiceError;
      
      if (error && typeof error === 'object' && 'type' in error) {
        voiceError = error as VoiceError;
      } else {
        voiceError = voiceErrorHandler.handleVoiceError(error, {
          ...lastOperation,
          retryCount,
          operation: 'retryLastOperation'
        });
      }

      setCurrentError(voiceError);
      setIsRetrying(false);
      
      throw voiceError;
    }
  }, [lastOperation, retryCount]);

  const clearError = useCallback(() => {
    setCurrentError(null);
    setIsRetrying(false);
    setRetryCount(0);
  }, []);

  return {
    processVoiceWithErrorHandling,
    currentError,
    isRetrying,
    retryCount,
    clearError,
    retryLastOperation,
    queueStatus
  };
};

export default useVoiceErrorHandling;