import { useState, useCallback, useRef } from 'react';
import { supabase } from '../lib/supabase';
import { createReceivingWithLot } from '../lib/api';

interface VoiceCommandOptions {
  onEventCreated?: (eventData: any) => void;
  onNavigate?: (view: string, filters?: Record<string, any>) => void;
  onFeedback?: (message: string, type: 'success' | 'error' | 'info') => void;
}

interface ExtractedEventData {
  action_type: 'create_event' | 'query_inventory' | 'navigate' | 'unknown';
  event_type?: 'receiving' | 'disposal' | 'sale' | 'physical_count';
  product_name?: string;
  quantity?: number;
  unit?: string;
  vendor_name?: string;
  customer_name?: string;
  price?: number;
  condition?: string;
  notes?: string;
  confidence_score?: number;
}

interface InventoryQueryResult {
  product_name: string;
  total_quantity: number;
  unit: string;
  last_updated: string;
}

/**
 * Hook for processing voice commands into structured actions
 * Handles event creation, inventory queries, and navigation commands
 */
export function useVoiceCommands({ onEventCreated, onNavigate, onFeedback }: VoiceCommandOptions) {
  const [isCommandProcessing, setIsCommandProcessing] = useState(false);
  const [lastResult, setLastResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  
  // Cache for quick lookups
  const productsCache = useRef<Map<string, string>>(new Map());
  const vendorsCache = useRef<Map<string, string>>(new Map());
  const customersCache = useRef<Map<string, string>>(new Map());

  // Load and cache reference data
  const loadReferenceData = useCallback(async () => {
    try {
      const [productsRes, vendorsRes, customersRes] = await Promise.all([
        supabase.from('Products').select('id, name'),
        supabase.from('vendors').select('id, name'),
        supabase.from('customers').select('id, name')
      ]);

      if (productsRes.data) {
        productsCache.current.clear();
        // Remove duplicates by name, keeping the first occurrence
        const uniqueProducts = productsRes.data.filter((product, index, array) => 
          array.findIndex(p => p.name === product.name) === index
        );
        uniqueProducts.forEach(p => {
          productsCache.current.set(p.name.toLowerCase(), p.id);
        });
      }

      if (vendorsRes.data) {
        vendorsCache.current.clear();
        // Remove duplicates by name, keeping the first occurrence
        const uniqueVendors = vendorsRes.data.filter((vendor, index, array) => 
          array.findIndex(v => v.name === vendor.name) === index
        );
        uniqueVendors.forEach(v => {
          vendorsCache.current.set(v.name.toLowerCase(), v.id);
        });
      }

      if (customersRes.data) {
        customersCache.current.clear();
        // Remove duplicates by name, keeping the first occurrence
        const uniqueCustomers = customersRes.data.filter((customer, index, array) => 
          array.findIndex(c => c.name === customer.name) === index
        );
        uniqueCustomers.forEach(c => {
          customersCache.current.set(c.name.toLowerCase(), c.id);
        });
      }
    } catch (error) {
      console.error('Failed to load reference data:', error);
    }
  }, []);

  // Extract structured data from voice command
  const extractCommandData = useCallback(async (command: string): Promise<ExtractedEventData> => {
    try {
      onFeedback?.('Analyzing seafood command...', 'info');
      
      // Try AI processing first
      const aiResult = await processWithAI(command);
      if (aiResult && aiResult.confidence_score > 0.7) {
        return aiResult;
      }

      // Fallback to pattern matching
      return processWithPatterns(command);
    } catch (error) {
      console.error('Command extraction failed:', error);
      return processWithPatterns(command);
    }
  }, [onFeedback]);

  // AI-powered command processing
  const processWithAI = useCallback(async (command: string): Promise<ExtractedEventData | null> => {
    try {
      const response = await fetch('/api/voice-command-extract', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ command })
      });

      if (!response.ok) throw new Error('AI processing failed');
      
      const result = await response.json();
      return result;
    } catch (error) {
      console.error('AI command processing failed:', error);
      return null;
    }
  }, []);

  // Pattern-based command processing (fallback)
  const processWithPatterns = useCallback((command: string): ExtractedEventData => {
    const lowerCommand = command.toLowerCase().trim();
    
    // Event creation patterns
    if (lowerCommand.includes('add') || lowerCommand.includes('receive') || lowerCommand.includes('receiving')) {
      return extractReceivingEvent(lowerCommand);
    }
    
    if (lowerCommand.includes('dispose') || lowerCommand.includes('disposal') || lowerCommand.includes('discard')) {
      return extractDisposalEvent(lowerCommand);
    }
    
    if (lowerCommand.includes('sale') || lowerCommand.includes('sold') || lowerCommand.includes('sell')) {
      return extractSaleEvent(lowerCommand);
    }
    
    if (lowerCommand.includes('count') || lowerCommand.includes('physical')) {
      return extractPhysicalCountEvent(lowerCommand);
    }
    
    // Query patterns
    if (lowerCommand.includes('how much') || lowerCommand.includes('inventory') || lowerCommand.includes('stock')) {
      return extractInventoryQuery(lowerCommand);
    }
    
    // Navigation patterns
    if (lowerCommand.includes('show') || lowerCommand.includes('view') || lowerCommand.includes('display')) {
      return extractNavigationCommand(lowerCommand);
    }
    
    return { action_type: 'unknown', confidence_score: 0.3 };
  }, []);

  // Extract receiving event data
  const extractReceivingEvent = useCallback((command: string): ExtractedEventData => {
    const data: ExtractedEventData = {
      action_type: 'create_event',
      event_type: 'receiving',
      confidence_score: 0.6
    };

    // Extract quantity and unit
    const quantityMatch = command.match(/(\d+(?:\.\d+)?)\s*(pounds?|lbs?|kg|kilograms?|cases?|units?)/i);
    if (quantityMatch) {
      data.quantity = parseFloat(quantityMatch[1]);
      data.unit = normalizeUnit(quantityMatch[2]);
      data.confidence_score += 0.2;
    }

    // Extract product name (seafood terms)
    const seafoodTerms = [
      'cod', 'salmon', 'halibut', 'tuna', 'crab', 'dungeness', 'king crab', 'snow crab',
      'oysters', 'mussels', 'clams', 'scallops', 'shrimp', 'prawns', 'lobster'
    ];
    
    for (const term of seafoodTerms) {
      if (command.includes(term)) {
        data.product_name = capitalizeSeafood(term);
        data.confidence_score += 0.15;
        break;
      }
    }

    // Extract vendor
    const vendorMatch = command.match(/from\s+([a-zA-Z\s&.-]+?)(?:\s+(?:at|for|received|delivered|$))/i);
    if (vendorMatch) {
      data.vendor_name = vendorMatch[1].trim();
      data.confidence_score += 0.1;
    }

    // Extract condition
    const conditionTerms = ['excellent', 'good', 'fair', 'poor', 'damaged'];
    for (const condition of conditionTerms) {
      if (command.includes(condition)) {
        data.condition = capitalizeFirst(condition);
        data.confidence_score += 0.05;
        break;
      }
    }

    return data;
  }, []);

  // Extract disposal event data
  const extractDisposalEvent = useCallback((command: string): ExtractedEventData => {
    const data: ExtractedEventData = {
      action_type: 'create_event',
      event_type: 'disposal',
      confidence_score: 0.6
    };

    // Extract quantity and unit
    const quantityMatch = command.match(/(\d+(?:\.\d+)?)\s*(pounds?|lbs?|kg|kilograms?|cases?|units?)/i);
    if (quantityMatch) {
      data.quantity = parseFloat(quantityMatch[1]);
      data.unit = normalizeUnit(quantityMatch[2]);
      data.confidence_score += 0.2;
    }

    // Extract product name
    const seafoodTerms = [
      'cod', 'salmon', 'halibut', 'tuna', 'crab', 'dungeness', 'king crab', 'snow crab',
      'oysters', 'mussels', 'clams', 'scallops', 'shrimp', 'prawns', 'lobster'
    ];
    
    for (const term of seafoodTerms) {
      if (command.includes(term)) {
        data.product_name = capitalizeSeafood(term);
        data.confidence_score += 0.15;
        break;
      }
    }

    // Extract reason
    const reasonTerms = ['expired', 'spoiled', 'damaged', 'contaminated', 'recalled'];
    for (const reason of reasonTerms) {
      if (command.includes(reason)) {
        data.notes = `Disposal reason: ${reason}`;
        data.confidence_score += 0.05;
        break;
      }
    }

    return data;
  }, []);

  // Extract sale event data
  const extractSaleEvent = useCallback((command: string): ExtractedEventData => {
    const data: ExtractedEventData = {
      action_type: 'create_event',
      event_type: 'sale',
      confidence_score: 0.6
    };

    // Extract quantity and unit
    const quantityMatch = command.match(/(\d+(?:\.\d+)?)\s*(pounds?|lbs?|kg|kilograms?|cases?|units?)/i);
    if (quantityMatch) {
      data.quantity = parseFloat(quantityMatch[1]);
      data.unit = normalizeUnit(quantityMatch[2]);
      data.confidence_score += 0.2;
    }

    // Extract product name
    const seafoodTerms = [
      'cod', 'salmon', 'halibut', 'tuna', 'crab', 'dungeness', 'king crab', 'snow crab',
      'oysters', 'mussels', 'clams', 'scallops', 'shrimp', 'prawns', 'lobster'
    ];
    
    for (const term of seafoodTerms) {
      if (command.includes(term)) {
        data.product_name = capitalizeSeafood(term);
        data.confidence_score += 0.15;
        break;
      }
    }

    // Extract customer
    const customerMatch = command.match(/to\s+([a-zA-Z\s&.-]+?)(?:\s+(?:at|for|$))/i);
    if (customerMatch) {
      data.customer_name = customerMatch[1].trim();
      data.confidence_score += 0.1;
    }

    // Extract price
    const priceMatch = command.match(/\$?(\d+(?:\.\d{2})?)\s*(?:per\s+(?:pound|lb|unit|case))?/i);
    if (priceMatch) {
      data.price = parseFloat(priceMatch[1]);
      data.confidence_score += 0.1;
    }

    return data;
  }, []);

  // Extract physical count event data
  const extractPhysicalCountEvent = useCallback((command: string): ExtractedEventData => {
    const data: ExtractedEventData = {
      action_type: 'create_event',
      event_type: 'physical_count',
      confidence_score: 0.6
    };

    // Extract quantity and unit
    const quantityMatch = command.match(/(\d+(?:\.\d+)?)\s*(pounds?|lbs?|kg|kilograms?|cases?|units?)/i);
    if (quantityMatch) {
      data.quantity = parseFloat(quantityMatch[1]);
      data.unit = normalizeUnit(quantityMatch[2]);
      data.confidence_score += 0.2;
    }

    // Extract product name
    const seafoodTerms = [
      'cod', 'salmon', 'halibut', 'tuna', 'crab', 'dungeness', 'king crab', 'snow crab',
      'oysters', 'mussels', 'clams', 'scallops', 'shrimp', 'prawns', 'lobster'
    ];
    
    for (const term of seafoodTerms) {
      if (command.includes(term)) {
        data.product_name = capitalizeSeafood(term);
        data.confidence_score += 0.15;
        break;
      }
    }

    return data;
  }, []);

  // Extract inventory query
  const extractInventoryQuery = useCallback((command: string): ExtractedEventData => {
    const data: ExtractedEventData = {
      action_type: 'query_inventory',
      confidence_score: 0.7
    };

    // Extract product name for query
    const seafoodTerms = [
      'cod', 'salmon', 'halibut', 'tuna', 'crab', 'dungeness', 'king crab', 'snow crab',
      'oysters', 'mussels', 'clams', 'scallops', 'shrimp', 'prawns', 'lobster'
    ];
    
    for (const term of seafoodTerms) {
      if (command.includes(term)) {
        data.product_name = capitalizeSeafood(term);
        data.confidence_score += 0.2;
        break;
      }
    }

    return data;
  }, []);

  // Extract navigation command
  const extractNavigationCommand = useCallback((command: string): ExtractedEventData => {
    const data: ExtractedEventData = {
      action_type: 'navigate',
      confidence_score: 0.7
    };

    if (command.includes('events') || command.includes('recent')) {
      data.notes = 'Events';
      data.confidence_score += 0.2;
    } else if (command.includes('inventory') || command.includes('stock')) {
      data.notes = 'Inventory';
      data.confidence_score += 0.2;
    } else if (command.includes('dashboard')) {
      data.notes = 'Dashboard';
      data.confidence_score += 0.2;
    }

    return data;
  }, []);

  // Utility functions
  const normalizeUnit = (unit: string): string => {
    const lowerUnit = unit.toLowerCase();
    if (lowerUnit.includes('pound') || lowerUnit.includes('lb')) return 'lbs';
    if (lowerUnit.includes('kg') || lowerUnit.includes('kilogram')) return 'kg';
    if (lowerUnit.includes('case')) return 'cases';
    return 'units';
  };

  const capitalizeSeafood = (term: string): string => {
    const specialCases: Record<string, string> = {
      'dungeness': 'Dungeness Crab',
      'king crab': 'King Crab',
      'snow crab': 'Snow Crab'
    };
    
    return specialCases[term.toLowerCase()] || capitalizeFirst(term);
  };

  const capitalizeFirst = (str: string): string => {
    return str.charAt(0).toUpperCase() + str.slice(1);
  };

  // Main command processor
  const processCommand = useCallback(async (command: string) => {
    if (!command.trim()) return;

    setIsCommandProcessing(true);
    setError(null);

    try {
      // Load reference data if not cached
      if (productsCache.current.size === 0) {
        await loadReferenceData();
      }

      // Extract command data
      const commandData = await extractCommandData(command);
      setLastResult(commandData);

      // Execute the command
      switch (commandData.action_type) {
        case 'create_event':
          await executeEventCreation(commandData);
          break;
        case 'query_inventory':
          await executeInventoryQuery(commandData);
          break;
        case 'navigate':
          await executeNavigation(commandData);
          break;
        default:
          onFeedback?.('Command not recognized. Please try again.', 'error');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Command processing failed';
      setError(errorMessage);
      onFeedback?.(errorMessage, 'error');
    } finally {
      setIsCommandProcessing(false);
    }
  }, [extractCommandData, loadReferenceData, onFeedback]);

  // Execute event creation
  const executeEventCreation = useCallback(async (data: ExtractedEventData) => {
    if (!data.product_name || !data.quantity || !data.event_type) {
      throw new Error('Missing required information for event creation');
    }

    // Find product ID
    const productId = productsCache.current.get(data.product_name.toLowerCase());
    if (!productId) {
      throw new Error(`Product "${data.product_name}" not found in inventory`);
    }

    // Prepare event data
    const eventData: any = {
      event_type: data.event_type,
      product_id: productId,
      quantity: data.quantity,
      unit: data.unit || 'lbs',
      notes: data.notes || `Created via voice command: "${data.product_name}"`,
      metadata: {
        source: 'voice-assistant',
        confidence_score: data.confidence_score,
        product_name: data.product_name
      }
    };

    // Add event-specific data
    if (data.event_type === 'receiving') {
      if (data.vendor_name) {
        const vendorId = vendorsCache.current.get(data.vendor_name.toLowerCase());
        if (vendorId) {
          eventData.metadata.vendor_id = vendorId;
          eventData.metadata.vendor_name = data.vendor_name;
        }
      }
      if (data.condition) {
        eventData.metadata.condition = data.condition;
      }
    }

    if (data.event_type === 'sale') {
      if (data.customer_name) {
        const customerId = customersCache.current.get(data.customer_name.toLowerCase());
        if (customerId) {
          eventData.metadata.customer_id = customerId;
          eventData.metadata.customer_name = data.customer_name;
        }
      }
      if (data.price) {
        eventData.unit_price = data.price;
        eventData.total_amount = data.quantity * data.price;
      }
    }

    // Insert event
    const { error } = await supabase.from('inventory_events').insert(eventData);
    if (error) throw error;

    onEventCreated?.(eventData);
    onFeedback?.(
      `Successfully created ${data.event_type} event for ${data.quantity} ${data.unit} of ${data.product_name}`,
      'success'
    );
  }, [onEventCreated, onFeedback]);

  // Execute inventory query
  const executeInventoryQuery = useCallback(async (data: ExtractedEventData) => {
    let query = supabase
      .from('inventory_events')
      .select(`
        product_id,
        quantity,
        event_type,
        created_at,
        Products(name)
      `)
      .order('created_at', { ascending: false });

    if (data.product_name) {
      const productId = productsCache.current.get(data.product_name.toLowerCase());
      if (productId) {
        query = query.eq('product_id', productId);
      }
    }

    const { data: events, error } = await query.limit(100);
    if (error) throw error;

    // Calculate current inventory
    const inventory = new Map<string, { quantity: number; unit: string; name: string }>();
    
    events?.forEach(event => {
      const productName = (event.Products as any)?.name || 'Unknown';
      const current = inventory.get(event.product_id) || { quantity: 0, unit: 'lbs', name: productName };
      
      if (event.event_type === 'receiving' || event.event_type === 'physical_count') {
        current.quantity += event.quantity;
      } else if (event.event_type === 'disposal' || event.event_type === 'sale') {
        current.quantity -= event.quantity;
      }
      
      inventory.set(event.product_id, current);
    });

    if (data.product_name) {
      const productId = productsCache.current.get(data.product_name.toLowerCase());
      const productInventory = productId ? inventory.get(productId) : null;
      
      if (productInventory) {
        onFeedback?.(
          `Current inventory: ${productInventory.quantity} ${productInventory.unit} of ${productInventory.name}`,
          'success'
        );
      } else {
        onFeedback?.(
          `No inventory found for ${data.product_name}`,
          'info'
        );
      }
    } else {
      const totalProducts = inventory.size;
      onFeedback?.(
        `Current inventory contains ${totalProducts} different products`,
        'success'
      );
    }
  }, [onFeedback]);

  // Execute navigation
  const executeNavigation = useCallback(async (data: ExtractedEventData) => {
    const view = data.notes || 'Dashboard';
    
    onNavigate?.(view);
    onFeedback?.(
      `Navigating to ${view}`,
      'success'
    );
  }, [onNavigate, onFeedback]);

  return {
    processCommand,
    isCommandProcessing,
    lastResult,
    error
  };
}