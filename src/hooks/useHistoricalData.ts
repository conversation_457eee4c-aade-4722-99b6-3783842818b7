/**
 * useHistoricalData Hook
 * 
 * Custom hook for managing historical temperature and humidity data
 * Builds on existing useTemperatureDashboard patterns with optimizations for:
 * - Large date range handling
 * - Chart-specific data aggregation  
 * - Performance optimization with caching
 * - Statistics calculation
 * - Export functionality
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { tempStickService } from '@/lib/tempstick-service';
import type {
  HistoricalDataRequest,
  HistoricalDataResponse,
  HistoricalDataPoint,
  SensorStatistics,
  DateRange,
  DateRangePreset
} from '@/types/historical-dashboard';
import { DATE_RANGE_PRESETS } from '@/types/historical-dashboard';

// Types specific to this hook
interface DashboardFilters {
  dateRange: DateRange;
  selectedSensors: string[];
  chartType: 'temperature' | 'humidity' | 'both';
  showOfflineSensors: boolean;
  groupByLocation: boolean;
}

interface HistoricalDataError {
  code: string;
  message: string;
  details?: any;
  retryable?: boolean;
  timestamp?: Date;
}

type AggregationInterval = 'minute' | 'hour' | 'day' | 'week';

interface AggregatedDataPoint {
  timestamp: Date;
  sensorId: string;
  avgTemperature: number;
  minTemperature: number;
  maxTemperature: number;
  avgHumidity: number;
  minHumidity: number;
  maxHumidity: number;
  dataPoints: number;
}

interface UseHistoricalDataOptions {
  autoRefreshInterval?: number;
  maxDataPoints?: number;
  enableAggregation?: boolean;
  aggregationThreshold?: number;
  cacheResults?: boolean;
}

interface HistoricalDataState {
  data: HistoricalDataPoint[];
  aggregatedData: AggregatedDataPoint[];
  statistics: SensorStatistics[];
  isLoading: boolean;
  error: HistoricalDataError | null;
  lastUpdate: Date | null;
  totalPoints: number;
  hasMore: boolean;
  currentRequest: HistoricalDataRequest | null;
}

export const useHistoricalData = (options: UseHistoricalDataOptions = {}) => {
  const {
    autoRefreshInterval = 300000, // 5 minutes default
    maxDataPoints = 10000,
    enableAggregation = true,
    aggregationThreshold = 1000,
    cacheResults = true
  } = options;

  // State management
  const [state, setState] = useState<HistoricalDataState>({
    data: [],
    aggregatedData: [],
    statistics: [],
    isLoading: false,
    error: null,
    lastUpdate: null,
    totalPoints: 0,
    hasMore: false,
    currentRequest: null
  });

  // Filters and preferences
  const [filters, setFilters] = useState<DashboardFilters>({
    dateRange: DATE_RANGE_PRESETS[1].value, // Default to last 24 hours
    selectedSensors: [],
    chartType: 'both',
    showOfflineSensors: true,
    groupByLocation: false
  });

  // Simple cache for frequently accessed data
  const [dataCache] = useState<Map<string, { data: HistoricalDataResponse; timestamp: number }>>(new Map());
  const cacheExpiryTime = 5 * 60 * 1000; // 5 minutes

  /**
   * Generate cache key from request parameters
   */
  const getCacheKey = useCallback((request: HistoricalDataRequest): string => {
    return `${request.sensorIds.sort().join(',')}-${request.startDate.getTime()}-${request.endDate.getTime()}-${request.interval || 'raw'}`;
  }, []);

  /**
   * Check if we should use aggregation based on data size and time range
   */
  const shouldUseAggregation = useMemo(() => {
    if (!enableAggregation) return false;
    
    const timeRange = filters.dateRange.end.getTime() - filters.dateRange.start.getTime();
    const days = timeRange / (1000 * 60 * 60 * 24);
    
    // Use aggregation for ranges > 7 days or when specifically requested
    return days > 7 || state.totalPoints > aggregationThreshold;
  }, [enableAggregation, filters.dateRange, state.totalPoints, aggregationThreshold]);

  /**
   * Determine optimal aggregation interval based on date range
   */
  const getOptimalAggregationInterval = useCallback((dateRange: DateRange): AggregationInterval => {
    const timeRange = dateRange.end.getTime() - dateRange.start.getTime();
    const days = timeRange / (1000 * 60 * 60 * 24);
    
    if (days <= 1) return 'minute';
    if (days <= 7) return 'hour';
    if (days <= 30) return 'day';
    return 'week';
  }, []);

  /**
   * Main data fetching function
   */
  const fetchHistoricalData = useCallback(async (request: HistoricalDataRequest) => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null, currentRequest: request }));
      
      console.log(`📊 Fetching historical data for ${request.sensorIds.length} sensors from ${request.startDate.toLocaleDateString()} to ${request.endDate.toLocaleDateString()}`);

      // Check cache first if enabled
      const cacheKey = getCacheKey(request);
      if (cacheResults && dataCache.has(cacheKey)) {
        const cached = dataCache.get(cacheKey)!;
        if (Date.now() - cached.timestamp < cacheExpiryTime) {
          console.log('✅ Using cached historical data');
          
          setState(prev => ({
            ...prev,
            data: cached.data.data,
            statistics: cached.data.statistics,
            totalPoints: cached.data.totalPoints,
            hasMore: cached.data.hasMore,
            isLoading: false,
            lastUpdate: new Date(cached.timestamp)
          }));
          return cached.data;
        }
      }

      // Fetch fresh data from API
      const response = await tempStickService.getHistoricalData(request);
      
      // Cache the results
      if (cacheResults) {
        dataCache.set(cacheKey, { data: response, timestamp: Date.now() });
        
        // Clean old cache entries
        for (const [key, value] of dataCache.entries()) {
          if (Date.now() - value.timestamp > cacheExpiryTime) {
            dataCache.delete(key);
          }
        }
      }

      // Update state with fresh data
      setState(prev => ({
        ...prev,
        data: response.data,
        statistics: response.statistics,
        totalPoints: response.totalPoints,
        hasMore: response.hasMore,
        isLoading: false,
        lastUpdate: new Date(),
        error: null
      }));

      // Fetch aggregated data if needed
      if (shouldUseAggregation && response.totalPoints > aggregationThreshold) {
        console.log('📈 Fetching aggregated data for better performance...');
        const interval = getOptimalAggregationInterval(request.dateRange || { start: request.startDate, end: request.endDate });
        const aggregatedData = await tempStickService.getAggregatedHistoricalData(request, interval);
        
        setState(prev => ({
          ...prev,
          aggregatedData
        }));
      }

      console.log(`✅ Retrieved ${response.data.length} historical data points with ${response.statistics.length} sensor statistics`);
      return response;

    } catch (error) {
      const historicalError: HistoricalDataError = {
        code: 'FETCH_ERROR',
        message: error instanceof Error ? error.message : 'Failed to fetch historical data',
        details: { request },
        retryable: true,
        timestamp: new Date()
      };

      setState(prev => ({
        ...prev,
        isLoading: false,
        error: historicalError
      }));

      console.error('Failed to fetch historical data:', error);
      throw historicalError;
    }
  }, [getCacheKey, cacheResults, dataCache, cacheExpiryTime, shouldUseAggregation, aggregationThreshold, getOptimalAggregationInterval]);

  /**
   * Refresh data with current filters
   */
  const refreshData = useCallback(async () => {
    if (filters.selectedSensors.length === 0) {
      console.log('⚠️ No sensors selected, skipping historical data fetch');
      return;
    }

    const request: HistoricalDataRequest = {
      sensorIds: filters.selectedSensors,
      startDate: filters.dateRange.start,
      endDate: filters.dateRange.end,
      includeStatistics: true
    };

    return fetchHistoricalData(request);
  }, [filters, fetchHistoricalData]);

  /**
   * Update date range filter
   */
  const setDateRange = useCallback((dateRange: DateRange) => {
    setFilters(prev => ({ ...prev, dateRange }));
  }, []);

  /**
   * Update date range using preset
   */
  const setDateRangePreset = useCallback((presetKey: string) => {
    const preset = DATE_RANGE_PRESETS.find(p => p.key === presetKey);
    if (preset) {
      setDateRange(preset.value);
    }
  }, [setDateRange]);

  /**
   * Update selected sensors
   */
  const setSelectedSensors = useCallback((sensorIds: string[]) => {
    setFilters(prev => ({ ...prev, selectedSensors: sensorIds }));
  }, []);

  /**
   * Update chart type filter
   */
  const setChartType = useCallback((chartType: DashboardFilters['chartType']) => {
    setFilters(prev => ({ ...prev, chartType }));
  }, []);

  /**
   * Get filtered data based on current chart type
   */
  const getFilteredData = useCallback((): HistoricalDataPoint[] => {
    return state.data.filter(point => {
      if (filters.chartType === 'temperature') return point.temperature !== null;
      if (filters.chartType === 'humidity') return point.humidity !== null;
      return true; // 'both' - show all data
    });
  }, [state.data, filters.chartType]);

  /**
   * Clear cache manually
   */
  const clearCache = useCallback(() => {
    dataCache.clear();
    console.log('🗑️ Historical data cache cleared');
  }, [dataCache]);

  /**
   * Export data in CSV format
   */
  const exportData = useCallback(async (format: 'csv' = 'csv') => {
    try {
      const exportData = getFilteredData().map(point => ({
        timestamp: point.timestamp.toISOString(),
        sensorName: point.sensorName,
        temperature: point.temperature,
        humidity: point.humidity,
        dewpoint: point.dewpoint,
        heatIndex: point.heatIndex,
        batteryLevel: point.batteryLevel,
        signalStrength: point.signalStrength
      }));

      if (format === 'csv') {
        const headers = Object.keys(exportData[0] || {}).join(',');
        const rows = exportData.map(row => Object.values(row).join(','));
        const csvContent = [headers, ...rows].join('\n');
        
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `historical-temperature-data-${new Date().toISOString().split('T')[0]}.csv`;
        link.click();
      }

      return exportData;
    } catch (error) {
      throw new Error(`Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, [getFilteredData]);

  // Auto-refresh effect
  useEffect(() => {
    if (autoRefreshInterval > 0) {
      const interval = setInterval(refreshData, autoRefreshInterval);
      return () => clearInterval(interval);
    }
  }, [refreshData, autoRefreshInterval]);

  // Trigger data fetch when filters change
  useEffect(() => {
    if (filters.selectedSensors.length > 0) {
      refreshData();
    }
  }, [filters.dateRange, filters.selectedSensors]); // Intentionally exclude refreshData to prevent infinite loops

  return {
    // Data
    data: getFilteredData(),
    aggregatedData: state.aggregatedData,
    statistics: state.statistics,
    
    // State
    isLoading: state.isLoading,
    error: state.error,
    lastUpdate: state.lastUpdate,
    totalPoints: state.totalPoints,
    hasMore: state.hasMore,
    
    // Filters
    filters,
    setDateRange,
    setDateRangePreset,
    setSelectedSensors,
    setChartType,
    
    // Actions
    refreshData,
    clearCache,
    exportData,
    
    // Utility
    shouldUseAggregation,
    dateRangePresets: DATE_RANGE_PRESETS
  };
};

export default useHistoricalData;