import { useState, useEffect, useRef, useCallback } from 'react';

interface VoiceRealtimeProcessorOptions {
  onTranscript: (text: string, isFinal: boolean) => void;
  onStatusChange: (status: string) => void;
  enableWebRTC?: boolean;
  fallbackToWhisper?: boolean;
}

interface VoiceRealtimeProcessorState {
  isConnected: boolean;
  isListening: boolean;
  isProcessing: boolean;
  error: string | null;
  latency: number | null;
}

/**
 * Hook for realtime voice processing with OpenAI Realtime API and WebRTC fallback
 * Provides sub-300ms latency for natural conversation flow
 */
export function useVoiceRealtimeProcessor({
  onTranscript,
  onStatusChange,
  enableWebRTC = true,
  fallbackToWhisper = true
}: VoiceRealtimeProcessorOptions) {
  const [state, setState] = useState<VoiceRealtimeProcessorState>({
    isConnected: false,
    isListening: false,
    isProcessing: false,
    error: null,
    latency: null
  });

  const wsRef = useRef<WebSocket | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const mediaStreamRef = useRef<MediaStream | null>(null);
  const processorRef = useRef<ScriptProcessorNode | null>(null);
  const realtimeSessionRef = useRef<any>(null);
  const latencyStartRef = useRef<number>(0);
  const isInitializingRef = useRef(false);

  // Cleanup function
  const cleanup = useCallback(() => {
    if (mediaStreamRef.current) {
      mediaStreamRef.current.getTracks().forEach(track => track.stop());
      mediaStreamRef.current = null;
    }
    
    if (processorRef.current) {
      processorRef.current.disconnect();
      processorRef.current = null;
    }
    
    if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }
    
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    
    setState(prev => ({ ...prev, isListening: false, isConnected: false }));
  }, []);

  // Initialize OpenAI Realtime API connection
  const initializeRealtimeAPI = useCallback(async () => {
    if (isInitializingRef.current) return false;
    isInitializingRef.current = true;

    try {
      onStatusChange('connecting');
      
      // Check if OpenAI Realtime API is available
      const realtimeSupported = await checkRealtimeAPISupport();
      
      if (!realtimeSupported && !fallbackToWhisper) {
        throw new Error('Realtime API not available and fallback disabled');
      }

      if (realtimeSupported) {
        return await initializeRealtimeConnection();
      } else {
        return await initializeWhisperFallback();
      }
    } catch (error) {
      console.error('Failed to initialize voice processing:', error);
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Initialization failed',
        isConnected: false 
      }));
      onStatusChange('error');
      return false;
    } finally {
      isInitializingRef.current = false;
    }
  }, [onStatusChange, fallbackToWhisper]);

  // Check OpenAI Realtime API availability
  const checkRealtimeAPISupport = useCallback(async (): Promise<boolean> => {
    try {
      const response = await fetch('/api/voice-realtime-check', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      
      if (!response.ok) return false;
      
      const data = await response.json();
      return data.supported === true;
    } catch {
      return false;
    }
  }, []);

  // Initialize OpenAI Realtime API with WebRTC
  const initializeRealtimeConnection = useCallback(async (): Promise<boolean> => {
    try {
      // Request microphone access
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 24000
        } 
      });
      
      mediaStreamRef.current = stream;

      // Create WebSocket connection to OpenAI Realtime API
      const ws = new WebSocket('wss://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview-2024-10-01');
      wsRef.current = ws;

      return new Promise((resolve, reject) => {
        ws.onopen = () => {
          // Send session configuration
          ws.send(JSON.stringify({
            type: 'session.update',
            session: {
              modalities: ['text', 'audio'],
              instructions: `You are a seafood inventory assistant. Process voice commands for:
                - Receiving events: "Add receiving 50 pounds cod from Ocean Fresh"
                - Disposal events: "Dispose 10 pounds expired salmon"
                - Sales events: "Sale 30 pounds halibut to Restaurant ABC"
                - Inventory queries: "How much cod do we have?"
                
                Respond with structured JSON containing:
                - action_type: "create_event" | "query_inventory" | "navigate"
                - event_data: extracted inventory information
                - confirmation: brief success message
                
                Be concise and focused on seafood operations.`,
              voice: 'alloy',
              input_audio_format: 'pcm16',
              output_audio_format: 'pcm16',
              input_audio_transcription: {
                model: 'whisper-1'
              }
            }
          }));

          setState(prev => ({ ...prev, isConnected: true, error: null }));
          onStatusChange('connected');
          
          // Setup audio processing
          setupRealtimeAudioProcessing(stream, ws);
          resolve(true);
        };

        ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          setState(prev => ({ ...prev, error: 'Connection failed', isConnected: false }));
          onStatusChange('error');
          reject(error);
        };

        ws.onmessage = (event) => {
          handleRealtimeMessage(JSON.parse(event.data));
        };

        ws.onclose = () => {
          setState(prev => ({ ...prev, isConnected: false, isListening: false }));
          onStatusChange('disconnected');
        };

        // Timeout after 10 seconds
        setTimeout(() => {
          if (ws.readyState !== WebSocket.OPEN) {
            ws.close();
            reject(new Error('Connection timeout'));
          }
        }, 10000);
      });
    } catch (error) {
      console.error('Realtime connection failed:', error);
      return false;
    }
  }, [onStatusChange]);

  // Setup WebRTC audio processing for realtime streaming
  const setupRealtimeAudioProcessing = useCallback((stream: MediaStream, ws: WebSocket) => {
    if (!enableWebRTC) return;

    try {
      const audioContext = new AudioContext({ sampleRate: 24000 });
      audioContextRef.current = audioContext;

      const source = audioContext.createMediaStreamSource(stream);
      const processor = audioContext.createScriptProcessor(4096, 1, 1);
      processorRef.current = processor;

      processor.onaudioprocess = (event) => {
        if (!state.isListening || ws.readyState !== WebSocket.OPEN) return;

        const inputBuffer = event.inputBuffer;
        const inputData = inputBuffer.getChannelData(0);
        
        // Convert to 16-bit PCM
        const pcm16 = new Int16Array(inputData.length);
        for (let i = 0; i < inputData.length; i++) {
          pcm16[i] = Math.max(-32768, Math.min(32767, inputData[i] * 32768));
        }

        // Send audio data to OpenAI Realtime API
        ws.send(JSON.stringify({
          type: 'input_audio_buffer.append',
          audio: Array.from(pcm16)
        }));
      };

      source.connect(processor);
      processor.connect(audioContext.destination);
    } catch (error) {
      console.error('Audio processing setup failed:', error);
    }
  }, [enableWebRTC, state.isListening]);

  // Handle OpenAI Realtime API messages
  const handleRealtimeMessage = useCallback((message: any) => {
    latencyStartRef.current = Date.now();
    
    switch (message.type) {
      case 'input_audio_buffer.speech_started':
        setState(prev => ({ ...prev, isProcessing: true }));
        onStatusChange('processing');
        break;
        
      case 'input_audio_buffer.speech_stopped':
        // Commit the audio buffer for processing
        if (wsRef.current?.readyState === WebSocket.OPEN) {
          wsRef.current.send(JSON.stringify({
            type: 'input_audio_buffer.commit'
          }));
        }
        break;
        
      case 'conversation.item.input_audio_transcription.completed':
        const transcript = message.transcript;
        const latency = Date.now() - latencyStartRef.current;
        
        setState(prev => ({ ...prev, latency, isProcessing: false }));
        onTranscript(transcript, true);
        onStatusChange('idle');
        break;
        
      case 'response.audio.delta':
        // Handle audio response if needed
        break;
        
      case 'error':
        console.error('Realtime API error:', message.error);
        setState(prev => ({ 
          ...prev, 
          error: message.error.message || 'Realtime API error',
          isProcessing: false 
        }));
        onStatusChange('error');
        break;
    }
  }, [onTranscript, onStatusChange]);

  // Fallback to Whisper + Speech Recognition
  const initializeWhisperFallback = useCallback(async (): Promise<boolean> => {
    try {
      // Check browser speech recognition support
      if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
        throw new Error('Speech recognition not supported in this browser');
      }

      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      const recognition = new SpeechRecognition();
      
      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = 'en-US';

      recognition.onresult = (event) => {
        const last = event.results.length - 1;
        const transcript = event.results[last][0].transcript;
        const isFinal = event.results[last].isFinal;
        
        onTranscript(transcript, isFinal);
        
        if (isFinal) {
          const latency = Date.now() - latencyStartRef.current;
          setState(prev => ({ ...prev, latency: latency > 1000 ? latency : null }));
        }
      };

      recognition.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        setState(prev => ({ 
          ...prev, 
          error: `Speech recognition: ${event.error}`,
          isListening: false 
        }));
        onStatusChange('error');
      };

      recognition.onend = () => {
        setState(prev => ({ ...prev, isListening: false }));
      };

      realtimeSessionRef.current = recognition;
      setState(prev => ({ ...prev, isConnected: true, error: null }));
      onStatusChange('connected');
      
      return true;
    } catch (error) {
      console.error('Whisper fallback failed:', error);
      return false;
    }
  }, [onTranscript, onStatusChange]);

  // Start listening
  const startListening = useCallback(async () => {
    if (state.isListening) return;

    if (!state.isConnected) {
      const initialized = await initializeRealtimeAPI();
      if (!initialized) return;
    }

    latencyStartRef.current = Date.now();
    
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      // OpenAI Realtime API
      setState(prev => ({ ...prev, isListening: true }));
      onStatusChange('listening');
    } else if (realtimeSessionRef.current) {
      // Speech Recognition fallback
      try {
        realtimeSessionRef.current.start();
        setState(prev => ({ ...prev, isListening: true }));
        onStatusChange('listening');
      } catch (error) {
        console.error('Failed to start speech recognition:', error);
        setState(prev => ({ ...prev, error: 'Failed to start listening' }));
      }
    }
  }, [state.isListening, state.isConnected, initializeRealtimeAPI, onStatusChange]);

  // Stop listening
  const stopListening = useCallback(() => {
    if (!state.isListening) return;

    if (wsRef.current?.readyState === WebSocket.OPEN) {
      // Stop OpenAI Realtime API
      setState(prev => ({ ...prev, isListening: false }));
    } else if (realtimeSessionRef.current) {
      // Stop Speech Recognition
      try {
        realtimeSessionRef.current.stop();
      } catch (error) {
        console.error('Failed to stop speech recognition:', error);
      }
      setState(prev => ({ ...prev, isListening: false }));
    }
    
    onStatusChange('idle');
  }, [state.isListening, onStatusChange]);

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  return {
    ...state,
    startListening,
    stopListening,
    cleanup
  };
}