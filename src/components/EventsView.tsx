import { useState } from 'react';
import { useNavigationContext } from '../contexts/NavigationContext';
import EventsTable from './EventsTable';

export default function EventsView() {
  const { viewFilters, setViewFilter, clearViewFilters } = useNavigationContext();
  const [localFilter, setLocalFilter] = useState<string>('');
  const [voiceOnlyFilter, setVoiceOnlyFilter] = useState<boolean>(false);

  const eventTypes = [
    { value: '', label: 'All Events' },
    { value: 'receiving', label: 'Receiving' },
    { value: 'disposal', label: 'Disposal' },
    { value: 'physical_count', label: 'Physical Count' },
    { value: 'sale', label: 'Sales' },
  ];

  const handleFilterChange = (eventType: string) => {
    setLocalFilter(eventType);
    if (eventType) {
      setViewFilter('eventType', eventType);
    } else {
      clearViewFilters();
    }
  };

  // Use context filter or local filter
  const activeFilter = viewFilters.eventType || localFilter;

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Events</h1>
          <p className="text-gray-600 mt-1">View and manage all inventory events</p>
        </div>
        
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <label htmlFor="eventTypeFilter" className="text-sm font-medium text-gray-700">
              Filter by type:
            </label>
            <select
              id="eventTypeFilter"
              value={activeFilter || ''}
              onChange={(e) => handleFilterChange(e.target.value)}
              className="rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            >
              {eventTypes.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>

          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              id="voiceOnlyFilter"
              checked={voiceOnlyFilter}
              onChange={(e) => setVoiceOnlyFilter(e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <label htmlFor="voiceOnlyFilter" className="text-sm font-medium text-gray-700">
              Voice events only
            </label>
          </div>
          
          {(activeFilter || voiceOnlyFilter) && (
            <button
              type="button"
              onClick={() => {
                handleFilterChange('');
                setVoiceOnlyFilter(false);
              }}
              className="text-sm text-blue-600 hover:text-blue-800 underline"
            >
              Clear filters
            </button>
          )}
        </div>
      </div>

      <EventsTable showTitle={false} limit={100} voiceOnly={voiceOnlyFilter} />
    </div>
  );
}