import React, { useState } from 'react';
import { Search, Building2 } from 'lucide-react';
import PartnerView from './PartnerView';
import type { Partner } from '../../types';

// Mock data - replace with actual data from your backend
const mockPartners: Partner[] = [
  {
    id: '1',
    type: 'vendor',
    name: 'Ocean Fresh Ltd',
    contact: '<PERSON>',
    email: '<EMAIL>',
    phone: '******-0123',
    address: '123 Harbor Drive, Seattle, WA 98101',
    since: '2023-01-15',
    status: 'active',
    creditLimit: 50000,
    paymentTerms: 'Net 30',
    notes: [],
    tags: ['Premium', 'Salmon', 'Certified']
  },
  {
    id: '2',
    type: 'customer',
    name: 'Seafood Express',
    contact: '<PERSON>',
    email: '<EMAIL>',
    phone: '******-0124',
    address: '456 Market St, San Francisco, CA 94105',
    since: '2023-03-01',
    status: 'active',
    notes: [],
    tags: ['Wholesale', 'Restaurant']
  }
];

export default function PartnersView() {
  const [selectedPartner, setSelectedPartner] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'vendors' | 'customers'>('all');
  const [searchTerm, setSearchTerm] = useState('');

  const filteredPartners = mockPartners.filter(partner => {
    const matchesFilter = 
      filter === 'all' || 
      (filter === 'vendors' && partner.type === 'vendor') ||
      (filter === 'customers' && partner.type === 'customer');

    const matchesSearch = 
      partner.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      partner.contact.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesFilter && matchesSearch;
  });

  if (selectedPartner) {
    return (
      <div className="h-full flex flex-col">
        <div className="flex items-center gap-2 p-4 bg-gray-100 border-b">
          <button
            onClick={() => setSelectedPartner(null)}
            className="text-gray-600 hover:text-gray-900"
          >
            ← Back to Partners
          </button>
        </div>
        <div className="flex-1 overflow-hidden">
          <PartnerView partnerId={selectedPartner} />
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Partners</h1>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
          Add Partner
        </button>
      </div>

      <div className="flex gap-4 flex-col sm:flex-row">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            placeholder="Search partners..."
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex gap-2">
          <button
            onClick={() => setFilter('all')}
            className={`px-4 py-2 rounded-lg ${
              filter === 'all'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
            }`}
          >
            All
          </button>
          <button
            onClick={() => setFilter('vendors')}
            className={`px-4 py-2 rounded-lg ${
              filter === 'vendors'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
            }`}
          >
            Vendors
          </button>
          <button
            onClick={() => setFilter('customers')}
            className={`px-4 py-2 rounded-lg ${
              filter === 'customers'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
            }`}
          >
            Customers
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredPartners.map((partner) => (
          <button
            key={partner.id}
            onClick={() => setSelectedPartner(partner.id)}
            className="text-left bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6"
          >
            <div className="flex items-start justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">{partner.name}</h3>
                <p className="text-sm text-gray-500">{partner.type === 'vendor' ? 'Vendor' : 'Customer'}</p>
              </div>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                partner.status === 'active' 
                  ? 'bg-green-100 text-green-800'
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {partner.status.toUpperCase()}
              </span>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Building2 className="w-4 h-4" />
                <span>{partner.address}</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Users className="w-4 h-4" />
                <span>{partner.contact}</span>
              </div>
            </div>

            <div className="mt-4 flex flex-wrap gap-2">
              {partner.tags.map((tag) => (
                <span
                  key={tag}
                  className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs"
                >
                  {tag}
                </span>
              ))}
            </div>
          </button>
        ))}
      </div>
    </div>
  );
}