import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  Search, 
  Filter, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  CheckCircle,
  Star,
  Clock,
  Package,
  Plus,
  Eye
} from 'lucide-react';
import { vendorAPI } from '../../lib/vendor-api';
import { VendorDashboardSummary } from '../../types/schema';
import VendorReportCard from './VendorReportCard';
import ErrorBoundary from '../ErrorBoundary';

export default function VendorDashboard() {
  const [vendors, setVendors] = useState<VendorDashboardSummary[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'grade' | 'completion_rate' | 'alerts'>('grade');
  const [filterBy, setFilterBy] = useState<'all' | 'A' | 'B' | 'C' | 'D' | 'F' | 'alerts'>('all');
  const [selectedVendorId, setSelectedVendorId] = useState<string | null>(null);

  useEffect(() => {
    loadVendorDashboard();
  }, [loadVendorDashboard]);

  const loadVendorDashboard = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const summary = await vendorAPI.dashboard.getSummary();
      if (!Array.isArray(summary)) {
        throw new Error('Invalid dashboard summary data received');
      }
      setVendors(summary);
    } catch (err) {
      console.error('Error loading vendor dashboard:', err);
      setError(err instanceof Error ? err.message : 'Failed to load vendor dashboard');
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshDashboard = useCallback(async () => {
    try {
      await vendorAPI.dashboard.refreshSummary();
      await loadVendorDashboard();
    } catch (err) {
      console.error('Error refreshing dashboard:', err);
    }
  }, [loadVendorDashboard]);

  // Filter and sort vendors with memoization for performance
  const filteredAndSortedVendors = useMemo(() => {
    const gradeOrder: Record<string, number> = { 'A': 1, 'B': 2, 'C': 3, 'D': 4, 'F': 5 };
    
    return vendors
      .filter(vendor => {
        const matchesSearch = vendor.vendor_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                             (vendor.contact_name?.toLowerCase().includes(searchQuery.toLowerCase()));
        
        if (!matchesSearch) return false;

        switch (filterBy) {
          case 'all':
            return true;
          case 'alerts':
            return (vendor.active_alerts_count || 0) > 0;
          case 'A':
          case 'B':
          case 'C':
          case 'D':
          case 'F':
            return vendor.overall_letter_grade === filterBy;
          default:
            return true;
        }
      })
      .sort((a, b) => {
        switch (sortBy) {
          case 'name':
            return (a.vendor_name || '').localeCompare(b.vendor_name || '');
          case 'grade':
            return (gradeOrder[a.overall_letter_grade || ''] || 6) - 
                   (gradeOrder[b.overall_letter_grade || ''] || 6);
          case 'completion_rate':
            return (b.completion_rate || 0) - (a.completion_rate || 0);
          case 'alerts':
            return (b.active_alerts_count || 0) - (a.active_alerts_count || 0);
          default:
            return 0;
        }
      });
  }, [vendors, searchQuery, filterBy, sortBy]);

  const getGradeColor = (grade: string | null | undefined) => {
    switch (grade) {
      case 'A': return 'text-green-600 bg-green-100 border-green-200';
      case 'B': return 'text-blue-600 bg-blue-100 border-blue-200';
      case 'C': return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 'D': return 'text-orange-600 bg-orange-100 border-orange-200';
      case 'F': return 'text-red-600 bg-red-100 border-red-200';
      default: return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const gradeStats = useMemo(() => {
    const stats = { A: 0, B: 0, C: 0, D: 0, F: 0, unrated: 0 };
    vendors.forEach(vendor => {
      if (vendor.overall_letter_grade && vendor.overall_letter_grade in stats) {
        stats[vendor.overall_letter_grade as keyof Omit<typeof stats, 'unrated'>]++;
      } else {
        stats.unrated++;
      }
    });
    return stats;
  }, [vendors]);
  
  const totalAlerts = useMemo(() => 
    vendors.reduce((sum, vendor) => sum + (vendor.active_alerts_count || 0), 0),
    [vendors]
  );

  if (selectedVendorId) {
    return (
      <div className="p-6">
        <ErrorBoundary>
          <VendorReportCard 
            vendorId={selectedVendorId} 
            onClose={() => setSelectedVendorId(null)}
          />
        </ErrorBoundary>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Vendor Performance Dashboard</h1>
          <p className="text-gray-600">Monitor and evaluate vendor performance across all metrics</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={refreshDashboard}
            className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          >
            Refresh Data
          </button>
          <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <Plus className="w-4 h-4 mr-2 inline" />
            Add Vendor
          </button>
        </div>
      </div>

      {/* Performance Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Vendors</p>
              <p className="text-2xl font-bold text-gray-900">{vendors.length}</p>
            </div>
            <Package className="w-8 h-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">A-Grade Vendors</p>
              <p className="text-2xl font-bold text-green-600">{gradeStats.A}</p>
            </div>
            <Star className="w-8 h-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Active Alerts</p>
              <p className="text-2xl font-bold text-red-600">{totalAlerts}</p>
            </div>
            <AlertTriangle className="w-8 h-8 text-red-500" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Avg Completion Rate</p>
              <p className="text-2xl font-bold text-blue-600">
                {vendors.length > 0 
                  ? (vendors.reduce((sum, v) => sum + (v.completion_rate || 0), 0) / vendors.length).toFixed(1)
                  : '0'
                }%
              </p>
            </div>
            <TrendingUp className="w-8 h-8 text-blue-500" />
          </div>
        </div>
      </div>

      {/* Grade Distribution */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Grade Distribution</h3>
        <div className="flex space-x-4">
          {Object.entries(gradeStats).map(([grade, count]) => (
            <div key={grade} className="flex-1 text-center">
              <div className={`w-12 h-12 mx-auto rounded-full flex items-center justify-center text-lg font-bold mb-2 ${
                grade === 'unrated' ? 'bg-gray-100 text-gray-600' : getGradeColor(grade)
              }`}>
                {grade === 'unrated' ? '?' : grade}
              </div>
              <div className="text-sm text-gray-600">{grade === 'unrated' ? 'Unrated' : `Grade ${grade}`}</div>
              <div className="text-lg font-semibold text-gray-900">{count}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white p-4 rounded-lg shadow-sm border">
        <div className="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
          {/* Search */}
          <div className="flex-1 relative">
            <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search vendors..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Sort By */}
          <div className="flex items-center space-x-2">
            <label className="text-sm text-gray-600">Sort by:</label>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as typeof sortBy)}
              className="border border-gray-300 rounded px-3 py-2 text-sm"
            >
              <option value="grade">Grade</option>
              <option value="name">Name</option>
              <option value="completion_rate">Completion Rate</option>
              <option value="alerts">Alerts</option>
            </select>
          </div>

          {/* Filter By */}
          <div className="flex items-center space-x-2">
            <label className="text-sm text-gray-600">Filter:</label>
            <select
              value={filterBy}
              onChange={(e) => setFilterBy(e.target.value as typeof filterBy)}
              className="border border-gray-300 rounded px-3 py-2 text-sm"
            >
              <option value="all">All Vendors</option>
              <option value="A">Grade A</option>
              <option value="B">Grade B</option>
              <option value="C">Grade C</option>
              <option value="D">Grade D</option>
              <option value="F">Grade F</option>
              <option value="alerts">With Alerts</option>
            </select>
          </div>
        </div>
      </div>

      {/* Vendor List */}
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
            <p className="text-gray-600 mt-4">Loading vendor dashboard...</p>
          </div>
        ) : error ? (
          <div className="p-8 text-center">
            <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Dashboard</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={loadVendorDashboard}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        ) : filteredAndSortedVendors.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            {searchQuery || filterBy !== 'all' ? 'No vendors match your filters' : 'No vendors found'}
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredAndSortedVendors.map((vendor) => (
              <div key={vendor.vendor_id} className="p-6 hover:bg-gray-50 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center text-lg font-bold border-2 ${getGradeColor(vendor.overall_letter_grade)}`}>
                      {vendor.overall_letter_grade || '?'}
                    </div>
                    
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">{vendor.vendor_name}</h3>
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <span>{vendor.contact_name}</span>
                        <span>{vendor.email}</span>
                        <span>{vendor.phone}</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-6">
                    {/* Performance Metrics */}
                    <div className="text-center">
                      <div className="text-lg font-semibold text-gray-900">
                        {vendor.completion_rate?.toFixed(1) || 'N/A'}%
                      </div>
                      <div className="text-xs text-gray-500">Completion</div>
                    </div>

                    <div className="text-center">
                      <div className="text-lg font-semibold text-gray-900">
                        {vendor.on_time_delivery_rate?.toFixed(1) || 'N/A'}%
                      </div>
                      <div className="text-xs text-gray-500">On-Time</div>
                    </div>

                    <div className="text-center">
                      <div className="text-lg font-semibold text-gray-900">
                        {vendor.total_interactions || 0}
                      </div>
                      <div className="text-xs text-gray-500">Orders</div>
                    </div>

                    {/* Alerts */}
                    {(vendor.active_alerts_count || 0) > 0 && (
                      <div className="flex items-center space-x-1 px-2 py-1 bg-red-100 text-red-800 rounded-full">
                        <AlertTriangle className="w-3 h-3" />
                        <span className="text-xs font-medium">
                          {vendor.active_alerts_count} alert{vendor.active_alerts_count !== 1 ? 's' : ''}
                        </span>
                      </div>
                    )}

                    {/* Actions */}
                    <button
                      onClick={() => setSelectedVendorId(vendor.vendor_id)}
                      className="flex items-center space-x-1 px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
                    >
                      <Eye className="w-4 h-4" />
                      <span className="text-sm font-medium">View Report</span>
                    </button>
                  </div>
                </div>

                {/* Last Delivery Info */}
                {vendor.last_delivery_date && (
                  <div className="mt-4 flex items-center text-sm text-gray-500">
                    <Clock className="w-4 h-4 mr-1" />
                    Last delivery: {new Date(vendor.last_delivery_date).toLocaleDateString()}
                    <span className="mx-2">•</span>
                    Status: <span className="font-medium">{vendor.last_delivery_status}</span>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
      </div>
    </ErrorBoundary>
  );
}