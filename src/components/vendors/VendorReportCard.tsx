import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  Star, 
  TrendingUp, 
  TrendingDown, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  Package,
  DollarSign,
  Calendar,
  Shield,
  FileText,
  Award
} from 'lucide-react';
import { vendorAPI } from '../../lib/vendor-api';
import { VendorMetrics, VendorRating, VendorCompliance, VendorPerformanceAlert } from '../../types/schema';

interface VendorReportCardProps {
  vendorId: string;
  onClose?: () => void;
}

interface VendorDetails {
  vendor: {
    id: string;
    name: string;
    contact_person?: string;
    email?: string;
    phone?: string;
    address?: string;
    status?: 'active' | 'inactive';
    payment_terms?: string;
    credit_limit?: number;
  };
  currentMetrics: VendorMetrics | null;
  recentRatings: VendorRating[];
  complianceStatus: VendorCompliance | null;
  activeAlerts: VendorPerformanceAlert[];
}

export default function VendorReportCard({ vendorId, onClose }: VendorReportCardProps) {
  const [vendorDetails, setVendorDetails] = useState<VendorDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'ratings' | 'compliance' | 'alerts'>('overview');

  useEffect(() => {
    loadVendorDetails();
  }, [loadVendorDetails]);

  const loadVendorDetails = useCallback(async () => {
    try {
      setLoading(true);
      const details = await vendorAPI.dashboard.getVendorDetails(vendorId);
      setVendorDetails(details);
    } catch (err) {
      console.error('Error loading vendor details:', err);
      setError(err instanceof Error ? err.message : 'Failed to load vendor details');
    } finally {
      setLoading(false);
    }
  }, [vendorId]);

  const getGradeColor = (grade: string | null | undefined) => {
    switch (grade) {
      case 'A': return 'text-green-600 bg-green-100';
      case 'B': return 'text-blue-600 bg-blue-100';
      case 'C': return 'text-yellow-600 bg-yellow-100';
      case 'D': return 'text-orange-600 bg-orange-100';
      case 'F': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-500 text-white';
      case 'high': return 'bg-orange-500 text-white';
      case 'medium': return 'bg-yellow-500 text-white';
      case 'low': return 'bg-blue-500 text-white';
      default: return 'bg-gray-500 text-white';
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-8">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
          <span className="ml-3 text-gray-600">Loading vendor report card...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-8">
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Report Card</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={loadVendorDetails}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!vendorDetails) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-8">
        <div className="text-center text-gray-600">
          No vendor data available
        </div>
      </div>
    );
  }

  const { vendor, currentMetrics, recentRatings, complianceStatus, activeAlerts } = vendorDetails;

  return (
    <div className="bg-white rounded-lg shadow-xl max-w-6xl mx-auto">
      {/* Header */}
      <div className="border-b border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className={`w-16 h-16 rounded-full flex items-center justify-center text-2xl font-bold ${getGradeColor(currentMetrics?.overall_letter_grade)}`}>
              {currentMetrics?.overall_letter_grade || 'N/A'}
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{vendor.name}</h1>
              <p className="text-gray-600">{vendor.contact_person}</p>
              <div className="flex items-center space-x-4 mt-1">
                <span className="text-sm text-gray-500">{vendor.email}</span>
                <span className="text-sm text-gray-500">{vendor.phone}</span>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            {activeAlerts.length > 0 && (
              <div className="flex items-center space-x-2 px-3 py-1 bg-red-100 text-red-800 rounded-full">
                <AlertTriangle className="w-4 h-4" />
                <span className="text-sm font-medium">{activeAlerts.length} Alert{activeAlerts.length !== 1 ? 's' : ''}</span>
              </div>
            )}
            {onClose && (
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                ✕
              </button>
            )}
          </div>
        </div>

        {/* Performance Summary Cards */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-blue-600 font-medium">Completion Rate</p>
                <p className="text-2xl font-bold text-blue-900">
                  {currentMetrics?.completion_rate?.toFixed(1) || 'N/A'}%
                </p>
              </div>
              <Package className="w-8 h-8 text-blue-500" />
            </div>
          </div>

          <div className="bg-green-50 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-green-600 font-medium">On-Time Delivery</p>
                <p className="text-2xl font-bold text-green-900">
                  {currentMetrics?.on_time_delivery_rate?.toFixed(1) || 'N/A'}%
                </p>
              </div>
              <Clock className="w-8 h-8 text-green-500" />
            </div>
          </div>

          <div className="bg-yellow-50 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-yellow-600 font-medium">Avg Quality</p>
                <p className="text-2xl font-bold text-yellow-900">
                  {currentMetrics?.average_quality_score?.toFixed(1) || 'N/A'}/10
                </p>
              </div>
              <Star className="w-8 h-8 text-yellow-500" />
            </div>
          </div>

          <div className="bg-purple-50 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-purple-600 font-medium">Total Orders</p>
                <p className="text-2xl font-bold text-purple-900">
                  {currentMetrics?.total_interactions || 0}
                </p>
              </div>
              <Calendar className="w-8 h-8 text-purple-500" />
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {[
            { id: 'overview' as const, label: 'Overview', icon: TrendingUp },
            { id: 'ratings' as const, label: 'Ratings', icon: Star },
            { id: 'compliance' as const, label: 'Compliance', icon: Shield },
            { id: 'alerts' as const, label: 'Alerts', icon: AlertTriangle, count: activeAlerts.length }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 py-4 border-b-2 transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span className="font-medium">{tab.label}</span>
              {tab.count !== undefined && tab.count > 0 && (
                <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="p-6">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Financial Metrics */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Financial Performance</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Total Order Value</p>
                      <p className="text-xl font-bold text-gray-900">
                        ${currentMetrics?.total_order_value?.toLocaleString() || '0'}
                      </p>
                    </div>
                    <DollarSign className="w-6 h-6 text-gray-500" />
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Delivered Value</p>
                      <p className="text-xl font-bold text-gray-900">
                        ${currentMetrics?.total_delivered_value?.toLocaleString() || '0'}
                      </p>
                    </div>
                    <Package className="w-6 h-6 text-gray-500" />
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Avg Order Value</p>
                      <p className="text-xl font-bold text-gray-900">
                        ${currentMetrics?.average_order_value?.toLocaleString() || '0'}
                      </p>
                    </div>
                    <TrendingUp className="w-6 h-6 text-gray-500" />
                  </div>
                </div>
              </div>
            </div>

            {/* Issue Resolution */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Issue Resolution</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-600">Resolution Rate</p>
                  <p className="text-xl font-bold text-gray-900">
                    {currentMetrics?.issue_resolution_rate?.toFixed(1) || '0'}%
                  </p>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                    <div 
                      className="bg-green-500 h-2 rounded-full" 
                      style={{ width: `${currentMetrics?.issue_resolution_rate || 0}%` }}
                    ></div>
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-600">Avg Resolution Time</p>
                  <p className="text-xl font-bold text-gray-900">
                    {currentMetrics?.average_resolution_time_hours?.toFixed(1) || '0'}h
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'ratings' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">Recent Ratings</h3>
              <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                Add New Rating
              </button>
            </div>

            {recentRatings.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                No ratings available yet
              </div>
            ) : (
              <div className="space-y-4">
                {recentRatings.slice(0, 5).map((rating) => (
                  <div key={rating.id} className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-4">
                        <div className={`px-3 py-1 rounded-full text-sm font-medium ${getGradeColor(rating.letter_grade)}`}>
                          Grade: {rating.letter_grade || 'N/A'}
                        </div>
                        <span className="text-sm text-gray-600">
                          {new Date(rating.rating_date || '').toLocaleDateString()}
                        </span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Star className="w-4 h-4 text-yellow-500 fill-current" />
                        <span className="font-medium">{rating.overall_satisfaction || 'N/A'}/10</span>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Quality:</span>
                        <span className="ml-1 font-medium">{rating.quality_score || 'N/A'}/10</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Delivery:</span>
                        <span className="ml-1 font-medium">{rating.delivery_timeliness_score || 'N/A'}/10</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Communication:</span>
                        <span className="ml-1 font-medium">{rating.communication_score || 'N/A'}/10</span>
                      </div>
                    </div>

                    {rating.manager_comments && (
                      <div className="mt-3 p-3 bg-white rounded border">
                        <p className="text-sm text-gray-700">{rating.manager_comments}</p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'compliance' && (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Compliance Status</h3>
            
            {complianceStatus ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* HACCP Compliance */}
                <div className="bg-gray-50 p-6 rounded-lg">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="font-semibold text-gray-900">HACCP Compliance</h4>
                    <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                      complianceStatus.haccp_certified 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {complianceStatus.haccp_certified ? 'Certified' : 'Not Certified'}
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Certificate Expiry:</span>
                      <span className="text-sm font-medium">
                        {complianceStatus.haccp_cert_expiry_date 
                          ? new Date(complianceStatus.haccp_cert_expiry_date).toLocaleDateString()
                          : 'N/A'
                        }
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Temperature Logs:</span>
                      <span className={`text-sm font-medium ${
                        complianceStatus.temperature_logs_complete 
                          ? 'text-green-600' 
                          : 'text-red-600'
                      }`}>
                        {complianceStatus.temperature_logs_complete ? 'Complete' : 'Incomplete'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">CCP Monitoring:</span>
                      <span className={`text-sm font-medium ${
                        complianceStatus.critical_control_points_met 
                          ? 'text-green-600' 
                          : 'text-red-600'
                      }`}>
                        {complianceStatus.critical_control_points_met ? 'Met' : 'Not Met'}
                      </span>
                    </div>
                  </div>
                </div>

                {/* GDST Traceability */}
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h4 className="font-semibold text-gray-900 mb-4">GDST Traceability</h4>
                  
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Catch Certificate:</span>
                      <span className={`text-sm font-medium ${
                        complianceStatus.catch_certificate_provided 
                          ? 'text-green-600' 
                          : 'text-red-600'
                      }`}>
                        {complianceStatus.catch_certificate_provided ? 'Provided' : 'Missing'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Chain of Custody:</span>
                      <span className={`text-sm font-medium ${
                        complianceStatus.chain_of_custody_complete 
                          ? 'text-green-600' 
                          : 'text-red-600'
                      }`}>
                        {complianceStatus.chain_of_custody_complete ? 'Complete' : 'Incomplete'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Species Verification:</span>
                      <span className={`text-sm font-medium ${
                        complianceStatus.species_verification_done 
                          ? 'text-green-600' 
                          : 'text-red-600'
                      }`}>
                        {complianceStatus.species_verification_done ? 'Verified' : 'Not Verified'}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Compliance Score */}
                <div className="md:col-span-2 bg-white border-2 border-gray-200 p-6 rounded-lg">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="font-semibold text-gray-900">Overall Compliance Score</h4>
                    <div className={`px-4 py-2 rounded-lg text-lg font-bold ${
                      (complianceStatus.compliance_score || 0) >= 90 
                        ? 'bg-green-100 text-green-800'
                        : (complianceStatus.compliance_score || 0) >= 70
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {complianceStatus.compliance_score || 0}/100
                    </div>
                  </div>
                  
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div 
                      className={`h-3 rounded-full transition-all ${
                        (complianceStatus.compliance_score || 0) >= 90 
                          ? 'bg-green-500'
                          : (complianceStatus.compliance_score || 0) >= 70
                          ? 'bg-yellow-500'
                          : 'bg-red-500'
                      }`}
                      style={{ width: `${complianceStatus.compliance_score || 0}%` }}
                    ></div>
                  </div>
                  
                  <p className="text-sm text-gray-600 mt-2">
                    Status: <span className="font-medium">{complianceStatus.compliance_status}</span>
                  </p>
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                No compliance data available
              </div>
            )}
          </div>
        )}

        {activeTab === 'alerts' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">Performance Alerts</h3>
              <span className="text-sm text-gray-600">
                {activeAlerts.length} active alert{activeAlerts.length !== 1 ? 's' : ''}
              </span>
            </div>

            {activeAlerts.length === 0 ? (
              <div className="text-center py-8">
                <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
                <h4 className="text-lg font-medium text-gray-900 mb-2">All Clear!</h4>
                <p className="text-gray-600">No performance alerts for this vendor</p>
              </div>
            ) : (
              <div className="space-y-4">
                {activeAlerts.map((alert) => (
                  <div key={alert.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(alert.severity)}`}>
                            {alert.severity.toUpperCase()}
                          </span>
                          <span className="text-sm text-gray-600">
                            {new Date(alert.created_at || '').toLocaleDateString()}
                          </span>
                        </div>
                        <h4 className="font-medium text-gray-900 mb-1">{alert.title}</h4>
                        <p className="text-sm text-gray-600 mb-3">{alert.description}</p>
                        
                        {alert.triggered_by_metric && (
                          <div className="text-xs text-gray-500">
                            Triggered by: {alert.triggered_by_metric} 
                            {alert.actual_value && alert.threshold_value && (
                              <span> (actual: {alert.actual_value}, threshold: {alert.threshold_value})</span>
                            )}
                          </div>
                        )}
                      </div>
                      
                      <div className="flex space-x-2 ml-4">
                        <button
                          onClick={() => alert.id && vendorAPI.alerts.acknowledge(alert.id)}
                          disabled={!alert.id}
                          className="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded hover:bg-blue-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          Acknowledge
                        </button>
                        <button
                          onClick={() => alert.id && vendorAPI.alerts.resolve(alert.id, 'Resolved via dashboard')}
                          disabled={!alert.id}
                          className="px-3 py-1 text-sm bg-green-100 text-green-800 rounded hover:bg-green-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          Resolve
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Footer Actions */}
      <div className="border-t border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-500">
            Last updated: {currentMetrics?.calculated_at 
              ? new Date(currentMetrics.calculated_at).toLocaleString()
              : 'Never'
            }
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={() => vendorId && vendorAPI.metrics.calculate(vendorId)}
              disabled={!vendorId}
              className="px-4 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Refresh Metrics
            </button>
            <button className="px-4 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              Export Report
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}