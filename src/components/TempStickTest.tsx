import React, { useState, useEffect } from 'react';
import { tempStickService } from '@/lib/tempstick-service';
import type { TempStickSensor, TempStickReading } from '@/types/tempstick';

/**
 * TempStick Service Test Component
 * Tests TempStick integration within React environment
 */
export const TempStickTest: React.FC = () => {
  const [sensors, setSensors] = useState<TempStickSensor[]>([]);
  const [readings, setReadings] = useState<TempStickReading[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [testResults, setTestResults] = useState<Record<string, boolean>>({});

  useEffect(() => {
    runTests();
  }, []);

  const runTests = async () => {
    setLoading(true);
    setError(null);
    const results: Record<string, boolean> = {};

    try {
      // Test 1: Get sensors
      console.log('🌡️ Testing getSensors()...');
      const sensorData = await tempStickService.getSensors();
      setSensors(sensorData);
      results.sensors = sensorData.length > 0;
      console.log(`✅ Found ${sensorData.length} sensors`);

      if (sensorData.length > 0) {
        const testSensor = sensorData[0];

        // Test 2: Get latest readings
        console.log('📊 Testing getLatestReadings()...');
        try {
          const readingData = await tempStickService.getLatestReadings(testSensor.id, 10);
          setReadings(readingData);
          results.readings = readingData.length > 0;
          console.log(`✅ Found ${readingData.length} readings`);
        } catch (readingError) {
          console.warn('⚠️ Readings test failed:', readingError);
          results.readings = false;
        }

        // Test 3: Health check
        console.log('🔍 Testing getSensorHealth()...');
        try {
          const health = await tempStickService.getSensorHealth(testSensor.id);
          results.health = health.status === 'online' || health.status === 'offline';
          console.log(`✅ Health check: ${health.status}`);
        } catch (healthError) {
          console.warn('⚠️ Health check failed:', healthError);
          results.health = false;
        }

        // Test 4: Batch health check
        console.log('🔄 Testing performHealthCheck()...');
        try {
          const batchHealth = await tempStickService.performHealthCheck();
          results.batchHealth = batchHealth.sensors.total > 0;
          console.log(`✅ Batch health: ${batchHealth.sensors.online}/${batchHealth.sensors.total} online`);
        } catch (batchError) {
          console.warn('⚠️ Batch health check failed:', batchError);
          results.batchHealth = false;
        }
      }

      // Test 5: Configuration
      console.log('⚙️ Testing configuration...');
      const config = tempStickService.getConfiguration();
      results.config = config.dataSource !== undefined;
      console.log(`✅ Configuration: ${config.dataSource} mode`);

      setTestResults(results);

    } catch (serviceError) {
      console.error('❌ Service test failed:', serviceError);
      setError(serviceError instanceof Error ? serviceError.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const formatTemp = (celsius: number) => {
    const fahrenheit = (celsius * 9/5) + 32;
    return `${fahrenheit.toFixed(1)}°F (${celsius.toFixed(1)}°C)`;
  };

  return (
    <div className="p-6 max-w-4xl mx-auto space-y-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold mb-4 flex items-center">
          🌡️ TempStick Service Test
        </h2>
        
        <div className="flex gap-4 mb-6">
          <button
            onClick={runTests}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Testing...' : 'Run Tests'}
          </button>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded mb-6">
            <strong>Error:</strong> {error}
          </div>
        )}

        {/* Test Results Summary */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
          {Object.entries(testResults).map(([test, passed]) => (
            <div
              key={test}
              className={`p-3 rounded text-center ${
                passed ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}
            >
              <div className="font-semibold">{passed ? '✅' : '❌'}</div>
              <div className="text-sm capitalize">{test}</div>
            </div>
          ))}
        </div>

        {/* Sensors Display */}
        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-semibold mb-3">📊 Sensors ({sensors.length})</h3>
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {sensors.map((sensor) => (
                <div key={sensor.id} className="border rounded p-3">
                  <div className="font-medium">{sensor.name}</div>
                  <div className="text-sm text-gray-600">{sensor.location}</div>
                  <div className="text-xs text-gray-500 mt-1">
                    Status: <span className={`px-1 rounded ${
                      sensor.status === 'online' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {sensor.status}
                    </span>
                    {sensor.battery_level && (
                      <span className="ml-2">
                        🔋 {sensor.battery_level}%
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Latest Readings */}
          <div>
            <h3 className="text-lg font-semibold mb-3">🌡️ Latest Readings ({readings.length})</h3>
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {readings.slice(0, 5).map((reading, index) => (
                <div key={`${reading.sensor_id}-${index}`} className="border rounded p-3">
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="font-medium">
                        {formatTemp(reading.temp_celsius)}
                      </div>
                      {reading.humidity && (
                        <div className="text-sm text-blue-600">
                          💧 {reading.humidity.toFixed(1)}% humidity
                        </div>
                      )}
                    </div>
                    <div className="text-right">
                      <div className={`text-xs px-1 rounded ${
                        reading.temperature > -10 && reading.temperature < 80 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {reading.temperature > -10 && reading.temperature < 80 ? 'Safe' : 'Alert'}
                      </div>
                      {(reading.temperature < -10 || reading.temperature > 80) && (
                        <div className="text-xs text-red-600 mt-1">⚠️ Temp Violation</div>
                      )}
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 mt-2">
                    {new Date(reading.timestamp).toLocaleString()}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Service Configuration */}
        <div className="mt-6 p-4 bg-gray-50 rounded">
          <h3 className="text-lg font-semibold mb-2">⚙️ Service Configuration</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <div className="font-medium">Data Source</div>
              <div className="text-gray-600">{tempStickService.getConfiguration().dataSource}</div>
            </div>
            <div>
              <div className="font-medium">Rate Limit</div>
              <div className="text-gray-600">{tempStickService.getConfiguration().rateLimit?.requestsPerMinute}/min</div>
            </div>
            <div>
              <div className="font-medium">Retry Attempts</div>
              <div className="text-gray-600">{tempStickService.getConfiguration().retry?.maxAttempts}</div>
            </div>

          </div>
        </div>
      </div>
    </div>
  );
};

export default TempStickTest;