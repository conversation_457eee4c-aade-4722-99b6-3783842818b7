import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../../lib/supabase';
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Alert } from '../ui/alert';
import { Badge } from '../ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '../ui/tabs';
import { AlertTriangle, ThermometerSun, Clock, CheckCircle, XCircle, TrendingUp, FileText, Settings } from 'lucide-react';

// Enhanced interfaces for production-grade HACCP compliance
interface EnhancedCCP {
  id: string;
  ccp_number: string;
  ccp_name: string;
  process_step: string;
  hazard_controlled: string;
  critical_limits: {
    temperature_min?: number;
    temperature_max?: number;
    ph_min?: number;
    ph_max?: number;
    water_activity_max?: number;
    salt_concentration_min?: number;
    time_limit_minutes?: number;
    [key: string]: string | number | undefined;
  };
  monitoring_frequency: string;
  monitoring_method: string;
  monitoring_equipment?: string;
  responsible_person: string;
  record_keeping_requirements: string;
  verification_frequency: string;
  regulatory_references: string[];
  corrective_action_procedure: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface RealTimeReading {
  id: string;
  ccp_id: string;
  sensor_id?: string;
  measurement_type: 'temperature' | 'ph' | 'humidity' | 'pressure' | 'time';
  measurement_value: number;
  measurement_unit: string;
  timestamp: string;
  within_limits: boolean;
  deviation_severity?: 'minor' | 'major' | 'critical';
  auto_corrective_action_triggered?: boolean;
}

interface ComplianceStatus {
  ccp_id: string;
  status: 'compliant' | 'minor_deviation' | 'major_deviation' | 'critical_failure';
  last_monitoring: string;
  consecutive_compliant_readings: number;
  recent_deviations: number;
  trending: 'improving' | 'stable' | 'declining';
  next_verification_due: string;
  calibration_status: 'current' | 'due_soon' | 'overdue';
}

interface EnhancedCCPManagementProps {
  productId?: string;
  lotId?: string;
  viewMode?: 'monitoring' | 'configuration' | 'audit';
  enableRealTimeMonitoring?: boolean;
  enableIoTIntegration?: boolean;
}

export default function EnhancedCCPManagement({
  productId,
  lotId,
  viewMode = 'monitoring',
  enableRealTimeMonitoring = true,
  enableIoTIntegration = false
}: EnhancedCCPManagementProps) {
  // Remove unused parameters to prevent TypeScript warnings
  void enableIoTIntegration;
  // State management for enhanced CCP system
  const [ccps, setCcps] = useState<EnhancedCCP[]>([]);
  const [selectedCcp, setSelectedCcp] = useState<EnhancedCCP | null>(null);
  const [realtimeReadings, setRealtimeReadings] = useState<RealTimeReading[]>([]);
  const [complianceStatus, setComplianceStatus] = useState<ComplianceStatus[]>([]);
  // const [activeAlerts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [monitoringActive, setMonitoringActive] = useState(false);
  // const [autoCorrectiveActions] = useState(true);
  
  // Real-time monitoring configuration
  const [monitoringInterval] = useState(60000); // 1 minute default
  // const [alertThresholds] = useState({
  //   minor: 0.1, // 10% deviation
  //   major: 0.2, // 20% deviation
  //   critical: 0.3 // 30% deviation
  // });

  // Load CCPs with enhanced metadata
  const loadEnhancedCCPs = useCallback(async () => {
    try {
      setLoading(true);
      let query = supabase
        .from('critical_control_points')
        .select(`
          *,
          hazard_analysis!inner(
            hazard_description,
            severity,
            likelihood
          )
        `)
        .eq('is_active', true)
        .order('ccp_number');

      if (productId) {
        query = query.eq('product_id', productId);
      }

      const { data, error } = await query;
      if (error) throw error;

      // Enhance CCPs with additional metadata
      const enhancedCCPs = (data || []).map(ccp => ({
        ...ccp,
        regulatory_references: ccp.regulatory_references || ['21 CFR 123', 'FDA FSMA 204'],
        corrective_action_procedure: ccp.corrective_action_procedure || 'Follow standard corrective action protocol'
      }));

      setCcps(enhancedCCPs);
      if (enhancedCCPs.length > 0) {
        setSelectedCcp(enhancedCCPs[0]);
      }

      // Load compliance status for each CCP
      await loadComplianceStatus(enhancedCCPs.map(ccp => ccp.id));
      
    } catch (err) {
      console.error('Failed to load enhanced CCPs:', err);
      setError(err instanceof Error ? err.message : 'Failed to load CCPs');
    } finally {
      setLoading(false);
    }
  }, [productId]);

  // Load compliance status for CCPs
  const loadComplianceStatus = async (ccpIds: string[]) => {
    try {
      const statusPromises = ccpIds.map(async (ccpId) => {
        // Get recent monitoring logs to determine compliance status
        const { data: recentLogs } = await supabase
          .from('ccp_monitoring_logs')
          .select('*')
          .eq('ccp_id', ccpId)
          .order('monitoring_timestamp', { ascending: false })
          .limit(10);

        // Calculate compliance metrics
        const compliantReadings = recentLogs?.filter(log => log.within_critical_limits).length || 0;
        const totalReadings = recentLogs?.length || 0;
        const recentDeviations = totalReadings - compliantReadings;
        
        // Determine overall status
        let status: ComplianceStatus['status'] = 'compliant';
        if (recentDeviations > 0) {
          const deviationRate = recentDeviations / Math.max(totalReadings, 1);
          if (deviationRate > 0.3) status = 'critical_failure';
          else if (deviationRate > 0.1) status = 'major_deviation';
          else status = 'minor_deviation';
        }

        // Get next verification due date
        const { data: verificationData } = await supabase
          .from('verification_activities')
          .select('next_due')
          .eq('ccp_id', ccpId)
          .order('next_due')
          .limit(1);

        const trending: ComplianceStatus['trending'] = recentDeviations === 0 ? 'stable' : 'declining';
        const calibration_status: ComplianceStatus['calibration_status'] = 'current';
        
        return {
          ccp_id: ccpId,
          status,
          last_monitoring: recentLogs?.[0]?.monitoring_timestamp || new Date().toISOString(),
          consecutive_compliant_readings: compliantReadings,
          recent_deviations: recentDeviations,
          trending,
          next_verification_due: verificationData?.[0]?.next_due || new Date().toISOString(),
          calibration_status // TODO: Implement calibration tracking
        };
      });

      const statusResults = await Promise.all(statusPromises);
      setComplianceStatus(statusResults);
    } catch (err) {
      console.error('Failed to load compliance status:', err);
    }
  };

  // Real-time monitoring setup
  useEffect(() => {
    if (!enableRealTimeMonitoring || !selectedCcp) return;

    const startRealTimeMonitoring = () => {
      const interval = setInterval(async () => {
        try {
          // Simulate IoT sensor readings (replace with actual IoT integration)
          const mockReading: RealTimeReading = {
            id: `reading-${Date.now()}`,
            ccp_id: selectedCcp.id,
            sensor_id: `sensor-${selectedCcp.ccp_number}`,
            measurement_type: 'temperature',
            measurement_value: -1.5 + (Math.random() * 2), // -2.5 to 0.5°C
            measurement_unit: '°C',
            timestamp: new Date().toISOString(),
            within_limits: true,
          };

          // Check against critical limits
          const limits = selectedCcp.critical_limits;
          if (limits.temperature_min !== undefined && mockReading.measurement_value < limits.temperature_min) {
            mockReading.within_limits = false;
            mockReading.deviation_severity = 'major';
          }
          if (limits.temperature_max !== undefined && mockReading.measurement_value > limits.temperature_max) {
            mockReading.within_limits = false;
            mockReading.deviation_severity = 'major';
          }

          // Update real-time readings
          setRealtimeReadings(prev => [mockReading, ...prev.slice(0, 49)]); // Keep last 50 readings

          // Log to database
          const { error } = await supabase.from('ccp_monitoring_logs').insert({
            ccp_id: selectedCcp.id,
            lot_id: lotId || null,
            measurements: { [mockReading.measurement_type]: mockReading.measurement_value },
            within_critical_limits: mockReading.within_limits,
            monitored_by: 'Automated IoT System',
            monitoring_equipment_id: mockReading.sensor_id,
            notes: mockReading.within_limits ? null : `Automated deviation detected: ${mockReading.deviation_severity}`
          });

          if (error) {
            console.error('Failed to log monitoring reading:', error);
          }

        } catch (err) {
          console.error('Real-time monitoring error:', err);
        }
      }, monitoringInterval);

      return interval;
    };

    let interval: NodeJS.Timeout | null = null;
    if (monitoringActive) {
      interval = startRealTimeMonitoring();
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [selectedCcp, monitoringActive, monitoringInterval, lotId, enableRealTimeMonitoring]);

  // Load initial data
  useEffect(() => {
    loadEnhancedCCPs();
  }, [loadEnhancedCCPs]);

  // Get CCP status badge
  const getCCPStatusBadge = (ccpId: string) => {
    const status = complianceStatus.find(s => s.ccp_id === ccpId);
    if (!status) return <Badge variant="outline">Unknown</Badge>;

    const variants = {
      compliant: 'default',
      minor_deviation: 'secondary',
      major_deviation: 'destructive',
      critical_failure: 'destructive'
    } as const;

    const icons = {
      compliant: <CheckCircle className="w-3 h-3" />,
      minor_deviation: <AlertTriangle className="w-3 h-3" />,
      major_deviation: <XCircle className="w-3 h-3" />,
      critical_failure: <XCircle className="w-3 h-3" />
    };

    return (
      <Badge variant={variants[status.status]} className="flex items-center gap-1">
        {icons[status.status]}
        {status.status.replace('_', ' ').toUpperCase()}
      </Badge>
    );
  };

  // Render loading state
  if (loading) {
    return (
      <Card className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          <div className="h-4 bg-gray-200 rounded w-2/3"></div>
        </div>
      </Card>
    );
  }

  // Render error state
  if (error) {
    return (
      <Alert className="border-red-200 bg-red-50">
        <AlertTriangle className="h-4 w-4" />
        <div className="text-red-800">{error}</div>
      </Alert>
    );
  }

  // Render no CCPs state
  if (ccps.length === 0) {
    return (
      <Card className="p-6 text-center">
        <Settings className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">No Critical Control Points Configured</h3>
        <p className="text-gray-600 mb-4">Set up CCPs to monitor critical limits and ensure HACCP compliance.</p>
        <Button onClick={() => window.location.href = '/haccp-setup'}>Configure CCPs</Button>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Enhanced Header with Real-time Status */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">HACCP Critical Control Points</h2>
          <p className="text-sm text-gray-600 mt-1">
            Real-time monitoring and compliance management • {ccps.length} CCPs configured
          </p>
        </div>
        <div className="flex items-center gap-3">
          {enableRealTimeMonitoring && (
            <Button
              onClick={() => setMonitoringActive(!monitoringActive)}
              variant={monitoringActive ? 'default' : 'outline'}
              className="flex items-center gap-2"
            >
              <TrendingUp className="w-4 h-4" />
              {monitoringActive ? 'Stop Monitoring' : 'Start Monitoring'}
            </Button>
          )}
          <Badge variant={monitoringActive ? 'default' : 'secondary'} className="flex items-center gap-1">
            <div className={`w-2 h-2 rounded-full ${monitoringActive ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`}></div>
            {monitoringActive ? 'Live' : 'Offline'}
          </Badge>
        </div>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={viewMode} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="monitoring" className="flex items-center gap-2">
            <ThermometerSun className="w-4 h-4" />
            Monitoring
          </TabsTrigger>
          <TabsTrigger value="configuration" className="flex items-center gap-2">
            <Settings className="w-4 h-4" />
            Configuration
          </TabsTrigger>
          <TabsTrigger value="audit" className="flex items-center gap-2">
            <FileText className="w-4 h-4" />
            Audit Trail
          </TabsTrigger>
        </TabsList>

        <TabsContent value="monitoring" className="space-y-6">
          {/* CCP Selection Grid */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Critical Control Points Overview</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {ccps.map((ccp) => {
                const status = complianceStatus.find(s => s.ccp_id === ccp.id);
                return (
                  <button
                    key={ccp.id}
                    onClick={() => setSelectedCcp(ccp)}
                    className={`p-4 rounded-lg border text-left transition-all hover:shadow-md ${
                      selectedCcp?.id === ccp.id
                        ? 'border-blue-500 bg-blue-50 shadow-md'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="font-semibold text-sm text-blue-600">{ccp.ccp_number}</div>
                      {getCCPStatusBadge(ccp.id)}
                    </div>
                    <div className="font-medium text-gray-900 mb-1">{ccp.ccp_name}</div>
                    <div className="text-sm text-gray-600 mb-2">{ccp.process_step}</div>
                    <div className="text-xs text-gray-500">
                      Hazard: {ccp.hazard_controlled}
                    </div>
                    {status && (
                      <div className="mt-3 pt-3 border-t border-gray-100">
                        <div className="flex items-center justify-between text-xs">
                          <span className="text-gray-500">Compliance Rate</span>
                          <span className={`font-medium ${
                            status.status === 'compliant' ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {Math.round((status.consecutive_compliant_readings / (status.consecutive_compliant_readings + status.recent_deviations)) * 100) || 0}%
                          </span>
                        </div>
                        <div className="flex items-center justify-between text-xs mt-1">
                          <span className="text-gray-500">Last Check</span>
                          <span className="text-gray-600">
                            {new Date(status.last_monitoring).toLocaleTimeString()}
                          </span>
                        </div>
                      </div>
                    )}
                  </button>
                );
              })}
            </div>
          </Card>

          {/* Real-time Monitoring Panel */}
          {selectedCcp && enableRealTimeMonitoring && (
            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">
                  Real-time Monitoring: {selectedCcp.ccp_number}
                </h3>
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-gray-500" />
                  <span className="text-sm text-gray-600">
                    Updated every {monitoringInterval / 1000}s
                  </span>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Current Reading */}
                <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-blue-700">Current Reading</span>
                    <ThermometerSun className="w-5 h-5 text-blue-600" />
                  </div>
                  <div className="text-2xl font-bold text-blue-900">
                    {realtimeReadings[0]?.measurement_value?.toFixed(1) || '--'}
                    {realtimeReadings[0]?.measurement_unit || '°C'}
                  </div>
                  <div className="text-xs text-blue-600 mt-1">
                    {realtimeReadings[0]?.within_limits ? '✓ Within Limits' : '⚠ Deviation Detected'}
                  </div>
                </div>

                {/* Critical Limits */}
                <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-yellow-700">Critical Limits</span>
                    <AlertTriangle className="w-5 h-5 text-yellow-600" />
                  </div>
                  <div className="space-y-1">
                    {selectedCcp.critical_limits.temperature_min !== undefined && (
                      <div className="text-sm text-yellow-900">
                        Min: {selectedCcp.critical_limits.temperature_min}°C
                      </div>
                    )}
                    {selectedCcp.critical_limits.temperature_max !== undefined && (
                      <div className="text-sm text-yellow-900">
                        Max: {selectedCcp.critical_limits.temperature_max}°C
                      </div>
                    )}
                  </div>
                </div>

                {/* Compliance Status */}
                <div className={`bg-gradient-to-br rounded-lg p-4 ${
                  complianceStatus.find(s => s.ccp_id === selectedCcp.id)?.status === 'compliant'
                    ? 'from-green-50 to-green-100'
                    : 'from-red-50 to-red-100'
                }`}>
                  <div className="flex items-center justify-between mb-2">
                    <span className={`text-sm font-medium ${
                      complianceStatus.find(s => s.ccp_id === selectedCcp.id)?.status === 'compliant'
                        ? 'text-green-700'
                        : 'text-red-700'
                    }`}>Compliance Status</span>
                    {complianceStatus.find(s => s.ccp_id === selectedCcp.id)?.status === 'compliant'
                      ? <CheckCircle className="w-5 h-5 text-green-600" />
                      : <XCircle className="w-5 h-5 text-red-600" />
                    }
                  </div>
                  <div className={`text-lg font-bold ${
                    complianceStatus.find(s => s.ccp_id === selectedCcp.id)?.status === 'compliant'
                      ? 'text-green-900'
                      : 'text-red-900'
                  }`}>
                    {complianceStatus.find(s => s.ccp_id === selectedCcp.id)?.status?.replace('_', ' ').toUpperCase() || 'CHECKING'}
                  </div>
                </div>
              </div>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="configuration">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">CCP Configuration (Coming Soon)</h3>
            <p className="text-gray-600">
              Advanced configuration panel for managing CCPs, critical limits, and monitoring parameters.
            </p>
          </Card>
        </TabsContent>

        <TabsContent value="audit">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Audit Trail (Coming Soon)</h3>
            <p className="text-gray-600">
              Comprehensive audit trail with digital signatures and regulatory reporting.
            </p>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}