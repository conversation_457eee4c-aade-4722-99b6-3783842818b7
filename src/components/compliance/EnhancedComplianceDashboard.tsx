import { useState, useEffect, useCallback } from 'react';
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Alert } from '../ui/alert';
import { Badge } from '../ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '../ui/tabs';
import { 
  Shield,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  FileText,
  Thermometer,
  Activity,
  BarChart3,
  Download,
  Scale
} from 'lucide-react';

// Import our specialized compliance components
import EnhancedCCPManagement from './EnhancedCCPManagement';
import RegulatoryComplianceFramework from './RegulatoryComplianceFramework';
import DocumentManagementSystem from './DocumentManagementSystem';
import RealTimeMonitoringDashboard from './RealTimeMonitoringDashboard';

// Dashboard overview interfaces
interface ComplianceOverview {
  overall_compliance_score: number;
  critical_issues: number;
  pending_actions: number;
  expiring_documents: number;
  active_ccps: number;
  compliant_ccps: number;
  offline_sensors: number;
  recent_alerts: number;
  last_assessment_date: string;
  next_assessment_due: string;
  regulatory_frameworks: {
    name: string;
    compliance_score: number;
    status: 'compliant' | 'non_compliant' | 'needs_attention';
  }[];
}

interface ComplianceMetric {
  metric_name: string;
  current_value: number;
  target_value: number;
  trend: 'up' | 'down' | 'stable';
  change_percentage: number;
  status: 'good' | 'warning' | 'critical';
  last_updated: string;
}

interface UpcomingDeadline {
  id: string;
  title: string;
  type: 'document_expiry' | 'assessment_due' | 'calibration_due' | 'training_due' | 'regulatory_filing';
  due_date: string;
  days_remaining: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
  assigned_to: string;
  description: string;
}

interface RecentActivity {
  id: string;
  activity_type: 'ccp_deviation' | 'document_approved' | 'assessment_completed' | 'sensor_alert' | 'corrective_action';
  title: string;
  description: string;
  performed_by: string;
  timestamp: string;
  severity?: 'info' | 'warning' | 'critical';
}

export default function EnhancedComplianceDashboard() {
  // State management
  const [overview, setOverview] = useState<ComplianceOverview | null>(null);
  const [metrics, setMetrics] = useState<ComplianceMetric[]>([]);
  const [upcomingDeadlines, setUpcomingDeadlines] = useState<UpcomingDeadline[]>([]);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [refreshTime, setRefreshTime] = useState<Date>(new Date());

  // Load compliance dashboard data
  const loadComplianceData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // In a production environment, these would be actual database queries
      // For now, we'll use mock data to demonstrate the interface

      // Mock compliance overview
      const mockOverview: ComplianceOverview = {
        overall_compliance_score: 87,
        critical_issues: 2,
        pending_actions: 5,
        expiring_documents: 3,
        active_ccps: 12,
        compliant_ccps: 10,
        offline_sensors: 1,
        recent_alerts: 4,
        last_assessment_date: '2024-07-15',
        next_assessment_due: '2024-10-15',
        regulatory_frameworks: [
          { name: 'FDA HACCP', compliance_score: 92, status: 'compliant' },
          { name: 'GDST Traceability', compliance_score: 85, status: 'compliant' },
          { name: 'USDC Inspection', compliance_score: 78, status: 'needs_attention' },
          { name: 'State Requirements', compliance_score: 95, status: 'compliant' }
        ]
      };

      // Mock metrics
      const mockMetrics: ComplianceMetric[] = [
        {
          metric_name: 'CCP Compliance Rate',
          current_value: 83.3,
          target_value: 95.0,
          trend: 'up',
          change_percentage: 5.2,
          status: 'warning',
          last_updated: new Date().toISOString()
        },
        {
          metric_name: 'Document Completion',
          current_value: 94.7,
          target_value: 100.0,
          trend: 'stable',
          change_percentage: 0.1,
          status: 'good',
          last_updated: new Date().toISOString()
        },
        {
          metric_name: 'Sensor Uptime',
          current_value: 98.2,
          target_value: 99.5,
          trend: 'down',
          change_percentage: -1.3,
          status: 'warning',
          last_updated: new Date().toISOString()
        },
        {
          metric_name: 'Training Compliance',
          current_value: 100.0,
          target_value: 100.0,
          trend: 'stable',
          change_percentage: 0.0,
          status: 'good',
          last_updated: new Date().toISOString()
        }
      ];

      // Mock upcoming deadlines
      const mockDeadlines: UpcomingDeadline[] = [
        {
          id: '1',
          title: 'HACCP Plan Review',
          type: 'assessment_due',
          due_date: '2024-09-01',
          days_remaining: 11,
          priority: 'high',
          assigned_to: 'Quality Manager',
          description: 'Annual HACCP plan review and update required'
        },
        {
          id: '2',
          title: 'pH Meter Calibration',
          type: 'calibration_due',
          due_date: '2024-08-25',
          days_remaining: 4,
          priority: 'critical',
          assigned_to: 'Maintenance Team',
          description: 'Brine solution pH meter requires recalibration'
        },
        {
          id: '3',
          title: 'Food Safety Training',
          type: 'training_due',
          due_date: '2024-09-15',
          days_remaining: 25,
          priority: 'medium',
          assigned_to: 'HR Department',
          description: 'Quarterly food safety training for processing staff'
        }
      ];

      // Mock recent activity
      const mockActivity: RecentActivity[] = [
        {
          id: '1',
          activity_type: 'ccp_deviation',
          title: 'Temperature Deviation Detected',
          description: 'Cold storage temperature exceeded 2°C for 15 minutes',
          performed_by: 'Automated System',
          timestamp: new Date(Date.now() - 1800000).toISOString(), // 30 minutes ago
          severity: 'warning'
        },
        {
          id: '2',
          activity_type: 'document_approved',
          title: 'SOP Document Approved',
          description: 'Receiving inspection procedures updated and approved',
          performed_by: 'John Smith',
          timestamp: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
          severity: 'info'
        },
        {
          id: '3',
          activity_type: 'sensor_alert',
          title: 'pH Sensor Offline',
          description: 'Brine solution pH monitor has been offline for 30 minutes',
          performed_by: 'Monitoring System',
          timestamp: new Date(Date.now() - 1800000).toISOString(),
          severity: 'critical'
        },
        {
          id: '4',
          activity_type: 'corrective_action',
          title: 'Corrective Action Completed',
          description: 'Replaced faulty temperature sensor in cold storage unit 2',
          performed_by: 'Maintenance Team',
          timestamp: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
          severity: 'info'
        }
      ];

      setOverview(mockOverview);
      setMetrics(mockMetrics);
      setUpcomingDeadlines(mockDeadlines);
      setRecentActivity(mockActivity);
      setRefreshTime(new Date());

    } catch (err) {
      console.error('Failed to load compliance data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load compliance data');
    } finally {
      setLoading(false);
    }
  }, []);

  // Auto-refresh data every 5 minutes
  useEffect(() => {
    loadComplianceData();
    
    const interval = setInterval(() => {
      loadComplianceData();
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, [loadComplianceData]);

  // Get compliance score color
  const getComplianceColor = (score: number) => {
    if (score >= 95) return 'text-green-600';
    if (score >= 85) return 'text-yellow-600';
    if (score >= 75) return 'text-orange-600';
    return 'text-red-600';
  };

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'border-red-500 bg-red-50 text-red-700';
      case 'high': return 'border-orange-500 bg-orange-50 text-orange-700';
      case 'medium': return 'border-yellow-500 bg-yellow-50 text-yellow-700';
      default: return 'border-blue-500 bg-blue-50 text-blue-700';
    }
  };

  // Get activity icon
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'ccp_deviation': return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case 'document_approved': return <FileText className="w-4 h-4 text-green-500" />;
      case 'sensor_alert': return <Thermometer className="w-4 h-4 text-orange-500" />;
      case 'corrective_action': return <CheckCircle className="w-4 h-4 text-blue-500" />;
      default: return <Activity className="w-4 h-4 text-gray-500" />;
    }
  };

  // Render loading state
  if (loading) {
    return (
      <Card className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </Card>
    );
  }

  // Render error state
  if (error) {
    return (
      <Alert className="border-red-200 bg-red-50">
        <AlertTriangle className="h-4 w-4" />
        <div className="text-red-800">{error}</div>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">HACCP Compliance Dashboard</h1>
          <p className="text-sm text-gray-600 mt-1">
            Comprehensive seafood safety compliance management • Last updated: {refreshTime.toLocaleString()}
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button onClick={loadComplianceData} variant="outline" className="flex items-center gap-2">
            <Activity className="w-4 h-4" />
            Refresh
          </Button>
          <Button className="flex items-center gap-2">
            <Download className="w-4 h-4" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Critical Alerts */}
      {overview && overview.critical_issues > 0 && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4" />
          <div className="flex items-center justify-between w-full">
            <div className="text-red-800">
              <strong>{overview.critical_issues} Critical Issue{overview.critical_issues !== 1 ? 's' : ''} Require Immediate Attention</strong>
              <div className="text-sm mt-1">
                pH sensor offline, temperature deviation in cold storage
              </div>
            </div>
            <Button variant="outline" size="sm" className="ml-4">
              View Details
            </Button>
          </div>
        </Alert>
      )}

      {/* Main Dashboard Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="ccp-management" className="flex items-center gap-2">
            <Shield className="w-4 h-4" />
            CCP Management
          </TabsTrigger>
          <TabsTrigger value="regulatory" className="flex items-center gap-2">
            <Scale className="w-4 h-4" />
            Regulatory
          </TabsTrigger>
          <TabsTrigger value="documents" className="flex items-center gap-2">
            <FileText className="w-4 h-4" />
            Documents
          </TabsTrigger>
          <TabsTrigger value="monitoring" className="flex items-center gap-2">
            <Activity className="w-4 h-4" />
            Monitoring
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Overview Cards */}
          {overview && (
            <>
              {/* Key Metrics Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card className="p-6">
                  <div className="flex items-center gap-4">
                    <div className="p-3 rounded-full bg-blue-100">
                      <Shield className="w-8 h-8 text-blue-600" />
                    </div>
                    <div>
                      <div className={`text-3xl font-bold ${getComplianceColor(overview.overall_compliance_score)}`}>
                        {overview.overall_compliance_score}%
                      </div>
                      <div className="text-sm text-gray-600">Overall Compliance</div>
                    </div>
                  </div>
                </Card>

                <Card className="p-6">
                  <div className="flex items-center gap-4">
                    <div className="p-3 rounded-full bg-red-100">
                      <AlertTriangle className="w-8 h-8 text-red-600" />
                    </div>
                    <div>
                      <div className="text-3xl font-bold text-red-600">{overview.critical_issues}</div>
                      <div className="text-sm text-gray-600">Critical Issues</div>
                    </div>
                  </div>
                </Card>

                <Card className="p-6">
                  <div className="flex items-center gap-4">
                    <div className="p-3 rounded-full bg-yellow-100">
                      <Clock className="w-8 h-8 text-yellow-600" />
                    </div>
                    <div>
                      <div className="text-3xl font-bold text-yellow-600">{overview.pending_actions}</div>
                      <div className="text-sm text-gray-600">Pending Actions</div>
                    </div>
                  </div>
                </Card>

                <Card className="p-6">
                  <div className="flex items-center gap-4">
                    <div className="p-3 rounded-full bg-green-100">
                      <CheckCircle className="w-8 h-8 text-green-600" />
                    </div>
                    <div>
                      <div className="text-3xl font-bold text-green-600">
                        {overview.compliant_ccps}/{overview.active_ccps}
                      </div>
                      <div className="text-sm text-gray-600">Compliant CCPs</div>
                    </div>
                  </div>
                </Card>
              </div>

              {/* Regulatory Framework Status */}
              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">Regulatory Framework Compliance</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {overview.regulatory_frameworks.map((framework, index) => (
                    <div key={index} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium">{framework.name}</span>
                        <Badge variant={
                          framework.status === 'compliant' ? 'default' :
                          framework.status === 'needs_attention' ? 'secondary' : 'destructive'
                        }>
                          {framework.status.replace('_', ' ').toUpperCase()}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="flex-1">
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className={`h-2 rounded-full ${
                                framework.compliance_score >= 90 ? 'bg-green-500' :
                                framework.compliance_score >= 80 ? 'bg-yellow-500' : 'bg-red-500'
                              }`}
                              style={{ width: `${framework.compliance_score}%` }}
                            ></div>
                          </div>
                        </div>
                        <span className={`text-sm font-medium ${getComplianceColor(framework.compliance_score)}`}>
                          {framework.compliance_score}%
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            </>
          )}

          {/* Key Performance Metrics */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Key Performance Metrics</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {metrics.map((metric, index) => (
                <div key={index} className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium">{metric.metric_name}</span>
                    <div className="flex items-center gap-1">
                      {metric.trend === 'up' ? (
                        <TrendingUp className="w-4 h-4 text-green-500" />
                      ) : metric.trend === 'down' ? (
                        <TrendingDown className="w-4 h-4 text-red-500" />
                      ) : (
                        <div className="w-4 h-4"></div>
                      )}
                      <span className={`text-xs ${
                        metric.trend === 'up' ? 'text-green-600' :
                        metric.trend === 'down' ? 'text-red-600' : 'text-gray-600'
                      }`}>
                        {metric.change_percentage > 0 ? '+' : ''}{metric.change_percentage}%
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="text-2xl font-bold">
                      <span className={
                        metric.status === 'good' ? 'text-green-600' :
                        metric.status === 'warning' ? 'text-yellow-600' : 'text-red-600'
                      }>
                        {metric.current_value.toFixed(1)}%
                      </span>
                    </div>
                    <div className="flex-1">
                      <div className="text-xs text-gray-500 mb-1">
                        Target: {metric.target_value.toFixed(1)}%
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${
                            metric.status === 'good' ? 'bg-green-500' :
                            metric.status === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${(metric.current_value / metric.target_value) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>

          {/* Two Column Layout for Deadlines and Activity */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Upcoming Deadlines */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Upcoming Deadlines</h3>
              <div className="space-y-3">
                {upcomingDeadlines.map((deadline) => (
                  <div key={deadline.id} className={`p-3 border-l-4 rounded-r-lg ${getPriorityColor(deadline.priority)}`}>
                    <div className="flex items-center justify-between mb-1">
                      <span className="font-medium">{deadline.title}</span>
                      <Badge variant="outline" className="text-xs">
                        {deadline.days_remaining} days
                      </Badge>
                    </div>
                    <div className="text-sm opacity-90 mb-1">{deadline.description}</div>
                    <div className="flex items-center justify-between text-xs">
                      <span>Assigned: {deadline.assigned_to}</span>
                      <span>Due: {new Date(deadline.due_date).toLocaleDateString()}</span>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* Recent Activity */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Recent Activity</h3>
              <div className="space-y-3">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-start gap-3 p-3 border rounded-lg">
                    <div className="pt-1">
                      {getActivityIcon(activity.activity_type)}
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-sm">{activity.title}</div>
                      <div className="text-xs text-gray-600 mb-1">{activity.description}</div>
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>{activity.performed_by}</span>
                        <span>{new Date(activity.timestamp).toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="ccp-management">
          <EnhancedCCPManagement />
        </TabsContent>

        <TabsContent value="regulatory">
          <RegulatoryComplianceFramework />
        </TabsContent>

        <TabsContent value="documents">
          <DocumentManagementSystem />
        </TabsContent>

        <TabsContent value="monitoring">
          <RealTimeMonitoringDashboard />
        </TabsContent>
      </Tabs>
    </div>
  );
}