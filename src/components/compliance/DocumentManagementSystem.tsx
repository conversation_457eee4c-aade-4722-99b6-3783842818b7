import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../../lib/supabase';
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from '../ui/tabs';
import { 
  FileText, 
  Upload, 
  Download, 
  Search, 
  Filter,
  Eye,
  Clock,
  Shield,
  Archive,
  Share,
  CheckCircle,
  AlertTriangle,
  Fingerprint,
  User,
  Scale,
  FileCheck
} from 'lucide-react';

// Document management interfaces for regulatory compliance
interface ComplianceDocument {
  id: string;
  title: string;
  document_type: 'haccp_plan' | 'sop' | 'calibration_cert' | 'audit_report' | 'training_record' | 'corrective_action' | 'verification_report' | 'regulatory_filing';
  regulatory_category: 'fda_haccp' | 'gdst_traceability' | 'usdc_inspection' | 'state_requirement' | 'facility_license';
  file_path: string;
  file_size: number;
  mime_type: string;
  version: number;
  status: 'draft' | 'under_review' | 'approved' | 'superseded' | 'archived';
  retention_period_years: number;
  created_by: string;
  approved_by?: string;
  approved_at?: string;
  digital_signature?: DigitalSignature;
  metadata: DocumentMetadata;
  tags: string[];
  access_level: 'public' | 'restricted' | 'confidential';
  encryption_status: boolean;
  blockchain_hash?: string;
  audit_trail: DocumentAuditEntry[];
  created_at: string;
  updated_at: string;
  retention_end_date: string;
  auto_delete_eligible: boolean;
}

interface DigitalSignature {
  signature_id: string;
  signer_name: string;
  signer_email: string;
  signature_timestamp: string;
  signature_method: 'electronic' | 'digital_certificate' | 'biometric';
  certificate_authority?: string;
  signature_hash: string;
  verification_status: 'valid' | 'invalid' | 'expired' | 'pending';
  signature_image_url?: string;
}

interface DocumentMetadata {
  product_ids?: string[];
  lot_ids?: string[];
  ccp_ids?: string[];
  inspection_date?: string;
  inspector_name?: string;
  facility_id?: string;
  equipment_ids?: string[];
  temperature_range?: { min: number; max: number };
  ph_range?: { min: number; max: number };
  corrective_actions_taken?: string[];
  next_review_date?: string;
  regulatory_deadline?: string;
  submission_required?: boolean;
  external_reference?: string;
}

interface DocumentAuditEntry {
  id: string;
  action: 'created' | 'viewed' | 'downloaded' | 'modified' | 'approved' | 'superseded' | 'archived' | 'deleted';
  performed_by: string;
  timestamp: string;
  ip_address?: string;
  user_agent?: string;
  details?: string;
}




export default function DocumentManagementSystem() {
  // State management
  const [documents, setDocuments] = useState<ComplianceDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('documents');

  // Document statistics
  const [stats, setStats] = useState({
    total_documents: 0,
    pending_approval: 0,
    expiring_soon: 0,
    digitally_signed: 0,
    average_retention_years: 0
  });

  // Load documents and related data
  const loadDocuments = useCallback(async () => {
    try {
      setLoading(true);
      
      // Load compliance documents
      const query = supabase
        .from('compliance_documents')
        .select(`
          *,
          digital_signatures(*),
          document_audit_trail(*)
        `)
        .order('created_at', { ascending: false });

      // Apply search filters if needed in the future
      // Additional filters can be added here

      const { data, error } = await query;
      if (error) throw error;

      setDocuments(data || []);
      calculateStatistics(data || []);
      
    } catch (err) {
      console.error('Failed to load documents:', err);
    } finally {
      setLoading(false);
    }
  }, []);


  // Calculate document statistics
  const calculateStatistics = (docs: ComplianceDocument[]) => {
    const stats = {
      total_documents: docs.length,
      pending_approval: docs.filter(d => d.status === 'under_review').length,
      expiring_soon: docs.filter(d => {
        const thirtyDaysFromNow = new Date();
        thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
        return new Date(d.retention_end_date) <= thirtyDaysFromNow;
      }).length,
      digitally_signed: docs.filter(d => d.digital_signature).length,
      average_retention_years: docs.length > 0 
        ? Math.round(docs.reduce((sum, d) => sum + d.retention_period_years, 0) / docs.length)
        : 0
    };
    setStats(stats);
  };




  // Load initial data
  useEffect(() => {
    loadDocuments();
  }, [loadDocuments]);

  // Get document type icon
  const getDocumentTypeIcon = (type: string) => {
    switch (type) {
      case 'haccp_plan': return <Shield className="w-4 h-4" />;
      case 'sop': return <FileText className="w-4 h-4" />;
      case 'calibration_cert': return <CheckCircle className="w-4 h-4" />;
      case 'audit_report': return <Eye className="w-4 h-4" />;
      case 'training_record': return <User className="w-4 h-4" />;
      case 'corrective_action': return <AlertTriangle className="w-4 h-4" />;
      case 'verification_report': return <FileCheck className="w-4 h-4" />;
      case 'regulatory_filing': return <Scale className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    const variants = {
      draft: 'outline',
      under_review: 'secondary',
      approved: 'default',
      superseded: 'secondary',
      archived: 'outline'
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'outline'}>
        {status.replace('_', ' ').toUpperCase()}
      </Badge>
    );
  };

  // Render loading state
  if (loading) {
    return (
      <Card className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Statistics */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Document Management System</h2>
          <p className="text-sm text-gray-600 mt-1">
            Regulatory compliance documents with 7-year retention and digital signatures
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            onClick={() => console.log('Upload modal would open')}
            className="flex items-center gap-2"
          >
            <Upload className="w-4 h-4" />
            Upload Document
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <FileText className="w-8 h-8 text-blue-600" />
            <div>
              <div className="text-2xl font-bold text-gray-900">{stats.total_documents}</div>
              <div className="text-sm text-gray-600">Total Documents</div>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <Clock className="w-8 h-8 text-yellow-600" />
            <div>
              <div className="text-2xl font-bold text-gray-900">{stats.pending_approval}</div>
              <div className="text-sm text-gray-600">Pending Approval</div>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <AlertTriangle className="w-8 h-8 text-red-600" />
            <div>
              <div className="text-2xl font-bold text-gray-900">{stats.expiring_soon}</div>
              <div className="text-sm text-gray-600">Expiring Soon</div>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <Fingerprint className="w-8 h-8 text-green-600" />
            <div>
              <div className="text-2xl font-bold text-gray-900">{stats.digitally_signed}</div>
              <div className="text-sm text-gray-600">Digitally Signed</div>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <Archive className="w-8 h-8 text-purple-600" />
            <div>
              <div className="text-2xl font-bold text-gray-900">{stats.average_retention_years}y</div>
              <div className="text-sm text-gray-600">Avg. Retention</div>
            </div>
          </div>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="retention">Retention Policies</TabsTrigger>
          <TabsTrigger value="audit">Audit Trail</TabsTrigger>
        </TabsList>

        <TabsContent value="documents" className="space-y-6">
          {/* Documents Table */}
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Compliance Documents</h3>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <Filter className="w-4 h-4 mr-2" />
                  Filter
                </Button>
                <Button variant="outline" size="sm">
                  <Search className="w-4 h-4 mr-2" />
                  Search
                </Button>
              </div>
            </div>
            
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2">Document</th>
                    <th className="text-left p-2">Type</th>
                    <th className="text-left p-2">Status</th>
                    <th className="text-left p-2">Category</th>
                    <th className="text-left p-2">Created</th>
                    <th className="text-left p-2">Retention</th>
                    <th className="text-left p-2">Signature</th>
                    <th className="text-left p-2">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {documents.map((doc) => (
                    <tr key={doc.id} className="border-b hover:bg-gray-50">
                      <td className="p-2">
                        <div className="flex items-center gap-2">
                          {getDocumentTypeIcon(doc.document_type)}
                          <span className="font-medium">{doc.title}</span>
                        </div>
                      </td>
                      <td className="p-2">{doc.document_type.replace('_', ' ')}</td>
                      <td className="p-2">{getStatusBadge(doc.status)}</td>
                      <td className="p-2"><Badge variant="outline">{doc.regulatory_category}</Badge></td>
                      <td className="p-2">{new Date(doc.created_at).toLocaleDateString()}</td>
                      <td className="p-2">
                        <span className={`text-sm ${
                          new Date(doc.retention_end_date) <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
                            ? 'text-red-600 font-medium'
                            : 'text-gray-600'
                        }`}>
                          {new Date(doc.retention_end_date).toLocaleDateString()}
                        </span>
                      </td>
                      <td className="p-2">
                        {doc.digital_signature ? (
                          <div className="flex items-center gap-1">
                            <Fingerprint className="w-4 h-4 text-green-600" />
                            <span className="text-xs text-green-600">Signed</span>
                          </div>
                        ) : (
                          <span className="text-xs text-gray-400">Not Signed</span>
                        )}
                      </td>
                      <td className="p-2">
                        <div className="flex items-center gap-1">
                          <Button variant="ghost" size="sm">
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Download className="w-4 h-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Share className="w-4 h-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="templates">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Document Templates (Coming Soon)</h3>
            <p className="text-gray-600">
              Pre-configured templates for regulatory documents with auto-population and compliance validation.
            </p>
          </Card>
        </TabsContent>

        <TabsContent value="retention">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Retention Policies (Coming Soon)</h3>
            <p className="text-gray-600">
              Automated retention policies with secure destruction and legal hold capabilities.
            </p>
          </Card>
        </TabsContent>

        <TabsContent value="audit">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Document Audit Trail (Coming Soon)</h3>
            <p className="text-gray-600">
              Complete audit trail with tamper-evident logging and regulatory reporting.
            </p>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}