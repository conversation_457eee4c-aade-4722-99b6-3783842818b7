import { useState } from 'react';
import { Tabs } from '../ui/tabs';
import ComplianceDashboard from './ComplianceDashboard';
import CCPMonitoring from './CCPMonitoring';
import TraceabilityChain from './TraceabilityChain';

interface ComplianceInterfaceProps {
  productId?: string;
  lotId?: string;
  tlc?: string;
}

export default function ComplianceInterface({ productId, lotId, tlc }: ComplianceInterfaceProps) {
  const [activeTab, setActiveTab] = useState('dashboard');

  const tabs = [
    {
      id: 'dashboard',
      label: 'Compliance Dashboard',
      component: <ComplianceDashboard />
    },
    {
      id: 'ccp-monitoring',
      label: 'CCP Monitoring',
      component: <CCPMonitoring productId={productId} lotId={lotId} />
    },
    {
      id: 'traceability',
      label: 'Traceability Chain',
      component: <TraceabilityChain lotId={lotId} tlc={tlc} />
    },
    {
      id: 'regulatory',
      label: 'Regulatory Reporting',
      component: (
        <div className="p-6 bg-white rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Regulatory Reporting</h2>
          <div className="space-y-4">
            <div className="p-4 border border-blue-200 bg-blue-50 rounded-lg">
              <h3 className="font-medium text-blue-900">FDA 24-Hour Rule</h3>
              <p className="text-sm text-blue-700 mt-1">
                Automated compliance reporting for FDA traceability requests
              </p>
              <button className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                Generate FDA Report
              </button>
            </div>
            <div className="p-4 border border-green-200 bg-green-50 rounded-lg">
              <h3 className="font-medium text-green-900">GDST Data Export</h3>
              <p className="text-sm text-green-700 mt-1">
                Export traceability data in GDST 1.2 compliant format
              </p>
              <button className="mt-2 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">
                Export GDST Data
              </button>
            </div>
            <div className="p-4 border border-purple-200 bg-purple-50 rounded-lg">
              <h3 className="font-medium text-purple-900">SIMP Documentation</h3>
              <p className="text-sm text-purple-700 mt-1">
                Seafood Import Monitoring Program compliance documentation
              </p>
              <button className="mt-2 px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700">
                Generate SIMP Report
              </button>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'recall',
      label: 'Recall Management',
      component: (
        <div className="p-6 bg-white rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Recall Management</h2>
          <div className="space-y-4">
            <div className="p-4 border border-red-200 bg-red-50 rounded-lg">
              <h3 className="font-medium text-red-900">Active Recalls</h3>
              <p className="text-sm text-red-700 mt-1">
                No active recalls at this time
              </p>
            </div>
            <div className="p-4 border border-orange-200 bg-orange-50 rounded-lg">
              <h3 className="font-medium text-orange-900">Trace-Back Capabilities</h3>
              <p className="text-sm text-orange-700 mt-1">
                Rapid product tracing for recall scenarios
              </p>
              <button className="mt-2 px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700">
                Initiate Trace-Back
              </button>
            </div>
            <div className="p-4 border border-yellow-200 bg-yellow-50 rounded-lg">
              <h3 className="font-medium text-yellow-900">Customer Notifications</h3>
              <p className="text-sm text-yellow-700 mt-1">
                Automated customer notification system
              </p>
              <button className="mt-2 px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700">
                Manage Notifications
              </button>
            </div>
          </div>
        </div>
      )
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">HACCP & Compliance Management</h1>
              <p className="mt-1 text-sm text-gray-500">
                Comprehensive seafood safety and traceability compliance system
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-sm text-gray-600">System Online</span>
              </div>
              <div className="text-sm text-gray-500">
                Last Updated: {new Date().toLocaleTimeString()}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <div className="border-b border-gray-200 mb-8">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-3 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          <div className="tab-content">
            {tabs.find(tab => tab.id === activeTab)?.component}
          </div>
        </Tabs>
      </div>

      {/* Quick Actions Footer */}
      <div className="fixed bottom-4 right-4 space-y-2">
        <button
          onClick={() => setActiveTab('ccp-monitoring')}
          className="block w-full px-4 py-2 bg-blue-600 text-white rounded-lg shadow-lg hover:bg-blue-700 transition-colors"
        >
          Quick CCP Log
        </button>
        <button
          onClick={() => setActiveTab('traceability')}
          className="block w-full px-4 py-2 bg-green-600 text-white rounded-lg shadow-lg hover:bg-green-700 transition-colors"
        >
          Trace Product
        </button>
      </div>
    </div>
  );
}