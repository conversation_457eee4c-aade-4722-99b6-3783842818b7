import { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { Card } from '../ui/card';
import { But<PERSON> } from '../ui/button';
import { Alert } from '../ui/alert';
import { Badge } from '../ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '../ui/tabs';
import { 
  Shield, 
  AlertOctagon, 
  Download,
  CheckSquare,
  XSquare,
  Clock,
  Globe,
  Book,
  Scale,
  Thermometer,
  TestTube,
  Users,
  FileText,
  Eye
} from 'lucide-react';

// Regulatory frameworks and their requirements
interface RegulatoryFramework {
  id: string;
  name: string;
  acronym: string;
  authority: string;
  scope: 'federal' | 'state' | 'international';
  jurisdiction: string;
  applicableProducts: string[];
  requirements: ComplianceRequirement[];
  lastUpdated: string;
  nextReviewDate: string;
  mandatoryReporting: boolean;
  inspectionFrequency?: string;
}

interface ComplianceRequirement {
  id: string;
  frameworkId: string;
  category: 'haccp' | 'traceability' | 'labeling' | 'temperature' | 'documentation' | 'testing' | 'facility';
  title: string;
  description: string;
  regulatorySection: string;
  criticalityLevel: 'low' | 'medium' | 'high' | 'critical';
  implementationDeadline?: string;
  verificationMethod: 'inspection' | 'documentation' | 'testing' | 'certification';
  recordRetentionPeriod: number; // in years
  penaltyForNonCompliance: string;
  applicableOperations: string[];
  complianceChecklist: ComplianceChecklistItem[];
}

interface ComplianceChecklistItem {
  id: string;
  requirement: string;
  status: 'not_started' | 'in_progress' | 'completed' | 'non_compliant';
  evidence?: string[];
  lastVerified?: string;
  verifiedBy?: string;
  notes?: string;
  dueDate?: string;
}

interface ComplianceAssessment {
  frameworkId: string;
  overallScore: number;
  categoryScores: Record<string, number>;
  criticalIssues: number;
  majorIssues: number;
  minorIssues: number;
  lastAssessed: string;
  nextAssessmentDue: string;
  assessedBy: string;
  recommendations: string[];
}

interface RegulatoryAlert {
  id: string;
  frameworkId: string;
  alertType: 'deadline_approaching' | 'non_compliance' | 'regulation_update' | 'inspection_scheduled';
  severity: 'info' | 'warning' | 'critical';
  title: string;
  description: string;
  dueDate?: string;
  actionRequired: string;
  assignedTo?: string;
  created: string;
  resolved: boolean;
}

// FDA Seafood HACCP Framework
const FDA_SEAFOOD_HACCP: RegulatoryFramework = {
  id: 'fda-haccp-123',
  name: 'Seafood HACCP Regulations',
  acronym: 'FDA HACCP 123',
  authority: 'Food and Drug Administration',
  scope: 'federal',
  jurisdiction: 'United States',
  applicableProducts: ['fish', 'shellfish', 'crustaceans', 'mollusks', 'processed_seafood'],
  lastUpdated: '2023-12-15',
  nextReviewDate: '2024-12-15',
  mandatoryReporting: true,
  inspectionFrequency: 'biannual',
  requirements: [
    {
      id: 'haccp-plan-req',
      frameworkId: 'fda-haccp-123',
      category: 'haccp',
      title: 'HACCP Plan Development and Implementation',
      description: 'Develop, implement, and maintain a HACCP plan for each product',
      regulatorySection: '21 CFR 123.6',
      criticalityLevel: 'critical',
      verificationMethod: 'inspection',
      recordRetentionPeriod: 7,
      penaltyForNonCompliance: 'Product recall, facility shutdown, civil penalties up to $100,000',
      applicableOperations: ['processing', 'packaging', 'storage', 'distribution'],
      complianceChecklist: [
        {
          id: 'hazard-analysis',
          requirement: 'Conduct hazard analysis for all products and processes',
          status: 'completed'
        },
        {
          id: 'ccp-identification',
          requirement: 'Identify Critical Control Points (CCPs)',
          status: 'completed'
        },
        {
          id: 'critical-limits',
          requirement: 'Establish critical limits for each CCP',
          status: 'in_progress'
        }
      ]
    }
  ]
};

// GDST Traceability Framework
const GDST_TRACEABILITY: RegulatoryFramework = {
  id: 'gdst-1.2',
  name: 'Global Dialogue on Seafood Traceability Standard',
  acronym: 'GDST 1.2',
  authority: 'Global Dialogue on Seafood Traceability',
  scope: 'international',
  jurisdiction: 'Global',
  applicableProducts: ['all_seafood'],
  lastUpdated: '2024-03-01',
  nextReviewDate: '2025-03-01',
  mandatoryReporting: false,
  requirements: [
    {
      id: 'key-data-elements',
      frameworkId: 'gdst-1.2',
      category: 'traceability',
      title: 'Key Data Elements (KDE) Collection',
      description: 'Collect and maintain all required KDEs for complete traceability',
      regulatorySection: 'GDST 1.2 Section 4.1',
      criticalityLevel: 'high',
      verificationMethod: 'documentation',
      recordRetentionPeriod: 5,
      penaltyForNonCompliance: 'Loss of certification, market access restrictions',
      applicableOperations: ['harvesting', 'processing', 'transshipment', 'landing'],
      complianceChecklist: [
        {
          id: 'harvest-event',
          requirement: 'Capture harvest event KDEs',
          status: 'completed'
        },
        {
          id: 'transformation-event',
          requirement: 'Record transformation events',
          status: 'in_progress'
        }
      ]
    }
  ]
};

export default function RegulatoryComplianceFramework() {
  const [frameworks, setFrameworks] = useState<RegulatoryFramework[]>([]);
  const [selectedFramework, setSelectedFramework] = useState<RegulatoryFramework | null>(null);
  const [, setAssessments] = useState<ComplianceAssessment[]>([]);
  const [, setAlerts] = useState<RegulatoryAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  // Initialize with predefined frameworks
  useEffect(() => {
    const initializeFrameworks = async () => {
      try {
        setLoading(true);
        
        // In production, these would be loaded from database
        const predefinedFrameworks = [
          FDA_SEAFOOD_HACCP,
          GDST_TRACEABILITY,
          // Additional frameworks can be added here
        ];

        setFrameworks(predefinedFrameworks);
        setSelectedFramework(predefinedFrameworks[0]);

        // Load existing assessments and alerts
        await loadComplianceData();
        
      } catch (err) {
        console.error('Failed to initialize regulatory frameworks:', err);
      } finally {
        setLoading(false);
      }
    };

    initializeFrameworks();
  }, []);

  const loadComplianceData = async () => {
    try {
      // Load compliance assessments from database
      const { data: assessmentData } = await supabase
        .from('compliance_assessments')
        .select('*')
        .order('last_assessed', { ascending: false });

      if (assessmentData) {
        setAssessments(assessmentData);
      }

      // Load regulatory alerts
      const { data: alertData } = await supabase
        .from('regulatory_alerts')
        .select('*')
        .eq('resolved', false)
        .order('created', { ascending: false });

      if (alertData) {
        setAlerts(alertData);
      }

    } catch (err) {
      console.error('Failed to load compliance data:', err);
    }
  };

  const calculateComplianceScore = (requirements: ComplianceRequirement[]): number => {
    if (!requirements || requirements.length === 0) return 0;

    const totalWeight = requirements.reduce((sum, req) => {
      const weight = req.criticalityLevel === 'critical' ? 4 : 
                    req.criticalityLevel === 'high' ? 3 :
                    req.criticalityLevel === 'medium' ? 2 : 1;
      return sum + weight;
    }, 0);

    const completedWeight = requirements.reduce((sum, req) => {
      const weight = req.criticalityLevel === 'critical' ? 4 : 
                    req.criticalityLevel === 'high' ? 3 :
                    req.criticalityLevel === 'medium' ? 2 : 1;
      
      const completedItems = req.complianceChecklist.filter(item => item.status === 'completed').length;
      const totalItems = req.complianceChecklist.length;
      const completionRatio = totalItems > 0 ? completedItems / totalItems : 0;
      
      return sum + (weight * completionRatio);
    }, 0);

    return Math.round((completedWeight / totalWeight) * 100);
  };

  const getComplianceStatusBadge = (score: number) => {
    if (score >= 95) return <Badge className="bg-green-100 text-green-800">Fully Compliant</Badge>;
    if (score >= 80) return <Badge className="bg-yellow-100 text-yellow-800">Mostly Compliant</Badge>;
    if (score >= 60) return <Badge className="bg-orange-100 text-orange-800">Partially Compliant</Badge>;
    return <Badge className="bg-red-100 text-red-800">Non-Compliant</Badge>;
  };

  const getCriticalityIcon = (level: string) => {
    switch (level) {
      case 'critical': return <AlertOctagon className="w-4 h-4 text-red-500" />;
      case 'high': return <XSquare className="w-4 h-4 text-orange-500" />;
      case 'medium': return <Clock className="w-4 h-4 text-yellow-500" />;
      default: return <CheckSquare className="w-4 h-4 text-blue-500" />;
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'haccp': return <Shield className="w-4 h-4" />;
      case 'traceability': return <Eye className="w-4 h-4" />;
      case 'temperature': return <Thermometer className="w-4 h-4" />;
      case 'testing': return <TestTube className="w-4 h-4" />;
      case 'documentation': return <FileText className="w-4 h-4" />;
      case 'facility': return <Users className="w-4 h-4" />;
      default: return <Book className="w-4 h-4" />;
    }
  };

  const generateComplianceReport = async (frameworkId: string) => {
    try {
      // Generate comprehensive compliance report
      const framework = frameworks.find(f => f.id === frameworkId);
      if (!framework) return;

      // In production, this would generate a PDF report
      console.log('Generating compliance report for:', framework.name);
      
      // Mock report generation
      const reportData = {
        framework: framework.name,
        generatedDate: new Date().toISOString(),
        complianceScore: calculateComplianceScore(framework.requirements),
        requirements: framework.requirements,
        nextActions: ['Complete CCP monitoring setup', 'Update documentation', 'Schedule verification audit']
      };

      // Save report to database
      await supabase.from('compliance_reports').insert({
        framework_id: frameworkId,
        report_data: reportData,
        generated_by: 'System',
        report_type: 'assessment'
      });

    } catch (err) {
      console.error('Failed to generate compliance report:', err);
    }
  };

  if (loading) {
    return (
      <Card className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Regulatory Compliance Framework</h2>
          <p className="text-sm text-gray-600 mt-1">
            FDA, USDC, GDST, and international seafood safety compliance management
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            onClick={() => selectedFramework && generateComplianceReport(selectedFramework.id)}
            variant="outline"
            className="flex items-center gap-2"
          >
            <Download className="w-4 h-4" />
            Generate Report
          </Button>
          <Badge variant="outline" className="flex items-center gap-1">
            <Globe className="w-3 h-3" />
            {frameworks.length} Frameworks
          </Badge>
        </div>
      </div>

      {/* Framework Selection */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Regulatory Frameworks</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {frameworks.map((framework) => {
            const complianceScore = calculateComplianceScore(framework.requirements);
            return (
              <button
                key={framework.id}
                onClick={() => setSelectedFramework(framework)}
                className={`p-4 rounded-lg border text-left transition-all hover:shadow-md ${
                  selectedFramework?.id === framework.id
                    ? 'border-blue-500 bg-blue-50 shadow-md'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Scale className="w-5 h-5 text-blue-600" />
                    <span className="font-semibold text-sm text-blue-600">{framework.acronym}</span>
                  </div>
                  {getComplianceStatusBadge(complianceScore)}
                </div>
                <div className="font-medium text-gray-900 mb-1">{framework.name}</div>
                <div className="text-sm text-gray-600 mb-2">{framework.authority}</div>
                <div className="text-xs text-gray-500 mb-3">
                  {framework.jurisdiction} • {framework.scope}
                </div>
                
                <div className="pt-3 border-t border-gray-100">
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-gray-500">Compliance Score</span>
                    <span className={`font-medium ${
                      complianceScore >= 95 ? 'text-green-600' : 
                      complianceScore >= 80 ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {complianceScore}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                    <div 
                      className={`h-2 rounded-full ${
                        complianceScore >= 95 ? 'bg-green-500' : 
                        complianceScore >= 80 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${complianceScore}%` }}
                    ></div>
                  </div>
                </div>
              </button>
            );
          })}
        </div>
      </Card>

      {/* Framework Details */}
      {selectedFramework && (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="requirements">Requirements</TabsTrigger>
            <TabsTrigger value="assessment">Assessment</TabsTrigger>
            <TabsTrigger value="documentation">Documentation</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">{selectedFramework.name} Overview</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Regulatory Authority</h4>
                  <p className="text-sm text-gray-600">{selectedFramework.authority}</p>
                  <p className="text-xs text-gray-500 mt-1">{selectedFramework.jurisdiction}</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Scope & Products</h4>
                  <div className="flex flex-wrap gap-1">
                    {selectedFramework.applicableProducts.map((product) => (
                      <Badge key={product} variant="secondary" className="text-xs">
                        {product.replace('_', ' ')}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Compliance Status</h4>
                  <div className="space-y-2">
                    {getComplianceStatusBadge(calculateComplianceScore(selectedFramework.requirements))}
                    <div className="text-xs text-gray-500">
                      Last Updated: {new Date(selectedFramework.lastUpdated).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              </div>
              
              {selectedFramework.mandatoryReporting && (
                <Alert className="mt-4 border-yellow-200 bg-yellow-50">
                  <AlertOctagon className="h-4 w-4" />
                  <div className="text-yellow-800">
                    <strong>Mandatory Reporting Required:</strong> This framework requires regular reporting to regulatory authorities.
                    {selectedFramework.inspectionFrequency && (
                      <span> Inspection frequency: {selectedFramework.inspectionFrequency}.</span>
                    )}
                  </div>
                </Alert>
              )}
            </Card>
          </TabsContent>

          <TabsContent value="requirements" className="space-y-6">
            {selectedFramework.requirements.map((requirement) => (
              <Card key={requirement.id} className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-start gap-3">
                    {getCategoryIcon(requirement.category)}
                    <div>
                      <h4 className="font-semibold text-gray-900">{requirement.title}</h4>
                      <p className="text-sm text-gray-600 mt-1">{requirement.description}</p>
                      <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                        <span>Section: {requirement.regulatorySection}</span>
                        <span>Retention: {requirement.recordRetentionPeriod} years</span>
                        <span>Verification: {requirement.verificationMethod}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {getCriticalityIcon(requirement.criticalityLevel)}
                    <Badge variant={requirement.criticalityLevel === 'critical' ? 'destructive' : 'secondary'}>
                      {requirement.criticalityLevel.toUpperCase()}
                    </Badge>
                  </div>
                </div>

                <div className="border-t pt-4">
                  <h5 className="font-medium text-gray-900 mb-3">Compliance Checklist</h5>
                  <div className="space-y-2">
                    {requirement.complianceChecklist.map((item) => (
                      <div key={item.id} className="flex items-center gap-3 p-2 rounded border">
                        {item.status === 'completed' ? (
                          <CheckSquare className="w-4 h-4 text-green-500" />
                        ) : item.status === 'in_progress' ? (
                          <Clock className="w-4 h-4 text-yellow-500" />
                        ) : item.status === 'non_compliant' ? (
                          <XSquare className="w-4 h-4 text-red-500" />
                        ) : (
                          <div className="w-4 h-4 border-2 border-gray-300 rounded"></div>
                        )}
                        <span className="flex-1 text-sm">{item.requirement}</span>
                        <Badge variant={
                          item.status === 'completed' ? 'default' :
                          item.status === 'in_progress' ? 'secondary' :
                          item.status === 'non_compliant' ? 'destructive' : 'outline'
                        }>
                          {item.status.replace('_', ' ')}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>

                {requirement.penaltyForNonCompliance && (
                  <Alert className="mt-4 border-red-200 bg-red-50">
                    <AlertOctagon className="h-4 w-4" />
                    <div className="text-red-800">
                      <strong>Non-Compliance Penalties:</strong> {requirement.penaltyForNonCompliance}
                    </div>
                  </Alert>
                )}
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="assessment">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Compliance Assessment (Coming Soon)</h3>
              <p className="text-gray-600">
                Automated compliance assessment with scoring, gap analysis, and corrective action recommendations.
              </p>
            </Card>
          </TabsContent>

          <TabsContent value="documentation">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Regulatory Documentation (Coming Soon)</h3>
              <p className="text-gray-600">
                Document management system with digital signatures, version control, and regulatory submission capabilities.
              </p>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}