import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../../lib/supabase';
import { Card } from '../ui/card';

interface ComplianceAlert {
  id: string;
  alert_type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  triggered_at: string;
  acknowledged: boolean;
  resolved: boolean;
  metadata: Record<string, string | number | boolean | null>;
}

interface ComplianceMetrics {
  total_ccps: number;
  active_ccps: number;
  deviations_last_30_days: number;
  pending_corrective_actions: number;
  overdue_verifications: number;
  calibrations_due: number;
  unresolved_alerts: number;
  compliance_score: number;
}

interface EnvironmentalReading {
  id: string;
  zone_name: string;
  measurement_type: string;
  measurement_value: number;
  measurement_unit: string;
  measurement_timestamp: string;
  within_limits: boolean;
  critical_limit_min?: number;
  critical_limit_max?: number;
}

export default function ComplianceDashboard() {
  const [metrics, setMetrics] = useState<ComplianceMetrics | null>(null);
  const [alerts, setAlerts] = useState<ComplianceAlert[]>([]);
  const [environmentalReadings, setEnvironmentalReadings] = useState<EnvironmentalReading[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTab, setSelectedTab] = useState('overview');

  useEffect(() => {
    loadDashboardData();
    const interval = setInterval(loadDashboardData, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, [loadDashboardData]);

  const loadDashboardData = useCallback(async () => {
    try {
      await Promise.all([
        loadComplianceMetrics(),
        loadAlerts(),
        loadEnvironmentalReadings()
      ]);

      setLoading(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load dashboard data');
      setLoading(false);
    }
  }, []);

  const loadComplianceMetrics = async () => {
    // Get CCP metrics
    const { data: ccpData } = await supabase
      .from('critical_control_points')
      .select('id, is_active');

    // Get recent deviations
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const { data: deviationData } = await supabase
      .from('ccp_monitoring_logs')
      .select('id')
      .eq('within_critical_limits', false)
      .gte('monitoring_timestamp', thirtyDaysAgo.toISOString());

    // Get pending corrective actions
    const { data: correctiveActionData } = await supabase
      .from('corrective_actions')
      .select('id')
      .eq('verification_completed', false);

    // Get overdue verifications
    const { data: verificationData } = await supabase
      .from('verification_activities')
      .select('id')
      .eq('status', 'overdue');

    // Get calibrations due
    const { data: calibrationData } = await supabase
      .from('equipment_calibrations')
      .select('id')
      .lte('next_calibration_due', new Date().toISOString());

    // Get unresolved alerts
    const { data: alertData } = await supabase
      .from('compliance_alerts')
      .select('id')
      .eq('resolved', false);

    const totalCcps = ccpData?.length ?? 0;
    const activeCcps = ccpData?.filter(ccp => ccp.is_active).length ?? 0;
    const deviations = deviationData?.length ?? 0;
    const pendingActions = correctiveActionData?.length ?? 0;
    const overdueVerifications = verificationData?.length ?? 0;
    const calibrationsDue = calibrationData?.length ?? 0;
    const unresolvedAlerts = alertData?.length ?? 0;

    // Calculate compliance score (0-100)
    let complianceScore = 100;
    if (totalCcps > 0) {
      complianceScore -= (deviations * 10); // -10 points per deviation
      complianceScore -= (pendingActions * 5); // -5 points per pending action
      complianceScore -= (overdueVerifications * 15); // -15 points per overdue verification
      complianceScore -= (calibrationsDue * 10); // -10 points per overdue calibration
      complianceScore = Math.max(0, complianceScore);
    }

    const metrics: ComplianceMetrics = {
      total_ccps: totalCcps,
      active_ccps: activeCcps,
      deviations_last_30_days: deviations,
      pending_corrective_actions: pendingActions,
      overdue_verifications: overdueVerifications,
      calibrations_due: calibrationsDue,
      unresolved_alerts: unresolvedAlerts,
      compliance_score: complianceScore
    };

    setMetrics(metrics);
  };

  const loadAlerts = async () => {
    const { data, error } = await supabase
      .from('compliance_alerts')
      .select('*')
      .eq('resolved', false)
      .order('triggered_at', { ascending: false })
      .limit(20);

    if (error) throw error;
    setAlerts(data || []);
  };

  const loadEnvironmentalReadings = async () => {
    const { data, error } = await supabase
      .from('environmental_monitoring')
      .select('*')
      .order('measurement_timestamp', { ascending: false })
      .limit(50);

    if (error) throw error;
    setEnvironmentalReadings(data || []);
  };

  const acknowledgeAlert = async (alertId: string) => {
    try {
      const { error } = await supabase
        .from('compliance_alerts')
        .update({
          acknowledged: true,
          acknowledged_at: new Date().toISOString(),
          acknowledged_by: 'current_user' // TODO: Get from auth context
        })
        .eq('id', alertId);

      if (error) throw error;
      await loadAlerts();
    } catch (err) {
      console.error('Failed to acknowledge alert:', err);
    }
  };

  const resolveAlert = async (alertId: string, resolutionNotes: string) => {
    try {
      const { error } = await supabase
        .from('compliance_alerts')
        .update({
          resolved: true,
          resolved_at: new Date().toISOString(),
          resolved_by: 'current_user', // TODO: Get from auth context
          resolution_notes: resolutionNotes
        })
        .eq('id', alertId);

      if (error) throw error;
      await loadAlerts();
    } catch (err) {
      console.error('Failed to resolve alert:', err);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getComplianceScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 75) return 'text-yellow-600';
    if (score >= 60) return 'text-orange-600';
    return 'text-red-600';
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">Loading compliance dashboard...</div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Compliance Dashboard</h1>
        <div className="text-sm text-gray-500">
          Last updated: {new Date().toLocaleTimeString()}
        </div>
      </div>

      {error && (
        <div className="border border-red-200 bg-red-50 p-4 rounded-lg">
          <div className="text-red-800">{error}</div>
        </div>
      )}

      {/* Compliance Score */}
      {metrics && (
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Overall Compliance Score</h2>
              <p className="text-sm text-gray-600">Based on recent HACCP performance and compliance activities</p>
            </div>
            <div className={`text-4xl font-bold ${getComplianceScoreColor(metrics.compliance_score)}`}>
              {metrics.compliance_score}%
            </div>
          </div>
          <div className="mt-4 w-full bg-gray-200 rounded-full h-3">
            <div
              className={`h-3 rounded-full transition-all duration-300 ${
                metrics.compliance_score >= 90 ? 'bg-green-500' :
                metrics.compliance_score >= 75 ? 'bg-yellow-500' :
                metrics.compliance_score >= 60 ? 'bg-orange-500' : 'bg-red-500'
              }`}
              style={{ width: `${metrics.compliance_score}%` }}
            />
          </div>
        </Card>
      )}

      {/* Key Metrics */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <Card className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{metrics.active_ccps}</div>
            <div className="text-sm text-gray-600">Active CCPs</div>
          </Card>
          <Card className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">{metrics.deviations_last_30_days}</div>
            <div className="text-sm text-gray-600">Deviations (30d)</div>
          </Card>
          <Card className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">{metrics.pending_corrective_actions}</div>
            <div className="text-sm text-gray-600">Pending Actions</div>
          </Card>
          <Card className="p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">{metrics.overdue_verifications}</div>
            <div className="text-sm text-gray-600">Overdue Verifications</div>
          </Card>
          <Card className="p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">{metrics.calibrations_due}</div>
            <div className="text-sm text-gray-600">Calibrations Due</div>
          </Card>
          <Card className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">{metrics.unresolved_alerts}</div>
            <div className="text-sm text-gray-600">Active Alerts</div>
          </Card>
        </div>
      )}

      {/* Tabs */}
      <div className="w-full">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setSelectedTab('alerts')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                selectedTab === 'alerts'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Active Alerts ({alerts.length})
            </button>
            <button
              onClick={() => setSelectedTab('environmental')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                selectedTab === 'environmental'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Environmental Monitoring
            </button>
          </nav>
        </div>

        {/* Alerts Tab */}
        {selectedTab === 'alerts' && (
          <div className="mt-6 space-y-4">
            {alerts.length === 0 ? (
              <Card className="p-6">
                <p className="text-gray-600 text-center">No active alerts. Your compliance system is running smoothly!</p>
              </Card>
            ) : (
              alerts.map((alert) => (
                <Card key={alert.id} className={`p-4 border-l-4 ${getSeverityColor(alert.severity)}`}>
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className={`text-xs px-2 py-1 rounded-full font-medium ${getSeverityColor(alert.severity)}`}>
                          {alert.severity.toUpperCase()}
                        </span>
                        <span className="text-xs text-gray-500">
                          {alert.alert_type.replace(/_/g, ' ').toUpperCase()}
                        </span>
                      </div>
                      <h3 className="font-medium mt-2">{alert.title}</h3>
                      <p className="text-sm text-gray-600 mt-1">{alert.description}</p>
                      <div className="text-xs text-gray-500 mt-2">
                        Triggered: {new Date(alert.triggered_at).toLocaleString()}
                      </div>
                    </div>
                    <div className="flex gap-2 ml-4">
                      {!alert.acknowledged && (
                        <button
                          onClick={() => acknowledgeAlert(alert.id)}
                          className="text-xs px-3 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
                        >
                          Acknowledge
                        </button>
                      )}
                      <button
                        onClick={() => resolveAlert(alert.id, 'Resolved from dashboard')}
                        className="text-xs px-3 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200"
                      >
                        Resolve
                      </button>
                    </div>
                  </div>
                </Card>
              ))
            )}
          </div>
        )}

        {/* Environmental Monitoring Tab */}
        {selectedTab === 'environmental' && (
          <div className="mt-6">
            <Card className="p-4">
              <h3 className="font-semibold mb-4">Recent Environmental Readings</h3>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead className="border-b">
                    <tr>
                      <th className="text-left py-2">Zone</th>
                      <th className="text-left py-2">Type</th>
                      <th className="text-left py-2">Value</th>
                      <th className="text-left py-2">Status</th>
                      <th className="text-left py-2">Time</th>
                    </tr>
                  </thead>
                  <tbody>
                    {environmentalReadings.map((reading) => (
                      <tr key={reading.id} className="border-b">
                        <td className="py-2">{reading.zone_name}</td>
                        <td className="py-2 capitalize">{reading.measurement_type}</td>
                        <td className="py-2">
                          {reading.measurement_value} {reading.measurement_unit}
                        </td>
                        <td className="py-2">
                          <span className={`text-xs px-2 py-1 rounded ${
                            reading.within_limits
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {reading.within_limits ? 'Normal' : 'Alert'}
                          </span>
                        </td>
                        <td className="py-2 text-gray-500">
                          {new Date(reading.measurement_timestamp).toLocaleString()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}