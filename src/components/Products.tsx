import { useState, useEffect } from 'react';
import { Search, Filter, Plus } from 'lucide-react';
import { supabase } from '../lib/supabase';
import CategoryView from './CategoryView';
import ProductDetails from './ProductDetails';
import Modal from './modals/Modal';
import ProductForm from './forms/ProductForm';
import type { Product } from '../types';

export default function Products() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<{ id: string; name: string }>({ id: 'all', name: 'All' });
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<{ id: string; name: string }[]>([{ id: 'all', name: 'All' }]);

  useEffect(() => {
    const fetchProducts = async () => {
      console.log('Fetching products...');
      const { data, error } = await supabase
        .from('Products')
        .select('*');

      if (error) {
        console.error('Error fetching products:', error);
      } else {
        console.log('Raw product data:', data);
        setProducts(data || []);
        const uniqueCategories = Array.from(new Set(data?.map((product: Product) => product.category).filter((category): category is string => Boolean(category)) ?? []));
        setCategories([{ id: 'all', name: 'All' }, ...uniqueCategories.map(category => ({ id: category, name: category }))]);
      }
    };

    fetchProducts();
  }, []);

  const filteredProducts = products.filter(product => {
    const matchesCategory = selectedCategory.id === 'all' || product.category === selectedCategory.name;
    const matchesSearch = product.supplier?.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  if (selectedProduct) {
    return (
      <div className="p-6">
        <ProductDetails
          product={selectedProduct}
          onBack={() => setSelectedProduct(null)}
        />
      </div>
    );
  }

  const handleAddProduct = () => {
    console.log('Add Product button clicked');
    setIsModalOpen(true);
    console.log('isModalOpen set to true');
  };

  const handleFormSuccess = async () => {
    console.log('Form submitted successfully');
    setIsModalOpen(false);
    console.log('Refreshing products...');
    // Refresh products
    const { data, error } = await supabase
      .from('Products')
      .select('*');

    if (error) {
      console.error('Error fetching products:', error);
    } else {
      console.log('Products refreshed:', data);
      setProducts(data || []);
      const uniqueCategories = Array.from(new Set(data?.map((product: Product) => product.category).filter((category): category is string => Boolean(category)) ?? []));
      setCategories([
        { id: 'all', name: 'All' }, 
        ...uniqueCategories.map(category => ({ id: category, name: category }))
      ]);
    }
  };

  console.log('Current modal state:', { isModalOpen });

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Products</h1>
        <button 
          onClick={handleAddProduct}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-blue-700 transition-colors"
        >
          <Plus className="w-4 h-4" />
          Add Product
        </button>
      </div>

      <div className="flex gap-4 flex-col sm:flex-row">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            placeholder="Search by supplier..."
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
          <Filter className="w-5 h-5" />
          Filters
        </button>
      </div>

      <div className="flex gap-4 overflow-x-auto pb-2 scrollbar-hide">
        {categories.map((category) => (
          <button
            key={category.id}
            onClick={() => setSelectedCategory(category)}
            className={`px-4 py-2 rounded-lg whitespace-nowrap transition-colors ${
              selectedCategory.id === category.id
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
            }`}
          >
            {category.name}
          </button>
        ))}
      </div>

      <CategoryView
        category={selectedCategory}
        products={filteredProducts}
        onProductSelect={setSelectedProduct}
      />

      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title="Add New Product"
      >
        <ProductForm
          onSuccess={handleFormSuccess}
          onCancel={() => setIsModalOpen(false)}
        />
      </Modal>
    </div>
  );
}
