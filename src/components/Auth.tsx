import { Auth as SupabaseAuth } from '@supabase/auth-ui-react';
import { ThemeSupa } from '@supabase/auth-ui-shared';
import { supabase } from '../lib/supabase';
import { useState } from 'react';

export default function Auth() {
  const [error, setError] = useState<string | null>(null);

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center p-4">
      <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold text-gray-900">Pacific Cloud Seafoods</h1>
          <p className="text-gray-600">Inventory Management System</p>
        </div>
        
        {error && (
          <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        <SupabaseAuth 
          supabaseClient={supabase}
          appearance={{
            theme: ThemeSupa,
            variables: {
              default: {
                colors: {
                  brand: '#2563eb',
                  brandAccent: '#1d4ed8',
                }
              }
            },
            style: {
              button: {
                borderRadius: '0.5rem',
                padding: '0.75rem 1rem',
              },
              input: {
                borderRadius: '0.5rem',
                padding: '0.75rem 1rem',
              },
              anchor: {
                color: '#2563eb',
                textDecoration: 'none',
              },
            },
          }}
          localization={{
            variables: {
              sign_in: {
                email_label: 'Email address',
                password_label: 'Password',
                button_label: 'Sign in',
                loading_button_label: 'Signing in...',
                social_provider_text: 'Sign in with {{provider}}',
                link_text: "Already have an account? Sign in",
              },
              sign_up: {
                email_label: 'Email address',
                password_label: 'Create a Password',
                button_label: 'Sign up',
                loading_button_label: 'Signing up...',
                social_provider_text: 'Sign up with {{provider}}',
                link_text: "Don't have an account? Sign up",
              },
              magic_link: {
                email_input_label: 'Email address',
                button_label: 'Send Magic Link',
                loading_button_label: 'Sending Magic Link...',
                link_text: 'Send a magic link email',
              },
              forgotten_password: {
                email_label: 'Email address',
                password_label: 'Your Password',
                button_label: 'Send reset password instructions',
                loading_button_label: 'Sending reset instructions...',
                link_text: 'Forgot your password?',
              },
            },
          }}
          providers={[]}
          redirectTo={window.location.origin}
        />

        {error && (
          <button
            onClick={() => setError(null)}
            className="mt-4 w-full text-sm text-gray-500 hover:text-gray-700"
          >
            Dismiss error and try again
          </button>
        )}
      </div>
    </div>
  );
}
