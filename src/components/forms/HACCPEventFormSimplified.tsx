import { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { supabase } from '../../lib/supabase';
import { createReceivingWithLot } from '../../lib/api';
import BatchNumberGenerator from '../batch/BatchNumberGenerator';
import { useNavigationContext } from '../../contexts/NavigationContext';

// Core event types only
const EVENT_OPTIONS = [
  { label: 'Receiving', value: 'receiving' },
  { label: 'Disposal', value: 'disposal' },
  { label: 'Physical Count', value: 'physical_count' },
  { label: 'Sales', value: 'sale' },
] as const;

const UNIT_OPTIONS = [
  { label: 'Pounds (lbs)', value: 'lbs' },
  { label: 'Kilograms (kg)', value: 'kg' },
  { label: 'Cases', value: 'cases' },
  { label: 'Units', value: 'units' },
] as const;

const haccpEventSchema = z.object({
  eventType: z.enum(['receiving', 'disposal', 'physical_count', 'sale']),
  productId: z.string().uuid({ message: 'Select a product' }),
  vendorId: z.string().uuid().optional().or(z.literal('')),
  customerId: z.string().uuid().optional().or(z.literal('')),
  quantity: z.number({ invalid_type_error: 'Quantity is required' }).positive('Enter a positive quantity'),
  unit: z.enum(['lbs', 'kg', 'cases', 'units']).default('lbs'),
  eventDate: z.string().optional(),
  eventTime: z.string().optional(),
  condition: z.enum(['Excellent', 'Good', 'Fair', 'Poor', 'Damaged']).optional(),
  unitPrice: z.union([z.number().min(0, 'Must be >= 0'), z.nan()]).optional().transform((val) => {
    if (val === undefined || Number.isNaN(val)) return undefined;
    return val;
  }),
  notes: z.string().max(500, 'Max 500 chars').optional(),
  batchNumberMode: z.enum(['auto', 'manual']).default('auto'),
  manualBatchNumber: z.string().max(64).optional(),
}).refine((data) => {
  if (data.eventType === 'receiving') return !!data.vendorId && !!data.eventDate;
  if (data.eventType === 'sale') return !!data.customerId;
  return true;
}, {
  message: 'Missing required fields for selected event',
  path: ['eventType']
});

export type HACCPEventFormData = z.infer<typeof haccpEventSchema>;

interface HACCPEventFormProps {
  onSuccess: () => void;
  onCancel: () => void;
  eventTypeOverride?: 'receiving' | 'disposal' | 'physical_count' | 'sale';
  dateOverride?: string;
}

export default function HACCPEventFormSimplified({ 
  onSuccess, 
  onCancel, 
  eventTypeOverride, 
  dateOverride 
}: HACCPEventFormProps) {
  const { setActiveView, setViewFilter } = useNavigationContext();
  const { register, handleSubmit, watch, setValue, formState: { errors, isSubmitting } } = useForm<HACCPEventFormData>({
    resolver: zodResolver(haccpEventSchema),
    defaultValues: {
      eventType: eventTypeOverride || 'receiving',
      unit: 'lbs',
      eventDate: dateOverride || new Date().toISOString().split('T')[0],
      eventTime: new Date().toTimeString().slice(0, 5),
      batchNumberMode: 'auto',
    }
  });

  const eventType = watch('eventType');
  const batchNumberMode = watch('batchNumberMode');
  const productId = watch('productId');
  const vendorId = watch('vendorId');
  const eventDate = watch('eventDate');

  const [products, setProducts] = useState<Array<{ id: string; name: string }>>([]);
  const [vendors, setVendors] = useState<Array<{ id: string; name: string }>>([]);
  const [customers, setCustomers] = useState<Array<{ id: string; name: string }>>([]);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState<string | null>(null);

  const productNameById = useMemo(() => Object.fromEntries(products.map(p => [p.id, p.name])), [products]);
  const vendorNameById = useMemo(() => Object.fromEntries(vendors.map(v => [v.id, v.name])), [vendors]);

  // Load data
  useEffect(() => {
    const load = async () => {
      const [prodRes, vendRes, custRes] = await Promise.all([
        supabase.from('Products').select('id, name').order('name', { ascending: true }),
        supabase.from('vendors').select('id, name').order('name', { ascending: true }),
        supabase.from('customers').select('id, name').order('name', { ascending: true }),
      ]);
      
      if (!prodRes.error && prodRes.data) {
        const uniqueProducts = prodRes.data.filter((product, index, array) => 
          array.findIndex(p => p.name === product.name) === index
        );
        setProducts(uniqueProducts);
      }
      if (!vendRes.error && vendRes.data) {
        const uniqueVendors = vendRes.data.filter((vendor, index, array) => 
          array.findIndex(v => v.name === vendor.name) === index
        );
        setVendors(uniqueVendors);
      }
      if (!custRes.error && custRes.data) {
        const uniqueCustomers = custRes.data.filter((customer, index, array) => 
          array.findIndex(c => c.name === customer.name) === index
        );
        setCustomers(uniqueCustomers);
      }
    };
    load();
  }, []);

  // Apply external overrides
  useEffect(() => {
    if (eventTypeOverride) setValue('eventType', eventTypeOverride);
  }, [eventTypeOverride, setValue]);

  useEffect(() => {
    if (dateOverride && /\d{4}-\d{2}-\d{2}/.test(dateOverride)) {
      setValue('eventDate', dateOverride);
    }
  }, [dateOverride, setValue]);

  const handleViewEvents = (eventType: string) => {
    setViewFilter('eventType', eventType);
    setActiveView('Events');
  };

  const onSubmit = async (data: HACCPEventFormData) => {
    setSubmitError(null);
    setSubmitSuccess(null);
    
    try {
      const { eventType, productId, quantity, unit, unitPrice, notes } = data;
      let batchMeta: { batch_number?: string; lot_id?: string; tlc?: string } = {};

      const createdAtISO = dateOverride ? 
        (() => {
          const [y, m, d] = dateOverride.split('-').map(Number);
          const dt = new Date(y, (m as number) - 1, d as number, 12, 0, 0, 0);
          return dt.toISOString();
        })() : 
        new Date().toISOString();

      const occurredAtISO = (() => {
        if (data.eventDate && /\d{4}-\d{2}-\d{2}/.test(data.eventDate)) {
          const [y, m, d] = data.eventDate.split('-').map(Number);
          let hour = 12, minute = 0;
          
          if (data.eventTime && /\d{1,2}:\d{2}/.test(data.eventTime)) {
            const [h, min] = data.eventTime.split(':').map(Number);
            hour = h;
            minute = min;
          }
          
          const dt = new Date(y, (m as number) - 1, d as number, hour, minute, 0, 0);
          return dt.toISOString();
        }
        return new Date().toISOString();
      })();

      if (eventType === 'receiving') {
        const vendorId = data.vendorId as string;
        const vendorName = vendorNameById[vendorId];
        const productName = productNameById[productId];
        const eventDate = data.eventDate;

        if (!vendorName || !productName || !eventDate) {
          throw new Error('Missing product, vendor, or date');
        }

        try {
          const result = await createReceivingWithLot({
            productName,
            quantity,
            unit,
            vendorName,
            receivingDate: eventDate,
            condition: data.condition,
            notes,
            tlc: data.batchNumberMode === 'manual' ? data.manualBatchNumber : undefined,
          });
          
          if (!result.success) throw new Error(result.error?.message || 'Failed to create receiving/lot');
          batchMeta = { batch_number: result.data?.tlc, lot_id: result.data?.lot_id, tlc: result.data?.tlc };

          const totalAmount = unitPrice ? Number((quantity * unitPrice).toFixed(2)) : null;
          
          const inventoryEventData = {
            event_type: 'receiving',
            product_id: productId,
            quantity,
            unit_price: unitPrice ?? null,
            total_amount: totalAmount,
            notes: notes || null,
            metadata: {
              source: 'haccp-form-simplified',
              ...batchMeta,
              batch_number_mode: data.batchNumberMode,
              unit,
              vendor_id: vendorId,
              vendor_name: vendorName,
              occurred_at: occurredAtISO,
            },
            created_at: createdAtISO,
          };
          
          const { error: invErr } = await supabase.from('inventory_events').insert(inventoryEventData);
          if (invErr) throw invErr;
        } catch (traceErr) {
          console.warn('Traceability path failed, falling back to inventory_events only:', traceErr);
          const totalAmount = unitPrice ? Number((quantity * unitPrice).toFixed(2)) : null;
          
          const fallbackEventData = {
            event_type: 'receiving',
            product_id: productId,
            quantity,
            unit_price: unitPrice ?? null,
            total_amount: totalAmount,
            notes: notes || null,
            metadata: {
              source: 'haccp-form-simplified',
              batch_number_mode: data.batchNumberMode,
              unit,
              vendor_id: vendorId,
              vendor_name: vendorName,
              occurred_at: occurredAtISO,
              tlc: data.batchNumberMode === 'manual' ? data.manualBatchNumber : undefined,
            },
            created_at: createdAtISO,
          };
          
          const { error: invErr } = await supabase.from('inventory_events').insert(fallbackEventData);
          if (invErr) throw invErr;
        }
      }

      // Other event types
      if (eventType !== 'receiving') {
        const payload: Record<string, unknown> = {
          event_type: eventType,
          product_id: productId,
          quantity,
          notes: notes || null,
          metadata: { 
            source: 'haccp-form-simplified', 
            unit,
            occurred_at: occurredAtISO 
          },
          created_at: createdAtISO,
        };
        
        if (eventType === 'sale') {
          payload.metadata = { 
            ...payload.metadata as Record<string, unknown>, 
            customer_id: data.customerId || null 
          };
          if (typeof data.unitPrice === 'number') {
            payload.unit_price = data.unitPrice;
            payload.total_amount = Number((quantity * data.unitPrice).toFixed(2));
          }
        }
        
        const { error } = await supabase.from('inventory_events').insert(payload);
        if (error) throw error;
      }

      const successMessage = eventType === 'receiving' && batchMeta.batch_number 
        ? `Successfully saved ${eventType} event! Batch number: ${batchMeta.batch_number}`
        : `Successfully saved ${eventType} event!`;
      
      setSubmitSuccess(successMessage);
      
      // Call onSuccess immediately to trigger calendar reload
      onSuccess();
      
      // Clear success message after delay
      setTimeout(() => {
        setSubmitSuccess(null);
      }, 3000);
      
    } catch (err: unknown) {
      console.error('HACCP submit failed:', err);
      setSubmitError(err instanceof Error ? err.message : 'Failed to submit');
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Event Type */}
        <div>
          <label htmlFor="eventType" className="block text-sm font-medium text-gray-700">Event Type</label>
          <select id="eventType" {...register('eventType')} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            {EVENT_OPTIONS.map(opt => (
              <option key={opt.value} value={opt.value}>{opt.label}</option>
            ))}
          </select>
          {errors.eventType && <p className="mt-1 text-sm text-red-600">{errors.eventType.message}</p>}
        </div>

        {/* Product */}
        <div>
          <label htmlFor="productId" className="block text-sm font-medium text-gray-700">Product</label>
          <select id="productId" {...register('productId')} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            <option value="">Select a product</option>
            {products.map(p => <option key={p.id} value={p.id}>{p.name}</option>)}
          </select>
          {errors.productId && <p className="mt-1 text-sm text-red-600">{errors.productId.message}</p>}
        </div>

        {/* Quantity */}
        <div>
          <label htmlFor="quantity" className="block text-sm font-medium text-gray-700">Quantity</label>
          <input id="quantity" type="number" step="0.01" {...register('quantity', { valueAsNumber: true })} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" />
          {errors.quantity && <p className="mt-1 text-sm text-red-600">{errors.quantity.message}</p>}
        </div>

        {/* Unit */}
        <div>
          <label htmlFor="unit" className="block text-sm font-medium text-gray-700">Unit</label>
          <select id="unit" {...register('unit')} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            {UNIT_OPTIONS.map(u => <option key={u.value} value={u.value}>{u.label}</option>)}
          </select>
          {errors.unit && <p className="mt-1 text-sm text-red-600">{errors.unit.message as string}</p>}
        </div>

        {/* Receiving-only fields */}
        {eventType === 'receiving' && (
          <>
            <div>
              <label htmlFor="vendorId" className="block text-sm font-medium text-gray-700">Vendor</label>
              <select id="vendorId" {...register('vendorId')} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                <option value="">Select a vendor</option>
                {vendors.map(v => <option key={v.id} value={v.id}>{v.name}</option>)}
              </select>
              {errors.vendorId && <p className="mt-1 text-sm text-red-600">Vendor is required</p>}
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="eventDate" className="block text-sm font-medium text-gray-700">Event Date</label>
                <input id="eventDate" type="date" {...register('eventDate')} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" />
              </div>
              <div>
                <label htmlFor="eventTime" className="block text-sm font-medium text-gray-700">Event Time</label>
                <input id="eventTime" type="time" {...register('eventTime')} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" />
              </div>
            </div>

            <div>
              <label htmlFor="batchNumberMode" className="block text-sm font-medium text-gray-700">Batch Number</label>
              <select id="batchNumberMode" {...register('batchNumberMode')} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                <option value="auto">Auto-generate</option>
                <option value="manual">Manual entry</option>
              </select>
              <BatchNumberGenerator
                date={eventDate}
                productId={productId}
                vendorId={vendorId}
                productNameById={productNameById}
                vendorNameById={vendorNameById}
                onGenerate={(val) => {
                  setValue('manualBatchNumber', val, { shouldDirty: true, shouldValidate: true });
                  setValue('batchNumberMode', 'manual', { shouldDirty: true, shouldValidate: true });
                }}
                disabled={isSubmitting}
              />
              {batchNumberMode === 'manual' && (
                <input
                  id="manualBatchNumber"
                  placeholder="Enter batch number (TLC)"
                  type="text"
                  {...register('manualBatchNumber')}
                  className="mt-2 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                />
              )}
            </div>

            <div>
              <label htmlFor="condition" className="block text-sm font-medium text-gray-700">Condition</label>
              <select id="condition" {...register('condition')} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                <option value="">Select condition</option>
                {['Excellent','Good','Fair','Poor','Damaged'].map(c => <option key={c} value={c}>{c}</option>)}
              </select>
            </div>

            <div>
              <label htmlFor="unitPrice" className="block text-sm font-medium text-gray-700">Cost per Unit</label>
              <input id="unitPrice" type="number" step="0.01" min="0" {...register('unitPrice', { valueAsNumber: true })} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" />
            </div>
          </>
        )}

        {/* Sales-only fields */}
        {eventType === 'sale' && (
          <>
            <div>
              <label htmlFor="customerId" className="block text-sm font-medium text-gray-700">Customer</label>
              <select id="customerId" {...register('customerId')} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                <option value="">Select a customer</option>
                {customers.map(c => <option key={c.id} value={c.id}>{c.name}</option>)}
              </select>
              {errors.customerId && <p className="mt-1 text-sm text-red-600">Customer is required</p>}
            </div>
            <div>
              <label htmlFor="unitPrice" className="block text-sm font-medium text-gray-700">Unit Price</label>
              <input id="unitPrice" type="number" step="0.01" min="0" {...register('unitPrice', { valueAsNumber: true })} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" />
            </div>
          </>
        )}

        {/* Notes */}
        <div className={eventType === 'receiving' ? 'md:col-span-2' : ''}>
          <label htmlFor="notes" className="block text-sm font-medium text-gray-700">Notes</label>
          <textarea id="notes" rows={3} {...register('notes')} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" />
          {errors.notes && <p className="mt-1 text-sm text-red-600">{errors.notes.message}</p>}
        </div>
      </div>

      {submitError && (
        <div className="bg-red-50 border border-red-200 rounded p-3 text-sm text-red-700">{submitError}</div>
      )}

      {submitSuccess && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-green-800">
          <div className="flex items-start gap-3">
            <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-white text-xs font-bold">✓</span>
            </div>
            <div className="flex-1">
              <div className="font-medium text-green-900">Event Saved Successfully</div>
              <div className="text-sm text-green-700 mt-1">{submitSuccess}</div>
              <button
                onClick={() => handleViewEvents(eventType)}
                className="mt-3 inline-flex items-center px-3 py-2 text-sm font-medium text-blue-700 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 hover:border-blue-300 transition-colors"
              >
                View all {eventType === 'receiving' ? 'receiving' : 
                        eventType === 'disposal' ? 'disposal' :
                        eventType === 'physical_count' ? 'physical count' :
                        eventType === 'sale' ? 'sales' : eventType} events
                <span className="ml-1">→</span>
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="flex justify-end gap-3">
        <button 
          type="button" 
          onClick={onCancel} 
          className="px-4 py-2 text-sm font-medium rounded-lg bg-gray-200 text-gray-800 hover:bg-gray-300 transition-colors"
        >
          Cancel
        </button>
        <button 
          type="submit" 
          disabled={isSubmitting} 
          className="px-4 py-2 text-sm font-medium rounded-lg bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50 transition-colors"
        >
          {isSubmitting ? (
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              Saving...
            </div>
          ) : 'Save Event'}
        </button>
      </div>
    </form>
  );
}