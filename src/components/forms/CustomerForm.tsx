import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { customerSchema } from '../../lib/validation';
import { createCustomer, updateCustomer } from '../../lib/api';
import { Mic } from 'lucide-react';
import VoiceInput from '../voice/VoiceInput';
import type { Customer } from '../../types';

interface CustomerFormProps {
  customer?: Customer;
  onSuccess: () => void;
  onCancel: () => void;
}

type CustomerFormData = {
  name: string;
  contactName?: string;
  email?: string;
  phone?: string;
  address?: string;
  channelType: 'wholesale' | 'retail' | 'distributor';
  status: 'active' | 'inactive';
  paymentTerms?: string;
  creditLimit?: number;
  customerSource?: string;
};

interface VoiceInputData {
  product?: string;
  vendor?: string;
  quantity?: number;
  unit?: string;
}

export default function CustomerForm({ customer, onSuccess, onCancel }: CustomerFormProps) {
  const [showVoiceInput, setShowVoiceInput] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const { register, handleSubmit, setValue, watch, formState: { errors, isSubmitting } } = useForm<CustomerFormData>({
    resolver: zodResolver(customerSchema),
    defaultValues: customer ?? {
      status: 'active',
      channelType: 'wholesale'
    }
  });

  const channelType = watch('channelType');
  const isRetail = channelType === 'retail';

  const handleVoiceData = (data: VoiceInputData) => {
    // Map voice input data to customer fields
    if (data.product) setValue('name', data.product); // Business name from product
    if (data.vendor) setValue('contactName', data.vendor); // Contact name from vendor
    setShowVoiceInput(false);
  };

  const handleVoiceError = (error: string) => {
    console.error('Voice input error:', error);
    setShowVoiceInput(false);
  };

  const onSubmit = async (data: CustomerFormData) => {
    setSubmitError(null);
    try {
      console.log('Submitting customer data:', data);
      let result: { success: boolean; error?: Error | string | { message?: string }; data?: Customer };
      
      if (customer?.id) {
        console.log('Updating existing customer:', customer.id);
        result = await updateCustomer(customer.id, data);
      } else {
        console.log('Creating new customer');
        result = await createCustomer(data);
      }

      if (!result.success) {
        console.error('Error saving customer:', result.error);
        const errorMessage = result.error && typeof result.error === 'object' && 'message' in result.error 
          ? (result.error as { message?: string }).message 
          : typeof result.error === 'string' 
            ? result.error 
            : 'Failed to save customer';
        setSubmitError(errorMessage ?? 'Failed to save customer');
        return;
      }

      console.log('Customer saved successfully:', result.data);
      onSuccess();
    } catch (error) {
      console.error('Error saving customer:', error);
      setSubmitError(error instanceof Error ? error.message : 'An unexpected error occurred');
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-end">
        <button
          type="button"
          onClick={() => setShowVoiceInput(!showVoiceInput)}
          className="flex items-center gap-2 px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
        >
          <Mic className="w-5 h-5" />
          {showVoiceInput ? 'Hide Voice Input' : 'Use Voice Input'}
        </button>
      </div>

      {showVoiceInput && (
        <div className="bg-gray-50 rounded-lg p-4">
          <VoiceInput
            onTranscriptionComplete={handleVoiceData}
            onError={handleVoiceError}
          />
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {submitError && (
            <div className="md:col-span-2 bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-sm text-red-600">{submitError}</p>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700">
              Business Name {!isRetail && <span className="text-red-500">*</span>}
            </label>
            <input
              type="text"
              {...register('name')}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              aria-invalid={errors.name ? "true" : "false"}
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">
              Customer Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              {...register('contactName')}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              aria-invalid={errors.contactName ? "true" : "false"}
            />
            {errors.contactName && (
              <p className="mt-1 text-sm text-red-600">{errors.contactName.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Referral Source</label>
            <input
              type="text"
              {...register('customerSource')}
              placeholder="How did you hear about us?"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Email</label>
            <input
              type="email"
              {...register('email')}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              aria-invalid={errors.email ? "true" : "false"}
            />
            {errors.email && (
              <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Phone</label>
            <input
              type="tel"
              {...register('phone')}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              aria-invalid={errors.phone ? "true" : "false"}
            />
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700">Address</label>
            <input
              type="text"
              {...register('address')}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              aria-invalid={errors.address ? "true" : "false"}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Channel Type</label>
            <select
              {...register('channelType')}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              aria-invalid={errors.channelType ? "true" : "false"}
            >
              <option value="wholesale">Wholesale</option>
              <option value="retail">Retail</option>
              <option value="distributor">Distributor</option>
            </select>
            {errors.channelType && (
              <p className="mt-1 text-sm text-red-600">{errors.channelType.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Status</label>
            <select
              {...register('status')}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              aria-invalid={errors.status ? "true" : "false"}
            >
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Payment Terms</label>
            <input
              type="text"
              {...register('paymentTerms')}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              aria-invalid={errors.paymentTerms ? "true" : "false"}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Credit Limit</label>
            <input
              type="number"
              {...register('creditLimit', { valueAsNumber: true })}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              aria-invalid={errors.creditLimit ? "true" : "false"}
            />
          </div>
        </div>

        <div className="flex justify-end gap-4">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
          >
            {isSubmitting ? 'Saving...' : customer ? 'Update Customer' : 'Create Customer'}
          </button>
        </div>
      </form>
    </div>
  );
}
