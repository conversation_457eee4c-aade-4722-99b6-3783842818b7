import { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { supabase } from '../../lib/supabase';
import { createReceivingWithLot } from '../../lib/api';
import BatchNumberGenerator from '../batch/BatchNumberGenerator';
import CCPMonitoring from '../compliance/CCPMonitoring';
import { useNavigationContext } from '../../contexts/NavigationContext';
import VoiceFormIntegration from '../voice/VoiceFormIntegration';
import ConversationalVoiceInterface from '../voice/ConversationalVoiceInterface';

// Event type options mapped to internal values used across the app
const EVENT_OPTIONS = [
  // Core HACCP Events
  { label: 'Receiving', value: 'receiving', category: 'Core' },
  { label: 'Disposal', value: 'disposal', category: 'Core' },
  { label: 'Physical Count', value: 'physical_count', category: 'Core' },
  { label: 'Sales', value: 'sale', category: 'Core' },
] as const;

const UNIT_OPTIONS = [
  { label: 'Pounds (lbs)', value: 'lbs' },
  { label: 'Kilograms (kg)', value: 'kg' },
  { label: 'Cases', value: 'cases' },
  { label: 'Units', value: 'units' },
] as const;

const haccpEventSchema = z.object({
  eventType: z.enum(['receiving', 'disposal', 'physical_count', 'sale']),
  productId: z.string().uuid({ message: 'Select a product' }),
  vendorId: z.string().uuid().optional().or(z.literal('')),
  customerId: z.string().uuid().optional().or(z.literal('')),
  quantity: z.number({ invalid_type_error: 'Quantity is required' }).positive('Enter a positive quantity'),
  unit: z.enum(['lbs', 'kg', 'cases', 'units']).default('lbs'),
  eventDate: z.string().optional(), // When the event actually occurred
  eventTime: z.string().optional(), // Time when the event occurred
  condition: z.enum(['Excellent', 'Good', 'Fair', 'Poor', 'Damaged']).optional(),
  unitPrice: z.union([z.number().min(0, 'Must be >= 0'), z.nan()]).optional().transform((val) => {
    if (val === undefined || Number.isNaN(val)) return undefined;
    return val;
  }),
  notes: z.string().max(500, 'Max 500 chars').optional(),
  // Batch number creation for Receiving
  batchNumberMode: z.enum(['auto', 'manual']).default('auto'),
  manualBatchNumber: z.string().max(64).optional(),
  // Temperature monitoring for HACCP compliance
  temperature: z.union([z.number(), z.nan()]).optional().transform((val) => {
    if (val === undefined || Number.isNaN(val)) return undefined;
    return val;
  }),
  temperatureUnit: z.enum(['celsius', 'fahrenheit']).default('fahrenheit'),
  requiresCcpMonitoring: z.boolean().default(false),
}).refine((data) => {
  if (data.eventType === 'receiving') return !!data.vendorId && !!data.eventDate;
  if (data.eventType === 'sale') return !!data.customerId;
  return true;
}, {
  message: 'Missing required fields for selected event',
  path: ['eventType']
}).refine((data) => {
  if (data.eventType === 'receiving' && data.batchNumberMode === 'manual') {
    return !!data.manualBatchNumber && data.manualBatchNumber.trim().length > 0;
  }
  return true;
}, {
  message: 'Batch number is required when using Manual entry',
  path: ['manualBatchNumber']
});

export type HACCPEventFormData = z.infer<typeof haccpEventSchema>;

// Voice processing interfaces
interface VoiceExtractedData {
  quantity?: number;
  unit?: string;
  product?: string;
  vendor?: string;
  customer?: string;
  condition?: string;
  temperature?: number;
  temperatureUnit?: string;
  notes?: string;
  processing_method?: string;
  quality_grade?: string;
  market_form?: string;
  occurred_at?: string;
}

interface ConversationalVoiceData {
  product_name?: string;
  quantity?: number;
  unit?: string;
  vendor_name?: string;
  notes?: string;
}

interface HACCPEventFormProps {
  onSuccess: () => void;
  onCancel: () => void;
  // Optional external controls
  eventTypeOverride?: 'receiving' | 'disposal' | 'physical_count' | 'sale';
  useExternalBatchControls?: boolean;
  batchNumberModeOverride?: 'auto' | 'manual';
  manualBatchNumberOverride?: string;
  // When provided (YYYY-MM-DD), the form will prefill the date and use it for created_at
  dateOverride?: string;
}

export default function HACCPEventForm({ onSuccess, onCancel, eventTypeOverride, useExternalBatchControls, batchNumberModeOverride, manualBatchNumberOverride, dateOverride }: HACCPEventFormProps) {
  const { setActiveView, setViewFilter } = useNavigationContext();
  const { register, handleSubmit, watch, setValue, formState: { errors, isSubmitting } } = useForm<HACCPEventFormData>({
    resolver: zodResolver(haccpEventSchema),
    defaultValues: {
      eventType: 'receiving',
      unit: 'lbs',
      eventDate: new Date().toISOString().split('T')[0],
      eventTime: new Date().toTimeString().slice(0, 5), // HH:MM format
      batchNumberMode: 'auto',
      temperatureUnit: 'fahrenheit',
    }
  });

  const eventType = watch('eventType');
  const batchNumberMode = watch('batchNumberMode');
  const productId = watch('productId');
  const vendorId = watch('vendorId');
  const eventDate = watch('eventDate');

  const [products, setProducts] = useState<Array<{ id: string; name: string }>>([]);
  const [vendors, setVendors] = useState<Array<{ id: string; name: string }>>([]);
  const [customers, setCustomers] = useState<Array<{ id: string; name: string }>>([]);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState<string | null>(null);
  const [showCcpMonitoring, setShowCcpMonitoring] = useState(false);
  const [currentLotId, setCurrentLotId] = useState<string | null>(null);
  const [useConversationalVoice, setUseConversationalVoice] = useState(false);
  
  const handleViewEvents = (eventType: string) => {
    setViewFilter('eventType', eventType);
    setActiveView('Events');
  };

  useEffect(() => {
    const load = async () => {
      const [prodRes, vendRes, custRes] = await Promise.all([
        supabase.from('Products').select('id, name').order('name', { ascending: true }),
        supabase.from('vendors').select('id, name').order('name', { ascending: true }),
        supabase.from('customers').select('id, name').order('name', { ascending: true }),
      ]);
      
      // Remove duplicates by name, keeping the first occurrence
      if (!prodRes.error && prodRes.data) {
        const uniqueProducts = prodRes.data.filter((product, index, array) => 
          array.findIndex(p => p.name === product.name) === index
        );
        setProducts(uniqueProducts);
      }
      if (!vendRes.error && vendRes.data) {
        const uniqueVendors = vendRes.data.filter((vendor, index, array) => 
          array.findIndex(v => v.name === vendor.name) === index
        );
        setVendors(uniqueVendors);
      }
      if (!custRes.error && custRes.data) {
        const uniqueCustomers = custRes.data.filter((customer, index, array) => 
          array.findIndex(c => c.name === customer.name) === index
        );
        setCustomers(uniqueCustomers);
      }
    };
    load();
  }, []);

  const productNameById = useMemo(() => Object.fromEntries(products.map(p => [p.id, p.name])), [products]);
  const vendorNameById = useMemo(() => Object.fromEntries(vendors.map(v => [v.id, v.name])), [vendors]);

  // Apply external overrides
  useEffect(() => {
    if (eventTypeOverride) setValue('eventType', eventTypeOverride);
  }, [eventTypeOverride, setValue]);

  useEffect(() => {
    if (batchNumberModeOverride) setValue('batchNumberMode', batchNumberModeOverride);
  }, [batchNumberModeOverride, setValue]);

  useEffect(() => {
    if (typeof manualBatchNumberOverride === 'string') setValue('manualBatchNumber', manualBatchNumberOverride);
  }, [manualBatchNumberOverride, setValue]);

  // Apply external date override for event date (YYYY-MM-DD)
  useEffect(() => {
    if (dateOverride && /\d{4}-\d{2}-\d{2}/.test(dateOverride)) {
      setValue('eventDate', dateOverride);
    }
  }, [dateOverride, setValue]);

  const onSubmit = async (data: HACCPEventFormData) => {
    setSubmitError(null);
    setSubmitSuccess(null);
    console.log('🚀 Form submission started:', data);
    try {
      const { eventType, productId, quantity, unit, unitPrice, notes } = data;
      let batchMeta: { batch_number?: string; lot_id?: string; tlc?: string } = {};

      // Resolve created_at using dateOverride if provided (set to local noon to avoid TZ shift)
      const createdAtISO = (() => {
        if (dateOverride && /\d{4}-\d{2}-\d{2}/.test(dateOverride)) {
          const [y, m, d] = dateOverride.split('-').map(Number);
          const dt = new Date(y, (m as number) - 1, d as number, 12, 0, 0, 0);
          return dt.toISOString();
        }
        return new Date().toISOString();
      })();

      // Compute occurred_at for the actual event time
      // Priority: eventDate+eventTime -> dateOverride (calendar click) -> now
      const occurredAtISO = (() => {
        if (data.eventDate && /\d{4}-\d{2}-\d{2}/.test(data.eventDate)) {
          const [y, m, d] = data.eventDate.split('-').map(Number);
          let hour = 12, minute = 0; // Default to noon
          
          // Parse time if provided
          if (data.eventTime && /\d{1,2}:\d{2}/.test(data.eventTime)) {
            const [h, min] = data.eventTime.split(':').map(Number);
            hour = h;
            minute = min;
          }
          
          const dt = new Date(y, (m as number) - 1, d as number, hour, minute, 0, 0);
          return dt.toISOString();
        }
        if (dateOverride && /\d{4}-\d{2}-\d{2}/.test(dateOverride)) {
          const [y, m, d] = dateOverride.split('-').map(Number);
          let hour = 12, minute = 0; // Default to noon
          
          // Parse time if provided
          if (data.eventTime && /\d{1,2}:\d{2}/.test(data.eventTime)) {
            const [h, min] = data.eventTime.split(':').map(Number);
            hour = h;
            minute = min;
          }
          
          const dt = new Date(y, (m as number) - 1, d as number, hour, minute, 0, 0);
          return dt.toISOString();
        }
        return new Date().toISOString();
      })();

      // Receiving: prefer traceability path (partners/lots). If unavailable (e.g., 404 tables),
      // gracefully fall back to inserting into inventory_events only so the form still works.
      if (eventType === 'receiving') {
        const vendorId = data.vendorId as string;
        const vendorName = vendorNameById[vendorId];
        const productName = productNameById[productId];
        const eventDate = data.eventDate;

        if (!vendorName || !productName || !eventDate) {
          throw new Error('Missing product, vendor, or date');
        }

        try {
          const result = await createReceivingWithLot({
            productName,
            quantity,
            unit,
            vendorName,
            receivingDate: eventDate,
            condition: data.condition,
            notes,
            tlc: data.batchNumberMode === 'manual' ? data.manualBatchNumber : undefined,
          });
          if (!result.success) throw new Error(result.error?.message || 'Failed to create receiving/lot');
          batchMeta = { batch_number: result.data?.tlc, lot_id: result.data?.lot_id, tlc: result.data?.tlc };

          // Also log to unified inventory_events for dashboards/reporting
          const totalAmount = unitPrice ? Number((quantity * unitPrice).toFixed(2)) : null;
          
          // Insert inventory event with occurred_at in metadata until migration is applied
          const inventoryEventData = {
            event_type: 'receiving',
            product_id: productId,
            quantity,
            unit_price: unitPrice ?? null,
            total_amount: totalAmount,
            notes: notes || null,
            metadata: {
              source: 'haccp-form',
              ...batchMeta,
              batch_number_mode: data.batchNumberMode,
              unit,
              vendor_id: vendorId,
              vendor_name: vendorName,
              occurred_at: occurredAtISO, // Store in metadata until column exists
            },
            created_at: createdAtISO,
          };
          
          const { error: invErr } = await supabase.from('inventory_events').insert(inventoryEventData);
          if (invErr) throw invErr;
        } catch (traceErr) {
          console.warn('Traceability path failed, falling back to inventory_events only:', traceErr);
          const totalAmount = unitPrice ? Number((quantity * unitPrice).toFixed(2)) : null;
          const fallbackMeta: Record<string, unknown> = {
            source: 'haccp-form',
            batch_number_mode: data.batchNumberMode,
          };
          // If user used the generator (switches to manual and sets manualBatchNumber), persist it as TLC
          if (data.batchNumberMode === 'manual' && data.manualBatchNumber) {
            fallbackMeta['tlc'] = data.manualBatchNumber;
            fallbackMeta['batch_number'] = data.manualBatchNumber;
          }
          // Persist unit and vendor context in metadata for dashboards/reporting
          fallbackMeta['unit'] = unit;
          fallbackMeta['vendor_id'] = vendorId;
          fallbackMeta['vendor_name'] = vendorName;
          // Fallback: insert to inventory_events only with occurred_at in metadata
          const fallbackEventData = {
            event_type: 'receiving',
            product_id: productId,
            quantity,
            unit_price: unitPrice ?? null,
            total_amount: totalAmount,
            notes: notes || null,
            metadata: {
              ...fallbackMeta,
              occurred_at: occurredAtISO, // Store in metadata until column exists
            },
            created_at: createdAtISO,
          };
          
          const { error: invErr } = await supabase.from('inventory_events').insert(fallbackEventData);
          if (invErr) throw invErr;
        }
      }

      // Disposal / Physical Count / Sales: insert into inventory_events only
      if (eventType !== 'receiving') {
        const payload: Record<string, unknown> = {
          event_type: eventType,
          product_id: productId,
          quantity,
          notes: notes || null,
          metadata: { 
            source: 'haccp-form', 
            unit,
            occurred_at: occurredAtISO // Store in metadata until column exists
          },
          created_at: createdAtISO,
        };
        
        if (eventType === 'sale') {
          // Store customer info in metadata since customer_id column doesn't exist yet
          payload.metadata = { 
            ...payload.metadata as Record<string, unknown>, 
            customer_id: data.customerId || null 
          };
          if (typeof data.unitPrice === 'number') {
            payload.unit_price = data.unitPrice;
            payload.total_amount = Number((quantity * data.unitPrice).toFixed(2));
          }
        }
        const { error } = await supabase.from('inventory_events').insert(payload);
        if (error) throw error;
      }

      // Success feedback
      console.log('✅ Form submission successful!');
      const successMessage = eventType === 'receiving' && batchMeta.batch_number 
        ? `Successfully saved ${eventType} event! Batch number: ${batchMeta.batch_number}`
        : `Successfully saved ${eventType} event to database!`;
      
      setSubmitSuccess(successMessage);
      
      // Check if CCP monitoring is required
      if (data.requiresCcpMonitoring && productId) {
        setCurrentLotId(batchMeta.lot_id || null);
        setShowCcpMonitoring(true);
      } else {
        // Call onSuccess immediately to trigger calendar reload
        onSuccess();
        // Clear success message after delay
        setTimeout(() => {
          setSubmitSuccess(null);
        }, 3000);
      }
    } catch (err: unknown) {
      console.error('HACCP submit failed:', err);
      setSubmitError(err instanceof Error ? err.message : 'Failed to submit');
    }
  };

  const handleCcpMonitoringComplete = () => {
    setShowCcpMonitoring(false);
    setCurrentLotId(null);
    onSuccess();
  };

  if (showCcpMonitoring) {
    return (
      <div className="space-y-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-blue-900 mb-2">
            HACCP Critical Control Point Monitoring Required
          </h3>
          <p className="text-sm text-blue-700 mb-4">
            This event requires CCP monitoring. Please complete the monitoring before continuing.
          </p>
        </div>
        <CCPMonitoring 
          productId={productId} 
          lotId={currentLotId || undefined}
        />
        <div className="flex justify-end gap-3">
          <button
            type="button"
            onClick={() => setShowCcpMonitoring(false)}
            className="px-4 py-2 rounded-lg bg-gray-200 text-gray-800 hover:bg-gray-300"
          >
            Skip CCP Monitoring
          </button>
          <button
            type="button"
            onClick={handleCcpMonitoringComplete}
            className="px-4 py-2 rounded-lg bg-blue-600 text-white hover:bg-blue-700"
          >
            Complete Event
          </button>
        </div>
      </div>
    );
  }

  const handleVoiceDataExtracted = (voiceData: VoiceExtractedData) => {
    // Map voice-extracted data to form fields
    if (voiceData.quantity !== undefined) {
      setValue('quantity', voiceData.quantity, { shouldValidate: true, shouldDirty: true });
    }
    if (voiceData.unit && ['lbs', 'kg', 'cases', 'units'].includes(voiceData.unit)) {
      setValue('unit', voiceData.unit as 'lbs' | 'kg' | 'cases' | 'units', { shouldValidate: true, shouldDirty: true });
    }
    
    // Enhanced product matching with fuzzy search
    if (voiceData.product && products.length > 0) {
      const productName = voiceData.product.toLowerCase();
      // First try exact match
      let matchedProduct = products.find(p => 
        p.name.toLowerCase() === productName
      );
      
      // If no exact match, try partial match
      if (!matchedProduct) {
        matchedProduct = products.find(p => 
          p.name.toLowerCase().includes(productName) ||
          productName.includes(p.name.toLowerCase())
        );
      }
      
      if (matchedProduct) {
        setValue('productId', matchedProduct.id, { shouldValidate: true, shouldDirty: true });
      }
    }
    
    // Enhanced vendor matching with common aliases
    if (voiceData.vendor && vendors.length > 0) {
      const vendorName = voiceData.vendor.toLowerCase();
      const vendorAliases: Record<string, string[]> = {
        '49th State Seafoods': ['forty ninth state', '49th state', 'forty-ninth state', 'forty nine state'],
        'Pacific Seafoods': ['pacific seafood', 'pac seafoods', 'pacific sea foods'],
        'Ocean Fresh Seafoods': ['ocean fresh', 'ocean fresh seafood'],
        'Trident Seafoods': ['trident', 'trident seafood']
      };

      let matchedVendor = vendors.find(v => 
        v.name.toLowerCase() === vendorName
      );

      if (!matchedVendor) {
        // Check aliases
        for (const [name, aliases] of Object.entries(vendorAliases)) {
          if (aliases.some(alias => vendorName.includes(alias))) {
            matchedVendor = vendors.find(v => v.name === name);
            break;
          }
        }
      }

      if (!matchedVendor) {
        // Fallback to partial match
        matchedVendor = vendors.find(v => 
          v.name.toLowerCase().includes(vendorName) ||
          vendorName.includes(v.name.toLowerCase())
        );
      }

      if (matchedVendor) {
        setValue('vendorId', matchedVendor.id, { shouldValidate: true, shouldDirty: true });
      }
    }
    
    if (voiceData.customer && customers.length > 0) {
      const customerName = voiceData.customer.toLowerCase();
      const matchedCustomer = customers.find(c => 
        c.name.toLowerCase().includes(customerName) ||
        customerName.includes(c.name.toLowerCase())
      );
      if (matchedCustomer) {
        setValue('customerId', matchedCustomer.id, { shouldValidate: true, shouldDirty: true });
      }
    }
    if (voiceData.condition) {
      const capitalizedCondition = voiceData.condition.charAt(0).toUpperCase() + voiceData.condition.slice(1) as 'Excellent' | 'Good' | 'Fair' | 'Poor' | 'Damaged';
      setValue('condition', capitalizedCondition, { shouldValidate: true, shouldDirty: true });
    }
    if (voiceData.temperature !== undefined) {
      setValue('temperature', voiceData.temperature, { shouldValidate: true, shouldDirty: true });
    }
    if (voiceData.temperatureUnit) {
      setValue('temperatureUnit', voiceData.temperatureUnit as 'celsius' | 'fahrenheit', { shouldValidate: true, shouldDirty: true });
    }
    
    // Enhanced notes with processing details
    let enhancedNotes = voiceData.notes || '';
    if (voiceData.processing_method) {
      enhancedNotes += `${enhancedNotes ? ' | ' : ''  }Processing: ${voiceData.processing_method}`;
    }
    if (voiceData.quality_grade) {
      enhancedNotes += `${enhancedNotes ? ' | ' : ''  }Quality: ${voiceData.quality_grade}`;
    }
    if (voiceData.market_form) {
      enhancedNotes += `${enhancedNotes ? ' | ' : ''  }Form: ${voiceData.market_form}`;
    }
    if (enhancedNotes) {
      setValue('notes', enhancedNotes, { shouldValidate: true, shouldDirty: true });
    }

    // Set occurred_at if provided by voice processing
    if (voiceData.occurred_at) {
      const occurredDate = new Date(voiceData.occurred_at).toISOString().split('T')[0];
      setValue('eventDate', occurredDate, { shouldValidate: true, shouldDirty: true });
    }
  };

  const handleConversationalVoiceComplete = (transactionData: ConversationalVoiceData) => {
    // Map conversational voice data to form fields
    if (transactionData.product_name && products.length > 0) {
      const productName = transactionData.product_name.toLowerCase();
      const matchedProduct = products.find(p => 
        p.name.toLowerCase().includes(productName)
      );
      if (matchedProduct) {
        setValue('productId', matchedProduct.id, { shouldValidate: true, shouldDirty: true });
      }
    }
    
    if (transactionData.quantity) {
      setValue('quantity', transactionData.quantity, { shouldValidate: true, shouldDirty: true });
    }
    
    if (transactionData.unit && ['lbs', 'kg', 'cases', 'units'].includes(transactionData.unit)) {
      setValue('unit', transactionData.unit as 'lbs' | 'kg' | 'cases' | 'units', { shouldValidate: true, shouldDirty: true });
    }
    
    if (transactionData.vendor_name && vendors.length > 0) {
      const vendorName = transactionData.vendor_name.toLowerCase();
      const matchedVendor = vendors.find(v => 
        v.name.toLowerCase().includes(vendorName)
      );
      if (matchedVendor) {
        setValue('vendorId', matchedVendor.id, { shouldValidate: true, shouldDirty: true });
      }
    }
    
    if (transactionData.notes) {
      setValue('notes', transactionData.notes, { shouldValidate: true, shouldDirty: true });
    }
    
    // Set success message and close conversational interface
    setSubmitSuccess(`Successfully processed via conversational voice: ${transactionData.product_name}`);
    setUseConversationalVoice(false);
    
    // Auto-submit the form after a delay
    setTimeout(() => {
      setSubmitSuccess(null);
      onSuccess();
    }, 3000);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Voice Integration Mode Toggle */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-sm font-medium text-blue-900">Voice Input Mode</h3>
            <p className="text-xs text-blue-700">Choose between simple voice commands or intelligent conversation</p>
          </div>
          <button
            type="button"
            onClick={() => setUseConversationalVoice(!useConversationalVoice)}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              useConversationalVoice 
                ? 'bg-blue-600 text-white hover:bg-blue-700' 
                : 'bg-white text-blue-600 border border-blue-300 hover:bg-blue-50'
            }`}
          >
            {useConversationalVoice ? 'Switch to Simple Voice' : 'Switch to Conversational Voice'}
          </button>
        </div>
      </div>

      {/* Conversational Voice Interface */}
      {useConversationalVoice && (
        <ConversationalVoiceInterface
          onTransactionComplete={handleConversationalVoiceComplete}
          eventType={eventType}
          className="mb-6"
        />
      )}

      {/* Simple Voice Form Integration */}
      {!useConversationalVoice && (
        <VoiceFormIntegration
          onDataExtracted={handleVoiceDataExtracted}
          eventType={eventType}
          className="mb-4"
        />
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Event Type - only show if not using external controls */}
        {!useExternalBatchControls && (
          <div>
            <label htmlFor="eventType" className="block text-sm font-medium text-gray-700">Event Type</label>
            <select id="eventType" {...register('eventType')} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
              {Object.entries(
                EVENT_OPTIONS.reduce((acc, opt) => {
                  if (!acc[opt.category]) acc[opt.category] = [];
                  acc[opt.category].push(opt);
                  return acc;
                }, {} as Record<string, typeof EVENT_OPTIONS[number][]>)
              ).map(([category, options]) => (
                <optgroup key={category} label={category}>
                  {options.map(opt => (
                    <option key={opt.value} value={opt.value}>{opt.label}</option>
                  ))}
                </optgroup>
              ))}
            </select>
            {errors.eventType && <p className="mt-1 text-sm text-red-600">{errors.eventType.message}</p>}
          </div>
        )}

        {/* Product */}
        <div>
          <label htmlFor="productId" className="block text-sm font-medium text-gray-700">
            Product {products.length > 0 && <span className="text-xs text-gray-500">({products.length} available)</span>}
          </label>
          <select id="productId" {...register('productId')} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            <option value="">{products.length === 0 ? 'Loading products...' : 'Select a product'}</option>
            {products.map(p => <option key={p.id} value={p.id}>{p.name}</option>)}
          </select>
          {errors.productId && <p className="mt-1 text-sm text-red-600">{errors.productId.message}</p>}
          {products.length === 0 && (
            <p className="mt-1 text-xs text-yellow-600">No products found. Check console for errors.</p>
          )}
        </div>

        {/* Quantity */}
        <div>
          <label htmlFor="quantity" className="block text-sm font-medium text-gray-700">Quantity</label>
          <input id="quantity" type="number" step="0.01" {...register('quantity', { valueAsNumber: true })} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" />
          {errors.quantity && <p className="mt-1 text-sm text-red-600">{errors.quantity.message}</p>}
        </div>

        {/* Unit */}
        <div>
          <label htmlFor="unit" className="block text-sm font-medium text-gray-700">Unit</label>
          <select id="unit" {...register('unit')} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            {UNIT_OPTIONS.map(u => <option key={u.value} value={u.value}>{u.label}</option>)}
          </select>
          {errors.unit && <p className="mt-1 text-sm text-red-600">{errors.unit.message as string}</p>}
        </div>

        
        {/* Receiving-only fields */}
        {eventType === 'receiving' && (
          <>
            <div>
              <label htmlFor="vendorId" className="block text-sm font-medium text-gray-700">Vendor</label>
              <select id="vendorId" {...register('vendorId')} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                <option value="">Select a vendor</option>
                {vendors.map(v => <option key={v.id} value={v.id}>{v.name}</option>)}
              </select>
              {errors.vendorId && <p className="mt-1 text-sm text-red-600">Vendor is required</p>}
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="eventDate" className="block text-sm font-medium text-gray-700">Event Date</label>
                <input id="eventDate" type="date" {...register('eventDate')} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" />
              </div>
              <div>
                <label htmlFor="eventTime" className="block text-sm font-medium text-gray-700">Event Time</label>
                <input id="eventTime" type="time" {...register('eventTime')} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" />
              </div>
              <div className="col-span-2">
                <p className="text-xs text-gray-500">When this event actually occurred (may be different from when it's being recorded)</p>
              </div>
            </div>
            {!useExternalBatchControls && (
              <div>
                <label htmlFor="batchNumberMode" className="block text-sm font-medium text-gray-700">Batch Number</label>
                <select id="batchNumberMode" {...register('batchNumberMode')} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                  <option value="auto">Auto-generate (recommended)</option>
                  <option value="manual">Manual entry</option>
                </select>
                {/* Generator helper - can fill manual and switch mode */}
                <BatchNumberGenerator
                  date={eventDate}
                  productId={productId}
                  vendorId={vendorId}
                  productNameById={productNameById}
                  vendorNameById={vendorNameById}
                  onGenerate={(val) => {
                    setValue('manualBatchNumber', val, { shouldDirty: true, shouldValidate: true });
                    setValue('batchNumberMode', 'manual', { shouldDirty: true, shouldValidate: true });
                  }}
                  disabled={isSubmitting}
                />
                {batchNumberMode === 'manual' && (
                  <input
                    id="manualBatchNumber"
                    placeholder="Enter batch number (TLC)"
                    type="text"
                    {...register('manualBatchNumber')}
                    className="mt-2 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  />
                )}
                {errors.manualBatchNumber && (
                  <p className="mt-1 text-sm text-red-600">{errors.manualBatchNumber.message as string}</p>
                )}
              </div>
            )}
            {useExternalBatchControls && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Batch Number Generator</label>
                <BatchNumberGenerator
                  date={eventDate}
                  productId={productId}
                  vendorId={vendorId}
                  productNameById={productNameById}
                  vendorNameById={vendorNameById}
                  onGenerate={(val) => {
                    setValue('manualBatchNumber', val, { shouldDirty: true, shouldValidate: true });
                    setValue('batchNumberMode', 'manual', { shouldDirty: true, shouldValidate: true });
                  }}
                  disabled={isSubmitting}
                />
              </div>
            )}
            <div>
              <label htmlFor="condition" className="block text-sm font-medium text-gray-700">Condition</label>
              <select id="condition" {...register('condition')} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                {['Excellent','Good','Fair','Poor','Damaged'].map(c => <option key={c} value={c}>{c}</option>)}
              </select>
            </div>
            <div>
              <label htmlFor="unitPrice" className="block text-sm font-medium text-gray-700">Cost per Unit <span className="text-gray-400 font-normal">(optional)</span></label>
              <input id="unitPrice" type="number" step="0.01" min="0" {...register('unitPrice', { valueAsNumber: true })} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="Enter cost per unit" />
            </div>
          </>
        )}

        {/* Sales-only fields */}
        {eventType === 'sale' && (
          <>
            <div>
              <label htmlFor="customerId" className="block text-sm font-medium text-gray-700">Customer</label>
              <select id="customerId" {...register('customerId')} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                <option value="">Select a customer</option>
                {customers.map(c => <option key={c.id} value={c.id}>{c.name}</option>)}
              </select>
              {errors.customerId && <p className="mt-1 text-sm text-red-600">Customer is required</p>}
            </div>
            <div>
              <label htmlFor="unitPrice" className="block text-sm font-medium text-gray-700">Unit Price</label>
              <input id="unitPrice" type="number" step="0.01" min="0" {...register('unitPrice', { valueAsNumber: true })} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" />
            </div>
          </>
        )}

        {/* Temperature Monitoring for HACCP */}
        {(eventType === 'receiving' || eventType === 'sale') && (
          <>
            <div>
              <label htmlFor="temperature" className="block text-sm font-medium text-gray-700">Temperature (HACCP)</label>
              <div className="flex gap-2">
                <input
                  id="temperature"
                  type="number"
                  step="0.1"
                  {...register('temperature', { valueAsNumber: true })}
                  className="flex-1 rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="Enter temperature"
                />
                <select
                  {...register('temperatureUnit')}
                  className="rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                >
                  <option value="fahrenheit">°F</option>
                  <option value="celsius">°C</option>
                </select>
              </div>
              <p className="mt-1 text-xs text-gray-500">
                Required for HACCP compliance monitoring
              </p>
            </div>
            <div className="flex items-center">
              <input
                id="requiresCcpMonitoring"
                type="checkbox"
                {...register('requiresCcpMonitoring')}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label htmlFor="requiresCcpMonitoring" className="ml-2 text-sm text-gray-700">
                Trigger CCP Monitoring
              </label>
            </div>
          </>
        )}

        {/* Notes */}
        <div className={eventType === 'receiving' ? 'md:col-span-2' : ''}>
          <label htmlFor="notes" className="block text-sm font-medium text-gray-700">Notes</label>
          <textarea id="notes" rows={3} {...register('notes')} className="mt-1 w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" />
          {errors.notes && <p className="mt-1 text-sm text-red-600">{errors.notes.message}</p>}
        </div>
      </div>

      {submitError && (
        <div className="bg-red-50 border border-red-200 rounded p-3 text-sm text-red-700">{submitError}</div>
      )}

      {submitSuccess && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-green-800">
          <div className="flex items-start gap-3">
            <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-white text-xs font-bold">✓</span>
            </div>
            <div className="flex-1">
              <div className="font-medium text-green-900">Event Saved Successfully</div>
              <div className="text-sm text-green-700 mt-1">{submitSuccess}</div>
              <button
                onClick={() => handleViewEvents(eventType)}
                className="mt-3 inline-flex items-center px-3 py-2 text-sm font-medium text-blue-700 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 hover:border-blue-300 transition-colors"
              >
                View all {eventType === 'receiving' ? 'receiving' : 
                        eventType === 'disposal' ? 'disposal' :
                        eventType === 'physical_count' ? 'physical count' :
                        eventType === 'sale' ? 'sales' : eventType} events
                <span className="ml-1">→</span>
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="flex justify-end gap-3">
        <button 
          type="button" 
          onClick={onCancel} 
          className="px-4 py-2 text-sm font-medium rounded-lg bg-gray-200 text-gray-800 hover:bg-gray-300 transition-colors"
        >
          Cancel
        </button>
        <button 
          type="submit" 
          disabled={isSubmitting} 
          className="px-4 py-2 text-sm font-medium rounded-lg bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50 transition-colors"
        >
          {isSubmitting ? (
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              Saving to Database...
            </div>
          ) : 'Save Event'}
        </button>
      </div>
    </form>
  );
}
