# Components Directory

This directory contains all React components for the Pacific Cloud Seafoods Manager application, organized by feature and functionality.

## Overview

Components are structured to promote reusability, maintainability, and clear separation of concerns. Each component follows React best practices with TypeScript for type safety.

## Directory Structure

### Core Application Components

#### Root Level Components
- `App.tsx` - Main application wrapper (located in `/src`)
- `AppLayout.tsx` - Main layout wrapper with navigation
- `Dashboard.tsx` - Main dashboard with key metrics and navigation
- `Sidebar.tsx` - Navigation sidebar component
- `ErrorBoundary.tsx` - Error handling wrapper component

#### Feature Components
- `Analytics.tsx` - Analytics dashboard and reporting
- `Auth.tsx` - Authentication forms and user management
- `BatchTracking.tsx` - Batch tracking and traceability
- `CategoryView.tsx` - Product category management
- `EventsTable.tsx` - Event data table display
- `EventsView.tsx` - Event management interface
- `HACCPCalendar.tsx` - HACCP compliance calendar
- `HACCPEventsView.tsx` - HACCP event management
- `Inventory.tsx` - Main inventory management interface
- `Messages.tsx` - System messaging and notifications
- `ProductDetails.tsx` - Individual product detail view
- `Products.tsx` - Product catalog and management
- `Settings.tsx` - Application settings and configuration

### Feature-Specific Directories

#### `/accessibility` - Accessibility Components
Components focused on accessibility compliance and assistive technology support.

#### `/batch` - Batch Management
Components for batch tracking, lot management, and traceability workflows.

#### `/compliance` - Compliance Management
HACCP, FDA, and other regulatory compliance components including:
- Compliance dashboards
- Audit trail components
- Regulatory reporting interfaces

#### `/customers` - Customer Management
Customer relationship management components including:
- Customer profiles
- Order history
- Communication tracking

#### `/forms` - Reusable Form Components
Generic form components and form utilities:
- Input components
- Validation helpers
- Form layouts

#### `/import` - Data Import
Components for importing data from various sources:
- CSV import interfaces
- Data mapping tools
- Import validation

#### `/layout` - Layout Components
Structural layout components:
- Headers and footers
- Grid systems
- Responsive containers

#### `/modals` - Modal Components
Reusable modal dialogs for various purposes:
- Confirmation dialogs
- Data entry modals
- Information displays

#### `/partners` - Partner Management
Components for managing business partnerships and integrations.

#### `/ui` - UI Library Components
Base UI components built on Radix UI:
- Buttons, inputs, selects
- Toast notifications
- Alert dialogs
- Tabs and navigation

#### `/vendors` - Vendor Management
Vendor relationship management including:
- Vendor profiles
- Performance tracking
- Report cards
- Communication interfaces

#### `/voice` - Voice Interface Components
Voice-enabled components for hands-free operation:
- `VoiceEventList.tsx` - Display voice-captured events
- `VoiceEventEditor.tsx` - Edit voice events
- `AudioPlayback.tsx` - Audio playback controls
- `QualityReviewPanel.tsx` - Review low-confidence voice events

## Component Architecture

### Design Patterns

1. **Container/Presentational Pattern**: Separate data logic from presentation
2. **Compound Components**: Complex components broken into smaller, composable parts
3. **Render Props**: Flexible component composition for shared logic
4. **Custom Hooks**: Extract component logic into reusable hooks

### Props and TypeScript

All components use TypeScript interfaces for props:
```typescript
interface ComponentProps {
  required: string;
  optional?: number;
  children?: React.ReactNode;
}
```

### State Management

- Local state with `useState` for component-specific data
- Context API for shared state across component trees
- React Hook Form for form state management

## Integration Points

### Database Integration
Components connect to Supabase through:
- `/src/lib/supabase.ts` - Database client
- `/src/services/*` - Business logic services
- `/src/types/schema.ts` - Database type definitions

### Voice Integration
Voice components integrate with:
- `/src/hooks/useVoiceCommands.ts` - Voice command processing
- `/src/services/VoiceEventService.ts` - Voice event management
- `/src/services/AudioStorageService.ts` - Audio file handling

### Styling
- Tailwind CSS for utility-first styling
- Radix UI for accessible base components
- Custom CSS modules for complex styling needs

## Testing Strategy

Each component directory includes `__tests__` subdirectories with:
- Unit tests using Vitest and React Testing Library
- Integration tests for component interactions
- Accessibility tests using jest-axe
- Visual regression tests where appropriate

## Performance Optimization

- Lazy loading for large components using `React.lazy()`
- Memoization with `React.memo()` for expensive renders
- Virtual scrolling for large lists
- Code splitting at the route level

## Accessibility

All components follow WCAG 2.1 AA guidelines:
- Semantic HTML structure
- ARIA labels and roles
- Keyboard navigation support
- Screen reader compatibility
- Color contrast compliance

## Development Guidelines

1. **Component Naming**: Use PascalCase for component files
2. **Props Interface**: Define props interface above component
3. **Default Props**: Use default parameters instead of defaultProps
4. **Error Handling**: Implement proper error boundaries
5. **Loading States**: Include loading and error states
6. **Responsive Design**: Mobile-first responsive design

## Common Patterns

### Data Fetching Component
```typescript
const DataComponent: React.FC<Props> = ({ id }) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch logic, error handling, loading states
  
  return (
    // Component JSX
  );
};
```

### Form Component with Validation
```typescript
const FormComponent: React.FC<Props> = () => {
  const { register, handleSubmit, formState: { errors } } = useForm();
  
  // Form logic
  
  return (
    // Form JSX with validation
  );
};
```