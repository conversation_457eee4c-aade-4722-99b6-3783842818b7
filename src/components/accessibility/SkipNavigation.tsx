import React from 'react';

interface SkipLink {
  href: string;
  label: string;
}

interface SkipNavigationProps {
  links?: SkipLink[];
}

const defaultLinks: SkipLink[] = [
  { href: '#main-content', label: 'Skip to main content' },
  { href: '#navigation', label: 'Skip to navigation' },
  { href: '#footer', label: 'Skip to footer' }
];

export default function SkipNavigation({ links = defaultLinks }: SkipNavigationProps) {
  return (
    <div className="sr-only focus-within:not-sr-only">
      <div className="fixed top-0 left-0 z-50 bg-blue-600 text-white p-2 space-x-2">
        {links.map((link) => (
          <a
            key={link.href}
            href={link.href}
            className="bg-blue-700 px-3 py-1 rounded text-sm font-medium hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600"
          >
            {link.label}
          </a>
        ))}
      </div>
    </div>
  );
}

// Screen reader only utility component
export function ScreenReaderOnly({ children }: { children: React.ReactNode }) {
  return <div className="sr-only">{children}</div>;
}

// Live region for announcements
export function LiveRegion({ 
  children, 
  polite = true 
}: { 
  children: React.ReactNode; 
  polite?: boolean; 
}) {
  return (
    <div 
      aria-live={polite ? 'polite' : 'assertive'}
      aria-atomic="true"
      className="sr-only"
    >
      {children}
    </div>
  );
}

// Focus trap for modals
export function useFocusTrap(isActive: boolean) {
  const firstElementRef = React.useRef<HTMLElement>(null);
  const lastElementRef = React.useRef<HTMLElement>(null);

  React.useEffect(() => {
    if (!isActive) return;

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        // Shift + Tab
        if (document.activeElement === firstElementRef.current) {
          e.preventDefault();
          lastElementRef.current?.focus();
        }
      } else {
        // Tab
        if (document.activeElement === lastElementRef.current) {
          e.preventDefault();
          firstElementRef.current?.focus();
        }
      }
    };

    const handleEscapeKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        // Handle escape to close modal
        // This should be handled by the parent component
      }
    };

    document.addEventListener('keydown', handleTabKey);
    document.addEventListener('keydown', handleEscapeKey);

    // Focus first element when trap becomes active
    firstElementRef.current?.focus();

    return () => {
      document.removeEventListener('keydown', handleTabKey);
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isActive]);

  return { firstElementRef, lastElementRef };
}