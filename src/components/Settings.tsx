import React, { useState, useEffect, useCallback } from 'react';
import { User, Bell, Database, Mic, Shield, Save, RefreshCw, Download, Mail, Smartphone, Eye } from 'lucide-react';
import { supabase } from '../lib/supabase';

interface UserProfile {
  id: string;
  email: string;
  full_name: string;
  role: string;
  phone?: string;
  created_at: string;
}

interface AppSettings {
  notifications: {
    email_enabled: boolean;
    push_enabled: boolean;
    inventory_alerts: boolean;
    quality_alerts: boolean;
    expiration_warnings: boolean;
    daily_summaries: boolean;
    low_stock_threshold: number;
  };
  voice: {
    enabled: boolean;
    auto_start: boolean;
    continuous_mode: boolean;
    seafood_terminology: boolean;
    voice_feedback: boolean;
    sensitivity: number;
  };
  display: {
    theme: 'light' | 'dark' | 'auto';
    language: string;
    timezone: string;
    date_format: string;
    time_format: '12h' | '24h';
    currency: string;
  };
  data: {
    auto_backup: boolean;
    backup_frequency: 'daily' | 'weekly' | 'monthly';
    export_format: 'csv' | 'excel' | 'json';
    retention_period: number;
  };
  privacy: {
    analytics_enabled: boolean;
    error_reporting: boolean;
    usage_tracking: boolean;
    data_sharing: boolean;
  };
}

export default function Settings() {
  const [user, setUser] = useState<UserProfile | null>(null);
  const [settings, setSettings] = useState<AppSettings>({
    notifications: {
      email_enabled: true,
      push_enabled: true,
      inventory_alerts: true,
      quality_alerts: true,
      expiration_warnings: true,
      daily_summaries: false,
      low_stock_threshold: 50
    },
    voice: {
      enabled: true,
      auto_start: false,
      continuous_mode: false,
      seafood_terminology: true,
      voice_feedback: true,
      sensitivity: 75
    },
    display: {
      theme: 'light',
      language: 'en-US',
      timezone: 'America/Los_Angeles',
      date_format: 'MM/dd/yyyy',
      time_format: '12h',
      currency: 'USD'
    },
    data: {
      auto_backup: true,
      backup_frequency: 'daily',
      export_format: 'csv',
      retention_period: 365
    },
    privacy: {
      analytics_enabled: true,
      error_reporting: true,
      usage_tracking: false,
      data_sharing: false
    }
  });
  const [activeTab, setActiveTab] = useState('profile');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const loadUserProfile = useCallback(async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        setUser({
          id: user.id,
          email: user.email ?? '',
          full_name: user.user_metadata?.full_name ?? '',
          role: user.user_metadata?.role ?? 'user',
          phone: user.user_metadata?.phone,
          created_at: user.created_at
        });
      }
    } catch (error) {
      console.error('Error loading user profile:', error);
    }
  }, []);

  const loadSettings = useCallback(async () => {
    try {
      // In production, load from user_settings table
      // For now, use localStorage
      const saved = localStorage.getItem('seafood-manager-settings');
      if (saved) {
        setSettings(prev => ({ ...prev, ...JSON.parse(saved) }));
      }
    } catch (error) {
      console.error('Error loading settings:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadUserProfile();
    loadSettings();
  }, [loadUserProfile, loadSettings]);

  const saveSettings = async () => {
    try {
      setSaving(true);
      // In production, save to user_settings table
      localStorage.setItem('seafood-manager-settings', JSON.stringify(settings));
      setMessage({ type: 'success', text: 'Settings saved successfully' });
      setTimeout(() => setMessage(null), 3000);
    } catch (error) {
      console.error('Error saving settings:', error);
      setMessage({ type: 'error', text: 'Failed to save settings' });
    } finally {
      setSaving(false);
    }
  };

  const updateProfile = async (updates: Partial<UserProfile>) => {
    try {
      setSaving(true);
      const { error } = await supabase.auth.updateUser({
        data: { ...updates }
      });
      if (error) throw error;
      
      setUser(prev => prev ? { ...prev, ...updates } : null);
      setMessage({ type: 'success', text: 'Profile updated successfully' });
      setTimeout(() => setMessage(null), 3000);
    } catch (error) {
      console.error('Error updating profile:', error);
      setMessage({ type: 'error', text: 'Failed to update profile' });
    } finally {
      setSaving(false);
    }
  };

  const resetSettings = () => {
    if (confirm('Are you sure you want to reset all settings to defaults? This cannot be undone.')) {
      localStorage.removeItem('seafood-manager-settings');
      window.location.reload();
    }
  };

  const exportData = async (format: string) => {
    try {
      setSaving(true);
      // Implement data export functionality
      setMessage({ type: 'success', text: `Data export in ${format} format started` });
      setTimeout(() => setMessage(null), 3000);
    } catch (error) {
      console.error('Error exporting data:', error);
      setMessage({ type: 'error', text: 'Failed to export data' });
    } finally {
      setSaving(false);
    }
  };

  const tabs = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'voice', label: 'Voice Assistant', icon: Mic },
    { id: 'display', label: 'Display', icon: Eye },
    { id: 'data', label: 'Data & Backup', icon: Database },
    { id: 'privacy', label: 'Privacy', icon: Shield }
  ];

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p className="text-center text-gray-600 mt-4">Loading settings...</p>
      </div>
    );
  }

  return (
    <div className="h-screen flex bg-gray-50">
      {/* Settings Navigation */}
      <div className="w-64 bg-white border-r border-gray-200 flex flex-col">
        <div className="p-6 border-b border-gray-200">
          <h1 className="text-xl font-semibold text-gray-900">Settings</h1>
        </div>
        <nav className="flex-1 p-4">
          <ul className="space-y-2">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <li key={tab.id}>
                  <button
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-colors text-left ${
                      activeTab === tab.id
                        ? 'bg-blue-50 text-blue-700 border border-blue-200'
                        : 'text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    {tab.label}
                  </button>
                </li>
              );
            })}
          </ul>
        </nav>
        <div className="p-4 border-t border-gray-200">
          <button
            onClick={resetSettings}
            className="w-full flex items-center gap-2 px-3 py-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
          >
            <RefreshCw className="w-4 h-4" />
            Reset to Defaults
          </button>
        </div>
      </div>

      {/* Settings Content */}
      <div className="flex-1 flex flex-col">
        <div className="flex-1 overflow-y-auto p-6">
          {message && (
            <div className={`mb-6 p-4 rounded-lg ${
              message.type === 'success' ? 'bg-green-50 text-green-800 border border-green-200' : 'bg-red-50 text-red-800 border border-red-200'
            }`}>
              {message.text}
            </div>
          )}

          {/* Profile Tab */}
          {activeTab === 'profile' && (
            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-6">Profile Information</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                    <input
                      type="text"
                      value={user?.full_name ?? ''}
                      onChange={(e) => setUser(prev => prev ? { ...prev, full_name: e.target.value } : null)}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                    <input
                      type="email"
                      value={user?.email ?? ''}
                      disabled
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 bg-gray-50 text-gray-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                    <input
                      type="tel"
                      value={user?.phone ?? ''}
                      onChange={(e) => setUser(prev => prev ? { ...prev, phone: e.target.value } : null)}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Role</label>
                    <input
                      type="text"
                      value={user?.role ?? ''}
                      disabled
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 bg-gray-50 text-gray-500"
                    />
                  </div>
                </div>
                <div className="mt-6 flex justify-end">
                  <button
                    onClick={() => user && updateProfile({ full_name: user.full_name, phone: user.phone })}
                    disabled={saving}
                    className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                  >
                    <Save className="w-4 h-4" />
                    Save Profile
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Notifications Tab */}
          {activeTab === 'notifications' && (
            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-6">Notification Preferences</h2>
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Mail className="w-5 h-5 text-gray-500" />
                      <div>
                        <h3 className="font-medium text-gray-900">Email Notifications</h3>
                        <p className="text-sm text-gray-500">Receive notifications via email</p>
                      </div>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={settings.notifications.email_enabled}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          notifications: { ...prev.notifications, email_enabled: e.target.checked }
                        }))}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Smartphone className="w-5 h-5 text-gray-500" />
                      <div>
                        <h3 className="font-medium text-gray-900">Push Notifications</h3>
                        <p className="text-sm text-gray-500">Receive push notifications</p>
                      </div>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={settings.notifications.push_enabled}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          notifications: { ...prev.notifications, push_enabled: e.target.checked }
                        }))}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  <div className="border-t pt-4">
                    <h3 className="font-medium text-gray-900 mb-4">Alert Types</h3>
                    <div className="space-y-3">
                      {[
                        { key: 'inventory_alerts', label: 'Low Inventory Alerts', description: 'Get notified when stock is low' },
                        { key: 'quality_alerts', label: 'Quality Alerts', description: 'Notifications for quality issues' },
                        { key: 'expiration_warnings', label: 'Expiration Warnings', description: 'Alerts for expiring products' },
                        { key: 'daily_summaries', label: 'Daily Summaries', description: 'Daily inventory reports' }
                      ].map((alert) => (
                        <div key={alert.key} className="flex items-center justify-between">
                          <div>
                            <h4 className="text-sm font-medium text-gray-900">{alert.label}</h4>
                            <p className="text-xs text-gray-500">{alert.description}</p>
                          </div>
                          <input
                            type="checkbox"
                            checked={settings.notifications[alert.key as keyof typeof settings.notifications] as boolean}
                            onChange={(e) => setSettings(prev => ({
                              ...prev,
                              notifications: { ...prev.notifications, [alert.key]: e.target.checked }
                            }))}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="border-t pt-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Low Stock Threshold</label>
                    <div className="flex items-center gap-4">
                      <input
                        type="range"
                        min="0"
                        max="200"
                        value={settings.notifications.low_stock_threshold}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          notifications: { ...prev.notifications, low_stock_threshold: parseInt(e.target.value) }
                        }))}
                        className="flex-1"
                      />
                      <span className="text-sm text-gray-600 w-16 text-right">
                        {settings.notifications.low_stock_threshold} units
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Voice Assistant Tab */}
          {activeTab === 'voice' && (
            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-6">Voice Assistant Settings</h2>
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Mic className="w-5 h-5 text-gray-500" />
                      <div>
                        <h3 className="font-medium text-gray-900">Voice Assistant</h3>
                        <p className="text-sm text-gray-500">Enable voice commands and transcription</p>
                      </div>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={settings.voice.enabled}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          voice: { ...prev.voice, enabled: e.target.checked }
                        }))}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  {settings.voice.enabled && (
                    <>
                      <div className="space-y-3 pl-8">
                        {[
                          { key: 'auto_start', label: 'Auto-start on app launch', description: 'Automatically enable voice assistant when app opens' },
                          { key: 'continuous_mode', label: 'Continuous listening', description: 'Keep listening after each command' },
                          { key: 'seafood_terminology', label: 'Seafood terminology optimization', description: 'Enhanced recognition for seafood terms' },
                          { key: 'voice_feedback', label: 'Voice feedback', description: 'Spoken confirmations and responses' }
                        ].map((option) => (
                          <div key={option.key} className="flex items-center justify-between">
                            <div>
                              <h4 className="text-sm font-medium text-gray-900">{option.label}</h4>
                              <p className="text-xs text-gray-500">{option.description}</p>
                            </div>
                            <input
                              type="checkbox"
                              checked={settings.voice[option.key as keyof typeof settings.voice] as boolean}
                              onChange={(e) => setSettings(prev => ({
                                ...prev,
                                voice: { ...prev.voice, [option.key]: e.target.checked }
                              }))}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                          </div>
                        ))}
                      </div>

                      <div className="border-t pt-4">
                        <label className="block text-sm font-medium text-gray-700 mb-2">Voice Sensitivity</label>
                        <div className="flex items-center gap-4">
                          <span className="text-sm text-gray-600">Low</span>
                          <input
                            type="range"
                            min="0"
                            max="100"
                            value={settings.voice.sensitivity}
                            onChange={(e) => setSettings(prev => ({
                              ...prev,
                              voice: { ...prev.voice, sensitivity: parseInt(e.target.value) }
                            }))}
                            className="flex-1"
                          />
                          <span className="text-sm text-gray-600">High</span>
                          <span className="text-sm text-gray-600 w-12 text-right">
                            {settings.voice.sensitivity}%
                          </span>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Display Tab */}
          {activeTab === 'display' && (
            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-6">Display & Language</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Theme</label>
                    <select
                      value={settings.display.theme}
                      onChange={(e) => setSettings(prev => ({
                        ...prev,
                        display: { ...prev.display, theme: e.target.value as 'light' | 'dark' | 'auto' }
                      }))}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="light">Light</option>
                      <option value="dark">Dark</option>
                      <option value="auto">Auto (System)</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Language</label>
                    <select
                      value={settings.display.language}
                      onChange={(e) => setSettings(prev => ({
                        ...prev,
                        display: { ...prev.display, language: e.target.value }
                      }))}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="en-US">English (US)</option>
                      <option value="en-GB">English (UK)</option>
                      <option value="es-ES">Spanish</option>
                      <option value="fr-FR">French</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Timezone</label>
                    <select
                      value={settings.display.timezone}
                      onChange={(e) => setSettings(prev => ({
                        ...prev,
                        display: { ...prev.display, timezone: e.target.value }
                      }))}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="America/Los_Angeles">Pacific Time</option>
                      <option value="America/Denver">Mountain Time</option>
                      <option value="America/Chicago">Central Time</option>
                      <option value="America/New_York">Eastern Time</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Time Format</label>
                    <select
                      value={settings.display.time_format}
                      onChange={(e) => setSettings(prev => ({
                        ...prev,
                        display: { ...prev.display, time_format: e.target.value as '12h' | '24h' }
                      }))}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="12h">12 Hour (AM/PM)</option>
                      <option value="24h">24 Hour</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Date Format</label>
                    <select
                      value={settings.display.date_format}
                      onChange={(e) => setSettings(prev => ({
                        ...prev,
                        display: { ...prev.display, date_format: e.target.value }
                      }))}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="MM/dd/yyyy">MM/DD/YYYY</option>
                      <option value="dd/MM/yyyy">DD/MM/YYYY</option>
                      <option value="yyyy-MM-dd">YYYY-MM-DD</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Currency</label>
                    <select
                      value={settings.display.currency}
                      onChange={(e) => setSettings(prev => ({
                        ...prev,
                        display: { ...prev.display, currency: e.target.value }
                      }))}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="USD">USD ($)</option>
                      <option value="EUR">EUR (€)</option>
                      <option value="GBP">GBP (£)</option>
                      <option value="CAD">CAD (C$)</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Data & Backup Tab */}
          {activeTab === 'data' && (
            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-6">Data Management</h2>
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium text-gray-900">Automatic Backups</h3>
                      <p className="text-sm text-gray-500">Automatically backup your data</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={settings.data.auto_backup}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          data: { ...prev.data, auto_backup: e.target.checked }
                        }))}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  {settings.data.auto_backup && (
                    <div className="pl-4 space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Backup Frequency</label>
                        <select
                          value={settings.data.backup_frequency}
                          onChange={(e) => setSettings(prev => ({
                            ...prev,
                            data: { ...prev.data, backup_frequency: e.target.value as 'daily' | 'weekly' | 'monthly' }
                          }))}
                          className="w-full max-w-xs border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="daily">Daily</option>
                          <option value="weekly">Weekly</option>
                          <option value="monthly">Monthly</option>
                        </select>
                      </div>
                    </div>
                  )}

                  <div className="border-t pt-4">
                    <h3 className="font-medium text-gray-900 mb-4">Data Export</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {['csv', 'excel', 'json'].map((format) => (
                        <button
                          key={format}
                          onClick={() => exportData(format)}
                          disabled={saving}
                          className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
                        >
                          <Download className="w-4 h-4" />
                          Export as {format.toUpperCase()}
                        </button>
                      ))}
                    </div>
                  </div>

                  <div className="border-t pt-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Data Retention Period</label>
                    <div className="flex items-center gap-4">
                      <input
                        type="range"
                        min="30"
                        max="2555"
                        value={settings.data.retention_period}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          data: { ...prev.data, retention_period: parseInt(e.target.value) }
                        }))}
                        className="flex-1"
                      />
                      <span className="text-sm text-gray-600 w-24 text-right">
                        {Math.round(settings.data.retention_period / 365)} year{Math.round(settings.data.retention_period / 365) !== 1 ? 's' : ''}
                      </span>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      Data older than this will be automatically archived
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Privacy Tab */}
          {activeTab === 'privacy' && (
            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-6">Privacy & Security</h2>
                <div className="space-y-6">
                  {[
                    { key: 'analytics_enabled', label: 'Analytics', description: 'Help improve the app by sharing anonymous usage data' },
                    { key: 'error_reporting', label: 'Error Reporting', description: 'Automatically report errors to help fix issues' },
                    { key: 'usage_tracking', label: 'Usage Tracking', description: 'Track feature usage for optimization' },
                    { key: 'data_sharing', label: 'Data Sharing', description: 'Share anonymized data with partners' }
                  ].map((option) => (
                    <div key={option.key} className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium text-gray-900">{option.label}</h3>
                        <p className="text-sm text-gray-500">{option.description}</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={settings.privacy[option.key as keyof typeof settings.privacy]}
                          onChange={(e) => setSettings(prev => ({
                            ...prev,
                            privacy: { ...prev.privacy, [option.key]: e.target.checked }
                          }))}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Save Button */}
        <div className="border-t border-gray-200 px-6 py-4 bg-white">
          <div className="flex justify-end">
            <button
              onClick={saveSettings}
              disabled={saving}
              className="flex items-center gap-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {saving ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <Save className="w-4 h-4" />
              )}
              {saving ? 'Saving...' : 'Save Settings'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}