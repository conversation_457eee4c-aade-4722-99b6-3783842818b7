// Real-Time Performance Monitoring Dashboard
// Enterprise-grade monitoring for Seafood Manager production environment

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Card } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Alert, AlertDescription } from '../ui/alert';
import { PerformanceMonitor, SeafoodBusinessMetrics, ErrorTracker } from '../../lib/monitoring/performanceMonitor';
import { healthMonitor, HealthCheck, UptimeMetrics } from '../../../monitoring/uptime-monitoring';
import { AlertTriangle, CheckCircle, XCircle, Activity, Clock, TrendingUp, TrendingDown, Zap, Database, Mic, Upload, Shield, Globe, Smartphone } from 'lucide-react';

interface MonitoringMetrics {
  performance: {
    coreWebVitals: {
      lcp: number;
      fid: number;
      cls: number;
    };
    voiceProcessing: {
      transcriptionTime: number;
      aiProcessingTime: number;
      accuracy: number;
      failureRate: number;
    };
    importOperations: {
      avgProcessingTime: number;
      successRate: number;
      throughput: number;
    };
    databaseQueries: {
      avgResponseTime: number;
      slowQueries: number;
      connectionPoolUsage: number;
    };
  };
  business: {
    haccpCompliance: {
      complianceRate: number;
      violations: number;
      criticalAlerts: number;
    };
    userEngagement: {
      activeUsers: number;
      sessionDuration: number;
      featureUsage: Record<string, number>;
    };
    systemAvailability: {
      uptime: number;
      slaTarget: number;
      mttr: number;
      mtbf: number;
    };
  };
  alerts: {
    critical: number;
    warning: number;
    info: number;
    acknowledged: number;
  };
  security: {
    threatLevel: 'low' | 'medium' | 'high' | 'critical';
    blockedRequests: number;
    suspiciousActivity: number;
  };
}

interface AlertData {
  id: string;
  type: 'critical' | 'warning' | 'info';
  category: 'performance' | 'business' | 'security' | 'compliance';
  title: string;
  description: string;
  timestamp: Date;
  acknowledged: boolean;
  actionRequired: string[];
}

export const RealTimeMonitoringDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<MonitoringMetrics | null>(null);
  const [healthChecks, setHealthChecks] = useState<HealthCheck[]>([]);
  const [uptimeMetrics, setUptimeMetrics] = useState<UptimeMetrics | null>(null);
  const [alerts, setAlerts] = useState<AlertData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [selectedTimeRange, setSelectedTimeRange] = useState<'1h' | '6h' | '24h' | '7d'>('1h');
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [mobileView, setMobileView] = useState(false);

  const monitor = PerformanceMonitor.getInstance();
  const businessMetrics = new SeafoodBusinessMetrics();

  // Real-time data fetching
  const fetchMonitoringData = useCallback(async () => {
    try {
      setIsLoading(true);

      // Fetch health checks
      const healthReport = await healthMonitor.generateHealthReport();
      setHealthChecks(healthReport.checks);

      // Fetch uptime metrics
      const uptime = healthMonitor.getUptimeMetrics();
      setUptimeMetrics(uptime);

      // Simulate real-time metrics (in production, these would come from actual monitoring APIs)
      const mockMetrics: MonitoringMetrics = {
        performance: {
          coreWebVitals: {
            lcp: 1200 + Math.random() * 800,
            fid: 20 + Math.random() * 30,
            cls: 0.05 + Math.random() * 0.1
          },
          voiceProcessing: {
            transcriptionTime: 800 + Math.random() * 400,
            aiProcessingTime: 1200 + Math.random() * 600,
            accuracy: 94 + Math.random() * 4,
            failureRate: Math.random() * 3
          },
          importOperations: {
            avgProcessingTime: 15000 + Math.random() * 10000,
            successRate: 96 + Math.random() * 3,
            throughput: 1000 + Math.random() * 500
          },
          databaseQueries: {
            avgResponseTime: 150 + Math.random() * 100,
            slowQueries: Math.floor(Math.random() * 5),
            connectionPoolUsage: 60 + Math.random() * 30
          }
        },
        business: {
          haccpCompliance: {
            complianceRate: 98 + Math.random() * 2,
            violations: Math.floor(Math.random() * 3),
            criticalAlerts: Math.floor(Math.random() * 2)
          },
          userEngagement: {
            activeUsers: 45 + Math.floor(Math.random() * 20),
            sessionDuration: 18 + Math.random() * 12,
            featureUsage: {
              voice: 75 + Math.random() * 20,
              import: 45 + Math.random() * 25,
              haccp: 60 + Math.random() * 30,
              analytics: 35 + Math.random() * 15
            }
          },
          systemAvailability: {
            uptime: uptime.availability,
            slaTarget: 99.9,
            mttr: uptime.mttr / (1000 * 60), // Convert to minutes
            mtbf: uptime.mtbf / (1000 * 60 * 60) // Convert to hours
          }
        },
        alerts: {
          critical: Math.floor(Math.random() * 3),
          warning: Math.floor(Math.random() * 8),
          info: Math.floor(Math.random() * 15),
          acknowledged: Math.floor(Math.random() * 20)
        },
        security: {
          threatLevel: Math.random() > 0.9 ? 'medium' : 'low',
          blockedRequests: Math.floor(Math.random() * 50),
          suspiciousActivity: Math.floor(Math.random() * 5)
        }
      };

      setMetrics(mockMetrics);

      // Generate sample alerts
      const sampleAlerts: AlertData[] = [
        {
          id: '1',
          type: 'warning',
          category: 'performance',
          title: 'Database Query Performance Degraded',
          description: 'Average query response time exceeded 300ms threshold',
          timestamp: new Date(Date.now() - 5 * 60 * 1000),
          acknowledged: false,
          actionRequired: ['Check database indexes', 'Review query optimization', 'Monitor connection pool']
        },
        {
          id: '2',
          type: 'info',
          category: 'business',
          title: 'Voice Processing Accuracy High',
          description: 'Voice recognition accuracy improved to 97.2%',
          timestamp: new Date(Date.now() - 15 * 60 * 1000),
          acknowledged: true,
          actionRequired: []
        }
      ];

      if (mockMetrics.business.haccpCompliance.violations > 1) {
        sampleAlerts.unshift({
          id: '3',
          type: 'critical',
          category: 'compliance',
          title: 'HACCP Compliance Violations Detected',
          description: `${mockMetrics.business.haccpCompliance.violations} compliance violations in the last hour`,
          timestamp: new Date(Date.now() - 2 * 60 * 1000),
          acknowledged: false,
          actionRequired: ['Review temperature logs', 'Check equipment calibration', 'Contact quality assurance']
        });
      }

      setAlerts(sampleAlerts);
      setLastUpdate(new Date());
    } catch (error) {
      console.error('Failed to fetch monitoring data:', error);
      ErrorTracker.trackBusinessError('monitoring_failure', error as Error, {
        component: 'RealTimeMonitoringDashboard'
      });
    } finally {
      setIsLoading(false);
    }
  }, [selectedTimeRange]);

  // Auto-refresh functionality
  useEffect(() => {
    fetchMonitoringData();

    if (autoRefresh) {
      const interval = setInterval(fetchMonitoringData, 30000); // Refresh every 30 seconds
      return () => clearInterval(interval);
    }
  }, [fetchMonitoringData, autoRefresh]);

  // Responsive design check
  useEffect(() => {
    const checkMobileView = () => setMobileView(window.innerWidth < 768);
    checkMobileView();
    window.addEventListener('resize', checkMobileView);
    return () => window.removeEventListener('resize', checkMobileView);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600';
      case 'degraded': return 'text-yellow-600';
      case 'unhealthy': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="w-4 h-4" />;
      case 'degraded': return <AlertTriangle className="w-4 h-4" />;
      case 'unhealthy': return <XCircle className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  const acknowledgeAlert = (alertId: string) => {
    setAlerts(prev => prev.map(alert => 
      alert.id === alertId ? { ...alert, acknowledged: true } : alert
    ));
  };

  const criticalAlerts = alerts.filter(alert => alert.type === 'critical' && !alert.acknowledged);
  const overallHealth = healthChecks.length > 0 
    ? healthChecks.every(check => check.status === 'healthy') 
      ? 'healthy' 
      : healthChecks.some(check => check.status === 'unhealthy') 
        ? 'unhealthy' 
        : 'degraded'
    : 'unknown';

  if (isLoading && !metrics) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        <div className="ml-4 text-lg">Loading monitoring dashboard...</div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 p-6 ${mobileView ? 'px-4' : ''}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Real-Time Monitoring</h1>
          <p className="text-gray-600">Live performance and business metrics for Seafood Manager</p>
        </div>
        
        <div className="flex flex-wrap gap-2">
          <Button
            variant={autoRefresh ? "default" : "outline"}
            onClick={() => setAutoRefresh(!autoRefresh)}
            size="sm"
          >
            <Activity className="w-4 h-4 mr-2" />
            Auto-refresh {autoRefresh ? 'ON' : 'OFF'}
          </Button>
          
          <Button onClick={fetchMonitoringData} size="sm" variant="outline">
            Refresh Now
          </Button>
          
          {mobileView && (
            <Button size="sm" variant="outline">
              <Smartphone className="w-4 h-4 mr-2" />
              Mobile View
            </Button>
          )}
        </div>
      </div>

      {/* Critical Alerts Banner */}
      {criticalAlerts.length > 0 && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            <strong>{criticalAlerts.length} Critical Alert{criticalAlerts.length !== 1 ? 's' : ''}</strong>
            {criticalAlerts.map(alert => (
              <div key={alert.id} className="mt-2 p-2 bg-red-100 rounded">
                <div className="font-medium">{alert.title}</div>
                <div className="text-sm">{alert.description}</div>
                <Button
                  size="sm"
                  className="mt-2"
                  onClick={() => acknowledgeAlert(alert.id)}
                >
                  Acknowledge
                </Button>
              </div>
            ))}
          </AlertDescription>
        </Alert>
      )}

      {/* System Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Overall Health</p>
              <div className={`flex items-center mt-1 ${getStatusColor(overallHealth)}`}>
                {getStatusIcon(overallHealth)}
                <span className="ml-2 text-lg font-semibold capitalize">{overallHealth}</span>
              </div>
            </div>
            <Activity className="w-8 h-8 text-blue-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">System Uptime</p>
              <div className="flex items-center mt-1">
                <span className="text-lg font-semibold text-green-600">
                  {uptimeMetrics?.availability.toFixed(2) || 0}%
                </span>
                <Badge variant="outline" className="ml-2">SLA: 99.9%</Badge>
              </div>
            </div>
            <Clock className="w-8 h-8 text-green-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Alerts</p>
              <div className="flex items-center mt-1 gap-2">
                <span className="text-lg font-semibold text-red-600">{alerts.filter(a => a.type === 'critical').length}</span>
                <span className="text-sm text-yellow-600">/ {alerts.filter(a => a.type === 'warning').length} warnings</span>
              </div>
            </div>
            <AlertTriangle className="w-8 h-8 text-red-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Security Status</p>
              <div className="flex items-center mt-1">
                <Badge 
                  variant={metrics?.security.threatLevel === 'low' ? 'default' : 'destructive'}
                  className="capitalize"
                >
                  {metrics?.security.threatLevel || 'unknown'} Risk
                </Badge>
              </div>
            </div>
            <Shield className="w-8 h-8 text-blue-600" />
          </div>
        </Card>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Core Web Vitals */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Core Web Vitals</h3>
            <Globe className="w-5 h-5 text-blue-600" />
          </div>
          
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Largest Contentful Paint (LCP)</span>
              <div className="flex items-center">
                <span className={`font-semibold ${(metrics?.performance.coreWebVitals.lcp || 0) < 2500 ? 'text-green-600' : 'text-red-600'}`}>
                  {metrics?.performance.coreWebVitals.lcp.toFixed(0) || 0}ms
                </span>
                <div className={`ml-2 ${(metrics?.performance.coreWebVitals.lcp || 0) < 2500 ? 'text-green-600' : 'text-red-600'}`}>
                  {(metrics?.performance.coreWebVitals.lcp || 0) < 2500 ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />}
                </div>
              </div>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">First Input Delay (FID)</span>
              <div className="flex items-center">
                <span className={`font-semibold ${(metrics?.performance.coreWebVitals.fid || 0) < 100 ? 'text-green-600' : 'text-red-600'}`}>
                  {metrics?.performance.coreWebVitals.fid.toFixed(0) || 0}ms
                </span>
                <div className={`ml-2 ${(metrics?.performance.coreWebVitals.fid || 0) < 100 ? 'text-green-600' : 'text-red-600'}`}>
                  {(metrics?.performance.coreWebVitals.fid || 0) < 100 ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />}
                </div>
              </div>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Cumulative Layout Shift (CLS)</span>
              <div className="flex items-center">
                <span className={`font-semibold ${(metrics?.performance.coreWebVitals.cls || 0) < 0.1 ? 'text-green-600' : 'text-red-600'}`}>
                  {metrics?.performance.coreWebVitals.cls.toFixed(3) || 0}
                </span>
                <div className={`ml-2 ${(metrics?.performance.coreWebVitals.cls || 0) < 0.1 ? 'text-green-600' : 'text-red-600'}`}>
                  {(metrics?.performance.coreWebVitals.cls || 0) < 0.1 ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />}
                </div>
              </div>
            </div>
          </div>
        </Card>

        {/* Voice Processing Performance */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Voice Processing</h3>
            <Mic className="w-5 h-5 text-blue-600" />
          </div>
          
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Transcription Time</span>
              <span className={`font-semibold ${(metrics?.performance.voiceProcessing.transcriptionTime || 0) < 1000 ? 'text-green-600' : 'text-yellow-600'}`}>
                {metrics?.performance.voiceProcessing.transcriptionTime.toFixed(0) || 0}ms
              </span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">AI Processing Time</span>
              <span className={`font-semibold ${(metrics?.performance.voiceProcessing.aiProcessingTime || 0) < 1500 ? 'text-green-600' : 'text-yellow-600'}`}>
                {metrics?.performance.voiceProcessing.aiProcessingTime.toFixed(0) || 0}ms
              </span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Recognition Accuracy</span>
              <span className={`font-semibold ${(metrics?.performance.voiceProcessing.accuracy || 0) > 95 ? 'text-green-600' : 'text-yellow-600'}`}>
                {metrics?.performance.voiceProcessing.accuracy.toFixed(1) || 0}%
              </span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Failure Rate</span>
              <span className={`font-semibold ${(metrics?.performance.voiceProcessing.failureRate || 0) < 2 ? 'text-green-600' : 'text-red-600'}`}>
                {metrics?.performance.voiceProcessing.failureRate.toFixed(1) || 0}%
              </span>
            </div>
          </div>
        </Card>

        {/* Database Performance */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Database Performance</h3>
            <Database className="w-5 h-5 text-blue-600" />
          </div>
          
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Avg Response Time</span>
              <span className={`font-semibold ${(metrics?.performance.databaseQueries.avgResponseTime || 0) < 300 ? 'text-green-600' : 'text-yellow-600'}`}>
                {metrics?.performance.databaseQueries.avgResponseTime.toFixed(0) || 0}ms
              </span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Slow Queries</span>
              <span className={`font-semibold ${(metrics?.performance.databaseQueries.slowQueries || 0) < 3 ? 'text-green-600' : 'text-red-600'}`}>
                {metrics?.performance.databaseQueries.slowQueries || 0}
              </span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Connection Pool Usage</span>
              <span className={`font-semibold ${(metrics?.performance.databaseQueries.connectionPoolUsage || 0) < 80 ? 'text-green-600' : 'text-yellow-600'}`}>
                {metrics?.performance.databaseQueries.connectionPoolUsage.toFixed(0) || 0}%
              </span>
            </div>
          </div>
        </Card>

        {/* Import Operations */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Import Operations</h3>
            <Upload className="w-5 h-5 text-blue-600" />
          </div>
          
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Avg Processing Time</span>
              <span className={`font-semibold ${(metrics?.performance.importOperations.avgProcessingTime || 0) < 20000 ? 'text-green-600' : 'text-yellow-600'}`}>
                {((metrics?.performance.importOperations.avgProcessingTime || 0) / 1000).toFixed(1)}s
              </span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Success Rate</span>
              <span className={`font-semibold ${(metrics?.performance.importOperations.successRate || 0) > 95 ? 'text-green-600' : 'text-yellow-600'}`}>
                {metrics?.performance.importOperations.successRate.toFixed(1) || 0}%
              </span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Throughput</span>
              <span className="font-semibold text-blue-600">
                {metrics?.performance.importOperations.throughput.toFixed(0) || 0} rows/min
              </span>
            </div>
          </div>
        </Card>
      </div>

      {/* Business Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* HACCP Compliance */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">HACCP Compliance</h3>
            <Shield className="w-5 h-5 text-blue-600" />
          </div>
          
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Compliance Rate</span>
              <span className={`font-semibold ${(metrics?.business.haccpCompliance.complianceRate || 0) > 98 ? 'text-green-600' : 'text-red-600'}`}>
                {metrics?.business.haccpCompliance.complianceRate.toFixed(1) || 0}%
              </span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Violations (24h)</span>
              <span className={`font-semibold ${(metrics?.business.haccpCompliance.violations || 0) === 0 ? 'text-green-600' : 'text-red-600'}`}>
                {metrics?.business.haccpCompliance.violations || 0}
              </span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Critical Alerts</span>
              <span className={`font-semibold ${(metrics?.business.haccpCompliance.criticalAlerts || 0) === 0 ? 'text-green-600' : 'text-red-600'}`}>
                {metrics?.business.haccpCompliance.criticalAlerts || 0}
              </span>
            </div>
          </div>
        </Card>

        {/* User Engagement */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">User Engagement</h3>
            <Activity className="w-5 h-5 text-blue-600" />
          </div>
          
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Active Users</span>
              <span className="font-semibold text-blue-600">
                {metrics?.business.userEngagement.activeUsers || 0}
              </span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Avg Session Duration</span>
              <span className="font-semibold text-blue-600">
                {metrics?.business.userEngagement.sessionDuration.toFixed(0) || 0}min
              </span>
            </div>

            <div className="space-y-2">
              <span className="text-sm font-medium">Feature Usage</span>
              {Object.entries(metrics?.business.userEngagement.featureUsage || {}).map(([feature, usage]) => (
                <div key={feature} className="flex justify-between items-center text-sm">
                  <span className="capitalize">{feature}</span>
                  <span className="font-medium">{usage.toFixed(0)}%</span>
                </div>
              ))}
            </div>
          </div>
        </Card>
      </div>

      {/* Health Checks */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">System Health Checks</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {healthChecks.map((check) => (
            <div key={check.name} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center">
                <div className={getStatusColor(check.status)}>
                  {getStatusIcon(check.status)}
                </div>
                <span className="ml-2 text-sm font-medium capitalize">
                  {check.name.replace(/_/g, ' ')}
                </span>
              </div>
              <div className="text-right">
                <div className="text-xs text-gray-500">{check.responseTime.toFixed(0)}ms</div>
                {check.message && (
                  <div className="text-xs text-gray-600 max-w-20 truncate">{check.message}</div>
                )}
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Recent Alerts */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Recent Alerts</h3>
          <Badge variant="outline">
            {alerts.filter(a => !a.acknowledged).length} unacknowledged
          </Badge>
        </div>
        
        <div className="space-y-3">
          {alerts.slice(0, 5).map((alert) => (
            <div key={alert.id} className={`p-3 rounded-lg border ${
              alert.type === 'critical' ? 'border-red-200 bg-red-50' :
              alert.type === 'warning' ? 'border-yellow-200 bg-yellow-50' :
              'border-blue-200 bg-blue-50'
            }`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Badge variant={
                    alert.type === 'critical' ? 'destructive' :
                    alert.type === 'warning' ? 'default' : 'outline'
                  }>
                    {alert.type}
                  </Badge>
                  <span className="ml-2 font-medium">{alert.title}</span>
                </div>
                <div className="text-xs text-gray-500">
                  {alert.timestamp.toLocaleTimeString()}
                </div>
              </div>
              
              <p className="text-sm text-gray-600 mt-1">{alert.description}</p>
              
              {alert.actionRequired.length > 0 && (
                <div className="mt-2">
                  <p className="text-xs font-medium text-gray-700">Action Required:</p>
                  <ul className="text-xs text-gray-600 list-disc list-inside">
                    {alert.actionRequired.map((action, index) => (
                      <li key={index}>{action}</li>
                    ))}
                  </ul>
                </div>
              )}
              
              {!alert.acknowledged && (
                <Button
                  size="sm"
                  variant="outline"
                  className="mt-2"
                  onClick={() => acknowledgeAlert(alert.id)}
                >
                  Acknowledge
                </Button>
              )}
            </div>
          ))}
        </div>
      </Card>

      {/* Footer */}
      <div className="text-center text-sm text-gray-500">
        Last updated: {lastUpdate.toLocaleString()} | 
        {autoRefresh ? ' Auto-refreshing every 30 seconds' : ' Auto-refresh disabled'}
      </div>
    </div>
  );
};

export default RealTimeMonitoringDashboard;