import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Card } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Alert } from '../ui/alert';
import { 
  BarChart3, 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Cpu, 
  Database, 
  Mic, 
  Upload, 
  Shield,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  Settings,
  Bell,
  Download
} from 'lucide-react';
import { LineChart, Line, AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Bar<PERSON>hart, Bar } from 'recharts';

// Enhanced Performance Monitor with real-time capabilities
export class RealTimePerformanceMonitor {
  private static instance: RealTimePerformanceMonitor;
  private metrics: Map<string, any[]> = new Map();
  private subscribers: Set<(metrics: any) => void> = new Set();
  private intervalId: NodeJS.Timeout | null = null;
  private alertThresholds: Map<string, any> = new Map();

  static getInstance(): RealTimePerformanceMonitor {
    if (!this.instance) {
      this.instance = new RealTimePerformanceMonitor();
    }
    return this.instance;
  }

  subscribe(callback: (metrics: any) => void): () => void {
    this.subscribers.add(callback);
    return () => this.subscribers.delete(callback);
  }

  startRealTimeMonitoring(): void {
    if (this.intervalId) return;

    this.intervalId = setInterval(() => {
      this.collectMetrics();
      this.notifySubscribers();
    }, 1000); // Collect metrics every second

    // Initial collection
    this.collectMetrics();
    this.notifySubscribers();
  }

  stopRealTimeMonitoring(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  private collectMetrics(): void {
    const timestamp = Date.now();
    
    // Core Web Vitals
    this.collectWebVitals(timestamp);
    
    // System Performance
    this.collectSystemMetrics(timestamp);
    
    // Business Metrics
    this.collectBusinessMetrics(timestamp);
  }

  private collectWebVitals(timestamp: number): void {
    if (typeof window === 'undefined') return;

    // Performance API metrics
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    if (navigation) {
      this.addMetric('page_load_time', {
        timestamp,
        value: navigation.loadEventEnd - navigation.navigationStart,
        category: 'core_web_vitals'
      });

      this.addMetric('time_to_first_byte', {
        timestamp,
        value: navigation.responseStart - navigation.requestStart,
        category: 'core_web_vitals'
      });

      this.addMetric('dom_content_loaded', {
        timestamp,
        value: navigation.domContentLoadedEventEnd - navigation.navigationStart,
        category: 'core_web_vitals'
      });
    }

    // Memory usage
    if ((performance as any).memory) {
      const memory = (performance as any).memory;
      this.addMetric('memory_usage', {
        timestamp,
        value: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100,
        details: {
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          limit: memory.jsHeapSizeLimit
        },
        category: 'system_performance'
      });
    }
  }

  private collectSystemMetrics(timestamp: number): void {
    // Connection quality
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      this.addMetric('connection_quality', {
        timestamp,
        value: connection.effectiveType === '4g' ? 100 : 
               connection.effectiveType === '3g' ? 75 :
               connection.effectiveType === '2g' ? 50 : 25,
        details: {
          effectiveType: connection.effectiveType,
          downlink: connection.downlink,
          rtt: connection.rtt
        },
        category: 'system_performance'
      });
    }

    // Long tasks
    if (typeof PerformanceObserver !== 'undefined') {
      try {
        new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.addMetric('long_task', {
              timestamp: Date.now(),
              value: entry.duration,
              category: 'performance_issues'
            });
          }
        }).observe({ entryTypes: ['longtask'] });
      } catch (e) {
        // PerformanceObserver not supported
      }
    }
  }

  private collectBusinessMetrics(timestamp: number): void {
    // Voice processing queue size (simulated)
    const voiceQueueSize = this.getVoiceQueueSize();
    this.addMetric('voice_queue_size', {
      timestamp,
      value: voiceQueueSize,
      category: 'business_metrics'
    });

    // Active user sessions
    const activeSessions = this.getActiveSessionCount();
    this.addMetric('active_sessions', {
      timestamp,
      value: activeSessions,
      category: 'business_metrics'
    });

    // Database connection pool
    const dbPoolUsage = this.getDatabasePoolUsage();
    this.addMetric('db_pool_usage', {
      timestamp,
      value: dbPoolUsage,
      category: 'database_performance'
    });
  }

  private addMetric(name: string, data: any): void {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    
    const metricArray = this.metrics.get(name)!;
    metricArray.push(data);
    
    // Keep only last 300 data points (5 minutes at 1-second intervals)
    if (metricArray.length > 300) {
      metricArray.shift();
    }

    // Check alert thresholds
    this.checkAlertThresholds(name, data);
  }

  private checkAlertThresholds(metricName: string, data: any): void {
    const threshold = this.alertThresholds.get(metricName);
    if (!threshold) return;

    const isAlert = threshold.condition === 'greater_than' 
      ? data.value > threshold.value
      : data.value < threshold.value;

    if (isAlert) {
      this.triggerAlert({
        metric: metricName,
        value: data.value,
        threshold: threshold.value,
        severity: threshold.severity,
        timestamp: data.timestamp
      });
    }
  }

  private triggerAlert(alert: any): void {
    // Emit alert to subscribers
    this.subscribers.forEach(callback => {
      callback({ type: 'alert', data: alert });
    });

    // Log to console for debugging
    console.warn('Performance Alert:', alert);
  }

  private notifySubscribers(): void {
    const metrics = this.getAllMetrics();
    this.subscribers.forEach(callback => {
      callback({ type: 'metrics', data: metrics });
    });
  }

  getAllMetrics(): any {
    const result: any = {};
    for (const [name, data] of this.metrics.entries()) {
      result[name] = data;
    }
    return result;
  }

  setAlertThreshold(metric: string, condition: 'greater_than' | 'less_than', value: number, severity: 'low' | 'medium' | 'high' | 'critical'): void {
    this.alertThresholds.set(metric, { condition, value, severity });
  }

  // Simulated business metric getters (replace with real implementations)
  private getVoiceQueueSize(): number {
    return Math.floor(Math.random() * 10);
  }

  private getActiveSessionCount(): number {
    return Math.floor(Math.random() * 100) + 20;
  }

  private getDatabasePoolUsage(): number {
    return Math.floor(Math.random() * 100);
  }
}

interface PerformanceAlert {
  id: string;
  metric: string;
  value: number;
  threshold: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: number;
  acknowledged: boolean;
}

export const RealTimePerformanceDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<any>({});
  const [alerts, setAlerts] = useState<PerformanceAlert[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [selectedTimeRange, setSelectedTimeRange] = useState('5m');
  const [autoRefresh, setAutoRefresh] = useState(true);

  const monitor = useMemo(() => RealTimePerformanceMonitor.getInstance(), []);

  useEffect(() => {
    // Set up alert thresholds
    monitor.setAlertThreshold('page_load_time', 'greater_than', 3000, 'medium');
    monitor.setAlertThreshold('memory_usage', 'greater_than', 80, 'high');
    monitor.setAlertThreshold('voice_queue_size', 'greater_than', 5, 'medium');
    monitor.setAlertThreshold('db_pool_usage', 'greater_than', 90, 'critical');

    const unsubscribe = monitor.subscribe((data) => {
      if (data.type === 'metrics') {
        setMetrics(data.data);
      } else if (data.type === 'alert') {
        const newAlert: PerformanceAlert = {
          id: `alert_${Date.now()}`,
          ...data.data,
          acknowledged: false
        };
        setAlerts(prev => [newAlert, ...prev.slice(0, 49)]); // Keep last 50 alerts
      }
    });

    if (autoRefresh) {
      monitor.startRealTimeMonitoring();
      setIsMonitoring(true);
    }

    return () => {
      unsubscribe();
      monitor.stopRealTimeMonitoring();
    };
  }, [monitor, autoRefresh]);

  const toggleMonitoring = useCallback(() => {
    if (isMonitoring) {
      monitor.stopRealTimeMonitoring();
      setIsMonitoring(false);
    } else {
      monitor.startRealTimeMonitoring();
      setIsMonitoring(true);
    }
  }, [monitor, isMonitoring]);

  const acknowledgeAlert = useCallback((alertId: string) => {
    setAlerts(prev => prev.map(alert => 
      alert.id === alertId ? { ...alert, acknowledged: true } : alert
    ));
  }, []);

  const formatMetricData = useCallback((metricName: string) => {
    const metricData = metrics[metricName] || [];
    const timeRangeMs = selectedTimeRange === '1m' ? 60000 :
                       selectedTimeRange === '5m' ? 300000 :
                       selectedTimeRange === '15m' ? 900000 : 1800000;
    
    const cutoffTime = Date.now() - timeRangeMs;
    return metricData
      .filter((point: any) => point.timestamp > cutoffTime)
      .map((point: any) => ({
        timestamp: new Date(point.timestamp).toLocaleTimeString(),
        value: Math.round(point.value * 100) / 100,
        ...point
      }));
  }, [metrics, selectedTimeRange]);

  const getMetricStatus = useCallback((metricName: string, currentValue: number) => {
    if (metricName === 'page_load_time') {
      return currentValue > 3000 ? 'critical' : currentValue > 2000 ? 'warning' : 'good';
    }
    if (metricName === 'memory_usage') {
      return currentValue > 80 ? 'critical' : currentValue > 60 ? 'warning' : 'good';
    }
    if (metricName === 'voice_queue_size') {
      return currentValue > 5 ? 'warning' : 'good';
    }
    return 'good';
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'critical': return 'text-red-600 bg-red-50';
      case 'warning': return 'text-yellow-600 bg-yellow-50';
      case 'good': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const activeAlerts = alerts.filter(alert => !alert.acknowledged);
  const criticalAlerts = activeAlerts.filter(alert => alert.severity === 'critical');

  return (
    <div className="p-6 space-y-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Real-Time Performance Dashboard</h1>
          <p className="text-gray-600 mt-1">Monitor application performance, business metrics, and system health</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <select 
            value={selectedTimeRange} 
            onChange={(e) => setSelectedTimeRange(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2"
          >
            <option value="1m">Last 1 minute</option>
            <option value="5m">Last 5 minutes</option>
            <option value="15m">Last 15 minutes</option>
            <option value="30m">Last 30 minutes</option>
          </select>
          
          <Button
            onClick={toggleMonitoring}
            variant={isMonitoring ? "destructive" : "default"}
            className="flex items-center space-x-2"
          >
            <RefreshCw className={`h-4 w-4 ${isMonitoring ? 'animate-spin' : ''}`} />
            <span>{isMonitoring ? 'Stop Monitoring' : 'Start Monitoring'}</span>
          </Button>
        </div>
      </div>

      {/* Alert Banner */}
      {criticalAlerts.length > 0 && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <div className="ml-2">
            <h4 className="text-red-800 font-semibold">Critical Performance Issues Detected</h4>
            <p className="text-red-700">
              {criticalAlerts.length} critical alert(s) require immediate attention
            </p>
          </div>
        </Alert>
      )}

      {/* Status Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Page Load Time</p>
              <p className="text-2xl font-bold text-gray-900">
                {metrics.page_load_time?.slice(-1)[0]?.value?.toFixed(0) || '--'}ms
              </p>
            </div>
            <div className={`p-2 rounded-full ${getStatusColor(getMetricStatus('page_load_time', metrics.page_load_time?.slice(-1)[0]?.value || 0))}`}>
              <Clock className="h-6 w-6" />
            </div>
          </div>
          <div className="mt-4">
            <Badge variant={getMetricStatus('page_load_time', metrics.page_load_time?.slice(-1)[0]?.value || 0) === 'good' ? 'default' : 'destructive'}>
              {getMetricStatus('page_load_time', metrics.page_load_time?.slice(-1)[0]?.value || 0)}
            </Badge>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Memory Usage</p>
              <p className="text-2xl font-bold text-gray-900">
                {metrics.memory_usage?.slice(-1)[0]?.value?.toFixed(1) || '--'}%
              </p>
            </div>
            <div className={`p-2 rounded-full ${getStatusColor(getMetricStatus('memory_usage', metrics.memory_usage?.slice(-1)[0]?.value || 0))}`}>
              <Cpu className="h-6 w-6" />
            </div>
          </div>
          <div className="mt-4">
            <Badge variant={getMetricStatus('memory_usage', metrics.memory_usage?.slice(-1)[0]?.value || 0) === 'good' ? 'default' : 'destructive'}>
              {getMetricStatus('memory_usage', metrics.memory_usage?.slice(-1)[0]?.value || 0)}
            </Badge>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Voice Queue</p>
              <p className="text-2xl font-bold text-gray-900">
                {metrics.voice_queue_size?.slice(-1)[0]?.value || '--'}
              </p>
            </div>
            <div className={`p-2 rounded-full ${getStatusColor(getMetricStatus('voice_queue_size', metrics.voice_queue_size?.slice(-1)[0]?.value || 0))}`}>
              <Mic className="h-6 w-6" />
            </div>
          </div>
          <div className="mt-4">
            <Badge variant={getMetricStatus('voice_queue_size', metrics.voice_queue_size?.slice(-1)[0]?.value || 0) === 'good' ? 'default' : 'destructive'}>
              {getMetricStatus('voice_queue_size', metrics.voice_queue_size?.slice(-1)[0]?.value || 0) === 'good' ? 'Normal' : 'High'}
            </Badge>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Sessions</p>
              <p className="text-2xl font-bold text-gray-900">
                {metrics.active_sessions?.slice(-1)[0]?.value || '--'}
              </p>
            </div>
            <div className="p-2 rounded-full bg-blue-50 text-blue-600">
              <Activity className="h-6 w-6" />
            </div>
          </div>
          <div className="mt-4">
            <Badge variant="default">Online</Badge>
          </div>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="business">Business</TabsTrigger>
          <TabsTrigger value="database">Database</TabsTrigger>
          <TabsTrigger value="alerts">Alerts ({activeAlerts.length})</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Page Load Time Chart */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Page Load Performance</h3>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={formatMetricData('page_load_time')}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Area type="monotone" dataKey="value" stroke="#3b82f6" fill="#dbeafe" />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </Card>

            {/* Memory Usage Chart */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Memory Usage</h3>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={formatMetricData('memory_usage')}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="value" stroke="#ef4444" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">Active Alerts</h3>
            <Badge variant={activeAlerts.length > 0 ? "destructive" : "default"}>
              {activeAlerts.length} Active
            </Badge>
          </div>
          
          <div className="space-y-3">
            {alerts.slice(0, 20).map((alert) => (
              <Card key={alert.id} className={`p-4 ${alert.acknowledged ? 'opacity-50' : ''}`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-full ${
                      alert.severity === 'critical' ? 'bg-red-100 text-red-600' :
                      alert.severity === 'high' ? 'bg-orange-100 text-orange-600' :
                      alert.severity === 'medium' ? 'bg-yellow-100 text-yellow-600' :
                      'bg-blue-100 text-blue-600'
                    }`}>
                      <AlertTriangle className="h-4 w-4" />
                    </div>
                    <div>
                      <h4 className="font-semibold">{alert.metric}</h4>
                      <p className="text-sm text-gray-600">
                        Value: {alert.value} (Threshold: {alert.threshold})
                      </p>
                      <p className="text-xs text-gray-500">
                        {new Date(alert.timestamp).toLocaleString()}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Badge variant={alert.severity === 'critical' ? 'destructive' : 'secondary'}>
                      {alert.severity}
                    </Badge>
                    {!alert.acknowledged && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => acknowledgeAlert(alert.id)}
                      >
                        Acknowledge
                      </Button>
                    )}
                  </div>
                </div>
              </Card>
            ))}
            
            {alerts.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
                <p>No alerts at this time. System is running smoothly!</p>
              </div>
            )}
          </div>
        </TabsContent>

        {/* Additional tabs content would continue here... */}
      </Tabs>
    </div>
  );
};

export default RealTimePerformanceDashboard;