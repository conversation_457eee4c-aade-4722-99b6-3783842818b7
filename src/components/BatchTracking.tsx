import { useState } from 'react';
import { Plus } from 'lucide-react';
import ReceivingForm from './forms/ReceivingForm';
import EventsTable from './EventsTable';

export default function BatchTracking() {
  const [showReceivingForm, setShowReceivingForm] = useState(false);

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Batch Tracking</h1>
        <button
          onClick={() => setShowReceivingForm(true)}
          className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="w-4 h-4" />
          New Receiving
        </button>
      </div>

      {showReceivingForm && (
        <div className="bg-white rounded-lg shadow p-6">
          <ReceivingForm
            onSuccess={() => setShowReceivingForm(false)}
            onCancel={() => setShowReceivingForm(false)}
          />
        </div>
      )}

      {/* Show receiving events */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Receiving Records</h2>
          <p className="text-gray-600 mt-1">View all receiving events and batch information</p>
        </div>
        <EventsTable 
          eventTypeFilter="receiving" 
          showTitle={false}
          limit={100}
        />
      </div>
    </div>
  );
}