/**
 * Sensor Status Card Component
 * 
 * Displays individual sensor status with:
 * - Real-time temperature readings
 * - Connection status indicators
 * - Battery level monitoring
 * - Alert notifications
 * - Responsive design
 */

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Thermometer, 
  Wifi, 
  WifiOff, 
  Battery, 
  BatteryLow, 
  AlertTriangle, 
  Settings,
  MapPin,
  Clock,
  Droplets
} from 'lucide-react';
import type { SensorStatus } from '@/types/tempstick';

interface SensorStatusCardProps {
  sensorStatus: SensorStatus;
  compactMode?: boolean;
  showControls?: boolean;
  onConfigure?: (sensorId: string) => void;
  onViewDetails?: (sensorId: string) => void;
  className?: string;
}

export const SensorStatusCard: React.FC<SensorStatusCardProps> = ({ 
  sensorStatus, 
  compactMode = false,
  showControls = true,
  onConfigure,
  onViewDetails,
  className = ''
}) => {
  const { sensor, latestReading, status, activeAlerts, lastSyncTime } = sensorStatus;

  /**
   * Get status styling based on sensor status
   */
  const getStatusStyling = (status: string) => {
    switch (status) {
      case 'online': 
        return {
          badge: 'bg-green-100 text-green-800 border-green-200',
          card: 'border-green-200 bg-green-50/30',
          icon: <Wifi className="h-4 w-4" />
        };
      case 'warning': 
        return {
          badge: 'bg-yellow-100 text-yellow-800 border-yellow-200',
          card: 'border-yellow-200 bg-yellow-50/30',
          icon: <AlertTriangle className="h-4 w-4" />
        };
      case 'critical': 
        return {
          badge: 'bg-red-100 text-red-800 border-red-200',
          card: 'border-red-200 bg-red-50/30',
          icon: <AlertTriangle className="h-4 w-4" />
        };
      case 'offline': 
        return {
          badge: 'bg-gray-100 text-gray-800 border-gray-200',
          card: 'border-gray-200 bg-gray-50/30',
          icon: <WifiOff className="h-4 w-4" />
        };
      default: 
        return {
          badge: 'bg-gray-100 text-gray-800 border-gray-200',
          card: 'border-gray-200',
          icon: <WifiOff className="h-4 w-4" />
        };
    }
  };

  /**
   * Get battery icon based on level
   */
  const getBatteryIcon = (level?: number) => {
    if (!level) return <Battery className="h-4 w-4 text-gray-400" />;
    
    if (level < 25) {
      return <BatteryLow className="h-4 w-4 text-red-500" />;
    } else if (level < 50) {
      return <Battery className="h-4 w-4 text-yellow-500" />;
    } else {
      return <Battery className="h-4 w-4 text-green-500" />;
    }
  };

  /**
   * Format temperature with trend indicator
   */
  const formatTemperature = (temp: number) => {
    return `${temp.toFixed(1)}°F`;
  };

  /**
   * Get time since last reading
   */
  const getTimeSinceReading = (timestamp: string) => {
    const now = new Date();
    const readingTime = new Date(timestamp);
    const diffMs = now.getTime() - readingTime.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    
    if (diffMinutes < 1) return 'Just now';
    if (diffMinutes < 60) return `${diffMinutes}m ago`;
    
    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}d ago`;
  };

  const styling = getStatusStyling(status);

  return (
    <Card className={`hover:shadow-md transition-all duration-200 ${styling.card} ${className}`}>
      <CardHeader className={compactMode ? "pb-2" : "pb-3"}>
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-sm font-medium truncate flex items-center gap-2">
              <Thermometer className="h-4 w-4 flex-shrink-0" />
              {sensor.name}
            </CardTitle>
            {!compactMode && sensor.location && (
              <p className="text-xs text-muted-foreground truncate flex items-center gap-1 mt-1">
                <MapPin className="h-3 w-3 flex-shrink-0" />
                {sensor.location}
              </p>
            )}
          </div>
          
          <div className="flex items-center gap-2 flex-shrink-0">
            <Badge className={`${styling.badge} flex items-center gap-1 text-xs`}>
              {styling.icon}
              <span className="capitalize">{status}</span>
            </Badge>
            
            {showControls && onConfigure && (
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={() => onConfigure(sensor.id)}
              >
                <Settings className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className={compactMode ? "pt-0 pb-4" : "pt-0"}>
        {latestReading ? (
          <div className="space-y-3">
            {/* Temperature Display */}
            <div className="flex items-center justify-between">
              <div>
                <span className="text-2xl font-bold">
                  {formatTemperature(latestReading.temperature)}
                </span>
                {latestReading.humidity && (
                  <div className="flex items-center gap-1 text-sm text-muted-foreground mt-1">
                    <Droplets className="h-3 w-3" />
                    <span>{latestReading.humidity.toFixed(0)}% RH</span>
                  </div>
                )}
              </div>
              
              {/* Battery Level */}
              <div className="text-right">
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  {getBatteryIcon(85)} {/* Would come from sensor data */}
                  <span>85%</span>
                </div>
              </div>
            </div>
            
            {!compactMode && (
              <>
                {/* Last Reading Time */}
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Clock className="h-3 w-3" />
                  <span>
                    Last reading: {getTimeSinceReading(latestReading.reading_timestamp)}
                  </span>
                </div>
                
                {/* Active Alerts */}
                {activeAlerts.length > 0 && (
                  <div className="flex items-center gap-1 text-xs">
                    <AlertTriangle className="h-3 w-3 text-orange-500" />
                    <span className="text-orange-600">
                      {activeAlerts.length} active alert{activeAlerts.length > 1 ? 's' : ''}
                    </span>
                  </div>
                )}
                
                {/* Temperature Status Indicator */}
                {latestReading.alert_triggered && (
                  <div className="flex items-center gap-1 text-xs text-red-600">
                    <AlertTriangle className="h-3 w-3" />
                    <span>Temperature violation detected</span>
                  </div>
                )}
                
                {/* Action Buttons */}
                {showControls && (
                  <div className="flex gap-2 pt-2">
                    {onViewDetails && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex-1 text-xs"
                        onClick={() => onViewDetails(sensor.id)}
                      >
                        View Details
                      </Button>
                    )}
                  </div>
                )}
              </>
            )}
          </div>
        ) : (
          <div className="text-center py-4">
            <div className="text-muted-foreground">
              <Thermometer className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No recent readings</p>
              {status === 'offline' && (
                <p className="text-xs mt-1">Sensor appears to be offline</p>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SensorStatusCard;