/**
 * Sensor Discovery Component
 * 
 * Automatic sensor discovery and registration with:
 * - TempStick API integration for sensor detection
 * - Bulk registration capabilities
 * - Real-time status monitoring
 * - Configuration wizard for new sensors
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { 
  Search, 
  RefreshCw, 
  Plus, 
  CheckCircle, 
  AlertTriangle, 
  Wifi, 
  WifiOff, 
  Battery,
  MapPin,
  Settings
} from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { tempStickService } from '@/lib/tempstick-service';
import type { TempStickSensor, StorageArea, Sensor } from '@/types/tempstick';

interface SensorDiscoveryProps {
  onSensorRegistered?: (sensor: Sensor) => void;
  className?: string;
}

interface DiscoveredSensor extends TempStickSensor {
  isRegistered: boolean;
  isSelected: boolean;
  suggestedName?: string;
  suggestedLocation?: string;
  suggestedStorageArea?: string;
}

interface RegistrationProgress {
  total: number;
  completed: number;
  current: string;
  errors: string[];
}

export const SensorDiscovery: React.FC<SensorDiscoveryProps> = ({
  onSensorRegistered,
  className = ''
}) => {
  // State management
  const [discoveredSensors, setDiscoveredSensors] = useState<DiscoveredSensor[]>([]);
  const [storageAreas, setStorageAreas] = useState<StorageArea[]>([]);
  const [registeredSensors, setRegisteredSensors] = useState<Sensor[]>([]);
  const [isDiscovering, setIsDiscovering] = useState(false);
  const [isRegistering, setIsRegistering] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [registrationProgress, setRegistrationProgress] = useState<RegistrationProgress | null>(null);
  const [showRegistrationDialog, setShowRegistrationDialog] = useState(false);

  /**
   * Load existing registered sensors and storage areas
   */
  const loadExistingData = useCallback(async () => {
    try {
      // Load registered sensors
      const { data: sensorsData, error: sensorsError } = await supabase
        .from('sensors')
        .select('*');

      if (sensorsError) {
        throw new Error(`Failed to load sensors: ${sensorsError.message}`);
      }

      setRegisteredSensors(sensorsData || []);

      // Load storage areas
      const { data: areasData, error: areasError } = await supabase
        .from('storage_areas')
        .select('*')
        .eq('is_active', true)
        .order('name');

      if (areasError) {
        throw new Error(`Failed to load storage areas: ${areasError.message}`);
      }

      setStorageAreas(areasData || []);

    } catch (err) {
      console.error('Failed to load existing data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load existing data');
    }
  }, []);

  /**
   * Discover sensors from TempStick API
   */
  const discoverSensors = useCallback(async () => {
    setIsDiscovering(true);
    setError(null);

    try {
      // Get sensors from TempStick API
      const tempStickSensors = await tempStickService.getSensors();
      
      // Create set of registered sensor IDs for quick lookup
      const registeredSensorIds = new Set(registeredSensors.map(s => s.sensor_id));
      
      // Process discovered sensors
      const processedSensors: DiscoveredSensor[] = tempStickSensors.map(sensor => ({
        ...sensor,
        isRegistered: registeredSensorIds.has(sensor.id),
        isSelected: false,
        suggestedName: generateSuggestedName(sensor),
        suggestedLocation: generateSuggestedLocation(sensor),
        suggestedStorageArea: suggestStorageArea(sensor, storageAreas)
      }));

      setDiscoveredSensors(processedSensors);

    } catch (err) {
      console.error('Failed to discover sensors:', err);
      setError(err instanceof Error ? err.message : 'Failed to discover sensors from TempStick API');
    } finally {
      setIsDiscovering(false);
    }
  }, [registeredSensors, storageAreas]);

  /**
   * Generate suggested name for sensor
   */
  const generateSuggestedName = (sensor: TempStickSensor): string => {
    if (sensor.name) return sensor.name;
    
    // Generate name based on location or ID
    if (sensor.location) {
      return `TempStick - ${sensor.location}`;
    }
    
    return `TempStick ${sensor.id.slice(-4)}`;
  };

  /**
   * Generate suggested location for sensor
   */
  const generateSuggestedLocation = (sensor: TempStickSensor): string => {
    if (sensor.location) return sensor.location;
    return 'Unknown Location';
  };

  /**
   * Suggest storage area based on sensor location
   */
  const suggestStorageArea = (sensor: TempStickSensor, areas: StorageArea[]): string | undefined => {
    if (!sensor.location || areas.length === 0) return undefined;
    
    const location = sensor.location.toLowerCase();
    
    // Simple matching logic - could be enhanced with ML
    for (const area of areas) {
      const areaName = area.name.toLowerCase();
      const areaType = area.area_type.toLowerCase();
      
      if (location.includes(areaName) || location.includes(areaType)) {
        return area.id;
      }
    }
    
    return undefined;
  };

  /**
   * Toggle sensor selection
   */
  const toggleSensorSelection = useCallback((sensorId: string) => {
    setDiscoveredSensors(prev => 
      prev.map(sensor => 
        sensor.id === sensorId 
          ? { ...sensor, isSelected: !sensor.isSelected }
          : sensor
      )
    );
  }, []);

  /**
   * Select all unregistered sensors
   */
  const selectAllUnregistered = useCallback(() => {
    setDiscoveredSensors(prev => 
      prev.map(sensor => ({
        ...sensor,
        isSelected: !sensor.isRegistered
      }))
    );
  }, []);

  /**
   * Clear all selections
   */
  const clearAllSelections = useCallback(() => {
    setDiscoveredSensors(prev => 
      prev.map(sensor => ({
        ...sensor,
        isSelected: false
      }))
    );
  }, []);

  /**
   * Update sensor configuration
   */
  const updateSensorConfig = useCallback((sensorId: string, field: string, value: string) => {
    setDiscoveredSensors(prev => 
      prev.map(sensor => 
        sensor.id === sensorId 
          ? { ...sensor, [field]: value }
          : sensor
      )
    );
  }, []);

  /**
   * Register selected sensors
   */
  const registerSelectedSensors = useCallback(async () => {
    const selectedSensors = discoveredSensors.filter(s => s.isSelected && !s.isRegistered);
    
    if (selectedSensors.length === 0) {
      setError('No sensors selected for registration');
      return;
    }

    setIsRegistering(true);
    setShowRegistrationDialog(true);
    setRegistrationProgress({
      total: selectedSensors.length,
      completed: 0,
      current: '',
      errors: []
    });

    try {
      for (let i = 0; i < selectedSensors.length; i++) {
        const sensor = selectedSensors[i];
        
        setRegistrationProgress(prev => prev ? {
          ...prev,
          current: sensor.suggestedName || sensor.id,
          completed: i
        } : null);

        try {
          const newSensor = {
            sensor_id: sensor.id,
            name: sensor.suggestedName || `TempStick ${sensor.id}`,
            location: sensor.suggestedLocation || 'Unknown Location',
            storage_area_id: sensor.suggestedStorageArea || null,
            is_online: sensor.status === 'online',
            battery_level: sensor.battery_level || null,
            is_active: true
          };

          const { data, error } = await supabase
            .from('sensors')
            .insert([newSensor])
            .select()
            .single();

          if (error) {
            throw new Error(`Failed to register sensor: ${error.message}`);
          }

          // Mark as registered
          setDiscoveredSensors(prev => 
            prev.map(s => 
              s.id === sensor.id 
                ? { ...s, isRegistered: true, isSelected: false }
                : s
            )
          );

          // Notify parent component
          if (onSensorRegistered && data) {
            onSensorRegistered(data);
          }

        } catch (err) {
          const errorMsg = `Failed to register ${sensor.suggestedName}: ${err instanceof Error ? err.message : 'Unknown error'}`;
          setRegistrationProgress(prev => prev ? {
            ...prev,
            errors: [...prev.errors, errorMsg]
          } : null);
        }
      }

      setRegistrationProgress(prev => prev ? {
        ...prev,
        completed: selectedSensors.length,
        current: 'Registration complete'
      } : null);

      // Refresh data
      await loadExistingData();

    } catch (err) {
      console.error('Registration process failed:', err);
      setError(err instanceof Error ? err.message : 'Registration process failed');
    } finally {
      setIsRegistering(false);
    }
  }, [discoveredSensors, onSensorRegistered, loadExistingData]);

  /**
   * Get sensor status styling
   */
  const getSensorStatusStyling = (sensor: DiscoveredSensor) => {
    if (sensor.isRegistered) {
      return { color: 'bg-blue-100 text-blue-800', icon: <CheckCircle className="h-4 w-4" /> };
    }
    if (sensor.status === 'online') {
      return { color: 'bg-green-100 text-green-800', icon: <Wifi className="h-4 w-4" /> };
    }
    return { color: 'bg-gray-100 text-gray-800', icon: <WifiOff className="h-4 w-4" /> };
  };

  // Load existing data on mount
  useEffect(() => {
    loadExistingData();
  }, [loadExistingData]);

  const selectedCount = discoveredSensors.filter(s => s.isSelected).length;
  const unregisteredCount = discoveredSensors.filter(s => !s.isRegistered).length;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-xl font-semibold">Sensor Discovery</h2>
          <p className="text-muted-foreground">
            Discover and register TempStick sensors automatically
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={discoverSensors}
            disabled={isDiscovering}
          >
            <Search className={`h-4 w-4 mr-2 ${isDiscovering ? 'animate-spin' : ''}`} />
            {isDiscovering ? 'Discovering...' : 'Discover Sensors'}
          </Button>
          
          {selectedCount > 0 && (
            <Button
              onClick={registerSelectedSensors}
              disabled={isRegistering}
            >
              <Plus className="h-4 w-4 mr-2" />
              Register Selected ({selectedCount})
            </Button>
          )}
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Discovery Results */}
      {discoveredSensors.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>
                Discovered Sensors ({discoveredSensors.length})
              </CardTitle>
              
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={selectAllUnregistered}
                  disabled={unregisteredCount === 0}
                >
                  Select All ({unregisteredCount})
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearAllSelections}
                  disabled={selectedCount === 0}
                >
                  Clear Selection
                </Button>
              </div>
            </div>
          </CardHeader>
          
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {discoveredSensors.map((sensor) => {
                const styling = getSensorStatusStyling(sensor);
                
                return (
                  <Card 
                    key={sensor.id} 
                    className={`transition-all ${
                      sensor.isSelected ? 'ring-2 ring-blue-500' : ''
                    } ${sensor.isRegistered ? 'opacity-60' : ''}`}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            checked={sensor.isSelected}
                            onCheckedChange={() => toggleSensorSelection(sensor.id)}
                            disabled={sensor.isRegistered}
                          />
                          <div>
                            <h4 className="font-medium text-sm">
                              {sensor.name || sensor.id}
                            </h4>
                            <p className="text-xs text-muted-foreground">
                              ID: {sensor.id}
                            </p>
                          </div>
                        </div>
                        
                        <Badge className={`${styling.color} flex items-center gap-1`}>
                          {styling.icon}
                          <span className="text-xs">
                            {sensor.isRegistered ? 'Registered' : sensor.status}
                          </span>
                        </Badge>
                      </div>

                      {!sensor.isRegistered && sensor.isSelected && (
                        <div className="space-y-3 pt-3 border-t">
                          <div className="space-y-2">
                            <Label className="text-xs">Sensor Name</Label>
                            <Input
                              size="sm"
                              value={sensor.suggestedName || ''}
                              onChange={(e) => updateSensorConfig(sensor.id, 'suggestedName', e.target.value)}
                              placeholder="Enter sensor name"
                            />
                          </div>
                          
                          <div className="space-y-2">
                            <Label className="text-xs">Location</Label>
                            <Input
                              size="sm"
                              value={sensor.suggestedLocation || ''}
                              onChange={(e) => updateSensorConfig(sensor.id, 'suggestedLocation', e.target.value)}
                              placeholder="Enter location"
                            />
                          </div>
                          
                          <div className="space-y-2">
                            <Label className="text-xs">Storage Area</Label>
                            <Select
                              value={sensor.suggestedStorageArea || 'none'}
                              onValueChange={(value) => 
                                updateSensorConfig(sensor.id, 'suggestedStorageArea', value === 'none' ? '' : value)
                              }
                            >
                              <SelectTrigger className="h-8">
                                <SelectValue placeholder="Select area" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="none">No storage area</SelectItem>
                                {storageAreas.map(area => (
                                  <SelectItem key={area.id} value={area.id}>
                                    {area.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      )}

                      {/* Sensor Details */}
                      <div className="mt-3 space-y-1 text-xs text-muted-foreground">
                        {sensor.location && (
                          <div className="flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            <span>{sensor.location}</span>
                          </div>
                        )}
                        
                        {sensor.battery_level && (
                          <div className="flex items-center gap-1">
                            <Battery className="h-3 w-3" />
                            <span>{sensor.battery_level}% battery</span>
                          </div>
                        )}
                        
                        {sensor.firmware_version && (
                          <div className="flex items-center gap-1">
                            <Settings className="h-3 w-3" />
                            <span>Firmware: {sensor.firmware_version}</span>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {discoveredSensors.length === 0 && !isDiscovering && (
        <Card>
          <CardContent className="p-8 text-center">
            <Search className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No sensors discovered</h3>
            <p className="text-muted-foreground mb-4">
              Click "Discover Sensors" to search for available TempStick sensors.
            </p>
            <Button onClick={discoverSensors} disabled={isDiscovering}>
              <Search className="h-4 w-4 mr-2" />
              Discover Sensors
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Registration Progress Dialog */}
      <Dialog open={showRegistrationDialog} onOpenChange={setShowRegistrationDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Registering Sensors</DialogTitle>
          </DialogHeader>
          
          {registrationProgress && (
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress</span>
                  <span>{registrationProgress.completed}/{registrationProgress.total}</span>
                </div>
                <Progress 
                  value={(registrationProgress.completed / registrationProgress.total) * 100} 
                />
              </div>
              
              <div className="text-sm text-muted-foreground">
                {registrationProgress.current}
              </div>
              
              {registrationProgress.errors.length > 0 && (
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-red-600">Errors:</h4>
                  <div className="space-y-1">
                    {registrationProgress.errors.map((error, index) => (
                      <p key={index} className="text-xs text-red-600">
                        {error}
                      </p>
                    ))}
                  </div>
                </div>
              )}
              
              {registrationProgress.completed === registrationProgress.total && (
                <div className="flex justify-end">
                  <Button onClick={() => setShowRegistrationDialog(false)}>
                    Close
                  </Button>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SensorDiscovery;