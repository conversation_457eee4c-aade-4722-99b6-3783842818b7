/**
 * Main Temperature Sensor Dashboard
 * 
 * Provides comprehensive temperature monitoring with real-time updates,
 * sensor status overview, alerts panel, and temperature trends visualization.
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { RefreshCw, Settings, AlertTriangle, Thermometer, Activity } from 'lucide-react';
import { useSensorStatuses, useTemperatureAlerts, useTemperatureSync } from '../../hooks/useTempStick';
import { useThemeAwareStyles } from '../../contexts/ThemeContext';
import { SensorTile } from './SensorTile';
import { TemperatureTrends } from './TemperatureTrends';
import { AlertsPanel } from './AlertsPanel';
import { SensorConfiguration } from './SensorConfiguration';
import { ExportControls } from './ExportControls';

export function SensorDashboard() {
  const [showConfig, setShowConfig] = useState(false);
  const [selectedSensorIds, setSelectedSensorIds] = useState<string[]>([]);
  
  const { sensorStatuses, summary } = useSensorStatuses();
  const { alerts } = useTemperatureAlerts(true);
  const { sync, syncing, lastSyncTime, error: syncError } = useTemperatureSync();
  const styles = useThemeAwareStyles();

  const handleSensorSelect = (sensorId: string, selected: boolean) => {
    setSelectedSensorIds(prev => 
      selected 
        ? [...prev, sensorId]
        : prev.filter(id => id !== sensorId)
    );
  };

  const formatLastSync = (date: Date | null) => {
    if (!date) return 'Never';
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Just now';
    if (minutes === 1) return '1 minute ago';
    if (minutes < 60) return `${minutes} minutes ago`;
    
    const hours = Math.floor(minutes / 60);
    if (hours === 1) return '1 hour ago';
    if (hours < 24) return `${hours} hours ago`;
    
    return date.toLocaleDateString();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className={`text-3xl font-bold ${styles.text.primary}`}>Temperature Monitoring</h1>
          <p className={`${styles.text.secondary} mt-2`}>
            Real-time temperature monitoring and HACCP compliance tracking
          </p>
        </div>
        
        <div className="flex space-x-2">
          <ExportControls 
            selectedSensorIds={selectedSensorIds}
            className="hidden md:block"
          />
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowConfig(true)}
            className="hidden md:flex items-center space-x-2"
          >
            <Settings size={16} />
            <span>Configure</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={sync}
            disabled={syncing}
            className="flex items-center space-x-2"
          >
            <RefreshCw size={16} className={syncing ? 'animate-spin' : ''} />
            <span>{syncing ? 'Syncing...' : 'Sync'}</span>
          </Button>
        </div>
      </div>

      {/* Sync Status */}
      {syncError && (
        <Card className={styles.isDark ? "bg-red-900/50 border-red-800" : "bg-red-50 border-red-200"}>
          <CardContent className="p-4">
            <div className={`flex items-center space-x-2 ${styles.isDark ? "text-red-200" : "text-red-800"}`}>
              <AlertTriangle size={20} />
              <span className="font-medium">Sync Error:</span>
              <span>{syncError}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Sensors</CardTitle>
            <Thermometer className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary.totalSensors}</div>
            <p className="text-xs text-muted-foreground">
              {summary.onlineSensors} online
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Alerts</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${styles.isDark ? "text-red-400" : "text-red-600"}`}>{summary.activeAlerts}</div>
            <p className="text-xs text-muted-foreground">
              {summary.criticalAlerts} critical
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Temperature</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {summary.averageTemperature 
                ? `${summary.averageTemperature.toFixed(1)}°F`
                : '--'
              }
            </div>
            <p className="text-xs text-muted-foreground">
              Last hour average
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Temperature Range</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {summary.temperatureRange 
                ? `${summary.temperatureRange.min.toFixed(1)}° - ${summary.temperatureRange.max.toFixed(1)}°F`
                : '--'
              }
            </div>
            <p className="text-xs text-muted-foreground">
              Current range
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Last Sync</CardTitle>
            <RefreshCw className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-sm">
              {formatLastSync(lastSyncTime)}
            </div>
            <p className="text-xs text-muted-foreground">
              Auto-sync every 5 min
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Sensor Grid */}
      <div>
        <div className="flex justify-between items-center mb-4">
          <h2 className={`text-xl font-semibold ${styles.text.primary}`}>Sensors</h2>
          <div className="flex items-center space-x-2">
            <Badge variant="secondary">
              {selectedSensorIds.length} selected
            </Badge>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSelectedSensorIds([])}
              disabled={selectedSensorIds.length === 0}
            >
              Clear Selection
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {sensorStatuses.map(sensorStatus => (
            <SensorTile
              key={sensorStatus.sensor.id}
              sensorStatus={sensorStatus}
              selected={selectedSensorIds.includes(sensorStatus.sensor.id)}
              onSelectionChange={(selected) => 
                handleSensorSelect(sensorStatus.sensor.id, selected)
              }
            />
          ))}
        </div>

        {sensorStatuses.length === 0 && (
          <Card>
            <CardContent className="p-8 text-center">
              <Thermometer className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className={`text-lg font-medium ${styles.text.primary} mb-2`}>No Sensors Found</h3>
              <p className={`${styles.text.secondary} mb-4`}>
                Connect your TempStick sensors and sync to get started with temperature monitoring.
              </p>
              <Button onClick={sync} disabled={syncing}>
                {syncing ? 'Syncing...' : 'Sync Sensors'}
              </Button>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Temperature Trends */}
      {sensorStatuses.length > 0 && (
        <div>
          <h2 className={`text-xl font-semibold ${styles.text.primary} mb-4`}>Temperature Trends</h2>
          <TemperatureTrends 
            sensorIds={selectedSensorIds.length > 0 ? selectedSensorIds : undefined}
          />
        </div>
      )}

      {/* Alerts Panel */}
      {alerts.length > 0 && (
        <div>
          <h2 className={`text-xl font-semibold ${styles.text.primary} mb-4`}>Active Alerts</h2>
          <AlertsPanel />
        </div>
      )}

      {/* Configuration Modal */}
      {showConfig && (
        <SensorConfiguration
          onClose={() => setShowConfig(false)}
        />
      )}
    </div>
  );
}