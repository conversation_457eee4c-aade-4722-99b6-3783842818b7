/**
 * Export Controls Component
 * 
 * Provides temperature data export functionality for PDF reports,
 * Excel files, Google Sheets integration, and email automation.
 * Now integrated with the advanced PDF report generation system.
 */

import React, { useState } from 'react';
import { Button } from '../ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '../ui/dropdown-menu';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../ui/dialog';
import { useThemeAwareStyles } from '../../contexts/ThemeContext';
import { 
  Download, 
  FileText, 
  FileSpreadsheet, 
  Mail, 
  Share,
  Calendar,
  ChevronDown,
  Zap
} from 'lucide-react';

import { AdvancedReportGenerator } from './AdvancedReportGenerator';
import { useSensors } from '../../hooks/useTempStick';
import { reportService } from '../../lib/report-service-integration';

import type { 
  TemperatureReportParams,
  EmailReportConfig 
} from '../../types/tempstick';
import type { ReportGenerationResult } from '../../lib/report-service-integration';

interface ExportControlsProps {
  selectedSensorIds: string[];
  className?: string;
  defaultDateRange?: {
    startDate: Date;
    endDate: Date;
  };
}

export function ExportControls({ 
  selectedSensorIds, 
  className,
  defaultDateRange = {
    startDate: new Date(Date.now() - 24 * 60 * 60 * 1000),
    endDate: new Date()
  }
}: ExportControlsProps) {
  const [showAdvancedReports, setShowAdvancedReports] = useState(false);
  const [showEmailDialog, setShowEmailDialog] = useState(false);
  const [exporting, setExporting] = useState(false);

  const { sensors } = useSensors();
  const styles = useThemeAwareStyles();

  const handleQuickExport = async (templateId: string) => {
    setExporting(true);
    
    try {
      const params: TemperatureReportParams = {
        startDate: defaultDateRange.startDate,
        endDate: defaultDateRange.endDate,
        sensorIds: selectedSensorIds.length > 0 ? selectedSensorIds : [],
        includeAlerts: true,
        includeCharts: true,
        includeHACCPData: true,
        format: 'pdf'
      };

      const reportId = await reportService.queueReport({
        templateId,
        params,
        delivery: {
          download: {}
        },
        options: {
          priority: 'normal',
          compress: true
        }
      });

      // Poll for completion and auto-download
      const pollForCompletion = setInterval(async () => {
        const status = reportService.getReportStatus(reportId);
        if (status?.status === 'completed' && status.result?.success) {
          clearInterval(pollForCompletion);
          handleDownloadResult(status.result);
          setExporting(false);
        } else if (status?.status === 'failed') {
          clearInterval(pollForCompletion);
          alert(`Export failed: ${  status.error || 'Unknown error'}`);
          setExporting(false);
        }
      }, 1000);

      // Cleanup after 5 minutes to prevent endless polling
      setTimeout(() => {
        clearInterval(pollForCompletion);
        if (exporting) {
          setExporting(false);
        }
      }, 5 * 60 * 1000);
      
    } catch (error) {
      console.error('Export failed:', error);
      alert('Export failed. Please try again.');
      setExporting(false);
    }
  };

  const handleDownloadResult = (result: ReportGenerationResult) => {
    if (result.downloadUrl) {
      const link = document.createElement('a');
      link.href = result.downloadUrl;
      link.download = result.filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else if (result.pdfBuffer) {
      const blob = new Blob([result.pdfBuffer], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = result.filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }
  };

  const handleReportGenerated = (result: ReportGenerationResult) => {
    handleDownloadResult(result);
    setShowAdvancedReports(false);
  };

  const handleLegacyExport = async (format: 'excel' | 'csv') => {
    setExporting(true);
    
    try {
      // Simulate legacy export functionality
      await new Promise(resolve => setTimeout(resolve, 2000));
      alert(`${format.toUpperCase()} export completed! (Legacy functionality)`);
    } catch (error) {
      console.error('Legacy export failed:', error);
      alert('Export failed. Please try again.');
    } finally {
      setExporting(false);
    }
  };

  return (
    <>
      <div className={className}>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button 
              variant="outline" 
              className="flex items-center space-x-2"
              disabled={exporting}
            >
              <Download className="h-4 w-4" />
              <span>{exporting ? 'Exporting...' : 'Export'}</span>
              <ChevronDown className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-64">
            {/* Quick PDF Reports */}
            <div className={`px-2 py-1.5 text-xs font-semibold ${styles.text.secondary} uppercase tracking-wide`}>
              PDF Reports
            </div>
            <DropdownMenuItem onClick={() => handleQuickExport('daily-summary')}>
              <FileText className="h-4 w-4 mr-2" />
              <div>
                <div>Daily Summary</div>
                <div className={`text-xs ${styles.text.muted}`}>24-hour overview</div>
              </div>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleQuickExport('haccp-compliance')}>
              <FileText className="h-4 w-4 mr-2" />
              <div>
                <div>HACCP Compliance</div>
                <div className={`text-xs ${styles.text.muted}`}>Violations & actions</div>
              </div>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleQuickExport('comprehensive')}>
              <FileText className="h-4 w-4 mr-2" />
              <div>
                <div>Comprehensive Report</div>
                <div className={`text-xs ${styles.text.muted}`}>Full analysis</div>
              </div>
            </DropdownMenuItem>

            <DropdownMenuSeparator />

            {/* Advanced Options */}
            <DropdownMenuItem onClick={() => setShowAdvancedReports(true)}>
              <Zap className="h-4 w-4 mr-2" />
              <div>
                <div>Advanced Reports</div>
                <div className={`text-xs ${styles.text.muted}`}>Custom templates & scheduling</div>
              </div>
            </DropdownMenuItem>

            <DropdownMenuSeparator />

            {/* Legacy Exports */}
            <div className={`px-2 py-1.5 text-xs font-semibold ${styles.text.secondary} uppercase tracking-wide`}>
              Data Exports
            </div>
            <DropdownMenuItem onClick={() => handleLegacyExport('excel')}>
              <FileSpreadsheet className="h-4 w-4 mr-2" />
              Excel Spreadsheet
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleLegacyExport('csv')}>
              <FileSpreadsheet className="h-4 w-4 mr-2" />
              CSV Data
            </DropdownMenuItem>
            
            <DropdownMenuSeparator />
            
            {/* Email & Scheduling */}
            <DropdownMenuItem onClick={() => setShowEmailDialog(true)}>
              <Mail className="h-4 w-4 mr-2" />
              Schedule Email Reports
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => alert('Google Sheets integration coming soon!')}>
              <Share className="h-4 w-4 mr-2" />
              Google Sheets Sync
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Advanced Report Generator Dialog */}
      <Dialog open={showAdvancedReports} onOpenChange={setShowAdvancedReports}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Advanced Report Generation</DialogTitle>
          </DialogHeader>
          <AdvancedReportGenerator
            defaultDateRange={defaultDateRange}
            defaultSensorIds={selectedSensorIds}
            onReportGenerated={handleReportGenerated}
          />
        </DialogContent>
      </Dialog>

      {/* Email Reports Dialog (Legacy - simplified) */}
      <Dialog open={showEmailDialog} onOpenChange={setShowEmailDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Schedule Email Reports</DialogTitle>
          </DialogHeader>

          <div className="p-6 text-center space-y-4">
            <Calendar className={`h-12 w-12 mx-auto ${styles.isDark ? 'text-blue-400' : 'text-blue-500'}`} />
            <div>
              <h3 className={`text-lg font-medium ${styles.text.primary} mb-2`}>Enhanced Email Scheduling Available</h3>
              <p className={`${styles.text.secondary} mb-4`}>
                Use the Advanced Report Generator for comprehensive email scheduling with 
                template selection, customization options, and automated delivery.
              </p>
            </div>
            
            <div className="flex items-center justify-center space-x-3">
              <Button variant="outline" onClick={() => setShowEmailDialog(false)}>
                Cancel
              </Button>
              <Button onClick={() => {
                setShowEmailDialog(false);
                setShowAdvancedReports(true);
              }}>
                <Zap className="h-4 w-4 mr-2" />
                Open Advanced Reports
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}