/**
 * Real-Time Sync Dashboard
 * 
 * Focuses on getting current TempStick readings and syncing to remote Supabase database
 * with comprehensive monitoring, manual controls, and real-time feedback.
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription } from '../ui/alert';
import { 
  RefreshCw, 
  Database, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Activity,
  AlertTriangle,
  Thermometer,
  Wifi,
  WifiOff,
  ArrowDownToLine,
  Settings
} from 'lucide-react';
import { tempStickService } from '../../lib/tempstick-service';
import { useSensors, useTemperatureSync } from '../../hooks/useTempStick';

interface SyncMetrics {
  totalApiCalls: number;
  successfulSyncs: number;
  failedSyncs: number;
  totalReadingsSynced: number;
  averageResponseTime: number;
  lastSyncDuration: number;
}

interface DatabaseStatus {
  connected: boolean;
  lastWrite: Date | null;
  pendingWrites: number;
  errorCount: number;
  latency: number;
}

export function RealTimeSyncDashboard() {
  // Core state
  const [isManualSyncing, setIsManualSyncing] = useState(false);
  const [syncStatus, setSyncStatus] = useState<'idle' | 'syncing' | 'success' | 'error'>('idle');
  const [currentReadings, setCurrentReadings] = useState<any[]>([]);
  const [syncMetrics, setSyncMetrics] = useState<SyncMetrics>({
    totalApiCalls: 0,
    successfulSyncs: 0,
    failedSyncs: 0,
    totalReadingsSynced: 0,
    averageResponseTime: 0,
    lastSyncDuration: 0
  });
  const [databaseStatus, setDatabaseStatus] = useState<DatabaseStatus>({
    connected: true,
    lastWrite: null,
    pendingWrites: 0,
    errorCount: 0,
    latency: 0
  });

  // Use existing hooks
  const { sensors, loading: sensorsLoading, error: sensorsError, syncSensors } = useSensors();
  const { sync: autoSync, syncing: autoSyncing, lastSyncTime, error: syncError } = useTemperatureSync();

  /**
   * Enhanced manual sync with detailed progress tracking
   */
  const performManualSync = useCallback(async () => {
    if (isManualSyncing) return;

    setIsManualSyncing(true);
    setSyncStatus('syncing');
    const startTime = Date.now();

    try {
      console.log('🚀 Starting manual temperature sync...');

      // Step 1: Get current sensors from TempStick API
      const tempStickSensors = await tempStickService.getAllSensors();
      console.log(`📡 Retrieved ${tempStickSensors.length} sensors from TempStick API`);

      // Step 2: Sync sensors to database
      await tempStickService.syncSensors();
      console.log('✅ Sensors synced to database');

      // Step 3: Get current readings for all sensors
      const allReadings: any[] = [];
      for (const sensor of tempStickSensors) {
        try {
          const readings = await tempStickService.getLatestReadings(sensor.sensor_id, 5);
          if (readings.length > 0) {
            allReadings.push(...readings.map(reading => ({
              ...reading,
              sensor_name: sensor.sensor_name,
              sensor_location: sensor.location
            })));
          }
        } catch (error) {
          console.warn(`Failed to get readings for sensor ${sensor.sensor_id}:`, error);
        }
      }

      setCurrentReadings(allReadings);
      console.log(`📊 Retrieved ${allReadings.length} total readings`);

      // Step 4: Sync temperature readings to database
      const syncResult = await tempStickService.syncAllTemperatureReadings();
      
      // Update metrics
      const duration = Date.now() - startTime;
      setSyncMetrics(prev => ({
        totalApiCalls: prev.totalApiCalls + tempStickSensors.length + 1,
        successfulSyncs: prev.successfulSyncs + 1,
        failedSyncs: prev.failedSyncs + syncResult.errors.length,
        totalReadingsSynced: prev.totalReadingsSynced + syncResult.newReadings,
        averageResponseTime: (prev.averageResponseTime + duration) / 2,
        lastSyncDuration: duration
      }));

      // Update database status
      setDatabaseStatus(prev => ({
        ...prev,
        connected: true,
        lastWrite: new Date(),
        pendingWrites: 0,
        latency: duration / 10 // Rough estimate
      }));

      setSyncStatus('success');
      console.log(`🎉 Manual sync completed in ${duration}ms`);
      console.log(`📈 Synced ${syncResult.newReadings} new readings`);
      console.log(`🚨 Generated ${syncResult.newAlerts} alerts`);

    } catch (error) {
      console.error('❌ Manual sync failed:', error);
      setSyncStatus('error');
      
      // Update error metrics
      setSyncMetrics(prev => ({
        ...prev,
        failedSyncs: prev.failedSyncs + 1
      }));

      setDatabaseStatus(prev => ({
        ...prev,
        connected: false,
        errorCount: prev.errorCount + 1
      }));
    } finally {
      setIsManualSyncing(false);
      
      // Reset status after 3 seconds
      setTimeout(() => {
        if (syncStatus !== 'syncing') {
          setSyncStatus('idle');
        }
      }, 3000);
    }
  }, [isManualSyncing, syncStatus]);

  /**
   * Test TempStick API connectivity
   */
  const testApiConnection = useCallback(async () => {
    try {
      console.log('🔍 Testing TempStick API connectivity...');
      const result = await tempStickService.performHealthCheck();
      
      if (result.tempstickApi.status === 'healthy') {
        console.log('✅ TempStick API is healthy');
        return { success: true, latency: result.tempstickApi.latency };
      } else {
        console.error('❌ TempStick API is down');
        return { success: false, error: 'API is down' };
      }
    } catch (error) {
      console.error('❌ API connection test failed:', error);
      return { success: false, error: error.message };
    }
  }, []);

  /**
   * Get sync status color and icon
   */
  const getSyncStatusDisplay = useCallback(() => {
    const isCurrentlySyncing = isManualSyncing || autoSyncing;
    
    if (isCurrentlySyncing) {
      return {
        color: 'bg-blue-100 text-blue-800 border-blue-200',
        icon: <RefreshCw className="w-4 h-4 animate-spin" />,
        text: 'Syncing...'
      };
    }

    switch (syncStatus) {
      case 'success':
        return {
          color: 'bg-green-100 text-green-800 border-green-200',
          icon: <CheckCircle className="w-4 h-4" />,
          text: 'Success'
        };
      case 'error':
        return {
          color: 'bg-red-100 text-red-800 border-red-200',
          icon: <XCircle className="w-4 h-4" />,
          text: 'Error'
        };
      default:
        return {
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          icon: <Clock className="w-4 h-4" />,
          text: 'Ready'
        };
    }
  }, [isManualSyncing, autoSyncing, syncStatus]);

  const statusDisplay = getSyncStatusDisplay();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Real-Time Temperature Sync</h1>
          <p className="text-gray-600 mt-2">
            Live sync from TempStick sensors to remote Supabase database
          </p>
        </div>

        <div className="flex space-x-3">
          <Button
            onClick={testApiConnection}
            variant="outline"
            size="sm"
            className="flex items-center space-x-2"
          >
            <Wifi className="w-4 h-4" />
            <span>Test API</span>
          </Button>
          <Button
            onClick={performManualSync}
            disabled={isManualSyncing || autoSyncing}
            className="flex items-center space-x-2"
          >
            <ArrowDownToLine className="w-4 h-4" />
            <span>Sync Now</span>
          </Button>
        </div>
      </div>

      {/* Sync Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Current Status */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Sync Status</CardTitle>
            {statusDisplay.icon}
          </CardHeader>
          <CardContent>
            <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusDisplay.color}`}>
              {statusDisplay.text}
            </div>
            {lastSyncTime && (
              <p className="text-xs text-gray-500 mt-1">
                Last: {lastSyncTime.toLocaleTimeString()}
              </p>
            )}
          </CardContent>
        </Card>

        {/* Database Status */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Database</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              {databaseStatus.connected ? (
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-green-700">Connected</span>
                </div>
              ) : (
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  <span className="text-sm text-red-700">Disconnected</span>
                </div>
              )}
            </div>
            {databaseStatus.lastWrite && (
              <p className="text-xs text-gray-500 mt-1">
                Last write: {databaseStatus.lastWrite.toLocaleTimeString()}
              </p>
            )}
          </CardContent>
        </Card>

        {/* Active Sensors */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Sensors</CardTitle>
            <Thermometer className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{sensors.length}</div>
            <p className="text-xs text-muted-foreground">
              {sensors.filter(s => s.is_online).length} online
            </p>
          </CardContent>
        </Card>

        {/* Sync Performance */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Performance</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {syncMetrics.averageResponseTime > 0 
                ? `${Math.round(syncMetrics.averageResponseTime / 1000)}s`
                : '--'
              }
            </div>
            <p className="text-xs text-muted-foreground">
              Avg sync time
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Error Display */}
      {(syncError || sensorsError) && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-700">
            {syncError || sensorsError}
          </AlertDescription>
        </Alert>
      )}

      {/* Current Readings Display */}
      {currentReadings.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Thermometer className="w-5 h-5" />
              <span>Latest Readings from TempStick API</span>
              <Badge variant="secondary">{currentReadings.length} readings</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {currentReadings.slice(0, 9).map((reading, index) => (
                <div key={index} className="bg-gray-50 p-3 rounded-lg">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="font-medium text-sm text-gray-900">
                        {reading.sensor_name || `Sensor ${reading.sensor_id}`}
                      </h4>
                      {reading.sensor_location && (
                        <p className="text-xs text-gray-500">{reading.sensor_location}</p>
                      )}
                    </div>
                    <Badge 
                      variant="secondary" 
                      className="bg-blue-100 text-blue-800"
                    >
                      Live
                    </Badge>
                  </div>
                  
                  <div className="space-y-1">
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-600">Temperature:</span>
                      <span className="text-sm font-mono">
                        {typeof reading.temperature === 'number' 
                          ? `${reading.temperature.toFixed(1)}°F`
                          : 'N/A'
                        }
                      </span>
                    </div>
                    
                    {reading.humidity && (
                      <div className="flex justify-between items-center">
                        <span className="text-xs text-gray-600">Humidity:</span>
                        <span className="text-sm font-mono">{reading.humidity}%</span>
                      </div>
                    )}
                    
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-600">Time:</span>
                      <span className="text-xs font-mono">
                        {new Date(reading.timestamp).toLocaleTimeString()}
                      </span>
                    </div>

                    {reading.battery_level && (
                      <div className="flex justify-between items-center">
                        <span className="text-xs text-gray-600">Battery:</span>
                        <span className={`text-xs font-mono ${
                          reading.battery_level > 50 ? 'text-green-600' : 
                          reading.battery_level > 25 ? 'text-yellow-600' : 'text-red-600'
                        }`}>
                          {reading.battery_level}%
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
            
            {currentReadings.length > 9 && (
              <p className="text-sm text-gray-500 mt-4 text-center">
                Showing 9 of {currentReadings.length} readings
              </p>
            )}
          </CardContent>
        </Card>
      )}

      {/* Sync Metrics */}
      <Card>
        <CardHeader>
          <CardTitle>Sync Statistics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <div className="text-2xl font-bold text-blue-600">
                {syncMetrics.totalApiCalls}
              </div>
              <p className="text-sm text-gray-600">Total API calls</p>
            </div>
            
            <div>
              <div className="text-2xl font-bold text-green-600">
                {syncMetrics.successfulSyncs}
              </div>
              <p className="text-sm text-gray-600">Successful syncs</p>
            </div>
            
            <div>
              <div className="text-2xl font-bold text-red-600">
                {syncMetrics.failedSyncs}
              </div>
              <p className="text-sm text-gray-600">Failed syncs</p>
            </div>
            
            <div>
              <div className="text-2xl font-bold text-purple-600">
                {syncMetrics.totalReadingsSynced}
              </div>
              <p className="text-sm text-gray-600">Readings synced</p>
            </div>
          </div>
          
          {syncMetrics.lastSyncDuration > 0 && (
            <div className="mt-4 pt-4 border-t">
              <p className="text-sm text-gray-600">
                Last sync took <strong>{Math.round(syncMetrics.lastSyncDuration / 1000)} seconds</strong>
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}