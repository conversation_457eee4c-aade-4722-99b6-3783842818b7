/**
 * Temperature Chart Component
 * 
 * Interactive temperature trend chart with:
 * - Real-time data visualization
 * - Multiple sensor support
 * - Alert threshold indicators
 * - Responsive design
 * - Customizable time ranges
 */

import React, { useMemo } from 'react';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer, 
  ReferenceLine,
  Legend,
  Area,
  AreaChart
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { TrendingUp, TrendingDown, Minus, Download } from 'lucide-react';
import type { TemperatureTrendData, TimeRange } from '@/types/tempstick';

interface TemperatureChartProps {
  data: TemperatureTrendData[];
  timeRange: TimeRange;
  onTimeRangeChange?: (range: TimeRange) => void;
  showHumidity?: boolean;
  showAlertThresholds?: boolean;
  alertThresholds?: {
    minTemp: number;
    maxTemp: number;
  };
  height?: number;
  className?: string;
  title?: string;
  showControls?: boolean;
  onExport?: () => void;
}

interface ChartDataPoint {
  timestamp: string;
  time: number;
  averageTemp: number;
  minTemp: number;
  maxTemp: number;
  averageHumidity?: number;
  alertCount: number;
  sensorCount: number;
  formattedTime: string;
}

export const TemperatureChart: React.FC<TemperatureChartProps> = ({
  data,
  timeRange,
  onTimeRangeChange,
  showHumidity = false,
  showAlertThresholds = true,
  alertThresholds,
  height = 400,
  className = '',
  title = 'Temperature Trends',
  showControls = true,
  onExport
}) => {
  /**
   * Process and aggregate temperature data for chart display
   */
  const chartData = useMemo((): ChartDataPoint[] => {
    if (data.length === 0) return [];

    // Group data by time intervals based on time range
    const intervalMs = getIntervalMs(timeRange);
    const groupedData = new Map<number, TemperatureTrendData[]>();

    data.forEach(reading => {
      const timestamp = new Date(reading.timestamp).getTime();
      const intervalStart = Math.floor(timestamp / intervalMs) * intervalMs;
      
      if (!groupedData.has(intervalStart)) {
        groupedData.set(intervalStart, []);
      }
      groupedData.get(intervalStart)!.push(reading);
    });

    // Convert grouped data to chart format
    const chartPoints: ChartDataPoint[] = Array.from(groupedData.entries())
      .map(([timestamp, readings]) => {
        const temperatures = readings.map(r => r.temperature);
        const humidities = readings.map(r => r.humidity).filter(h => h !== undefined) as number[];
        const alertCount = readings.filter(r => r.alertLevel === 'critical').length;
        
        return {
          timestamp: new Date(timestamp).toISOString(),
          time: timestamp,
          averageTemp: Math.round((temperatures.reduce((sum, temp) => sum + temp, 0) / temperatures.length) * 10) / 10,
          minTemp: Math.min(...temperatures),
          maxTemp: Math.max(...temperatures),
          averageHumidity: humidities.length > 0 
            ? Math.round((humidities.reduce((sum, h) => sum + h, 0) / humidities.length) * 10) / 10 
            : undefined,
          alertCount,
          sensorCount: new Set(readings.map(r => r.sensorId)).size,
          formattedTime: formatTimeForRange(new Date(timestamp), timeRange)
        };
      })
      .sort((a, b) => a.time - b.time);

    return chartPoints;
  }, [data, timeRange]);

  /**
   * Calculate temperature statistics
   */
  const temperatureStats = useMemo(() => {
    if (chartData.length === 0) return null;

    const temperatures = chartData.map(d => d.averageTemp);
    const current = temperatures[temperatures.length - 1];
    const previous = temperatures[temperatures.length - 2];
    const trend = previous ? (current > previous ? 'up' : current < previous ? 'down' : 'stable') : 'stable';
    
    return {
      current: current.toFixed(1),
      min: Math.min(...temperatures).toFixed(1),
      max: Math.max(...temperatures).toFixed(1),
      average: (temperatures.reduce((sum, temp) => sum + temp, 0) / temperatures.length).toFixed(1),
      trend,
      change: previous ? Math.abs(current - previous).toFixed(1) : '0.0'
    };
  }, [chartData]);

  /**
   * Custom tooltip component
   */
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (!active || !payload?.length) return null;

    const data = payload[0].payload as ChartDataPoint;
    
    return (
      <div className="bg-white p-3 border rounded-lg shadow-lg">
        <p className="font-medium text-sm mb-2">
          {new Date(data.timestamp).toLocaleString()}
        </p>
        <div className="space-y-1 text-xs">
          <div className="flex justify-between items-center">
            <span className="text-blue-600">Average:</span>
            <span className="font-medium">{data.averageTemp}°F</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-green-600">Min:</span>
            <span className="font-medium">{data.minTemp}°F</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-red-600">Max:</span>
            <span className="font-medium">{data.maxTemp}°F</span>
          </div>
          {showHumidity && data.averageHumidity && (
            <div className="flex justify-between items-center">
              <span className="text-purple-600">Humidity:</span>
              <span className="font-medium">{data.averageHumidity}%</span>
            </div>
          )}
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Sensors:</span>
            <span className="font-medium">{data.sensorCount}</span>
          </div>
          {data.alertCount > 0 && (
            <div className="flex justify-between items-center">
              <span className="text-orange-600">Alerts:</span>
              <span className="font-medium">{data.alertCount}</span>
            </div>
          )}
        </div>
      </div>
    );
  };

  /**
   * Get trend icon based on temperature trend
   */
  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="h-4 w-4 text-red-500" />;
      case 'down': return <TrendingDown className="h-4 w-4 text-blue-500" />;
      default: return <Minus className="h-4 w-4 text-gray-500" />;
    }
  };

  if (chartData.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64 text-muted-foreground">
            <div className="text-center">
              <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No temperature data available</p>
              <p className="text-sm">Data will appear here once sensors start reporting</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <CardTitle className="flex items-center gap-2">
              {title}
              {temperatureStats && getTrendIcon(temperatureStats.trend)}
            </CardTitle>
            {temperatureStats && (
              <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                <span>Current: {temperatureStats.current}°F</span>
                <span>Avg: {temperatureStats.average}°F</span>
                <span>Range: {temperatureStats.min}°F - {temperatureStats.max}°F</span>
              </div>
            )}
          </div>
          
          {showControls && (
            <div className="flex items-center gap-2">
              {onTimeRangeChange && (
                <Select value={timeRange} onValueChange={onTimeRangeChange}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1h">Last Hour</SelectItem>
                    <SelectItem value="6h">Last 6 Hours</SelectItem>
                    <SelectItem value="24h">Last 24 Hours</SelectItem>
                    <SelectItem value="7d">Last 7 Days</SelectItem>
                    <SelectItem value="30d">Last 30 Days</SelectItem>
                  </SelectContent>
                </Select>
              )}
              
              {onExport && (
                <Button variant="outline" size="sm" onClick={onExport}>
                  <Download className="h-4 w-4" />
                </Button>
              )}
            </div>
          )}
        </div>
      </CardHeader>
      
      <CardContent>
        <div style={{ height }}>
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              
              <XAxis 
                dataKey="formattedTime"
                tick={{ fontSize: 12 }}
                tickLine={{ stroke: '#e0e0e0' }}
              />
              
              <YAxis 
                label={{ value: 'Temperature (°F)', angle: -90, position: 'insideLeft' }}
                tick={{ fontSize: 12 }}
                tickLine={{ stroke: '#e0e0e0' }}
              />
              
              {showHumidity && (
                <YAxis 
                  yAxisId="humidity"
                  orientation="right"
                  label={{ value: 'Humidity (%)', angle: 90, position: 'insideRight' }}
                  tick={{ fontSize: 12 }}
                  tickLine={{ stroke: '#e0e0e0' }}
                />
              )}
              
              <Tooltip content={<CustomTooltip />} />
              
              <Legend />
              
              {/* Alert thresholds */}
              {showAlertThresholds && alertThresholds && (
                <>
                  <ReferenceLine 
                    y={alertThresholds.maxTemp} 
                    stroke="#ef4444" 
                    strokeDasharray="5 5"
                    label={{ value: "Max Temp", position: "topRight" }}
                  />
                  <ReferenceLine 
                    y={alertThresholds.minTemp} 
                    stroke="#3b82f6" 
                    strokeDasharray="5 5"
                    label={{ value: "Min Temp", position: "bottomRight" }}
                  />
                </>
              )}
              
              {/* Temperature lines */}
              <Line 
                type="monotone" 
                dataKey="averageTemp" 
                stroke="#2563eb" 
                strokeWidth={2}
                dot={false}
                name="Average Temperature"
                connectNulls={false}
              />
              
              <Line 
                type="monotone" 
                dataKey="minTemp" 
                stroke="#10b981" 
                strokeWidth={1}
                strokeDasharray="3 3"
                dot={false}
                name="Min Temperature"
                connectNulls={false}
              />
              
              <Line 
                type="monotone" 
                dataKey="maxTemp" 
                stroke="#ef4444" 
                strokeWidth={1}
                strokeDasharray="3 3"
                dot={false}
                name="Max Temperature"
                connectNulls={false}
              />
              
              {/* Humidity line */}
              {showHumidity && (
                <Line 
                  yAxisId="humidity"
                  type="monotone" 
                  dataKey="averageHumidity" 
                  stroke="#8b5cf6" 
                  strokeWidth={1}
                  dot={false}
                  name="Humidity"
                  connectNulls={false}
                />
              )}
            </LineChart>
          </ResponsiveContainer>
        </div>
        
        {/* Chart legend with statistics */}
        <div className="flex flex-wrap items-center justify-center gap-4 mt-4 text-xs">
          <div className="flex items-center gap-1">
            <div className="w-3 h-0.5 bg-blue-500"></div>
            <span>Average</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-0.5 bg-green-500 border-dashed border-t"></div>
            <span>Minimum</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-0.5 bg-red-500 border-dashed border-t"></div>
            <span>Maximum</span>
          </div>
          {showHumidity && (
            <div className="flex items-center gap-1">
              <div className="w-3 h-0.5 bg-purple-500"></div>
              <span>Humidity</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * Get appropriate interval for data grouping based on time range
 */
function getIntervalMs(timeRange: TimeRange): number {
  switch (timeRange) {
    case '1h': return 2 * 60 * 1000; // 2 minutes
    case '6h': return 10 * 60 * 1000; // 10 minutes
    case '24h': return 30 * 60 * 1000; // 30 minutes
    case '7d': return 2 * 60 * 60 * 1000; // 2 hours
    case '30d': return 6 * 60 * 60 * 1000; // 6 hours
    default: return 30 * 60 * 1000; // 30 minutes
  }
}

/**
 * Format time display based on time range
 */
function formatTimeForRange(date: Date, timeRange: TimeRange): string {
  switch (timeRange) {
    case '1h':
    case '6h':
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    case '24h':
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    case '7d':
      return `${date.toLocaleDateString([], { month: 'short', day: 'numeric' })  } ${ 
             date.toLocaleTimeString([], { hour: '2-digit' })}`;
    case '30d':
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    default:
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }
}

export default TemperatureChart;