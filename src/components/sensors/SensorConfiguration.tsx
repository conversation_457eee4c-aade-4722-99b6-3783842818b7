/**
 * Sensor Configuration Modal
 * 
 * Allows users to configure sensor settings, thresholds, storage areas,
 * and alert preferences for temperature monitoring and HACCP compliance.
 */

import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '../ui/dialog';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Checkbox } from '../ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { 
  Settings, 
  Thermometer, 
  MapPin, 
  AlertTriangle, 
  Save,
  X,
  Plus,
  Trash2
} from 'lucide-react';
import { useSensors } from '../../hooks/useTempStick';
import { supabase } from '../../lib/supabase';
import type { Sensor, StorageArea } from '../../types/tempstick';

interface SensorConfigurationProps {
  onClose: () => void;
}

export function SensorConfiguration({ onClose }: SensorConfigurationProps) {
  const { sensors, refetch } = useSensors();
  const [storageAreas, setStorageAreas] = useState<StorageArea[]>([]);
  const [selectedSensorId, setSelectedSensorId] = useState<string | null>(null);
  const [sensorConfig, setSensorConfig] = useState<Partial<Sensor>>({});
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const selectedSensor = sensors.find(s => s.id === selectedSensorId);

  // Fetch storage areas
  useEffect(() => {
    const fetchStorageAreas = async () => {
      const { data, error } = await supabase
        .from('storage_areas')
        .select('*')
        .order('name');

      if (error) {
        console.error('Failed to fetch storage areas:', error);
      } else {
        setStorageAreas(data || []);
      }
    };

    fetchStorageAreas();
  }, []);

  // Initialize sensor config when sensor is selected
  useEffect(() => {
    if (selectedSensor) {
      setSensorConfig({
        name: selectedSensor.name,
        location: selectedSensor.location,
        temp_min_threshold: selectedSensor.temp_min_threshold,
        temp_max_threshold: selectedSensor.temp_max_threshold,
        humidity_min_threshold: selectedSensor.humidity_min_threshold,
        humidity_max_threshold: selectedSensor.humidity_max_threshold,
        storage_area_id: selectedSensor.storage_area_id,
        active: selectedSensor.active,
      });
    }
  }, [selectedSensor]);

  const handleSaveSensorConfig = async () => {
    if (!selectedSensorId || !sensorConfig) return;

    setSaving(true);
    setError(null);

    try {
      const { error } = await supabase
        .from('sensors')
        .update(sensorConfig)
        .eq('id', selectedSensorId);

      if (error) throw error;

      await refetch();
      setSelectedSensorId(null);
      setSensorConfig({});
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save sensor configuration');
    } finally {
      setSaving(false);
    }
  };

  const handleCreateStorageArea = async () => {
    const name = prompt('Enter storage area name:');
    if (!name) return;

    try {
      const { error } = await supabase
        .from('storage_areas')
        .insert([{
          name,
          area_type: 'other',
          haccp_control_point: false,
        }]);

      if (error) throw error;

      // Refresh storage areas
      const { data } = await supabase
        .from('storage_areas')
        .select('*')
        .order('name');
      
      setStorageAreas(data || []);
    } catch (err) {
      console.error('Failed to create storage area:', err);
      setError('Failed to create storage area');
    }
  };

  const getAreaTypeColor = (type: StorageArea['area_type']) => {
    switch (type) {
      case 'freezer':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'refrigerator':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'dry_storage':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'processing':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>Sensor Configuration</span>
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Sensor List */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Sensors</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {sensors.map(sensor => (
                    <button
                      key={sensor.id}
                      onClick={() => setSelectedSensorId(sensor.id)}
                      className={`w-full p-3 text-left rounded-lg border transition-colors ${
                        selectedSensorId === sensor.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:bg-gray-50'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-sm">{sensor.name}</div>
                          <div className="text-xs text-gray-600 flex items-center">
                            <MapPin className="h-3 w-3 mr-1" />
                            {sensor.location}
                          </div>
                        </div>
                        <div className="flex flex-col items-end space-y-1">
                          <div className={`w-2 h-2 rounded-full ${
                            sensor.active ? 'bg-green-500' : 'bg-red-500'
                          }`} />
                          {sensor.storage_areas && (
                            <Badge variant="outline" className="text-xs">
                              {sensor.storage_areas.name}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Configuration Panel */}
          <div className="lg:col-span-2">
            {selectedSensor ? (
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">
                    Configure {selectedSensor.name}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Tabs defaultValue="basic" className="w-full">
                    <TabsList className="grid w-full grid-cols-3">
                      <TabsTrigger value="basic">Basic Settings</TabsTrigger>
                      <TabsTrigger value="thresholds">Thresholds</TabsTrigger>
                      <TabsTrigger value="alerts">Alerts</TabsTrigger>
                    </TabsList>

                    <TabsContent value="basic" className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="sensor-name">Sensor Name</Label>
                          <Input
                            id="sensor-name"
                            value={sensorConfig.name || ''}
                            onChange={(e) => setSensorConfig(prev => ({ ...prev, name: e.target.value }))}
                            placeholder="Enter sensor name"
                          />
                        </div>

                        <div>
                          <Label htmlFor="sensor-location">Location</Label>
                          <Input
                            id="sensor-location"
                            value={sensorConfig.location || ''}
                            onChange={(e) => setSensorConfig(prev => ({ ...prev, location: e.target.value }))}
                            placeholder="Enter location"
                          />
                        </div>
                      </div>

                      <div>
                        <Label>Storage Area</Label>
                        <div className="flex space-x-2 mt-2">
                          <Select
                            value={sensorConfig.storage_area_id || ''}
                            onValueChange={(value) => setSensorConfig(prev => ({ 
                              ...prev, 
                              storage_area_id: value || null 
                            }))}
                          >
                            <SelectTrigger className="flex-1">
                              <SelectValue placeholder="Select storage area" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="">No storage area</SelectItem>
                              {storageAreas.map(area => (
                                <SelectItem key={area.id} value={area.id}>
                                  <div className="flex items-center space-x-2">
                                    <span>{area.name}</span>
                                    <Badge className={getAreaTypeColor(area.area_type)}>
                                      {area.area_type.replace('_', ' ')}
                                    </Badge>
                                    {area.haccp_control_point && (
                                      <Badge variant="secondary" className="text-xs">
                                        HACCP
                                      </Badge>
                                    )}
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={handleCreateStorageArea}
                            className="flex items-center space-x-1"
                          >
                            <Plus className="h-4 w-4" />
                            <span>Add</span>
                          </Button>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="sensor-active"
                          checked={sensorConfig.active || false}
                          onCheckedChange={(checked) => setSensorConfig(prev => ({ 
                            ...prev, 
                            active: checked as boolean 
                          }))}
                        />
                        <Label htmlFor="sensor-active">Sensor Active</Label>
                      </div>
                    </TabsContent>

                    <TabsContent value="thresholds" className="space-y-4">
                      <div>
                        <h3 className="text-sm font-medium mb-3 flex items-center space-x-2">
                          <Thermometer className="h-4 w-4" />
                          <span>Temperature Thresholds</span>
                        </h3>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="temp-min">Minimum Temperature (°F)</Label>
                            <Input
                              id="temp-min"
                              type="number"
                              step="0.1"
                              value={sensorConfig.temp_min_threshold || ''}
                              onChange={(e) => setSensorConfig(prev => ({ 
                                ...prev, 
                                temp_min_threshold: e.target.value ? parseFloat(e.target.value) : null 
                              }))}
                              placeholder="e.g., 32.0"
                            />
                          </div>

                          <div>
                            <Label htmlFor="temp-max">Maximum Temperature (°F)</Label>
                            <Input
                              id="temp-max"
                              type="number"
                              step="0.1"
                              value={sensorConfig.temp_max_threshold || ''}
                              onChange={(e) => setSensorConfig(prev => ({ 
                                ...prev, 
                                temp_max_threshold: e.target.value ? parseFloat(e.target.value) : null 
                              }))}
                              placeholder="e.g., 40.0"
                            />
                          </div>
                        </div>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium mb-3">Humidity Thresholds (Optional)</h3>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="humidity-min">Minimum Humidity (%)</Label>
                            <Input
                              id="humidity-min"
                              type="number"
                              step="1"
                              value={sensorConfig.humidity_min_threshold || ''}
                              onChange={(e) => setSensorConfig(prev => ({ 
                                ...prev, 
                                humidity_min_threshold: e.target.value ? parseFloat(e.target.value) : null 
                              }))}
                              placeholder="e.g., 40"
                            />
                          </div>

                          <div>
                            <Label htmlFor="humidity-max">Maximum Humidity (%)</Label>
                            <Input
                              id="humidity-max"
                              type="number"
                              step="1"
                              value={sensorConfig.humidity_max_threshold || ''}
                              onChange={(e) => setSensorConfig(prev => ({ 
                                ...prev, 
                                humidity_max_threshold: e.target.value ? parseFloat(e.target.value) : null 
                              }))}
                              placeholder="e.g., 80"
                            />
                          </div>
                        </div>
                      </div>

                      {/* Storage Area Requirements Display */}
                      {selectedSensor?.storage_areas && (
                        <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                          <h4 className="text-sm font-medium text-blue-900 mb-2">
                            Storage Area Requirements
                          </h4>
                          <div className="text-sm text-blue-800">
                            <div>Area: {selectedSensor.storage_areas.name}</div>
                            {selectedSensor.storage_areas.required_temp_min && (
                              <div>Min Temp: {selectedSensor.storage_areas.required_temp_min}°F</div>
                            )}
                            {selectedSensor.storage_areas.required_temp_max && (
                              <div>Max Temp: {selectedSensor.storage_areas.required_temp_max}°F</div>
                            )}
                            {selectedSensor.storage_areas.haccp_control_point && (
                              <Badge variant="secondary" className="mt-2">
                                HACCP Critical Control Point
                              </Badge>
                            )}
                          </div>
                        </div>
                      )}
                    </TabsContent>

                    <TabsContent value="alerts" className="space-y-4">
                      <div className="text-center py-6">
                        <AlertTriangle className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                        <p className="text-gray-600">Alert configuration coming soon</p>
                        <p className="text-sm text-gray-500">
                          Configure email notifications, escalation rules, and custom alert conditions
                        </p>
                      </div>
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <Settings className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Select a Sensor</h3>
                  <p className="text-gray-600">
                    Choose a sensor from the list to configure its settings and thresholds.
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
            {error}
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            <X className="h-4 w-4 mr-1" />
            Cancel
          </Button>
          {selectedSensor && (
            <Button 
              onClick={handleSaveSensorConfig}
              disabled={saving}
              className="flex items-center space-x-1"
            >
              <Save className="h-4 w-4" />
              <span>{saving ? 'Saving...' : 'Save Configuration'}</span>
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}