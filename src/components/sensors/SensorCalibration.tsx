/**
 * Sensor Calibration Component
 * 
 * Calibration tracking and maintenance scheduling with:
 * - Calibration history tracking
 * - Maintenance reminder system
 * - Calibration wizard workflow
 * - Performance monitoring
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Calendar, 
  Clock, 
  Wrench, 
  AlertTriangle, 
  CheckCircle, 
  TrendingUp,
  FileText,
  Download,
  Plus
} from 'lucide-react';
import { supabase } from '@/lib/supabase';
import type { Sensor } from '@/types/tempstick';

interface SensorCalibrationProps {
  className?: string;
}

interface CalibrationRecord {
  id: string;
  sensor_id: string;
  calibration_date: string;
  calibrated_by: string;
  reference_temperature: number;
  sensor_reading: number;
  offset_applied: number;
  notes: string;
  created_at: string;
}

interface MaintenanceSchedule {
  sensor_id: string;
  sensor_name: string;
  last_calibration: string | null;
  next_calibration_due: string | null;
  days_overdue: number;
  status: 'current' | 'due_soon' | 'overdue' | 'never_calibrated';
}

export const SensorCalibration: React.FC<SensorCalibrationProps> = ({ className = '' }) => {
  // State management
  const [sensors, setSensors] = useState<Sensor[]>([]);
  const [calibrationRecords, setCalibrationRecords] = useState<CalibrationRecord[]>([]);
  const [maintenanceSchedule, setMaintenanceSchedule] = useState<MaintenanceSchedule[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedSensor, setSelectedSensor] = useState<Sensor | null>(null);
  const [showCalibrationDialog, setShowCalibrationDialog] = useState(false);

  // Calibration form state
  const [calibrationForm, setCalibrationForm] = useState({
    reference_temperature: '',
    sensor_reading: '',
    notes: '',
    calibration_date: new Date().toISOString().split('T')[0]
  });

  /**
   * Load sensors and calibration data
   */
  const loadData = useCallback(async () => {
    try {
      setError(null);
      
      // Load sensors
      const { data: sensorsData, error: sensorsError } = await supabase
        .from('sensors')
        .select('*')
        .eq('is_active', true)
        .order('name');

      if (sensorsError) {
        throw new Error(`Failed to load sensors: ${sensorsError.message}`);
      }

      setSensors(sensorsData || []);

      // Load calibration records
      const { data: calibrationData, error: calibrationError } = await supabase
        .from('sensor_calibrations')
        .select('*')
        .order('calibration_date', { ascending: false });

      if (calibrationError && calibrationError.code !== 'PGRST116') {
        console.warn('Calibration table may not exist:', calibrationError.message);
        setCalibrationRecords([]);
      } else {
        setCalibrationRecords(calibrationData || []);
      }

      // Calculate maintenance schedule
      const schedule = calculateMaintenanceSchedule(sensorsData || [], calibrationData || []);
      setMaintenanceSchedule(schedule);

    } catch (err) {
      console.error('Failed to load calibration data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load calibration data');
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Calculate maintenance schedule for all sensors
   */
  const calculateMaintenanceSchedule = (
    sensors: Sensor[], 
    calibrations: CalibrationRecord[]
  ): MaintenanceSchedule[] => {
    return sensors.map(sensor => {
      const sensorCalibrations = calibrations.filter(c => c.sensor_id === sensor.id);
      const lastCalibration = sensorCalibrations[0]; // Most recent first
      
      let status: MaintenanceSchedule['status'] = 'never_calibrated';
      let daysOverdue = 0;
      
      if (lastCalibration) {
        const lastCalibrationDate = new Date(lastCalibration.calibration_date);
        const now = new Date();
        const daysSinceCalibration = Math.floor((now.getTime() - lastCalibrationDate.getTime()) / (1000 * 60 * 60 * 24));
        
        // Assume 90-day calibration cycle
        const calibrationIntervalDays = 90;
        daysOverdue = daysSinceCalibration - calibrationIntervalDays;
        
        if (daysOverdue > 0) {
          status = 'overdue';
        } else if (daysOverdue > -7) { // Due within 7 days
          status = 'due_soon';
        } else {
          status = 'current';
        }
      }
      
      return {
        sensor_id: sensor.id,
        sensor_name: sensor.name,
        last_calibration: lastCalibration?.calibration_date || null,
        next_calibration_due: lastCalibration 
          ? new Date(new Date(lastCalibration.calibration_date).getTime() + 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
          : null,
        days_overdue: Math.max(0, daysOverdue),
        status
      };
    });
  };

  /**
   * Open calibration dialog for sensor
   */
  const openCalibrationDialog = useCallback((sensor: Sensor) => {
    setSelectedSensor(sensor);
    setCalibrationForm({
      reference_temperature: '',
      sensor_reading: '',
      notes: '',
      calibration_date: new Date().toISOString().split('T')[0]
    });
    setShowCalibrationDialog(true);
  }, []);

  /**
   * Save calibration record
   */
  const saveCalibration = useCallback(async () => {
    if (!selectedSensor) return;

    try {
      const referenceTemp = parseFloat(calibrationForm.reference_temperature);
      const sensorReading = parseFloat(calibrationForm.sensor_reading);
      
      if (isNaN(referenceTemp) || isNaN(sensorReading)) {
        setError('Please enter valid temperature values');
        return;
      }

      const offsetApplied = referenceTemp - sensorReading;

      // Create calibration record
      const calibrationRecord = {
        sensor_id: selectedSensor.id,
        calibration_date: calibrationForm.calibration_date,
        reference_temperature: referenceTemp,
        sensor_reading: sensorReading,
        offset_applied: offsetApplied,
        notes: calibrationForm.notes,
        calibrated_by: 'current_user' // Would be actual user ID in production
      };

      // Try to insert calibration record (table may not exist)
      try {
        const { error: calibrationError } = await supabase
          .from('sensor_calibrations')
          .insert([calibrationRecord]);

        if (calibrationError) {
          console.warn('Could not save to calibration table:', calibrationError.message);
        }
      } catch (err) {
        console.warn('Calibration table may not exist, updating sensor directly');
      }

      // Update sensor with calibration info
      const { error: sensorError } = await supabase
        .from('sensors')
        .update({
          calibration_date: calibrationForm.calibration_date,
          temp_offset_celsius: offsetApplied * 5/9, // Convert F to C offset
          temp_offset_fahrenheit: offsetApplied,
          last_calibrated_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', selectedSensor.id);

      if (sensorError) {
        throw new Error(`Failed to update sensor: ${sensorError.message}`);
      }

      setShowCalibrationDialog(false);
      setSelectedSensor(null);
      await loadData();

    } catch (err) {
      console.error('Failed to save calibration:', err);
      setError(err instanceof Error ? err.message : 'Failed to save calibration');
    }
  }, [selectedSensor, calibrationForm, loadData]);

  /**
   * Get status styling for maintenance schedule
   */
  const getStatusStyling = (status: MaintenanceSchedule['status']) => {
    switch (status) {
      case 'current':
        return { color: 'bg-green-100 text-green-800', icon: <CheckCircle className="h-4 w-4" /> };
      case 'due_soon':
        return { color: 'bg-yellow-100 text-yellow-800', icon: <Clock className="h-4 w-4" /> };
      case 'overdue':
        return { color: 'bg-red-100 text-red-800', icon: <AlertTriangle className="h-4 w-4" /> };
      case 'never_calibrated':
        return { color: 'bg-gray-100 text-gray-800', icon: <Wrench className="h-4 w-4" /> };
      default:
        return { color: 'bg-gray-100 text-gray-800', icon: <Wrench className="h-4 w-4" /> };
    }
  };

  /**
   * Get status text
   */
  const getStatusText = (schedule: MaintenanceSchedule) => {
    switch (schedule.status) {
      case 'current':
        return 'Current';
      case 'due_soon':
        return 'Due Soon';
      case 'overdue':
        return `${schedule.days_overdue} days overdue`;
      case 'never_calibrated':
        return 'Never Calibrated';
      default:
        return 'Unknown';
    }
  };

  // Load data on mount
  useEffect(() => {
    loadData();
  }, [loadData]);

  if (loading) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="flex items-center space-x-2">
          <Wrench className="h-4 w-4 animate-spin" />
          <span>Loading calibration data...</span>
        </div>
      </div>
    );
  }

  const overdueCount = maintenanceSchedule.filter(s => s.status === 'overdue').length;
  const dueSoonCount = maintenanceSchedule.filter(s => s.status === 'due_soon').length;
  const neverCalibratedCount = maintenanceSchedule.filter(s => s.status === 'never_calibrated').length;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">Sensor Calibration</h1>
          <p className="text-muted-foreground">
            Track calibration schedules and maintain sensor accuracy
          </p>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Sensors</p>
                <p className="text-2xl font-bold">{sensors.length}</p>
              </div>
              <Wrench className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Overdue</p>
                <p className="text-2xl font-bold text-red-600">{overdueCount}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Due Soon</p>
                <p className="text-2xl font-bold text-yellow-600">{dueSoonCount}</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Never Calibrated</p>
                <p className="text-2xl font-bold text-gray-600">{neverCalibratedCount}</p>
              </div>
              <Wrench className="h-8 w-8 text-gray-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Maintenance Schedule */}
      <Card>
        <CardHeader>
          <CardTitle>Calibration Schedule</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {maintenanceSchedule.map((schedule) => {
              const styling = getStatusStyling(schedule.status);
              const sensor = sensors.find(s => s.id === schedule.sensor_id);
              
              return (
                <div key={schedule.sensor_id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div>
                      <h4 className="font-medium">{schedule.sensor_name}</h4>
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                        {schedule.last_calibration && (
                          <span>Last: {new Date(schedule.last_calibration).toLocaleDateString()}</span>
                        )}
                        {schedule.next_calibration_due && (
                          <span>Next: {new Date(schedule.next_calibration_due).toLocaleDateString()}</span>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <Badge className={`${styling.color} flex items-center gap-1`}>
                      {styling.icon}
                      <span>{getStatusText(schedule)}</span>
                    </Badge>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => sensor && openCalibrationDialog(sensor)}
                    >
                      <Wrench className="h-4 w-4 mr-2" />
                      Calibrate
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Calibration History */}
      {calibrationRecords.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Recent Calibrations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {calibrationRecords.slice(0, 10).map((record) => {
                const sensor = sensors.find(s => s.id === record.sensor_id);
                
                return (
                  <div key={record.id} className="flex items-center justify-between p-3 border rounded">
                    <div>
                      <h5 className="font-medium">{sensor?.name || 'Unknown Sensor'}</h5>
                      <p className="text-sm text-muted-foreground">
                        {new Date(record.calibration_date).toLocaleDateString()} - 
                        Offset: {record.offset_applied.toFixed(2)}°F
                      </p>
                    </div>
                    
                    <div className="text-right text-sm">
                      <div>Ref: {record.reference_temperature}°F</div>
                      <div>Reading: {record.sensor_reading}°F</div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Calibration Dialog */}
      <Dialog open={showCalibrationDialog} onOpenChange={setShowCalibrationDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>
              Calibrate Sensor: {selectedSensor?.name}
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="calibration-date">Calibration Date</Label>
              <Input
                id="calibration-date"
                type="date"
                value={calibrationForm.calibration_date}
                onChange={(e) => setCalibrationForm(prev => ({ 
                  ...prev, 
                  calibration_date: e.target.value 
                }))}
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="reference-temp">Reference Temperature (°F)</Label>
                <Input
                  id="reference-temp"
                  type="number"
                  step="0.1"
                  value={calibrationForm.reference_temperature}
                  onChange={(e) => setCalibrationForm(prev => ({ 
                    ...prev, 
                    reference_temperature: e.target.value 
                  }))}
                  placeholder="e.g., 32.0"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="sensor-reading">Sensor Reading (°F)</Label>
                <Input
                  id="sensor-reading"
                  type="number"
                  step="0.1"
                  value={calibrationForm.sensor_reading}
                  onChange={(e) => setCalibrationForm(prev => ({ 
                    ...prev, 
                    sensor_reading: e.target.value 
                  }))}
                  placeholder="e.g., 31.8"
                />
              </div>
            </div>
            
            {calibrationForm.reference_temperature && calibrationForm.sensor_reading && (
              <div className="p-3 bg-blue-50 rounded-lg">
                <p className="text-sm font-medium">Calculated Offset:</p>
                <p className="text-lg font-bold text-blue-600">
                  {(parseFloat(calibrationForm.reference_temperature) - parseFloat(calibrationForm.sensor_reading)).toFixed(2)}°F
                </p>
              </div>
            )}
            
            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                value={calibrationForm.notes}
                onChange={(e) => setCalibrationForm(prev => ({ 
                  ...prev, 
                  notes: e.target.value 
                }))}
                placeholder="Enter calibration notes, reference standard used, etc."
                rows={3}
              />
            </div>
          </div>
          
          <div className="flex justify-end space-x-2 pt-4">
            <Button variant="outline" onClick={() => setShowCalibrationDialog(false)}>
              Cancel
            </Button>
            <Button onClick={saveCalibration}>
              Save Calibration
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SensorCalibration;