/**
 * Temperature Heat Map Component
 * 
 * Advanced data visualization component that provides:
 * - Temperature heat maps for multiple sensors over time
 * - Color-coded temperature ranges for easy visual analysis
 * - Interactive tooltips showing detailed temperature data
 * - Time-based aggregation (hourly, daily, weekly views)
 * - Sensor comparison heat maps
 * - HACCP compliance visualization
 * - Export functionality for heat map data
 */

import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Calendar,
  Download,
  Settings,
  Thermometer,
  Clock,
  BarChart3,
  Grid,
  RefreshCw,
  AlertTriangle,
  TrendingUp,
  Eye,
  Palette
} from 'lucide-react';
import { format, subDays, subHours, startOfHour, startOfDay, startOfWeek, eachHourOfInterval, eachDayOfInterval, eachWeekOfInterval } from 'date-fns';
import { tempStickService } from '@/lib/tempstick-service';
import type { 
  TempStickReading,
  Sensor,
  TimeRange 
} from '@/types/tempstick';

// Heat map data structures
interface HeatMapCell {
  x: number; // time bucket index
  y: number; // sensor index
  value: number; // temperature value
  temperature: number;
  timestamp: Date;
  sensorId: string;
  sensorName: string;
  alertLevel: 'normal' | 'warning' | 'critical';
  complianceStatus: 'compliant' | 'violation' | 'unknown';
  humidity?: number;
  readingsCount: number;
}

interface HeatMapData {
  cells: HeatMapCell[];
  xLabels: string[]; // time labels
  yLabels: string[]; // sensor labels
  temperatureRange: {
    min: number;
    max: number;
  };
  timeRange: {
    start: Date;
    end: Date;
  };
}

interface HeatMapProps {
  className?: string;
  initialTimeRange?: TimeRange;
  sensors?: Sensor[];
  showComplianceMode?: boolean;
  aggregationLevel?: 'hour' | 'day' | 'week';
  colorScheme?: 'temperature' | 'compliance' | 'alerts';
}

export const TemperatureHeatMap: React.FC<HeatMapProps> = ({
  className = '',
  initialTimeRange = '24h',
  sensors = [],
  showComplianceMode = false,
  aggregationLevel: initialAggregation = 'hour',
  colorScheme: initialColorScheme = 'temperature'
}) => {
  // State management
  const [heatMapData, setHeatMapData] = useState<HeatMapData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState<TimeRange>(initialTimeRange);
  const [selectedSensors, setSelectedSensors] = useState<string[]>([]);
  const [aggregationLevel, setAggregationLevel] = useState<'hour' | 'day' | 'week'>(initialAggregation);
  const [colorScheme, setColorScheme] = useState<'temperature' | 'compliance' | 'alerts'>(initialColorScheme);
  const [showHumidity, setShowHumidity] = useState(false);
  const [showFahrenheit, setShowFahrenheit] = useState(true);
  const [hoveredCell, setHoveredCell] = useState<HeatMapCell | null>(null);
  const [availableSensors, setAvailableSensors] = useState<Sensor[]>([]);

  /**
   * Fetch available sensors
   */
  const fetchSensors = useCallback(async () => {
    try {
      const sensorData = await tempStickService.getAllSensors();
      const formattedSensors: Sensor[] = sensorData.map(sensor => ({
        id: sensor.sensor_id,
        tempstick_sensor_id: sensor.sensor_id,
        name: sensor.sensor_name,
        location: sensor.ssid || 'Unknown Location',
        sensor_type: 'temperature_humidity' as const,
        temp_min_threshold: null,
        temp_max_threshold: null,
        humidity_min_threshold: null,
        humidity_max_threshold: null,
        storage_area_id: null,
        active: sensor.offline !== '1',
        created_at: new Date().toISOString(),
      }));
      
      setAvailableSensors(formattedSensors);
      
      // Select all sensors by default if none selected
      if (selectedSensors.length === 0) {
        setSelectedSensors(formattedSensors.map(s => s.id));
      }
    } catch (err) {
      console.error('Failed to fetch sensors:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch sensors');
    }
  }, [selectedSensors.length]);

  /**
   * Generate time buckets based on aggregation level
   */
  const generateTimeBuckets = useCallback((startDate: Date, endDate: Date, level: 'hour' | 'day' | 'week'): Date[] => {
    switch (level) {
      case 'hour':
        return eachHourOfInterval({ start: startOfHour(startDate), end: endDate });
      case 'day':
        return eachDayOfInterval({ start: startOfDay(startDate), end: endDate });
      case 'week':
        return eachWeekOfInterval({ start: startOfWeek(startDate), end: endDate });
      default:
        return eachHourOfInterval({ start: startOfHour(startDate), end: endDate });
    }
  }, []);

  /**
   * Get alert level based on temperature thresholds
   */
  const getAlertLevel = useCallback((temperature: number): 'normal' | 'warning' | 'critical' => {
    // Basic thresholds - in production these would come from sensor configuration
    if (temperature < 32 || temperature > 40) {
      return 'critical';
    } else if (temperature < 35 || temperature > 38) {
      return 'warning';
    }
    return 'normal';
  }, []);

  /**
   * Get compliance status based on temperature and HACCP requirements
   */
  const getComplianceStatus = useCallback((temperature: number, sensorId: string): 'compliant' | 'violation' | 'unknown' => {
    // In production, this would check against specific HACCP requirements for each sensor
    const alertLevel = getAlertLevel(temperature);
    if (alertLevel === 'critical') return 'violation';
    if (alertLevel === 'warning') return 'violation';
    return 'compliant';
  }, [getAlertLevel]);

  /**
   * Aggregate readings into time buckets
   */
  const aggregateReadings = useCallback((
    readings: TempStickReading[],
    timeBuckets: Date[],
    level: 'hour' | 'day' | 'week'
  ) => {
    const bucketMap = new Map<string, TempStickReading[]>();

    // Initialize buckets
    timeBuckets.forEach((bucket, index) => {
      bucketMap.set(`${index}`, []);
    });

    // Assign readings to buckets
    readings.forEach(reading => {
      const readingDate = new Date(reading.timestamp);
      let bucketIndex = -1;

      // Find the appropriate bucket
      for (let i = 0; i < timeBuckets.length - 1; i++) {
        if (readingDate >= timeBuckets[i] && readingDate < timeBuckets[i + 1]) {
          bucketIndex = i;
          break;
        }
      }

      // Handle last bucket
      if (bucketIndex === -1 && readingDate >= timeBuckets[timeBuckets.length - 1]) {
        bucketIndex = timeBuckets.length - 1;
      }

      if (bucketIndex >= 0) {
        const key = `${bucketIndex}`;
        const bucket = bucketMap.get(key) || [];
        bucket.push(reading);
        bucketMap.set(key, bucket);
      }
    });

    return bucketMap;
  }, []);

  /**
   * Fetch and process heat map data
   */
  const fetchHeatMapData = useCallback(async () => {
    if (selectedSensors.length === 0) {
      setHeatMapData(null);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Calculate date range
      const endDate = new Date();
      let startDate: Date;

      switch (timeRange) {
        case '1h':
          startDate = subHours(endDate, 1);
          break;
        case '6h':
          startDate = subHours(endDate, 6);
          break;
        case '24h':
          startDate = subDays(endDate, 1);
          break;
        case '7d':
          startDate = subDays(endDate, 7);
          break;
        case '30d':
          startDate = subDays(endDate, 30);
          break;
        default:
          startDate = subDays(endDate, 1);
      }

      // Generate time buckets
      const timeBuckets = generateTimeBuckets(startDate, endDate, aggregationLevel);
      
      // Fetch data for all selected sensors
      const allReadings: Array<{ sensorId: string; readings: TempStickReading[] }> = [];
      
      for (const sensorId of selectedSensors) {
        try {
          const readings = await tempStickService.getLatestReadings(sensorId, 1000);
          
          // Filter readings by date range
          const filteredReadings = readings.filter(reading => {
            const readingDate = new Date(reading.timestamp);
            return readingDate >= startDate && readingDate <= endDate;
          });
          
          allReadings.push({ sensorId, readings: filteredReadings });
        } catch (err) {
          console.warn(`Failed to fetch data for sensor ${sensorId}:`, err);
        }
      }

      // Process data into heat map format
      const cells: HeatMapCell[] = [];
      let minTemp = Infinity;
      let maxTemp = -Infinity;

      allReadings.forEach(({ sensorId, readings }, sensorIndex) => {
        const sensor = availableSensors.find(s => s.id === sensorId);
        if (!sensor) return;

        // Aggregate readings into time buckets
        const bucketMap = aggregateReadings(readings, timeBuckets, aggregationLevel);

        // Create heat map cells
        timeBuckets.forEach((bucket, timeIndex) => {
          const bucketReadings = bucketMap.get(`${timeIndex}`) || [];
          
          if (bucketReadings.length > 0) {
            // Calculate average temperature for this bucket
            const avgTemp = bucketReadings.reduce((sum, r) => sum + r.temperature, 0) / bucketReadings.length;
            const avgHumidity = bucketReadings.filter(r => r.humidity !== undefined).length > 0
              ? bucketReadings.filter(r => r.humidity !== undefined).reduce((sum, r) => sum + (r.humidity || 0), 0) / bucketReadings.filter(r => r.humidity !== undefined).length
              : undefined;

            const temperature = showFahrenheit ? (avgTemp * 9/5) + 32 : avgTemp;
            
            minTemp = Math.min(minTemp, temperature);
            maxTemp = Math.max(maxTemp, temperature);

            cells.push({
              x: timeIndex,
              y: sensorIndex,
              value: temperature,
              temperature,
              timestamp: bucket,
              sensorId,
              sensorName: sensor.name,
              alertLevel: getAlertLevel(avgTemp),
              complianceStatus: getComplianceStatus(avgTemp, sensorId),
              humidity: avgHumidity,
              readingsCount: bucketReadings.length
            });
          } else {
            // No data for this time bucket
            cells.push({
              x: timeIndex,
              y: sensorIndex,
              value: NaN,
              temperature: NaN,
              timestamp: bucket,
              sensorId,
              sensorName: sensor.name,
              alertLevel: 'normal',
              complianceStatus: 'unknown',
              readingsCount: 0
            });
          }
        });
      });

      // Generate labels
      const xLabels = timeBuckets.map(bucket => {
        switch (aggregationLevel) {
          case 'hour':
            return format(bucket, 'HH:mm');
          case 'day':
            return format(bucket, 'MMM dd');
          case 'week':
            return format(bucket, 'MMM dd');
          default:
            return format(bucket, 'HH:mm');
        }
      });

      const yLabels = selectedSensors.map(sensorId => {
        const sensor = availableSensors.find(s => s.id === sensorId);
        return sensor ? sensor.name : sensorId;
      });

      setHeatMapData({
        cells,
        xLabels,
        yLabels,
        temperatureRange: { min: minTemp, max: maxTemp },
        timeRange: { start: startDate, end: endDate }
      });

    } catch (err) {
      console.error('Failed to fetch heat map data:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch heat map data');
    } finally {
      setLoading(false);
    }
  }, [selectedSensors, timeRange, aggregationLevel, showFahrenheit, availableSensors, generateTimeBuckets, aggregateReadings, getAlertLevel, getComplianceStatus]);

  /**
   * Get color for heat map cell based on color scheme
   */
  const getCellColor = useCallback((cell: HeatMapCell): string => {
    if (isNaN(cell.value) || cell.readingsCount === 0) {
      return '#f3f4f6'; // Gray for no data
    }

    switch (colorScheme) {
      case 'temperature':
        return getTemperatureColor(cell.temperature, heatMapData?.temperatureRange);
      case 'compliance':
        return getComplianceColor(cell.complianceStatus);
      case 'alerts':
        return getAlertColor(cell.alertLevel);
      default:
        return getTemperatureColor(cell.temperature, heatMapData?.temperatureRange);
    }
  }, [colorScheme, heatMapData?.temperatureRange]);

  /**
   * Get temperature-based color
   */
  const getTemperatureColor = (temperature: number, range?: { min: number; max: number }): string => {
    if (!range || isNaN(temperature)) return '#f3f4f6';
    
    const ratio = (temperature - range.min) / (range.max - range.min);
    const clampedRatio = Math.max(0, Math.min(1, ratio));
    
    // Blue to red gradient
    const blue = Math.round(255 * (1 - clampedRatio));
    const red = Math.round(255 * clampedRatio);
    const green = Math.round(255 * (1 - Math.abs(clampedRatio - 0.5) * 2));
    
    return `rgb(${red}, ${green}, ${blue})`;
  };

  /**
   * Get compliance-based color
   */
  const getComplianceColor = (status: 'compliant' | 'violation' | 'unknown'): string => {
    switch (status) {
      case 'compliant':
        return '#16a34a'; // Green
      case 'violation':
        return '#dc2626'; // Red
      case 'unknown':
        return '#9ca3af'; // Gray
      default:
        return '#f3f4f6';
    }
  };

  /**
   * Get alert-based color
   */
  const getAlertColor = (level: 'normal' | 'warning' | 'critical'): string => {
    switch (level) {
      case 'normal':
        return '#16a34a'; // Green
      case 'warning':
        return '#f59e0b'; // Yellow
      case 'critical':
        return '#dc2626'; // Red
      default:
        return '#f3f4f6';
    }
  };

  /**
   * Export heat map data
   */
  const handleExport = useCallback(async () => {
    if (!heatMapData) return;

    const csvContent = generateHeatMapCSV(heatMapData);
    downloadFile(csvContent, 'temperature-heatmap.csv', 'text/csv');
  }, [heatMapData]);

  /**
   * Generate CSV content for heat map
   */
  const generateHeatMapCSV = (data: HeatMapData): string => {
    const headers = ['Time', 'Sensor', 'Temperature', 'Alert Level', 'Compliance Status', 'Readings Count', 'Humidity'];
    
    const rows = data.cells
      .filter(cell => !isNaN(cell.value))
      .map(cell => [
        format(cell.timestamp, 'yyyy-MM-dd HH:mm:ss'),
        cell.sensorName,
        cell.temperature.toFixed(1),
        cell.alertLevel,
        cell.complianceStatus,
        cell.readingsCount.toString(),
        cell.humidity?.toFixed(1) || ''
      ]);
    
    return [headers, ...rows]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');
  };

  /**
   * Download file utility
   */
  const downloadFile = (content: string, filename: string, mimeType: string) => {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    URL.revokeObjectURL(url);
  };

  // Effects
  useEffect(() => {
    fetchSensors();
  }, [fetchSensors]);

  useEffect(() => {
    if (availableSensors.length > 0) {
      fetchHeatMapData();
    }
  }, [fetchHeatMapData, availableSensors.length]);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">Temperature Heat Map</h1>
          <p className="text-muted-foreground">
            Visual analysis of temperature patterns across sensors and time
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={fetchHeatMapData}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleExport}
            disabled={!heatMapData || loading}
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Controls */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium flex items-center">
            <Settings className="h-4 w-4 mr-2" />
            Heat Map Controls
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {/* Time Range */}
            <div className="space-y-2">
              <Label>Time Range</Label>
              <Select
                value={timeRange}
                onValueChange={(value: TimeRange) => setTimeRange(value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1h">Last Hour</SelectItem>
                  <SelectItem value="6h">Last 6 Hours</SelectItem>
                  <SelectItem value="24h">Last 24 Hours</SelectItem>
                  <SelectItem value="7d">Last 7 Days</SelectItem>
                  <SelectItem value="30d">Last 30 Days</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Aggregation Level */}
            <div className="space-y-2">
              <Label>Aggregation</Label>
              <Select
                value={aggregationLevel}
                onValueChange={(value: 'hour' | 'day' | 'week') => setAggregationLevel(value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="hour">Hourly</SelectItem>
                  <SelectItem value="day">Daily</SelectItem>
                  <SelectItem value="week">Weekly</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Color Scheme */}
            <div className="space-y-2">
              <Label>Color Scheme</Label>
              <Select
                value={colorScheme}
                onValueChange={(value: 'temperature' | 'compliance' | 'alerts') => setColorScheme(value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="temperature">Temperature</SelectItem>
                  <SelectItem value="compliance">HACCP Compliance</SelectItem>
                  <SelectItem value="alerts">Alert Levels</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Sensors Selection */}
            <div className="space-y-2">
              <Label>Sensors</Label>
              <Select
                value={selectedSensors.length === 1 ? selectedSensors[0] : 'multiple'}
                onValueChange={(value) => {
                  if (value === 'all') {
                    setSelectedSensors(availableSensors.map(s => s.id));
                  } else if (value !== 'multiple') {
                    setSelectedSensors([value]);
                  }
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder={`${selectedSensors.length} selected`} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Sensors</SelectItem>
                  {availableSensors.map((sensor) => (
                    <SelectItem key={sensor.id} value={sensor.id}>
                      {sensor.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Display Options */}
            <div className="space-y-2">
              <Label>Display Options</Label>
              <div className="flex flex-col space-y-2">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="fahrenheit"
                    checked={showFahrenheit}
                    onCheckedChange={setShowFahrenheit}
                  />
                  <Label htmlFor="fahrenheit" className="text-sm">°F</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="humidity"
                    checked={showHumidity}
                    onCheckedChange={setShowHumidity}
                  />
                  <Label htmlFor="humidity" className="text-sm">Humidity</Label>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Heat Map Visualization */}
      {heatMapData && (
        <Card>
          <CardHeader>
            <CardTitle>Temperature Heat Map</CardTitle>
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <span>
                {format(heatMapData.timeRange.start, 'MMM dd, yyyy HH:mm')} - {format(heatMapData.timeRange.end, 'MMM dd, yyyy HH:mm')}
              </span>
              <span>
                Range: {heatMapData.temperatureRange.min.toFixed(1)}° - {heatMapData.temperatureRange.max.toFixed(1)}°{showFahrenheit ? 'F' : 'C'}
              </span>
            </div>
          </CardHeader>
          <CardContent>
            <div className="relative">
              {/* Heat Map Grid */}
              <div className="overflow-x-auto">
                <div className="inline-block min-w-full">
                  {/* Y-axis Labels (Sensors) */}
                  <div className="flex">
                    <div className="w-32 flex-shrink-0">
                      <div className="h-8"></div> {/* Space for x-axis labels */}
                      {heatMapData.yLabels.map((label, index) => (
                        <div
                          key={index}
                          className="h-8 flex items-center justify-end pr-2 text-xs font-medium truncate"
                        >
                          {label}
                        </div>
                      ))}
                    </div>
                    
                    {/* Heat Map Cells */}
                    <div className="flex-1">
                      {/* X-axis Labels (Time) */}
                      <div className="flex h-8">
                        {heatMapData.xLabels.map((label, index) => (
                          <div
                            key={index}
                            className="w-16 flex items-center justify-center text-xs font-medium"
                            style={{ writingMode: 'vertical-rl', textOrientation: 'mixed' }}
                          >
                            {label}
                          </div>
                        ))}
                      </div>
                      
                      {/* Heat Map Rows */}
                      {heatMapData.yLabels.map((sensorLabel, sensorIndex) => (
                        <div key={sensorIndex} className="flex h-8">
                          {heatMapData.xLabels.map((timeLabel, timeIndex) => {
                            const cell = heatMapData.cells.find(c => c.x === timeIndex && c.y === sensorIndex);
                            if (!cell) return <div key={timeIndex} className="w-16 h-8 border border-gray-200"></div>;
                            
                            return (
                              <div
                                key={`${timeIndex}-${sensorIndex}`}
                                className="w-16 h-8 border border-gray-200 cursor-pointer transition-all hover:scale-110 hover:z-10 relative"
                                style={{ backgroundColor: getCellColor(cell) }}
                                onMouseEnter={() => setHoveredCell(cell)}
                                onMouseLeave={() => setHoveredCell(null)}
                                title={`${cell.sensorName}\n${format(cell.timestamp, 'MMM dd, HH:mm')}\nTemp: ${isNaN(cell.temperature) ? 'No data' : `${cell.temperature.toFixed(1)}°${showFahrenheit ? 'F' : 'C'}`}\nReadings: ${cell.readingsCount}`}
                              >
                                {/* Cell content for better visualization */}
                                {!isNaN(cell.temperature) && cell.readingsCount > 0 && (
                                  <div className="w-full h-full flex items-center justify-center">
                                    {cell.alertLevel === 'critical' && (
                                      <AlertTriangle className="h-3 w-3 text-white" />
                                    )}
                                    {cell.alertLevel === 'warning' && (
                                      <div className="w-1 h-1 rounded-full bg-white"></div>
                                    )}
                                  </div>
                                )}
                              </div>
                            );
                          })}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Color Legend */}
              <div className="mt-4 flex items-center justify-center">
                <div className="flex items-center space-x-4">
                  <span className="text-sm font-medium">
                    {colorScheme === 'temperature' && 'Temperature'}
                    {colorScheme === 'compliance' && 'HACCP Compliance'}
                    {colorScheme === 'alerts' && 'Alert Levels'}
                  </span>
                  
                  {colorScheme === 'temperature' && heatMapData.temperatureRange && (
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4" style={{ backgroundColor: getTemperatureColor(heatMapData.temperatureRange.min, heatMapData.temperatureRange) }}></div>
                      <span className="text-xs">{heatMapData.temperatureRange.min.toFixed(1)}°</span>
                      <div className="w-16 h-4 flex">
                        {Array.from({ length: 8 }).map((_, i) => {
                          const ratio = i / 7;
                          const temp = heatMapData.temperatureRange.min + ratio * (heatMapData.temperatureRange.max - heatMapData.temperatureRange.min);
                          return (
                            <div
                              key={i}
                              className="flex-1 h-full"
                              style={{ backgroundColor: getTemperatureColor(temp, heatMapData.temperatureRange) }}
                            ></div>
                          );
                        })}
                      </div>
                      <span className="text-xs">{heatMapData.temperatureRange.max.toFixed(1)}°</span>
                      <div className="w-4 h-4" style={{ backgroundColor: getTemperatureColor(heatMapData.temperatureRange.max, heatMapData.temperatureRange) }}></div>
                    </div>
                  )}
                  
                  {colorScheme === 'compliance' && (
                    <div className="flex items-center space-x-2">
                      <div className="flex items-center space-x-1">
                        <div className="w-4 h-4" style={{ backgroundColor: getComplianceColor('compliant') }}></div>
                        <span className="text-xs">Compliant</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <div className="w-4 h-4" style={{ backgroundColor: getComplianceColor('violation') }}></div>
                        <span className="text-xs">Violation</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <div className="w-4 h-4" style={{ backgroundColor: getComplianceColor('unknown') }}></div>
                        <span className="text-xs">Unknown</span>
                      </div>
                    </div>
                  )}
                  
                  {colorScheme === 'alerts' && (
                    <div className="flex items-center space-x-2">
                      <div className="flex items-center space-x-1">
                        <div className="w-4 h-4" style={{ backgroundColor: getAlertColor('normal') }}></div>
                        <span className="text-xs">Normal</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <div className="w-4 h-4" style={{ backgroundColor: getAlertColor('warning') }}></div>
                        <span className="text-xs">Warning</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <div className="w-4 h-4" style={{ backgroundColor: getAlertColor('critical') }}></div>
                        <span className="text-xs">Critical</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Hover Tooltip */}
            {hoveredCell && (
              <div className="absolute top-0 right-0 bg-black text-white p-2 rounded shadow-lg z-50 text-xs">
                <div><strong>{hoveredCell.sensorName}</strong></div>
                <div>{format(hoveredCell.timestamp, 'MMM dd, yyyy HH:mm')}</div>
                <div>Temperature: {isNaN(hoveredCell.temperature) ? 'No data' : `${hoveredCell.temperature.toFixed(1)}°${showFahrenheit ? 'F' : 'C'}`}</div>
                {hoveredCell.humidity !== undefined && (
                  <div>Humidity: {hoveredCell.humidity.toFixed(1)}%</div>
                )}
                <div>Readings: {hoveredCell.readingsCount}</div>
                <div>Alert Level: {hoveredCell.alertLevel}</div>
                <div>Compliance: {hoveredCell.complianceStatus}</div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Statistics Summary */}
      {heatMapData && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Data Points</p>
                  <p className="text-2xl font-bold">
                    {heatMapData.cells.filter(c => !isNaN(c.value) && c.readingsCount > 0).length}
                  </p>
                </div>
                <Grid className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Temperature Violations</p>
                  <p className="text-2xl font-bold text-red-600">
                    {heatMapData.cells.filter(c => c.alertLevel === 'critical' || c.alertLevel === 'warning').length}
                  </p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Compliance Rate</p>
                  <p className="text-2xl font-bold text-green-600">
                    {heatMapData.cells.length > 0 
                      ? ((heatMapData.cells.filter(c => c.complianceStatus === 'compliant').length / heatMapData.cells.filter(c => c.complianceStatus !== 'unknown').length) * 100).toFixed(1)
                      : 0
                    }%
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <Card>
          <CardContent className="p-8 text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">Loading heat map data...</p>
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {!loading && !heatMapData && selectedSensors.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <Thermometer className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No sensors selected</h3>
            <p className="text-muted-foreground mb-4">
              Please select sensors to generate the heat map.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default TemperatureHeatMap;