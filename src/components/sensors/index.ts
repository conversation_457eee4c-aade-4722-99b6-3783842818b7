/**
 * Sensor Components Export Index
 * 
 * Centralized exports for all temperature monitoring and sensor components
 */

// Dashboard and monitoring components
export { TemperatureDashboard } from './TemperatureDashboard';
export { TemperatureChart } from './TemperatureChart';
export { SensorStatusCard } from './SensorStatusCard';

// Management and configuration components
export { SensorManagement } from './SensorManagement';
export { SensorDiscovery } from './SensorDiscovery';
export { SensorCalibration } from './SensorCalibration';

// Re-export the custom hook for convenience
export { useTemperatureDashboard } from '../../hooks/useTemperatureDashboard';

// Type exports for component props (if needed)
export type { default as TemperatureDashboardProps } from './TemperatureDashboard';
export type { default as TemperatureChartProps } from './TemperatureChart';
export type { default as SensorStatusCardProps } from './SensorStatusCard';
export type { default as SensorManagementProps } from './SensorManagement';
export type { default as SensorDiscoveryProps } from './SensorDiscovery';
export type { default as SensorCalibrationProps } from './SensorCalibration';