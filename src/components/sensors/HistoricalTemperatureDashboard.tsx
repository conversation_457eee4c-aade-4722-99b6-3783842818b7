/**
 * Historical Temperature Dashboard Component
 * 
 * Comprehensive historical data analysis with:
 * - Time series charts showing temperature trends over time
 * - Multiple sensor comparison charts
 * - Historical data table with pagination
 * - Export functionality (CSV, PDF)
 * - Date range filtering
 * - Temperature unit conversion (Celsius/Fahrenheit)
 * - Statistical analysis and insights
 * - Mobile responsive design
 */

import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  CalendarIcon,
  Download,
  TrendingUp,
  TrendingDown,
  BarChart3,
  LineChart as LineChartIcon,
  Settings,
  Filter,
  RefreshCw,
  Clock,
  Thermometer,
  AlertTriangle,
  FileText,
  Table,
  Activity,
  Zap
} from 'lucide-react';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer, 
  ReferenceLine,
  AreaChart,
  Area,
  BarChart,
  Bar,
  ScatterChart,
  Scatter,
  Cell,
  PieChart,
  Pie,
  Legend
} from 'recharts';
import { format, subDays, subHours, subWeeks, subMonths } from 'date-fns';
import { tempStickService } from '@/lib/tempstick-service';
import type { 
  TempStickReading,
  Sensor,
  TemperatureTrendData,
  TimeRange 
} from '@/types/tempstick';

// Types for historical dashboard
interface HistoricalDataPoint {
  timestamp: number;
  date: string;
  time: string;
  temperature: number;
  temperatureF: number;
  humidity?: number;
  sensorId: string;
  sensorName: string;
  alertLevel?: 'normal' | 'warning' | 'critical';
  batteryLevel?: number;
  signalStrength?: number;
}

interface SensorStatistics {
  sensorId: string;
  sensorName: string;
  totalReadings: number;
  averageTemp: number;
  minTemp: number;
  maxTemp: number;
  tempRange: number;
  averageHumidity?: number;
  alertsCount: number;
  complianceRate: number;
  lastReading: Date;
  dataQuality: 'excellent' | 'good' | 'fair' | 'poor';
}

interface DateRangeFilter {
  startDate: Date;
  endDate: Date;
  preset: TimeRange | 'custom';
}

interface ExportOptions {
  format: 'csv' | 'pdf' | 'excel';
  includeCharts: boolean;
  includeStatistics: boolean;
  includeAlerts: boolean;
  sensorIds: string[];
}

interface HistoricalDashboardProps {
  className?: string;
  initialTimeRange?: TimeRange;
  showControls?: boolean;
  compactMode?: boolean;
}

export const HistoricalTemperatureDashboard: React.FC<HistoricalDashboardProps> = ({
  className = '',
  initialTimeRange = '7d',
  showControls = true,
  compactMode = false
}) => {
  // State management
  const [historicalData, setHistoricalData] = useState<HistoricalDataPoint[]>([]);
  const [sensorStatistics, setSensorStatistics] = useState<SensorStatistics[]>([]);
  const [availableSensors, setAvailableSensors] = useState<Sensor[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedSensors, setSelectedSensors] = useState<string[]>([]);
  const [showFahrenheit, setShowFahrenheit] = useState(true);
  const [showHumidity, setShowHumidity] = useState(true);
  const [chartType, setChartType] = useState<'line' | 'area' | 'bar' | 'scatter'>('line');
  const [activeTab, setActiveTab] = useState<'trends' | 'comparison' | 'statistics' | 'export'>('trends');
  
  // Date range filter state
  const [dateRange, setDateRange] = useState<DateRangeFilter>(() => {
    const endDate = new Date();
    let startDate: Date;
    
    switch (initialTimeRange) {
      case '1h':
        startDate = subHours(endDate, 1);
        break;
      case '6h':
        startDate = subHours(endDate, 6);
        break;
      case '24h':
        startDate = subHays(endDate, 1);
        break;
      case '7d':
        startDate = subDays(endDate, 7);
        break;
      case '30d':
        startDate = subDays(endDate, 30);
        break;
      default:
        startDate = subDays(endDate, 7);
    }
    
    return {
      startDate,
      endDate,
      preset: initialTimeRange
    };
  });

  /**
   * Fetch available sensors
   */
  const fetchAvailableSensors = useCallback(async () => {
    try {
      const sensors = await tempStickService.getAllSensors();
      const sensorData = sensors.map(sensor => ({
        id: sensor.sensor_id,
        tempstick_sensor_id: sensor.sensor_id,
        name: sensor.sensor_name,
        location: sensor.ssid || 'Unknown Location',
        sensor_type: 'temperature_humidity' as const,
        temp_min_threshold: null,
        temp_max_threshold: null,
        humidity_min_threshold: null,
        humidity_max_threshold: null,
        storage_area_id: null,
        active: sensor.offline !== '1',
        created_at: new Date().toISOString(),
      }));
      
      setAvailableSensors(sensorData);
      
      // Select all sensors by default if none selected
      if (selectedSensors.length === 0) {
        setSelectedSensors(sensorData.map(s => s.id));
      }
    } catch (err) {
      console.error('Failed to fetch sensors:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch sensors');
    }
  }, [selectedSensors.length]);

  /**
   * Fetch historical data for selected sensors and date range
   */
  const fetchHistoricalData = useCallback(async () => {
    if (selectedSensors.length === 0) {
      setHistoricalData([]);
      setSensorStatistics([]);
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      const allData: HistoricalDataPoint[] = [];
      const statistics: SensorStatistics[] = [];
      
      // Fetch data for each selected sensor
      for (const sensorId of selectedSensors) {
        try {
          // Get sensor info
          const sensor = availableSensors.find(s => s.id === sensorId);
          if (!sensor) continue;
          
          // Fetch historical readings
          const readings = await tempStickService.getLatestReadings(sensorId, 1000);
          
          // Filter readings by date range
          const filteredReadings = readings.filter(reading => {
            const readingDate = new Date(reading.timestamp);
            return readingDate >= dateRange.startDate && readingDate <= dateRange.endDate;
          });
          
          // Convert to chart data format
          const sensorData: HistoricalDataPoint[] = filteredReadings.map(reading => {
            const timestamp = new Date(reading.timestamp);
            return {
              timestamp: timestamp.getTime(),
              date: format(timestamp, 'yyyy-MM-dd'),
              time: format(timestamp, 'HH:mm:ss'),
              temperature: reading.temperature,
              temperatureF: (reading.temperature * 9/5) + 32,
              humidity: reading.humidity,
              sensorId,
              sensorName: sensor.name,
              alertLevel: getAlertLevel(reading.temperature),
              batteryLevel: reading.battery_level,
              signalStrength: reading.signal_strength
            };
          }).sort((a, b) => a.timestamp - b.timestamp);
          
          allData.push(...sensorData);
          
          // Calculate statistics
          if (sensorData.length > 0) {
            const temps = sensorData.map(d => d.temperature);
            const humidities = sensorData.filter(d => d.humidity !== undefined).map(d => d.humidity!);
            
            const stats: SensorStatistics = {
              sensorId,
              sensorName: sensor.name,
              totalReadings: sensorData.length,
              averageTemp: temps.reduce((sum, temp) => sum + temp, 0) / temps.length,
              minTemp: Math.min(...temps),
              maxTemp: Math.max(...temps),
              tempRange: Math.max(...temps) - Math.min(...temps),
              averageHumidity: humidities.length > 0 
                ? humidities.reduce((sum, hum) => sum + hum, 0) / humidities.length 
                : undefined,
              alertsCount: sensorData.filter(d => d.alertLevel !== 'normal').length,
              complianceRate: (sensorData.filter(d => d.alertLevel === 'normal').length / sensorData.length) * 100,
              lastReading: new Date(Math.max(...sensorData.map(d => d.timestamp))),
              dataQuality: getDataQuality(sensorData.length, dateRange)
            };
            
            statistics.push(stats);
          }
        } catch (err) {
          console.warn(`Failed to fetch data for sensor ${sensorId}:`, err);
        }
      }
      
      setHistoricalData(allData);
      setSensorStatistics(statistics);
      
    } catch (err) {
      console.error('Failed to fetch historical data:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch historical data');
    } finally {
      setLoading(false);
    }
  }, [selectedSensors, dateRange, availableSensors]);

  /**
   * Get alert level based on temperature
   */
  const getAlertLevel = (temperature: number): 'normal' | 'warning' | 'critical' => {
    // Basic alert logic - can be enhanced with sensor-specific thresholds
    if (temperature < 32 || temperature > 40) {
      return 'critical';
    } else if (temperature < 35 || temperature > 38) {
      return 'warning';
    }
    return 'normal';
  };

  /**
   * Get data quality assessment
   */
  const getDataQuality = (readingsCount: number, dateRange: DateRangeFilter): 'excellent' | 'good' | 'fair' | 'poor' => {
    const expectedReadings = getExpectedReadingsCount(dateRange);
    const ratio = readingsCount / expectedReadings;
    
    if (ratio >= 0.95) return 'excellent';
    if (ratio >= 0.85) return 'good';
    if (ratio >= 0.70) return 'fair';
    return 'poor';
  };

  /**
   * Calculate expected readings based on date range
   */
  const getExpectedReadingsCount = (dateRange: DateRangeFilter): number => {
    const diffMs = dateRange.endDate.getTime() - dateRange.startDate.getTime();
    const diffHours = diffMs / (1000 * 60 * 60);
    return Math.floor(diffHours * 6); // Assume 6 readings per hour
  };

  /**
   * Handle date range preset change
   */
  const handleDateRangePreset = useCallback((preset: TimeRange) => {
    const endDate = new Date();
    let startDate: Date;
    
    switch (preset) {
      case '1h':
        startDate = subHours(endDate, 1);
        break;
      case '6h':
        startDate = subHours(endDate, 6);
        break;
      case '24h':
        startDate = subDays(endDate, 1);
        break;
      case '7d':
        startDate = subDays(endDate, 7);
        break;
      case '30d':
        startDate = subDays(endDate, 30);
        break;
      default:
        return;
    }
    
    setDateRange({
      startDate,
      endDate,
      preset
    });
  }, []);

  /**
   * Export data functionality
   */
  const handleExport = useCallback(async (options: ExportOptions) => {
    try {
      setLoading(true);
      
      // Filter data for selected sensors
      const exportData = historicalData.filter(d => options.sensorIds.includes(d.sensorId));
      
      if (options.format === 'csv') {
        await exportToCSV(exportData, options);
      } else if (options.format === 'pdf') {
        await exportToPDF(exportData, sensorStatistics, options);
      } else if (options.format === 'excel') {
        await exportToExcel(exportData, sensorStatistics, options);
      }
      
    } catch (err) {
      console.error('Export failed:', err);
      setError(err instanceof Error ? err.message : 'Export failed');
    } finally {
      setLoading(false);
    }
  }, [historicalData, sensorStatistics]);

  /**
   * Export to CSV
   */
  const exportToCSV = async (data: HistoricalDataPoint[], options: ExportOptions) => {
    const csvContent = generateCSVContent(data);
    downloadFile(csvContent, 'temperature-history.csv', 'text/csv');
  };

  /**
   * Export to PDF
   */
  const exportToPDF = async (
    data: HistoricalDataPoint[], 
    statistics: SensorStatistics[], 
    options: ExportOptions
  ) => {
    // Implementation would use a PDF library like jsPDF
    console.log('PDF export not yet implemented');
  };

  /**
   * Export to Excel
   */
  const exportToExcel = async (
    data: HistoricalDataPoint[], 
    statistics: SensorStatistics[], 
    options: ExportOptions
  ) => {
    // Implementation would use a library like xlsx
    console.log('Excel export not yet implemented');
  };

  /**
   * Generate CSV content
   */
  const generateCSVContent = (data: HistoricalDataPoint[]): string => {
    const headers = [
      'Date',
      'Time',
      'Sensor ID',
      'Sensor Name',
      'Temperature (°F)',
      'Temperature (°C)',
      'Humidity (%)',
      'Alert Level',
      'Battery Level (%)',
      'Signal Strength (%)'
    ];
    
    const rows = data.map(d => [
      d.date,
      d.time,
      d.sensorId,
      d.sensorName,
      d.temperatureF.toFixed(1),
      d.temperature.toFixed(1),
      d.humidity?.toFixed(1) || '',
      d.alertLevel || 'normal',
      d.batteryLevel?.toString() || '',
      d.signalStrength?.toString() || ''
    ]);
    
    return [headers, ...rows]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');
  };

  /**
   * Download file utility
   */
  const downloadFile = (content: string, filename: string, mimeType: string) => {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    URL.revokeObjectURL(url);
  };

  // Memoized chart data
  const chartData = useMemo(() => {
    if (historicalData.length === 0) return [];
    
    // Group data by timestamp for multi-sensor charts
    const groupedData = historicalData.reduce((acc, reading) => {
      const timestamp = reading.timestamp;
      if (!acc[timestamp]) {
        acc[timestamp] = {
          timestamp,
          date: reading.date,
          time: reading.time,
          readings: []
        };
      }
      acc[timestamp].readings.push(reading);
      return acc;
    }, {} as Record<number, { timestamp: number; date: string; time: string; readings: HistoricalDataPoint[] }>);

    return Object.values(groupedData)
      .map(group => {
        const result: any = {
          timestamp: group.timestamp,
          date: group.date,
          time: group.time,
          formattedDate: format(new Date(group.timestamp), 'MMM dd HH:mm')
        };
        
        // Add temperature data for each sensor
        group.readings.forEach(reading => {
          const sensorKey = `temp_${reading.sensorId}`;
          const sensorName = `temp_${reading.sensorName.replace(/\s+/g, '_')}`;
          result[sensorKey] = showFahrenheit ? reading.temperatureF : reading.temperature;
          result[sensorName] = showFahrenheit ? reading.temperatureF : reading.temperature;
          
          if (showHumidity && reading.humidity !== undefined) {
            const humidityKey = `humidity_${reading.sensorId}`;
            result[humidityKey] = reading.humidity;
          }
        });
        
        return result;
      })
      .sort((a, b) => a.timestamp - b.timestamp);
  }, [historicalData, showFahrenheit, showHumidity]);

  // Color palette for multiple sensors
  const sensorColors = ['#2563eb', '#dc2626', '#16a34a', '#ca8a04', '#9333ea', '#c2410c', '#0891b2', '#be123c'];

  // Effects
  useEffect(() => {
    fetchAvailableSensors();
  }, [fetchAvailableSensors]);

  useEffect(() => {
    if (availableSensors.length > 0) {
      fetchHistoricalData();
    }
  }, [fetchHistoricalData, availableSensors.length]);

  // Loading state
  if (loading && historicalData.length === 0) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="flex items-center space-x-2">
          <RefreshCw className="h-4 w-4 animate-spin" />
          <span>Loading historical temperature data...</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">Historical Temperature Analysis</h1>
          <p className="text-muted-foreground">
            Comprehensive historical data analysis and insights
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={fetchHistoricalData}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh Data
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Controls */}
      {showControls && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center">
              <Settings className="h-4 w-4 mr-2" />
              Analysis Controls
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Date Range */}
              <div className="space-y-2">
                <Label>Date Range</Label>
                <Select
                  value={dateRange.preset}
                  onValueChange={(value: TimeRange) => handleDateRangePreset(value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1h">Last Hour</SelectItem>
                    <SelectItem value="6h">Last 6 Hours</SelectItem>
                    <SelectItem value="24h">Last 24 Hours</SelectItem>
                    <SelectItem value="7d">Last 7 Days</SelectItem>
                    <SelectItem value="30d">Last 30 Days</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Sensor Selection */}
              <div className="space-y-2">
                <Label>Sensors</Label>
                <Select
                  value={selectedSensors.length === 1 ? selectedSensors[0] : 'multiple'}
                  onValueChange={(value) => {
                    if (value === 'all') {
                      setSelectedSensors(availableSensors.map(s => s.id));
                    } else if (value !== 'multiple') {
                      setSelectedSensors([value]);
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={`${selectedSensors.length} selected`} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Sensors</SelectItem>
                    {availableSensors.map((sensor) => (
                      <SelectItem key={sensor.id} value={sensor.id}>
                        {sensor.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Chart Type */}
              <div className="space-y-2">
                <Label>Chart Type</Label>
                <Select
                  value={chartType}
                  onValueChange={(value: 'line' | 'area' | 'bar' | 'scatter') => setChartType(value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="line">Line Chart</SelectItem>
                    <SelectItem value="area">Area Chart</SelectItem>
                    <SelectItem value="bar">Bar Chart</SelectItem>
                    <SelectItem value="scatter">Scatter Plot</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Display Options */}
              <div className="space-y-2">
                <Label>Display Options</Label>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="fahrenheit"
                      checked={showFahrenheit}
                      onCheckedChange={setShowFahrenheit}
                    />
                    <Label htmlFor="fahrenheit" className="text-sm">°F</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="humidity"
                      checked={showHumidity}
                      onCheckedChange={setShowHumidity}
                    />
                    <Label htmlFor="humidity" className="text-sm">Humidity</Label>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="trends" className="flex items-center space-x-2">
            <TrendingUp className="h-4 w-4" />
            <span>Trends</span>
          </TabsTrigger>
          <TabsTrigger value="comparison" className="flex items-center space-x-2">
            <BarChart3 className="h-4 w-4" />
            <span>Comparison</span>
          </TabsTrigger>
          <TabsTrigger value="statistics" className="flex items-center space-x-2">
            <Activity className="h-4 w-4" />
            <span>Statistics</span>
          </TabsTrigger>
          <TabsTrigger value="export" className="flex items-center space-x-2">
            <Download className="h-4 w-4" />
            <span>Export</span>
          </TabsTrigger>
        </TabsList>

        {/* Temperature Trends Tab */}
        <TabsContent value="trends" className="space-y-4">
          {chartData.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Temperature Trends Over Time</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-96">
                  <ResponsiveContainer width="100%" height="100%">
                    {chartType === 'line' && (
                      <LineChart data={chartData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis 
                          dataKey="formattedDate"
                          tick={{ fontSize: 12 }}
                        />
                        <YAxis 
                          label={{ 
                            value: `Temperature (${showFahrenheit ? '°F' : '°C'})`, 
                            angle: -90, 
                            position: 'insideLeft' 
                          }}
                        />
                        <Tooltip 
                          labelFormatter={(value) => `Time: ${value}`}
                          formatter={(value: number, name: string) => [
                            `${value.toFixed(1)}${showFahrenheit ? '°F' : '°C'}`,
                            name.replace('temp_', '').replace(/_/g, ' ')
                          ]}
                        />
                        {selectedSensors.map((sensorId, index) => {
                          const sensor = availableSensors.find(s => s.id === sensorId);
                          if (!sensor) return null;
                          
                          return (
                            <Line
                              key={sensorId}
                              type="monotone"
                              dataKey={`temp_${sensorId}`}
                              stroke={sensorColors[index % sensorColors.length]}
                              strokeWidth={2}
                              dot={false}
                              connectNulls={false}
                              name={sensor.name}
                            />
                          );
                        })}
                      </LineChart>
                    )}
                    
                    {chartType === 'area' && (
                      <AreaChart data={chartData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="formattedDate" tick={{ fontSize: 12 }} />
                        <YAxis 
                          label={{ 
                            value: `Temperature (${showFahrenheit ? '°F' : '°C'})`, 
                            angle: -90, 
                            position: 'insideLeft' 
                          }}
                        />
                        <Tooltip />
                        {selectedSensors.map((sensorId, index) => {
                          const sensor = availableSensors.find(s => s.id === sensorId);
                          if (!sensor) return null;
                          
                          return (
                            <Area
                              key={sensorId}
                              type="monotone"
                              dataKey={`temp_${sensorId}`}
                              stackId={index}
                              stroke={sensorColors[index % sensorColors.length]}
                              fill={sensorColors[index % sensorColors.length]}
                              fillOpacity={0.3}
                              name={sensor.name}
                            />
                          );
                        })}
                      </AreaChart>
                    )}
                    
                    {chartType === 'scatter' && (
                      <ScatterChart data={chartData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="timestamp" type="number" scale="time" domain={['dataMin', 'dataMax']} />
                        <YAxis 
                          dataKey="temperature"
                          label={{ 
                            value: `Temperature (${showFahrenheit ? '°F' : '°C'})`, 
                            angle: -90, 
                            position: 'insideLeft' 
                          }}
                        />
                        <Tooltip />
                        {selectedSensors.map((sensorId, index) => {
                          const sensor = availableSensors.find(s => s.id === sensorId);
                          if (!sensor) return null;
                          
                          return (
                            <Scatter
                              key={sensorId}
                              name={sensor.name}
                              fill={sensorColors[index % sensorColors.length]}
                            />
                          );
                        })}
                      </ScatterChart>
                    )}
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          )}
          
          {/* Humidity Chart */}
          {showHumidity && chartData.some(d => Object.keys(d).some(k => k.startsWith('humidity_'))) && (
            <Card>
              <CardHeader>
                <CardTitle>Humidity Trends Over Time</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={chartData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="formattedDate" tick={{ fontSize: 12 }} />
                      <YAxis 
                        label={{ 
                          value: 'Humidity (%)', 
                          angle: -90, 
                          position: 'insideLeft' 
                        }}
                      />
                      <Tooltip />
                      {selectedSensors.map((sensorId, index) => (
                        <Line
                          key={`humidity_${sensorId}`}
                          type="monotone"
                          dataKey={`humidity_${sensorId}`}
                          stroke={sensorColors[index % sensorColors.length]}
                          strokeWidth={2}
                          strokeDasharray="5 5"
                          dot={false}
                        />
                      ))}
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Sensor Comparison Tab */}
        <TabsContent value="comparison" className="space-y-4">
          {sensorStatistics.length > 0 && (
            <>
              {/* Temperature Comparison Chart */}
              <Card>
                <CardHeader>
                  <CardTitle>Average Temperature Comparison</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={sensorStatistics}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="sensorName" tick={{ fontSize: 12 }} />
                        <YAxis 
                          label={{ 
                            value: `Temperature (${showFahrenheit ? '°F' : '°C'})`, 
                            angle: -90, 
                            position: 'insideLeft' 
                          }}
                        />
                        <Tooltip />
                        <Bar 
                          dataKey="averageTemp" 
                          fill="#2563eb"
                          name="Average Temperature"
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              {/* Sensor Performance Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {sensorStatistics.map((stats) => (
                  <Card key={stats.sensorId}>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium truncate">
                        {stats.sensorName}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Avg Temp:</span>
                          <span className="font-medium">
                            {showFahrenheit 
                              ? `${((stats.averageTemp * 9/5) + 32).toFixed(1)}°F`
                              : `${stats.averageTemp.toFixed(1)}°C`
                            }
                          </span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Range:</span>
                          <span className="font-medium">
                            {showFahrenheit 
                              ? `${((stats.tempRange * 9/5)).toFixed(1)}°F`
                              : `${stats.tempRange.toFixed(1)}°C`
                            }
                          </span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Readings:</span>
                          <span className="font-medium">{stats.totalReadings.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Compliance:</span>
                          <span className="font-medium">{stats.complianceRate.toFixed(1)}%</span>
                        </div>
                        <div className="mt-2">
                          <Badge 
                            variant={stats.dataQuality === 'excellent' ? 'default' : 'secondary'}
                            className="text-xs"
                          >
                            {stats.dataQuality} data quality
                          </Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </>
          )}
        </TabsContent>

        {/* Statistics Tab */}
        <TabsContent value="statistics" className="space-y-4">
          {sensorStatistics.length > 0 && (
            <>
              {/* Overall Summary */}
              <Card>
                <CardHeader>
                  <CardTitle>Overall Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold">
                        {sensorStatistics.length}
                      </div>
                      <div className="text-sm text-muted-foreground">Sensors Analyzed</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold">
                        {sensorStatistics.reduce((sum, s) => sum + s.totalReadings, 0).toLocaleString()}
                      </div>
                      <div className="text-sm text-muted-foreground">Total Readings</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold">
                        {(sensorStatistics.reduce((sum, s) => sum + s.complianceRate, 0) / sensorStatistics.length).toFixed(1)}%
                      </div>
                      <div className="text-sm text-muted-foreground">Avg Compliance</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold">
                        {sensorStatistics.reduce((sum, s) => sum + s.alertsCount, 0)}
                      </div>
                      <div className="text-sm text-muted-foreground">Total Alerts</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Detailed Statistics Table */}
              <Card>
                <CardHeader>
                  <CardTitle>Detailed Statistics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left p-2">Sensor</th>
                          <th className="text-right p-2">Readings</th>
                          <th className="text-right p-2">Avg Temp</th>
                          <th className="text-right p-2">Min Temp</th>
                          <th className="text-right p-2">Max Temp</th>
                          <th className="text-right p-2">Range</th>
                          <th className="text-right p-2">Compliance</th>
                          <th className="text-center p-2">Data Quality</th>
                        </tr>
                      </thead>
                      <tbody>
                        {sensorStatistics.map((stats) => (
                          <tr key={stats.sensorId} className="border-b hover:bg-muted/50">
                            <td className="p-2 font-medium">{stats.sensorName}</td>
                            <td className="p-2 text-right">{stats.totalReadings.toLocaleString()}</td>
                            <td className="p-2 text-right">
                              {showFahrenheit 
                                ? `${((stats.averageTemp * 9/5) + 32).toFixed(1)}°F`
                                : `${stats.averageTemp.toFixed(1)}°C`
                              }
                            </td>
                            <td className="p-2 text-right">
                              {showFahrenheit 
                                ? `${((stats.minTemp * 9/5) + 32).toFixed(1)}°F`
                                : `${stats.minTemp.toFixed(1)}°C`
                              }
                            </td>
                            <td className="p-2 text-right">
                              {showFahrenheit 
                                ? `${((stats.maxTemp * 9/5) + 32).toFixed(1)}°F`
                                : `${stats.maxTemp.toFixed(1)}°C`
                              }
                            </td>
                            <td className="p-2 text-right">
                              {showFahrenheit 
                                ? `${(stats.tempRange * 9/5).toFixed(1)}°F`
                                : `${stats.tempRange.toFixed(1)}°C`
                              }
                            </td>
                            <td className="p-2 text-right">{stats.complianceRate.toFixed(1)}%</td>
                            <td className="p-2 text-center">
                              <Badge 
                                variant={stats.dataQuality === 'excellent' ? 'default' : 'secondary'}
                                className="text-xs"
                              >
                                {stats.dataQuality}
                              </Badge>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>

        {/* Export Tab */}
        <TabsContent value="export" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Export Historical Data</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button
                    onClick={() => handleExport({
                      format: 'csv',
                      includeCharts: false,
                      includeStatistics: true,
                      includeAlerts: false,
                      sensorIds: selectedSensors
                    })}
                    className="flex items-center justify-center space-x-2"
                    disabled={loading || historicalData.length === 0}
                  >
                    <FileText className="h-4 w-4" />
                    <span>Export to CSV</span>
                  </Button>
                  
                  <Button
                    onClick={() => handleExport({
                      format: 'pdf',
                      includeCharts: true,
                      includeStatistics: true,
                      includeAlerts: true,
                      sensorIds: selectedSensors
                    })}
                    className="flex items-center justify-center space-x-2"
                    disabled={loading || historicalData.length === 0}
                  >
                    <FileText className="h-4 w-4" />
                    <span>Export to PDF</span>
                  </Button>
                  
                  <Button
                    onClick={() => handleExport({
                      format: 'excel',
                      includeCharts: true,
                      includeStatistics: true,
                      includeAlerts: true,
                      sensorIds: selectedSensors
                    })}
                    className="flex items-center justify-center space-x-2"
                    disabled={loading || historicalData.length === 0}
                  >
                    <Table className="h-4 w-4" />
                    <span>Export to Excel</span>
                  </Button>
                </div>
                
                {historicalData.length === 0 && (
                  <div className="text-center text-muted-foreground py-4">
                    No data available for export. Please select sensors and date range.
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Empty State */}
      {historicalData.length === 0 && !loading && (
        <Card>
          <CardContent className="p-8 text-center">
            <LineChartIcon className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No historical data found</h3>
            <p className="text-muted-foreground mb-4">
              No temperature data available for the selected sensors and date range.
            </p>
            <Button variant="outline" onClick={fetchHistoricalData}>
              Refresh Data
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default HistoricalTemperatureDashboard;