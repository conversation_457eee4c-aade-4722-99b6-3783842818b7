/**
 * Alerts Management Component
 * 
 * Comprehensive alert management interface with:
 * - Active alerts list with status indicators
 * - Alert history with filtering by type/sensor/date
 * - Alert configuration interface
 * - Notification management
 * - Alert acknowledgment system
 * - Real-time alert updates
 * - HACCP compliance tracking
 */

import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from '@/components/ui/dialog';
import { 
  AlertTriangle,
  Bell,
  BellOff,
  CheckCircle,
  Clock,
  Settings,
  Filter,
  RefreshCw,
  Eye,
  EyeOff,
  Mail,
  MessageSquare,
  Smartphone,
  AlertCircle,
  Info,
  Shield,
  Zap,
  Calendar,
  User,
  FileText,
  Download,
  Search,
  X,
  Plus
} from 'lucide-react';
import { format, formatDistanceToNow } from 'date-fns';
import { tempStickService } from '@/lib/tempstick-service';
import type { 
  TemperatureAlert,
  Sensor,
  AlertConfig 
} from '@/types/tempstick';

// Enhanced alert types
interface AlertWithDetails extends TemperatureAlert {
  sensor?: Sensor;
  timeAgo?: string;
  durationText?: string;
  escalationStatus?: 'none' | 'pending' | 'escalated';
}

interface AlertFilters {
  severity: TemperatureAlert['severity'] | 'all';
  status: TemperatureAlert['alert_status'] | 'all';
  alertType: TemperatureAlert['alert_type'] | 'all';
  sensorId: string | 'all';
  dateRange: 'all' | '1h' | '6h' | '24h' | '7d' | '30d';
  searchQuery: string;
}

interface AlertNotificationSettings {
  email: boolean;
  sms: boolean;
  push: boolean;
  webhooks: string[];
  escalationEnabled: boolean;
  escalationDelays: number[]; // minutes
}

interface AlertsManagementProps {
  className?: string;
  showActiveOnly?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export const AlertsManagement: React.FC<AlertsManagementProps> = ({
  className = '',
  showActiveOnly = false,
  autoRefresh = true,
  refreshInterval = 30000
}) => {
  // State management
  const [alerts, setAlerts] = useState<AlertWithDetails[]>([]);
  const [sensors, setSensors] = useState<Sensor[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedAlert, setSelectedAlert] = useState<AlertWithDetails | null>(null);
  const [showAlertDialog, setShowAlertDialog] = useState(false);
  const [showConfigDialog, setShowConfigDialog] = useState(false);
  const [activeTab, setActiveTab] = useState<'active' | 'history' | 'configuration' | 'notifications'>('active');
  
  // Filters state
  const [filters, setFilters] = useState<AlertFilters>({
    severity: 'all',
    status: showActiveOnly ? 'active' : 'all',
    alertType: 'all',
    sensorId: 'all',
    dateRange: '24h',
    searchQuery: ''
  });

  // Notification settings
  const [notificationSettings, setNotificationSettings] = useState<AlertNotificationSettings>({
    email: true,
    sms: false,
    push: true,
    webhooks: [],
    escalationEnabled: true,
    escalationDelays: [15, 60, 240] // 15 min, 1 hour, 4 hours
  });

  // Alert configuration
  const [alertConfigs, setAlertConfigs] = useState<Record<string, AlertConfig>>({});
  const [selectedSensorConfig, setSelectedSensorConfig] = useState<string>('');

  /**
   * Fetch alerts from API and database
   */
  const fetchAlerts = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Fetch from TempStick API
      const apiAlerts = await tempStickService.getAlerts();
      
      // Get current alerts from database
      const currentAlerts = await tempStickService.getActiveAlerts();
      
      // Combine and format alerts
      const allAlerts: AlertWithDetails[] = [
        ...currentAlerts,
        ...apiAlerts.map((apiAlert: any) => convertApiAlertToLocal(apiAlert))
      ];
      
      // Remove duplicates and add metadata
      const uniqueAlerts = removeDuplicateAlerts(allAlerts).map(alert => ({
        ...alert,
        timeAgo: formatDistanceToNow(new Date(alert.created_at), { addSuffix: true }),
        durationText: alert.resolved_at 
          ? `Resolved after ${formatDistanceToNow(new Date(alert.resolved_at), new Date(alert.created_at))}`
          : `Active for ${formatDistanceToNow(new Date(alert.created_at))}`,
        escalationStatus: getEscalationStatus(alert)
      }));
      
      setAlerts(uniqueAlerts);
      
    } catch (err) {
      console.error('Failed to fetch alerts:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch alerts');
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Fetch available sensors
   */
  const fetchSensors = useCallback(async () => {
    try {
      const sensorData = await tempStickService.getAllSensors();
      const formattedSensors: Sensor[] = sensorData.map(sensor => ({
        id: sensor.sensor_id,
        tempstick_sensor_id: sensor.sensor_id,
        name: sensor.sensor_name,
        location: sensor.ssid || 'Unknown Location',
        sensor_type: 'temperature_humidity' as const,
        temp_min_threshold: null,
        temp_max_threshold: null,
        humidity_min_threshold: null,
        humidity_max_threshold: null,
        storage_area_id: null,
        active: sensor.offline !== '1',
        created_at: new Date().toISOString(),
      }));
      
      setSensors(formattedSensors);
    } catch (err) {
      console.error('Failed to fetch sensors:', err);
    }
  }, []);

  /**
   * Convert TempStick API alert to local format
   */
  const convertApiAlertToLocal = (apiAlert: any): AlertWithDetails => {
    return {
      id: apiAlert.id || `api-${Date.now()}-${Math.random()}`,
      sensor_id: apiAlert.sensor_id || '',
      alert_type: mapApiAlertType(apiAlert.type),
      title: apiAlert.title || 'TempStick Alert',
      message: apiAlert.message || apiAlert.description || 'Alert from TempStick API',
      threshold_value: parseFloat(apiAlert.threshold_value || 0),
      actual_value: parseFloat(apiAlert.actual_value || 0),
      created_at: apiAlert.created_at || apiAlert.timestamp || new Date().toISOString(),
      resolved_at: apiAlert.resolved_at || null,
      severity: mapApiSeverity(apiAlert.severity),
      alert_status: mapApiStatus(apiAlert.status),
      acknowledged_at: apiAlert.acknowledged_at || null,
      acknowledged_by: apiAlert.acknowledged_by || null,
      resolved_by: apiAlert.resolved_by || null,
      resolution_notes: apiAlert.resolution_notes || null,
      corrective_action_required: Boolean(apiAlert.corrective_action_required),
      corrective_action_taken: apiAlert.corrective_action_taken || null,
      escalation_level: parseInt(apiAlert.escalation_level || 0),
      escalated_at: apiAlert.escalated_at || null,
      metadata: apiAlert.metadata || {},
      updated_at: apiAlert.updated_at || new Date().toISOString()
    };
  };

  /**
   * Map API alert types to local types
   */
  const mapApiAlertType = (apiType: string): TemperatureAlert['alert_type'] => {
    const mapping: Record<string, TemperatureAlert['alert_type']> = {
      'high_temp': 'temp_high',
      'low_temp': 'temp_low',
      'high_humidity': 'humidity_high',
      'low_humidity': 'humidity_low',
      'offline': 'sensor_offline',
      'low_battery': 'battery_low',
      'calibration': 'calibration_due',
      'maintenance': 'maintenance_due',
      'data_gap': 'data_gap',
      'system_error': 'system_error'
    };
    return mapping[apiType] || 'system_error';
  };

  /**
   * Map API severity to local severity
   */
  const mapApiSeverity = (apiSeverity: string): TemperatureAlert['severity'] => {
    const mapping: Record<string, TemperatureAlert['severity']> = {
      'low': 'info',
      'medium': 'warning',
      'high': 'critical',
      'critical': 'emergency',
      'emergency': 'emergency'
    };
    return mapping[apiSeverity?.toLowerCase()] || 'warning';
  };

  /**
   * Map API status to local status
   */
  const mapApiStatus = (apiStatus: string): TemperatureAlert['alert_status'] => {
    const mapping: Record<string, TemperatureAlert['alert_status']> = {
      'open': 'active',
      'active': 'active',
      'acknowledged': 'acknowledged',
      'investigating': 'investigating',
      'resolved': 'resolved',
      'closed': 'resolved',
      'dismissed': 'dismissed'
    };
    return mapping[apiStatus?.toLowerCase()] || 'active';
  };

  /**
   * Remove duplicate alerts
   */
  const removeDuplicateAlerts = (alerts: AlertWithDetails[]): AlertWithDetails[] => {
    const seen = new Set<string>();
    return alerts.filter(alert => {
      const key = `${alert.sensor_id}-${alert.alert_type}-${alert.created_at}`;
      if (seen.has(key)) return false;
      seen.add(key);
      return true;
    });
  };

  /**
   * Get escalation status
   */
  const getEscalationStatus = (alert: AlertWithDetails): 'none' | 'pending' | 'escalated' => {
    if (!notificationSettings.escalationEnabled) return 'none';
    if (alert.escalation_level && alert.escalation_level > 0) return 'escalated';
    
    const alertAge = Date.now() - new Date(alert.created_at).getTime();
    const escalationThreshold = notificationSettings.escalationDelays[0] * 60 * 1000; // First escalation delay
    
    if (alertAge > escalationThreshold && alert.alert_status === 'active') {
      return 'pending';
    }
    
    return 'none';
  };

  /**
   * Filter alerts based on current filters
   */
  const filteredAlerts = useMemo(() => {
    return alerts.filter(alert => {
      // Severity filter
      if (filters.severity !== 'all' && alert.severity !== filters.severity) return false;
      
      // Status filter
      if (filters.status !== 'all' && alert.alert_status !== filters.status) return false;
      
      // Alert type filter
      if (filters.alertType !== 'all' && alert.alert_type !== filters.alertType) return false;
      
      // Sensor filter
      if (filters.sensorId !== 'all' && alert.sensor_id !== filters.sensorId) return false;
      
      // Date range filter
      if (filters.dateRange !== 'all') {
        const alertDate = new Date(alert.created_at);
        const now = new Date();
        let cutoffDate: Date;
        
        switch (filters.dateRange) {
          case '1h':
            cutoffDate = new Date(now.getTime() - 60 * 60 * 1000);
            break;
          case '6h':
            cutoffDate = new Date(now.getTime() - 6 * 60 * 60 * 1000);
            break;
          case '24h':
            cutoffDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            break;
          case '7d':
            cutoffDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
          case '30d':
            cutoffDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            break;
          default:
            cutoffDate = new Date(0);
        }
        
        if (alertDate < cutoffDate) return false;
      }
      
      // Search query filter
      if (filters.searchQuery.trim()) {
        const searchLower = filters.searchQuery.toLowerCase();
        const matchesTitle = alert.title.toLowerCase().includes(searchLower);
        const matchesMessage = alert.message.toLowerCase().includes(searchLower);
        const matchesSensor = alert.sensor?.name?.toLowerCase().includes(searchLower) || false;
        
        if (!matchesTitle && !matchesMessage && !matchesSensor) return false;
      }
      
      return true;
    });
  }, [alerts, filters]);

  /**
   * Handle alert acknowledgment
   */
  const handleAcknowledgeAlert = useCallback(async (alertId: string) => {
    try {
      // Update alert status in database
      await tempStickService.resolveAlert(alertId);
      
      // Update local state
      setAlerts(prevAlerts => 
        prevAlerts.map(alert => 
          alert.id === alertId 
            ? { 
                ...alert, 
                alert_status: 'acknowledged',
                acknowledged_at: new Date().toISOString(),
                acknowledged_by: 'current_user' // Would be actual user ID in real implementation
              }
            : alert
        )
      );
    } catch (err) {
      console.error('Failed to acknowledge alert:', err);
      setError(err instanceof Error ? err.message : 'Failed to acknowledge alert');
    }
  }, []);

  /**
   * Handle alert resolution
   */
  const handleResolveAlert = useCallback(async (alertId: string, resolutionNotes?: string) => {
    try {
      await tempStickService.resolveAlert(alertId);
      
      setAlerts(prevAlerts => 
        prevAlerts.map(alert => 
          alert.id === alertId 
            ? { 
                ...alert, 
                alert_status: 'resolved',
                resolved_at: new Date().toISOString(),
                resolved_by: 'current_user',
                resolution_notes: resolutionNotes || null
              }
            : alert
        )
      );
    } catch (err) {
      console.error('Failed to resolve alert:', err);
      setError(err instanceof Error ? err.message : 'Failed to resolve alert');
    }
  }, []);

  /**
   * Get severity color and icon
   */
  const getSeverityDisplay = (severity: TemperatureAlert['severity']) => {
    switch (severity) {
      case 'info':
        return { color: 'bg-blue-100 text-blue-800', icon: Info, label: 'Info' };
      case 'warning':
        return { color: 'bg-yellow-100 text-yellow-800', icon: AlertTriangle, label: 'Warning' };
      case 'critical':
        return { color: 'bg-red-100 text-red-800', icon: AlertCircle, label: 'Critical' };
      case 'emergency':
        return { color: 'bg-red-600 text-white', icon: Shield, label: 'Emergency' };
      default:
        return { color: 'bg-gray-100 text-gray-800', icon: Info, label: 'Unknown' };
    }
  };

  /**
   * Get status color and icon
   */
  const getStatusDisplay = (status: TemperatureAlert['alert_status']) => {
    switch (status) {
      case 'active':
        return { color: 'bg-red-100 text-red-800', icon: AlertTriangle, label: 'Active' };
      case 'acknowledged':
        return { color: 'bg-yellow-100 text-yellow-800', icon: Eye, label: 'Acknowledged' };
      case 'investigating':
        return { color: 'bg-blue-100 text-blue-800', icon: Search, label: 'Investigating' };
      case 'resolved':
        return { color: 'bg-green-100 text-green-800', icon: CheckCircle, label: 'Resolved' };
      case 'dismissed':
        return { color: 'bg-gray-100 text-gray-800', icon: X, label: 'Dismissed' };
      default:
        return { color: 'bg-gray-100 text-gray-800', icon: Clock, label: 'Unknown' };
    }
  };

  // Auto-refresh effect
  useEffect(() => {
    fetchSensors();
    fetchAlerts();
    
    if (autoRefresh && refreshInterval > 0) {
      const interval = setInterval(fetchAlerts, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [fetchSensors, fetchAlerts, autoRefresh, refreshInterval]);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">Alerts Management</h1>
          <p className="text-muted-foreground">
            Monitor and manage temperature alerts and notifications
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={fetchAlerts}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Alerts</p>
                <p className="text-2xl font-bold text-red-600">
                  {alerts.filter(a => a.alert_status === 'active').length}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Critical Alerts</p>
                <p className="text-2xl font-bold text-orange-600">
                  {alerts.filter(a => a.severity === 'critical' || a.severity === 'emergency').length}
                </p>
              </div>
              <Shield className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Acknowledged</p>
                <p className="text-2xl font-bold text-blue-600">
                  {alerts.filter(a => a.alert_status === 'acknowledged').length}
                </p>
              </div>
              <Eye className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Resolved Today</p>
                <p className="text-2xl font-bold text-green-600">
                  {alerts.filter(a => 
                    a.alert_status === 'resolved' && 
                    a.resolved_at && 
                    new Date(a.resolved_at) > new Date(Date.now() - 24 * 60 * 60 * 1000)
                  ).length}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium flex items-center">
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
            <div>
              <Label>Severity</Label>
              <Select
                value={filters.severity}
                onValueChange={(value: TemperatureAlert['severity'] | 'all') => 
                  setFilters(prev => ({ ...prev, severity: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Severities</SelectItem>
                  <SelectItem value="info">Info</SelectItem>
                  <SelectItem value="warning">Warning</SelectItem>
                  <SelectItem value="critical">Critical</SelectItem>
                  <SelectItem value="emergency">Emergency</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Status</Label>
              <Select
                value={filters.status}
                onValueChange={(value: TemperatureAlert['alert_status'] | 'all') => 
                  setFilters(prev => ({ ...prev, status: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="acknowledged">Acknowledged</SelectItem>
                  <SelectItem value="investigating">Investigating</SelectItem>
                  <SelectItem value="resolved">Resolved</SelectItem>
                  <SelectItem value="dismissed">Dismissed</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Type</Label>
              <Select
                value={filters.alertType}
                onValueChange={(value: TemperatureAlert['alert_type'] | 'all') => 
                  setFilters(prev => ({ ...prev, alertType: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="temp_high">High Temperature</SelectItem>
                  <SelectItem value="temp_low">Low Temperature</SelectItem>
                  <SelectItem value="humidity_high">High Humidity</SelectItem>
                  <SelectItem value="humidity_low">Low Humidity</SelectItem>
                  <SelectItem value="sensor_offline">Sensor Offline</SelectItem>
                  <SelectItem value="battery_low">Low Battery</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Sensor</Label>
              <Select
                value={filters.sensorId}
                onValueChange={(value: string) => 
                  setFilters(prev => ({ ...prev, sensorId: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Sensors</SelectItem>
                  {sensors.map((sensor) => (
                    <SelectItem key={sensor.id} value={sensor.id}>
                      {sensor.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Time Range</Label>
              <Select
                value={filters.dateRange}
                onValueChange={(value: AlertFilters['dateRange']) => 
                  setFilters(prev => ({ ...prev, dateRange: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Time</SelectItem>
                  <SelectItem value="1h">Last Hour</SelectItem>
                  <SelectItem value="6h">Last 6 Hours</SelectItem>
                  <SelectItem value="24h">Last 24 Hours</SelectItem>
                  <SelectItem value="7d">Last 7 Days</SelectItem>
                  <SelectItem value="30d">Last 30 Days</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Search</Label>
              <div className="relative">
                <Input
                  placeholder="Search alerts..."
                  value={filters.searchQuery}
                  onChange={(e) => setFilters(prev => ({ ...prev, searchQuery: e.target.value }))}
                  className="pr-8"
                />
                <Search className="absolute right-2 top-2.5 h-4 w-4 text-muted-foreground" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="active">
            Active Alerts ({alerts.filter(a => a.alert_status === 'active').length})
          </TabsTrigger>
          <TabsTrigger value="history">
            History ({alerts.length})
          </TabsTrigger>
          <TabsTrigger value="configuration">
            Configuration
          </TabsTrigger>
          <TabsTrigger value="notifications">
            Notifications
          </TabsTrigger>
        </TabsList>

        {/* Active Alerts Tab */}
        <TabsContent value="active" className="space-y-4">
          <div className="space-y-4">
            {filteredAlerts
              .filter(alert => alert.alert_status === 'active')
              .map((alert) => (
                <AlertCard
                  key={alert.id}
                  alert={alert}
                  sensors={sensors}
                  onAcknowledge={handleAcknowledgeAlert}
                  onResolve={handleResolveAlert}
                  onViewDetails={(alert) => {
                    setSelectedAlert(alert);
                    setShowAlertDialog(true);
                  }}
                />
              ))}
            
            {filteredAlerts.filter(alert => alert.alert_status === 'active').length === 0 && (
              <Card>
                <CardContent className="p-8 text-center">
                  <CheckCircle className="h-12 w-12 mx-auto text-green-500 mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Active Alerts</h3>
                  <p className="text-muted-foreground">
                    All alerts have been addressed. Great job!
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        {/* History Tab */}
        <TabsContent value="history" className="space-y-4">
          <div className="space-y-4">
            {filteredAlerts.map((alert) => (
              <AlertCard
                key={alert.id}
                alert={alert}
                sensors={sensors}
                onAcknowledge={handleAcknowledgeAlert}
                onResolve={handleResolveAlert}
                onViewDetails={(alert) => {
                  setSelectedAlert(alert);
                  setShowAlertDialog(true);
                }}
                showAllStatuses={true}
              />
            ))}
            
            {filteredAlerts.length === 0 && (
              <Card>
                <CardContent className="p-8 text-center">
                  <AlertTriangle className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Alerts Found</h3>
                  <p className="text-muted-foreground">
                    No alerts match your current filters.
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        {/* Configuration Tab */}
        <TabsContent value="configuration" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Alert Thresholds</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label>Select Sensor</Label>
                  <Select
                    value={selectedSensorConfig}
                    onValueChange={setSelectedSensorConfig}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Choose sensor to configure" />
                    </SelectTrigger>
                    <SelectContent>
                      {sensors.map((sensor) => (
                        <SelectItem key={sensor.id} value={sensor.id}>
                          {sensor.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                {selectedSensorConfig && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 border rounded-lg">
                    <div>
                      <Label>Temperature Min (°F)</Label>
                      <Input
                        type="number"
                        placeholder="32"
                        value={alertConfigs[selectedSensorConfig]?.thresholds.tempMin || ''}
                        onChange={(e) => {
                          const value = e.target.value ? parseFloat(e.target.value) : undefined;
                          setAlertConfigs(prev => ({
                            ...prev,
                            [selectedSensorConfig]: {
                              ...prev[selectedSensorConfig],
                              sensorId: selectedSensorConfig,
                              thresholds: {
                                ...prev[selectedSensorConfig]?.thresholds,
                                tempMin: value
                              }
                            }
                          }));
                        }}
                      />
                    </div>
                    <div>
                      <Label>Temperature Max (°F)</Label>
                      <Input
                        type="number"
                        placeholder="40"
                        value={alertConfigs[selectedSensorConfig]?.thresholds.tempMax || ''}
                        onChange={(e) => {
                          const value = e.target.value ? parseFloat(e.target.value) : undefined;
                          setAlertConfigs(prev => ({
                            ...prev,
                            [selectedSensorConfig]: {
                              ...prev[selectedSensorConfig],
                              sensorId: selectedSensorConfig,
                              thresholds: {
                                ...prev[selectedSensorConfig]?.thresholds,
                                tempMax: value
                              }
                            }
                          }));
                        }}
                      />
                    </div>
                    <div>
                      <Label>Humidity Min (%)</Label>
                      <Input
                        type="number"
                        placeholder="30"
                        value={alertConfigs[selectedSensorConfig]?.thresholds.humidityMin || ''}
                        onChange={(e) => {
                          const value = e.target.value ? parseFloat(e.target.value) : undefined;
                          setAlertConfigs(prev => ({
                            ...prev,
                            [selectedSensorConfig]: {
                              ...prev[selectedSensorConfig],
                              sensorId: selectedSensorConfig,
                              thresholds: {
                                ...prev[selectedSensorConfig]?.thresholds,
                                humidityMin: value
                              }
                            }
                          }));
                        }}
                      />
                    </div>
                    <div>
                      <Label>Humidity Max (%)</Label>
                      <Input
                        type="number"
                        placeholder="80"
                        value={alertConfigs[selectedSensorConfig]?.thresholds.humidityMax || ''}
                        onChange={(e) => {
                          const value = e.target.value ? parseFloat(e.target.value) : undefined;
                          setAlertConfigs(prev => ({
                            ...prev,
                            [selectedSensorConfig]: {
                              ...prev[selectedSensorConfig],
                              sensorId: selectedSensorConfig,
                              thresholds: {
                                ...prev[selectedSensorConfig]?.thresholds,
                                humidityMax: value
                              }
                            }
                          }));
                        }}
                      />
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Notifications Tab */}
        <TabsContent value="notifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Notification Settings</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Mail className="h-4 w-4" />
                    <Label>Email Notifications</Label>
                  </div>
                  <Switch
                    checked={notificationSettings.email}
                    onCheckedChange={(checked) =>
                      setNotificationSettings(prev => ({ ...prev, email: checked }))
                    }
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <MessageSquare className="h-4 w-4" />
                    <Label>SMS Notifications</Label>
                  </div>
                  <Switch
                    checked={notificationSettings.sms}
                    onCheckedChange={(checked) =>
                      setNotificationSettings(prev => ({ ...prev, sms: checked }))
                    }
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Smartphone className="h-4 w-4" />
                    <Label>Push Notifications</Label>
                  </div>
                  <Switch
                    checked={notificationSettings.push}
                    onCheckedChange={(checked) =>
                      setNotificationSettings(prev => ({ ...prev, push: checked }))
                    }
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Zap className="h-4 w-4" />
                    <Label>Escalation Enabled</Label>
                  </div>
                  <Switch
                    checked={notificationSettings.escalationEnabled}
                    onCheckedChange={(checked) =>
                      setNotificationSettings(prev => ({ ...prev, escalationEnabled: checked }))
                    }
                  />
                </div>
                
                {notificationSettings.escalationEnabled && (
                  <div className="pl-6 space-y-2">
                    <Label>Escalation Delays (minutes)</Label>
                    <div className="grid grid-cols-3 gap-2">
                      {notificationSettings.escalationDelays.map((delay, index) => (
                        <Input
                          key={index}
                          type="number"
                          placeholder={`Level ${index + 1}`}
                          value={delay}
                          onChange={(e) => {
                            const newDelays = [...notificationSettings.escalationDelays];
                            newDelays[index] = parseInt(e.target.value) || 0;
                            setNotificationSettings(prev => ({ ...prev, escalationDelays: newDelays }));
                          }}
                        />
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Alert Details Dialog */}
      <Dialog open={showAlertDialog} onOpenChange={setShowAlertDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Alert Details</DialogTitle>
          </DialogHeader>
          
          {selectedAlert && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Severity</Label>
                  <div className="mt-1">
                    {(() => {
                      const { color, icon: Icon, label } = getSeverityDisplay(selectedAlert.severity);
                      return (
                        <Badge className={color}>
                          <Icon className="h-3 w-3 mr-1" />
                          {label}
                        </Badge>
                      );
                    })()}
                  </div>
                </div>
                
                <div>
                  <Label>Status</Label>
                  <div className="mt-1">
                    {(() => {
                      const { color, icon: Icon, label } = getStatusDisplay(selectedAlert.alert_status);
                      return (
                        <Badge className={color}>
                          <Icon className="h-3 w-3 mr-1" />
                          {label}
                        </Badge>
                      );
                    })()}
                  </div>
                </div>
              </div>
              
              <div>
                <Label>Message</Label>
                <p className="mt-1 text-sm">{selectedAlert.message}</p>
              </div>
              
              {selectedAlert.threshold_value && selectedAlert.actual_value && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Threshold Value</Label>
                    <p className="mt-1 text-sm font-mono">{selectedAlert.threshold_value}</p>
                  </div>
                  <div>
                    <Label>Actual Value</Label>
                    <p className="mt-1 text-sm font-mono">{selectedAlert.actual_value}</p>
                  </div>
                </div>
              )}
              
              <div>
                <Label>Created</Label>
                <p className="mt-1 text-sm">{format(new Date(selectedAlert.created_at), 'PPpp')}</p>
              </div>
              
              {selectedAlert.resolution_notes && (
                <div>
                  <Label>Resolution Notes</Label>
                  <p className="mt-1 text-sm">{selectedAlert.resolution_notes}</p>
                </div>
              )}
            </div>
          )}
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAlertDialog(false)}>
              Close
            </Button>
            {selectedAlert && selectedAlert.alert_status === 'active' && (
              <>
                <Button
                  variant="outline"
                  onClick={() => {
                    if (selectedAlert) {
                      handleAcknowledgeAlert(selectedAlert.id);
                      setShowAlertDialog(false);
                    }
                  }}
                >
                  Acknowledge
                </Button>
                <Button
                  onClick={() => {
                    if (selectedAlert) {
                      handleResolveAlert(selectedAlert.id);
                      setShowAlertDialog(false);
                    }
                  }}
                >
                  Resolve
                </Button>
              </>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

/**
 * Individual Alert Card Component
 */
interface AlertCardProps {
  alert: AlertWithDetails;
  sensors: Sensor[];
  onAcknowledge: (alertId: string) => void;
  onResolve: (alertId: string) => void;
  onViewDetails: (alert: AlertWithDetails) => void;
  showAllStatuses?: boolean;
}

const AlertCard: React.FC<AlertCardProps> = ({
  alert,
  sensors,
  onAcknowledge,
  onResolve,
  onViewDetails,
  showAllStatuses = false
}) => {
  const sensor = sensors.find(s => s.id === alert.sensor_id);
  const { color: severityColor, icon: SeverityIcon } = getSeverityDisplay(alert.severity);
  const { color: statusColor, icon: StatusIcon } = getStatusDisplay(alert.alert_status);

  const getSeverityDisplay = (severity: TemperatureAlert['severity']) => {
    switch (severity) {
      case 'info':
        return { color: 'bg-blue-100 text-blue-800', icon: Info };
      case 'warning':
        return { color: 'bg-yellow-100 text-yellow-800', icon: AlertTriangle };
      case 'critical':
        return { color: 'bg-red-100 text-red-800', icon: AlertCircle };
      case 'emergency':
        return { color: 'bg-red-600 text-white', icon: Shield };
      default:
        return { color: 'bg-gray-100 text-gray-800', icon: Info };
    }
  };

  const getStatusDisplay = (status: TemperatureAlert['alert_status']) => {
    switch (status) {
      case 'active':
        return { color: 'bg-red-100 text-red-800', icon: AlertTriangle };
      case 'acknowledged':
        return { color: 'bg-yellow-100 text-yellow-800', icon: Eye };
      case 'investigating':
        return { color: 'bg-blue-100 text-blue-800', icon: Search };
      case 'resolved':
        return { color: 'bg-green-100 text-green-800', icon: CheckCircle };
      case 'dismissed':
        return { color: 'bg-gray-100 text-gray-800', icon: X };
      default:
        return { color: 'bg-gray-100 text-gray-800', icon: Clock };
    }
  };

  return (
    <Card className={`hover:shadow-md transition-shadow ${alert.severity === 'emergency' ? 'border-red-500' : ''}`}>
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <Badge className={severityColor}>
                <SeverityIcon className="h-3 w-3 mr-1" />
                {alert.severity}
              </Badge>
              
              {(showAllStatuses || alert.alert_status !== 'active') && (
                <Badge className={statusColor}>
                  <StatusIcon className="h-3 w-3 mr-1" />
                  {alert.alert_status}
                </Badge>
              )}
              
              {alert.escalationStatus === 'escalated' && (
                <Badge variant="destructive">
                  <Zap className="h-3 w-3 mr-1" />
                  Escalated
                </Badge>
              )}
            </div>
            
            <h3 className="font-medium mb-1">{alert.title}</h3>
            <p className="text-sm text-muted-foreground mb-2">{alert.message}</p>
            
            <div className="flex items-center space-x-4 text-xs text-muted-foreground">
              <span className="flex items-center">
                <Thermometer className="h-3 w-3 mr-1" />
                {sensor?.name || alert.sensor_id}
              </span>
              <span className="flex items-center">
                <Clock className="h-3 w-3 mr-1" />
                {alert.timeAgo}
              </span>
              {alert.threshold_value && alert.actual_value && (
                <span>
                  {alert.actual_value} / {alert.threshold_value}
                </span>
              )}
            </div>
          </div>
          
          <div className="flex items-center space-x-2 ml-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onViewDetails(alert)}
            >
              <Eye className="h-4 w-4" />
            </Button>
            
            {alert.alert_status === 'active' && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onAcknowledge(alert.id)}
                >
                  Acknowledge
                </Button>
                <Button
                  size="sm"
                  onClick={() => onResolve(alert.id)}
                >
                  Resolve
                </Button>
              </>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AlertsManagement;