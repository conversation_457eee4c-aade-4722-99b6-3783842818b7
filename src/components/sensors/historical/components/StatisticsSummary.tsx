/**
 * StatisticsSummary Component
 * 
 * Displays comprehensive statistics for selected sensors and date range
 * Shows min/max/average with timestamps, data points, and alerts
 */

import React from 'react';
import { 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  AlertTriangle, 
  Droplet, 
  Thermometer,
  Calendar,
  BarChart3
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import type { SensorStatistics } from '@/types/historical-dashboard';

interface StatisticsSummaryProps {
  statistics: SensorStatistics[];
  isLoading?: boolean;
  className?: string;
  compactView?: boolean;
}

export const StatisticsSummary: React.FC<StatisticsSummaryProps> = ({
  statistics,
  isLoading = false,
  className,
  compactView = false
}) => {
  if (isLoading) {
    return (
      <div className={cn("space-y-4", className)}>
        {[1, 2, 3].map(i => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (statistics.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <BarChart3 className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <p className="text-gray-500">No data available for selected sensors and date range</p>
        </CardContent>
      </Card>
    );
  }

  /**
   * Calculate overall statistics across all sensors
   */
  const overallStats = React.useMemo(() => {
    const allTemps = statistics.flatMap(stat => 
      Array(stat.temperature.dataPoints).fill(stat.temperature.average)
    );
    
    const allHumidity = statistics
      .filter(stat => stat.humidity)
      .flatMap(stat => 
        Array(stat.humidity!.dataPoints).fill(stat.humidity!.average)
      );

    const globalTempMin = Math.min(...statistics.map(s => s.temperature.min));
    const globalTempMax = Math.max(...statistics.map(s => s.temperature.max));
    const globalTempAvg = allTemps.length > 0 
      ? allTemps.reduce((sum, t) => sum + t, 0) / allTemps.length 
      : 0;

    const globalHumidityAvg = allHumidity.length > 0
      ? allHumidity.reduce((sum, h) => sum + h, 0) / allHumidity.length
      : undefined;

    const totalDataPoints = statistics.reduce((sum, stat) => sum + stat.temperature.dataPoints, 0);

    // Find min/max timestamps
    const minTempStat = statistics.find(s => s.temperature.min === globalTempMin);
    const maxTempStat = statistics.find(s => s.temperature.max === globalTempMax);

    return {
      temperature: {
        min: globalTempMin,
        max: globalTempMax,
        average: globalTempAvg,
        minTimestamp: minTempStat?.temperature.minTimestamp,
        maxTimestamp: maxTempStat?.temperature.maxTimestamp
      },
      humidity: globalHumidityAvg ? {
        average: globalHumidityAvg
      } : undefined,
      totalDataPoints,
      sensorsCount: statistics.length
    };
  }, [statistics]);

  /**
   * Format temperature with unit
   */
  const formatTemp = (temp: number) => `${temp.toFixed(1)}°F`;

  /**
   * Format humidity with unit  
   */
  const formatHumidity = (humidity: number) => `${humidity.toFixed(1)}%`;

  /**
   * Format timestamp for display
   */
  const formatTimestamp = (timestamp: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit'
    }).format(timestamp);
  };

  /**
   * Determine temperature status color
   */
  const getTempStatusColor = (temp: number) => {
    if (temp < 32) return 'text-blue-600'; // Freezing
    if (temp < 40) return 'text-blue-500'; // Cold storage
    if (temp < 80) return 'text-green-600'; // Normal
    if (temp < 90) return 'text-yellow-600'; // Warm
    return 'text-red-600'; // Hot
  };

  if (compactView) {
    return (
      <div className={cn("grid grid-cols-2 md:grid-cols-4 gap-4", className)}>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <TrendingDown className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Min</p>
                <p className={cn("font-semibold", getTempStatusColor(overallStats.temperature.min))}>
                  {formatTemp(overallStats.temperature.min)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Activity className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Avg</p>
                <p className={cn("font-semibold", getTempStatusColor(overallStats.temperature.average))}>
                  {formatTemp(overallStats.temperature.average)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-red-600" />
              <div>
                <p className="text-sm text-gray-600">Max</p>
                <p className={cn("font-semibold", getTempStatusColor(overallStats.temperature.max))}>
                  {formatTemp(overallStats.temperature.max)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4 text-gray-600" />
              <div>
                <p className="text-sm text-gray-600">Data Points</p>
                <p className="font-semibold">{overallStats.totalDataPoints.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Overall Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Overall Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {/* Temperature Min */}
            <div className="text-center">
              <div className="flex items-center justify-center gap-2 mb-2">
                <TrendingDown className="h-5 w-5 text-blue-600" />
                <span className="text-sm font-medium text-gray-600">Minimum</span>
              </div>
              <div className={cn("text-2xl font-bold", getTempStatusColor(overallStats.temperature.min))}>
                {formatTemp(overallStats.temperature.min)}
              </div>
              {overallStats.temperature.minTimestamp && (
                <div className="text-xs text-gray-500 mt-1">
                  {formatTimestamp(overallStats.temperature.minTimestamp)}
                </div>
              )}
            </div>

            {/* Temperature Average */}
            <div className="text-center">
              <div className="flex items-center justify-center gap-2 mb-2">
                <Activity className="h-5 w-5 text-green-600" />
                <span className="text-sm font-medium text-gray-600">Average</span>
              </div>
              <div className={cn("text-2xl font-bold", getTempStatusColor(overallStats.temperature.average))}>
                {formatTemp(overallStats.temperature.average)}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                {overallStats.sensorsCount} sensors
              </div>
            </div>

            {/* Temperature Max */}
            <div className="text-center">
              <div className="flex items-center justify-center gap-2 mb-2">
                <TrendingUp className="h-5 w-5 text-red-600" />
                <span className="text-sm font-medium text-gray-600">Maximum</span>
              </div>
              <div className={cn("text-2xl font-bold", getTempStatusColor(overallStats.temperature.max))}>
                {formatTemp(overallStats.temperature.max)}
              </div>
              {overallStats.temperature.maxTimestamp && (
                <div className="text-xs text-gray-500 mt-1">
                  {formatTimestamp(overallStats.temperature.maxTimestamp)}
                </div>
              )}
            </div>

            {/* Data Points */}
            <div className="text-center">
              <div className="flex items-center justify-center gap-2 mb-2">
                <BarChart3 className="h-5 w-5 text-gray-600" />
                <span className="text-sm font-medium text-gray-600">Data Points</span>
              </div>
              <div className="text-2xl font-bold text-gray-900">
                {overallStats.totalDataPoints.toLocaleString()}
              </div>
              {overallStats.humidity && (
                <div className="text-xs text-gray-500 mt-1 flex items-center justify-center gap-1">
                  <Droplet className="h-3 w-3" />
                  {formatHumidity(overallStats.humidity.average)} RH
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Individual Sensor Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {statistics.map((sensorStat) => (
          <Card key={sensorStat.sensorId} className="border-l-4 border-l-blue-500">
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Thermometer className="h-4 w-4" />
                  <span className="truncate">{sensorStat.sensorName}</span>
                </div>
                <Badge variant="secondary" className="text-xs">
                  {sensorStat.temperature.dataPoints} pts
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {/* Temperature Stats */}
                <div className="grid grid-cols-3 gap-2 text-center">
                  <div>
                    <div className="text-xs text-gray-500 mb-1">Min</div>
                    <div className={cn("font-semibold text-sm", getTempStatusColor(sensorStat.temperature.min))}>
                      {formatTemp(sensorStat.temperature.min)}
                    </div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500 mb-1">Avg</div>
                    <div className={cn("font-semibold text-sm", getTempStatusColor(sensorStat.temperature.average))}>
                      {formatTemp(sensorStat.temperature.average)}
                    </div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500 mb-1">Max</div>
                    <div className={cn("font-semibold text-sm", getTempStatusColor(sensorStat.temperature.max))}>
                      {formatTemp(sensorStat.temperature.max)}
                    </div>
                  </div>
                </div>

                {/* Humidity Stats if available */}
                {sensorStat.humidity && (
                  <div className="border-t pt-3">
                    <div className="flex items-center gap-2 mb-2">
                      <Droplet className="h-3 w-3 text-blue-500" />
                      <span className="text-xs font-medium text-gray-600">Humidity</span>
                    </div>
                    <div className="grid grid-cols-3 gap-2 text-center">
                      <div>
                        <div className="text-xs text-gray-500 mb-1">Min</div>
                        <div className="font-semibold text-sm">
                          {formatHumidity(sensorStat.humidity.min)}
                        </div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-500 mb-1">Avg</div>
                        <div className="font-semibold text-sm">
                          {formatHumidity(sensorStat.humidity.average)}
                        </div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-500 mb-1">Max</div>
                        <div className="font-semibold text-sm">
                          {formatHumidity(sensorStat.humidity.max)}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Time Range */}
                <div className="text-xs text-gray-500 flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  <span>
                    {formatTimestamp(sensorStat.period.start)} - {formatTimestamp(sensorStat.period.end)}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default StatisticsSummary;