/**
 * HistoricalDashboard Container Component
 * 
 * Main container for the historical temperature dashboard
 * Orchestrates data fetching, filtering, and display components
 */

import React, { useEffect, useState } from 'react';
import { 
  RefreshCw, 
  Download, 
  Settings, 
  BarChart3, 
  TrendingUp, 
  AlertCircle,
  Loader2
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';

// Import our new components
import DateRangeSelector from './controls/DateRangeSelector';
import SensorSelector from './controls/SensorSelector';
import StatisticsSummary from './components/StatisticsSummary';

// Import hooks
import { useHistoricalData } from '@/hooks/useHistoricalData';
import { useTemperatureDashboard } from '@/hooks/useTemperatureDashboard';

import { cn } from '@/lib/utils';
import type { DateRange } from '@/types/historical-dashboard';

interface HistoricalDashboardProps {
  className?: string;
  initialDateRange?: DateRange;
  initialSensors?: string[];
}

export const HistoricalDashboard: React.FC<HistoricalDashboardProps> = ({
  className,
  initialDateRange,
  initialSensors = []
}) => {
  // Get current sensors for selection
  const { 
    sensorStatuses, 
    loading: sensorsLoading,
    error: sensorsError,
    refreshData: refreshSensors
  } = useTemperatureDashboard();

  // Historical data management
  const {
    data: historicalData,
    statistics,
    isLoading: historicalLoading,
    error: historicalError,
    filters,
    setDateRange,
    setSelectedSensors,
    setChartType,
    refreshData: refreshHistoricalData,
    exportData,
    shouldUseAggregation,
    totalPoints,
    lastUpdate
  } = useHistoricalData({
    autoRefreshInterval: 300000, // 5 minutes
    maxDataPoints: 10000,
    enableAggregation: true
  });

  // UI state
  const [activeTab, setActiveTab] = useState<'overview' | 'charts' | 'data'>('overview');
  const [isExporting, setIsExporting] = useState(false);

  /**
   * Initialize with available sensors on mount
   */
  useEffect(() => {
    if (sensorStatuses.length > 0 && filters.selectedSensors.length === 0) {
      // Auto-select first 3 online sensors or all if less than 3
      const onlineSensors = sensorStatuses
        .filter(status => status.status === 'online')
        .slice(0, 3)
        .map(status => status.sensor.id);
      
      if (onlineSensors.length > 0) {
        setSelectedSensors(onlineSensors);
      }
    }
  }, [sensorStatuses, filters.selectedSensors.length, setSelectedSensors]);

  /**
   * Set initial date range if provided
   */
  useEffect(() => {
    if (initialDateRange) {
      setDateRange(initialDateRange);
    }
  }, [initialDateRange, setDateRange]);

  /**
   * Handle CSV export
   */
  const handleExport = async () => {
    try {
      setIsExporting(true);
      await exportData('csv');
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  /**
   * Handle refresh all data
   */
  const handleRefreshAll = async () => {
    await Promise.all([
      refreshSensors(),
      refreshHistoricalData()
    ]);
  };

  /**
   * Convert sensor statuses to sensor info for selector
   */
  const availableSensors = sensorStatuses.map(status => ({
    id: status.sensor.id,
    name: status.sensor.name,
    location: status.sensor.location,
    isOnline: status.status === 'online',
    batteryLevel: status.batteryLevel,
    currentTemperature: status.latestReading?.temperature,
    signalStrength: status.signalStrength
  }));

  /**
   * Get dashboard status info
   */
  const getDashboardStatus = () => {
    if (sensorsError || historicalError) {
      return {
        status: 'error',
        message: typeof sensorsError === 'string' ? sensorsError : 
                typeof historicalError === 'object' ? historicalError.message : 
                'Unknown error'
      };
    }
    
    if (sensorsLoading || historicalLoading) {
      return {
        status: 'loading',
        message: 'Loading dashboard data...'
      };
    }
    
    if (filters.selectedSensors.length === 0) {
      return {
        status: 'no-sensors',
        message: 'Select sensors to view historical data'
      };
    }
    
    if (historicalData.length === 0) {
      return {
        status: 'no-data',
        message: 'No historical data available for selected date range'
      };
    }
    
    return {
      status: 'ready',
      message: `${totalPoints.toLocaleString()} data points loaded`
    };
  };

  const dashboardStatus = getDashboardStatus();

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Historical Temperature Dashboard</h1>
          <p className="text-gray-600">
            Analyze temperature trends and statistics over time
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          {lastUpdate && (
            <span className="text-sm text-gray-500">
              Updated {lastUpdate.toLocaleTimeString()}
            </span>
          )}
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefreshAll}
            disabled={sensorsLoading || historicalLoading}
          >
            {sensorsLoading || historicalLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            Refresh
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleExport}
            disabled={historicalData.length === 0 || isExporting}
          >
            {isExporting ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Download className="h-4 w-4" />
            )}
            Export CSV
          </Button>
        </div>
      </div>

      {/* Controls */}
      <Card>
        <CardContent className="p-6">
          <div className="grid gap-6 md:grid-cols-2">
            <DateRangeSelector
              value={filters.dateRange}
              onChange={setDateRange}
            />
            
            <SensorSelector
              sensors={availableSensors}
              selectedSensorIds={filters.selectedSensors}
              onChange={setSelectedSensors}
              showOfflineSensors={true}
            />
          </div>
        </CardContent>
      </Card>

      {/* Status Alert */}
      {dashboardStatus.status === 'error' && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{dashboardStatus.message}</AlertDescription>
        </Alert>
      )}

      {dashboardStatus.status === 'no-sensors' && (
        <Alert>
          <Settings className="h-4 w-4" />
          <AlertDescription>{dashboardStatus.message}</AlertDescription>
        </Alert>
      )}

      {/* Performance Warning */}
      {shouldUseAggregation && totalPoints > 1000 && (
        <Alert>
          <TrendingUp className="h-4 w-4" />
          <AlertDescription>
            Large dataset detected ({totalPoints.toLocaleString()} points). 
            Using data aggregation for optimal performance.
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content */}
      {dashboardStatus.status === 'ready' && (
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as typeof activeTab)}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="charts" className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Charts
            </TabsTrigger>
            <TabsTrigger value="data" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Data
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {/* Quick Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {filters.selectedSensors.length}
                  </div>
                  <div className="text-sm text-gray-600">Sensors</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {totalPoints.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-600">Data Points</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {Math.ceil((filters.dateRange.end.getTime() - filters.dateRange.start.getTime()) / (1000 * 60 * 60 * 24))}
                  </div>
                  <div className="text-sm text-gray-600">Days</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-orange-600">
                    {statistics.length}
                  </div>
                  <div className="text-sm text-gray-600">Statistics</div>
                </CardContent>
              </Card>
            </div>

            {/* Statistics Summary */}
            <StatisticsSummary
              statistics={statistics}
              isLoading={historicalLoading}
            />
          </TabsContent>

          {/* Charts Tab */}
          <TabsContent value="charts" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Temperature Charts</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center text-gray-500">
                  Chart components will be implemented next
                  <br />
                  ({historicalData.length} data points ready for visualization)
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Data Tab */}
          <TabsContent value="data" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Raw Data</span>
                  <Badge variant="secondary">
                    {historicalData.length} records
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="max-h-96 overflow-y-auto">
                  <table className="w-full text-sm">
                    <thead className="bg-gray-50 sticky top-0">
                      <tr>
                        <th className="p-2 text-left">Timestamp</th>
                        <th className="p-2 text-left">Sensor</th>
                        <th className="p-2 text-left">Temperature</th>
                        <th className="p-2 text-left">Humidity</th>
                      </tr>
                    </thead>
                    <tbody>
                      {historicalData.slice(0, 100).map((point, index) => (
                        <tr key={index} className="border-t">
                          <td className="p-2">
                            {point.timestamp.toLocaleString()}
                          </td>
                          <td className="p-2">{point.sensorName}</td>
                          <td className="p-2">{point.temperature.toFixed(1)}°F</td>
                          <td className="p-2">
                            {point.humidity ? `${point.humidity.toFixed(1)}%` : '-'}
                          </td>
                        </tr>
                      ))}
                      {historicalData.length > 100 && (
                        <tr>
                          <td colSpan={4} className="p-4 text-center text-gray-500">
                            ... and {historicalData.length - 100} more records
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}

      {/* Loading State */}
      {dashboardStatus.status === 'loading' && (
        <Card>
          <CardContent className="p-12 text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-gray-600">{dashboardStatus.message}</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default HistoricalDashboard;