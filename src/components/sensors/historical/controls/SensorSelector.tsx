/**
 * SensorSelector Component
 * 
 * Multi-select dropdown for choosing sensors to display in historical dashboard
 * Built with existing UI patterns and TempStick sensor data
 */

import React, { useState, useMemo } from 'react';
import { Check, ChevronDown, Search, Thermometer, Wifi, WifiOff, Battery } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface SensorInfo {
  id: string;
  name: string;
  location: string;
  isOnline: boolean;
  batteryLevel?: number;
  currentTemperature?: number;
  signalStrength?: number;
}

interface SensorSelectorProps {
  sensors: SensorInfo[];
  selectedSensorIds: string[];
  onChange: (sensorIds: string[]) => void;
  disabled?: boolean;
  className?: string;
  placeholder?: string;
  showOfflineSensors?: boolean;
}

export const SensorSelector: React.FC<SensorSelectorProps> = ({
  sensors,
  selectedSensorIds,
  onChange,
  disabled = false,
  className,
  placeholder = "Select sensors...",
  showOfflineSensors = true
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isOpen, setIsOpen] = useState(false);

  /**
   * Filter sensors based on search term and online status
   */
  const filteredSensors = useMemo(() => {
    let filtered = sensors;

    // Filter by online status if needed
    if (!showOfflineSensors) {
      filtered = filtered.filter(sensor => sensor.isOnline);
    }

    // Filter by search term
    if (searchTerm.trim()) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(sensor =>
        sensor.name.toLowerCase().includes(term) ||
        sensor.location.toLowerCase().includes(term) ||
        sensor.id.toLowerCase().includes(term)
      );
    }

    return filtered;
  }, [sensors, searchTerm, showOfflineSensors]);

  /**
   * Handle sensor selection toggle
   */
  const handleSensorToggle = (sensorId: string) => {
    const newSelection = selectedSensorIds.includes(sensorId)
      ? selectedSensorIds.filter(id => id !== sensorId)
      : [...selectedSensorIds, sensorId];
    
    onChange(newSelection);
  };

  /**
   * Handle select all / deselect all
   */
  const handleSelectAll = () => {
    const allFilteredIds = filteredSensors.map(s => s.id);
    const allSelected = allFilteredIds.every(id => selectedSensorIds.includes(id));
    
    if (allSelected) {
      // Deselect all filtered sensors
      onChange(selectedSensorIds.filter(id => !allFilteredIds.includes(id)));
    } else {
      // Select all filtered sensors
      const newSelection = [...new Set([...selectedSensorIds, ...allFilteredIds])];
      onChange(newSelection);
    }
  };

  /**
   * Get display text for selected sensors
   */
  const getDisplayText = () => {
    if (selectedSensorIds.length === 0) {
      return placeholder;
    }
    
    if (selectedSensorIds.length === 1) {
      const sensor = sensors.find(s => s.id === selectedSensorIds[0]);
      return sensor?.name || 'Unknown Sensor';
    }
    
    if (selectedSensorIds.length === sensors.length) {
      return 'All Sensors';
    }
    
    return `${selectedSensorIds.length} sensors selected`;
  };

  /**
   * Get sensor status indicator
   */
  const getSensorStatusIcon = (sensor: SensorInfo) => {
    if (!sensor.isOnline) {
      return <WifiOff className="h-3 w-3 text-red-500" />;
    }
    
    if (sensor.batteryLevel !== undefined && sensor.batteryLevel < 20) {
      return <Battery className="h-3 w-3 text-yellow-500" />;
    }
    
    return <Wifi className="h-3 w-3 text-green-500" />;
  };

  /**
   * Format temperature display
   */
  const formatTemperature = (temp?: number) => {
    if (temp === undefined) return '';
    return `${temp.toFixed(1)}°F`;
  };

  return (
    <div className={cn("flex flex-col gap-2", className)}>
      <div className="flex items-center gap-2 text-sm text-gray-600">
        <Thermometer className="h-4 w-4" />
        <span>Sensors:</span>
      </div>

      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="outline" 
            disabled={disabled}
            className="min-w-[250px] justify-between"
          >
            <span className="truncate">{getDisplayText()}</span>
            <ChevronDown className="h-4 w-4 opacity-50" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-80">
          {/* Search */}
          <div className="p-2">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search sensors..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>

          {/* Select All / Deselect All */}
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={handleSelectAll}>
            <div className="flex items-center justify-between w-full">
              <span>
                {filteredSensors.every(s => selectedSensorIds.includes(s.id))
                  ? 'Deselect All'
                  : 'Select All'
                }
              </span>
              <Badge variant="secondary" className="text-xs">
                {filteredSensors.length}
              </Badge>
            </div>
          </DropdownMenuItem>
          <DropdownMenuSeparator />

          {/* Sensor List */}
          <div className="max-h-64 overflow-y-auto">
            {filteredSensors.length === 0 ? (
              <div className="p-4 text-center text-gray-500 text-sm">
                {searchTerm ? 'No sensors match your search' : 'No sensors available'}
              </div>
            ) : (
              filteredSensors.map((sensor) => (
                <DropdownMenuItem
                  key={sensor.id}
                  onClick={() => handleSensorToggle(sensor.id)}
                  className="flex items-center justify-between p-3"
                >
                  <div className="flex items-center gap-3 flex-1">
                    {/* Checkbox */}
                    <div className="flex items-center justify-center w-4 h-4 border rounded border-gray-300">
                      {selectedSensorIds.includes(sensor.id) && (
                        <Check className="h-3 w-3 text-blue-600" />
                      )}
                    </div>

                    {/* Status Icon */}
                    {getSensorStatusIcon(sensor)}

                    {/* Sensor Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-sm truncate">
                          {sensor.name}
                        </span>
                        {sensor.currentTemperature && (
                          <Badge variant="outline" className="text-xs">
                            {formatTemperature(sensor.currentTemperature)}
                          </Badge>
                        )}
                      </div>
                      <div className="text-xs text-gray-500 truncate">
                        {sensor.location}
                      </div>
                    </div>
                  </div>

                  {/* Battery Level */}
                  {sensor.batteryLevel !== undefined && (
                    <div className="flex items-center gap-1 ml-2">
                      <Battery 
                        className={cn(
                          "h-3 w-3",
                          sensor.batteryLevel < 20 ? "text-red-500" : 
                          sensor.batteryLevel < 50 ? "text-yellow-500" : 
                          "text-green-500"
                        )}
                      />
                      <span className="text-xs text-gray-500">
                        {sensor.batteryLevel}%
                      </span>
                    </div>
                  )}
                </DropdownMenuItem>
              ))
            )}
          </div>

          {/* Selected Count */}
          {selectedSensorIds.length > 0 && (
            <>
              <DropdownMenuSeparator />
              <div className="p-2 text-center text-xs text-gray-500">
                {selectedSensorIds.length} of {sensors.length} sensors selected
              </div>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Selected Sensors Preview */}
      {selectedSensorIds.length > 0 && selectedSensorIds.length <= 3 && (
        <div className="flex flex-wrap gap-1">
          {selectedSensorIds.map(id => {
            const sensor = sensors.find(s => s.id === id);
            return sensor ? (
              <Badge key={id} variant="secondary" className="text-xs">
                {sensor.name}
              </Badge>
            ) : null;
          })}
        </div>
      )}
    </div>
  );
};

export default SensorSelector;