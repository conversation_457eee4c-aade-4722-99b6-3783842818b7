/**
 * DateRangeSelector Component
 * 
 * Provides predefined and custom date range selection for historical dashboard
 * Built with Radix UI components following existing patterns
 */

import React, { useState } from 'react';
import { Calendar, ChevronDown, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';
import type { DateRange, DateRangePreset } from '@/types/historical-dashboard';
import { DATE_RANGE_PRESETS } from '@/types/historical-dashboard';

interface DateRangeSelectorProps {
  value: DateRange;
  onChange: (range: DateRange) => void;
  presets?: DateRangePreset[];
  disabled?: boolean;
  className?: string;
}

export const DateRangeSelector: React.FC<DateRangeSelectorProps> = ({
  value,
  onChange,
  presets = DATE_RANGE_PRESETS,
  disabled = false,
  className
}) => {
  const [isCustomRangeOpen, setIsCustomRangeOpen] = useState(false);
  const [customRange, setCustomRange] = useState<DateRange>(value);

  /**
   * Handle preset selection
   */
  const handlePresetSelect = (preset: DateRangePreset) => {
    // Update preset values to current time
    const now = new Date();
    const updatedRange = {
      start: new Date(now.getTime() + (preset.value.start.getTime() - preset.value.end.getTime())),
      end: now
    };
    onChange(updatedRange);
  };

  /**
   * Handle custom range selection
   */
  const handleCustomRangeApply = () => {
    onChange(customRange);
    setIsCustomRangeOpen(false);
  };

  /**
   * Format date range for display
   */
  const formatDateRange = (range: DateRange): string => {
    const formatOptions: Intl.DateTimeFormatOptions = {
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit'
    };

    // Check if it matches a preset
    const matchingPreset = presets.find(preset => {
      const timeDiff = Math.abs(range.end.getTime() - range.start.getTime());
      const presetTimeDiff = Math.abs(preset.value.end.getTime() - preset.value.start.getTime());
      return Math.abs(timeDiff - presetTimeDiff) < 60000; // Within 1 minute
    });

    if (matchingPreset) {
      return matchingPreset.label;
    }

    // Custom range formatting
    const start = range.start.toLocaleDateString('en-US', formatOptions);
    const end = range.end.toLocaleDateString('en-US', formatOptions);
    
    if (range.start.toDateString() === range.end.toDateString()) {
      return `${start} - ${range.end.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' })}`;
    }
    
    return `${start} - ${end}`;
  };

  const currentRangeLabel = formatDateRange(value);

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <div className="flex items-center gap-2 text-sm text-gray-600">
        <Clock className="h-4 w-4" />
        <span>Time Range:</span>
      </div>
      
      {/* Preset Dropdown */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="outline" 
            disabled={disabled}
            className="min-w-[200px] justify-between"
          >
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              {currentRangeLabel}
            </div>
            <ChevronDown className="h-4 w-4 opacity-50" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-56">
          <DropdownMenuLabel>Quick Select</DropdownMenuLabel>
          <DropdownMenuSeparator />
          
          {presets.map((preset) => (
            <DropdownMenuItem
              key={preset.key}
              onClick={() => handlePresetSelect(preset)}
              className="flex items-center justify-between"
            >
              <span>{preset.label}</span>
              <Clock className="h-3 w-3 opacity-50" />
            </DropdownMenuItem>
          ))}
          
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => setIsCustomRangeOpen(true)}>
            <Calendar className="h-4 w-4 mr-2" />
            Custom Range...
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Custom Range Popover */}
      <Popover open={isCustomRangeOpen} onOpenChange={setIsCustomRangeOpen}>
        <PopoverTrigger asChild>
          <div /> {/* Hidden trigger - controlled via dropdown */}
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <div className="p-4 space-y-4">
            <div className="text-sm font-medium">Select Custom Date Range</div>
            
            <div className="grid grid-cols-2 gap-4">
              {/* Start Date */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Start Date</label>
                <CalendarComponent
                  mode="single"
                  selected={customRange.start}
                  onSelect={(date) => date && setCustomRange(prev => ({ ...prev, start: date }))}
                  disabled={(date) => date > new Date() || date < new Date('2020-01-01')}
                  initialFocus
                />
              </div>
              
              {/* End Date */}
              <div className="space-y-2">
                <label className="text-sm font-medium">End Date</label>
                <CalendarComponent
                  mode="single"
                  selected={customRange.end}
                  onSelect={(date) => date && setCustomRange(prev => ({ ...prev, end: date }))}
                  disabled={(date) => date > new Date() || date < customRange.start}
                />
              </div>
            </div>
            
            {/* Time Selection */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Start Time</label>
                <input
                  type="time"
                  value={customRange.start.toTimeString().slice(0, 5)}
                  onChange={(e) => {
                    const [hours, minutes] = e.target.value.split(':');
                    const newStart = new Date(customRange.start);
                    newStart.setHours(parseInt(hours), parseInt(minutes), 0, 0);
                    setCustomRange(prev => ({ ...prev, start: newStart }));
                  }}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">End Time</label>
                <input
                  type="time"
                  value={customRange.end.toTimeString().slice(0, 5)}
                  onChange={(e) => {
                    const [hours, minutes] = e.target.value.split(':');
                    const newEnd = new Date(customRange.end);
                    newEnd.setHours(parseInt(hours), parseInt(minutes), 0, 0);
                    setCustomRange(prev => ({ ...prev, end: newEnd }));
                  }}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            
            {/* Action Buttons */}
            <div className="flex justify-end gap-2 pt-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsCustomRangeOpen(false)}
              >
                Cancel
              </Button>
              <Button
                size="sm"
                onClick={handleCustomRangeApply}
                disabled={customRange.start >= customRange.end}
              >
                Apply Range
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>

      {/* Range Info */}
      <div className="text-xs text-gray-500">
        {Math.ceil((value.end.getTime() - value.start.getTime()) / (1000 * 60 * 60 * 24))} days
      </div>
    </div>
  );
};

export default DateRangeSelector;