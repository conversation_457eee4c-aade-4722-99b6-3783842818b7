/**
 * Individual Sensor Status Tile
 * 
 * Displays current sensor status, latest temperature reading,
 * alerts, and provides selection capability for bulk operations.
 */

import React from 'react';
import { Card, CardContent, CardHeader } from '../ui/card';
import { Badge } from '../ui/badge';
import { But<PERSON> } from '../ui/button';
import { Checkbox } from '../ui/checkbox';
import { 
  Thermometer, 
  Droplets, 
  AlertTriangle, 
  WifiOff, 
  MapPin,
  Clock
} from 'lucide-react';
import { useThemeAwareStyles } from '../../contexts/ThemeContext';
import type { SensorStatus } from '../../types/tempstick';

interface SensorTileProps {
  sensorStatus: SensorStatus;
  selected: boolean;
  onSelectionChange: (selected: boolean) => void;
}

export function SensorTile({ 
  sensorStatus, 
  selected, 
  onSelectionChange 
}: SensorTileProps) {
  const { sensor, latestReading, status, activeAlerts } = sensorStatus;
  const styles = useThemeAwareStyles();

  const getStatusColor = (status: SensorStatus['status']) => {
    switch (status) {
      case 'online':
        return styles.sensor.online;
      case 'warning':
        return styles.sensor.warning;
      case 'critical':
        return styles.sensor.critical;
      case 'offline':
        return styles.sensor.offline;
      default:
        return styles.sensor.offline;
    }
  };

  const getStatusIcon = (status: SensorStatus['status']) => {
    const iconColor = styles.isDark ? 'text-current' : '';
    switch (status) {
      case 'online':
        return <Thermometer className={`h-4 w-4 ${iconColor || 'text-green-600'}`} />;
      case 'warning':
        return <AlertTriangle className={`h-4 w-4 ${iconColor || 'text-yellow-600'}`} />;
      case 'critical':
        return <AlertTriangle className={`h-4 w-4 ${iconColor || 'text-red-600'}`} />;
      case 'offline':
        return <WifiOff className={`h-4 w-4 ${iconColor || 'text-gray-600'}`} />;
      default:
        return <WifiOff className={`h-4 w-4 ${iconColor || 'text-gray-600'}`} />;
    }
  };

  const formatTemperature = (temp: number) => {
    return `${temp.toFixed(1)}°F`;
  };

  const formatHumidity = (humidity: number | null) => {
    return humidity ? `${humidity.toFixed(1)}%` : '--';
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diff = now.getTime() - time.getTime();
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Just now';
    if (minutes === 1) return '1 min ago';
    if (minutes < 60) return `${minutes} min ago`;
    
    const hours = Math.floor(minutes / 60);
    if (hours === 1) return '1 hour ago';
    if (hours < 24) return `${hours} hours ago`;
    
    return time.toLocaleDateString();
  };

  const isInThreshold = (temp: number) => {
    const { temp_min_threshold, temp_max_threshold } = sensor;
    
    if (temp_min_threshold && temp < temp_min_threshold) return false;
    if (temp_max_threshold && temp > temp_max_threshold) return false;
    
    // Check storage area requirements
    if (sensor.storage_areas) {
      const { required_temp_min, required_temp_max } = sensor.storage_areas;
      if (required_temp_min && temp < required_temp_min) return false;
      if (required_temp_max && temp > required_temp_max) return false;
    }
    
    return true;
  };

  return (
    <Card className={`transition-all duration-200 hover:shadow-md ${
      selected ? 'ring-2 ring-blue-500' : ''
    }`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <Checkbox
              checked={selected}
              onCheckedChange={onSelectionChange}
              className="mt-1"
            />
            <div>
              <h3 className={`font-semibold text-lg ${styles.text.primary} leading-none`}>
                {sensor.name}
              </h3>
              <div className={`flex items-center text-sm ${styles.text.secondary} mt-1`}>
                <MapPin className="h-3 w-3 mr-1" />
                <span>{sensor.location}</span>
              </div>
              {sensor.storage_areas && (
                <div className={`flex items-center text-xs ${styles.text.tertiary} mt-1`}>
                  <span>Storage: {sensor.storage_areas.name}</span>
                  {sensor.storage_areas.haccp_control_point && (
                    <Badge variant="secondary" className="ml-2 text-xs">
                      HACCP CCP
                    </Badge>
                  )}
                </div>
              )}
            </div>
          </div>
          
          <div className="flex flex-col items-end space-y-1">
            <Badge className={getStatusColor(status)}>
              <div className="flex items-center space-x-1">
                {getStatusIcon(status)}
                <span className="capitalize">{status}</span>
              </div>
            </Badge>
            
            {activeAlerts.length > 0 && (
              <Badge variant="destructive" className="text-xs">
                {activeAlerts.length} alert{activeAlerts.length > 1 ? 's' : ''}
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {latestReading ? (
          <div className="space-y-4">
            {/* Temperature and Humidity */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1">
                <div className={`flex items-center text-sm ${styles.text.secondary}`}>
                  <Thermometer className="h-3 w-3 mr-1" />
                  <span>Temperature</span>
                </div>
                <div className={`text-xl font-semibold ${
                  isInThreshold(latestReading.temperature) 
                    ? styles.text.primary
                    : styles.isDark ? 'text-red-400' : 'text-red-600'
                }`}>
                  {formatTemperature(latestReading.temperature)}
                </div>
              </div>

              <div className="space-y-1">
                <div className={`flex items-center text-sm ${styles.text.secondary}`}>
                  <Droplets className="h-3 w-3 mr-1" />
                  <span>Humidity</span>
                </div>
                <div className={`text-xl font-semibold ${styles.text.primary}`}>
                  {formatHumidity(latestReading.humidity)}
                </div>
              </div>
            </div>

            {/* Thresholds */}
            {(sensor.temp_min_threshold || sensor.temp_max_threshold || sensor.storage_areas) && (
              <div className="space-y-2">
                <div className={`text-xs ${styles.text.muted}`}>Thresholds:</div>
                <div className="flex flex-wrap gap-2 text-xs">
                  {sensor.temp_min_threshold && (
                    <Badge variant="outline" className="text-xs">
                      Min: {formatTemperature(sensor.temp_min_threshold)}
                    </Badge>
                  )}
                  {sensor.temp_max_threshold && (
                    <Badge variant="outline" className="text-xs">
                      Max: {formatTemperature(sensor.temp_max_threshold)}
                    </Badge>
                  )}
                  {sensor.storage_areas?.required_temp_min && (
                    <Badge variant="outline" className="text-xs">
                      Storage Min: {formatTemperature(sensor.storage_areas.required_temp_min)}
                    </Badge>
                  )}
                  {sensor.storage_areas?.required_temp_max && (
                    <Badge variant="outline" className="text-xs">
                      Storage Max: {formatTemperature(sensor.storage_areas.required_temp_max)}
                    </Badge>
                  )}
                </div>
              </div>
            )}

            {/* Last Reading Time */}
            <div className={`flex items-center justify-between text-xs ${styles.text.muted}`}>
              <div className="flex items-center">
                <Clock className="h-3 w-3 mr-1" />
                <span>Updated {formatTimeAgo(latestReading.reading_timestamp)}</span>
              </div>
              
              {latestReading.alert_triggered && (
                <Badge variant="destructive" className="text-xs">
                  Alert Triggered
                </Badge>
              )}
            </div>

            {/* Active Alerts Preview */}
            {activeAlerts.length > 0 && (
              <div className="space-y-2">
                <div className={`text-xs ${styles.text.muted}`}>Recent Alerts:</div>
                <div className="space-y-1">
                  {activeAlerts.slice(0, 2).map(alert => (
                    <div key={alert.id} className="flex items-center justify-between text-xs">
                      <span className={`${styles.isDark ? 'text-red-400' : 'text-red-600'} capitalize`}>
                        {alert.alert_type.replace('_', ' ')}
                      </span>
                      <Badge 
                        variant="destructive" 
                        className={`text-xs ${
                          alert.severity === 'critical' 
                            ? 'bg-red-600' 
                            : alert.severity === 'high'
                            ? 'bg-red-500'
                            : 'bg-yellow-500'
                        }`}
                      >
                        {alert.severity}
                      </Badge>
                    </div>
                  ))}
                  {activeAlerts.length > 2 && (
                    <div className={`text-xs ${styles.text.muted}`}>
                      +{activeAlerts.length - 2} more alerts
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-6">
            <WifiOff className={`mx-auto h-8 w-8 ${styles.text.muted} mb-2`} />
            <div className={`text-sm ${styles.text.secondary}`}>No recent readings</div>
            <div className={`text-xs ${styles.text.muted} mt-1`}>
              Check sensor connection
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}