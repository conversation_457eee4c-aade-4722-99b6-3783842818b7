/**
 * Simple TempStick Dashboard Component
 * Displays real-time sensor data from TempStick API
 */

import React, { useState, useEffect } from 'react';
import { Thermometer, Droplet, Battery, Wifi, Clock, RefreshCw } from 'lucide-react';
import { tempStickService } from '../../lib/tempstick-service';

interface SensorData {
  sensor_id: string;
  sensor_name: string;
  location: string;
  status: 'online' | 'offline';
  last_temp: number;
  last_humidity: number;
  last_reading: string;
  battery_level: number;
  rssi?: number;
}

export function SimpleTempStickDashboard() {
  const [sensors, setSensors] = useState<SensorData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastSync, setLastSync] = useState<Date | null>(null);

  const fetchSensors = async () => {
    try {
      setError(null);
      const sensorData = await tempStickService.getAllSensors();
      setSensors(sensorData);
      setLastSync(new Date());
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch sensors');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSensors();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchSensors, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const formatTemperature = (celsius: number) => {
    const fahrenheit = (celsius * 9/5) + 32;
    return {
      celsius: celsius.toFixed(1),
      fahrenheit: fahrenheit.toFixed(1)
    };
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffMinutes < 1) return 'Just now';
    if (diffMinutes < 60) return `${diffMinutes}m ago`;
    
    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    
    return date.toLocaleDateString();
  };

  const getStatusColor = (status: string, lastReading: string) => {
    if (status === 'offline') return 'text-red-600 bg-red-50 border-red-200';
    
    const lastReadingDate = new Date(lastReading);
    const minutesAgo = (Date.now() - lastReadingDate.getTime()) / (1000 * 60);
    
    if (minutesAgo > 60) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    return 'text-green-600 bg-green-50 border-green-200';
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center">
          <RefreshCw className="w-6 h-6 animate-spin mr-2" />
          <span>Loading temperature sensors...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">TempStick Temperature Monitoring</h1>
          <p className="text-gray-600 mt-1">
            Real-time temperature monitoring for seafood storage areas
          </p>
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="text-sm text-gray-500">
            {lastSync && (
              <>
                Last sync: {lastSync.toLocaleTimeString()}
              </>
            )}
          </div>
          
          <button
            onClick={fetchSensors}
            disabled={loading}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="text-red-600 font-medium">Error loading sensors:</div>
            <div className="text-red-600 ml-2">{error}</div>
          </div>
        </div>
      )}

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <Thermometer className="w-8 h-8 text-blue-600" />
            <div className="ml-4">
              <div className="text-2xl font-bold">{sensors.length}</div>
              <div className="text-gray-600">Total Sensors</div>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <div className="w-3 h-3 bg-green-600 rounded-full"></div>
            </div>
            <div className="ml-4">
              <div className="text-2xl font-bold">
                {sensors.filter(s => s.status === 'online').length}
              </div>
              <div className="text-gray-600">Online</div>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <Battery className="w-8 h-8 text-green-600" />
            <div className="ml-4">
              <div className="text-2xl font-bold">
                {sensors.length > 0 
                  ? Math.round(sensors.reduce((sum, s) => sum + s.battery_level, 0) / sensors.length)
                  : 0}%
              </div>
              <div className="text-gray-600">Avg Battery</div>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <Thermometer className="w-8 h-8 text-purple-600" />
            <div className="ml-4">
              <div className="text-2xl font-bold">
                {sensors.length > 0 
                  ? formatTemperature(sensors.reduce((sum, s) => sum + s.last_temp, 0) / sensors.length).fahrenheit
                  : '--'}°F
              </div>
              <div className="text-gray-600">Avg Temp</div>
            </div>
          </div>
        </div>
      </div>

      {/* Sensor Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {sensors.map((sensor) => {
          const temp = formatTemperature(sensor.last_temp);
          const statusColor = getStatusColor(sensor.status, sensor.last_reading);
          
          return (
            <div key={sensor.sensor_id} className="bg-white rounded-lg shadow-lg overflow-hidden">
              {/* Header */}
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      {sensor.sensor_name}
                    </h3>
                    <p className="text-sm text-gray-600">ID: {sensor.sensor_id}</p>
                  </div>
                  <div className={`px-2 py-1 rounded-full text-xs font-medium border ${statusColor}`}>
                    {sensor.status}
                  </div>
                </div>
              </div>
              
              {/* Temperature Display */}
              <div className="px-6 py-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <Thermometer className="w-8 h-8 text-blue-600" />
                    <div className="ml-3">
                      <div className="text-3xl font-bold text-gray-900">
                        {temp.fahrenheit}°F
                      </div>
                      <div className="text-sm text-gray-600">
                        {temp.celsius}°C
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="flex items-center text-sm text-gray-600">
                      <Droplet className="w-4 h-4 mr-1" />
                      {sensor.last_humidity.toFixed(1)}%
                    </div>
                  </div>
                </div>
                
                {/* Status Info */}
                <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-100">
                  <div className="flex items-center text-sm text-gray-600">
                    <Battery className="w-4 h-4 mr-2" />
                    {sensor.battery_level}%
                  </div>
                  
                  {sensor.rssi && (
                    <div className="flex items-center text-sm text-gray-600">
                      <Wifi className="w-4 h-4 mr-2" />
                      {sensor.rssi} dBm
                    </div>
                  )}
                  
                  <div className="col-span-2 flex items-center text-sm text-gray-600">
                    <Clock className="w-4 h-4 mr-2" />
                    Last reading: {formatTimestamp(sensor.last_reading)}
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {sensors.length === 0 && !loading && (
        <div className="text-center py-12">
          <Thermometer className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-medium text-gray-900 mb-2">No sensors found</h3>
          <p className="text-gray-600 mb-4">
            No TempStick sensors are currently connected or available.
          </p>
          <button
            onClick={fetchSensors}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      )}
    </div>
  );
}