/**
 * Report Progress Tracker Component
 * 
 * Real-time progress tracking for PDF report generation with user-friendly
 * feedback, cancellation support, and performance monitoring.
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { <PERSON><PERSON> } from '../ui/button';
import { Progress } from '../ui/progress';
import { Badge } from '../ui/badge';
import { 
  FileText, 
  Download, 
  Mail, 
  X, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Loader2,
  Play,
  Pause,
  RotateCcw
} from 'lucide-react';

import { reportService } from '../../lib/report-service-integration';
import type { 
  ReportJobStatus,
  ReportGenerationResult 
} from '../../lib/report-service-integration';
import type { PDFGenerationProgress } from '../../lib/pdf-report-generator';

export interface ReportProgressTrackerProps {
  reportId?: string;
  onComplete?: (result: ReportGenerationResult) => void;
  onCancel?: () => void;
  onDownload?: (result: ReportGenerationResult) => void;
  showDetailedProgress?: boolean;
  compact?: boolean;
  className?: string;
}

interface ProgressStageInfo {
  name: string;
  description: string;
  icon: React.ReactNode;
  estimatedDuration: number; // seconds
}

const PROGRESS_STAGES: Record<PDFGenerationProgress['stage'], ProgressStageInfo> = {
  preparing: {
    name: 'Preparing',
    description: 'Initializing report generation and collecting data',
    icon: <Loader2 className="h-4 w-4 animate-spin" />,
    estimatedDuration: 5
  },
  charts: {
    name: 'Creating Charts',
    description: 'Generating temperature charts and visualizations',
    icon: <FileText className="h-4 w-4" />,
    estimatedDuration: 15
  },
  data: {
    name: 'Processing Data',
    description: 'Analyzing sensor data and calculating statistics',
    icon: <Loader2 className="h-4 w-4 animate-spin" />,
    estimatedDuration: 10
  },
  rendering: {
    name: 'Rendering PDF',
    description: 'Finalizing document layout and generating PDF',
    icon: <FileText className="h-4 w-4" />,
    estimatedDuration: 8
  },
  complete: {
    name: 'Complete',
    description: 'Report generated successfully',
    icon: <CheckCircle className="h-4 w-4 text-green-500" />,
    estimatedDuration: 0
  },
  error: {
    name: 'Error',
    description: 'Report generation failed',
    icon: <AlertCircle className="h-4 w-4 text-red-500" />,
    estimatedDuration: 0
  }
};

export function ReportProgressTracker({
  reportId,
  onComplete,
  onCancel,
  onDownload,
  showDetailedProgress = true,
  compact = false,
  className = ''
}: ReportProgressTrackerProps) {
  const [jobStatus, setJobStatus] = useState<ReportJobStatus | null>(null);
  const [isPolling, setIsPolling] = useState(false);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [error, setError] = useState<string | null>(null);

  // Poll for status updates
  useEffect(() => {
    if (!reportId) return;

    let intervalId: NodeJS.Timeout;
    
    const pollStatus = async () => {
      try {
        const status = reportService.getReportStatus(reportId);
        if (status) {
          setJobStatus(status);
          
          // Handle completion
          if (status.status === 'completed' && status.result?.success && onComplete) {
            onComplete(status.result);
            setIsPolling(false);
          }
          
          // Handle failure
          if (status.status === 'failed') {
            setError(status.error || 'Report generation failed');
            setIsPolling(false);
          }
          
          // Stop polling if job is finished
          if (['completed', 'failed', 'cancelled'].includes(status.status)) {
            setIsPolling(false);
          }
        }
      } catch (err) {
        console.error('Failed to poll report status:', err);
        setError('Failed to get report status');
        setIsPolling(false);
      }
    };

    if (isPolling) {
      // Poll every second
      intervalId = setInterval(pollStatus, 1000);
      
      // Initial poll
      pollStatus();
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [reportId, isPolling, onComplete]);

  // Start polling when reportId is provided
  useEffect(() => {
    if (reportId && !isPolling) {
      setIsPolling(true);
    }
  }, [reportId]);

  // Track elapsed time
  useEffect(() => {
    if (!jobStatus || !['queued', 'generating'].includes(jobStatus.status)) return;

    const startTime = jobStatus.startedAt?.getTime() || jobStatus.requestedAt.getTime();
    
    const updateElapsed = () => {
      setElapsedTime(Math.floor((Date.now() - startTime) / 1000));
    };

    updateElapsed();
    const intervalId = setInterval(updateElapsed, 1000);

    return () => clearInterval(intervalId);
  }, [jobStatus]);

  const handleCancel = async () => {
    if (!reportId || !jobStatus) return;

    try {
      const success = await reportService.cancelReport(reportId);
      if (success) {
        setIsPolling(false);
        if (onCancel) {
          onCancel();
        }
      }
    } catch (err) {
      console.error('Failed to cancel report:', err);
      setError('Failed to cancel report');
    }
  };

  const handleDownload = () => {
    if (jobStatus?.result && onDownload) {
      onDownload(jobStatus.result);
    }
  };

  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getStatusColor = (status: ReportJobStatus['status']): string => {
    switch (status) {
      case 'queued': return 'bg-blue-100 text-blue-800';
      case 'generating': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'cancelled': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (!reportId || !jobStatus) {
    return (
      <Card className={`${className} ${compact ? 'p-4' : ''}`}>
        <CardContent className={compact ? 'p-0' : 'pt-6'}>
          <div className="flex items-center space-x-2 text-gray-500">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span className="text-sm">Initializing report generation...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (compact) {
    return (
      <div className={`flex items-center space-x-3 p-3 bg-gray-50 rounded-lg ${className}`}>
        <div className="flex-1">
          <div className="flex items-center justify-between mb-2">
            <Badge className={getStatusColor(jobStatus.status)}>
              {jobStatus.status.toUpperCase()}
            </Badge>
            <span className="text-xs text-gray-500">
              {formatTime(elapsedTime)}
            </span>
          </div>
          
          {jobStatus.progress && jobStatus.status === 'generating' && (
            <div className="space-y-1">
              <Progress value={jobStatus.progress.progress} className="h-2" />
              <div className="flex justify-between text-xs text-gray-500">
                <span>{jobStatus.progress.currentTask || PROGRESS_STAGES[jobStatus.progress.stage].name}</span>
                <span>{jobStatus.progress.progress.toFixed(0)}%</span>
              </div>
            </div>
          )}
        </div>

        <div className="flex items-center space-x-1">
          {jobStatus.status === 'completed' && jobStatus.result?.success && (
            <Button size="sm" variant="outline" onClick={handleDownload}>
              <Download className="h-3 w-3" />
            </Button>
          )}
          
          {['queued', 'generating'].includes(jobStatus.status) && (
            <Button size="sm" variant="outline" onClick={handleCancel}>
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Report Generation</span>
          </div>
          
          <Badge className={getStatusColor(jobStatus.status)}>
            {jobStatus.status.toUpperCase()}
          </Badge>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Basic Progress Info */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <Clock className="h-4 w-4" />
            <span>Elapsed: {formatTime(elapsedTime)}</span>
          </div>
          
          {jobStatus.estimatedTimeRemaining && jobStatus.estimatedTimeRemaining > 0 && (
            <div className="text-sm text-gray-600">
              Est. remaining: {formatTime(Math.ceil(jobStatus.estimatedTimeRemaining / 1000))}
            </div>
          )}
        </div>

        {/* Progress Bar and Stage Info */}
        {jobStatus.progress && jobStatus.status === 'generating' && (
          <div className="space-y-4">
            <Progress value={jobStatus.progress.progress} className="h-3" />
            
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">
                {jobStatus.progress.progress.toFixed(1)}% Complete
              </span>
              
              {jobStatus.result?.fileSizeKB && (
                <span className="text-sm text-gray-500">
                  Size: {(jobStatus.result.fileSizeKB / 1024).toFixed(1)} MB
                </span>
              )}
            </div>

            {showDetailedProgress && (
              <div className="space-y-3">
                <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
                  {PROGRESS_STAGES[jobStatus.progress.stage].icon}
                  <div className="flex-1">
                    <div className="font-medium text-sm">
                      {PROGRESS_STAGES[jobStatus.progress.stage].name}
                    </div>
                    <div className="text-xs text-gray-600">
                      {jobStatus.progress.currentTask || 
                       PROGRESS_STAGES[jobStatus.progress.stage].description}
                    </div>
                  </div>
                </div>

                {/* Stage Progress Indicators */}
                <div className="grid grid-cols-4 gap-2">
                  {Object.entries(PROGRESS_STAGES).slice(0, 4).map(([stage, info], index) => {
                    const stageIndex = Object.keys(PROGRESS_STAGES).indexOf(stage);
                    const currentStageIndex = Object.keys(PROGRESS_STAGES).indexOf(jobStatus.progress!.stage);
                    const isActive = stageIndex === currentStageIndex;
                    const isComplete = stageIndex < currentStageIndex;
                    
                    return (
                      <div 
                        key={stage}
                        className={`p-2 rounded text-center text-xs ${
                          isActive ? 'bg-blue-100 text-blue-800' :
                          isComplete ? 'bg-green-100 text-green-800' :
                          'bg-gray-100 text-gray-500'
                        }`}
                      >
                        <div className="font-medium">{info.name}</div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Error Display */}
        {(error || (jobStatus.status === 'failed' && jobStatus.error)) && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center space-x-2 text-red-800">
              <AlertCircle className="h-4 w-4" />
              <span className="font-medium">Error</span>
            </div>
            <p className="mt-1 text-sm text-red-700">
              {error || jobStatus.error}
            </p>
          </div>
        )}

        {/* Success Display */}
        {jobStatus.status === 'completed' && jobStatus.result?.success && (
          <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2 text-green-800">
                <CheckCircle className="h-4 w-4" />
                <span className="font-medium">Report Generated Successfully</span>
              </div>
              <Badge variant="outline" className="text-green-700 border-green-200">
                {(jobStatus.result.fileSizeKB / 1024).toFixed(1)} MB
              </Badge>
            </div>
            
            <div className="mt-2 text-sm text-green-700">
              Generated in {formatTime(Math.floor(jobStatus.result.generationTimeMs / 1000))}
            </div>

            {jobStatus.result.warnings && jobStatus.result.warnings.length > 0 && (
              <div className="mt-2">
                <div className="text-sm font-medium text-yellow-700 mb-1">Warnings:</div>
                <ul className="text-xs text-yellow-600 space-y-1">
                  {jobStatus.result.warnings.map((warning, index) => (
                    <li key={index}>• {warning}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="flex items-center space-x-2">
            {['queued', 'generating'].includes(jobStatus.status) && (
              <Button variant="outline" onClick={handleCancel}>
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
            )}
          </div>

          <div className="flex items-center space-x-2">
            {jobStatus.status === 'completed' && jobStatus.result?.success && (
              <>
                <Button variant="outline" onClick={handleDownload}>
                  <Download className="h-4 w-4 mr-2" />
                  Download PDF
                </Button>
                
                {jobStatus.result.emailDeliveryIds && jobStatus.result.emailDeliveryIds.length > 0 && (
                  <Badge variant="outline" className="text-green-600 border-green-200">
                    <Mail className="h-3 w-3 mr-1" />
                    Emailed
                  </Badge>
                )}
              </>
            )}

            {jobStatus.status === 'failed' && (
              <Button variant="outline" onClick={() => window.location.reload()}>
                <RotateCcw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Multi-Report Progress Tracker for bulk operations
 */
export interface MultiReportProgressProps {
  reportIds: string[];
  onAllComplete?: (results: ReportGenerationResult[]) => void;
  onCancel?: () => void;
  className?: string;
}

export function MultiReportProgressTracker({
  reportIds,
  onAllComplete,
  onCancel,
  className = ''
}: MultiReportProgressProps) {
  const [jobStatuses, setJobStatuses] = useState<Map<string, ReportJobStatus>>(new Map());
  const [completedReports, setCompletedReports] = useState<ReportGenerationResult[]>([]);

  useEffect(() => {
    const intervalId = setInterval(async () => {
      const statusMap = new Map<string, ReportJobStatus>();
      const completed: ReportGenerationResult[] = [];

      for (const reportId of reportIds) {
        const status = reportService.getReportStatus(reportId);
        if (status) {
          statusMap.set(reportId, status);
          
          if (status.status === 'completed' && status.result?.success) {
            completed.push(status.result);
          }
        }
      }

      setJobStatuses(statusMap);
      setCompletedReports(completed);

      // Check if all reports are complete
      const allComplete = reportIds.every(id => {
        const status = statusMap.get(id);
        return status && ['completed', 'failed', 'cancelled'].includes(status.status);
      });

      if (allComplete && completed.length > 0 && onAllComplete) {
        onAllComplete(completed);
      }
    }, 2000); // Poll every 2 seconds for multiple reports

    return () => clearInterval(intervalId);
  }, [reportIds, onAllComplete]);

  const handleCancelAll = async () => {
    await Promise.all(reportIds.map(id => reportService.cancelReport(id)));
    if (onCancel) {
      onCancel();
    }
  };

  const totalReports = reportIds.length;
  const completedCount = Array.from(jobStatuses.values()).filter(
    status => ['completed', 'failed', 'cancelled'].includes(status.status)
  ).length;
  const overallProgress = totalReports > 0 ? (completedCount / totalReports) * 100 : 0;

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Bulk Report Generation</span>
          </div>
          <Badge variant="outline">
            {completedCount} / {totalReports}
          </Badge>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Overall Progress</span>
            <span>{overallProgress.toFixed(0)}%</span>
          </div>
          <Progress value={overallProgress} className="h-2" />
        </div>

        <div className="space-y-2 max-h-60 overflow-y-auto">
          {reportIds.map((reportId, index) => {
            const status = jobStatuses.get(reportId);
            return (
              <div 
                key={reportId}
                className="flex items-center justify-between p-2 bg-gray-50 rounded"
              >
                <span className="text-sm">Report {index + 1}</span>
                <Badge className={status ? getStatusColor(status.status) : 'bg-gray-100'}>
                  {status?.status.toUpperCase() || 'PENDING'}
                </Badge>
              </div>
            );
          })}
        </div>

        <div className="flex justify-between pt-4 border-t">
          <Button variant="outline" onClick={handleCancelAll}>
            <X className="h-4 w-4 mr-2" />
            Cancel All
          </Button>
          
          {completedReports.length > 0 && (
            <Button>
              <Download className="h-4 w-4 mr-2" />
              Download All ({completedReports.length})
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Helper function for status colors (moved outside component for reuse)
function getStatusColor(status: ReportJobStatus['status']): string {
  switch (status) {
    case 'queued': return 'bg-blue-100 text-blue-800';
    case 'generating': return 'bg-yellow-100 text-yellow-800';
    case 'completed': return 'bg-green-100 text-green-800';
    case 'failed': return 'bg-red-100 text-red-800';
    case 'cancelled': return 'bg-gray-100 text-gray-800';
    default: return 'bg-gray-100 text-gray-800';
  }
}