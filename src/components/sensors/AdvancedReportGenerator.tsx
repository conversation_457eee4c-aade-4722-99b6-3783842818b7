/**
 * Advanced Report Generator Component
 * 
 * Professional interface for generating comprehensive PDF reports with template
 * selection, customization options, and real-time progress tracking.
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { <PERSON><PERSON> } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Checkbox } from '../ui/checkbox';
import { DatePicker } from '../ui/date-picker';
import { Badge } from '../ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog';
import {
  FileText,
  Clock,
  AlertTriangle,
  CheckCircle,
  ChevronRight,
  Eye,
  Zap,
  Shield,
  BarChart3
} from 'lucide-react';

import { ReportProgressTracker } from './ReportProgressTracker';
import { useSensors } from '../../hooks/useTempStick';
import { reportService } from '../../lib/report-service-integration';

import type {
  ReportGenerationRequest,
  ReportGenerationResult
} from '../../lib/report-service-integration';
import type {
  TemperatureReportParams
} from '../../types/tempstick';
import type {
  ReportTemplateConfig,
  CustomizableTemplate
} from '../../lib/report-templates';

export interface AdvancedReportGeneratorProps {
  defaultDateRange?: {
    startDate: Date;
    endDate: Date;
  };
  defaultSensorIds?: string[];
  onReportGenerated?: (result: ReportGenerationResult) => void;
  className?: string;
}

const TEMPLATE_CATEGORIES = [
  { id: 'operational', name: 'Operational', icon: <BarChart3 className="h-4 w-4" />, color: 'text-blue-600' },
  { id: 'compliance', name: 'Compliance', icon: <Shield className="h-4 w-4" />, color: 'text-green-600' },
  { id: 'analytical', name: 'Analytical', icon: <BarChart3 className="h-4 w-4" />, color: 'text-purple-600' },
  { id: 'regulatory', name: 'Regulatory', icon: <FileText className="h-4 w-4" />, color: 'text-red-600' }
];

export function AdvancedReportGenerator({
  defaultDateRange,
  defaultSensorIds = [],
  onReportGenerated,
  className = ''
}: AdvancedReportGeneratorProps) {
  const [selectedTemplate, setSelectedTemplate] = useState<ReportTemplateConfig | null>(null);
  const [reportParams, setReportParams] = useState<TemperatureReportParams>({
    startDate: defaultDateRange?.startDate || new Date(Date.now() - 24 * 60 * 60 * 1000),
    endDate: defaultDateRange?.endDate || new Date(),
    sensorIds: defaultSensorIds,
    includeAlerts: true,
    includeCharts: true,
    includeHACCPData: true,
    format: 'pdf'
  });
  const [customization, _setCustomization] = useState<Partial<CustomizableTemplate>>({});
  const [emailConfig, setEmailConfig] = useState({
    enabled: false,
    recipients: [''],
    subject: '',
    includeCharts: true
  });
  const [scheduleConfig, setScheduleConfig] = useState({
    enabled: false,
    frequency: 'daily' as const,
    time: '08:00'
  });

  const [availableTemplates, setAvailableTemplates] = useState<ReportTemplateConfig[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentReportId, setCurrentReportId] = useState<string | null>(null);
  const [validationResult, setValidationResult] = useState<{
    valid: boolean;
    errors: string[];
    warnings: string[];
    estimatedTime: number;
    estimatedSize: number;
  } | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  const { sensors, loading: sensorsLoading } = useSensors();

  // Load available templates
  useEffect(() => {
    const templates = reportService.getAvailableTemplates();
    setAvailableTemplates(templates);
    
    // Select first comprehensive template as default
    const defaultTemplate = templates.find(t => t.id === 'comprehensive') || templates[0];
    setSelectedTemplate(defaultTemplate);
  }, []);

  // Validate report when parameters change
  useEffect(() => {
    if (selectedTemplate) {
      validateReport();
    }
  }, [selectedTemplate, reportParams]);

  const validateReport = async () => {
    if (!selectedTemplate) return;

    try {
      const result = await reportService.validateReportRequirements(
        selectedTemplate.id,
        reportParams
      );
      setValidationResult(result);
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const handleGenerateReport = async () => {
    if (!selectedTemplate || !validationResult?.valid) return;

    setIsGenerating(true);

    try {
      const request: ReportGenerationRequest = {
        templateId: selectedTemplate.id,
        params: reportParams,
        customization: Object.keys(customization).length > 0 ? {
          baseTemplate: selectedTemplate.id,
          customSections: [],
          excludedSections: [],
          chartConfigurations: {},
          ...customization
        } : undefined,
        delivery: {
          download: { filename: undefined },
          ...(emailConfig.enabled ? {
            email: {
              recipients: emailConfig.recipients.filter(r => r.trim()),
              subject: emailConfig.subject || `Temperature Report - ${new Date().toLocaleDateString()}`,
              includeCharts: emailConfig.includeCharts
            }
          } : {}),
          ...(scheduleConfig.enabled ? {
            schedule: {
              frequency: scheduleConfig.frequency,
              time: scheduleConfig.time,
              enabled: true
            }
          } : {})
        },
        options: {
          priority: 'normal',
          compress: true
        }
      };

      const reportId = await reportService.queueReport(request);
      setCurrentReportId(reportId);

    } catch (error) {
      console.error('Failed to generate report:', error);
      setIsGenerating(false);
    }
  };

  const handleReportComplete = (result: ReportGenerationResult) => {
    setIsGenerating(false);
    setCurrentReportId(null);
    
    if (onReportGenerated) {
      onReportGenerated(result);
    }
  };

  const handleReportCancel = () => {
    setIsGenerating(false);
    setCurrentReportId(null);
  };

  const handleDownloadReport = (result: ReportGenerationResult) => {
    if (result.downloadUrl) {
      const link = document.createElement('a');
      link.href = result.downloadUrl;
      link.download = result.filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else if (result.pdfBuffer) {
      const blob = new Blob([result.pdfBuffer], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = result.filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }
  };

  const handlePreviewTemplate = async () => {
    if (!selectedTemplate) return;

    try {
      setShowPreview(true);
      const previewDataUrl = await reportService.generateTemplatePreview(selectedTemplate.id);
      setPreviewUrl(previewDataUrl);
    } catch (error) {
      console.error('Failed to generate preview:', error);
    }
  };

  const addEmailRecipient = () => {
    setEmailConfig(prev => ({
      ...prev,
      recipients: [...prev.recipients, '']
    }));
  };

  const updateEmailRecipient = (index: number, email: string) => {
    setEmailConfig(prev => ({
      ...prev,
      recipients: prev.recipients.map((r, i) => i === index ? email : r)
    }));
  };

  const removeEmailRecipient = (index: number) => {
    setEmailConfig(prev => ({
      ...prev,
      recipients: prev.recipients.filter((_, i) => i !== index)
    }));
  };

  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return minutes > 0 ? `${minutes}m ${remainingSeconds}s` : `${remainingSeconds}s`;
  };

  if (isGenerating && currentReportId) {
    return (
      <div className={className}>
        <ReportProgressTracker
          reportId={currentReportId}
          onComplete={handleReportComplete}
          onCancel={handleReportCancel}
          onDownload={handleDownloadReport}
          showDetailedProgress={true}
        />
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-6 w-6" />
            <span>Advanced Report Generator</span>
            {selectedTemplate && (
              <Badge variant="outline" className="ml-auto">
                {selectedTemplate.name}
              </Badge>
            )}
          </CardTitle>
        </CardHeader>

        <CardContent>
          <Tabs defaultValue="template" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="template">Template</TabsTrigger>
              <TabsTrigger value="parameters">Parameters</TabsTrigger>
              <TabsTrigger value="delivery">Delivery</TabsTrigger>
              <TabsTrigger value="preview">Preview</TabsTrigger>
            </TabsList>

            {/* Template Selection */}
            <TabsContent value="template" className="space-y-6">
              <div>
                <Label className="text-base font-medium">Report Template</Label>
                <p className="text-sm text-gray-500 mb-4">
                  Choose a template that matches your reporting needs
                </p>

                <div className="grid gap-4">
                  {TEMPLATE_CATEGORIES.map(category => {
                    const templates = availableTemplates.filter(t => t.category === category.id);
                    if (templates.length === 0) return null;

                    return (
                      <div key={category.id} className="space-y-3">
                        <div className="flex items-center space-x-2">
                          <div className={category.color}>{category.icon}</div>
                          <h3 className="font-medium">{category.name}</h3>
                          <Badge variant="outline">{templates.length}</Badge>
                        </div>

                        <div className="grid gap-3 ml-6">
                          {templates.map(template => (
                            <div
                              key={template.id}
                              className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                                selectedTemplate?.id === template.id 
                                  ? 'border-blue-500 bg-blue-50' 
                                  : 'border-gray-200 hover:border-gray-300'
                              }`}
                              onClick={() => setSelectedTemplate(template)}
                            >
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  <div className="flex items-center space-x-2 mb-1">
                                    <h4 className="font-medium">{template.name}</h4>
                                    <Badge 
                                      variant={template.complianceLevel === 'regulatory' ? 'default' : 'outline'}
                                      className={
                                        template.complianceLevel === 'regulatory' ? 'bg-red-100 text-red-800' :
                                        template.complianceLevel === 'haccp' ? 'bg-orange-100 text-orange-800' :
                                        'bg-gray-100 text-gray-800'
                                      }
                                    >
                                      {template.complianceLevel.toUpperCase()}
                                    </Badge>
                                  </div>
                                  
                                  <p className="text-sm text-gray-600 mb-2">
                                    {template.description}
                                  </p>
                                  
                                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                                    <span className="flex items-center space-x-1">
                                      <FileText className="h-3 w-3" />
                                      <span>~{template.estimatedPages} pages</span>
                                    </span>
                                    <span className="flex items-center space-x-1">
                                      <Clock className="h-3 w-3" />
                                      <span>~{formatTime(template.estimatedGenerationTime)}</span>
                                    </span>
                                    {template.automationSupported && (
                                      <span className="flex items-center space-x-1">
                                        <Zap className="h-3 w-3 text-blue-500" />
                                        <span>Schedulable</span>
                                      </span>
                                    )}
                                  </div>
                                </div>

                                <ChevronRight className={`h-4 w-4 transition-transform ${
                                  selectedTemplate?.id === template.id ? 'rotate-90' : ''
                                }`} />
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </TabsContent>

            {/* Report Parameters */}
            <TabsContent value="parameters" className="space-y-6">
              <div className="grid gap-6">
                {/* Date Range */}
                <div className="space-y-3">
                  <Label className="text-base font-medium">Date Range</Label>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="startDate">Start Date</Label>
                      <DatePicker
                        selected={reportParams.startDate}
                        onSelect={(date) => date && setReportParams(prev => ({ 
                          ...prev, 
                          startDate: date 
                        }))}
                      />
                    </div>
                    <div>
                      <Label htmlFor="endDate">End Date</Label>
                      <DatePicker
                        selected={reportParams.endDate}
                        onSelect={(date) => date && setReportParams(prev => ({ 
                          ...prev, 
                          endDate: date 
                        }))}
                      />
                    </div>
                  </div>
                </div>

                {/* Sensor Selection */}
                <div className="space-y-3">
                  <Label className="text-base font-medium">Sensors</Label>
                  <Select
                    value={reportParams.sensorIds?.length === 0 ? 'all' : 'selected'}
                    onValueChange={(value) => {
                      if (value === 'all') {
                        setReportParams(prev => ({ ...prev, sensorIds: [] }));
                      }
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select sensors" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Sensors ({sensors.length})</SelectItem>
                      <SelectItem value="selected">
                        Selected ({reportParams.sensorIds?.length || 0})
                      </SelectItem>
                    </SelectContent>
                  </Select>

                  {!sensorsLoading && sensors.length > 0 && (
                    <div className="max-h-48 overflow-y-auto border rounded p-3 space-y-2">
                      {sensors.map(sensor => (
                        <div key={sensor.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={`sensor-${sensor.id}`}
                            checked={reportParams.sensorIds?.includes(sensor.id) || reportParams.sensorIds?.length === 0}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setReportParams(prev => ({
                                  ...prev,
                                  sensorIds: [...(prev.sensorIds || []), sensor.id]
                                }));
                              } else {
                                setReportParams(prev => ({
                                  ...prev,
                                  sensorIds: prev.sensorIds?.filter(id => id !== sensor.id) || []
                                }));
                              }
                            }}
                          />
                          <Label 
                            htmlFor={`sensor-${sensor.id}`}
                            className="flex-1 text-sm"
                          >
                            {sensor.name} - {sensor.location}
                          </Label>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Include Options */}
                <div className="space-y-3">
                  <Label className="text-base font-medium">Include in Report</Label>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeAlerts"
                        checked={reportParams.includeAlerts}
                        onCheckedChange={(checked) => setReportParams(prev => ({
                          ...prev,
                          includeAlerts: checked as boolean
                        }))}
                      />
                      <Label htmlFor="includeAlerts">Temperature Alerts and Violations</Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeCharts"
                        checked={reportParams.includeCharts}
                        onCheckedChange={(checked) => setReportParams(prev => ({
                          ...prev,
                          includeCharts: checked as boolean
                        }))}
                      />
                      <Label htmlFor="includeCharts">Temperature Trend Charts</Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeHACCP"
                        checked={reportParams.includeHACCPData}
                        onCheckedChange={(checked) => setReportParams(prev => ({
                          ...prev,
                          includeHACCPData: checked as boolean
                        }))}
                      />
                      <Label htmlFor="includeHACCP">HACCP Compliance Data</Label>
                    </div>
                  </div>
                </div>

                {/* Validation Results */}
                {validationResult && (
                  <div className="space-y-3">
                    <Label className="text-base font-medium">Report Validation</Label>
                    
                    {!validationResult.valid && (
                      <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                        <div className="flex items-center space-x-2 text-red-800 mb-2">
                          <AlertTriangle className="h-4 w-4" />
                          <span className="font-medium">Validation Errors</span>
                        </div>
                        <ul className="text-sm text-red-700 space-y-1">
                          {validationResult.errors.map((error, index) => (
                            <li key={index}>• {error}</li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {validationResult.warnings.length > 0 && (
                      <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div className="flex items-center space-x-2 text-yellow-800 mb-2">
                          <AlertTriangle className="h-4 w-4" />
                          <span className="font-medium">Warnings</span>
                        </div>
                        <ul className="text-sm text-yellow-700 space-y-1">
                          {validationResult.warnings.map((warning, index) => (
                            <li key={index}>• {warning}</li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {validationResult.valid && (
                      <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                        <div className="flex items-center space-x-2 text-green-800 mb-2">
                          <CheckCircle className="h-4 w-4" />
                          <span className="font-medium">Ready to Generate</span>
                        </div>
                        <div className="text-sm text-green-700 space-y-1">
                          <div>Estimated time: {formatTime(validationResult.estimatedTime)}</div>
                          <div>Estimated pages: ~{validationResult.estimatedSize}</div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </TabsContent>

            {/* Delivery Options */}
            <TabsContent value="delivery" className="space-y-6">
              <div className="grid gap-6">
                {/* Email Delivery */}
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="emailEnabled"
                      checked={emailConfig.enabled}
                      onCheckedChange={(checked) => setEmailConfig(prev => ({
                        ...prev,
                        enabled: checked as boolean
                      }))}
                    />
                    <Label htmlFor="emailEnabled" className="text-base font-medium">
                      Email Delivery
                    </Label>
                  </div>

                  {emailConfig.enabled && (
                    <div className="space-y-4 ml-6">
                      <div>
                        <Label>Email Subject</Label>
                        <Input
                          value={emailConfig.subject}
                          onChange={(e) => setEmailConfig(prev => ({
                            ...prev,
                            subject: e.target.value
                          }))}
                          placeholder="Temperature Report - [Date]"
                        />
                      </div>

                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <Label>Recipients</Label>
                          <Button size="sm" variant="outline" onClick={addEmailRecipient}>
                            Add Recipient
                          </Button>
                        </div>
                        <div className="space-y-2">
                          {emailConfig.recipients.map((email, index) => (
                            <div key={index} className="flex space-x-2">
                              <Input
                                type="email"
                                value={email}
                                onChange={(e) => updateEmailRecipient(index, e.target.value)}
                                placeholder="Enter email address"
                              />
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => removeEmailRecipient(index)}
                                disabled={emailConfig.recipients.length === 1}
                              >
                                Remove
                              </Button>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="includeChartsEmail"
                          checked={emailConfig.includeCharts}
                          onCheckedChange={(checked) => setEmailConfig(prev => ({
                            ...prev,
                            includeCharts: checked as boolean
                          }))}
                        />
                        <Label htmlFor="includeChartsEmail">Include charts in PDF</Label>
                      </div>
                    </div>
                  )}
                </div>

                {/* Scheduling */}
                {selectedTemplate?.automationSupported && (
                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="scheduleEnabled"
                        checked={scheduleConfig.enabled}
                        onCheckedChange={(checked) => setScheduleConfig(prev => ({
                          ...prev,
                          enabled: checked as boolean
                        }))}
                      />
                      <Label htmlFor="scheduleEnabled" className="text-base font-medium">
                        Schedule Recurring Report
                      </Label>
                    </div>

                    {scheduleConfig.enabled && (
                      <div className="space-y-4 ml-6">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label>Frequency</Label>
                            <Select
                              value={scheduleConfig.frequency}
                              onValueChange={(value: any) => setScheduleConfig(prev => ({
                                ...prev,
                                frequency: value
                              }))}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="daily">Daily</SelectItem>
                                <SelectItem value="weekly">Weekly</SelectItem>
                                <SelectItem value="monthly">Monthly</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          <div>
                            <Label>Time</Label>
                            <Input
                              type="time"
                              value={scheduleConfig.time}
                              onChange={(e) => setScheduleConfig(prev => ({
                                ...prev,
                                time: e.target.value
                              }))}
                            />
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </TabsContent>

            {/* Preview */}
            <TabsContent value="preview" className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-base font-medium">Template Preview</Label>
                    <p className="text-sm text-gray-500">
                      Generate a sample report to preview the layout and content
                    </p>
                  </div>
                  <Button onClick={handlePreviewTemplate} disabled={!selectedTemplate}>
                    <Eye className="h-4 w-4 mr-2" />
                    Generate Preview
                  </Button>
                </div>

                {previewUrl && (
                  <div className="border rounded-lg p-4">
                    <iframe
                      src={previewUrl}
                      className="w-full h-96 border-0"
                      title="Report Preview"
                    />
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>

          {/* Action Buttons */}
          <div className="flex items-center justify-between pt-6 border-t">
            <div className="text-sm text-gray-500">
              {validationResult && validationResult.valid && (
                <>Estimated generation time: {formatTime(validationResult.estimatedTime)}</>
              )}
            </div>
            
            <div className="flex items-center space-x-3">
              <Dialog open={showPreview} onOpenChange={setShowPreview}>
                <DialogTrigger asChild>
                  <Button variant="outline" disabled={!selectedTemplate}>
                    <Eye className="h-4 w-4 mr-2" />
                    Preview
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-4xl max-h-[80vh]">
                  <DialogHeader>
                    <DialogTitle>Template Preview</DialogTitle>
                  </DialogHeader>
                  {previewUrl && (
                    <iframe
                      src={previewUrl}
                      className="w-full h-[70vh] border-0"
                      title="Report Preview"
                    />
                  )}
                </DialogContent>
              </Dialog>

              <Button
                onClick={handleGenerateReport}
                disabled={!selectedTemplate || !validationResult?.valid}
                className="min-w-[120px]"
              >
                <FileText className="h-4 w-4 mr-2" />
                Generate Report
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}