/**
 * TempStick Data Source Selector Component
 * 
 * Allows users to switch between real TempStick API data and mock data for development/testing
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Database, 
  Wifi, 
  WifiOff, 
  Settings, 
  RefreshCw,
  CheckCircle,
  AlertTriangle,
  Info
} from 'lucide-react';
import { tempStickService } from '@/lib/tempstick-service';

interface TempStickDataSourceSelectorProps {
  className?: string;
  onDataSourceChange?: (mode: 'real' | 'mock' | 'auto') => void;
}

export const TempStickDataSourceSelector: React.FC<TempStickDataSourceSelectorProps> = ({ 
  className = '',
  onDataSourceChange
}) => {
  const [currentMode, setCurrentMode] = useState<'real' | 'mock' | 'auto'>('auto');
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'unknown' | 'connected' | 'failed'>('unknown');
  const [apiStatus, setApiStatus] = useState<{
    sensors: boolean;
    readings: boolean;
    alerts: boolean;
  }>({
    sensors: false,
    readings: false,
    alerts: false
  });
  const [lastTestAt, setLastTestAt] = useState<Date | null>(null);

  /**
   * Test API connectivity and update status
   */
  const testApiConnectivity = async () => {
    setIsConnecting(true);
    setConnectionStatus('unknown');
    
    try {
      // Test each API endpoint using the correct service methods
      const results = await Promise.allSettled([
        tempStickService.getAllSensors().then(() => true).catch(() => false),
        tempStickService.getCurrentReadings().then(() => true).catch(() => false),
        tempStickService.getActiveAlerts().then(() => true).catch(() => false)
      ]);

      const [sensorsResult, readingsResult, alertsResult] = results;
      
      setApiStatus({
        sensors: sensorsResult.status === 'fulfilled' && sensorsResult.value,
        readings: readingsResult.status === 'fulfilled' && readingsResult.value,
        alerts: alertsResult.status === 'fulfilled' && alertsResult.value
      });

      // Overall connection status
      const allConnected = results.every(result => 
        result.status === 'fulfilled' && result.value
      );
      
      setConnectionStatus(allConnected ? 'connected' : 'failed');
      setLastTestAt(new Date());

    } catch (error) {
      console.error('API connectivity test failed:', error);
      setConnectionStatus('failed');
      setApiStatus({
        sensors: false,
        readings: false,
        alerts: false
      });
    } finally {
      setIsConnecting(false);
    }
  };

  /**
   * Handle data source mode change
   */
  const handleModeChange = (mode: 'real' | 'mock' | 'auto') => {
    setCurrentMode(mode);
    tempStickService.setDataMode(mode);
    onDataSourceChange?.(mode);
    
    // Test connectivity when switching to real mode
    if (mode === 'real' || mode === 'auto') {
      testApiConnectivity();
    }
  };

  /**
   * Get status styling for connection state
   */
  const getConnectionStyling = () => {
    switch (connectionStatus) {
      case 'connected':
        return {
          color: 'bg-green-100 text-green-800',
          icon: <Wifi className="h-4 w-4" />,
          text: 'Connected'
        };
      case 'failed':
        return {
          color: 'bg-red-100 text-red-800',
          icon: <WifiOff className="h-4 w-4" />,
          text: 'Connection Failed'
        };
      default:
        return {
          color: 'bg-gray-100 text-gray-800',
          icon: <Settings className="h-4 w-4" />,
          text: 'Unknown'
        };
    }
  };

  /**
   * Get mode description
   */
  const getModeDescription = (mode: 'real' | 'mock' | 'auto') => {
    switch (mode) {
      case 'real':
        return 'Always use real TempStick API data. Requires internet connection and valid API key.';
      case 'mock':
        return 'Always use mock data for development and testing. No API calls will be made.';
      case 'auto':
        return 'Automatically choose data source. Uses real API when available, falls back to mock data when offline.';
    }
  };

  // Test connectivity on component mount
  useEffect(() => {
    testApiConnectivity();
  }, []);

  const connectionStyling = getConnectionStyling();

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Data Source Selector */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              <Database className="h-5 w-5" />
              TempStick Data Source
            </CardTitle>
            
            <Badge className={`${connectionStyling.color} flex items-center gap-1`}>
              {connectionStyling.icon}
              <span>{connectionStyling.text}</span>
            </Badge>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Mode Selection */}
          <div className="space-y-2">
            <Label htmlFor="data-source-mode">Data Source Mode</Label>
            <Select
              value={currentMode}
              onValueChange={(value: 'real' | 'mock' | 'auto') => handleModeChange(value)}
            >
              <SelectTrigger id="data-source-mode">
                <SelectValue placeholder="Select data source mode" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="auto">
                  <div className="flex items-center gap-2">
                    <RefreshCw className="h-4 w-4" />
                    <div>
                      <div className="font-medium">Auto (Recommended)</div>
                      <div className="text-xs text-muted-foreground">Smart fallback</div>
                    </div>
                  </div>
                </SelectItem>
                <SelectItem value="real">
                  <div className="flex items-center gap-2">
                    <Wifi className="h-4 w-4" />
                    <div>
                      <div className="font-medium">Real API Data</div>
                      <div className="text-xs text-muted-foreground">Live TempStick data</div>
                    </div>
                  </div>
                </SelectItem>
                <SelectItem value="mock">
                  <div className="flex items-center gap-2">
                    <Database className="h-4 w-4" />
                    <div>
                      <div className="font-medium">Mock Data</div>
                      <div className="text-xs text-muted-foreground">Development data</div>
                    </div>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Mode Description */}
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              {getModeDescription(currentMode)}
            </AlertDescription>
          </Alert>

          {/* API Connection Test */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">API Connectivity Test</Label>
              <Button
                variant="outline"
                size="sm"
                onClick={testApiConnectivity}
                disabled={isConnecting}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isConnecting ? 'animate-spin' : ''}`} />
                Test Connection
              </Button>
            </div>

            {/* Endpoint Status */}
            <div className="grid grid-cols-1 gap-2 text-sm">
              <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
                <span>Sensors API</span>
                <div className="flex items-center gap-1">
                  {apiStatus.sensors ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <AlertTriangle className="h-4 w-4 text-red-600" />
                  )}
                  <span className={apiStatus.sensors ? 'text-green-600' : 'text-red-600'}>
                    {apiStatus.sensors ? 'OK' : 'Failed'}
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
                <span>Readings API</span>
                <div className="flex items-center gap-1">
                  {apiStatus.readings ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <AlertTriangle className="h-4 w-4 text-red-600" />
                  )}
                  <span className={apiStatus.readings ? 'text-green-600' : 'text-red-600'}>
                    {apiStatus.readings ? 'OK' : 'Failed'}
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
                <span>Alerts API</span>
                <div className="flex items-center gap-1">
                  {apiStatus.alerts ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <AlertTriangle className="h-4 w-4 text-red-600" />
                  )}
                  <span className={apiStatus.alerts ? 'text-green-600' : 'text-red-600'}>
                    {apiStatus.alerts ? 'OK' : 'Failed'}
                  </span>
                </div>
              </div>
            </div>

            {lastTestAt && (
              <div className="text-xs text-muted-foreground">
                Last tested: {lastTestAt.toLocaleTimeString()}
              </div>
            )}
          </div>

          {/* Connection Failed Alert */}
          {connectionStatus === 'failed' && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                TempStick API connection failed. The system will automatically use mock data until connectivity is restored.
                Check your internet connection and API key configuration.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Current Status Info */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium">Current Status</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span>Selected Mode:</span>
            <Badge variant="outline">{currentMode.toUpperCase()}</Badge>
          </div>
          
          <div className="flex justify-between">
            <span>Data Source:</span>
            <span className="font-medium">
              {currentMode === 'mock' ? 'Mock Data' : 
               connectionStatus === 'connected' ? 'Real API Data' : 'Mock Data (Fallback)'}
            </span>
          </div>
          
          <div className="flex justify-between">
            <span>API Status:</span>
            <span className={
              connectionStatus === 'connected' ? 'text-green-600' : 
              connectionStatus === 'failed' ? 'text-red-600' : 'text-gray-600'
            }>
              {connectionStatus === 'connected' ? 'Online' : 
               connectionStatus === 'failed' ? 'Offline' : 'Unknown'}
            </span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TempStickDataSourceSelector;