/**
 * ExportControls Component Tests
 * 
 * Comprehensive test suite for the export controls component including:
 * - PDF report generation with mocked services
 * - Theme-aware dropdown rendering
 * - Export functionality for various formats
 * - Advanced report generator integration
 * - Email scheduling dialog
 * - Error handling for failed exports
 * - User interaction flows
 * - Accessibility compliance
 */

import React from 'react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ExportControls } from '../ExportControls';
import { renderWithTheme, waitForNextTick } from '../../../__tests__/utils/test-utils';

// Mock services and dependencies
const mockReportService = {
  queueReport: vi.fn(),
  getReportStatus: vi.fn(),
  generatePDFReport: vi.fn(),
  sendReportEmail: vi.fn(),
};

const mockUseSensors = vi.fn();

vi.mock('../../../lib/report-service-integration', () => ({
  reportService: mockReportService,
}));

vi.mock('../../../hooks/useTempStick', () => ({
  useSensors: mockUseSensors,
}));

// Mock child components
vi.mock('../AdvancedReportGenerator', () => ({
  AdvancedReportGenerator: ({ onReportGenerated, defaultDateRange, defaultSensorIds }: any) => (
    <div data-testid="advanced-report-generator">
      <span>Advanced Report Generator</span>
      <span data-testid="date-range">{`${defaultDateRange.startDate.toISOString()} - ${defaultDateRange.endDate.toISOString()}`}</span>
      <span data-testid="sensor-ids">{defaultSensorIds.join(',')}</span>
      <button onClick={() => onReportGenerated({
        success: true,
        filename: 'test-report.pdf',
        downloadUrl: 'https://example.com/report.pdf',
        pdfBuffer: new ArrayBuffer(1000),
      })}>
        Generate Test Report
      </button>
    </div>
  ),
}));

// Mock UI components
vi.mock('../../ui/dropdown-menu', () => ({
  DropdownMenu: ({ children }: any) => <div data-testid="dropdown-menu">{children}</div>,
  DropdownMenuTrigger: ({ children, asChild }: any) => <div data-testid="dropdown-trigger">{children}</div>,
  DropdownMenuContent: ({ children, align }: any) => <div data-testid="dropdown-content" data-align={align}>{children}</div>,
  DropdownMenuItem: ({ children, onClick }: any) => (
    <button data-testid="dropdown-item" onClick={onClick}>{children}</button>
  ),
  DropdownMenuSeparator: () => <hr data-testid="dropdown-separator" />,
}));

vi.mock('../../ui/dialog', () => ({
  Dialog: ({ children, open, onOpenChange }: any) => 
    open ? <div data-testid="dialog" role="dialog">{children}</div> : null,
  DialogContent: ({ children, className }: any) => 
    <div data-testid="dialog-content" className={className}>{children}</div>,
  DialogHeader: ({ children }: any) => <div data-testid="dialog-header">{children}</div>,
  DialogTitle: ({ children }: any) => <h2 data-testid="dialog-title">{children}</h2>,
}));

vi.mock('../../ui/button', () => ({
  Button: ({ children, onClick, disabled, variant, className }: any) => (
    <button 
      onClick={onClick} 
      disabled={disabled} 
      data-variant={variant}
      className={className}
      data-testid="export-button"
    >
      {children}
    </button>
  ),
}));

describe('ExportControls', () => {
  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();
    
    // Default mock implementations
    mockUseSensors.mockReturnValue({
      sensors: [
        { id: 'sensor-001', name: 'Freezer Unit A' },
        { id: 'sensor-002', name: 'Cooler Unit B' },
      ],
      loading: false,
      error: null,
    });

    mockReportService.queueReport.mockResolvedValue('report-123');
    mockReportService.getReportStatus.mockReturnValue({
      id: 'report-123',
      status: 'completed',
      result: {
        success: true,
        filename: 'temperature-report.pdf',
        downloadUrl: 'https://example.com/report.pdf',
      },
    });

    // Mock global fetch for download simulation
    global.fetch = vi.fn();
    
    // Mock document.createElement for download links
    const mockCreateElement = vi.fn();
    mockCreateElement.mockImplementation((tagName: string) => {
      if (tagName === 'a') {
        return {
          href: '',
          download: '',
          click: vi.fn(),
          style: {},
        };
      }
      return {};
    });
    document.createElement = mockCreateElement;
    
    // Mock document.body methods
    document.body.appendChild = vi.fn();
    document.body.removeChild = vi.fn();
    
    // Mock URL methods
    global.URL.createObjectURL = vi.fn().mockReturnValue('blob:mock-url');
    global.URL.revokeObjectURL = vi.fn();
  });

  afterEach(() => {
    vi.clearAllMocks();
    vi.clearAllTimers();
  });

  describe('Basic Rendering', () => {
    it('renders export button with correct initial state', () => {
      renderWithTheme(
        <ExportControls selectedSensorIds={[]} />
      );

      const exportButton = screen.getByTestId('export-button');
      expect(exportButton).toBeInTheDocument();
      expect(exportButton).toHaveTextContent('Export');
      expect(exportButton).not.toBeDisabled();
    });

    it('renders with custom className', () => {
      renderWithTheme(
        <ExportControls 
          selectedSensorIds={[]} 
          className="custom-class"
        />
      );

      const container = screen.getByTestId('export-button').closest('.custom-class');
      expect(container).toBeInTheDocument();
    });

    it('renders dropdown menu structure', () => {
      renderWithTheme(
        <ExportControls selectedSensorIds={['sensor-001']} />
      );

      expect(screen.getByTestId('dropdown-menu')).toBeInTheDocument();
      expect(screen.getByTestId('dropdown-trigger')).toBeInTheDocument();
    });
  });

  describe('Theme Support', () => {
    it('applies light theme styles correctly', () => {
      renderWithTheme(
        <ExportControls selectedSensorIds={[]} />,
        { theme: 'light' }
      );

      expect(document.documentElement).toHaveClass('light');
    });

    it('applies dark theme styles correctly', () => {
      renderWithTheme(
        <ExportControls selectedSensorIds={[]} />,
        { theme: 'dark' }
      );

      expect(document.documentElement).toHaveClass('dark');
    });
  });

  describe('Quick Export Functionality', () => {
    it('handles daily summary export', async () => {
      vi.useFakeTimers();
      const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime });
      
      renderWithTheme(
        <ExportControls selectedSensorIds={['sensor-001']} />
      );

      // Click export button to open dropdown
      const exportButton = screen.getByTestId('export-button');
      await user.click(exportButton);

      // Find and click daily summary option
      const dailySummaryButton = screen.getByText('Daily Summary');
      await user.click(dailySummaryButton);

      expect(mockReportService.queueReport).toHaveBeenCalledWith({
        templateId: 'daily-summary',
        params: expect.objectContaining({
          sensorIds: ['sensor-001'],
          includeAlerts: true,
          includeCharts: true,
          includeHACCPData: true,
          format: 'pdf',
        }),
        delivery: { download: {} },
        options: {
          priority: 'normal',
          compress: true,
        },
      });

      vi.useRealTimers();
    });

    it('handles HACCP compliance export', async () => {
      const user = userEvent.setup();
      
      renderWithTheme(
        <ExportControls selectedSensorIds={['sensor-001', 'sensor-002']} />
      );

      const exportButton = screen.getByTestId('export-button');
      await user.click(exportButton);

      const haccpButton = screen.getByText('HACCP Compliance');
      await user.click(haccpButton);

      expect(mockReportService.queueReport).toHaveBeenCalledWith({
        templateId: 'haccp-compliance',
        params: expect.objectContaining({
          sensorIds: ['sensor-001', 'sensor-002'],
        }),
        delivery: { download: {} },
        options: {
          priority: 'normal',
          compress: true,
        },
      });
    });

    it('handles comprehensive report export', async () => {
      const user = userEvent.setup();
      
      renderWithTheme(
        <ExportControls selectedSensorIds={[]} />
      );

      const exportButton = screen.getByTestId('export-button');
      await user.click(exportButton);

      const comprehensiveButton = screen.getByText('Comprehensive Report');
      await user.click(comprehensiveButton);

      expect(mockReportService.queueReport).toHaveBeenCalledWith({
        templateId: 'comprehensive',
        params: expect.objectContaining({
          sensorIds: [], // Empty when no sensors selected
        }),
        delivery: { download: {} },
        options: {
          priority: 'normal',
          compress: true,
        },
      });
    });

    it('shows exporting state during report generation', async () => {
      vi.useFakeTimers();
      const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime });
      
      // Make queueReport return a pending promise
      mockReportService.queueReport.mockImplementation(() => new Promise(() => {}));
      
      renderWithTheme(
        <ExportControls selectedSensorIds={['sensor-001']} />
      );

      const exportButton = screen.getByTestId('export-button');
      await user.click(exportButton);

      const dailySummaryButton = screen.getByText('Daily Summary');
      await user.click(dailySummaryButton);

      // Button should show exporting state
      await waitFor(() => {
        expect(screen.getByText('Exporting...')).toBeInTheDocument();
        expect(screen.getByTestId('export-button')).toBeDisabled();
      });

      vi.useRealTimers();
    });
  });

  describe('Legacy Export Functionality', () => {
    it('handles Excel export', async () => {
      vi.useFakeTimers();
      const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime });
      
      // Mock window.alert
      const mockAlert = vi.spyOn(window, 'alert').mockImplementation(() => {});
      
      renderWithTheme(
        <ExportControls selectedSensorIds={[]} />
      );

      const exportButton = screen.getByTestId('export-button');
      await user.click(exportButton);

      const excelButton = screen.getByText('Excel Spreadsheet');
      await user.click(excelButton);

      // Advance timers to complete the mock export
      vi.advanceTimersByTime(2000);

      await waitFor(() => {
        expect(mockAlert).toHaveBeenCalledWith('EXCEL export completed! (Legacy functionality)');
      });

      mockAlert.mockRestore();
      vi.useRealTimers();
    });

    it('handles CSV export', async () => {
      vi.useFakeTimers();
      const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime });
      
      const mockAlert = vi.spyOn(window, 'alert').mockImplementation(() => {});
      
      renderWithTheme(
        <ExportControls selectedSensorIds={[]} />
      );

      const exportButton = screen.getByTestId('export-button');
      await user.click(exportButton);

      const csvButton = screen.getByText('CSV Data');
      await user.click(csvButton);

      vi.advanceTimersByTime(2000);

      await waitFor(() => {
        expect(mockAlert).toHaveBeenCalledWith('CSV export completed! (Legacy functionality)');
      });

      mockAlert.mockRestore();
      vi.useRealTimers();
    });
  });

  describe('Advanced Report Generator Integration', () => {
    it('opens advanced report generator dialog', async () => {
      const user = userEvent.setup();
      
      renderWithTheme(
        <ExportControls selectedSensorIds={['sensor-001']} />
      );

      const exportButton = screen.getByTestId('export-button');
      await user.click(exportButton);

      const advancedButton = screen.getByText('Advanced Reports');
      await user.click(advancedButton);

      await waitFor(() => {
        expect(screen.getByTestId('dialog')).toBeInTheDocument();
        expect(screen.getByText('Advanced Report Generation')).toBeInTheDocument();
        expect(screen.getByTestId('advanced-report-generator')).toBeInTheDocument();
      });
    });

    it('passes correct props to advanced report generator', async () => {
      const user = userEvent.setup();
      const selectedSensorIds = ['sensor-001', 'sensor-002'];
      
      renderWithTheme(
        <ExportControls selectedSensorIds={selectedSensorIds} />
      );

      const exportButton = screen.getByTestId('export-button');
      await user.click(exportButton);

      const advancedButton = screen.getByText('Advanced Reports');
      await user.click(advancedButton);

      await waitFor(() => {
        const sensorIds = screen.getByTestId('sensor-ids');
        expect(sensorIds).toHaveTextContent('sensor-001,sensor-002');
        
        const dateRange = screen.getByTestId('date-range');
        expect(dateRange.textContent).toMatch(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z - \d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/);
      });
    });

    it('handles report generation result from advanced generator', async () => {
      const user = userEvent.setup();
      
      renderWithTheme(
        <ExportControls selectedSensorIds={[]} />
      );

      const exportButton = screen.getByTestId('export-button');
      await user.click(exportButton);

      const advancedButton = screen.getByText('Advanced Reports');
      await user.click(advancedButton);

      await waitFor(() => {
        expect(screen.getByTestId('advanced-report-generator')).toBeInTheDocument();
      });

      // Click generate report in the mock component
      const generateButton = screen.getByText('Generate Test Report');
      await user.click(generateButton);

      // Dialog should close and download should be triggered
      await waitFor(() => {
        expect(screen.queryByTestId('dialog')).not.toBeInTheDocument();
      });

      // Verify download link creation
      expect(document.createElement).toHaveBeenCalledWith('a');
    });
  });

  describe('Email Scheduling Dialog', () => {
    it('opens email scheduling dialog', async () => {
      const user = userEvent.setup();
      
      renderWithTheme(
        <ExportControls selectedSensorIds={[]} />
      );

      const exportButton = screen.getByTestId('export-button');
      await user.click(exportButton);

      const emailButton = screen.getByText('Schedule Email Reports');
      await user.click(emailButton);

      await waitFor(() => {
        expect(screen.getByTestId('dialog')).toBeInTheDocument();
        expect(screen.getByText('Schedule Email Reports')).toBeInTheDocument();
        expect(screen.getByText('Enhanced Email Scheduling Available')).toBeInTheDocument();
      });
    });

    it('redirects to advanced reports from email dialog', async () => {
      const user = userEvent.setup();
      
      renderWithTheme(
        <ExportControls selectedSensorIds={[]} />
      );

      const exportButton = screen.getByTestId('export-button');
      await user.click(exportButton);

      const emailButton = screen.getByText('Schedule Email Reports');
      await user.click(emailButton);

      await waitFor(() => {
        expect(screen.getByText('Schedule Email Reports')).toBeInTheDocument();
      });

      const advancedReportsButton = screen.getByText('Open Advanced Reports');
      await user.click(advancedReportsButton);

      await waitFor(() => {
        expect(screen.getByText('Advanced Report Generation')).toBeInTheDocument();
        expect(screen.getByTestId('advanced-report-generator')).toBeInTheDocument();
      });
    });

    it('allows canceling email dialog', async () => {
      const user = userEvent.setup();
      
      renderWithTheme(
        <ExportControls selectedSensorIds={[]} />
      );

      const exportButton = screen.getByTestId('export-button');
      await user.click(exportButton);

      const emailButton = screen.getByText('Schedule Email Reports');
      await user.click(emailButton);

      await waitFor(() => {
        expect(screen.getByText('Schedule Email Reports')).toBeInTheDocument();
      });

      const cancelButton = screen.getByText('Cancel');
      await user.click(cancelButton);

      await waitFor(() => {
        expect(screen.queryByTestId('dialog')).not.toBeInTheDocument();
      });
    });
  });

  describe('Google Sheets Integration', () => {
    it('shows coming soon message for Google Sheets', async () => {
      const user = userEvent.setup();
      const mockAlert = vi.spyOn(window, 'alert').mockImplementation(() => {});
      
      renderWithTheme(
        <ExportControls selectedSensorIds={[]} />
      );

      const exportButton = screen.getByTestId('export-button');
      await user.click(exportButton);

      const googleSheetsButton = screen.getByText('Google Sheets Sync');
      await user.click(googleSheetsButton);

      expect(mockAlert).toHaveBeenCalledWith('Google Sheets integration coming soon!');
      
      mockAlert.mockRestore();
    });
  });

  describe('Error Handling', () => {
    it('handles report generation errors', async () => {
      vi.useFakeTimers();
      const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime });
      const mockAlert = vi.spyOn(window, 'alert').mockImplementation(() => {});
      
      mockReportService.queueReport.mockRejectedValue(new Error('Network error'));
      
      renderWithTheme(
        <ExportControls selectedSensorIds={['sensor-001']} />
      );

      const exportButton = screen.getByTestId('export-button');
      await user.click(exportButton);

      const dailySummaryButton = screen.getByText('Daily Summary');
      await user.click(dailySummaryButton);

      await waitFor(() => {
        expect(mockAlert).toHaveBeenCalledWith('Export failed. Please try again.');
      });

      // Button should be re-enabled
      expect(screen.getByTestId('export-button')).not.toBeDisabled();
      expect(screen.getByText('Export')).toBeInTheDocument();

      mockAlert.mockRestore();
      vi.useRealTimers();
    });

    it('handles failed report status', async () => {
      vi.useFakeTimers();
      const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime });
      const mockAlert = vi.spyOn(window, 'alert').mockImplementation(() => {});
      
      mockReportService.getReportStatus.mockReturnValue({
        id: 'report-123',
        status: 'failed',
        error: 'PDF generation failed',
      });
      
      renderWithTheme(
        <ExportControls selectedSensorIds={['sensor-001']} />
      );

      const exportButton = screen.getByTestId('export-button');
      await user.click(exportButton);

      const dailySummaryButton = screen.getByText('Daily Summary');
      await user.click(dailySummaryButton);

      // Advance timer to trigger polling
      vi.advanceTimersByTime(1000);

      await waitFor(() => {
        expect(mockAlert).toHaveBeenCalledWith('Export failed: PDF generation failed');
      });

      mockAlert.mockRestore();
      vi.useRealTimers();
    });

    it('handles polling timeout', async () => {
      vi.useFakeTimers();
      const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime });
      
      // Mock report that never completes
      mockReportService.getReportStatus.mockReturnValue({
        id: 'report-123',
        status: 'processing',
      });
      
      renderWithTheme(
        <ExportControls selectedSensorIds={['sensor-001']} />
      );

      const exportButton = screen.getByTestId('export-button');
      await user.click(exportButton);

      const dailySummaryButton = screen.getByText('Daily Summary');
      await user.click(dailySummaryButton);

      // Advance timer to trigger timeout (5 minutes)
      vi.advanceTimersByTime(5 * 60 * 1000);

      // Button should be re-enabled after timeout
      await waitFor(() => {
        expect(screen.getByTestId('export-button')).not.toBeDisabled();
      });

      vi.useRealTimers();
    });
  });

  describe('Download Functionality', () => {
    it('handles download with URL', async () => {
      const user = userEvent.setup();
      
      renderWithTheme(
        <ExportControls selectedSensorIds={[]} />
      );

      const exportButton = screen.getByTestId('export-button');
      await user.click(exportButton);

      const advancedButton = screen.getByText('Advanced Reports');
      await user.click(advancedButton);

      await waitFor(() => {
        expect(screen.getByTestId('advanced-report-generator')).toBeInTheDocument();
      });

      const generateButton = screen.getByText('Generate Test Report');
      await user.click(generateButton);

      await waitFor(() => {
        expect(document.createElement).toHaveBeenCalledWith('a');
      });
    });

    it('handles download with PDF buffer', async () => {
      const user = userEvent.setup();
      
      // Mock the advanced report generator to return PDF buffer instead of URL
      vi.unmock('../AdvancedReportGenerator');
      vi.doMock('../AdvancedReportGenerator', () => ({
        AdvancedReportGenerator: ({ onReportGenerated }: any) => (
          <div data-testid="advanced-report-generator">
            <button onClick={() => onReportGenerated({
              success: true,
              filename: 'test-report.pdf',
              pdfBuffer: new ArrayBuffer(1000),
            })}>
              Generate Buffer Report
            </button>
          </div>
        ),
      }));
      
      renderWithTheme(
        <ExportControls selectedSensorIds={[]} />
      );

      const exportButton = screen.getByTestId('export-button');
      await user.click(exportButton);

      const advancedButton = screen.getByText('Advanced Reports');
      await user.click(advancedButton);

      await waitFor(() => {
        expect(screen.getByTestId('advanced-report-generator')).toBeInTheDocument();
      });

      const generateButton = screen.getByText('Generate Buffer Report');
      await user.click(generateButton);

      await waitFor(() => {
        expect(global.URL.createObjectURL).toHaveBeenCalled();
        expect(document.createElement).toHaveBeenCalledWith('a');
      });
    });
  });

  describe('Accessibility', () => {
    it('provides proper button accessibility', () => {
      renderWithTheme(
        <ExportControls selectedSensorIds={[]} />
      );

      const exportButton = screen.getByRole('button');
      expect(exportButton).toBeInTheDocument();
      expect(exportButton).toHaveTextContent('Export');
    });

    it('provides proper dialog accessibility', async () => {
      const user = userEvent.setup();
      
      renderWithTheme(
        <ExportControls selectedSensorIds={[]} />
      );

      const exportButton = screen.getByTestId('export-button');
      await user.click(exportButton);

      const advancedButton = screen.getByText('Advanced Reports');
      await user.click(advancedButton);

      await waitFor(() => {
        const dialog = screen.getByRole('dialog');
        expect(dialog).toBeInTheDocument();
        expect(screen.getByText('Advanced Report Generation')).toBeInTheDocument();
      });
    });

    it('maintains focus management in dialogs', async () => {
      const user = userEvent.setup();
      
      renderWithTheme(
        <ExportControls selectedSensorIds={[]} />
      );

      const exportButton = screen.getByTestId('export-button');
      await user.click(exportButton);

      const emailButton = screen.getByText('Schedule Email Reports');
      await user.click(emailButton);

      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument();
      });

      // Should be able to navigate through dialog buttons
      const cancelButton = screen.getByText('Cancel');
      const advancedButton = screen.getByText('Open Advanced Reports');
      
      expect(cancelButton).toBeInTheDocument();
      expect(advancedButton).toBeInTheDocument();
    });
  });

  describe('Date Range Handling', () => {
    it('uses default date range when none provided', () => {
      const now = new Date();
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      
      renderWithTheme(
        <ExportControls selectedSensorIds={[]} />
      );

      // Default date range should be last 24 hours
      // This is tested indirectly through the report service calls
      expect(screen.getByTestId('export-button')).toBeInTheDocument();
    });

    it('uses custom date range when provided', async () => {
      const user = userEvent.setup();
      const customDateRange = {
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-01-02'),
      };
      
      renderWithTheme(
        <ExportControls 
          selectedSensorIds={[]} 
          defaultDateRange={customDateRange}
        />
      );

      const exportButton = screen.getByTestId('export-button');
      await user.click(exportButton);

      const advancedButton = screen.getByText('Advanced Reports');
      await user.click(advancedButton);

      await waitFor(() => {
        const dateRange = screen.getByTestId('date-range');
        expect(dateRange.textContent).toContain('2024-01-01');
        expect(dateRange.textContent).toContain('2024-01-02');
      });
    });
  });
});