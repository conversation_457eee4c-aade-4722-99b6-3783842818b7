/**
 * SensorDashboard Component Tests
 * 
 * Comprehensive test suite for the main temperature sensor dashboard including:
 * - Component rendering with various data states
 * - Dark/light theme styling validation
 * - User interactions and state management
 * - Real-time data updates simulation
 * - Error handling and offline scenarios
 * - Performance testing with large datasets
 * - Accessibility compliance
 */

import React from 'react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { SensorDashboard } from '../SensorDashboard';
import { renderWithTheme, createMockSensorDataset, expectThemeStyles, waitForNextTick } from '../../../__tests__/utils/test-utils';
import { createMockSupabaseClient, mockScenarios } from '../../../__tests__/mocks/supabase-mocks';

// Mock hooks
const mockUseSensorStatuses = vi.fn();
const mockUseTemperatureAlerts = vi.fn();
const mockUseTemperatureSync = vi.fn();

vi.mock('../../../hooks/useTempStick', () => ({
  useSensorStatuses: mockUseSensorStatuses,
  useTemperatureAlerts: mockUseTemperatureAlerts,
  useTemperatureSync: mockUseTemperatureSync,
}));

// Mock child components to isolate SensorDashboard testing
vi.mock('../SensorTile', () => ({
  SensorTile: ({ sensorStatus, selected, onSelectionChange }: any) => (
    <div 
      data-testid={`sensor-tile-${sensorStatus.sensor.id}`}
      role="checkbox"
      aria-checked={selected}
      onClick={() => onSelectionChange(!selected)}
    >
      <span>{sensorStatus.sensor.name}</span>
      <span data-testid="temperature">
        {sensorStatus.latest_reading?.temperature.toFixed(1)}°F
      </span>
      <span data-testid="status">{sensorStatus.sensor.isOnline ? 'online' : 'offline'}</span>
    </div>
  ),
}));

vi.mock('../TemperatureTrends', () => ({
  TemperatureTrends: ({ sensorIds }: any) => (
    <div data-testid="temperature-trends">
      <span>Trends for sensors: {sensorIds ? sensorIds.join(', ') : 'all'}</span>
    </div>
  ),
}));

vi.mock('../AlertsPanel', () => ({
  AlertsPanel: () => <div data-testid="alerts-panel">Active Alerts Panel</div>,
}));

vi.mock('../ExportControls', () => ({
  ExportControls: ({ selectedSensorIds }: any) => (
    <div data-testid="export-controls">
      <span>Export {selectedSensorIds.length} sensors</span>
    </div>
  ),
}));

vi.mock('../SensorConfiguration', () => ({
  SensorConfiguration: ({ onClose }: any) => (
    <div data-testid="sensor-configuration" role="dialog">
      <button onClick={onClose}>Close Configuration</button>
    </div>
  ),
}));

describe('SensorDashboard', () => {
  let mockSupabaseClient: ReturnType<typeof createMockSupabaseClient>;
  
  beforeEach(() => {
    mockSupabaseClient = createMockSupabaseClient();
    
    // Reset all mocks to default success state
    mockUseSensorStatuses.mockReturnValue({
      sensorStatuses: [],
      summary: {
        totalSensors: 0,
        onlineSensors: 0,
        activeAlerts: 0,
        criticalAlerts: 0,
        averageTemperature: null,
        temperatureRange: null,
      },
      loading: false,
      error: null,
      refetch: vi.fn(),
    });

    mockUseTemperatureAlerts.mockReturnValue({
      alerts: [],
      unreadCount: 0,
      loading: false,
      error: null,
      dismissAlert: vi.fn(),
      resolveAlert: vi.fn(),
      refetch: vi.fn(),
    });

    mockUseTemperatureSync.mockReturnValue({
      sync: vi.fn(),
      syncing: false,
      lastSyncTime: new Date(),
      error: null,
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
    mockSupabaseClient.__clearMocks();
  });

  describe('Basic Rendering', () => {
    it('renders the main dashboard header', () => {
      renderWithTheme(<SensorDashboard />);
      
      expect(screen.getByRole('heading', { name: /temperature monitoring/i })).toBeInTheDocument();
      expect(screen.getByText(/real-time temperature monitoring and haccp compliance tracking/i)).toBeInTheDocument();
    });

    it('renders with empty state when no sensors', () => {
      renderWithTheme(<SensorDashboard />);
      
      expect(screen.getByText(/no sensors found/i)).toBeInTheDocument();
      expect(screen.getByText(/connect your tempstick sensors/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /sync sensors/i })).toBeInTheDocument();
    });

    it('renders summary cards with correct data', () => {
      const mockData = createMockSensorDataset(3);
      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: mockData,
        summary: {
          totalSensors: 3,
          onlineSensors: 2,
          activeAlerts: 1,
          criticalAlerts: 0,
          averageTemperature: -15.5,
          temperatureRange: { min: -20.0, max: -10.0 },
        },
        loading: false,
        error: null,
        refetch: vi.fn(),
      });

      renderWithTheme(<SensorDashboard />);

      expect(screen.getByText('3')).toBeInTheDocument(); // Total sensors
      expect(screen.getByText('2 online')).toBeInTheDocument(); // Online sensors
      expect(screen.getByText('1')).toBeInTheDocument(); // Active alerts
      expect(screen.getByText('-15.5°F')).toBeInTheDocument(); // Average temp
      expect(screen.getByText('-20.0° - -10.0°F')).toBeInTheDocument(); // Temperature range
    });
  });

  describe('Theme Support', () => {
    it('applies light theme styles correctly', () => {
      const mockData = createMockSensorDataset(1);
      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: mockData,
        summary: {
          totalSensors: 1,
          onlineSensors: 1,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: -18.5,
          temperatureRange: null,
        },
        loading: false,
        error: null,
        refetch: vi.fn(),
      });

      const { container } = renderWithTheme(<SensorDashboard />, { theme: 'light' });
      
      // Check that light theme is applied to root
      expect(document.documentElement).toHaveClass('light');
      
      // Check that dashboard elements use theme-aware classes
      const heading = screen.getByRole('heading', { name: /temperature monitoring/i });
      expect(heading).toBeInTheDocument();
    });

    it('applies dark theme styles correctly', () => {
      const mockData = createMockSensorDataset(1);
      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: mockData,
        summary: {
          totalSensors: 1,
          onlineSensors: 1,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: -18.5,
          temperatureRange: null,
        },
        loading: false,
        error: null,
        refetch: vi.fn(),
      });

      renderWithTheme(<SensorDashboard />, { theme: 'dark' });
      
      // Check that dark theme is applied
      expect(document.documentElement).toHaveClass('dark');
    });

    it('updates theme dynamically', () => {
      const { rerenderWithTheme } = renderWithTheme(<SensorDashboard />, { theme: 'light' });
      
      expect(document.documentElement).toHaveClass('light');
      
      rerenderWithTheme('dark');
      expect(document.documentElement).toHaveClass('dark');
    });
  });

  describe('Sensor Selection', () => {
    it('allows selecting individual sensors', async () => {
      const user = userEvent.setup();
      const mockData = createMockSensorDataset(3);
      
      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: mockData,
        summary: {
          totalSensors: 3,
          onlineSensors: 3,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: -18.5,
          temperatureRange: null,
        },
        loading: false,
        error: null,
        refetch: vi.fn(),
      });

      renderWithTheme(<SensorDashboard />);

      // Initially no sensors selected
      expect(screen.getByText('0 selected')).toBeInTheDocument();

      // Select first sensor
      const firstSensorTile = screen.getByTestId(`sensor-tile-${mockData[0].sensor.id}`);
      await user.click(firstSensorTile);

      await waitFor(() => {
        expect(screen.getByText('1 selected')).toBeInTheDocument();
      });

      // Select second sensor
      const secondSensorTile = screen.getByTestId(`sensor-tile-${mockData[1].sensor.id}`);
      await user.click(secondSensorTile);

      await waitFor(() => {
        expect(screen.getByText('2 selected')).toBeInTheDocument();
      });

      // Deselect first sensor
      await user.click(firstSensorTile);

      await waitFor(() => {
        expect(screen.getByText('1 selected')).toBeInTheDocument();
      });
    });

    it('clears all selections when clear button clicked', async () => {
      const user = userEvent.setup();
      const mockData = createMockSensorDataset(2);
      
      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: mockData,
        summary: {
          totalSensors: 2,
          onlineSensors: 2,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: -18.5,
          temperatureRange: null,
        },
        loading: false,
        error: null,
        refetch: vi.fn(),
      });

      renderWithTheme(<SensorDashboard />);

      // Select both sensors
      const firstSensorTile = screen.getByTestId(`sensor-tile-${mockData[0].sensor.id}`);
      const secondSensorTile = screen.getByTestId(`sensor-tile-${mockData[1].sensor.id}`);
      
      await user.click(firstSensorTile);
      await user.click(secondSensorTile);

      await waitFor(() => {
        expect(screen.getByText('2 selected')).toBeInTheDocument();
      });

      // Clear selections
      const clearButton = screen.getByRole('button', { name: /clear selection/i });
      await user.click(clearButton);

      await waitFor(() => {
        expect(screen.getByText('0 selected')).toBeInTheDocument();
      });
    });

    it('updates temperature trends with selected sensors', async () => {
      const user = userEvent.setup();
      const mockData = createMockSensorDataset(2);
      
      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: mockData,
        summary: {
          totalSensors: 2,
          onlineSensors: 2,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: -18.5,
          temperatureRange: null,
        },
        loading: false,
        error: null,
        refetch: vi.fn(),
      });

      renderWithTheme(<SensorDashboard />);

      // Initially shows trends for all sensors
      expect(screen.getByText('Trends for sensors: all')).toBeInTheDocument();

      // Select first sensor
      const firstSensorTile = screen.getByTestId(`sensor-tile-${mockData[0].sensor.id}`);
      await user.click(firstSensorTile);

      await waitFor(() => {
        expect(screen.getByText(`Trends for sensors: ${mockData[0].sensor.id}`)).toBeInTheDocument();
      });
    });
  });

  describe('Sync Functionality', () => {
    it('calls sync function when sync button clicked', async () => {
      const user = userEvent.setup();
      const mockSync = vi.fn();
      
      mockUseTemperatureSync.mockReturnValue({
        sync: mockSync,
        syncing: false,
        lastSyncTime: new Date(),
        error: null,
      });

      renderWithTheme(<SensorDashboard />);

      const syncButton = screen.getByRole('button', { name: /sync/i });
      await user.click(syncButton);

      expect(mockSync).toHaveBeenCalledTimes(1);
    });

    it('shows syncing state when sync is in progress', () => {
      mockUseTemperatureSync.mockReturnValue({
        sync: vi.fn(),
        syncing: true,
        lastSyncTime: new Date(),
        error: null,
      });

      renderWithTheme(<SensorDashboard />);

      const syncButton = screen.getByRole('button', { name: /syncing\.\.\./i });
      expect(syncButton).toBeDisabled();
      
      // Check for spinning icon
      const spinningIcon = syncButton.querySelector('[class*="animate-spin"]');
      expect(spinningIcon).toBeInTheDocument();
    });

    it('displays sync error when sync fails', () => {
      const syncError = 'Unable to connect to TempStick API';
      mockUseTemperatureSync.mockReturnValue({
        sync: vi.fn(),
        syncing: false,
        lastSyncTime: null,
        error: syncError,
      });

      renderWithTheme(<SensorDashboard />);

      expect(screen.getByText(/sync error:/i)).toBeInTheDocument();
      expect(screen.getByText(syncError)).toBeInTheDocument();
    });

    it('formats last sync time correctly', () => {
      const now = new Date();
      const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);
      
      mockUseTemperatureSync.mockReturnValue({
        sync: vi.fn(),
        syncing: false,
        lastSyncTime: fiveMinutesAgo,
        error: null,
      });

      renderWithTheme(<SensorDashboard />);

      expect(screen.getByText('5 minutes ago')).toBeInTheDocument();
    });
  });

  describe('Configuration Modal', () => {
    it('opens configuration modal when configure button clicked', async () => {
      const user = userEvent.setup();
      
      renderWithTheme(<SensorDashboard />);

      const configButton = screen.getByRole('button', { name: /configure/i });
      await user.click(configButton);

      await waitFor(() => {
        expect(screen.getByTestId('sensor-configuration')).toBeInTheDocument();
        expect(screen.getByRole('dialog')).toBeInTheDocument();
      });
    });

    it('closes configuration modal when close button clicked', async () => {
      const user = userEvent.setup();
      
      renderWithTheme(<SensorDashboard />);

      // Open modal
      const configButton = screen.getByRole('button', { name: /configure/i });
      await user.click(configButton);

      await waitFor(() => {
        expect(screen.getByTestId('sensor-configuration')).toBeInTheDocument();
      });

      // Close modal
      const closeButton = screen.getByRole('button', { name: /close configuration/i });
      await user.click(closeButton);

      await waitFor(() => {
        expect(screen.queryByTestId('sensor-configuration')).not.toBeInTheDocument();
      });
    });
  });

  describe('Alerts Display', () => {
    it('shows alerts panel when alerts are present', () => {
      mockUseTemperatureAlerts.mockReturnValue({
        alerts: [
          {
            id: 'alert-1',
            sensor_id: 'sensor-1',
            alert_type: 'high_temp',
            severity: 'critical',
            message: 'Temperature too high',
            created_at: new Date().toISOString(),
            is_resolved: false,
          }
        ],
        unreadCount: 1,
        loading: false,
        error: null,
        dismissAlert: vi.fn(),
        resolveAlert: vi.fn(),
        refetch: vi.fn(),
      });

      renderWithTheme(<SensorDashboard />);

      expect(screen.getByText(/active alerts/i)).toBeInTheDocument();
      expect(screen.getByTestId('alerts-panel')).toBeInTheDocument();
    });

    it('hides alerts panel when no alerts', () => {
      mockUseTemperatureAlerts.mockReturnValue({
        alerts: [],
        unreadCount: 0,
        loading: false,
        error: null,
        dismissAlert: vi.fn(),
        resolveAlert: vi.fn(),
        refetch: vi.fn(),
      });

      renderWithTheme(<SensorDashboard />);

      expect(screen.queryByText(/active alerts/i)).not.toBeInTheDocument();
      expect(screen.queryByTestId('alerts-panel')).not.toBeInTheDocument();
    });
  });

  describe('Temperature Trends', () => {
    it('shows temperature trends when sensors are present', () => {
      const mockData = createMockSensorDataset(2);
      
      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: mockData,
        summary: {
          totalSensors: 2,
          onlineSensors: 2,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: -18.5,
          temperatureRange: null,
        },
        loading: false,
        error: null,
        refetch: vi.fn(),
      });

      renderWithTheme(<SensorDashboard />);

      expect(screen.getByText(/temperature trends/i)).toBeInTheDocument();
      expect(screen.getByTestId('temperature-trends')).toBeInTheDocument();
    });

    it('hides temperature trends when no sensors', () => {
      renderWithTheme(<SensorDashboard />);

      expect(screen.queryByText(/temperature trends/i)).not.toBeInTheDocument();
      expect(screen.queryByTestId('temperature-trends')).not.toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('handles hook errors gracefully', () => {
      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: [],
        summary: {
          totalSensors: 0,
          onlineSensors: 0,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: null,
          temperatureRange: null,
        },
        loading: false,
        error: 'Failed to load sensor data',
        refetch: vi.fn(),
      });

      // Should render without crashing even with errors
      expect(() => renderWithTheme(<SensorDashboard />)).not.toThrow();
      
      // Empty state should still be shown
      expect(screen.getByText(/no sensors found/i)).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper heading hierarchy', () => {
      const mockData = createMockSensorDataset(1);
      
      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: mockData,
        summary: {
          totalSensors: 1,
          onlineSensors: 1,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: -18.5,
          temperatureRange: null,
        },
        loading: false,
        error: null,
        refetch: vi.fn(),
      });

      renderWithTheme(<SensorDashboard />);

      const h1 = screen.getByRole('heading', { level: 1, name: /temperature monitoring/i });
      const h2Elements = screen.getAllByRole('heading', { level: 2 });
      
      expect(h1).toBeInTheDocument();
      expect(h2Elements.length).toBeGreaterThan(0);
    });

    it('provides accessible button labels', () => {
      renderWithTheme(<SensorDashboard />);

      const syncButton = screen.getByRole('button', { name: /sync/i });
      const configButton = screen.getByRole('button', { name: /configure/i });
      
      expect(syncButton).toBeInTheDocument();
      expect(configButton).toBeInTheDocument();
    });

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      
      renderWithTheme(<SensorDashboard />);

      // Tab through interactive elements
      await user.tab();
      expect(screen.getByRole('button', { name: /configure/i })).toHaveFocus();
      
      await user.tab();
      expect(screen.getByRole('button', { name: /sync/i })).toHaveFocus();
    });
  });

  describe('Performance', () => {
    it('renders efficiently with large sensor dataset', async () => {
      const largeDataset = createMockSensorDataset(100);
      
      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: largeDataset,
        summary: {
          totalSensors: 100,
          onlineSensors: 90,
          activeAlerts: 5,
          criticalAlerts: 1,
          averageTemperature: -18.5,
          temperatureRange: { min: -25.0, max: 5.0 },
        },
        loading: false,
        error: null,
        refetch: vi.fn(),
      });

      const startTime = performance.now();
      renderWithTheme(<SensorDashboard />);
      const renderTime = performance.now() - startTime;

      // Verify all sensors are rendered
      expect(screen.getByText('100')).toBeInTheDocument(); // Total sensors
      
      // Performance should be reasonable (under 100ms for 100 sensors)
      expect(renderTime).toBeLessThan(100);
    });

    it('handles rapid state updates efficiently', async () => {
      const mockData = createMockSensorDataset(5);
      let renderCount = 0;
      
      const TestWrapper = () => {
        renderCount++;
        return <SensorDashboard />;
      };

      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: mockData,
        summary: {
          totalSensors: 5,
          onlineSensors: 5,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: -18.5,
          temperatureRange: null,
        },
        loading: false,
        error: null,
        refetch: vi.fn(),
      });

      const { rerender } = renderWithTheme(<TestWrapper />);
      
      const initialRenderCount = renderCount;

      // Simulate multiple rapid updates
      for (let i = 0; i < 10; i++) {
        mockUseSensorStatuses.mockReturnValue({
          sensorStatuses: mockData,
          summary: {
            totalSensors: 5,
            onlineSensors: 5 - i,
            activeAlerts: i,
            criticalAlerts: 0,
            averageTemperature: -18.5 - i,
            temperatureRange: null,
          },
          loading: false,
          error: null,
          refetch: vi.fn(),
        });
        
        rerender(<TestWrapper />);
        await waitForNextTick();
      }

      // Should not cause excessive re-renders
      const finalRenderCount = renderCount - initialRenderCount;
      expect(finalRenderCount).toBeLessThan(20); // Reasonable re-render count
    });
  });
});