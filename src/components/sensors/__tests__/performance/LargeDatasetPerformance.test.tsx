/**
 * Large Dataset Performance Tests
 * 
 * Performance benchmarking and stress tests for sensor dashboard components with:
 * - Large sensor datasets (100+, 500+, 1000+ sensors)
 * - Rendering performance benchmarks
 * - Memory usage monitoring
 * - Virtual scrolling validation
 * - Search and filtering performance
 * - Real-time update performance at scale
 * - Component re-render optimization validation
 * - Bundle size impact analysis
 */

import React, { useState, useMemo, useCallback } from 'react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { screen, render, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithTheme, createMockSensorDataset, measureRenderTime, waitForNextTick } from '../../../../__tests__/utils/test-utils';
import { SensorDashboard } from '../../SensorDashboard';
import { SensorTile } from '../../SensorTile';

// Mock hooks for performance testing
const mockUseSensorStatuses = vi.fn();
const mockUseTemperatureAlerts = vi.fn();
const mockUseTemperatureSync = vi.fn();

vi.mock('../../../../hooks/useTempStick', () => ({
  useSensorStatuses: mockUseSensorStatuses,
  useTemperatureAlerts: mockUseTemperatureAlerts,
  useTemperatureSync: mockUseTemperatureSync,
}));

// Mock child components for isolated performance testing
vi.mock('../../SensorTile', () => ({
  SensorTile: React.memo(({ sensorStatus, selected, onSelectionChange }: any) => (
    <div data-testid={`sensor-tile-${sensorStatus.sensor.id}`}>
      <span>{sensorStatus.sensor.name}</span>
      <span>{sensorStatus.latest_reading?.temperature}°F</span>
      <input 
        type="checkbox" 
        checked={selected} 
        onChange={(e) => onSelectionChange(e.target.checked)}
      />
    </div>
  )),
}));

vi.mock('../../TemperatureTrends', () => ({
  TemperatureTrends: React.memo(({ sensorIds }: any) => (
    <div data-testid="temperature-trends">
      Trends for {sensorIds?.length || 'all'} sensors
    </div>
  )),
}));

vi.mock('../../AlertsPanel', () => ({
  AlertsPanel: React.memo(() => (
    <div data-testid="alerts-panel">Alerts Panel</div>
  )),
}));

vi.mock('../../ExportControls', () => ({
  ExportControls: React.memo(() => (
    <div data-testid="export-controls">Export Controls</div>
  )),
}));

vi.mock('../../SensorConfiguration', () => ({
  SensorConfiguration: React.memo(() => (
    <div data-testid="sensor-configuration">Configuration</div>
  )),
}));

// Performance test component with optimizations
const PerformanceTestDashboard = ({ sensorCount }: { sensorCount: number }) => {
  const largeSensorDataset = useMemo(() => 
    createMockSensorDataset(sensorCount), 
    [sensorCount]
  );
  
  mockUseSensorStatuses.mockReturnValue({
    sensorStatuses: largeSensorDataset,
    summary: {
      totalSensors: sensorCount,
      onlineSensors: Math.floor(sensorCount * 0.95),
      activeAlerts: Math.floor(sensorCount * 0.1),
      criticalAlerts: Math.floor(sensorCount * 0.02),
      averageTemperature: -18.5,
      temperatureRange: { min: -25.0, max: -10.0 },
    },
    loading: false,
    error: null,
    refetch: vi.fn(),
  });

  return <SensorDashboard />;
};

// Virtualized sensor list component for large datasets
const VirtualizedSensorList = ({ sensors }: { sensors: any[] }) => {
  const [visibleRange, setVisibleRange] = useState({ start: 0, end: 50 });
  const [selectedSensors, setSelectedSensors] = useState<Set<string>>(new Set());
  
  const visibleSensors = useMemo(() => 
    sensors.slice(visibleRange.start, visibleRange.end),
    [sensors, visibleRange]
  );
  
  const handleSelectionChange = useCallback((sensorId: string, selected: boolean) => {
    setSelectedSensors(prev => {
      const newSet = new Set(prev);
      if (selected) {
        newSet.add(sensorId);
      } else {
        newSet.delete(sensorId);
      }
      return newSet;
    });
  }, []);
  
  return (
    <div data-testid="virtualized-sensor-list">
      <div data-testid="sensor-count">{sensors.length}</div>
      <div data-testid="visible-count">{visibleSensors.length}</div>
      <div data-testid="selected-count">{selectedSensors.size}</div>
      {visibleSensors.map(sensor => (
        <SensorTile
          key={sensor.sensor.id}
          sensorStatus={sensor}
          selected={selectedSensors.has(sensor.sensor.id)}
          onSelectionChange={(selected) => 
            handleSelectionChange(sensor.sensor.id, selected)
          }
        />
      ))}
    </div>
  );
};

describe('Large Dataset Performance Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Default mock setup
    mockUseTemperatureAlerts.mockReturnValue({
      alerts: [],
      unreadCount: 0,
      loading: false,
      error: null,
      dismissAlert: vi.fn(),
      resolveAlert: vi.fn(),
      refetch: vi.fn(),
    });

    mockUseTemperatureSync.mockReturnValue({
      sync: vi.fn(),
      syncing: false,
      lastSyncTime: new Date(),
      error: null,
    });

    // Mock performance.mark and performance.measure
    global.performance.mark = vi.fn();
    global.performance.measure = vi.fn();
    global.performance.getEntriesByName = vi.fn().mockReturnValue([
      { duration: 16.7 } // Simulate 60 FPS frame time
    ]);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering Performance Benchmarks', () => {
    it('renders 100 sensors within performance budget', async () => {
      const sensorCount = 100;
      const startTime = performance.now();
      
      renderWithTheme(<PerformanceTestDashboard sensorCount={sensorCount} />);
      
      await waitFor(() => {
        expect(screen.getByText(`${sensorCount}`)).toBeInTheDocument();
      });
      
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      // Should render 100 sensors in under 200ms
      expect(renderTime).toBeLessThan(200);
      
      // Verify all sensors are rendered
      const sensorElements = screen.getAllByTestId(/^sensor-tile-/);
      expect(sensorElements).toHaveLength(sensorCount);
    });

    it('maintains performance with 500 sensors', async () => {
      const sensorCount = 500;
      const startTime = performance.now();
      
      renderWithTheme(<PerformanceTestDashboard sensorCount={sensorCount} />);
      
      await waitFor(() => {
        expect(screen.getByText(`${sensorCount}`)).toBeInTheDocument();
      });
      
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      // Should render 500 sensors in under 1 second
      expect(renderTime).toBeLessThan(1000);
      
      // Verify performance metrics
      expect(renderTime / sensorCount).toBeLessThan(2); // Under 2ms per sensor
    });

    it('handles 1000 sensors with virtualization', async () => {
      const largeSensorDataset = createMockSensorDataset(1000);
      const startTime = performance.now();
      
      renderWithTheme(<VirtualizedSensorList sensors={largeSensorDataset} />);
      
      await waitFor(() => {
        expect(screen.getByTestId('sensor-count')).toHaveTextContent('1000');
        expect(screen.getByTestId('visible-count')).toHaveTextContent('50');
      });
      
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      // Virtualized rendering should be fast regardless of total count
      expect(renderTime).toBeLessThan(100);
      
      // Only visible sensors should be rendered
      const renderedSensors = screen.getAllByTestId(/^sensor-tile-/);
      expect(renderedSensors.length).toBeLessThanOrEqual(50);
    });

    it('benchmarks different sensor counts', async () => {
      const sensorCounts = [10, 50, 100, 250, 500];
      const benchmarkResults: Record<number, number> = {};
      
      for (const count of sensorCounts) {
        const startTime = performance.now();
        
        const { unmount } = renderWithTheme(
          <PerformanceTestDashboard sensorCount={count} />
        );
        
        await waitFor(() => {
          expect(screen.getByText(`${count}`)).toBeInTheDocument();
        });
        
        const endTime = performance.now();
        benchmarkResults[count] = endTime - startTime;
        
        unmount();
      }
      
      // Verify performance scales reasonably
      expect(benchmarkResults[10]).toBeLessThan(50);
      expect(benchmarkResults[50]).toBeLessThan(100);
      expect(benchmarkResults[100]).toBeLessThan(200);
      expect(benchmarkResults[250]).toBeLessThan(500);
      expect(benchmarkResults[500]).toBeLessThan(1000);
      
      // Performance should scale roughly linearly, not exponentially
      const scalingFactor = benchmarkResults[500] / benchmarkResults[50];
      expect(scalingFactor).toBeLessThan(15); // Less than 15x slower for 10x more sensors
    });
  });

  describe('Memory Usage Monitoring', () => {
    it('monitors memory usage with large datasets', async () => {
      // Mock memory API
      const mockMemory = {
        usedJSHeapSize: 50000000, // 50MB
        totalJSHeapSize: 100000000, // 100MB
        jsHeapSizeLimit: 2000000000, // 2GB
      };
      
      Object.defineProperty(performance, 'memory', {
        get: () => mockMemory,
        configurable: true,
      });
      
      const initialMemory = performance.memory.usedJSHeapSize;
      
      const { unmount } = renderWithTheme(
        <PerformanceTestDashboard sensorCount={500} />
      );
      
      await waitFor(() => {
        expect(screen.getByText('500')).toBeInTheDocument();
      });
      
      // Simulate memory increase
      mockMemory.usedJSHeapSize = 75000000; // 75MB
      const peakMemory = performance.memory.usedJSHeapSize;
      
      unmount();
      
      // Simulate memory cleanup
      mockMemory.usedJSHeapSize = 55000000; // 55MB
      const finalMemory = performance.memory.usedJSHeapSize;
      
      // Memory usage should be reasonable
      const memoryIncrease = peakMemory - initialMemory;
      expect(memoryIncrease).toBeLessThan(50000000); // Less than 50MB increase
      
      // Memory should be freed after unmount
      const memoryLeak = finalMemory - initialMemory;
      expect(memoryLeak).toBeLessThan(10000000); // Less than 10MB leak
    });

    it('prevents memory leaks with frequent updates', async () => {
      let memoryUsage = 50000000;
      
      Object.defineProperty(performance, 'memory', {
        get: () => ({ usedJSHeapSize: memoryUsage }),
        configurable: true,
      });
      
      const initialMemory = performance.memory.usedJSHeapSize;
      const sensorDataset = createMockSensorDataset(100);
      
      const { rerender, unmount } = renderWithTheme(
        <VirtualizedSensorList sensors={sensorDataset} />
      );
      
      // Simulate 100 rapid updates
      for (let i = 0; i < 100; i++) {
        memoryUsage += 100000; // Simulate small memory increase
        
        const updatedSensors = sensorDataset.map(sensor => ({
          ...sensor,
          latest_reading: {
            ...sensor.latest_reading,
            temperature: sensor.latest_reading.temperature + (i * 0.01),
          },
        }));
        
        rerender(<VirtualizedSensorList sensors={updatedSensors} />);
        await waitForNextTick();
      }
      
      const peakMemory = performance.memory.usedJSHeapSize;
      unmount();
      
      // Memory growth should be bounded
      const memoryGrowth = peakMemory - initialMemory;
      expect(memoryGrowth).toBeLessThan(20000000); // Less than 20MB growth
    });
  });

  describe('Update Performance at Scale', () => {
    it('handles bulk sensor updates efficiently', async () => {
      const sensorCount = 200;
      const largeSensorDataset = createMockSensorDataset(sensorCount);
      
      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: largeSensorDataset,
        summary: {
          totalSensors: sensorCount,
          onlineSensors: sensorCount,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: -18.5,
          temperatureRange: null,
        },
        loading: false,
        error: null,
        refetch: vi.fn(),
      });
      
      const { rerender } = renderWithTheme(<SensorDashboard />);
      
      await waitFor(() => {
        expect(screen.getByText(`${sensorCount}`)).toBeInTheDocument();
      });
      
      // Measure update performance
      const startTime = performance.now();
      
      // Update all sensor temperatures
      const updatedDataset = largeSensorDataset.map(sensor => ({
        ...sensor,
        latest_reading: {
          ...sensor.latest_reading!,
          temperature: sensor.latest_reading!.temperature + 1.0,
          recorded_at: new Date().toISOString(),
        },
      }));
      
      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: updatedDataset,
        summary: {
          totalSensors: sensorCount,
          onlineSensors: sensorCount,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: -17.5,
          temperatureRange: null,
        },
        loading: false,
        error: null,
        refetch: vi.fn(),
      });
      
      rerender(<SensorDashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('-17.5°F')).toBeInTheDocument();
      });
      
      const endTime = performance.now();
      const updateTime = endTime - startTime;
      
      // Bulk update should complete quickly
      expect(updateTime).toBeLessThan(100);
    });

    it('optimizes re-renders with React.memo', async () => {
      const sensorDataset = createMockSensorDataset(50);
      let renderCount = 0;
      
      const MemoizedSensorTile = React.memo(({ sensorStatus }: any) => {
        renderCount++;
        return (
          <div data-testid={`sensor-${sensorStatus.sensor.id}`}>
            {sensorStatus.sensor.name}: {sensorStatus.latest_reading?.temperature}°F
          </div>
        );
      });
      
      const OptimizedSensorGrid = ({ sensors }: { sensors: any[] }) => (
        <div>
          {sensors.map(sensor => (
            <MemoizedSensorTile key={sensor.sensor.id} sensorStatus={sensor} />
          ))}
        </div>
      );
      
      const { rerender } = renderWithTheme(
        <OptimizedSensorGrid sensors={sensorDataset} />
      );
      
      const initialRenderCount = renderCount;
      
      // Update only one sensor
      const updatedDataset = sensorDataset.map((sensor, index) => 
        index === 0 
          ? {
              ...sensor,
              latest_reading: {
                ...sensor.latest_reading!,
                temperature: sensor.latest_reading!.temperature + 1.0,
              },
            }
          : sensor
      );
      
      rerender(<OptimizedSensorGrid sensors={updatedDataset} />);
      
      await waitFor(() => {
        expect(screen.getAllByTestId(/^sensor-/)).toHaveLength(50);
      });
      
      // Only the updated sensor should re-render (plus initial render)
      const finalRenderCount = renderCount;
      const reRenderCount = finalRenderCount - initialRenderCount;
      
      // Should be much less than total sensor count due to memoization
      expect(reRenderCount).toBeLessThan(10);
    });

    it('measures frame rate during animations', async () => {
      const sensorCount = 100;
      const sensorDataset = createMockSensorDataset(sensorCount);
      let frameCount = 0;
      let lastFrameTime = performance.now();
      
      const AnimatedSensorGrid = () => {
        const [animationFrame, setAnimationFrame] = useState(0);
        
        React.useEffect(() => {
          const animate = () => {
            frameCount++;
            const currentTime = performance.now();
            const deltaTime = currentTime - lastFrameTime;
            
            if (deltaTime >= 16.67) { // Target 60 FPS
              setAnimationFrame(prev => prev + 1);
              lastFrameTime = currentTime;
            }
            
            if (frameCount < 60) { // Animate for ~1 second
              requestAnimationFrame(animate);
            }
          };
          
          requestAnimationFrame(animate);
        }, []);
        
        return (
          <div data-testid="animated-grid">
            <div data-testid="frame-count">{animationFrame}</div>
            {sensorDataset.slice(0, 20).map(sensor => ( // Show subset for animation
              <div key={sensor.sensor.id} style={{ 
                transform: `translateX(${Math.sin(animationFrame * 0.1) * 10}px)` 
              }}>
                {sensor.sensor.name}
              </div>
            ))}
          </div>
        );
      };
      
      renderWithTheme(<AnimatedSensorGrid />);
      
      // Wait for animation to complete
      await waitFor(() => {
        const frameCountElement = screen.getByTestId('frame-count');
        const frameCountValue = parseInt(frameCountElement.textContent || '0');
        return frameCountValue >= 30; // At least 30 frames
      }, { timeout: 2000 });
      
      // Should maintain reasonable frame rate
      expect(frameCount).toBeGreaterThan(30);
      expect(frameCount).toBeLessThan(100); // Not excessive
    });
  });

  describe('Search and Filtering Performance', () => {
    it('filters large sensor datasets efficiently', async () => {
      const sensorCount = 1000;
      const largeSensorDataset = createMockSensorDataset(sensorCount);
      
      const FilterableSensorList = () => {
        const [searchTerm, setSearchTerm] = useState('');
        
        const filteredSensors = useMemo(() => 
          largeSensorDataset.filter(sensor =>
            sensor.sensor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            sensor.sensor.location.toLowerCase().includes(searchTerm.toLowerCase())
          ),
          [searchTerm]
        );
        
        return (
          <div>
            <input 
              data-testid="search-input"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search sensors..."
            />
            <div data-testid="filtered-count">{filteredSensors.length}</div>
            <div data-testid="filtered-sensors">
              {filteredSensors.slice(0, 50).map(sensor => (
                <div key={sensor.sensor.id} data-testid={`filtered-sensor-${sensor.sensor.id}`}>
                  {sensor.sensor.name}
                </div>
              ))}
            </div>
          </div>
        );
      };
      
      const user = userEvent.setup();
      renderWithTheme(<FilterableSensorList />);
      
      // Initial state - all sensors
      expect(screen.getByTestId('filtered-count')).toHaveTextContent(`${sensorCount}`);
      
      const searchInput = screen.getByTestId('search-input');
      
      // Measure search performance
      const startTime = performance.now();
      
      await user.type(searchInput, 'Sensor 1');
      
      await waitFor(() => {
        const filteredCount = parseInt(
          screen.getByTestId('filtered-count').textContent || '0'
        );
        expect(filteredCount).toBeGreaterThan(0);
        expect(filteredCount).toBeLessThan(sensorCount);
      });
      
      const endTime = performance.now();
      const searchTime = endTime - startTime;
      
      // Search should be fast even with 1000 sensors
      expect(searchTime).toBeLessThan(500);
    });

    it('sorts large datasets efficiently', async () => {
      const sensorCount = 500;
      const largeSensorDataset = createMockSensorDataset(sensorCount);
      
      const SortableSensorList = () => {
        const [sortBy, setSortBy] = useState<'name' | 'temperature' | 'location'>('name');
        const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
        
        const sortedSensors = useMemo(() => {
          const sorted = [...largeSensorDataset].sort((a, b) => {
            let aVal: string | number;
            let bVal: string | number;
            
            switch (sortBy) {
              case 'name':
                aVal = a.sensor.name;
                bVal = b.sensor.name;
                break;
              case 'temperature':
                aVal = a.latest_reading?.temperature || 0;
                bVal = b.latest_reading?.temperature || 0;
                break;
              case 'location':
                aVal = a.sensor.location;
                bVal = b.sensor.location;
                break;
              default:
                aVal = a.sensor.name;
                bVal = b.sensor.name;
            }
            
            if (typeof aVal === 'string' && typeof bVal === 'string') {
              return sortOrder === 'asc' 
                ? aVal.localeCompare(bVal)
                : bVal.localeCompare(aVal);
            }
            
            return sortOrder === 'asc' 
              ? (aVal as number) - (bVal as number)
              : (bVal as number) - (aVal as number);
          });
          
          return sorted;
        }, [sortBy, sortOrder]);
        
        return (
          <div>
            <button 
              data-testid="sort-by-temperature"
              onClick={() => setSortBy('temperature')}
            >
              Sort by Temperature
            </button>
            <button 
              data-testid="toggle-sort-order"
              onClick={() => setSortOrder(order => order === 'asc' ? 'desc' : 'asc')}
            >
              {sortOrder}
            </button>
            <div data-testid="sorted-count">{sortedSensors.length}</div>
            <div data-testid="first-sensor">
              {sortedSensors[0]?.sensor.name}
            </div>
          </div>
        );
      };
      
      const user = userEvent.setup();
      renderWithTheme(<SortableSensorList />);
      
      expect(screen.getByTestId('sorted-count')).toHaveTextContent(`${sensorCount}`);
      
      // Measure sort performance
      const startTime = performance.now();
      
      await user.click(screen.getByTestId('sort-by-temperature'));
      
      await waitFor(() => {
        expect(screen.getByTestId('first-sensor')).toHaveTextContent(/Sensor/);
      });
      
      const endTime = performance.now();
      const sortTime = endTime - startTime;
      
      // Sort should complete quickly
      expect(sortTime).toBeLessThan(200);
    });
  });

  describe('Component Lifecycle Performance', () => {
    it('measures mount/unmount performance', async () => {
      const sensorCount = 200;
      
      // Measure mount time
      const mountStart = performance.now();
      const { unmount } = renderWithTheme(
        <PerformanceTestDashboard sensorCount={sensorCount} />
      );
      
      await waitFor(() => {
        expect(screen.getByText(`${sensorCount}`)).toBeInTheDocument();
      });
      
      const mountEnd = performance.now();
      const mountTime = mountEnd - mountStart;
      
      // Measure unmount time
      const unmountStart = performance.now();
      unmount();
      const unmountEnd = performance.now();
      const unmountTime = unmountEnd - unmountStart;
      
      // Both mount and unmount should be efficient
      expect(mountTime).toBeLessThan(500);
      expect(unmountTime).toBeLessThan(100);
    });

    it('validates lazy loading performance', async () => {
      const LazyLoadingComponent = React.lazy(() => 
        new Promise(resolve => {
          setTimeout(() => {
            resolve({
              default: () => (
                <div data-testid="lazy-loaded-component">
                  Lazy Loaded Sensor Dashboard
                </div>
              )
            } as any);
          }, 10); // Simulate small loading delay
        })
      );
      
      const LazyWrapper = () => (
        <React.Suspense fallback={<div data-testid="loading">Loading...</div>}>
          <LazyLoadingComponent />
        </React.Suspense>
      );
      
      const startTime = performance.now();
      renderWithTheme(<LazyWrapper />);
      
      expect(screen.getByTestId('loading')).toBeInTheDocument();
      
      await waitFor(() => {
        expect(screen.getByTestId('lazy-loaded-component')).toBeInTheDocument();
      });
      
      const endTime = performance.now();
      const lazyLoadTime = endTime - startTime;
      
      // Lazy loading should be relatively quick
      expect(lazyLoadTime).toBeLessThan(100);
    });
  });

  describe('Bundle Size Impact', () => {
    it('validates code splitting effectiveness', () => {
      // This test would typically run in a build environment
      // Here we simulate the concept
      
      const componentSize = JSON.stringify(<SensorDashboard />).length;
      const mockBundleSize = 150000; // 150KB mock bundle size
      
      // Component should not significantly impact bundle size
      expect(componentSize).toBeLessThan(5000); // Less than 5KB serialized
      expect(mockBundleSize).toBeLessThan(500000); // Bundle under 500KB
    });

    it('measures render size impact', () => {
      const sensorCount = 100;
      const { container } = renderWithTheme(
        <PerformanceTestDashboard sensorCount={sensorCount} />
      );
      
      const domNodeCount = container.querySelectorAll('*').length;
      const averageNodesPerSensor = domNodeCount / sensorCount;
      
      // Should maintain reasonable DOM size per sensor
      expect(averageNodesPerSensor).toBeLessThan(20);
      expect(domNodeCount).toBeLessThan(2000); // Total DOM nodes
    });
  });
});