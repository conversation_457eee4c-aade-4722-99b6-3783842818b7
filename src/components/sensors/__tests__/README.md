# TempStick Sensor Dashboard Testing Suite

This directory contains a comprehensive testing suite for the TempStick sensor dashboard components, providing robust validation of temperature monitoring functionality, dark mode theme integration, HACCP compliance workflows, and real-time data processing.

## Test Suite Overview

### 📁 Test Structure

```
__tests__/
├── utils/
│   └── test-utils.tsx           # Testing utilities and theme helpers
├── mocks/
│   └── supabase-mocks.ts        # Supabase client mocking
├── integration/
│   └── RealTimeSubscription.test.tsx    # Real-time data integration tests
├── performance/
│   └── LargeDatasetPerformance.test.tsx # Performance and scalability tests
├── compliance/
│   └── HACCPWorkflow.test.tsx           # HACCP compliance workflow tests
├── errorHandling/
│   └── OfflineScenarios.test.tsx       # Error handling and offline tests
├── SensorDashboard.test.tsx     # Main dashboard component tests
├── SensorTile.test.tsx          # Individual sensor tile tests
└── ExportControls.test.tsx      # PDF generation and export tests
```

## 🧪 Testing Categories

### Unit Tests
- **SensorDashboard.test.tsx**: Comprehensive tests for the main dashboard component
- **SensorTile.test.tsx**: Individual sensor status tiles with theme variations
- **ExportControls.test.tsx**: PDF report generation and data export functionality

### Integration Tests
- **RealTimeSubscription.test.tsx**: Supabase real-time subscription lifecycle and data synchronization

### Performance Tests
- **LargeDatasetPerformance.test.tsx**: Scalability testing with 100+, 500+, and 1000+ sensors

### Compliance Tests
- **HACCPWorkflow.test.tsx**: HACCP compliance workflows, critical control points, and audit trails

### Error Handling Tests
- **OfflineScenarios.test.tsx**: Network failures, offline functionality, and graceful degradation

## 🎨 Theme Testing

All components are tested with both light and dark themes to ensure:
- Proper CSS class application
- Color contrast compliance
- Visual consistency across theme changes
- Theme-aware component styling using `useThemeAwareStyles` hook

### Theme Test Pattern
```typescript
// Light theme test
renderWithTheme(<Component />, { theme: 'light' });
expect(document.documentElement).toHaveClass('light');

// Dark theme test
renderWithTheme(<Component />, { theme: 'dark' });
expect(document.documentElement).toHaveClass('dark');

// Dynamic theme switching
const { rerenderWithTheme } = renderWithTheme(<Component />, { theme: 'light' });
rerenderWithTheme('dark');
```

## 📊 Test Coverage Goals

### Target Coverage Levels
- **Critical Components**: 95%+ (SensorDashboard, SensorTile)
- **Business Logic**: 90%+ (HACCP workflows, real-time processing)
- **UI Components**: 85%+ (ExportControls, theme integration)
- **Error Handling**: 90%+ (offline scenarios, API failures)

### Coverage Categories
- **Lines**: 85%+ overall
- **Branches**: 80%+ overall
- **Functions**: 80%+ overall
- **Statements**: 85%+ overall

## 🚀 Running Tests

### All Tests
```bash
npm test
```

### Specific Test Categories
```bash
# Unit tests only
npm test -- __tests__/Sensor*.test.tsx

# Integration tests
npm test -- __tests__/integration/

# Performance tests
npm test -- __tests__/performance/

# Compliance tests
npm test -- __tests__/compliance/

# Error handling tests
npm test -- __tests__/errorHandling/
```

### Watch Mode
```bash
npm test -- --watch
```

### Coverage Report
```bash
npm test -- --coverage
```

## 🛠️ Test Utilities

### Mock Data Factories
```typescript
// Create mock sensor data
const mockSensorData = createMockSensorDataset(100);

// Create specific sensor status
const mockStatus = createMockSensorStatus({
  sensor: { name: 'Custom Sensor' },
  latest_reading: { temperature: -20.0 }
});
```

### Theme Testing
```typescript
// Render with theme support
renderWithTheme(<Component />, { theme: 'dark' });

// Validate theme styles
expectThemeStyles(element, 'dark');

// Test theme transitions
const { rerenderWithTheme } = renderWithTheme(<Component />);
rerenderWithTheme('dark');
```

### Performance Testing
```typescript
// Measure render time
const renderTime = await measureRenderTime(() => {
  renderWithTheme(<SensorDashboard />);
});

// Expect reasonable performance
expect(renderTime).toBeLessThan(100); // Under 100ms
```

## 🔗 Mock Services

### Supabase Client Mocking
```typescript
const mockClient = createMockSupabaseClient();

// Set mock data
mockClient.__setMockData('sensors', mockSensorData);

// Set mock error
mockClient.__setMockError('sensors', new Error('Network error'));

// Trigger real-time events
mockClient.__triggerRealtimeEvent('sensors', 'INSERT', newSensor);
```

### Mock Scenarios
- **Normal Operation**: Standard sensor data with no errors
- **Temperature Alerts**: High/low temperature violations
- **Offline Sensors**: Disconnected sensors and battery warnings
- **Network Errors**: API failures and timeout scenarios
- **Large Datasets**: 100+ sensors for performance testing

## 🏗️ Test Architecture

### Testing Principles
- **Isolation**: Each test is independent and can run in any order
- **Deterministic**: Tests produce consistent results across runs
- **Fast Feedback**: Unit tests complete in under 5 seconds
- **Realistic**: Tests reflect actual user scenarios and edge cases

### Mock Strategy
- **External Dependencies**: All API calls and Supabase operations are mocked
- **UI Components**: Child components are mocked to isolate testing focus
- **Time-Based Operations**: Use fake timers for predictable time-based testing
- **Random Data**: Seed random data generation for consistent test results

## 📈 Performance Benchmarks

### Rendering Performance
- **100 Sensors**: < 200ms initial render
- **500 Sensors**: < 1000ms initial render
- **1000 Sensors**: Virtual scrolling required, < 100ms for visible items

### Memory Usage
- **100 Sensors**: < 50MB memory increase
- **Memory Leaks**: < 10MB after component unmount
- **Update Performance**: < 50ms for bulk sensor updates

### Real-time Performance
- **Update Frequency**: Handle 1 update/second per sensor
- **Subscription Management**: Proper cleanup prevents memory leaks
- **Batch Updates**: Efficient handling of simultaneous sensor updates

## 🔧 Configuration

### Vitest Configuration
The test suite uses Vitest with these optimizations:
- **Happy DOM**: Lightweight DOM environment
- **Coverage**: v8 provider with detailed reporting
- **Parallel Execution**: 2-4 worker threads for faster test execution
- **Setup Files**: Global test setup and mocking configuration

### Environment Variables
Tests use these environment variables:
```bash
NODE_ENV=test
VITE_SUPABASE_URL=mock://supabase
VITE_SUPABASE_ANON_KEY=mock_key
```

## 🐛 Debugging Tests

### Common Issues
1. **Theme Not Applied**: Ensure `renderWithTheme` is used instead of regular `render`
2. **Mock Not Working**: Check mock is setup in `beforeEach` and cleared in `afterEach`
3. **Async Operations**: Use `waitFor` for async state changes
4. **Timer Issues**: Use `vi.useFakeTimers()` and `vi.advanceTimersByTime()` for time-based tests

### Debug Commands
```bash
# Run specific test file
npm test -- SensorDashboard.test.tsx

# Run test with verbose output
npm test -- --reporter=verbose

# Debug test in browser
npm test -- --ui
```

## 📋 Test Checklist

### Before Adding New Tests
- [ ] Mock external dependencies
- [ ] Test both light and dark themes
- [ ] Include error scenarios
- [ ] Test accessibility requirements
- [ ] Add performance assertions for large datasets
- [ ] Document complex test scenarios

### Code Review Checklist
- [ ] Tests cover both happy path and edge cases
- [ ] Proper cleanup in `afterEach` hooks
- [ ] Meaningful test descriptions
- [ ] No hardcoded timeouts or delays
- [ ] Mock data reflects realistic scenarios
- [ ] Performance tests have reasonable thresholds

## 🎯 Quality Standards

### Test Quality Metrics
- **Test Maintainability**: Clear, readable test descriptions
- **Test Reliability**: Consistent results across environments  
- **Test Coverage**: Comprehensive validation of all code paths
- **Test Performance**: Fast execution for rapid feedback

### Acceptance Criteria
All tests must:
1. Pass consistently in CI/CD environment
2. Complete within reasonable time limits
3. Provide clear failure messages
4. Cover both success and failure scenarios
5. Validate theme integration properly
6. Include accessibility checks where applicable

---

This testing suite ensures the TempStick sensor dashboard components are robust, performant, and provide excellent user experience across all supported themes and use cases.