/**
 * HACCP Compliance Workflow Tests
 * 
 * Comprehensive tests for HACCP (Hazard Analysis Critical Control Points) compliance features including:
 * - Critical Control Point (CCP) monitoring
 * - Temperature threshold violation detection
 * - Corrective action workflow automation
 * - Compliance reporting and documentation
 * - Audit trail generation and verification
 * - Regulatory requirement validation
 * - Alert escalation procedures
 * - Documentation requirements compliance
 */

import React, { useState, useEffect } from 'react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithTheme, createMockSensorDataset, waitForNextTick } from '../../../../__tests__/utils/test-utils';

// Mock HACCP compliance types
interface HACCPViolation {
  id: string;
  sensor_id: string;
  violation_type: 'temperature_high' | 'temperature_low' | 'sensor_offline' | 'battery_low';
  severity: 'minor' | 'major' | 'critical';
  detected_at: string;
  resolved_at?: string;
  corrective_action?: string;
  reviewed_by?: string;
  is_resolved: boolean;
  ccp_id?: string;
}

interface HACCPAuditRecord {
  id: string;
  sensor_id: string;
  audit_date: string;
  audit_type: 'routine' | 'incident' | 'regulatory';
  findings: string[];
  compliance_status: 'compliant' | 'non_compliant' | 'under_review';
  auditor_name: string;
  next_audit_date: string;
}

interface CriticalControlPoint {
  id: string;
  name: string;
  description: string;
  sensor_ids: string[];
  temperature_min: number;
  temperature_max: number;
  monitoring_frequency: number; // minutes
  corrective_actions: string[];
  regulatory_requirement: string;
  is_active: boolean;
}

// Mock hooks for HACCP compliance
const mockUseHACCPCompliance = vi.fn();
const mockUseCriticalControlPoints = vi.fn();
const mockUseComplianceAlerts = vi.fn();
const mockUseAuditTrail = vi.fn();

vi.mock('../../../../hooks/useHACCPCompliance', () => ({
  useHACCPCompliance: mockUseHACCPCompliance,
  useCriticalControlPoints: mockUseCriticalControlPoints,
  useComplianceAlerts: mockUseComplianceAlerts,
  useAuditTrail: mockUseAuditTrail,
}));

// HACCP Compliance Dashboard Component
const HACCPComplianceDashboard = () => {
  const { violations, complianceStatus, generateReport } = mockUseHACCPCompliance();
  const { ccps, updateCCP } = mockUseCriticalControlPoints();
  const { alerts, resolveAlert } = mockUseComplianceAlerts();
  const { auditRecords, createAuditRecord } = mockUseAuditTrail();

  return (
    <div data-testid="haccp-compliance-dashboard">
      <div data-testid="compliance-status">{complianceStatus}</div>
      <div data-testid="violation-count">{violations.length}</div>
      <div data-testid="ccp-count">{ccps.length}</div>
      <div data-testid="alert-count">{alerts.length}</div>
      <div data-testid="audit-record-count">{auditRecords.length}</div>
      
      <div data-testid="violations">
        {violations.map((violation: HACCPViolation) => (
          <div key={violation.id} data-testid={`violation-${violation.id}`}>
            <span data-testid="violation-type">{violation.violation_type}</span>
            <span data-testid="violation-severity">{violation.severity}</span>
            <span data-testid="violation-status">{violation.is_resolved ? 'resolved' : 'active'}</span>
          </div>
        ))}
      </div>

      <div data-testid="ccps">
        {ccps.map((ccp: CriticalControlPoint) => (
          <div key={ccp.id} data-testid={`ccp-${ccp.id}`}>
            <span data-testid="ccp-name">{ccp.name}</span>
            <span data-testid="ccp-status">{ccp.is_active ? 'active' : 'inactive'}</span>
            <span data-testid="ccp-temp-range">{ccp.temperature_min}°F - {ccp.temperature_max}°F</span>
          </div>
        ))}
      </div>

      <button 
        data-testid="generate-report-btn"
        onClick={() => generateReport()}
      >
        Generate Compliance Report
      </button>

      <button
        data-testid="create-audit-btn"
        onClick={() => createAuditRecord({
          audit_type: 'routine',
          findings: [],
          compliance_status: 'compliant',
          auditor_name: 'Test Auditor',
        })}
      >
        Create Audit Record
      </button>
    </div>
  );
};

// Corrective Action Workflow Component
const CorrectiveActionWorkflow = ({ violationId }: { violationId: string }) => {
  const [actionTaken, setActionTaken] = useState('');
  const [reviewerName, setReviewerName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmitAction = async () => {
    setIsSubmitting(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 100));
    setIsSubmitting(false);
  };

  return (
    <div data-testid="corrective-action-workflow">
      <div data-testid="violation-id">{violationId}</div>
      
      <textarea
        data-testid="action-description"
        value={actionTaken}
        onChange={(e) => setActionTaken(e.target.value)}
        placeholder="Describe corrective action taken..."
      />
      
      <input
        data-testid="reviewer-name"
        value={reviewerName}
        onChange={(e) => setReviewerName(e.target.value)}
        placeholder="Reviewer name"
      />
      
      <button
        data-testid="submit-action-btn"
        onClick={handleSubmitAction}
        disabled={isSubmitting || !actionTaken || !reviewerName}
      >
        {isSubmitting ? 'Submitting...' : 'Submit Corrective Action'}
      </button>
    </div>
  );
};

// Real-time HACCP Monitoring Component
const RealTimeHACCPMonitor = () => {
  const [monitoringData, setMonitoringData] = useState<any[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);

  useEffect(() => {
    if (isMonitoring) {
      const interval = setInterval(() => {
        // Simulate real-time temperature data
        const newReading = {
          timestamp: new Date().toISOString(),
          sensor_id: 'sensor-001',
          temperature: -18.0 + (Math.random() - 0.5) * 4,
          within_limits: Math.random() > 0.1, // 90% within limits
        };
        
        setMonitoringData(prev => [...prev.slice(-9), newReading]);
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [isMonitoring]);

  return (
    <div data-testid="realtime-haccp-monitor">
      <button
        data-testid="toggle-monitoring-btn"
        onClick={() => setIsMonitoring(!isMonitoring)}
      >
        {isMonitoring ? 'Stop Monitoring' : 'Start Monitoring'}
      </button>
      
      <div data-testid="monitoring-status">{isMonitoring ? 'active' : 'inactive'}</div>
      <div data-testid="reading-count">{monitoringData.length}</div>
      
      <div data-testid="latest-readings">
        {monitoringData.map((reading, index) => (
          <div key={index} data-testid={`reading-${index}`}>
            <span data-testid="reading-temp">{reading.temperature.toFixed(1)}°F</span>
            <span data-testid="reading-status">{reading.within_limits ? 'compliant' : 'violation'}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

describe('HACCP Compliance Workflow Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Default mock implementations
    mockUseHACCPCompliance.mockReturnValue({
      violations: [],
      complianceStatus: 'compliant',
      generateReport: vi.fn().mockResolvedValue({ success: true }),
      updateViolation: vi.fn(),
    });

    mockUseCriticalControlPoints.mockReturnValue({
      ccps: [],
      updateCCP: vi.fn(),
      createCCP: vi.fn(),
      deleteCCP: vi.fn(),
    });

    mockUseComplianceAlerts.mockReturnValue({
      alerts: [],
      resolveAlert: vi.fn(),
      escalateAlert: vi.fn(),
    });

    mockUseAuditTrail.mockReturnValue({
      auditRecords: [],
      createAuditRecord: vi.fn().mockResolvedValue({ success: true }),
      updateAuditRecord: vi.fn(),
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
    vi.clearAllTimers();
  });

  describe('Critical Control Point Management', () => {
    it('displays critical control points correctly', () => {
      const mockCCPs: CriticalControlPoint[] = [
        {
          id: 'ccp-001',
          name: 'Freezer Storage CCP',
          description: 'Seafood freezer temperature control',
          sensor_ids: ['sensor-001', 'sensor-002'],
          temperature_min: -22.0,
          temperature_max: -18.0,
          monitoring_frequency: 15,
          corrective_actions: [
            'Check freezer unit functionality',
            'Verify door seals',
            'Contact maintenance'
          ],
          regulatory_requirement: 'FDA Seafood HACCP Regulation 123.6',
          is_active: true,
        },
        {
          id: 'ccp-002',
          name: 'Display Case CCP',
          description: 'Fresh seafood display temperature',
          sensor_ids: ['sensor-003'],
          temperature_min: 32.0,
          temperature_max: 38.0,
          monitoring_frequency: 30,
          corrective_actions: [
            'Adjust display case temperature',
            'Check refrigeration system'
          ],
          regulatory_requirement: 'FDA Food Code 3-501.16',
          is_active: true,
        }
      ];

      mockUseCriticalControlPoints.mockReturnValue({
        ccps: mockCCPs,
        updateCCP: vi.fn(),
        createCCP: vi.fn(),
        deleteCCP: vi.fn(),
      });

      renderWithTheme(<HACCPComplianceDashboard />);

      expect(screen.getByTestId('ccp-count')).toHaveTextContent('2');
      expect(screen.getByTestId('ccp-ccp-001')).toBeInTheDocument();
      expect(screen.getByTestId('ccp-ccp-002')).toBeInTheDocument();
      
      // Check CCP details
      const freezerCCP = screen.getByTestId('ccp-ccp-001');
      expect(freezerCCP.querySelector('[data-testid="ccp-name"]')).toHaveTextContent('Freezer Storage CCP');
      expect(freezerCCP.querySelector('[data-testid="ccp-status"]')).toHaveTextContent('active');
      expect(freezerCCP.querySelector('[data-testid="ccp-temp-range"]')).toHaveTextContent('-22°F - -18°F');
    });

    it('handles CCP updates and modifications', async () => {
      const user = userEvent.setup();
      const mockUpdateCCP = vi.fn();
      
      mockUseCriticalControlPoints.mockReturnValue({
        ccps: [{
          id: 'ccp-001',
          name: 'Test CCP',
          description: 'Test description',
          sensor_ids: ['sensor-001'],
          temperature_min: -20.0,
          temperature_max: -15.0,
          monitoring_frequency: 15,
          corrective_actions: [],
          regulatory_requirement: 'Test requirement',
          is_active: true,
        }],
        updateCCP: mockUpdateCCP,
        createCCP: vi.fn(),
        deleteCCP: vi.fn(),
      });

      renderWithTheme(<HACCPComplianceDashboard />);

      expect(screen.getByTestId('ccp-001')).toBeInTheDocument();
      // Actual CCP update functionality would be tested with more specific UI components
    });
  });

  describe('Violation Detection and Management', () => {
    it('detects and displays temperature violations', () => {
      const mockViolations: HACCPViolation[] = [
        {
          id: 'violation-001',
          sensor_id: 'sensor-001',
          violation_type: 'temperature_high',
          severity: 'critical',
          detected_at: '2024-01-01T12:00:00Z',
          is_resolved: false,
          ccp_id: 'ccp-001',
        },
        {
          id: 'violation-002',
          sensor_id: 'sensor-002',
          violation_type: 'sensor_offline',
          severity: 'major',
          detected_at: '2024-01-01T11:30:00Z',
          resolved_at: '2024-01-01T12:15:00Z',
          corrective_action: 'Sensor reconnected, readings normal',
          reviewed_by: 'John Smith',
          is_resolved: true,
        }
      ];

      mockUseHACCPCompliance.mockReturnValue({
        violations: mockViolations,
        complianceStatus: 'non_compliant',
        generateReport: vi.fn(),
        updateViolation: vi.fn(),
      });

      renderWithTheme(<HACCPComplianceDashboard />);

      expect(screen.getByTestId('violation-count')).toHaveTextContent('2');
      expect(screen.getByTestId('compliance-status')).toHaveTextContent('non_compliant');
      
      // Check active violation
      const activeViolation = screen.getByTestId('violation-violation-001');
      expect(activeViolation.querySelector('[data-testid="violation-type"]')).toHaveTextContent('temperature_high');
      expect(activeViolation.querySelector('[data-testid="violation-severity"]')).toHaveTextContent('critical');
      expect(activeViolation.querySelector('[data-testid="violation-status"]')).toHaveTextContent('active');
      
      // Check resolved violation
      const resolvedViolation = screen.getByTestId('violation-violation-002');
      expect(resolvedViolation.querySelector('[data-testid="violation-status"]')).toHaveTextContent('resolved');
    });

    it('calculates compliance status correctly', () => {
      // Test various compliance scenarios
      const scenarios = [
        { violations: [], expected: 'compliant' },
        { 
          violations: [{ severity: 'minor', is_resolved: true }], 
          expected: 'compliant' 
        },
        { 
          violations: [{ severity: 'critical', is_resolved: false }], 
          expected: 'non_compliant' 
        },
        { 
          violations: [
            { severity: 'minor', is_resolved: false },
            { severity: 'major', is_resolved: true }
          ], 
          expected: 'under_review' 
        }
      ];

      scenarios.forEach(({ violations, expected }) => {
        mockUseHACCPCompliance.mockReturnValue({
          violations,
          complianceStatus: expected,
          generateReport: vi.fn(),
          updateViolation: vi.fn(),
        });

        const { unmount } = renderWithTheme(<HACCPComplianceDashboard />);
        expect(screen.getByTestId('compliance-status')).toHaveTextContent(expected);
        unmount();
      });
    });

    it('handles violation severity escalation', async () => {
      const mockViolation: HACCPViolation = {
        id: 'violation-001',
        sensor_id: 'sensor-001',
        violation_type: 'temperature_high',
        severity: 'minor',
        detected_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
        is_resolved: false,
      };

      // Simulate escalation after time threshold
      const escalatedViolation: HACCPViolation = {
        ...mockViolation,
        severity: 'major', // Escalated from minor to major
      };

      mockUseHACCPCompliance.mockReturnValue({
        violations: [mockViolation],
        complianceStatus: 'under_review',
        generateReport: vi.fn(),
        updateViolation: vi.fn(),
      });

      const { rerender } = renderWithTheme(<HACCPComplianceDashboard />);

      expect(screen.getByTestId('violation-violation-001')
        .querySelector('[data-testid="violation-severity"]'))
        .toHaveTextContent('minor');

      // Simulate escalation
      mockUseHACCPCompliance.mockReturnValue({
        violations: [escalatedViolation],
        complianceStatus: 'non_compliant',
        generateReport: vi.fn(),
        updateViolation: vi.fn(),
      });

      rerender(<HACCPComplianceDashboard />);

      expect(screen.getByTestId('violation-violation-001')
        .querySelector('[data-testid="violation-severity"]'))
        .toHaveTextContent('major');
    });
  });

  describe('Corrective Action Workflow', () => {
    it('handles corrective action submission', async () => {
      const user = userEvent.setup();
      
      renderWithTheme(<CorrectiveActionWorkflow violationId="violation-001" />);

      expect(screen.getByTestId('violation-id')).toHaveTextContent('violation-001');
      
      const actionDescription = screen.getByTestId('action-description');
      const reviewerName = screen.getByTestId('reviewer-name');
      const submitButton = screen.getByTestId('submit-action-btn');
      
      // Initially button should be disabled
      expect(submitButton).toBeDisabled();
      
      // Fill in required fields
      await user.type(actionDescription, 'Adjusted freezer temperature setting to -20°F. Verified proper operation.');
      await user.type(reviewerName, 'Jane Smith, QA Manager');
      
      // Button should now be enabled
      expect(submitButton).not.toBeDisabled();
      
      // Submit corrective action
      await user.click(submitButton);
      
      expect(screen.getByText('Submitting...')).toBeInTheDocument();
      
      await waitFor(() => {
        expect(screen.queryByText('Submitting...')).not.toBeInTheDocument();
      });
    });

    it('validates corrective action requirements', async () => {
      const user = userEvent.setup();
      
      renderWithTheme(<CorrectiveActionWorkflow violationId="violation-001" />);

      const submitButton = screen.getByTestId('submit-action-btn');
      
      // Button should be disabled without required fields
      expect(submitButton).toBeDisabled();
      
      // Add action description only
      const actionDescription = screen.getByTestId('action-description');
      await user.type(actionDescription, 'Test action');
      
      expect(submitButton).toBeDisabled(); // Still disabled without reviewer
      
      // Add reviewer name
      const reviewerName = screen.getByTestId('reviewer-name');
      await user.type(reviewerName, 'Test Reviewer');
      
      expect(submitButton).not.toBeDisabled(); // Now enabled
    });
  });

  describe('Real-Time Monitoring', () => {
    it('monitors temperature readings in real-time', async () => {
      vi.useFakeTimers();
      
      renderWithTheme(<RealTimeHACCPMonitor />);

      expect(screen.getByTestId('monitoring-status')).toHaveTextContent('inactive');
      expect(screen.getByTestId('reading-count')).toHaveTextContent('0');

      // Start monitoring
      const toggleButton = screen.getByTestId('toggle-monitoring-btn');
      fireEvent.click(toggleButton);

      expect(screen.getByTestId('monitoring-status')).toHaveTextContent('active');
      expect(screen.getByText('Stop Monitoring')).toBeInTheDocument();

      // Advance timer to generate readings
      vi.advanceTimersByTime(3000); // 3 seconds = 3 readings

      await waitFor(() => {
        expect(screen.getByTestId('reading-count')).toHaveTextContent('3');
      });

      // Check that readings are displayed
      expect(screen.getByTestId('reading-0')).toBeInTheDocument();
      expect(screen.getByTestId('reading-1')).toBeInTheDocument();
      expect(screen.getByTestId('reading-2')).toBeInTheDocument();

      // Stop monitoring
      fireEvent.click(toggleButton);
      expect(screen.getByTestId('monitoring-status')).toHaveTextContent('inactive');

      vi.useRealTimers();
    });

    it('detects compliance violations in real-time', async () => {
      vi.useFakeTimers();
      
      // Mock violation detection
      const mockMath = Object.create(global.Math);
      mockMath.random = () => 0.05; // Force violation (< 0.1 threshold)
      global.Math = mockMath;
      
      renderWithTheme(<RealTimeHACCPMonitor />);

      // Start monitoring
      fireEvent.click(screen.getByTestId('toggle-monitoring-btn'));
      
      // Generate readings that will trigger violations
      vi.advanceTimersByTime(2000);

      await waitFor(() => {
        const readings = screen.getAllByTestId(/^reading-\d+$/);
        expect(readings.length).toBeGreaterThan(0);
        
        // Check for violation status
        const violationReadings = screen.getAllByText('violation');
        expect(violationReadings.length).toBeGreaterThan(0);
      });

      // Restore Math object
      global.Math = Math;
      vi.useRealTimers();
    });

    it('maintains reading history within limits', async () => {
      vi.useFakeTimers();
      
      renderWithTheme(<RealTimeHACCPMonitor />);

      fireEvent.click(screen.getByTestId('toggle-monitoring-btn'));
      
      // Generate many readings
      vi.advanceTimersByTime(15000); // 15 seconds = 15 readings

      await waitFor(() => {
        const readingCount = parseInt(screen.getByTestId('reading-count').textContent || '0');
        // Should maintain only last 10 readings
        expect(readingCount).toBeLessThanOrEqual(10);
      });

      vi.useRealTimers();
    });
  });

  describe('Compliance Reporting', () => {
    it('generates compliance reports', async () => {
      const user = userEvent.setup();
      const mockGenerateReport = vi.fn().mockResolvedValue({
        success: true,
        reportId: 'report-001',
        filename: 'haccp-compliance-report.pdf'
      });

      mockUseHACCPCompliance.mockReturnValue({
        violations: [],
        complianceStatus: 'compliant',
        generateReport: mockGenerateReport,
        updateViolation: vi.fn(),
      });

      renderWithTheme(<HACCPComplianceDashboard />);

      const generateButton = screen.getByTestId('generate-report-btn');
      await user.click(generateButton);

      expect(mockGenerateReport).toHaveBeenCalledTimes(1);
    });

    it('includes all required compliance data in reports', async () => {
      const user = userEvent.setup();
      const mockGenerateReport = vi.fn();

      const mockViolations: HACCPViolation[] = [
        {
          id: 'violation-001',
          sensor_id: 'sensor-001',
          violation_type: 'temperature_high',
          severity: 'critical',
          detected_at: '2024-01-01T12:00:00Z',
          resolved_at: '2024-01-01T12:30:00Z',
          corrective_action: 'Temperature adjusted, system normal',
          reviewed_by: 'John Smith',
          is_resolved: true,
          ccp_id: 'ccp-001',
        }
      ];

      mockUseHACCPCompliance.mockReturnValue({
        violations: mockViolations,
        complianceStatus: 'compliant',
        generateReport: mockGenerateReport,
        updateViolation: vi.fn(),
      });

      renderWithTheme(<HACCPComplianceDashboard />);

      const generateButton = screen.getByTestId('generate-report-btn');
      await user.click(generateButton);

      expect(mockGenerateReport).toHaveBeenCalledWith(
        expect.objectContaining({
          includeViolations: true,
          includeCCPs: true,
          includeCorrectiveActions: true,
          includeAuditTrail: true,
        })
      );
    });
  });

  describe('Audit Trail Management', () => {
    it('creates audit records', async () => {
      const user = userEvent.setup();
      const mockCreateAuditRecord = vi.fn().mockResolvedValue({
        success: true,
        auditId: 'audit-001'
      });

      mockUseAuditTrail.mockReturnValue({
        auditRecords: [],
        createAuditRecord: mockCreateAuditRecord,
        updateAuditRecord: vi.fn(),
      });

      renderWithTheme(<HACCPComplianceDashboard />);

      const createAuditButton = screen.getByTestId('create-audit-btn');
      await user.click(createAuditButton);

      expect(mockCreateAuditRecord).toHaveBeenCalledWith({
        audit_type: 'routine',
        findings: [],
        compliance_status: 'compliant',
        auditor_name: 'Test Auditor',
      });
    });

    it('maintains chronological audit trail', () => {
      const mockAuditRecords: HACCPAuditRecord[] = [
        {
          id: 'audit-001',
          sensor_id: 'sensor-001',
          audit_date: '2024-01-01T09:00:00Z',
          audit_type: 'routine',
          findings: ['Temperature within limits', 'Sensor functioning normally'],
          compliance_status: 'compliant',
          auditor_name: 'Jane Smith',
          next_audit_date: '2024-01-02T09:00:00Z',
        },
        {
          id: 'audit-002',
          sensor_id: 'sensor-001',
          audit_date: '2024-01-01T15:00:00Z',
          audit_type: 'incident',
          findings: ['Temperature violation detected', 'Corrective action implemented'],
          compliance_status: 'non_compliant',
          auditor_name: 'John Doe',
          next_audit_date: '2024-01-01T21:00:00Z',
        }
      ];

      mockUseAuditTrail.mockReturnValue({
        auditRecords: mockAuditRecords,
        createAuditRecord: vi.fn(),
        updateAuditRecord: vi.fn(),
      });

      renderWithTheme(<HACCPComplianceDashboard />);

      expect(screen.getByTestId('audit-record-count')).toHaveTextContent('2');
    });

    it('validates regulatory compliance requirements', () => {
      const mockCCPs: CriticalControlPoint[] = [
        {
          id: 'ccp-001',
          name: 'Freezer Storage CCP',
          description: 'Seafood freezer temperature control',
          sensor_ids: ['sensor-001'],
          temperature_min: -22.0,
          temperature_max: -18.0,
          monitoring_frequency: 15, // Every 15 minutes
          corrective_actions: [
            'Check freezer unit functionality',
            'Verify door seals',
            'Contact maintenance'
          ],
          regulatory_requirement: 'FDA Seafood HACCP Regulation 123.6(c)(2)',
          is_active: true,
        }
      ];

      mockUseCriticalControlPoints.mockReturnValue({
        ccps: mockCCPs,
        updateCCP: vi.fn(),
        createCCP: vi.fn(),
        deleteCCP: vi.fn(),
      });

      renderWithTheme(<HACCPComplianceDashboard />);

      const ccpElement = screen.getByTestId('ccp-ccp-001');
      expect(ccpElement).toBeInTheDocument();
      
      // CCP should have regulatory requirement (tested indirectly through mock data)
      expect(mockCCPs[0].regulatory_requirement).toContain('FDA');
      expect(mockCCPs[0].monitoring_frequency).toBe(15);
      expect(mockCCPs[0].corrective_actions.length).toBeGreaterThan(0);
    });
  });

  describe('Alert Escalation Procedures', () => {
    it('escalates unresolved critical violations', async () => {
      const criticalViolation: HACCPViolation = {
        id: 'violation-001',
        sensor_id: 'sensor-001',
        violation_type: 'temperature_high',
        severity: 'critical',
        detected_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
        is_resolved: false,
        ccp_id: 'ccp-001',
      };

      mockUseHACCPCompliance.mockReturnValue({
        violations: [criticalViolation],
        complianceStatus: 'non_compliant',
        generateReport: vi.fn(),
        updateViolation: vi.fn(),
      });

      const mockEscalateAlert = vi.fn();
      mockUseComplianceAlerts.mockReturnValue({
        alerts: [{
          id: 'alert-001',
          violation_id: 'violation-001',
          escalation_level: 'management',
          escalated_at: new Date().toISOString(),
        }],
        resolveAlert: vi.fn(),
        escalateAlert: mockEscalateAlert,
      });

      renderWithTheme(<HACCPComplianceDashboard />);

      expect(screen.getByTestId('alert-count')).toHaveTextContent('1');
      
      // In a real implementation, escalation would be automatic based on time thresholds
      expect(criticalViolation.severity).toBe('critical');
      expect(Date.now() - new Date(criticalViolation.detected_at).getTime()).toBeGreaterThan(3 * 60 * 60 * 1000); // More than 3 hours
    });
  });

  describe('Documentation Requirements', () => {
    it('ensures all violations have required documentation', () => {
      const properlyDocumentedViolation: HACCPViolation = {
        id: 'violation-001',
        sensor_id: 'sensor-001',
        violation_type: 'temperature_high',
        severity: 'critical',
        detected_at: '2024-01-01T12:00:00Z',
        resolved_at: '2024-01-01T12:30:00Z',
        corrective_action: 'Adjusted freezer thermostat from -16°F to -20°F. Verified temperature stabilized within 30 minutes.',
        reviewed_by: 'John Smith, Quality Assurance Manager',
        is_resolved: true,
        ccp_id: 'ccp-001',
      };

      // Validate required fields are present
      expect(properlyDocumentedViolation.detected_at).toBeTruthy();
      expect(properlyDocumentedViolation.corrective_action).toBeTruthy();
      expect(properlyDocumentedViolation.reviewed_by).toBeTruthy();
      expect(properlyDocumentedViolation.resolved_at).toBeTruthy();
      expect(properlyDocumentedViolation.ccp_id).toBeTruthy();

      // Validate documentation quality
      expect(properlyDocumentedViolation.corrective_action!.length).toBeGreaterThan(20);
      expect(properlyDocumentedViolation.reviewed_by!).toContain(','); // Name and title
    });

    it('validates CCP documentation completeness', () => {
      const completelyDocumentedCCP: CriticalControlPoint = {
        id: 'ccp-001',
        name: 'Freezer Storage Critical Control Point',
        description: 'Monitoring temperature of frozen seafood products to prevent spoilage and ensure food safety compliance',
        sensor_ids: ['sensor-001', 'sensor-002'],
        temperature_min: -22.0,
        temperature_max: -18.0,
        monitoring_frequency: 15,
        corrective_actions: [
          'Immediately adjust freezer thermostat if temperature exceeds limits',
          'Verify freezer door seals and gaskets for proper closure',
          'Contact maintenance for equipment inspection if issue persists',
          'Document all actions taken and temperature readings',
          'Notify quality assurance manager of any extended violations'
        ],
        regulatory_requirement: 'FDA Food Code 3-501.17 and Seafood HACCP Regulation 21 CFR 123.6',
        is_active: true,
      };

      // Validate all required documentation fields
      expect(completelyDocumentedCCP.name).toBeTruthy();
      expect(completelyDocumentedCCP.description).toBeTruthy();
      expect(completelyDocumentedCCP.regulatory_requirement).toBeTruthy();
      expect(completelyDocumentedCCP.corrective_actions.length).toBeGreaterThan(3);
      expect(completelyDocumentedCCP.monitoring_frequency).toBeGreaterThan(0);
      expect(completelyDocumentedCCP.temperature_min).toBeLessThan(completelyDocumentedCCP.temperature_max);
      
      // Validate regulatory reference format
      expect(completelyDocumentedCCP.regulatory_requirement).toMatch(/FDA|CFR|Food Code/);
    });
  });
});