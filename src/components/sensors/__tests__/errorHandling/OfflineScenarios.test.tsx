/**
 * Error Handling and Offline Scenario Tests
 * 
 * Comprehensive tests for error handling, offline functionality, and resilience including:
 * - Network connectivity loss and recovery
 * - API service failures and timeout handling
 * - Sensor connectivity issues and offline detection
 * - Data synchronization when coming back online
 * - Error boundary behavior and graceful degradation
 * - User feedback and retry mechanisms
 * - Cache management during offline periods
 * - Background sync and conflict resolution
 */

import React, { useState, useEffect, createContext, useContext } from 'react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithTheme, createMockSensorDataset, simulateOfflineMode, waitForNextTick } from '../../../../__tests__/utils/test-utils';
import { SensorDashboard } from '../../SensorDashboard';

// Network status context for offline simulation
const NetworkContext = createContext({
  isOnline: true,
  isConnecting: false,
  lastOnline: null as Date | null,
  retry: () => Promise.resolve(),
});

const NetworkProvider = ({ children, initialOnline = true }: { children: React.ReactNode, initialOnline?: boolean }) => {
  const [isOnline, setIsOnline] = useState(initialOnline);
  const [isConnecting, setIsConnecting] = useState(false);
  const [lastOnline, setLastOnline] = useState<Date | null>(initialOnline ? new Date() : null);

  const retry = async () => {
    setIsConnecting(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    const shouldReconnect = Math.random() > 0.3; // 70% success rate
    setIsOnline(shouldReconnect);
    setIsConnecting(false);
    if (shouldReconnect) {
      setLastOnline(new Date());
    }
  };

  return (
    <NetworkContext.Provider value={{ isOnline, isConnecting, lastOnline, retry }}>
      {children}
    </NetworkContext.Provider>
  );
};

const useNetwork = () => useContext(NetworkContext);

// Error boundary for testing error scenarios
class TestErrorBoundary extends React.Component<
  { children: React.ReactNode; onError?: (error: Error) => void },
  { hasError: boolean; error: Error | null }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('Error boundary caught:', error, errorInfo);
    this.props.onError?.(error);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div data-testid="error-boundary">
          <h2>Something went wrong</h2>
          <p data-testid="error-message">{this.state.error?.message}</p>
          <button 
            data-testid="error-retry-btn"
            onClick={() => this.setState({ hasError: false, error: null })}
          >
            Try Again
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// Offline-aware sensor dashboard component
const OfflineAwareSensorDashboard = () => {
  const { isOnline, isConnecting, lastOnline, retry } = useNetwork();
  const [cachedData, setCachedData] = useState<any>(null);
  const [syncQueue, setSyncQueue] = useState<any[]>([]);
  
  return (
    <div data-testid="offline-aware-dashboard">
      {/* Network status indicator */}
      <div data-testid="network-status" className={`status-${isOnline ? 'online' : 'offline'}`}>
        {isConnecting ? 'Connecting...' : isOnline ? 'Online' : 'Offline'}
      </div>
      
      {/* Offline banner */}
      {!isOnline && (
        <div data-testid="offline-banner" className="offline-banner">
          <span>You're currently offline. Data shown may not be current.</span>
          {lastOnline && (
            <span data-testid="last-online">Last online: {lastOnline.toLocaleString()}</span>
          )}
          <button 
            data-testid="retry-connection-btn"
            onClick={retry}
            disabled={isConnecting}
          >
            {isConnecting ? 'Connecting...' : 'Retry Connection'}
          </button>
        </div>
      )}
      
      {/* Sync queue indicator */}
      {syncQueue.length > 0 && (
        <div data-testid="sync-queue">
          <span data-testid="sync-count">{syncQueue.length}</span> items pending sync
        </div>
      )}
      
      {/* Main dashboard */}
      <SensorDashboard />
    </div>
  );
};

// Component that simulates various error conditions
const ErrorSimulatorComponent = ({ errorType }: { errorType?: string }) => {
  const [shouldError, setShouldError] = useState(false);

  useEffect(() => {
    if (errorType === 'render-error') {
      setShouldError(true);
    }
  }, [errorType]);

  if (shouldError && errorType === 'render-error') {
    throw new Error('Simulated render error');
  }

  if (errorType === 'network-timeout') {
    throw new Error('Network request timed out');
  }

  if (errorType === 'api-error') {
    throw new Error('API returned 500 Internal Server Error');
  }

  return (
    <div data-testid="error-simulator">
      <button 
        data-testid="trigger-error-btn"
        onClick={() => setShouldError(true)}
      >
        Trigger Error
      </button>
      <div data-testid="error-type">{errorType || 'none'}</div>
    </div>
  );
};

// Mock hooks with error simulation capabilities
const mockUseSensorStatuses = vi.fn();
const mockUseTemperatureAlerts = vi.fn();
const mockUseTemperatureSync = vi.fn();

vi.mock('../../../../hooks/useTempStick', () => ({
  useSensorStatuses: mockUseSensorStatuses,
  useTemperatureAlerts: mockUseTemperatureAlerts,
  useTemperatureSync: mockUseTemperatureSync,
}));

describe('Error Handling and Offline Scenarios', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Reset navigator.onLine
    Object.defineProperty(navigator, 'onLine', {
      writable: true,
      value: true,
    });
    
    // Default successful mock implementations
    mockUseSensorStatuses.mockReturnValue({
      sensorStatuses: createMockSensorDataset(3),
      summary: {
        totalSensors: 3,
        onlineSensors: 3,
        activeAlerts: 0,
        criticalAlerts: 0,
        averageTemperature: -18.5,
        temperatureRange: null,
      },
      loading: false,
      error: null,
      refetch: vi.fn(),
    });

    mockUseTemperatureAlerts.mockReturnValue({
      alerts: [],
      unreadCount: 0,
      loading: false,
      error: null,
      dismissAlert: vi.fn(),
      resolveAlert: vi.fn(),
      refetch: vi.fn(),
    });

    mockUseTemperatureSync.mockReturnValue({
      sync: vi.fn().mockResolvedValue({ success: true }),
      syncing: false,
      lastSyncTime: new Date(),
      error: null,
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Network Connectivity Handling', () => {
    it('detects when going offline', () => {
      renderWithTheme(
        <NetworkProvider initialOnline={false}>
          <OfflineAwareSensorDashboard />
        </NetworkProvider>
      );

      expect(screen.getByTestId('network-status')).toHaveTextContent('Offline');
      expect(screen.getByTestId('offline-banner')).toBeInTheDocument();
      expect(screen.getByText("You're currently offline. Data shown may not be current.")).toBeInTheDocument();
    });

    it('shows online status when connected', () => {
      renderWithTheme(
        <NetworkProvider initialOnline={true}>
          <OfflineAwareSensorDashboard />
        </NetworkProvider>
      );

      expect(screen.getByTestId('network-status')).toHaveTextContent('Online');
      expect(screen.queryByTestId('offline-banner')).not.toBeInTheDocument();
    });

    it('handles network state transitions', async () => {
      const { rerender } = renderWithTheme(
        <NetworkProvider initialOnline={true}>
          <OfflineAwareSensorDashboard />
        </NetworkProvider>
      );

      expect(screen.getByTestId('network-status')).toHaveTextContent('Online');

      // Simulate going offline
      rerender(
        <NetworkProvider initialOnline={false}>
          <OfflineAwareSensorDashboard />
        </NetworkProvider>
      );

      expect(screen.getByTestId('network-status')).toHaveTextContent('Offline');
      expect(screen.getByTestId('offline-banner')).toBeInTheDocument();
    });

    it('shows retry connection functionality', async () => {
      const user = userEvent.setup();
      
      renderWithTheme(
        <NetworkProvider initialOnline={false}>
          <OfflineAwareSensorDashboard />
        </NetworkProvider>
      );

      const retryButton = screen.getByTestId('retry-connection-btn');
      expect(retryButton).toHaveTextContent('Retry Connection');

      await user.click(retryButton);

      // Should show connecting state
      await waitFor(() => {
        expect(screen.getByTestId('network-status')).toHaveTextContent('Connecting...');
      });

      // Button should be disabled during connection attempt
      expect(retryButton).toBeDisabled();
    });

    it('displays last online timestamp', () => {
      const lastOnlineTime = new Date('2024-01-01T12:00:00Z');
      
      renderWithTheme(
        <NetworkProvider initialOnline={false}>
          <div>
            <NetworkContext.Provider value={{
              isOnline: false,
              isConnecting: false,
              lastOnline: lastOnlineTime,
              retry: vi.fn(),
            }}>
              <OfflineAwareSensorDashboard />
            </NetworkContext.Provider>
          </div>
        </NetworkProvider>
      );

      expect(screen.getByTestId('last-online')).toHaveTextContent('Last online:');
    });
  });

  describe('API Service Failures', () => {
    it('handles sensor data loading errors', () => {
      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: [],
        summary: {
          totalSensors: 0,
          onlineSensors: 0,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: null,
          temperatureRange: null,
        },
        loading: false,
        error: 'Failed to load sensor data: Network timeout',
        refetch: vi.fn(),
      });

      renderWithTheme(<SensorDashboard />);

      // Should display empty state when data fails to load
      expect(screen.getByText(/no sensors found/i)).toBeInTheDocument();
    });

    it('handles API timeout errors', async () => {
      const timeoutError = new Error('Request timeout after 30 seconds');
      const mockRefetch = vi.fn().mockRejectedValue(timeoutError);

      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: [],
        summary: {
          totalSensors: 0,
          onlineSensors: 0,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: null,
          temperatureRange: null,
        },
        loading: false,
        error: timeoutError.message,
        refetch: mockRefetch,
      });

      renderWithTheme(<SensorDashboard />);

      // Should show empty state
      expect(screen.getByText(/no sensors found/i)).toBeInTheDocument();
    });

    it('handles sync operation failures', async () => {
      const user = userEvent.setup();
      const mockSync = vi.fn().mockRejectedValue(new Error('Sync failed: Unable to connect'));

      mockUseTemperatureSync.mockReturnValue({
        sync: mockSync,
        syncing: false,
        lastSyncTime: null,
        error: 'Sync failed: Unable to connect',
      });

      renderWithTheme(<SensorDashboard />);

      // Should display sync error
      expect(screen.getByText(/sync error:/i)).toBeInTheDocument();
      expect(screen.getByText('Sync failed: Unable to connect')).toBeInTheDocument();
    });

    it('retries failed operations with exponential backoff', async () => {
      vi.useFakeTimers();
      const user = userEvent.setup({ advanceTimers: vi.advanceTimersByTime });
      
      let attempts = 0;
      const mockSync = vi.fn().mockImplementation(() => {
        attempts++;
        if (attempts < 3) {
          return Promise.reject(new Error('Network error'));
        }
        return Promise.resolve({ success: true });
      });

      mockUseTemperatureSync.mockReturnValue({
        sync: mockSync,
        syncing: false,
        lastSyncTime: null,
        error: null,
      });

      renderWithTheme(<SensorDashboard />);

      const syncButton = screen.getByRole('button', { name: /sync/i });
      
      // First attempt fails
      await user.click(syncButton);
      await waitForNextTick();
      
      expect(attempts).toBe(1);
      
      // Simulate retry logic (would normally be in the hook)
      vi.advanceTimersByTime(1000); // 1 second delay
      
      // Second attempt fails
      mockSync();
      expect(attempts).toBe(2);
      
      vi.advanceTimersByTime(2000); // 2 second delay (exponential backoff)
      
      // Third attempt succeeds
      mockSync();
      expect(attempts).toBe(3);

      vi.useRealTimers();
    });
  });

  describe('Error Boundary Behavior', () => {
    it('catches and displays render errors', () => {
      const errorHandler = vi.fn();

      renderWithTheme(
        <TestErrorBoundary onError={errorHandler}>
          <ErrorSimulatorComponent errorType="render-error" />
        </TestErrorBoundary>
      );

      expect(screen.getByTestId('error-boundary')).toBeInTheDocument();
      expect(screen.getByText('Something went wrong')).toBeInTheDocument();
      expect(screen.getByTestId('error-message')).toHaveTextContent('Simulated render error');
      expect(errorHandler).toHaveBeenCalledWith(expect.any(Error));
    });

    it('provides error recovery mechanism', async () => {
      const user = userEvent.setup();
      const errorHandler = vi.fn();

      renderWithTheme(
        <TestErrorBoundary onError={errorHandler}>
          <ErrorSimulatorComponent errorType="render-error" />
        </TestErrorBoundary>
      );

      expect(screen.getByTestId('error-boundary')).toBeInTheDocument();

      const retryButton = screen.getByTestId('error-retry-btn');
      await user.click(retryButton);

      // Error boundary should reset and show component again
      await waitFor(() => {
        expect(screen.queryByTestId('error-boundary')).not.toBeInTheDocument();
        expect(screen.getByTestId('error-simulator')).toBeInTheDocument();
      });
    });

    it('handles nested component errors gracefully', () => {
      const errorHandler = vi.fn();

      renderWithTheme(
        <TestErrorBoundary onError={errorHandler}>
          <div>
            <SensorDashboard />
            <ErrorSimulatorComponent errorType="network-timeout" />
          </div>
        </TestErrorBoundary>
      );

      expect(screen.getByTestId('error-boundary')).toBeInTheDocument();
      expect(errorHandler).toHaveBeenCalledWith(
        expect.objectContaining({ message: 'Network request timed out' })
      );
    });
  });

  describe('Graceful Degradation', () => {
    it('shows cached data when API is unavailable', () => {
      const cachedSensorData = createMockSensorDataset(2);
      
      // Simulate API error but with cached data
      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: cachedSensorData,
        summary: {
          totalSensors: 2,
          onlineSensors: 0, // Show as offline due to API unavailability
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: -18.5,
          temperatureRange: null,
        },
        loading: false,
        error: 'API unavailable - showing cached data',
        refetch: vi.fn(),
      });

      renderWithTheme(
        <NetworkProvider initialOnline={false}>
          <OfflineAwareSensorDashboard />
        </NetworkProvider>
      );

      // Should show cached sensor data
      expect(screen.getByText('2')).toBeInTheDocument(); // Total sensors
      expect(screen.getByTestId('offline-banner')).toBeInTheDocument();
    });

    it('disables interactive features when offline', () => {
      renderWithTheme(
        <NetworkProvider initialOnline={false}>
          <OfflineAwareSensorDashboard />
        </NetworkProvider>
      );

      const syncButton = screen.getByRole('button', { name: /sync/i });
      const configButton = screen.getByRole('button', { name: /configure/i });
      
      // Interactive features should still be present but may show offline states
      expect(syncButton).toBeInTheDocument();
      expect(configButton).toBeInTheDocument();
    });

    it('maintains read-only functionality offline', () => {
      const offlineSensorData = createMockSensorDataset(3);
      
      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: offlineSensorData,
        summary: {
          totalSensors: 3,
          onlineSensors: 0,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: -18.5,
          temperatureRange: null,
        },
        loading: false,
        error: null,
        refetch: vi.fn(),
      });

      renderWithTheme(
        <NetworkProvider initialOnline={false}>
          <OfflineAwareSensorDashboard />
        </NetworkProvider>
      );

      // Should still display sensor information
      expect(screen.getByText('3')).toBeInTheDocument();
      expect(screen.getAllByTestId(/^sensor-tile-/)).toHaveLength(3);
    });
  });

  describe('User Feedback and Communication', () => {
    it('provides clear error messages for different failure types', () => {
      const errorScenarios = [
        {
          error: 'Network timeout after 30 seconds',
          expectedMessage: 'Network timeout after 30 seconds'
        },
        {
          error: 'Server returned 500 Internal Server Error',
          expectedMessage: 'Server returned 500 Internal Server Error'
        },
        {
          error: 'Authentication failed',
          expectedMessage: 'Authentication failed'
        }
      ];

      errorScenarios.forEach(({ error, expectedMessage }) => {
        mockUseTemperatureSync.mockReturnValue({
          sync: vi.fn(),
          syncing: false,
          lastSyncTime: null,
          error,
        });

        const { unmount } = renderWithTheme(<SensorDashboard />);

        expect(screen.getByText(/sync error:/i)).toBeInTheDocument();
        expect(screen.getByText(expectedMessage)).toBeInTheDocument();

        unmount();
      });
    });

    it('shows loading states during error recovery', async () => {
      const user = userEvent.setup();
      
      mockUseTemperatureSync.mockReturnValue({
        sync: vi.fn().mockImplementation(() => new Promise(() => {})), // Never resolves
        syncing: true,
        lastSyncTime: null,
        error: null,
      });

      renderWithTheme(<SensorDashboard />);

      const syncButton = screen.getByRole('button', { name: /syncing\.\.\./i });
      expect(syncButton).toBeDisabled();
      expect(syncButton.querySelector('[class*="animate-spin"]')).toBeInTheDocument();
    });

    it('provides actionable error recovery options', async () => {
      const user = userEvent.setup();
      const mockRefetch = vi.fn().mockResolvedValue({ success: true });

      mockUseSensorStatuses.mockReturnValue({
        sensorStatuses: [],
        summary: {
          totalSensors: 0,
          onlineSensors: 0,
          activeAlerts: 0,
          criticalAlerts: 0,
          averageTemperature: null,
          temperatureRange: null,
        },
        loading: false,
        error: 'Failed to load data',
        refetch: mockRefetch,
      });

      renderWithTheme(<SensorDashboard />);

      // Should provide retry option in empty state
      const syncButton = screen.getByRole('button', { name: /sync sensors/i });
      await user.click(syncButton);

      // This would trigger a refetch in the actual implementation
      expect(syncButton).toBeInTheDocument();
    });
  });

  describe('Data Integrity and Conflicts', () => {
    it('queues actions for later sync when offline', () => {
      renderWithTheme(
        <NetworkProvider initialOnline={false}>
          <div>
            <NetworkContext.Provider value={{
              isOnline: false,
              isConnecting: false,
              lastOnline: new Date(),
              retry: vi.fn(),
            }}>
              <div data-testid="sync-queue">
                <span data-testid="sync-count">3</span> items pending sync
              </div>
            </NetworkContext.Provider>
          </div>
        </NetworkProvider>
      );

      expect(screen.getByTestId('sync-count')).toHaveTextContent('3');
      expect(screen.getByText('items pending sync')).toBeInTheDocument();
    });

    it('handles data conflicts when coming back online', async () => {
      // This would be a complex scenario involving conflict resolution
      // Here we simulate the concept
      
      const conflictingData = {
        localTemperature: -18.5,
        serverTemperature: -19.2,
        timestamp: new Date().toISOString(),
        conflictResolution: 'server-wins', // or 'client-wins', 'merge', 'prompt-user'
      };

      // In a real implementation, this would be handled by the sync service
      expect(conflictingData.conflictResolution).toBe('server-wins');
      expect(conflictingData.serverTemperature).not.toBe(conflictingData.localTemperature);
    });

    it('validates data integrity after sync', async () => {
      const mockData = createMockSensorDataset(2);
      
      // Simulate successful sync with data validation
      const validateSensorData = (data: any[]) => {
        return data.every(sensor => {
          return (
            sensor.sensor &&
            sensor.sensor.id &&
            sensor.sensor.name &&
            typeof sensor.sensor.isOnline === 'boolean' &&
            (sensor.latest_reading === null || 
             (typeof sensor.latest_reading.temperature === 'number' &&
              typeof sensor.latest_reading.humidity === 'number'))
          );
        });
      };

      expect(validateSensorData(mockData)).toBe(true);
    });
  });

  describe('Cache Management', () => {
    it('manages cache expiration during offline periods', () => {
      const cacheEntry = {
        data: createMockSensorDataset(1),
        timestamp: Date.now() - (2 * 60 * 60 * 1000), // 2 hours ago
        maxAge: 60 * 60 * 1000, // 1 hour max age
      };

      const isCacheExpired = (entry: typeof cacheEntry) => {
        return Date.now() - entry.timestamp > entry.maxAge;
      };

      expect(isCacheExpired(cacheEntry)).toBe(true);
    });

    it('preserves critical data in cache', () => {
      const criticalData = {
        sensorStatus: 'critical_violation',
        temperature: -10.0, // Above critical threshold
        alertLevel: 'critical',
        lastUpdated: new Date().toISOString(),
        cacheForever: true, // Critical alerts should persist
      };

      // Critical data should be cached regardless of cache policies
      expect(criticalData.cacheForever).toBe(true);
      expect(criticalData.alertLevel).toBe('critical');
    });

    it('clears non-essential cache to free memory', () => {
      const cacheEntries = [
        { key: 'sensor-readings', priority: 'high', size: 1000 },
        { key: 'chart-data', priority: 'medium', size: 5000 },
        { key: 'ui-preferences', priority: 'low', size: 500 },
      ];

      // Simulate cache cleanup by removing low-priority items first
      const cleanCache = (entries: typeof cacheEntries, maxSize: number) => {
        const sorted = [...entries].sort((a, b) => {
          const priorityOrder = { high: 3, medium: 2, low: 1 };
          return priorityOrder[b.priority as keyof typeof priorityOrder] - priorityOrder[a.priority as keyof typeof priorityOrder];
        });

        let currentSize = 0;
        const kept = [];

        for (const entry of sorted) {
          if (currentSize + entry.size <= maxSize) {
            kept.push(entry);
            currentSize += entry.size;
          }
        }

        return kept;
      };

      const cleanedCache = cleanCache(cacheEntries, 4000);
      expect(cleanedCache.length).toBeLessThan(cacheEntries.length);
      expect(cleanedCache.find(e => e.priority === 'high')).toBeTruthy();
    });
  });

  describe('Background Sync and Recovery', () => {
    it('performs background sync when connection is restored', async () => {
      vi.useFakeTimers();
      
      const mockBackgroundSync = vi.fn().mockResolvedValue({ success: true });
      
      // Simulate connection restoration
      let isOnline = false;
      const simulateConnectionRestore = () => {
        isOnline = true;
        // Background sync should trigger automatically
        if (isOnline) {
          mockBackgroundSync();
        }
      };

      // Initially offline
      expect(isOnline).toBe(false);
      
      // Connection restored
      simulateConnectionRestore();
      
      // Background sync should have been triggered
      expect(mockBackgroundSync).toHaveBeenCalledTimes(1);
      expect(isOnline).toBe(true);

      vi.useRealTimers();
    });

    it('handles partial sync failures gracefully', async () => {
      const syncItems = [
        { id: '1', type: 'sensor-reading', data: { temperature: -18.5 } },
        { id: '2', type: 'alert-resolution', data: { resolved: true } },
        { id: '3', type: 'config-update', data: { threshold: -20.0 } },
      ];

      const mockPartialSync = vi.fn().mockImplementation((items) => {
        // Simulate partial failure - first item fails, others succeed
        return Promise.resolve({
          success: false,
          results: [
            { id: '1', success: false, error: 'Network timeout' },
            { id: '2', success: true },
            { id: '3', success: true },
          ],
          failedItems: items.filter((_: any, index: number) => index === 0)
        });
      });

      const result = await mockPartialSync(syncItems);
      
      expect(result.success).toBe(false);
      expect(result.failedItems).toHaveLength(1);
      expect(result.results.filter((r: any) => r.success)).toHaveLength(2);
    });

    it('prioritizes critical data in sync queue', () => {
      const syncQueue = [
        { id: '1', type: 'ui-preference', priority: 'low', data: {} },
        { id: '2', type: 'critical-alert', priority: 'critical', data: {} },
        { id: '3', type: 'sensor-reading', priority: 'high', data: {} },
        { id: '4', type: 'user-setting', priority: 'medium', data: {} },
      ];

      const prioritizeSync = (queue: typeof syncQueue) => {
        const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
        return [...queue].sort((a, b) => 
          priorityOrder[b.priority as keyof typeof priorityOrder] - 
          priorityOrder[a.priority as keyof typeof priorityOrder]
        );
      };

      const prioritized = prioritizeSync(syncQueue);
      
      expect(prioritized[0].type).toBe('critical-alert');
      expect(prioritized[1].type).toBe('sensor-reading');
      expect(prioritized[2].type).toBe('user-setting');
      expect(prioritized[3].type).toBe('ui-preference');
    });
  });
});