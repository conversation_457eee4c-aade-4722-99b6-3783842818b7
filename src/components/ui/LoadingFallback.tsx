/**
 * Optimized loading fallbacks for lazy-loaded components
 * Provides consistent loading states with performance considerations
 */
import React from 'react';

interface LoadingFallbackProps {
  message?: string;
  size?: 'small' | 'medium' | 'large' | 'full';
  variant?: 'spinner' | 'skeleton' | 'minimal';
  showIcon?: boolean;
}

export const LoadingFallback: React.FC<LoadingFallbackProps> = ({
  message = 'Loading...',
  size = 'medium',
  variant = 'spinner',
  showIcon = true
}) => {
  const sizeClasses = {
    small: 'h-16 text-sm',
    medium: 'h-32 text-base',
    large: 'h-64 text-lg',
    full: 'min-h-screen text-lg'
  };

  if (variant === 'minimal') {
    return (
      <div className={`flex items-center justify-center ${sizeClasses[size]} w-full`}>
        <span className="text-gray-500 font-medium">{message}</span>
      </div>
    );
  }

  if (variant === 'skeleton') {
    return (
      <div className={`${sizeClasses[size]} w-full p-4 animate-pulse`}>
        <div className="space-y-4">
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          <div className="h-4 bg-gray-200 rounded w-5/6"></div>
        </div>
      </div>
    );
  }

  // Default spinner variant
  return (
    <div className={`flex flex-col items-center justify-center ${sizeClasses[size]} w-full space-y-3`}>
      {showIcon && (
        <div className="relative">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-gray-200 border-t-blue-600"></div>
          <div className="absolute inset-0 rounded-full h-8 w-8 border-2 border-transparent border-r-blue-400 animate-spin" 
               style={{ animationDirection: 'reverse', animationDuration: '0.75s' }}></div>
        </div>
      )}
      <span className="text-gray-600 font-medium tracking-wide">{message}</span>
    </div>
  );
};

/**
 * Specialized loading components for different feature areas
 */
export const VoiceLoadingFallback: React.FC = () => (
  <LoadingFallback 
    message="Initializing voice processing..." 
    size="medium"
    variant="spinner"
  />
);

export const ImportLoadingFallback: React.FC = () => (
  <LoadingFallback 
    message="Loading import wizard..." 
    size="large"
    variant="skeleton"
  />
);

export const ChartLoadingFallback: React.FC = () => (
  <LoadingFallback 
    message="Preparing analytics..." 
    size="large"
    variant="skeleton"
  />
);

export const ComplianceLoadingFallback: React.FC = () => (
  <LoadingFallback 
    message="Loading compliance dashboard..." 
    size="medium"
    variant="spinner"
  />
);

export const VendorLoadingFallback: React.FC = () => (
  <LoadingFallback 
    message="Loading vendor management..." 
    size="medium"
    variant="skeleton"
  />
);

/**
 * Error boundary for lazy loading failures
 */
interface LazyLoadErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
}

interface LazyLoadErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  retryCount: number;
}

export class LazyLoadErrorBoundary extends React.Component<
  LazyLoadErrorBoundaryProps,
  LazyLoadErrorBoundaryState
> {
  private maxRetries = 3;

  constructor(props: LazyLoadErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null, retryCount: 0 };
  }

  static getDerivedStateFromError(error: Error): LazyLoadErrorBoundaryState {
    return { hasError: true, error, retryCount: 0 };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Lazy loading error:', error, errorInfo);
    
    // Report to monitoring service
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'exception', {
        description: `Lazy load error: ${error.message}`,
        fatal: false
      });
    }
  }

  retry = () => {
    if (this.state.retryCount < this.maxRetries) {
      this.setState({
        hasError: false,
        error: null,
        retryCount: this.state.retryCount + 1
      });
    }
  };

  render() {
    if (this.state.hasError) {
      const DefaultErrorFallback = () => (
        <div className="flex flex-col items-center justify-center h-32 w-full space-y-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="text-red-600 font-semibold">Failed to load component</div>
          <div className="text-sm text-gray-600 text-center">
            {this.state.error?.message || 'An unexpected error occurred'}
          </div>
          {this.state.retryCount < this.maxRetries && (
            <button
              onClick={this.retry}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Retry ({this.state.retryCount + 1}/{this.maxRetries})
            </button>
          )}
          {this.state.retryCount >= this.maxRetries && (
            <div className="text-sm text-gray-500">
              Please refresh the page if the problem persists
            </div>
          )}
        </div>
      );

      const ErrorFallback = this.props.fallback || DefaultErrorFallback;
      return <ErrorFallback error={this.state.error!} retry={this.retry} />;
    }

    return this.props.children;
  }
}

/**
 * Higher-order component that combines Suspense with error boundary
 */
export function withLazySuspense<P extends object>(
  LazyComponent: React.LazyExoticComponent<React.ComponentType<P>>,
  options: {
    fallback?: React.ComponentType;
    errorFallback?: React.ComponentType<{ error: Error; retry: () => void }>;
  } = {}
) {
  const { fallback: FallbackComponent = LoadingFallback, errorFallback } = options;

  return function LazyComponentWrapper(props: P) {
    return (
      <LazyLoadErrorBoundary fallback={errorFallback}>
        <React.Suspense fallback={<FallbackComponent />}>
          <LazyComponent {...props} />
        </React.Suspense>
      </LazyLoadErrorBoundary>
    );
  };
}

/**
 * Optimized Suspense wrapper for route-level components
 */
export const RouteSuspense: React.FC<{
  children: React.ReactNode;
  fallback?: React.ComponentType;
}> = ({ children, fallback: FallbackComponent = () => <LoadingFallback size="full" /> }) => {
  return (
    <LazyLoadErrorBoundary>
      <React.Suspense fallback={<FallbackComponent />}>
        {children}
      </React.Suspense>
    </LazyLoadErrorBoundary>
  );
};

export default LoadingFallback;