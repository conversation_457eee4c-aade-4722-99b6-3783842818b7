import React, { useEffect, useState } from 'react';

interface OverlayInfo {
  element: Element;
  zIndex: string;
  position: string;
  backgroundColor: string;
  className: string;
}

export default function OverlayDiagnostic() {
  const [overlays, setOverlays] = useState<OverlayInfo[]>([]);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const checkForOverlays = () => {
      const fixedElements = document.querySelectorAll('[class*="fixed"], [style*="position: fixed"], [style*="position:fixed"]');
      const absoluteElements = document.querySelectorAll('[class*="absolute"], [style*="position: absolute"], [style*="position:absolute"]');
      
      const allElements = [...fixedElements, ...absoluteElements];
      
      const overlayInfo: OverlayInfo[] = [];
      
      allElements.forEach(element => {
        const computedStyle = window.getComputedStyle(element);
        const rect = element.getBoundingClientRect();
        
        // Check if element covers significant area and might be an overlay
        if (rect.width > 100 && rect.height > 100) {
          overlayInfo.push({
            element,
            zIndex: computedStyle.zIndex || 'auto',
            position: computedStyle.position,
            backgroundColor: computedStyle.backgroundColor,
            className: element.className || element.tagName
          });
        }
      });
      
      setOverlays(overlayInfo);
    };

    // Check immediately and on interval
    checkForOverlays();
    const interval = setInterval(checkForOverlays, 2000);

    return () => clearInterval(interval);
  }, []);

  // Show diagnostic when Ctrl+Shift+D is pressed (development only)
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'D') {
        setIsVisible(prev => !prev);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  if (!isVisible || process.env.NODE_ENV === 'production') {
    return null;
  }

  return (
    <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-[9999] bg-white border-2 border-red-500 rounded-lg p-4 shadow-xl max-w-md max-h-96 overflow-y-auto">
      <div className="flex justify-between items-center mb-2">
        <h3 className="text-sm font-bold text-red-600">Overlay Diagnostic</h3>
        <button 
          onClick={() => setIsVisible(false)}
          className="text-red-500 hover:text-red-700 text-lg font-bold"
        >
          ×
        </button>
      </div>
      <div className="text-xs space-y-1">
        <p className="text-gray-600">Found {overlays.length} positioned elements:</p>
        {overlays.map((overlay, index) => (
          <div key={index} className="border border-gray-200 p-2 rounded">
            <div><strong>Class:</strong> {overlay.className}</div>
            <div><strong>Z-index:</strong> {overlay.zIndex}</div>
            <div><strong>Position:</strong> {overlay.position}</div>
            <div><strong>Background:</strong> {overlay.backgroundColor || 'transparent'}</div>
          </div>
        ))}
        <p className="text-gray-500 mt-2">Press Ctrl+Shift+D to toggle this diagnostic</p>
      </div>
    </div>
  );
}