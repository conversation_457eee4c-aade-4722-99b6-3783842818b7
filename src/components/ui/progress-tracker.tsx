import React from 'react';
import { CheckCircle, Circle, AlertCircle, Clock } from 'lucide-react';
import { Card } from './card';

export interface ProgressStep {
  id: string;
  title: string;
  description?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'error' | 'warning';
  timestamp?: Date;
  estimatedTime?: string;
  errorMessage?: string;
}

interface ProgressTrackerProps {
  steps: ProgressStep[];
  orientation?: 'horizontal' | 'vertical';
  showTimestamps?: boolean;
  showDescriptions?: boolean;
  className?: string;
}

export default function ProgressTracker({
  steps,
  orientation = 'vertical',
  showTimestamps = false,
  showDescriptions = true,
  className = ""
}: ProgressTrackerProps) {
  const getStatusIcon = (status: ProgressStep['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'in_progress':
        return (
          <div className="relative">
            <Circle className="w-5 h-5 text-blue-500" />
            <div className="absolute inset-0 w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
          </div>
        );
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      case 'warning':
        return <AlertCircle className="w-5 h-5 text-yellow-500" />;
      case 'pending':
      default:
        return <Circle className="w-5 h-5 text-gray-300" />;
    }
  };

  const getStatusColor = (status: ProgressStep['status']) => {
    switch (status) {
      case 'completed': return 'text-green-700 bg-green-50 border-green-200';
      case 'in_progress': return 'text-blue-700 bg-blue-50 border-blue-200';
      case 'error': return 'text-red-700 bg-red-50 border-red-200';
      case 'warning': return 'text-yellow-700 bg-yellow-50 border-yellow-200';
      case 'pending':
      default: return 'text-gray-700 bg-gray-50 border-gray-200';
    }
  };

  const getConnectorColor = (currentStatus: ProgressStep['status'], nextStatus?: ProgressStep['status']) => {
    if (currentStatus === 'completed') return 'bg-green-300';
    if (currentStatus === 'in_progress') return 'bg-blue-300';
    if (currentStatus === 'error') return 'bg-red-300';
    return 'bg-gray-200';
  };

  if (orientation === 'horizontal') {
    return (
      <div className={`flex items-center space-x-4 overflow-x-auto pb-2 ${className}`}>
        {steps.map((step, index) => (
          <React.Fragment key={step.id}>
            <div className="flex flex-col items-center min-w-0 flex-shrink-0">
              <div className={`p-3 rounded-lg border ${getStatusColor(step.status)}`}>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(step.status)}
                  <span className="font-medium text-sm whitespace-nowrap">{step.title}</span>
                </div>
                {showDescriptions && step.description && (
                  <p className="text-xs mt-1 text-center">{step.description}</p>
                )}
                {showTimestamps && step.timestamp && (
                  <p className="text-xs text-gray-500 mt-1 text-center">
                    {step.timestamp.toLocaleTimeString()}
                  </p>
                )}
                {step.estimatedTime && step.status === 'in_progress' && (
                  <div className="flex items-center justify-center space-x-1 text-xs text-gray-500 mt-1">
                    <Clock className="w-3 h-3" />
                    <span>{step.estimatedTime}</span>
                  </div>
                )}
                {step.errorMessage && step.status === 'error' && (
                  <p className="text-xs text-red-600 mt-1 text-center">{step.errorMessage}</p>
                )}
              </div>
            </div>
            
            {/* Horizontal Connector */}
            {index < steps.length - 1 && (
              <div className={`h-1 w-8 rounded ${getConnectorColor(step.status, steps[index + 1]?.status)}`} />
            )}
          </React.Fragment>
        ))}
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {steps.map((step, index) => (
        <React.Fragment key={step.id}>
          <Card className={`p-4 border ${getStatusColor(step.status)}`}>
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 mt-0.5">
                {getStatusIcon(step.status)}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium text-sm">{step.title}</h3>
                  {showTimestamps && step.timestamp && (
                    <span className="text-xs text-gray-500">
                      {step.timestamp.toLocaleTimeString()}
                    </span>
                  )}
                </div>
                
                {showDescriptions && step.description && (
                  <p className="text-sm text-gray-600 mt-1">{step.description}</p>
                )}
                
                {step.estimatedTime && step.status === 'in_progress' && (
                  <div className="flex items-center space-x-1 text-xs text-gray-500 mt-2">
                    <Clock className="w-3 h-3" />
                    <span>Estimated time: {step.estimatedTime}</span>
                  </div>
                )}
                
                {step.errorMessage && step.status === 'error' && (
                  <div className="mt-2 p-2 bg-red-100 border border-red-200 rounded text-xs text-red-700">
                    {step.errorMessage}
                  </div>
                )}
              </div>
            </div>
          </Card>
          
          {/* Vertical Connector */}
          {index < steps.length - 1 && (
            <div className="flex justify-start ml-6">
              <div className={`w-1 h-4 rounded ${getConnectorColor(step.status, steps[index + 1]?.status)}`} />
            </div>
          )}
        </React.Fragment>
      ))}
    </div>
  );
}

// Utility function to create progress steps
export function createProgressStep(
  id: string,
  title: string,
  options: Partial<Omit<ProgressStep, 'id' | 'title'>> = {}
): ProgressStep {
  return {
    id,
    title,
    status: 'pending',
    ...options
  };
}

// Hook for managing progress state
export function useProgressTracker(initialSteps: ProgressStep[]) {
  const [steps, setSteps] = React.useState<ProgressStep[]>(initialSteps);

  const updateStep = React.useCallback((
    stepId: string, 
    updates: Partial<Omit<ProgressStep, 'id'>>
  ) => {
    setSteps(prev => prev.map(step => 
      step.id === stepId 
        ? { ...step, ...updates, timestamp: updates.status ? new Date() : step.timestamp }
        : step
    ));
  }, []);

  const resetSteps = React.useCallback(() => {
    setSteps(prev => prev.map(step => ({ ...step, status: 'pending' as const, timestamp: undefined })));
  }, []);

  const getActiveStep = React.useCallback(() => {
    return steps.find(step => step.status === 'in_progress');
  }, [steps]);

  const getCompletedCount = React.useCallback(() => {
    return steps.filter(step => step.status === 'completed').length;
  }, [steps]);

  const getProgress = React.useCallback(() => {
    return (getCompletedCount() / steps.length) * 100;
  }, [steps.length, getCompletedCount]);

  return {
    steps,
    updateStep,
    resetSteps,
    getActiveStep,
    getCompletedCount,
    getProgress
  };
}