import { useEffect, useMemo, useState } from 'react';
import { supabase } from '../lib/supabase';
import Modal from './modals/Modal';
import HACCPEventForm from './forms/HACCPEventForm';

type InventoryEvent = {
  id: string;
  event_type: string;
  product_id: string | null;
  quantity: number | null;
  notes: string | null;
  created_at: string; // ISO
  // occurred_at: string | null; // Column doesn't exist yet
};

const EVENT_COLORS: Record<string, string> = {
  receiving: 'bg-green-100 text-green-800 border-green-200',
  disposal: 'bg-red-100 text-red-800 border-red-200',
  physical_count: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  sale: 'bg-blue-100 text-blue-800 border-blue-200',
  // New shipping and logistics events
  order_placed: 'bg-purple-100 text-purple-800 border-purple-200',
  shipping_arrival: 'bg-teal-100 text-teal-800 border-teal-200',
  shipping_departure: 'bg-orange-100 text-orange-800 border-orange-200',
  customer_delivery: 'bg-indigo-100 text-indigo-800 border-indigo-200',
  // Additional operational events
  quality_check: 'bg-cyan-100 text-cyan-800 border-cyan-200',
  temperature_log: 'bg-emerald-100 text-emerald-800 border-emerald-200',
  maintenance: 'bg-gray-100 text-gray-800 border-gray-200',
  default: 'bg-gray-100 text-gray-800 border-gray-200',
};

function formatISODate(d: Date) {
  const tzOff = d.getTimezoneOffset() * 60000;
  return new Date(d.getTime() - tzOff).toISOString().slice(0, 10);
}

function startOfMonth(date: Date) {
  return new Date(date.getFullYear(), date.getMonth(), 1);
}
function endOfMonth(date: Date) {
  return new Date(date.getFullYear(), date.getMonth() + 1, 0);
}
function startOfWeek(date: Date) {
  const d = new Date(date);
  const day = d.getDay(); // 0 Sun - 6 Sat
  d.setDate(d.getDate() - day);
  d.setHours(0, 0, 0, 0);
  return d;
}
function endOfWeek(date: Date) {
  const d = new Date(date);
  const day = d.getDay(); // 0 Sun - 6 Sat
  d.setDate(d.getDate() + (6 - day));
  d.setHours(23, 59, 59, 999);
  return d;
}

export default function HACCPCalendar() {
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [events, setEvents] = useState<InventoryEvent[]>([]);
  const [selectedDate, setSelectedDate] = useState<string | null>(null); // YYYY-MM-DD
  const [activeTypes, setActiveTypes] = useState<Record<string, boolean>>({
    receiving: true,
    disposal: true,
    physical_count: true,
    sale: true,
    order_placed: true,
    shipping_arrival: true,
    shipping_departure: true,
    customer_delivery: true,
    quality_check: true,
    temperature_log: true,
    maintenance: true,
  });
  const [isAddOpen, setIsAddOpen] = useState(false);
  const [dateForAdd, setDateForAdd] = useState<string | null>(null);
  const [reloadKey, setReloadKey] = useState(0);

  const monthStart = useMemo(() => startOfMonth(currentMonth), [currentMonth]);
  const monthEnd = useMemo(() => endOfMonth(currentMonth), [currentMonth]);
  const gridStart = useMemo(() => startOfWeek(monthStart), [monthStart]);
  const gridEnd = useMemo(() => endOfWeek(monthEnd), [monthEnd]);

  const days = useMemo(() => {
    const arr: Date[] = [];
    const cur = new Date(gridStart);
    while (cur <= gridEnd) {
      arr.push(new Date(cur));
      cur.setDate(cur.getDate() + 1);
    }
    return arr;
  }, [gridStart, gridEnd]);

  useEffect(() => {
    let cancelled = false;
    const load = async () => {
      setLoading(true);
      setError(null);
      console.log('HACCPCalendar: Loading events, reloadKey:', reloadKey);
      try {
        // Get all events and filter client-side to avoid date query issues
        const { data, error } = await supabase
          .from('inventory_events')
          .select('id, event_type, product_id, quantity, notes, created_at')
          .order('created_at', { ascending: false })
          .limit(1000);
        if (error) throw error;
        if (!cancelled) {
          setEvents(data || []);
          console.log('HACCPCalendar: Loaded', data?.length || 0, 'events');
        }
      } catch (e: unknown) {
        const msg = e instanceof Error ? e.message : 'Failed to load events';
        if (!cancelled) setError(msg);
      } finally {
        if (!cancelled) setLoading(false);
      }
    };
    load();
    return () => { cancelled = true; };
  }, [gridStart, gridEnd, reloadKey]);

  const typesInData = useMemo(() => {
    const set = new Set<string>();
    events.forEach(ev => set.add(ev.event_type));
    return Array.from(set);
  }, [events]);

  const eventsByDay = useMemo(() => {
    const map: Record<string, InventoryEvent[]> = {};
    console.log('HACCPCalendar: Processing', events.length, 'events for calendar view');
    console.log('Calendar range:', formatISODate(gridStart), 'to', formatISODate(gridEnd));
    
    for (const ev of events) {
      const enabled = Object.prototype.hasOwnProperty.call(activeTypes, ev.event_type)
        ? Boolean(activeTypes[ev.event_type as keyof typeof activeTypes])
        : true;
      if (!enabled) continue;
      
      // Use created_at since occurred_at column doesn't exist yet
      const d = new Date(ev.created_at);
      const eventDateKey = formatISODate(d);
      
      console.log('Event:', ev.event_type, 'date:', eventDateKey, 'in range?', d >= gridStart && d <= gridEnd);
      
      // Only include events within the current calendar view
      if (d >= gridStart && d <= gridEnd) {
        const key = formatISODate(d);
        if (!map[key]) map[key] = [];
        map[key].push(ev);
      }
    }
    console.log('Events by day:', Object.keys(map).length, 'days with events');
    return map;
  }, [events, activeTypes, gridStart, gridEnd]);

  const todayKey = formatISODate(new Date());

  const goPrev = () => setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1));
  const goNext = () => setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1));
  const goToday = () => setCurrentMonth(new Date());

  const openAddForDay = (key: string) => {
    setSelectedDate(key);
    const dayEvents = eventsByDay[key] || [];
    
    // If there are existing events, show them in the modal
    // If no events, show the form to add one
    if (dayEvents.length > 0) {
      setDateForAdd(null); // Don't show form initially
    } else {
      setDateForAdd(key); // Show form for empty days
    }
    setIsAddOpen(true);
  };

  const handleAddSuccess = () => {
    setIsAddOpen(false);
    setDateForAdd(null);
    // Small delay to ensure database write completes before reload
    setTimeout(() => {
      setReloadKey(k => k + 1);
    }, 100);
  };

  const handleAddCancel = () => {
    setIsAddOpen(false);
    setDateForAdd(null);
  };

  return (
    <div className="bg-white rounded-lg shadow p-4 md:p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <button aria-label="Previous month" className="px-2 py-1 rounded border hover:bg-gray-50" onClick={goPrev}>&lt;</button>
          <button aria-label="Today" className="px-2 py-1 rounded border hover:bg-gray-50" onClick={goToday}>Today</button>
          <button aria-label="Next month" className="px-2 py-1 rounded border hover:bg-gray-50" onClick={goNext}>&gt;</button>
        </div>
        <h2 className="text-lg font-semibold text-gray-900">
          {currentMonth.toLocaleString(undefined, { month: 'long', year: 'numeric' })}
        </h2>
        <div className="flex flex-wrap items-center gap-2">
          {typesInData.length === 0 && <span className="text-xs text-gray-500">No events</span>}
          
          {/* Core HACCP Events */}
          <div className="flex items-center gap-1 text-xs text-gray-600 font-medium">Core:</div>
          {['receiving','disposal','physical_count','sale'].map(t => (
            <label key={t} className="flex items-center gap-1 text-xs border rounded-full px-2 py-1 cursor-pointer select-none hover:bg-gray-50">
              <input
                type="checkbox"
                className="accent-blue-600"
                checked={activeTypes[t as keyof typeof activeTypes] ?? true}
                onChange={(e) => setActiveTypes(prev => ({ ...prev, [t]: e.target.checked }))}
              />
              <span className="capitalize">{t.replace('_',' ')}</span>
            </label>
          ))}
          
          {/* Logistics & Shipping */}
          <div className="flex items-center gap-1 text-xs text-gray-600 font-medium ml-2">Logistics:</div>
          {['order_placed','shipping_arrival','shipping_departure','customer_delivery'].map(t => (
            <label key={t} className="flex items-center gap-1 text-xs border rounded-full px-2 py-1 cursor-pointer select-none hover:bg-gray-50">
              <input
                type="checkbox"
                className="accent-blue-600"
                checked={activeTypes[t as keyof typeof activeTypes] ?? true}
                onChange={(e) => setActiveTypes(prev => ({ ...prev, [t]: e.target.checked }))}
              />
              <span className="capitalize">{t.replace('_',' ')}</span>
            </label>
          ))}
          
          {/* Operations */}
          <div className="flex items-center gap-1 text-xs text-gray-600 font-medium ml-2">Operations:</div>
          {['quality_check','temperature_log','maintenance'].map(t => (
            <label key={t} className="flex items-center gap-1 text-xs border rounded-full px-2 py-1 cursor-pointer select-none hover:bg-gray-50">
              <input
                type="checkbox"
                className="accent-blue-600"
                checked={activeTypes[t as keyof typeof activeTypes] ?? true}
                onChange={(e) => setActiveTypes(prev => ({ ...prev, [t]: e.target.checked }))}
              />
              <span className="capitalize">{t.replace('_',' ')}</span>
            </label>
          ))}
          
          {/* Additional dynamic types from data */}
          {typesInData.filter(t => ![
            'receiving','disposal','physical_count','sale',
            'order_placed','shipping_arrival','shipping_departure','customer_delivery',
            'quality_check','temperature_log','maintenance'
          ].includes(t)).map(t => (
            <label key={t} className="flex items-center gap-1 text-xs border rounded-full px-2 py-1 cursor-pointer select-none hover:bg-gray-50 border-dashed">
              <input
                type="checkbox"
                className="accent-blue-600"
                checked={activeTypes[t as keyof typeof activeTypes] ?? true}
                onChange={(e) => setActiveTypes(prev => ({ ...prev, [t]: e.target.checked }))}
              />
              <span className="capitalize">{t.replace('_',' ')}</span>
            </label>
          ))}
        </div>
      </div>

      {error && <div className="mb-3 text-sm text-red-700 bg-red-50 border border-red-200 rounded p-2">{error}</div>}
      {loading && !error && (
        <div className="mb-3 text-sm text-gray-600 bg-gray-50 border border-gray-200 rounded p-2">Loading events…</div>
      )}

      <div className="grid grid-cols-7 gap-2" aria-label="HACCP Events Calendar">
        {['Sun','Mon','Tue','Wed','Thu','Fri','Sat'].map(d => (
          <div key={d} className="text-xs font-medium text-gray-500 text-center py-1">{d}</div>
        ))}
        {days.map((d) => {
          const key = formatISODate(d);
          const inMonth = d.getMonth() === currentMonth.getMonth();
          const isToday = key === todayKey;
          const dayEvents = eventsByDay[key] || [];
          return (
            <button
              key={key}
              data-selected={selectedDate === key ? 'true' : 'false'}
              onClick={() => openAddForDay(key)}
              className={`min-h-[88px] rounded border p-1 text-left focus:outline-none focus:ring-2 focus:ring-blue-500 ${inMonth ? '' : 'bg-gray-50'} ${isToday ? 'border-blue-500' : 'border-gray-200'}`}
            >
              <div className="flex items-center justify-between mb-1">
                <span className={`text-xs ${inMonth ? 'text-gray-700' : 'text-gray-400'}`}>{d.getDate()}</span>
                {isToday && <span className="text-[10px] text-blue-600">Today</span>}
              </div>
              <div className="flex flex-wrap gap-1">
                {dayEvents.slice(0, 3).map(ev => (
                  <span
                    key={ev.id}
                    className={`border rounded px-1 py-0.5 text-[10px] ${EVENT_COLORS[ev.event_type] || EVENT_COLORS.default}`}
                    title={`${ev.event_type} • ${ev.quantity ?? ''}`.trim()}
                  >
                    {ev.event_type.replace('_',' ')}
                  </span>
                ))}
                {dayEvents.length > 3 && (
                  <span className="text-[10px] text-gray-600">+{dayEvents.length - 3} more</span>
                )}
              </div>
            </button>
          );
        })}
      </div>

      <div className="mt-4">
        {selectedDate ? (
          <>
            <h3 className="text-sm font-semibold text-gray-900 mb-2">Events on {selectedDate}</h3>
            <ul className="space-y-2 max-h-64 overflow-auto">
              {(eventsByDay[selectedDate] || []).map(ev => (
                <li key={ev.id} className="border rounded p-2">
                  <div className="flex items-center justify-between">
                    <span className={`inline-flex items-center gap-1 text-xs px-2 py-0.5 border rounded-full ${EVENT_COLORS[ev.event_type] || EVENT_COLORS.default}`}>
                      {ev.event_type.replace('_',' ')}
                    </span>
                    <span className="text-xs text-gray-500">{new Date(ev.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</span>
                  </div>
                  <div className="text-sm text-gray-800 mt-1">
                    Qty: {ev.quantity ?? '—'}
                  </div>
                  {ev.notes && <div className="text-xs text-gray-600 mt-1">{ev.notes}</div>}
                </li>
              ))}
              {(!eventsByDay[selectedDate] || eventsByDay[selectedDate].length === 0) && (
                <li className="text-sm text-gray-500">No events</li>
              )}
            </ul>
          </>
        ) : (
          <p className="text-sm text-gray-500">Select a day to view details.</p>
        )}
      </div>

      <Modal isOpen={isAddOpen} onClose={handleAddCancel} title={`${selectedDate ? `Events for ${selectedDate}` : 'Add Event'}`}>
        {selectedDate && !dateForAdd ? (
          // Show existing events with option to add new
          <div className="space-y-4">
            <div className="max-h-64 overflow-auto">
              {(eventsByDay[selectedDate] || []).length > 0 ? (
                <ul className="space-y-2">
                  {(eventsByDay[selectedDate] || []).map(ev => (
                    <li key={ev.id} className="border rounded p-3 bg-gray-50">
                      <div className="flex items-center justify-between mb-2">
                        <span className={`inline-flex items-center gap-1 text-xs px-2 py-0.5 border rounded-full ${EVENT_COLORS[ev.event_type] || EVENT_COLORS.default}`}>
                          {ev.event_type.replace('_',' ')}
                        </span>
                        <span className="text-xs text-gray-500">
                          {new Date(ev.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </span>
                      </div>
                      <div className="text-sm text-gray-800">
                        Quantity: {ev.quantity ?? '—'}
                      </div>
                      {ev.notes && (
                        <div className="text-xs text-gray-600 mt-1">{ev.notes}</div>
                      )}
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-sm text-gray-500 text-center py-4">No events on this day</p>
              )}
            </div>
            <div className="border-t pt-4">
              <button
                onClick={() => setDateForAdd(selectedDate)}
                className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Add New Event for {selectedDate}
              </button>
            </div>
          </div>
        ) : dateForAdd ? (
          // Show form to add new event
          <HACCPEventForm onSuccess={handleAddSuccess} onCancel={handleAddCancel} dateOverride={dateForAdd} />
        ) : null}
      </Modal>
    </div>
  );
}
