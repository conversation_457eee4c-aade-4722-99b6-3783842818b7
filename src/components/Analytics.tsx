import React, { useState } from 'react';
import { Bar<PERSON>hart2, TrendingUp, <PERSON><PERSON>hart, Activity } from 'lucide-react';

interface AnalyticsCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  change?: string;
}

const AnalyticsCard: React.FC<AnalyticsCardProps> = ({ title, value, icon, change }) => (
  <div className="bg-white p-6 rounded-lg shadow-sm">
    <div className="flex items-center justify-between mb-4">
      <h3 className="text-gray-500 text-sm">{title}</h3>
      <div className="text-gray-400">{icon}</div>
    </div>
    <div className="flex items-baseline">
      <h2 className="text-3xl font-semibold">{value}</h2>
      {change && (
        <span className={`ml-2 text-sm ${parseFloat(change) >= 0 ? 'text-green-500' : 'text-red-500'}`}>
          {parseFloat(change) >= 0 ? '+' : ''}{change}
        </span>
      )}
    </div>
  </div>
);

const Analytics: React.FC = () => {
  const [timeframe, setTimeframe] = useState<'day' | 'week' | 'month' | 'year'>('month');
  const [isLoading] = useState(false);
  const [error] = useState<string | null>(null);

  const handleTimeframeChange = (newTimeframe: 'day' | 'week' | 'month' | 'year') => {
    setTimeframe(newTimeframe);
    // Here you would typically fetch new data based on timeframe
  };

  if (isLoading) return <div className="p-6">Loading analytics...</div>;
  if (error) return <div className="p-6 text-red-500">Error: {error}</div>;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Analytics Dashboard</h1>
        
        <div className="flex gap-2">
          {(['day', 'week', 'month', 'year'] as const).map((period) => (
            <button
              key={period}
              onClick={() => handleTimeframeChange(period)}
              className={`px-4 py-2 rounded ${
                timeframe === period 
                  ? 'bg-blue-500 text-white' 
                  : 'bg-gray-100 hover:bg-gray-200'
              }`}
            >
              {period.charAt(0).toUpperCase() + period.slice(1)}
            </button>
          ))}
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <AnalyticsCard
          title="Total Revenue"
          value="$54,230"
          icon={<TrendingUp />}
          change="8.2%"
        />
        <AnalyticsCard
          title="Orders"
          value="1,234"
          icon={<BarChart2 />}
          change="12.5%"
        />
        <AnalyticsCard
          title="Customers"
          value="789"
          icon={<PieChart />}
          change="5.3%"
        />
        <AnalyticsCard
          title="Avg. Order Value"
          value="$43.90"
          icon={<Activity />}
          change="-2.1%"
        />
      </div>
    </div>
  );
};

export default Analytics;