import { useState } from 'react';
import { useDropzone } from 'react-dropzone';
import * as XLSX from 'xlsx';
import { AlertCircle, FileSpreadsheet, Upload } from 'lucide-react';
import { Customer } from '../../types';
import { batchCreateCustomers } from '../../lib/api';

interface ValidationError {
  row: number;
  message: string;
}

export default function ImportCustomers({ onSuccess }: { onSuccess: () => void }) {
  const [data, setData] = useState<Partial<Customer>[]>([]);
  const [errors, setErrors] = useState<ValidationError[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  const validateCustomerData = (data: Partial<Customer>[]): ValidationError[] => {
    const errors: ValidationError[] = [];
    data.forEach((row, index) => {
      if (!row.name) {
        errors.push({ row: index + 1, message: 'Name is required' });
      }
      if (!row.channelType || !['wholesale', 'retail', 'distributor'].includes(row.channelType)) {
        errors.push({ row: index + 1, message: 'Valid channel type is required (wholesale, retail, or distributor)' });
      }
      if (row.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(row.email)) {
        errors.push({ row: index + 1, message: 'Invalid email format' });
      }
      if (row.creditLimit && isNaN(Number(row.creditLimit))) {
        errors.push({ row: index + 1, message: 'Credit limit must be a number' });
      }
    });
    return errors;
  };

  const onDrop = async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    const reader = new FileReader();

    reader.onload = async (e) => {
      try {
        setIsProcessing(true);
        setErrors([]);

        const data = e.target?.result;
        const workbook = XLSX.read(data, { type: 'binary' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet) as Partial<Customer>[];

        // Validate data
        const validationErrors = validateCustomerData(jsonData);
        if (validationErrors.length > 0) {
          setErrors(validationErrors);
          return;
        }

        setData(jsonData);
      } catch (error) {
        setErrors([{ 
          row: 0, 
          message: error instanceof Error ? error.message : 'Failed to process file' 
        }]);
      } finally {
        setIsProcessing(false);
      }
    };

    reader.readAsBinaryString(file);
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls'],
      'text/csv': ['.csv']
    },
    multiple: false
  });

  const handleSave = async () => {
    if (!data.length) return;

    setIsProcessing(true);
    try {
      const results = await batchCreateCustomers(data);
      console.log('Imported customers:', results);
      
      // Clear form after successful save
      setData([]);
      setErrors([]);
      onSuccess();
    } catch (error) {
      setErrors([{ 
        row: 0, 
        message: error instanceof Error ? error.message : 'Failed to save data' 
      }]);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-semibold">Import Customers</h2>
        <a
          href="/customer-template.xlsx"
          download
          className="text-blue-600 hover:text-blue-700"
        >
          Download Template
        </a>
      </div>

      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors
          ${isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-blue-400'}`}
      >
        <input {...getInputProps()} />
        {isProcessing ? (
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
            <p className="text-gray-600">Processing file...</p>
          </div>
        ) : (
          <>
            <FileSpreadsheet className="w-12 h-12 mx-auto text-gray-400 mb-4" />
            <p className="text-gray-600">
              {isDragActive
                ? 'Drop the file here'
                : 'Drag & drop a file, or click to select'}
            </p>
            <p className="text-sm text-gray-500 mt-2">
              Supports XLSX, XLS, CSV
            </p>
          </>
        )}
      </div>

      {errors.length > 0 && (
        <div className="mt-4 bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center gap-2 text-red-800 mb-2">
            <AlertCircle className="w-5 h-5" />
            <h3 className="font-medium">Validation Errors</h3>
          </div>
          <ul className="text-sm text-red-700 space-y-1 list-disc list-inside">
            {errors.map((error, index) => (
              <li key={index}>Row {error.row}: {error.message}</li>
            ))}
          </ul>
        </div>
      )}

      {data.length > 0 && (
        <div className="mt-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-medium text-gray-900">
              {data.length} records ready to import
            </h3>
            <button
              onClick={handleSave}
              disabled={isProcessing}
              className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              <Upload className="w-4 h-4" />
              Import Customers
            </button>
          </div>
          <div className="max-h-96 overflow-y-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {Object.keys(data[0]).map((key) => (
                    <th
                      key={key}
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      {key}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {data.map((row, index) => (
                  <tr key={index}>
                    {Object.values(row).map((value, i) => (
                      <td
                        key={i}
                        className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
                      >
                        {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
} 