import React, { useState, useRef, useEffect, useCallback } from 'react';
import { 
  Play, 
  Pause, 
  Volume2, 
  VolumeX, 
  RotateCcw, 
  Download,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { audioStorageService } from '../../services/AudioStorageService';

interface AudioPlaybackProps {
  audioUrl?: string;
  eventId?: string;
  title?: string;
  showDownload?: boolean;
  showControls?: boolean;
  autoPlay?: boolean;
  className?: string;
}

interface AudioState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  isMuted: boolean;
  isLoading: boolean;
  error: string | null;
}

export const AudioPlayback: React.FC<AudioPlaybackProps> = ({
  audioUrl,
  eventId,
  title = 'Voice Recording',
  showDownload = true,
  showControls = true,
  autoPlay = false,
  className = ''
}) => {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [audioState, setAudioState] = useState<AudioState>({
    isPlaying: false,
    currentTime: 0,
    duration: 0,
    volume: 1,
    isMuted: false,
    isLoading: false,
    error: null
  });
  const [signedUrl, setSignedUrl] = useState<string | null>(null);

  // Load signed URL if needed
  useEffect(() => {
    const loadSignedUrl = async () => {
      if (!audioUrl) return;

      setAudioState(prev => ({ ...prev, isLoading: true, error: null }));

      try {
        // If it's already a blob URL or direct URL, use it
        if (audioUrl.startsWith('blob:') || audioUrl.startsWith('http')) {
          setSignedUrl(audioUrl);
        } else {
          // Get signed URL from storage service
          const url = await audioStorageService.getAudioRecordingUrl(audioUrl);
          if (url) {
            setSignedUrl(url);
          } else {
            setAudioState(prev => ({ ...prev, error: 'Failed to load audio recording' }));
          }
        }
      } catch (error) {
        console.error('Error loading audio URL:', error);
        setAudioState(prev => ({ ...prev, error: 'Failed to load audio recording' }));
      } finally {
        setAudioState(prev => ({ ...prev, isLoading: false }));
      }
    };

    loadSignedUrl();
  }, [audioUrl]);

  // Audio event handlers
  const handleLoadedMetadata = useCallback(() => {
    if (audioRef.current) {
      setAudioState(prev => ({
        ...prev,
        duration: audioRef.current?.duration || 0,
        error: null
      }));
    }
  }, []);

  const handleTimeUpdate = useCallback(() => {
    if (audioRef.current) {
      setAudioState(prev => ({
        ...prev,
        currentTime: audioRef.current?.currentTime || 0
      }));
    }
  }, []);

  const handleEnded = useCallback(() => {
    setAudioState(prev => ({ ...prev, isPlaying: false, currentTime: 0 }));
    if (audioRef.current) {
      audioRef.current.currentTime = 0;
    }
  }, []);

  const handleError = useCallback((error: any) => {
    console.error('Audio playback error:', error);
    setAudioState(prev => ({
      ...prev,
      isPlaying: false,
      error: 'Failed to play audio recording'
    }));
  }, []);

  // Control functions
  const togglePlayPause = useCallback(async () => {
    if (!audioRef.current || !signedUrl) return;

    try {
      if (audioState.isPlaying) {
        audioRef.current.pause();
        setAudioState(prev => ({ ...prev, isPlaying: false }));
      } else {
        await audioRef.current.play();
        setAudioState(prev => ({ ...prev, isPlaying: true, error: null }));
      }
    } catch (error) {
      console.error('Error toggling playback:', error);
      setAudioState(prev => ({ ...prev, error: 'Failed to play audio' }));
    }
  }, [audioState.isPlaying, signedUrl]);

  const handleSeek = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    if (!audioRef.current) return;

    const newTime = parseFloat(event.target.value);
    audioRef.current.currentTime = newTime;
    setAudioState(prev => ({ ...prev, currentTime: newTime }));
  }, []);

  const handleVolumeChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    if (!audioRef.current) return;

    const newVolume = parseFloat(event.target.value);
    audioRef.current.volume = newVolume;
    setAudioState(prev => ({ 
      ...prev, 
      volume: newVolume,
      isMuted: newVolume === 0
    }));
  }, []);

  const toggleMute = useCallback(() => {
    if (!audioRef.current) return;

    const newMuted = !audioState.isMuted;
    audioRef.current.muted = newMuted;
    setAudioState(prev => ({ ...prev, isMuted: newMuted }));
  }, [audioState.isMuted]);

  const restart = useCallback(() => {
    if (!audioRef.current) return;

    audioRef.current.currentTime = 0;
    setAudioState(prev => ({ ...prev, currentTime: 0 }));
  }, []);

  const downloadAudio = useCallback(async () => {
    if (!signedUrl) return;

    try {
      const response = await fetch(signedUrl);
      const blob = await response.blob();
      
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `voice-recording-${eventId || 'unknown'}.webm`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading audio:', error);
    }
  }, [signedUrl, eventId]);

  // Format time for display
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Auto-play effect
  useEffect(() => {
    if (autoPlay && signedUrl && audioRef.current && !audioState.isPlaying) {
      togglePlayPause();
    }
  }, [autoPlay, signedUrl, audioState.isPlaying, togglePlayPause]);

  if (!audioUrl && !signedUrl) {
    return null;
  }

  if (audioState.error) {
    return (
      <div className={`flex items-center space-x-2 text-red-600 ${className}`}>
        <AlertCircle className="w-4 h-4" />
        <span className="text-sm">{audioState.error}</span>
      </div>
    );
  }

  if (audioState.isLoading) {
    return (
      <div className={`flex items-center space-x-2 text-gray-500 ${className}`}>
        <Loader2 className="w-4 h-4 animate-spin" />
        <span className="text-sm">Loading audio...</span>
      </div>
    );
  }

  return (
    <div className={`bg-gray-50 rounded-lg p-4 ${className}`}>
      {/* Hidden audio element */}
      {signedUrl && (
        <audio
          ref={audioRef}
          src={signedUrl}
          onLoadedMetadata={handleLoadedMetadata}
          onTimeUpdate={handleTimeUpdate}
          onEnded={handleEnded}
          onError={handleError}
          preload="metadata"
        />
      )}

      {/* Title */}
      <div className="flex items-center justify-between mb-3">
        <h4 className="text-sm font-medium text-gray-700 flex items-center">
          <Volume2 className="w-4 h-4 mr-2" />
          {title}
        </h4>
        {showDownload && signedUrl && (
          <button
            onClick={downloadAudio}
            className="p-1 text-gray-400 hover:text-gray-600 rounded"
            title="Download audio"
          >
            <Download className="w-4 h-4" />
          </button>
        )}
      </div>

      {/* Main controls */}
      <div className="flex items-center space-x-3">
        {/* Play/Pause button */}
        <button
          onClick={togglePlayPause}
          disabled={!signedUrl}
          className="flex items-center justify-center w-10 h-10 bg-blue-600 text-white rounded-full hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {audioState.isPlaying ? (
            <Pause className="w-5 h-5" />
          ) : (
            <Play className="w-5 h-5 ml-0.5" />
          )}
        </button>

        {/* Progress bar */}
        {showControls && (
          <div className="flex-1">
            <input
              type="range"
              min="0"
              max={audioState.duration || 0}
              value={audioState.currentTime}
              onChange={handleSeek}
              disabled={!signedUrl || audioState.duration === 0}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>{formatTime(audioState.currentTime)}</span>
              <span>{formatTime(audioState.duration)}</span>
            </div>
          </div>
        )}

        {/* Additional controls */}
        {showControls && (
          <div className="flex items-center space-x-2">
            {/* Restart button */}
            <button
              onClick={restart}
              disabled={!signedUrl}
              className="p-2 text-gray-400 hover:text-gray-600 rounded focus:outline-none disabled:opacity-50"
              title="Restart"
            >
              <RotateCcw className="w-4 h-4" />
            </button>

            {/* Volume control */}
            <div className="flex items-center space-x-2">
              <button
                onClick={toggleMute}
                disabled={!signedUrl}
                className="p-1 text-gray-400 hover:text-gray-600 rounded focus:outline-none disabled:opacity-50"
                title={audioState.isMuted ? 'Unmute' : 'Mute'}
              >
                {audioState.isMuted ? (
                  <VolumeX className="w-4 h-4" />
                ) : (
                  <Volume2 className="w-4 h-4" />
                )}
              </button>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={audioState.isMuted ? 0 : audioState.volume}
                onChange={handleVolumeChange}
                disabled={!signedUrl}
                className="w-16 h-1 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                title="Volume"
              />
            </div>
          </div>
        )}
      </div>

      {/* Simple controls (just play button and time) */}
      {!showControls && audioState.duration > 0 && (
        <div className="flex items-center justify-between text-xs text-gray-500 mt-2">
          <span>{formatTime(audioState.currentTime)} / {formatTime(audioState.duration)}</span>
          {audioState.isPlaying && (
            <div className="flex items-center space-x-1">
              <div className="w-1 h-3 bg-blue-500 rounded animate-pulse"></div>
              <div className="w-1 h-2 bg-blue-400 rounded animate-pulse" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-1 h-4 bg-blue-500 rounded animate-pulse" style={{ animationDelay: '0.2s' }}></div>
              <div className="w-1 h-2 bg-blue-400 rounded animate-pulse" style={{ animationDelay: '0.3s' }}></div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AudioPlayback;