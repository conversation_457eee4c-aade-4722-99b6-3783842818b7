import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Mi<PERSON>, Mic<PERSON><PERSON>, MessageCircle, RotateCcw, Volume2, VolumeX, AlertCircle, CheckCircle, Clock } from 'lucide-react';
import ConversationalVoiceProcessor, { ConversationState, ConversationTurn, ConversationResponse } from '../../lib/conversational-voice-processor';

interface ConversationalVoiceInterfaceProps {
  onTransactionComplete: (data: Record<string, unknown>) => void;
  eventType?: 'receiving' | 'disposal' | 'physical_count' | 'sale';
  className?: string;
}

export default function ConversationalVoiceInterface({ 
  onTransactionComplete, 
  eventType = 'receiving',
  className = '' 
}: ConversationalVoiceInterfaceProps) {
  const [isListening, setIsListening] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentConversation, setCurrentConversation] = useState<ConversationState | null>(null);
  const [conversationHistory, setConversationHistory] = useState<ConversationTurn[]>([]);
  const [currentAgentMessage, setCurrentAgentMessage] = useState<string>('');
  const [suggestedResponses, setSuggestedResponses] = useState<string[]>([]);
  const [isEnabled, setIsEnabled] = useState(true);
  const [isSpeechEnabled, setIsSpeechEnabled] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const recognition = useRef<SpeechRecognition | null>(null);
  const speechSynthesis = useRef<SpeechSynthesis>(window.speechSynthesis);
  const processor = useRef<ConversationalVoiceProcessor>(new ConversationalVoiceProcessor());
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognitionClass = window.webkitSpeechRecognition || window.SpeechRecognition;
      recognition.current = new SpeechRecognitionClass();
      recognition.current.continuous = false;
      recognition.current.interimResults = false;
      recognition.current.lang = 'en-US';
      
      recognition.current.onstart = () => {
        setIsListening(true);
        setError(null);
      };
      
      recognition.current.onend = () => {
        setIsListening(false);
      };
      
      recognition.current.onresult = async (event) => {
        const result = event.results[0][0].transcript;
        await handleUserInput(result, 'voice');
      };
      
      recognition.current.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        setIsListening(false);
        setError('Voice recognition error. Please try again.');
      };
    } else {
      setIsEnabled(false);
    }

    return () => {
      if (recognition.current) {
        recognition.current.abort();
      }
    };
  }, []);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [conversationHistory, currentAgentMessage]);

  const handleUserInput = async (input: string, inputType: 'voice' | 'text' = 'voice') => {
    setIsProcessing(true);
    setError(null);

    try {
      let response: ConversationResponse;
      
      if (!currentConversation) {
        // Start new conversation
        response = await processor.current.startConversation(input);
      } else {
        // Continue existing conversation
        response = await processor.current.continueConversation(currentConversation.id, input, inputType);
      }

      if (response.success) {
        setCurrentConversation(response.state);
        setConversationHistory(response.state.conversationHistory);
        
        if (response.agentMessage) {
          setCurrentAgentMessage(response.agentMessage);
          
          // Speak the response if speech is enabled
          if (response.shouldSpeak && isSpeechEnabled) {
            speakMessage(response.agentMessage);
          }
        }
        
        if (response.suggestedResponses) {
          setSuggestedResponses(response.suggestedResponses);
        }

        // Check if conversation is completed
        if (response.state.stage === 'completed') {
          // Transaction completed successfully
          setTimeout(() => {
            onTransactionComplete(response.state.currentData);
            resetConversation();
          }, 2000);
        }
      } else {
        setError(response.error || 'Failed to process input');
        if (response.agentMessage) {
          setCurrentAgentMessage(response.agentMessage);
          if (response.suggestedResponses) {
            setSuggestedResponses(response.suggestedResponses);
          }
        }
      }
    } catch (error) {
      console.error('Error processing user input:', error);
      setError(error instanceof Error ? error.message : 'Unknown error occurred');
    } finally {
      setIsProcessing(false);
    }
  };

  const speakMessage = (message: string) => {
    if (!isSpeechEnabled || !speechSynthesis.current) return;
    
    // Cancel any ongoing speech
    speechSynthesis.current.cancel();
    
    const utterance = new SpeechSynthesisUtterance(message);
    utterance.rate = 0.9;
    utterance.pitch = 1;
    utterance.volume = 0.8;
    
    speechSynthesis.current.speak(utterance);
  };

  const startListening = useCallback(() => {
    if (recognition.current && isEnabled && !isListening && !isProcessing) {
      recognition.current.start();
    }
  }, [isEnabled, isListening, isProcessing]);

  const stopListening = useCallback(() => {
    if (recognition.current && isListening) {
      recognition.current.stop();
    }
  }, [isListening]);

  const handleSuggestedResponse = (response: string) => {
    handleUserInput(response, 'text');
    setSuggestedResponses([]);
  };

  const resetConversation = () => {
    if (currentConversation) {
      processor.current.resetConversation(currentConversation.id);
    }
    setCurrentConversation(null);
    setConversationHistory([]);
    setCurrentAgentMessage('');
    setSuggestedResponses([]);
    setError(null);
    speechSynthesis.current.cancel();
  };

  const toggleSpeech = () => {
    setIsSpeechEnabled(!isSpeechEnabled);
    if (!isSpeechEnabled && speechSynthesis.current) {
      speechSynthesis.current.cancel();
    }
  };

  const getConversationStageDisplay = () => {
    if (!currentConversation) return 'Ready to start';
    
    switch (currentConversation.stage) {
      case 'initial': return 'Processing initial input...';
      case 'collecting': return 'Gathering information...';
      case 'vendor_creation': return 'Setting up vendor...';
      case 'batch_confirmation': return 'Confirming batch details...';
      case 'confirming': return 'Ready to confirm...';
      case 'completed': return 'Transaction completed!';
      case 'error': return 'Error occurred';
      default: return 'In conversation...';
    }
  };

  const getStageIcon = () => {
    if (!currentConversation) return <MessageCircle className="w-4 h-4" />;
    
    switch (currentConversation.stage) {
      case 'collecting':
      case 'vendor_creation':
      case 'batch_confirmation':
        return <Clock className="w-4 h-4" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-600" />;
      default:
        return <MessageCircle className="w-4 h-4" />;
    }
  };

  const formatTurnContent = (turn: ConversationTurn) => {
    if (turn.type === 'user_voice' || turn.type === 'user_text') {
      return (
        <div className="flex justify-end mb-3">
          <div className="bg-blue-600 text-white rounded-lg px-4 py-2 max-w-xs lg:max-w-md">
            <div className="text-sm">{turn.content}</div>
            {turn.extractedData && turn.confidence && (
              <div className="text-xs text-blue-100 mt-1">
                Confidence: {(turn.confidence * 100).toFixed(0)}%
              </div>
            )}
          </div>
        </div>
      );
    } else {
      return (
        <div className="flex justify-start mb-3">
          <div className="bg-gray-100 text-gray-900 rounded-lg px-4 py-2 max-w-xs lg:max-w-md">
            <div className="text-sm">{turn.content}</div>
            <div className="text-xs text-gray-500 mt-1">
              {turn.type === 'agent_question' ? 'Question' : 
               turn.type === 'agent_confirmation' ? 'Confirmation' : 'System'}
            </div>
          </div>
        </div>
      );
    }
  };

  if (!isEnabled) {
    return (
      <div className={`bg-yellow-50 border border-yellow-200 rounded-lg p-4 ${className}`}>
        <div className="flex items-center gap-2">
          <AlertCircle className="w-5 h-5 text-yellow-600" />
          <span className="text-sm text-yellow-800">
            Voice recognition is not available in this browser.
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      {/* Header */}
      <div className="bg-blue-50 border-b border-blue-200 px-4 py-3 rounded-t-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getStageIcon()}
            <span className="text-sm font-medium text-blue-900">
              Conversational Voice Assistant
            </span>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={toggleSpeech}
              className={`p-1 rounded ${isSpeechEnabled ? 'text-blue-600' : 'text-gray-400'}`}
              title={isSpeechEnabled ? 'Disable speech' : 'Enable speech'}
            >
              {isSpeechEnabled ? <Volume2 className="w-4 h-4" /> : <VolumeX className="w-4 h-4" />}
            </button>
            <button
              onClick={resetConversation}
              className="p-1 rounded text-gray-600 hover:text-gray-800"
              title="Reset conversation"
            >
              <RotateCcw className="w-4 h-4" />
            </button>
          </div>
        </div>
        <div className="text-xs text-blue-700 mt-1">
          {getConversationStageDisplay()}
        </div>
      </div>

      {/* Conversation History */}
      <div className="h-64 overflow-y-auto p-4 bg-gray-50">
        {conversationHistory.length === 0 && !currentAgentMessage && (
          <div className="text-center text-gray-500 text-sm py-8">
            <MessageCircle className="w-8 h-8 mx-auto mb-2 text-gray-400" />
            <p>Start by saying something like:</p>
            <p className="font-medium mt-1">
              "Received 50 pounds of salmon from Pacific Seafoods"
            </p>
          </div>
        )}
        
        {conversationHistory.map((turn) => (
          <div key={turn.id}>
            {formatTurnContent(turn)}
          </div>
        ))}
        
        {currentAgentMessage && (
          <div className="flex justify-start mb-3">
            <div className="bg-green-100 text-green-900 rounded-lg px-4 py-2 max-w-xs lg:max-w-md">
              <div className="text-sm">{currentAgentMessage}</div>
              <div className="text-xs text-green-600 mt-1">Agent</div>
            </div>
          </div>
        )}
        
        {isProcessing && (
          <div className="flex justify-center mb-3">
            <div className="bg-gray-200 rounded-lg px-4 py-2">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                Processing...
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Error Display */}
      {error && (
        <div className="px-4 py-2 bg-red-50 border-t border-red-200">
          <div className="flex items-center gap-2 text-sm text-red-700">
            <AlertCircle className="w-4 h-4" />
            {error}
          </div>
        </div>
      )}

      {/* Suggested Responses */}
      {suggestedResponses.length > 0 && (
        <div className="px-4 py-3 bg-gray-50 border-t border-gray-200">
          <div className="text-xs text-gray-600 mb-2">Suggested responses:</div>
          <div className="flex flex-wrap gap-2">
            {suggestedResponses.map((response, index) => (
              <button
                key={index}
                onClick={() => handleSuggestedResponse(response)}
                className="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200 transition-colors"
                disabled={isProcessing}
              >
                {response}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Voice Controls */}
      <div className="px-4 py-3 border-t border-gray-200 rounded-b-lg">
        <div className="flex items-center justify-between">
          <button
            onClick={isListening ? stopListening : startListening}
            disabled={isProcessing}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all ${
              isListening 
                ? 'bg-red-500 hover:bg-red-600 text-white animate-pulse' 
                : 'bg-blue-500 hover:bg-blue-600 text-white'
            } ${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {isListening ? <MicOff className="w-4 h-4" /> : <Mic className="w-4 h-4" />}
            <span className="text-sm font-medium">
              {isListening ? 'Stop Listening' : isProcessing ? 'Processing...' : 'Start Speaking'}
            </span>
          </button>
          
          <div className="text-xs text-gray-500">
            {currentConversation?.missingFields.length > 0 && (
              <span>Missing: {currentConversation.missingFields.join(', ')}</span>
            )}
          </div>
        </div>
        
        {currentConversation?.currentData && (
          <div className="mt-3 p-3 bg-blue-50 rounded-lg">
            <div className="text-xs font-medium text-blue-900 mb-1">Current Data:</div>
            <div className="text-xs text-blue-700">
              {currentConversation.currentData.product_name && (
                <span className="mr-3">Product: {currentConversation.currentData.product_name}</span>
              )}
              {currentConversation.currentData.quantity && (
                <span className="mr-3">Qty: {currentConversation.currentData.quantity} {currentConversation.currentData.unit}</span>
              )}
              {currentConversation.currentData.vendor_name && (
                <span className="mr-3">Vendor: {currentConversation.currentData.vendor_name}</span>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}