import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QualityReviewPanel } from '../QualityReviewPanel';
import { voiceEventService } from '../../../services/VoiceEventService';
import { VoiceEvent } from '../../../types/schema';

// Mock the voice event service
vi.mock('../../../services/VoiceEventService', () => ({
  voiceEventService: {
    getEventsForQualityReview: vi.fn(),
    getVoiceEventStatistics: vi.fn()
  }
}));

// Mock audio API
global.Audio = vi.fn().mockImplementation(() => ({
  play: vi.fn().mockResolvedValue(undefined),
  pause: vi.fn(),
  load: vi.fn()
}));

const mockLowConfidenceEvent: VoiceEvent = {
  id: 'test-event-1',
  event_type: 'receiving',
  product_name: 'Salmon Fillet',
  quantity: 50,
  unit: 'lbs',
  voice_confidence_score: 0.65,
  voice_confidence_breakdown: {
    product_match: 0.7,
    quantity_extraction: 0.8,
    vendor_match: 0.5,
    overall: 0.65
  },
  raw_transcript: 'Received fifty pounds of salmon fillet from ocean fresh',
  audio_recording_url: 'https://example.com/audio.wav',
  occurred_at: '2024-01-15T10:30:00Z',
  created_at: '2024-01-15T10:30:00Z',
  vendor_name: 'Ocean Fresh',
  created_by_voice: true
};

const mockStats = {
  totalEvents: 10,
  highConfidenceEvents: 7,
  mediumConfidenceEvents: 2,
  lowConfidenceEvents: 1,
  averageConfidence: 0.85,
  eventsNeedingReview: 1
};

describe('QualityReviewPanel', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders loading state initially', () => {
    vi.mocked(voiceEventService.getEventsForQualityReview).mockImplementation(
      () => new Promise(() => {}) // Never resolves
    );

    render(<QualityReviewPanel />);
    
    expect(screen.getByText('Loading events for review...')).toBeInTheDocument();
  });

  it('renders empty state when no events need review', async () => {
    vi.mocked(voiceEventService.getEventsForQualityReview).mockResolvedValue([]);
    vi.mocked(voiceEventService.getVoiceEventStatistics).mockResolvedValue(mockStats);

    render(<QualityReviewPanel />);

    await waitFor(() => {
      expect(screen.getByText('All caught up!')).toBeInTheDocument();
      expect(screen.getByText('No voice events require quality review at this time.')).toBeInTheDocument();
    });
  });

  it('renders events that need review', async () => {
    vi.mocked(voiceEventService.getEventsForQualityReview).mockResolvedValue([mockLowConfidenceEvent]);
    vi.mocked(voiceEventService.getVoiceEventStatistics).mockResolvedValue(mockStats);

    render(<QualityReviewPanel />);

    await waitFor(() => {
      expect(screen.getByText('Salmon Fillet')).toBeInTheDocument();
      expect(screen.getByText('65% confidence')).toBeInTheDocument();
      expect(screen.getByText('Event: receiving')).toBeInTheDocument();
      expect(screen.getByText('Quantity: 50 lbs')).toBeInTheDocument();
    });
  });

  it('displays original transcript when available', async () => {
    vi.mocked(voiceEventService.getEventsForQualityReview).mockResolvedValue([mockLowConfidenceEvent]);
    vi.mocked(voiceEventService.getVoiceEventStatistics).mockResolvedValue(mockStats);

    render(<QualityReviewPanel />);

    await waitFor(() => {
      expect(screen.getByText('Original Transcript:')).toBeInTheDocument();
      expect(screen.getByText('"Received fifty pounds of salmon fillet from ocean fresh"')).toBeInTheDocument();
    });
  });

  it('displays confidence breakdown when available', async () => {
    vi.mocked(voiceEventService.getEventsForQualityReview).mockResolvedValue([mockLowConfidenceEvent]);
    vi.mocked(voiceEventService.getVoiceEventStatistics).mockResolvedValue(mockStats);

    render(<QualityReviewPanel />);

    await waitFor(() => {
      expect(screen.getByText('Confidence Breakdown:')).toBeInTheDocument();
      expect(screen.getByText('Product Match')).toBeInTheDocument();
      expect(screen.getByText('Quantity')).toBeInTheDocument();
      expect(screen.getByText('Vendor Match')).toBeInTheDocument();
      expect(screen.getByText('70%')).toBeInTheDocument(); // Product match
      expect(screen.getByText('80%')).toBeInTheDocument(); // Quantity extraction
      expect(screen.getByText('50%')).toBeInTheDocument(); // Vendor match
    });
  });

  it('shows analytics panel when toggled', async () => {
    vi.mocked(voiceEventService.getEventsForQualityReview).mockResolvedValue([mockLowConfidenceEvent]);
    vi.mocked(voiceEventService.getVoiceEventStatistics).mockResolvedValue(mockStats);

    render(<QualityReviewPanel />);

    await waitFor(() => {
      expect(screen.getByText('Show Analytics')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByText('Show Analytics'));

    await waitFor(() => {
      expect(screen.getByText('Review Analytics')).toBeInTheDocument();
      expect(screen.getByText('Pending Review')).toBeInTheDocument();
      expect(screen.getByText('Avg Confidence')).toBeInTheDocument();
      expect(screen.getByText('Reviewed Today')).toBeInTheDocument();
      expect(screen.getByText('Accuracy Trend')).toBeInTheDocument();
    });
  });

  it('calls onEventApprove when approve button is clicked', async () => {
    const mockOnApprove = vi.fn();
    vi.mocked(voiceEventService.getEventsForQualityReview).mockResolvedValue([mockLowConfidenceEvent]);
    vi.mocked(voiceEventService.getVoiceEventStatistics).mockResolvedValue(mockStats);

    render(<QualityReviewPanel onEventApprove={mockOnApprove} />);

    await waitFor(() => {
      expect(screen.getByTitle('Approve event')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByTitle('Approve event'));

    await waitFor(() => {
      expect(mockOnApprove).toHaveBeenCalledWith('test-event-1');
    });
  });

  it('calls onEventReject when reject button is clicked', async () => {
    const mockOnReject = vi.fn();
    vi.mocked(voiceEventService.getEventsForQualityReview).mockResolvedValue([mockLowConfidenceEvent]);
    vi.mocked(voiceEventService.getVoiceEventStatistics).mockResolvedValue(mockStats);

    // Mock window.prompt
    const mockPrompt = vi.fn().mockReturnValue('Incorrect product identification');
    global.prompt = mockPrompt;

    render(<QualityReviewPanel onEventReject={mockOnReject} />);

    await waitFor(() => {
      expect(screen.getByTitle('Reject event')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByTitle('Reject event'));

    await waitFor(() => {
      expect(mockPrompt).toHaveBeenCalledWith('Please provide a reason for rejection:');
      expect(mockOnReject).toHaveBeenCalledWith('test-event-1', 'Incorrect product identification');
    });
  });

  it('calls onEventEdit when edit button is clicked', async () => {
    const mockOnEdit = vi.fn();
    vi.mocked(voiceEventService.getEventsForQualityReview).mockResolvedValue([mockLowConfidenceEvent]);
    vi.mocked(voiceEventService.getVoiceEventStatistics).mockResolvedValue(mockStats);

    render(<QualityReviewPanel onEventEdit={mockOnEdit} />);

    await waitFor(() => {
      expect(screen.getByTitle('Edit event')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByTitle('Edit event'));

    await waitFor(() => {
      expect(mockOnEdit).toHaveBeenCalledWith(mockLowConfidenceEvent);
    });
  });

  it('plays audio when audio button is clicked', async () => {
    vi.mocked(voiceEventService.getEventsForQualityReview).mockResolvedValue([mockLowConfidenceEvent]);
    vi.mocked(voiceEventService.getVoiceEventStatistics).mockResolvedValue(mockStats);

    render(<QualityReviewPanel />);

    await waitFor(() => {
      expect(screen.getByTitle('Play audio recording')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByTitle('Play audio recording'));

    await waitFor(() => {
      expect(global.Audio).toHaveBeenCalledWith('https://example.com/audio.wav');
    });
  });

  it('handles service errors gracefully', async () => {
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    vi.mocked(voiceEventService.getEventsForQualityReview).mockRejectedValue(
      new Error('Service unavailable')
    );

    render(<QualityReviewPanel />);

    await waitFor(() => {
      expect(screen.getByText('Failed to load events for review. Please try again.')).toBeInTheDocument();
    });

    expect(consoleErrorSpy).toHaveBeenCalledWith(
      'Error loading pending events:',
      expect.any(Error)
    );

    consoleErrorSpy.mockRestore();
  });

  it('refreshes data when refresh button is clicked', async () => {
    vi.mocked(voiceEventService.getEventsForQualityReview).mockResolvedValue([mockLowConfidenceEvent]);
    vi.mocked(voiceEventService.getVoiceEventStatistics).mockResolvedValue(mockStats);

    render(<QualityReviewPanel />);

    await waitFor(() => {
      expect(screen.getByText('Refresh')).toBeInTheDocument();
    });

    // Clear previous calls
    vi.clearAllMocks();

    fireEvent.click(screen.getByText('Refresh'));

    await waitFor(() => {
      expect(voiceEventService.getEventsForQualityReview).toHaveBeenCalledWith(0.7);
      expect(voiceEventService.getVoiceEventStatistics).toHaveBeenCalled();
    });
  });

  it('uses custom confidence threshold', async () => {
    vi.mocked(voiceEventService.getEventsForQualityReview).mockResolvedValue([]);
    vi.mocked(voiceEventService.getVoiceEventStatistics).mockResolvedValue(mockStats);

    render(<QualityReviewPanel confidenceThreshold={0.8} />);

    await waitFor(() => {
      expect(voiceEventService.getEventsForQualityReview).toHaveBeenCalledWith(0.8);
    });

    expect(screen.getByText('Review voice events with confidence below 80%')).toBeInTheDocument();
  });

  it('removes event from list after approval', async () => {
    vi.mocked(voiceEventService.getEventsForQualityReview).mockResolvedValue([mockLowConfidenceEvent]);
    vi.mocked(voiceEventService.getVoiceEventStatistics).mockResolvedValue(mockStats);

    render(<QualityReviewPanel />);

    await waitFor(() => {
      expect(screen.getByText('Salmon Fillet')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByTitle('Approve event'));

    await waitFor(() => {
      expect(screen.queryByText('Salmon Fillet')).not.toBeInTheDocument();
      expect(screen.getByText('All caught up!')).toBeInTheDocument();
    });
  });

  it('removes event from list after rejection', async () => {
    vi.mocked(voiceEventService.getEventsForQualityReview).mockResolvedValue([mockLowConfidenceEvent]);
    vi.mocked(voiceEventService.getVoiceEventStatistics).mockResolvedValue(mockStats);

    global.prompt = vi.fn().mockReturnValue('Test rejection reason');

    render(<QualityReviewPanel />);

    await waitFor(() => {
      expect(screen.getByText('Salmon Fillet')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByTitle('Reject event'));

    await waitFor(() => {
      expect(screen.queryByText('Salmon Fillet')).not.toBeInTheDocument();
      expect(screen.getByText('All caught up!')).toBeInTheDocument();
    });
  });

  it('handles audio playback errors gracefully', async () => {
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    const mockAudio = {
      play: vi.fn().mockRejectedValue(new Error('Audio playback failed'))
    };
    global.Audio = vi.fn().mockImplementation(() => mockAudio);

    vi.mocked(voiceEventService.getEventsForQualityReview).mockResolvedValue([mockLowConfidenceEvent]);
    vi.mocked(voiceEventService.getVoiceEventStatistics).mockResolvedValue(mockStats);

    render(<QualityReviewPanel />);

    await waitFor(() => {
      expect(screen.getByTitle('Play audio recording')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByTitle('Play audio recording'));

    await waitFor(() => {
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'Error playing audio:',
        expect.any(Error)
      );
    });

    consoleErrorSpy.mockRestore();
  });
});