import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { AudioPlayback } from '../AudioPlayback';
import { audioStorageService } from '../../../services/AudioStorageService';

// Mock AudioStorageService
vi.mock('../../../services/AudioStorageService', () => ({
  audioStorageService: {
    getAudioRecordingUrl: vi.fn()
  }
}));

// Mock HTML Audio API
const mockAudio = {
  play: vi.fn().mockResolvedValue(undefined),
  pause: vi.fn(),
  load: vi.fn(),
  currentTime: 0,
  duration: 0,
  volume: 1,
  muted: false,
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  dispatchEvent: vi.fn()
};

global.HTMLAudioElement = vi.fn().mockImplementation(() => mockAudio);

// Mock URL.createObjectURL and revokeObjectURL
global.URL.createObjectURL = vi.fn().mockReturnValue('blob:mock-url');
global.URL.revokeObjectURL = vi.fn();

// Mock fetch for download functionality
global.fetch = vi.fn();

describe('AudioPlayback', () => {
  let mockAudioStorageService: any;

  beforeEach(() => {
    mockAudioStorageService = audioStorageService as any;
    vi.clearAllMocks();
    
    // Reset mock audio element
    mockAudio.currentTime = 0;
    mockAudio.duration = 0;
    mockAudio.volume = 1;
    mockAudio.muted = false;
  });

  describe('rendering', () => {
    it('should render nothing when no audio URL provided', () => {
      const { container } = render(<AudioPlayback />);
      expect(container.firstChild).toBeNull();
    });

    it('should render loading state initially', async () => {
      mockAudioStorageService.getAudioRecordingUrl.mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve('signed-url'), 100))
      );

      render(<AudioPlayback audioUrl="test-audio-path" />);
      
      expect(screen.getByText('Loading audio...')).toBeInTheDocument();
    });

    it('should render audio controls after loading', async () => {
      mockAudioStorageService.getAudioRecordingUrl.mockResolvedValue('https://example.com/audio.webm');

      render(<AudioPlayback audioUrl="test-audio-path" title="Test Recording" />);
      
      await waitFor(() => {
        expect(screen.getByText('Test Recording')).toBeInTheDocument();
        expect(screen.getByRole('button')).toBeInTheDocument(); // Play button
      });
    });

    it('should render error state when audio fails to load', async () => {
      mockAudioStorageService.getAudioRecordingUrl.mockResolvedValue(null);

      render(<AudioPlayback audioUrl="test-audio-path" />);
      
      await waitFor(() => {
        expect(screen.getByText('Failed to load audio recording')).toBeInTheDocument();
      });
    });

    it('should use blob URL directly without fetching signed URL', async () => {
      const blobUrl = 'blob:mock-audio-url';
      
      render(<AudioPlayback audioUrl={blobUrl} />);
      
      await waitFor(() => {
        expect(screen.getByText('Voice Recording')).toBeInTheDocument();
      });

      expect(mockAudioStorageService.getAudioRecordingUrl).not.toHaveBeenCalled();
    });
  });

  describe('playback controls', () => {
    beforeEach(async () => {
      mockAudioStorageService.getAudioRecordingUrl.mockResolvedValue('https://example.com/audio.webm');
    });

    it('should toggle play/pause when button is clicked', async () => {
      render(<AudioPlayback audioUrl="test-audio-path" />);
      
      await waitFor(() => {
        expect(screen.getByRole('button')).toBeInTheDocument();
      });

      const playButton = screen.getByRole('button');
      
      // Click to play
      fireEvent.click(playButton);
      expect(mockAudio.play).toHaveBeenCalled();

      // Simulate playing state
      fireEvent.load(screen.getByRole('audio', { hidden: true }));
      
      // Click to pause
      fireEvent.click(playButton);
      expect(mockAudio.pause).toHaveBeenCalled();
    });

    it('should handle audio metadata loading', async () => {
      render(<AudioPlayback audioUrl="test-audio-path" showControls={true} />);
      
      await waitFor(() => {
        expect(screen.getByRole('button')).toBeInTheDocument();
      });

      const audioElement = screen.getByRole('audio', { hidden: true });
      
      // Simulate metadata loaded
      Object.defineProperty(mockAudio, 'duration', { value: 120, writable: true });
      fireEvent.loadedMetadata(audioElement);

      await waitFor(() => {
        expect(screen.getByText('2:00')).toBeInTheDocument(); // Duration display
      });
    });

    it('should update progress during playback', async () => {
      render(<AudioPlayback audioUrl="test-audio-path" showControls={true} />);
      
      await waitFor(() => {
        expect(screen.getByRole('button')).toBeInTheDocument();
      });

      const audioElement = screen.getByRole('audio', { hidden: true });
      
      // Set duration first
      Object.defineProperty(mockAudio, 'duration', { value: 120, writable: true });
      fireEvent.loadedMetadata(audioElement);

      // Simulate time update
      Object.defineProperty(mockAudio, 'currentTime', { value: 30, writable: true });
      fireEvent.timeUpdate(audioElement);

      await waitFor(() => {
        expect(screen.getByText('0:30')).toBeInTheDocument(); // Current time display
      });
    });

    it('should handle seek when progress bar is changed', async () => {
      render(<AudioPlayback audioUrl="test-audio-path" showControls={true} />);
      
      await waitFor(() => {
        expect(screen.getByRole('button')).toBeInTheDocument();
      });

      const audioElement = screen.getByRole('audio', { hidden: true });
      
      // Set duration first
      Object.defineProperty(mockAudio, 'duration', { value: 120, writable: true });
      fireEvent.loadedMetadata(audioElement);

      await waitFor(() => {
        const progressBar = screen.getByRole('slider');
        fireEvent.change(progressBar, { target: { value: '60' } });
        
        expect(mockAudio.currentTime).toBe(60);
      });
    });

    it('should handle volume control', async () => {
      render(<AudioPlayback audioUrl="test-audio-path" showControls={true} />);
      
      await waitFor(() => {
        expect(screen.getByRole('button')).toBeInTheDocument();
      });

      const audioElement = screen.getByRole('audio', { hidden: true });
      fireEvent.loadedMetadata(audioElement);

      await waitFor(() => {
        const volumeSliders = screen.getAllByRole('slider');
        const volumeSlider = volumeSliders.find(slider => 
          slider.getAttribute('title') === 'Volume'
        );
        
        if (volumeSlider) {
          fireEvent.change(volumeSlider, { target: { value: '0.5' } });
          expect(mockAudio.volume).toBe(0.5);
        }
      });
    });

    it('should toggle mute when mute button is clicked', async () => {
      render(<AudioPlayback audioUrl="test-audio-path" showControls={true} />);
      
      await waitFor(() => {
        expect(screen.getByRole('button')).toBeInTheDocument();
      });

      const audioElement = screen.getByRole('audio', { hidden: true });
      fireEvent.loadedMetadata(audioElement);

      await waitFor(() => {
        const muteButton = screen.getByTitle('Mute');
        fireEvent.click(muteButton);
        
        expect(mockAudio.muted).toBe(true);
      });
    });

    it('should restart audio when restart button is clicked', async () => {
      render(<AudioPlayback audioUrl="test-audio-path" showControls={true} />);
      
      await waitFor(() => {
        expect(screen.getByRole('button')).toBeInTheDocument();
      });

      const audioElement = screen.getByRole('audio', { hidden: true });
      
      // Set some current time
      Object.defineProperty(mockAudio, 'currentTime', { value: 30, writable: true });
      Object.defineProperty(mockAudio, 'duration', { value: 120, writable: true });
      fireEvent.loadedMetadata(audioElement);

      await waitFor(() => {
        const restartButton = screen.getByTitle('Restart');
        fireEvent.click(restartButton);
        
        expect(mockAudio.currentTime).toBe(0);
      });
    });
  });

  describe('download functionality', () => {
    beforeEach(() => {
      mockAudioStorageService.getAudioRecordingUrl.mockResolvedValue('https://example.com/audio.webm');
      
      // Mock fetch for download
      (global.fetch as Mock).mockResolvedValue({
        blob: () => Promise.resolve(new Blob(['audio data']))
      });

      // Mock DOM methods for download
      const mockLink = {
        href: '',
        download: '',
        click: vi.fn()
      };
      
      vi.spyOn(document, 'createElement').mockReturnValue(mockLink as any);
      vi.spyOn(document.body, 'appendChild').mockImplementation(() => mockLink as any);
      vi.spyOn(document.body, 'removeChild').mockImplementation(() => mockLink as any);
    });

    it('should download audio when download button is clicked', async () => {
      render(<AudioPlayback audioUrl="test-audio-path" eventId="test-event" showDownload={true} />);
      
      await waitFor(() => {
        expect(screen.getByTitle('Download audio')).toBeInTheDocument();
      });

      const downloadButton = screen.getByTitle('Download audio');
      fireEvent.click(downloadButton);

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('https://example.com/audio.webm');
      });
    });

    it('should not show download button when showDownload is false', async () => {
      render(<AudioPlayback audioUrl="test-audio-path" showDownload={false} />);
      
      await waitFor(() => {
        expect(screen.getByRole('button')).toBeInTheDocument();
      });

      expect(screen.queryByTitle('Download audio')).not.toBeInTheDocument();
    });
  });

  describe('error handling', () => {
    it('should handle audio playback errors', async () => {
      mockAudioStorageService.getAudioRecordingUrl.mockResolvedValue('https://example.com/audio.webm');
      mockAudio.play.mockRejectedValue(new Error('Playback failed'));

      render(<AudioPlayback audioUrl="test-audio-path" />);
      
      await waitFor(() => {
        expect(screen.getByRole('button')).toBeInTheDocument();
      });

      const playButton = screen.getByRole('button');
      fireEvent.click(playButton);

      await waitFor(() => {
        expect(screen.getByText('Failed to play audio')).toBeInTheDocument();
      });
    });

    it('should handle audio element errors', async () => {
      mockAudioStorageService.getAudioRecordingUrl.mockResolvedValue('https://example.com/audio.webm');

      render(<AudioPlayback audioUrl="test-audio-path" />);
      
      await waitFor(() => {
        expect(screen.getByRole('button')).toBeInTheDocument();
      });

      const audioElement = screen.getByRole('audio', { hidden: true });
      fireEvent.error(audioElement);

      await waitFor(() => {
        expect(screen.getByText('Failed to play audio recording')).toBeInTheDocument();
      });
    });
  });

  describe('auto-play functionality', () => {
    it('should auto-play when autoPlay prop is true', async () => {
      mockAudioStorageService.getAudioRecordingUrl.mockResolvedValue('https://example.com/audio.webm');

      render(<AudioPlayback audioUrl="test-audio-path" autoPlay={true} />);
      
      await waitFor(() => {
        expect(mockAudio.play).toHaveBeenCalled();
      });
    });

    it('should not auto-play when autoPlay prop is false', async () => {
      mockAudioStorageService.getAudioRecordingUrl.mockResolvedValue('https://example.com/audio.webm');

      render(<AudioPlayback audioUrl="test-audio-path" autoPlay={false} />);
      
      await waitFor(() => {
        expect(screen.getByRole('button')).toBeInTheDocument();
      });

      expect(mockAudio.play).not.toHaveBeenCalled();
    });
  });

  describe('simplified controls', () => {
    it('should show simplified controls when showControls is false', async () => {
      mockAudioStorageService.getAudioRecordingUrl.mockResolvedValue('https://example.com/audio.webm');

      render(<AudioPlayback audioUrl="test-audio-path" showControls={false} />);
      
      await waitFor(() => {
        expect(screen.getByRole('button')).toBeInTheDocument();
      });

      // Should not show volume controls or progress bar
      expect(screen.queryByTitle('Volume')).not.toBeInTheDocument();
      expect(screen.queryByRole('slider')).not.toBeInTheDocument();
    });

    it('should show playing animation when audio is playing with simple controls', async () => {
      mockAudioStorageService.getAudioRecordingUrl.mockResolvedValue('https://example.com/audio.webm');

      render(<AudioPlayback audioUrl="test-audio-path" showControls={false} />);
      
      await waitFor(() => {
        expect(screen.getByRole('button')).toBeInTheDocument();
      });

      const audioElement = screen.getByRole('audio', { hidden: true });
      
      // Set duration and simulate playing
      Object.defineProperty(mockAudio, 'duration', { value: 120, writable: true });
      fireEvent.loadedMetadata(audioElement);

      // Click play button
      const playButton = screen.getByRole('button');
      fireEvent.click(playButton);

      // Should show time display
      await waitFor(() => {
        expect(screen.getByText('0:00 / 2:00')).toBeInTheDocument();
      });
    });
  });
});