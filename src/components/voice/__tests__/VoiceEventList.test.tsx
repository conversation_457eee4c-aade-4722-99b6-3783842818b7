import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { VoiceEventList } from '../VoiceEventList';
import { voiceEventService } from '../../../services/VoiceEventService';
import { supabase } from '../../../lib/supabase';
import { VoiceEvent } from '../../../types/schema';

// Mock the VoiceEventService
vi.mock('../../../services/VoiceEventService', () => ({
  voiceEventService: {
    getVoiceEvents: vi.fn()
  }
}));

// Mock Supabase
vi.mock('../../../lib/supabase', () => ({
  supabase: {
    channel: vi.fn(() => ({
      on: vi.fn(() => ({
        subscribe: vi.fn()
      }))
    })),
    removeChannel: vi.fn()
  }
}));

// Mock Audio API
global.Audio = vi.fn().mockImplementation(() => ({
  play: vi.fn().mockResolvedValue(undefined),
  pause: vi.fn(),
  currentTime: 0,
  duration: 0
}));

describe('VoiceEventList', () => {
  const mockEvents: VoiceEvent[] = [
    {
      id: '1',
      event_type: 'receiving',
      product_name: 'Salmon Fillet',
      quantity: 25,
      unit: 'lbs',
      vendor_name: 'Pacific Seafoods',
      voice_confidence_score: 0.9,
      voice_confidence_breakdown: {
        product_match: 0.95,
        quantity_extraction: 0.85,
        vendor_match: 0.9,
        overall: 0.9
      },
      raw_transcript: 'Received 25 pounds of salmon fillet from Pacific Seafoods',
      audio_recording_url: 'blob:mock-audio-url',
      created_by_voice: true,
      event_date: '2025-08-15',
      occurred_at: '2025-08-15T10:00:00Z',
      created_at: '2025-08-15T10:00:00Z'
    },
    {
      id: '2',
      event_type: 'sale',
      product_name: 'Crab Legs',
      quantity: 10,
      unit: 'lbs',
      customer_name: 'Restaurant ABC',
      voice_confidence_score: 0.6,
      voice_confidence_breakdown: {
        product_match: 0.7,
        quantity_extraction: 0.5,
        vendor_match: 0.6,
        overall: 0.6
      },
      raw_transcript: 'Sold 10 pounds of crab legs to Restaurant ABC',
      created_by_voice: true,
      event_date: '2025-08-15',
      occurred_at: '2025-08-15T14:00:00Z',
      created_at: '2025-08-15T14:00:00Z',
      condition: 'Good',
      temperature: 38,
      temperature_unit: 'fahrenheit',
      notes: 'Customer requested extra ice'
    }
  ];

  let mockVoiceEventService: any;
  let mockSupabase: any;

  beforeEach(() => {
    mockVoiceEventService = voiceEventService as any;
    mockSupabase = supabase as any;
    vi.clearAllMocks();
    
    // Default mock implementation
    mockVoiceEventService.getVoiceEvents.mockResolvedValue(mockEvents);
    
    // Mock Supabase channel
    const mockChannel = {
      on: vi.fn().mockReturnThis(),
      subscribe: vi.fn()
    };
    mockSupabase.channel.mockReturnValue(mockChannel);
  });

  describe('rendering', () => {
    it('should render loading state initially', async () => {
      mockVoiceEventService.getVoiceEvents.mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve(mockEvents), 100))
      );

      render(<VoiceEventList />);
      
      expect(screen.getByText('Loading voice events...')).toBeInTheDocument();
      
      await waitFor(() => {
        expect(screen.queryByText('Loading voice events...')).not.toBeInTheDocument();
      });
    });

    it('should render voice events list', async () => {
      render(<VoiceEventList />);
      
      await waitFor(() => {
        expect(screen.getByText('Voice Events')).toBeInTheDocument();
        expect(screen.getByText('Salmon Fillet')).toBeInTheDocument();
        expect(screen.getByText('Crab Legs')).toBeInTheDocument();
      });
    });

    it('should display confidence badges correctly', async () => {
      render(<VoiceEventList />);
      
      await waitFor(() => {
        expect(screen.getByText('90% high')).toBeInTheDocument();
        expect(screen.getByText('60% low')).toBeInTheDocument();
      });
    });

    it('should display event details correctly', async () => {
      render(<VoiceEventList />);
      
      await waitFor(() => {
        expect(screen.getByText('25 lbs')).toBeInTheDocument();
        expect(screen.getByText('From: Pacific Seafoods')).toBeInTheDocument();
        expect(screen.getByText('To: Restaurant ABC')).toBeInTheDocument();
        expect(screen.getByText('Condition: Good')).toBeInTheDocument();
        expect(screen.getByText('Temperature: 38°F')).toBeInTheDocument();
        expect(screen.getByText('Notes: Customer requested extra ice')).toBeInTheDocument();
      });
    });

    it('should show empty state when no events', async () => {
      mockVoiceEventService.getVoiceEvents.mockResolvedValue([]);
      
      render(<VoiceEventList />);
      
      await waitFor(() => {
        expect(screen.getByText('No voice events found')).toBeInTheDocument();
        expect(screen.getByText('Start by creating voice events through the voice interface.')).toBeInTheDocument();
      });
    });

    it('should show error state on failure', async () => {
      mockVoiceEventService.getVoiceEvents.mockRejectedValue(new Error('Network error'));
      
      render(<VoiceEventList />);
      
      await waitFor(() => {
        expect(screen.getByText('Failed to load voice events. Please try again.')).toBeInTheDocument();
        expect(screen.getByText('Try again')).toBeInTheDocument();
      });
    });
  });

  describe('filtering', () => {
    it('should render filters when showFilters is true', async () => {
      render(<VoiceEventList showFilters={true} />);
      
      await waitFor(() => {
        expect(screen.getByText('Filters')).toBeInTheDocument();
        expect(screen.getByPlaceholderText('Search products, vendors...')).toBeInTheDocument();
        expect(screen.getByDisplayValue('All Types')).toBeInTheDocument();
        expect(screen.getByDisplayValue('All Confidence')).toBeInTheDocument();
      });
    });

    it('should not render filters when showFilters is false', async () => {
      render(<VoiceEventList showFilters={false} />);
      
      await waitFor(() => {
        expect(screen.queryByText('Filters')).not.toBeInTheDocument();
      });
    });

    it('should filter by search query', async () => {
      render(<VoiceEventList />);
      
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Search products, vendors...')).toBeInTheDocument();
      });

      const searchInput = screen.getByPlaceholderText('Search products, vendors...');
      fireEvent.change(searchInput, { target: { value: 'salmon' } });
      fireEvent.blur(searchInput);

      await waitFor(() => {
        expect(mockVoiceEventService.getVoiceEvents).toHaveBeenCalledWith(
          expect.objectContaining({
            searchQuery: 'salmon'
          })
        );
      });
    });

    it('should filter by event type', async () => {
      render(<VoiceEventList />);
      
      await waitFor(() => {
        expect(screen.getByDisplayValue('All Types')).toBeInTheDocument();
      });

      const eventTypeSelect = screen.getByDisplayValue('All Types');
      fireEvent.change(eventTypeSelect, { target: { value: 'receiving' } });

      await waitFor(() => {
        expect(mockVoiceEventService.getVoiceEvents).toHaveBeenCalledWith(
          expect.objectContaining({
            eventType: ['receiving']
          })
        );
      });
    });

    it('should filter by confidence threshold', async () => {
      render(<VoiceEventList />);
      
      await waitFor(() => {
        expect(screen.getByDisplayValue('All Confidence')).toBeInTheDocument();
      });

      const confidenceSelect = screen.getByDisplayValue('All Confidence');
      fireEvent.change(confidenceSelect, { target: { value: '0.7' } });

      await waitFor(() => {
        expect(mockVoiceEventService.getVoiceEvents).toHaveBeenCalledWith(
          expect.objectContaining({
            confidenceThreshold: 0.7
          })
        );
      });
    });
  });

  describe('interactions', () => {
    it('should call onEventEdit when edit button is clicked', async () => {
      const mockOnEventEdit = vi.fn();
      render(<VoiceEventList onEventEdit={mockOnEventEdit} />);
      
      await waitFor(() => {
        expect(screen.getAllByTitle('Edit event')).toHaveLength(2);
      });

      const editButtons = screen.getAllByTitle('Edit event');
      fireEvent.click(editButtons[0]);

      expect(mockOnEventEdit).toHaveBeenCalledWith(mockEvents[0]);
    });

    it('should call onEventDelete when delete button is clicked', async () => {
      const mockOnEventDelete = vi.fn();
      render(<VoiceEventList onEventDelete={mockOnEventDelete} />);
      
      await waitFor(() => {
        expect(screen.getAllByTitle('Delete event')).toHaveLength(2);
      });

      const deleteButtons = screen.getAllByTitle('Delete event');
      fireEvent.click(deleteButtons[0]);

      expect(mockOnEventDelete).toHaveBeenCalledWith('1');
    });

    it('should play audio when audio button is clicked', async () => {
      render(<VoiceEventList />);
      
      await waitFor(() => {
        expect(screen.getByTitle('Play audio recording')).toBeInTheDocument();
      });

      const audioButton = screen.getByTitle('Play audio recording');
      fireEvent.click(audioButton);

      expect(global.Audio).toHaveBeenCalledWith('blob:mock-audio-url');
    });

    it('should refresh events when refresh button is clicked', async () => {
      render(<VoiceEventList />);
      
      await waitFor(() => {
        expect(screen.getByText('Refresh')).toBeInTheDocument();
      });

      // Clear previous calls
      mockVoiceEventService.getVoiceEvents.mockClear();

      const refreshButton = screen.getByText('Refresh');
      fireEvent.click(refreshButton);

      expect(mockVoiceEventService.getVoiceEvents).toHaveBeenCalled();
    });

    it('should retry loading on error', async () => {
      mockVoiceEventService.getVoiceEvents.mockRejectedValueOnce(new Error('Network error'));
      
      render(<VoiceEventList />);
      
      await waitFor(() => {
        expect(screen.getByText('Try again')).toBeInTheDocument();
      });

      // Mock successful retry
      mockVoiceEventService.getVoiceEvents.mockResolvedValueOnce(mockEvents);

      const retryButton = screen.getByText('Try again');
      fireEvent.click(retryButton);

      await waitFor(() => {
        expect(screen.getByText('Salmon Fillet')).toBeInTheDocument();
      });
    });
  });

  describe('real-time updates', () => {
    it('should set up Supabase subscription on mount', async () => {
      render(<VoiceEventList />);
      
      expect(mockSupabase.channel).toHaveBeenCalledWith('voice-events-changes');
    });

    it('should clean up subscription on unmount', async () => {
      const { unmount } = render(<VoiceEventList />);
      
      unmount();
      
      expect(mockSupabase.removeChannel).toHaveBeenCalled();
    });
  });

  describe('transcript display', () => {
    it('should show transcript in details', async () => {
      render(<VoiceEventList />);
      
      await waitFor(() => {
        expect(screen.getByText('View original transcript')).toBeInTheDocument();
      });

      const transcriptToggle = screen.getByText('View original transcript');
      fireEvent.click(transcriptToggle);

      expect(screen.getByText('"Received 25 pounds of salmon fillet from Pacific Seafoods"')).toBeInTheDocument();
    });
  });

  describe('maxEvents prop', () => {
    it('should limit events to maxEvents', async () => {
      const manyEvents = Array.from({ length: 10 }, (_, i) => ({
        ...mockEvents[0],
        id: `event-${i}`,
        product_name: `Product ${i}`
      }));
      
      mockVoiceEventService.getVoiceEvents.mockResolvedValue(manyEvents);
      
      render(<VoiceEventList maxEvents={5} />);
      
      await waitFor(() => {
        expect(mockVoiceEventService.getVoiceEvents).toHaveBeenCalled();
      });

      // Should only display first 5 events
      await waitFor(() => {
        expect(screen.getByText('Product 0')).toBeInTheDocument();
        expect(screen.getByText('Product 4')).toBeInTheDocument();
        expect(screen.queryByText('Product 5')).not.toBeInTheDocument();
      });
    });
  });
});