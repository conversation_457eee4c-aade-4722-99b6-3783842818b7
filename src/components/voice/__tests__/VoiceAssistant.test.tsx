/**
 * Comprehensive test suite for VoiceAssistant component
 * Tests voice processing, error handling, and user interactions
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { VoiceAssistant } from '../VoiceAssistant';
import { createMockSupabaseClient } from '../../../test/mocks/supabase-mocks';
import { MockOpenAIClient, generateVoiceTestScenarios } from '../../../test/mocks/openai-mocks';
import { MockSpeechRecognition, MockMediaRecorder } from '../../../test/mocks/voice-mocks';

// Mock Web APIs
global.SpeechRecognition = MockSpeechRecognition as any;
global.webkitSpeechRecognition = MockSpeechRecognition as any;
global.MediaRecorder = MockMediaRecorder as any;
global.navigator.mediaDevices = {
  getUserMedia: vi.fn().mockResolvedValue(new MediaStream())
} as any;

// Mock OpenAI
vi.mock('openai', () => ({
  default: vi.fn().mockImplementation(() => new MockOpenAIClient())
}));

// Mock Supabase
const mockSupabase = createMockSupabaseClient();
vi.mock('../../../lib/supabase', () => ({
  supabase: mockSupabase
}));

describe('VoiceAssistant', () => {
  let mockSpeechRecognition: MockSpeechRecognition;
  let mockOpenAI: MockOpenAIClient;

  beforeEach(() => {
    mockSpeechRecognition = new MockSpeechRecognition();
    mockOpenAI = new MockOpenAIClient();
    
    // Reset all mocks
    vi.clearAllMocks();
    mockSupabase.reset();
    mockOpenAI.reset();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Initial State', () => {
    it('renders with correct initial state', () => {
      render(<VoiceAssistant />);
      
      expect(screen.getByRole('button', { name: /start voice input/i })).toBeInTheDocument();
      expect(screen.queryByText(/listening/i)).not.toBeInTheDocument();
      expect(screen.queryByText(/processing/i)).not.toBeInTheDocument();
    });

    it('displays microphone permission prompt when needed', async () => {
      // Mock permission denied
      global.navigator.mediaDevices.getUserMedia = vi.fn().mockRejectedValue(
        new DOMException('Permission denied', 'NotAllowedError')
      );

      const user = userEvent.setup();
      render(<VoiceAssistant />);
      
      await user.click(screen.getByRole('button', { name: /start voice input/i }));
      
      await waitFor(() => {
        expect(screen.getByText(/microphone permission required/i)).toBeInTheDocument();
      });
    });
  });

  describe('Voice Recording', () => {
    it('starts and stops recording correctly', async () => {
      const user = userEvent.setup();
      render(<VoiceAssistant />);
      
      const startButton = screen.getByRole('button', { name: /start voice input/i });
      await user.click(startButton);
      
      await waitFor(() => {
        expect(screen.getByText(/listening/i)).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /stop recording/i })).toBeInTheDocument();
      });

      const stopButton = screen.getByRole('button', { name: /stop recording/i });
      await user.click(stopButton);
      
      await waitFor(() => {
        expect(screen.getByText(/processing/i)).toBeInTheDocument();
      });
    });

    it('handles voice recognition results', async () => {
      const user = userEvent.setup();
      const testTranscript = 'Received 25 pounds of salmon from Ocean Fresh Seafoods';
      
      mockSpeechRecognition.setRecognitionResult(testTranscript);
      
      render(<VoiceAssistant />);
      
      await user.click(screen.getByRole('button', { name: /start voice input/i }));
      
      await waitFor(() => {
        expect(screen.getByText(testTranscript)).toBeInTheDocument();
      });
    });

    it('handles voice recognition errors gracefully', async () => {
      const user = userEvent.setup();
      
      mockSpeechRecognition.setShouldFail(true);
      
      render(<VoiceAssistant />);
      
      await user.click(screen.getByRole('button', { name: /start voice input/i }));
      
      await waitFor(() => {
        expect(screen.getByText(/voice recognition error/i)).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /try again/i })).toBeInTheDocument();
      });
    });
  });

  describe('OpenAI Processing', () => {
    it('processes transcripts into structured commands', async () => {
      const user = userEvent.setup();
      const scenarios = generateVoiceTestScenarios();
      const scenario = scenarios[0]; // High confidence salmon receiving
      
      mockSpeechRecognition.setRecognitionResult(scenario.expectedTranscript);
      
      render(<VoiceAssistant />);
      
      await user.click(screen.getByRole('button', { name: /start voice input/i }));
      
      await waitFor(() => {
        expect(screen.getByText(/salmon/i)).toBeInTheDocument();
        expect(screen.getByText(/25/)).toBeInTheDocument();
        expect(screen.getByText(/ocean fresh seafoods/i)).toBeInTheDocument();
      });
    });

    it('handles high confidence commands automatically', async () => {
      const user = userEvent.setup();
      
      mockSpeechRecognition.setRecognitionResult('Received 30 pounds of cod from Pacific Catch');
      mockOpenAI.setResponseQuality('high');
      
      render(<VoiceAssistant />);
      
      await user.click(screen.getByRole('button', { name: /start voice input/i }));
      
      await waitFor(() => {
        expect(screen.getByText(/event created successfully/i)).toBeInTheDocument();
      });
    });

    it('prompts for confirmation on medium confidence commands', async () => {
      const user = userEvent.setup();
      
      mockSpeechRecognition.setRecognitionResult('Got some halibut from Pacific Catch');
      mockOpenAI.setResponseQuality('medium');
      
      render(<VoiceAssistant />);
      
      await user.click(screen.getByRole('button', { name: /start voice input/i }));
      
      await waitFor(() => {
        expect(screen.getByText(/please confirm/i)).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /confirm/i })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /edit/i })).toBeInTheDocument();
      });
    });

    it('requires manual review for low confidence commands', async () => {
      const user = userEvent.setup();
      
      mockSpeechRecognition.setRecognitionResult('Something about... tuna... from...');
      mockOpenAI.setResponseQuality('low');
      
      render(<VoiceAssistant />);
      
      await user.click(screen.getByRole('button', { name: /start voice input/i }));
      
      await waitFor(() => {
        expect(screen.getByText(/needs manual review/i)).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /review and edit/i })).toBeInTheDocument();
      });
    });

    it('handles OpenAI API errors gracefully', async () => {
      const user = userEvent.setup();
      
      mockSpeechRecognition.setRecognitionResult('Received 25 pounds of salmon');
      mockOpenAI.setShouldFail(true, 'rate_limit');
      
      render(<VoiceAssistant />);
      
      await user.click(screen.getByRole('button', { name: /start voice input/i }));
      
      await waitFor(() => {
        expect(screen.getByText(/rate limit exceeded/i)).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /retry/i })).toBeInTheDocument();
      });
    });
  });

  describe('Supabase Integration', () => {
    it('creates inventory events in database', async () => {
      const user = userEvent.setup();
      
      mockSpeechRecognition.setRecognitionResult('Received 25 pounds of salmon from Ocean Fresh Seafoods');
      
      render(<VoiceAssistant />);
      
      await user.click(screen.getByRole('button', { name: /start voice input/i }));
      
      // Wait for processing and auto-creation
      await waitFor(() => {
        expect(screen.getByText(/event created successfully/i)).toBeInTheDocument();
      });

      // Verify the database call was made
      expect(mockSupabase.from).toHaveBeenCalledWith('inventory_events');
    });

    it('handles database errors gracefully', async () => {
      const user = userEvent.setup();
      
      mockSpeechRecognition.setRecognitionResult('Received 25 pounds of salmon');
      mockSupabase.setConfig({ shouldFail: true, failureType: 'permission' });
      
      render(<VoiceAssistant />);
      
      await user.click(screen.getByRole('button', { name: /start voice input/i }));
      
      await waitFor(() => {
        expect(screen.getByText(/permission denied/i)).toBeInTheDocument();
      });
    });

    it('stores audio recordings in Supabase storage', async () => {
      const user = userEvent.setup();
      
      render(<VoiceAssistant />);
      
      await user.click(screen.getByRole('button', { name: /start voice input/i }));
      await user.click(screen.getByRole('button', { name: /stop recording/i }));
      
      await waitFor(() => {
        expect(mockSupabase.storage.from).toHaveBeenCalledWith('voice-recordings');
      });
    });
  });

  describe('User Experience', () => {
    it('provides real-time visual feedback during recording', async () => {
      const user = userEvent.setup();
      render(<VoiceAssistant />);
      
      await user.click(screen.getByRole('button', { name: /start voice input/i }));
      
      await waitFor(() => {
        const recordingIndicator = screen.getByTestId('recording-indicator');
        expect(recordingIndicator).toHaveClass('animate-pulse');
      });
    });

    it('shows processing progress for long operations', async () => {
      const user = userEvent.setup();
      
      mockOpenAI.setLatency(2000); // 2 second delay
      mockSpeechRecognition.setRecognitionResult('Received 25 pounds of salmon');
      
      render(<VoiceAssistant />);
      
      await user.click(screen.getByRole('button', { name: /start voice input/i }));
      
      expect(screen.getByText(/processing/i)).toBeInTheDocument();
      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });

    it('allows users to edit commands before saving', async () => {
      const user = userEvent.setup();
      
      mockSpeechRecognition.setRecognitionResult('Received 25 pounds of salmon');
      mockOpenAI.setResponseQuality('medium');
      
      render(<VoiceAssistant />);
      
      await user.click(screen.getByRole('button', { name: /start voice input/i }));
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /edit/i })).toBeInTheDocument();
      });

      await user.click(screen.getByRole('button', { name: /edit/i }));
      
      await waitFor(() => {
        expect(screen.getByRole('form')).toBeInTheDocument();
        expect(screen.getByDisplayValue('25')).toBeInTheDocument();
        expect(screen.getByDisplayValue('salmon')).toBeInTheDocument();
      });
    });

    it('provides helpful error messages and recovery options', async () => {
      const user = userEvent.setup();
      
      mockOpenAI.setShouldFail(true, 'auth');
      mockSpeechRecognition.setRecognitionResult('Received 25 pounds of salmon');
      
      render(<VoiceAssistant />);
      
      await user.click(screen.getByRole('button', { name: /start voice input/i }));
      
      await waitFor(() => {
        expect(screen.getByText(/authentication failed/i)).toBeInTheDocument();
        expect(screen.getByText(/check your api key/i)).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /retry/i })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /manual entry/i })).toBeInTheDocument();
      });
    });
  });

  describe('Performance', () => {
    it('handles multiple rapid voice inputs gracefully', async () => {
      const user = userEvent.setup();
      render(<VoiceAssistant />);
      
      // Simulate rapid clicking
      for (let i = 0; i < 5; i++) {
        await user.click(screen.getByRole('button', { name: /start voice input/i }));
        await user.click(screen.getByRole('button', { name: /stop recording/i }));
      }
      
      // Should not crash and should show appropriate feedback
      expect(screen.getByText(/please wait/i)).toBeInTheDocument();
    });

    it('cleans up resources when component unmounts', () => {
      const { unmount } = render(<VoiceAssistant />);
      
      unmount();
      
      // Verify cleanup (mocked functions should be called)
      expect(mockSpeechRecognition.stop).toHaveBeenCalled();
    });
  });

  describe('Accessibility', () => {
    it('provides proper ARIA labels for screen readers', () => {
      render(<VoiceAssistant />);
      
      expect(screen.getByRole('button', { name: /start voice input/i })).toHaveAttribute('aria-label');
      expect(screen.getByRole('region', { name: /voice assistant/i })).toBeInTheDocument();
    });

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      render(<VoiceAssistant />);
      
      const startButton = screen.getByRole('button', { name: /start voice input/i });
      startButton.focus();
      
      await user.keyboard('{Enter}');
      
      await waitFor(() => {
        expect(screen.getByText(/listening/i)).toBeInTheDocument();
      });
    });

    it('provides voice status announcements for screen readers', async () => {
      const user = userEvent.setup();
      render(<VoiceAssistant />);
      
      await user.click(screen.getByRole('button', { name: /start voice input/i }));
      
      await waitFor(() => {
        expect(screen.getByRole('status')).toHaveTextContent(/listening for voice input/i);
      });
    });
  });

  describe('Edge Cases', () => {
    it('handles empty transcription results', async () => {
      const user = userEvent.setup();
      
      mockSpeechRecognition.setRecognitionResult('');
      
      render(<VoiceAssistant />);
      
      await user.click(screen.getByRole('button', { name: /start voice input/i }));
      
      await waitFor(() => {
        expect(screen.getByText(/no speech detected/i)).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /try again/i })).toBeInTheDocument();
      });
    });

    it('handles very long transcriptions', async () => {
      const user = userEvent.setup();
      
      const longTranscript = 'Received '.repeat(100) + '25 pounds of salmon from Ocean Fresh Seafoods';
      mockSpeechRecognition.setRecognitionResult(longTranscript);
      
      render(<VoiceAssistant />);
      
      await user.click(screen.getByRole('button', { name: /start voice input/i }));
      
      await waitFor(() => {
        expect(screen.getByText(/transcript too long/i)).toBeInTheDocument();
      });
    });

    it('handles browser compatibility issues', () => {
      // Mock unsupported browser
      global.SpeechRecognition = undefined;
      global.webkitSpeechRecognition = undefined;
      
      render(<VoiceAssistant />);
      
      expect(screen.getByText(/voice input not supported/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /manual entry/i })).toBeInTheDocument();
    });
  });
});