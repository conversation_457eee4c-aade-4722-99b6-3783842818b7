import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { VoiceEventEditor } from '../VoiceEventEditor';
import { voiceEventService } from '../../../services/VoiceEventService';
import { VoiceEvent } from '../../../types/schema';

// Mock the VoiceEventService
vi.mock('../../../services/VoiceEventService', () => ({
  voiceEventService: {
    updateVoiceEvent: vi.fn(),
    getEventAuditTrail: vi.fn()
  }
}));

// Mock Audio API
global.Audio = vi.fn().mockImplementation(() => ({
  play: vi.fn().mockResolvedValue(undefined),
  pause: vi.fn(),
  currentTime: 0,
  duration: 0
}));

describe('VoiceEventEditor', () => {
  const mockEvent: VoiceEvent = {
    id: '1',
    event_type: 'receiving',
    product_name: 'Salmon Fillet',
    quantity: 25,
    unit: 'lbs',
    vendor_name: 'Pacific Seafoods',
    voice_confidence_score: 0.85,
    voice_confidence_breakdown: {
      product_match: 0.9,
      quantity_extraction: 0.8,
      vendor_match: 0.85,
      overall: 0.85
    },
    raw_transcript: 'Received 25 pounds of salmon fillet from Pacific Seafoods',
    audio_recording_url: 'blob:mock-audio-url',
    created_by_voice: true,
    event_date: '2025-08-15',
    occurred_at: '2025-08-15T10:00:00Z',
    created_at: '2025-08-15T10:00:00Z',
    updated_at: '2025-08-15T10:00:00Z',
    condition: 'Good',
    temperature: 38,
    temperature_unit: 'fahrenheit',
    notes: 'Fresh delivery'
  };

  const mockAuditTrail = [
    {
      id: '1',
      event_id: '1',
      field_name: 'quantity',
      old_value: 20,
      new_value: 25,
      changed_by: 'user123',
      changed_at: '2025-08-15T11:00:00Z',
      change_reason: 'Corrected quantity'
    }
  ];

  let mockVoiceEventService: any;
  let mockOnSave: Mock;
  let mockOnCancel: Mock;

  beforeEach(() => {
    mockVoiceEventService = voiceEventService as any;
    mockOnSave = vi.fn();
    mockOnCancel = vi.fn();
    vi.clearAllMocks();
    
    // Default mock implementations
    mockVoiceEventService.updateVoiceEvent.mockResolvedValue(mockEvent);
    mockVoiceEventService.getEventAuditTrail.mockResolvedValue(mockAuditTrail);
  });

  describe('rendering', () => {
    it('should render edit form with event data', async () => {
      render(
        <VoiceEventEditor
          event={mockEvent}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      expect(screen.getByText('Edit Voice Event')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Salmon Fillet')).toBeInTheDocument();
      expect(screen.getByDisplayValue('25')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Pacific Seafoods')).toBeInTheDocument();
    });

    it('should render in read-only mode', async () => {
      render(
        <VoiceEventEditor
          event={mockEvent}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
          readOnly={true}
        />
      );

      expect(screen.getByText('View Voice Event')).toBeInTheDocument();
      expect(screen.queryByText('Save Changes')).not.toBeInTheDocument();
      
      const productInput = screen.getByDisplayValue('Salmon Fillet');
      expect(productInput).toBeDisabled();
    });

    it('should display confidence score and breakdown', async () => {
      render(
        <VoiceEventEditor
          event={mockEvent}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      expect(screen.getByText('Overall: 85%')).toBeInTheDocument();
      expect(screen.getByText('90%')).toBeInTheDocument(); // Product match
      expect(screen.getByText('80%')).toBeInTheDocument(); // Quantity extraction
    });

    it('should display original transcript', async () => {
      render(
        <VoiceEventEditor
          event={mockEvent}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      expect(screen.getByText('Original Transcript')).toBeInTheDocument();
      expect(screen.getByText('"Received 25 pounds of salmon fillet from Pacific Seafoods"')).toBeInTheDocument();
    });

    it('should load and display audit trail', async () => {
      render(
        <VoiceEventEditor
          event={mockEvent}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      await waitFor(() => {
        expect(mockVoiceEventService.getEventAuditTrail).toHaveBeenCalledWith('1');
      });

      expect(screen.getByText('Change History (1)')).toBeInTheDocument();
    });
  });

  describe('form validation', () => {
    it('should validate required fields', async () => {
      render(
        <VoiceEventEditor
          event={mockEvent}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      // Clear required field
      const productInput = screen.getByDisplayValue('Salmon Fillet');
      fireEvent.change(productInput, { target: { value: '' } });

      const saveButton = screen.getByText('Save Changes');
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(screen.getByText('Product name is required')).toBeInTheDocument();
      });

      expect(mockOnSave).not.toHaveBeenCalled();
    });

    it('should validate quantity is positive', async () => {
      render(
        <VoiceEventEditor
          event={mockEvent}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      const quantityInput = screen.getByDisplayValue('25');
      fireEvent.change(quantityInput, { target: { value: '0' } });

      const saveButton = screen.getByText('Save Changes');
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(screen.getByText('Quantity must be greater than 0')).toBeInTheDocument();
      });

      expect(mockOnSave).not.toHaveBeenCalled();
    });

    it('should validate vendor for receiving events', async () => {
      render(
        <VoiceEventEditor
          event={mockEvent}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      // Clear vendor field
      const vendorInput = screen.getByDisplayValue('Pacific Seafoods');
      fireEvent.change(vendorInput, { target: { value: '' } });

      const saveButton = screen.getByText('Save Changes');
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(screen.getByText('Vendor is required for receiving events')).toBeInTheDocument();
      });

      expect(mockOnSave).not.toHaveBeenCalled();
    });

    it('should validate customer for sale events', async () => {
      const saleEvent = { ...mockEvent, event_type: 'sale' as const, customer_name: 'Restaurant ABC' };
      
      render(
        <VoiceEventEditor
          event={saleEvent}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      // Clear customer field
      const customerInput = screen.getByDisplayValue('Restaurant ABC');
      fireEvent.change(customerInput, { target: { value: '' } });

      const saveButton = screen.getByText('Save Changes');
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(screen.getByText('Customer is required for sale events')).toBeInTheDocument();
      });

      expect(mockOnSave).not.toHaveBeenCalled();
    });

    it('should validate temperature range', async () => {
      render(
        <VoiceEventEditor
          event={mockEvent}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      const temperatureInput = screen.getByDisplayValue('38');
      fireEvent.change(temperatureInput, { target: { value: '300' } });

      const saveButton = screen.getByText('Save Changes');
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(screen.getByText('Temperature must be between -50 and 200')).toBeInTheDocument();
      });

      expect(mockOnSave).not.toHaveBeenCalled();
    });
  });

  describe('form interactions', () => {
    it('should update form fields', async () => {
      render(
        <VoiceEventEditor
          event={mockEvent}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      const productInput = screen.getByDisplayValue('Salmon Fillet');
      fireEvent.change(productInput, { target: { value: 'Cod Fillet' } });

      expect(screen.getByDisplayValue('Cod Fillet')).toBeInTheDocument();
    });

    it('should clear validation errors when field is corrected', async () => {
      render(
        <VoiceEventEditor
          event={mockEvent}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      // Create validation error
      const productInput = screen.getByDisplayValue('Salmon Fillet');
      fireEvent.change(productInput, { target: { value: '' } });

      const saveButton = screen.getByText('Save Changes');
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(screen.getByText('Product name is required')).toBeInTheDocument();
      });

      // Fix the error
      fireEvent.change(productInput, { target: { value: 'Cod Fillet' } });

      await waitFor(() => {
        expect(screen.queryByText('Product name is required')).not.toBeInTheDocument();
      });
    });

    it('should show/hide vendor field based on event type', async () => {
      render(
        <VoiceEventEditor
          event={mockEvent}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      // Should show vendor field for receiving
      expect(screen.getByDisplayValue('Pacific Seafoods')).toBeInTheDocument();

      // Change to disposal
      const eventTypeSelect = screen.getByDisplayValue('receiving');
      fireEvent.change(eventTypeSelect, { target: { value: 'disposal' } });

      // Vendor field should still be visible since it has a value
      expect(screen.getByDisplayValue('Pacific Seafoods')).toBeInTheDocument();
    });
  });

  describe('saving', () => {
    it('should save valid form data', async () => {
      render(
        <VoiceEventEditor
          event={mockEvent}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
          userId="user123"
        />
      );

      // Modify a field
      const notesInput = screen.getByDisplayValue('Fresh delivery');
      fireEvent.change(notesInput, { target: { value: 'Updated notes' } });

      const saveButton = screen.getByText('Save Changes');
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(mockVoiceEventService.updateVoiceEvent).toHaveBeenCalledWith(
          '1',
          expect.objectContaining({
            notes: 'Updated notes'
          }),
          'user123',
          'Manual edit via voice event editor'
        );
      });

      expect(mockOnSave).toHaveBeenCalledWith(mockEvent);
    });

    it('should show loading state while saving', async () => {
      mockVoiceEventService.updateVoiceEvent.mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve(mockEvent), 100))
      );

      render(
        <VoiceEventEditor
          event={mockEvent}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      const saveButton = screen.getByText('Save Changes');
      fireEvent.click(saveButton);

      expect(screen.getByText('Saving...')).toBeInTheDocument();
      expect(saveButton).toBeDisabled();

      await waitFor(() => {
        expect(screen.queryByText('Saving...')).not.toBeInTheDocument();
      });
    });

    it('should handle save errors', async () => {
      mockVoiceEventService.updateVoiceEvent.mockRejectedValue(new Error('Save failed'));

      render(
        <VoiceEventEditor
          event={mockEvent}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      const saveButton = screen.getByText('Save Changes');
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(screen.getByText('Failed to save event. Please try again.')).toBeInTheDocument();
      });

      expect(mockOnSave).not.toHaveBeenCalled();
    });
  });

  describe('audio playback', () => {
    it('should play audio when audio button is clicked', async () => {
      render(
        <VoiceEventEditor
          event={mockEvent}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      const audioButton = screen.getByTitle('Play original audio');
      fireEvent.click(audioButton);

      expect(global.Audio).toHaveBeenCalledWith('blob:mock-audio-url');
    });

    it('should not show audio button when no audio URL', async () => {
      const eventWithoutAudio = { ...mockEvent, audio_recording_url: undefined };
      
      render(
        <VoiceEventEditor
          event={eventWithoutAudio}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      expect(screen.queryByTitle('Play original audio')).not.toBeInTheDocument();
    });
  });

  describe('audit trail', () => {
    it('should toggle audit trail visibility', async () => {
      render(
        <VoiceEventEditor
          event={mockEvent}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      await waitFor(() => {
        expect(screen.getByText('Change History (1)')).toBeInTheDocument();
      });

      // Should be hidden initially
      expect(screen.queryByText('quantity changed')).not.toBeInTheDocument();

      // Click to show
      const toggleButton = screen.getByText('Show');
      fireEvent.click(toggleButton);

      expect(screen.getByText('quantity changed')).toBeInTheDocument();
      expect(screen.getByText('Corrected quantity')).toBeInTheDocument();

      // Click to hide
      const hideButton = screen.getByText('Hide');
      fireEvent.click(hideButton);

      expect(screen.queryByText('quantity changed')).not.toBeInTheDocument();
    });
  });

  describe('cancel functionality', () => {
    it('should call onCancel when cancel button is clicked', async () => {
      render(
        <VoiceEventEditor
          event={mockEvent}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      const cancelButton = screen.getByText('Cancel');
      fireEvent.click(cancelButton);

      expect(mockOnCancel).toHaveBeenCalled();
    });

    it('should call onCancel when X button is clicked', async () => {
      render(
        <VoiceEventEditor
          event={mockEvent}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      const closeButton = screen.getByRole('button', { name: '' }); // X button has no text
      fireEvent.click(closeButton);

      expect(mockOnCancel).toHaveBeenCalled();
    });
  });

  describe('conflict detection', () => {
    it('should show conflict warning when detected', async () => {
      // Mock Math.random to always return a value that triggers conflict
      const originalRandom = Math.random;
      Math.random = vi.fn().mockReturnValue(0.05); // Less than 0.1 threshold

      const oldEvent = { ...mockEvent, updated_at: '2025-08-15T09:00:00Z' }; // Old timestamp
      
      render(
        <VoiceEventEditor
          event={oldEvent}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      const saveButton = screen.getByText('Save Changes');
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(screen.getByText('Conflict Detected')).toBeInTheDocument();
        expect(screen.getByText('This event has been modified by another user. Please review the changes and resolve conflicts.')).toBeInTheDocument();
      });

      // Restore original Math.random
      Math.random = originalRandom;
    });
  });
});