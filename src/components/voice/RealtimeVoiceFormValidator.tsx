import React, { useState, useEffect, useRef } from 'react';
import { 
  <PERSON><PERSON><PERSON>cle, 
  AlertTriangle, 
  XCircle, 
  Clock, 
  Mic,
  Eye,
  EyeOff
} from 'lucide-react';
import { z } from 'zod';

interface ValidationRule {
  field: string;
  validator: (value: any) => { valid: boolean; message: string; suggestions?: string[] };
  priority: 'high' | 'medium' | 'low';
  realTimeEnabled: boolean;
}

interface ValidationResult {
  field: string;
  valid: boolean;
  message: string;
  suggestions?: string[];
  confidence: number;
  timestamp: number;
}

interface RealtimeFormData {
  eventType?: string;
  productName?: string;
  quantity?: number;
  unit?: string;
  vendorName?: string;
  customerName?: string;
  condition?: string;
  temperature?: number;
  temperatureUnit?: string;
  notes?: string;
  voiceConfidence?: number;
}

interface VoiceValidatorProps {
  formData: RealtimeFormData;
  onValidationUpdate: (results: ValidationResult[]) => void;
  onSuggestionSelect: (field: string, value: any) => void;
  validationMode: 'strict' | 'permissive' | 'learning';
  enableRealTimeValidation: boolean;
}

/**
 * Real-time form validation during voice input with seafood industry intelligence
 */
export default function RealtimeVoiceFormValidator({
  formData,
  onValidationUpdate,
  onSuggestionSelect,
  validationMode = 'permissive',
  enableRealTimeValidation = true
}: VoiceValidatorProps) {
  
  const [validationResults, setValidationResults] = useState<ValidationResult[]>([]);
  const [showAdvancedValidation, setShowAdvancedValidation] = useState(false);
  const [validationHistory, setValidationHistory] = useState<ValidationResult[][]>([]);
  const validationTimeoutRef = useRef<NodeJS.Timeout>();
  
  // Seafood industry validation rules
  const validationRules: ValidationRule[] = [
    {
      field: 'eventType',
      priority: 'high',
      realTimeEnabled: true,
      validator: (value) => {
        const validTypes = ['receiving', 'sale', 'disposal', 'physical_count', 'production', 'adjustment'];
        if (!value) {
          return { valid: false, message: 'Event type is required', suggestions: validTypes };
        }
        if (!validTypes.includes(value)) {
          return { 
            valid: false, 
            message: 'Invalid event type',
            suggestions: validTypes.filter(t => t.includes(value.toLowerCase().substring(0, 3)))
          };
        }
        return { valid: true, message: 'Valid event type' };
      }
    },
    
    {
      field: 'productName',
      priority: 'high',
      realTimeEnabled: true,
      validator: (value) => {
        if (!value || value.length < 2) {
          return { 
            valid: false, 
            message: 'Product name is required (minimum 2 characters)',
            suggestions: ['Salmon', 'Cod', 'Halibut', 'Crab', 'Lobster', 'Shrimp']
          };
        }
        
        // Check against known seafood species
        const knownSeafood = [
          'atlantic salmon', 'king salmon', 'coho salmon', 'sockeye salmon',
          'pacific cod', 'atlantic cod', 'black cod',
          'pacific halibut', 'california halibut',
          'dungeness crab', 'king crab', 'snow crab',
          'maine lobster', 'spiny lobster',
          'tiger shrimp', 'white shrimp', 'spot prawns',
          'yellowfin tuna', 'bluefin tuna', 'albacore tuna',
          'pacific oysters', 'kumamoto oysters',
          'manila clams', 'littleneck clams',
          'bay scallops', 'sea scallops'
        ];
        
        const lowerValue = value.toLowerCase();
        const exactMatch = knownSeafood.find(species => species === lowerValue);
        const partialMatches = knownSeafood.filter(species => 
          species.includes(lowerValue) || lowerValue.includes(species.split(' ')[0])
        );
        
        if (exactMatch) {
          return { valid: true, message: 'Recognized seafood species' };
        }
        
        if (partialMatches.length > 0) {
          return {
            valid: true,
            message: 'Possible seafood species match',
            suggestions: partialMatches.slice(0, 3)
          };
        }
        
        // Generic seafood validation - allow but suggest corrections
        const genericTerms = ['fish', 'seafood', 'shellfish', 'crustacean'];
        if (genericTerms.some(term => lowerValue.includes(term))) {
          return {
            valid: validationMode !== 'strict',
            message: validationMode === 'strict' 
              ? 'Please specify exact seafood species'
              : 'Generic seafood term - consider being more specific',
            suggestions: ['Atlantic Salmon', 'Pacific Cod', 'Dungeness Crab', 'Maine Lobster']
          };
        }
        
        return {
          valid: validationMode === 'learning',
          message: 'Unknown seafood species - please verify',
          suggestions: knownSeafood.filter(species => 
            this.calculateSimilarity(lowerValue, species) > 0.6
          ).slice(0, 3)
        };
      }
    },
    
    {
      field: 'quantity',
      priority: 'high',
      realTimeEnabled: true,
      validator: (value) => {
        if (value === null || value === undefined) {
          return { valid: false, message: 'Quantity is required' };
        }
        
        const numValue = Number(value);
        if (isNaN(numValue)) {
          return { valid: false, message: 'Quantity must be a number' };
        }
        
        if (numValue <= 0) {
          return { valid: false, message: 'Quantity must be greater than 0' };
        }
        
        if (numValue > 10000) {
          return {
            valid: validationMode !== 'strict',
            message: 'Very large quantity - please verify',
            suggestions: ['100', '500', '1000']
          };
        }
        
        // Check for reasonable decimal places
        const decimalPlaces = (numValue.toString().split('.')[1] || '').length;
        if (decimalPlaces > 2) {
          return {
            valid: true,
            message: 'Consider rounding to 2 decimal places',
            suggestions: [numValue.toFixed(2)]
          };
        }
        
        return { valid: true, message: 'Valid quantity' };
      }
    },
    
    {
      field: 'unit',
      priority: 'medium',
      realTimeEnabled: true,
      validator: (value) => {
        const standardUnits = [
          'lbs', 'pounds', 'kg', 'kilograms',
          'cases', 'boxes', 'units', 'pieces',
          'dozens', 'each', 'bags', 'containers'
        ];
        
        if (!value) {
          return { 
            valid: false, 
            message: 'Unit is required',
            suggestions: ['lbs', 'kg', 'cases', 'dozens']
          };
        }
        
        const normalizedValue = value.toLowerCase().replace(/s$/, ''); // Remove plural 's'
        const normalizedUnits = standardUnits.map(u => u.replace(/s$/, ''));
        
        if (normalizedUnits.includes(normalizedValue)) {
          return { valid: true, message: 'Standard unit recognized' };
        }
        
        // Suggest similar units
        const suggestions = standardUnits.filter(unit => 
          unit.includes(normalizedValue) || normalizedValue.includes(unit.substring(0, 2))
        );
        
        return {
          valid: validationMode === 'learning',
          message: 'Non-standard unit - consider using standard units',
          suggestions: suggestions.length > 0 ? suggestions : ['lbs', 'kg', 'cases']
        };
      }
    },
    
    {
      field: 'temperature',
      priority: 'high',
      realTimeEnabled: true,
      validator: (value) => {
        if (value === null || value === undefined) {
          return { valid: true, message: 'Temperature is optional' };
        }
        
        const numValue = Number(value);
        if (isNaN(numValue)) {
          return { valid: false, message: 'Temperature must be a number' };
        }
        
        // HACCP temperature ranges for seafood
        const unit = formData.temperatureUnit?.toLowerCase() || 'fahrenheit';
        let minTemp, maxTemp, idealRange;
        
        if (unit === 'celsius' || unit === 'c') {
          minTemp = -20;
          maxTemp = 10;
          idealRange = '0°C to 4°C';
        } else {
          minTemp = -4;
          maxTemp = 50;
          idealRange = '32°F to 39°F';
        }
        
        if (numValue < minTemp || numValue > maxTemp) {
          return {
            valid: false,
            message: `Temperature outside safe range (${minTemp}° to ${maxTemp}°)`,
            suggestions: [idealRange]
          };
        }
        
        // Check for HACCP compliance
        const isInIdealRange = unit === 'celsius' || unit === 'c' 
          ? (numValue >= 0 && numValue <= 4)
          : (numValue >= 32 && numValue <= 39);
        
        if (!isInIdealRange) {
          return {
            valid: true,
            message: `Temperature acceptable but not in ideal range (${idealRange})`,
            suggestions: [idealRange]
          };
        }
        
        return { valid: true, message: 'Temperature within HACCP guidelines' };
      }
    },
    
    {
      field: 'condition',
      priority: 'medium',
      realTimeEnabled: true,
      validator: (value) => {
        if (!value) {
          return { 
            valid: true, 
            message: 'Condition is optional',
            suggestions: ['Excellent', 'Good', 'Fair']
          };
        }
        
        const validConditions = ['excellent', 'good', 'fair', 'poor', 'damaged'];
        const normalizedValue = value.toLowerCase();
        
        if (validConditions.includes(normalizedValue)) {
          // Warn about poor conditions
          if (normalizedValue === 'poor' || normalizedValue === 'damaged') {
            return {
              valid: true,
              message: 'Poor condition noted - ensure proper disposal/handling',
              suggestions: ['Document reason for poor condition']
            };
          }
          return { valid: true, message: 'Valid condition' };
        }
        
        // Suggest closest match
        const suggestions = validConditions.filter(condition =>
          condition.includes(normalizedValue.substring(0, 2)) ||
          normalizedValue.includes(condition.substring(0, 2))
        );
        
        return {
          valid: validationMode !== 'strict',
          message: 'Non-standard condition description',
          suggestions: suggestions.length > 0 ? suggestions : validConditions
        };
      }
    },
    
    {
      field: 'voiceConfidence',
      priority: 'low',
      realTimeEnabled: true,
      validator: (value) => {
        if (value === null || value === undefined) {
          return { valid: true, message: 'Voice confidence not available' };
        }
        
        const numValue = Number(value);
        if (numValue < 0.7) {
          return {
            valid: validationMode !== 'strict',
            message: 'Low voice recognition confidence - please verify input',
            suggestions: ['Review and correct any errors']
          };
        }
        
        if (numValue < 0.85) {
          return {
            valid: true,
            message: 'Moderate voice confidence - double-check important fields'
          };
        }
        
        return { valid: true, message: 'High voice recognition confidence' };
      }
    }
  ];
  
  // Perform validation when form data changes
  useEffect(() => {
    if (!enableRealTimeValidation) return;
    
    // Clear previous timeout
    if (validationTimeoutRef.current) {
      clearTimeout(validationTimeoutRef.current);
    }
    
    // Debounce validation by 500ms to avoid excessive validation during speech
    validationTimeoutRef.current = setTimeout(() => {
      performValidation();
    }, 500);
    
    return () => {
      if (validationTimeoutRef.current) {
        clearTimeout(validationTimeoutRef.current);
      }
    };
  }, [formData, validationMode, enableRealTimeValidation]);
  
  /**
   * Perform comprehensive validation of current form data
   */
  const performValidation = () => {
    const results: ValidationResult[] = [];
    const timestamp = Date.now();
    
    validationRules.forEach(rule => {
      if (rule.realTimeEnabled || !enableRealTimeValidation) {
        const fieldValue = (formData as any)[rule.field];
        const result = rule.validator(fieldValue);
        
        results.push({
          field: rule.field,
          valid: result.valid,
          message: result.message,
          suggestions: result.suggestions,
          confidence: calculateValidationConfidence(rule, result, formData),
          timestamp
        });
      }
    });
    
    // Cross-field validation
    const crossFieldResults = performCrossFieldValidation(formData, timestamp);
    results.push(...crossFieldResults);
    
    setValidationResults(results);
    setValidationHistory(prev => [...prev.slice(-10), results]); // Keep last 10 validation rounds
    onValidationUpdate(results);
  };
  
  /**
   * Cross-field validation for business logic consistency
   */
  const performCrossFieldValidation = (data: RealtimeFormData, timestamp: number): ValidationResult[] => {
    const results: ValidationResult[] = [];
    
    // Quantity-Unit consistency
    if (data.quantity && data.unit) {
      const isLargeQuantity = data.quantity > 1000;
      const isSmallUnit = ['pieces', 'each', 'dozens'].includes(data.unit.toLowerCase());
      
      if (isLargeQuantity && isSmallUnit) {
        results.push({
          field: 'quantity-unit',
          valid: validationMode !== 'strict',
          message: 'Large quantity with small unit - please verify',
          suggestions: ['Consider using cases or bulk units'],
          confidence: 0.7,
          timestamp
        });
      }
    }
    
    // Event type - Product consistency
    if (data.eventType === 'disposal' && data.condition === 'excellent') {
      results.push({
        field: 'eventType-condition',
        valid: validationMode === 'learning',
        message: 'Disposing excellent condition product - verify reason',
        suggestions: ['Change condition to reflect disposal reason'],
        confidence: 0.8,
        timestamp
      });
    }
    
    // Temperature - Product consistency
    if (data.temperature !== undefined && data.productName) {
      const productType = getProductType(data.productName);
      const isFrozenProduct = productType === 'frozen';
      const tempF = convertToFahrenheit(data.temperature, data.temperatureUnit);
      
      if (isFrozenProduct && tempF > 32) {
        results.push({
          field: 'temperature-product',
          valid: false,
          message: 'Frozen product temperature too high - HACCP violation',
          suggestions: ['Verify temperature reading', 'Check freezer operation'],
          confidence: 0.9,
          timestamp
        });
      }
    }
    
    return results;
  };
  
  /**
   * Calculate validation confidence based on voice confidence and field importance
   */
  const calculateValidationConfidence = (
    rule: ValidationRule, 
    result: { valid: boolean; message: string }, 
    data: RealtimeFormData
  ): number => {
    let confidence = 0.8; // Base confidence
    
    // Adjust based on voice confidence
    if (data.voiceConfidence) {
      confidence *= data.voiceConfidence;
    }
    
    // Adjust based on field priority
    switch (rule.priority) {
      case 'high':
        confidence *= result.valid ? 1.0 : 0.7; // Lower confidence for invalid high-priority fields
        break;
      case 'medium':
        confidence *= result.valid ? 1.0 : 0.8;
        break;
      case 'low':
        confidence *= result.valid ? 1.0 : 0.9;
        break;
    }
    
    return Math.max(0.1, confidence); // Minimum confidence of 0.1
  };
  
  /**
   * Utility functions
   */
  const calculateSimilarity = (str1: string, str2: string): number => {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 1.0;
    
    const editDistance = levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  };
  
  const levenshteinDistance = (str1: string, str2: string): number => {
    const track = Array(str2.length + 1).fill(null).map(() =>
      Array(str1.length + 1).fill(null));
    
    for (let i = 0; i <= str1.length; i++) track[0][i] = i;
    for (let j = 0; j <= str2.length; j++) track[j][0] = j;
    
    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        track[j][i] = Math.min(
          track[j][i - 1] + 1,
          track[j - 1][i] + 1,
          track[j - 1][i - 1] + indicator,
        );
      }
    }
    
    return track[str2.length][str1.length];
  };
  
  const getProductType = (productName: string): string => {
    const lowerName = productName.toLowerCase();
    if (lowerName.includes('frozen') || lowerName.includes('iqf')) return 'frozen';
    if (lowerName.includes('fresh')) return 'fresh';
    if (lowerName.includes('live')) return 'live';
    return 'fresh'; // Default assumption
  };
  
  const convertToFahrenheit = (temp: number, unit?: string): number => {
    if (!unit || unit.toLowerCase().includes('f')) return temp;
    return (temp * 9/5) + 32; // Convert Celsius to Fahrenheit
  };
  
  /**
   * Get validation summary for display
   */
  const getValidationSummary = () => {
    const valid = validationResults.filter(r => r.valid).length;
    const total = validationResults.length;
    const highPriorityIssues = validationResults.filter(r => 
      !r.valid && validationRules.find(rule => rule.field === r.field)?.priority === 'high'
    ).length;
    
    return { valid, total, highPriorityIssues };
  };
  
  const summary = getValidationSummary();
  
  return (
    <div className="bg-white rounded-lg shadow-sm border p-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <CheckCircle className={`w-5 h-5 ${
            summary.highPriorityIssues === 0 ? 'text-green-600' : 'text-yellow-600'
          }`} />
          <h3 className="text-lg font-semibold text-gray-900">
            Real-time Validation
          </h3>
          <span className="text-sm text-gray-600">
            ({summary.valid}/{summary.total} fields valid)
          </span>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={() => setShowAdvancedValidation(!showAdvancedValidation)}
            className="flex items-center gap-1 text-sm text-gray-600 hover:text-gray-800"
          >
            {showAdvancedValidation ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            {showAdvancedValidation ? 'Simple' : 'Advanced'}
          </button>
        </div>
      </div>
      
      {/* Validation Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <div className="bg-green-50 p-3 rounded-lg">
          <div className="flex items-center gap-2">
            <CheckCircle className="w-4 h-4 text-green-600" />
            <span className="text-sm font-medium text-green-900">Valid Fields</span>
          </div>
          <div className="text-2xl font-bold text-green-900 mt-1">
            {summary.valid}
          </div>
        </div>
        
        <div className="bg-yellow-50 p-3 rounded-lg">
          <div className="flex items-center gap-2">
            <AlertTriangle className="w-4 h-4 text-yellow-600" />
            <span className="text-sm font-medium text-yellow-900">Warnings</span>
          </div>
          <div className="text-2xl font-bold text-yellow-900 mt-1">
            {summary.total - summary.valid - summary.highPriorityIssues}
          </div>
        </div>
        
        <div className="bg-red-50 p-3 rounded-lg">
          <div className="flex items-center gap-2">
            <XCircle className="w-4 h-4 text-red-600" />
            <span className="text-sm font-medium text-red-900">Errors</span>
          </div>
          <div className="text-2xl font-bold text-red-900 mt-1">
            {summary.highPriorityIssues}
          </div>
        </div>
      </div>
      
      {/* Validation Results */}
      <div className="space-y-2">
        {validationResults.map((result, index) => {
          const rule = validationRules.find(r => r.field === result.field);
          const isHighPriority = rule?.priority === 'high';
          
          return (
            <div
              key={`${result.field}-${index}`}
              className={`p-3 rounded-lg border ${
                result.valid 
                  ? 'bg-green-50 border-green-200' 
                  : isHighPriority
                    ? 'bg-red-50 border-red-200'
                    : 'bg-yellow-50 border-yellow-200'
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    {result.valid ? (
                      <CheckCircle className="w-4 h-4 text-green-600" />
                    ) : isHighPriority ? (
                      <XCircle className="w-4 h-4 text-red-600" />
                    ) : (
                      <AlertTriangle className="w-4 h-4 text-yellow-600" />
                    )}
                    <span className="text-sm font-medium capitalize">
                      {result.field.replace(/([A-Z])/g, ' $1').toLowerCase()}
                    </span>
                    {showAdvancedValidation && (
                      <span className="text-xs text-gray-500">
                        ({Math.round(result.confidence * 100)}% confidence)
                      </span>
                    )}
                  </div>
                  
                  <div className="text-sm text-gray-700 mb-2">
                    {result.message}
                  </div>
                  
                  {result.suggestions && result.suggestions.length > 0 && (
                    <div className="space-y-1">
                      <div className="text-xs text-gray-600">Suggestions:</div>
                      <div className="flex flex-wrap gap-1">
                        {result.suggestions.map((suggestion, idx) => (
                          <button
                            key={idx}
                            onClick={() => onSuggestionSelect(result.field, suggestion)}
                            className="inline-flex items-center px-2 py-1 bg-white border border-gray-300 rounded text-xs hover:bg-gray-50 transition-colors"
                          >
                            {suggestion}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
                
                {showAdvancedValidation && (
                  <div className="ml-4 text-right">
                    <div className="text-xs text-gray-500">
                      <Clock className="w-3 h-3 inline mr-1" />
                      {new Date(result.timestamp).toLocaleTimeString()}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      Priority: {rule?.priority || 'unknown'}
                    </div>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
      
      {/* Validation History (Advanced Mode) */}
      {showAdvancedValidation && validationHistory.length > 0 && (
        <div className="mt-6 pt-4 border-t">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Validation History</h4>
          <div className="space-y-2">
            {validationHistory.slice(-3).map((historyResults, historyIndex) => {
              const historyValid = historyResults.filter(r => r.valid).length;
              const historyTotal = historyResults.length;
              const timestamp = historyResults[0]?.timestamp;
              
              return (
                <div key={historyIndex} className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">
                    {timestamp && new Date(timestamp).toLocaleTimeString()}
                  </span>
                  <span className={`font-medium ${
                    historyValid === historyTotal ? 'text-green-600' : 'text-yellow-600'
                  }`}>
                    {historyValid}/{historyTotal} valid
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}