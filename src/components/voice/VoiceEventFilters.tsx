import React from 'react';
import { Filter, Search } from 'lucide-react';

interface VoiceEventFiltersProps {
  searchQuery: string;
  selectedEventType: string;
  confidenceFilter: number;
  dateRange: { start: string; end: string };
  onSearchChange: (query: string) => void;
  onEventTypeChange: (type: string) => void;
  onConfidenceChange: (threshold: number) => void;
  onDateRangeChange: (range: { start: string; end: string }) => void;
  onFilterChange: () => void;
}

export const VoiceEventFilters: React.FC<VoiceEventFiltersProps> = ({
  searchQuery,
  selectedEventType,
  confidenceFilter,
  dateRange,
  onSearchChange,
  onEventTypeChange,
  onConfidenceChange,
  onDateRangeChange,
  onFilterChange
}) => {
  const handleDateStartChange = (start: string) => {
    onDateRangeChange({ ...dateRange, start });
  };

  const handleDateEndChange = (end: string) => {
    onDateRangeChange({ ...dateRange, end });
  };

  return (
    <div className="bg-white p-4 rounded-lg border border-gray-200 space-y-4">
      <div className="flex items-center space-x-4">
        <Filter className="w-5 h-5 text-gray-500" />
        <span className="font-medium text-gray-700">Filters</span>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Search */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Search
          </label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
              onBlur={onFilterChange}
              onKeyPress={(e) => e.key === 'Enter' && onFilterChange()}
              placeholder="Search products, vendors..."
              className="pl-10 w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Event Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Event Type
          </label>
          <select
            value={selectedEventType}
            onChange={(e) => {
              onEventTypeChange(e.target.value);
              onFilterChange();
            }}
            className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          >
            <option value="">All Types</option>
            <option value="receiving">Receiving</option>
            <option value="sale">Sale</option>
            <option value="disposal">Disposal</option>
            <option value="physical_count">Physical Count</option>
          </select>
        </div>

        {/* Confidence Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Min Confidence
          </label>
          <select
            value={confidenceFilter}
            onChange={(e) => {
              onConfidenceChange(Number(e.target.value));
              onFilterChange();
            }}
            className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          >
            <option value={0}>All Confidence</option>
            <option value={0.9}>High (90%+)</option>
            <option value={0.7}>Medium (70%+)</option>
            <option value={0.5}>Low (50%+)</option>
          </select>
        </div>

        {/* Date Range */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Date Range
          </label>
          <div className="flex space-x-2">
            <input
              type="date"
              value={dateRange.start}
              onChange={(e) => handleDateStartChange(e.target.value)}
              onBlur={onFilterChange}
              className="flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
            <input
              type="date"
              value={dateRange.end}
              onChange={(e) => handleDateEndChange(e.target.value)}
              onBlur={onFilterChange}
              className="flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default VoiceEventFilters;