import React, { useState, useCallback, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, CheckCircle, AlertCircle } from 'lucide-react';
import { getVoiceProcessor, type SeafoodCommandData } from '../../lib/voice-processor';

interface VoiceFormData {
  quantity?: number;
  unit?: string;
  product?: string;
  species?: string;
  vendor?: string;
  customer?: string;
  condition?: string;
  temperature?: number;
  temperatureUnit?: 'F' | 'C';
  price?: number;
  notes?: string;
  eventDate?: string;
}

interface EnhancedVoiceFormIntegrationProps {
  onDataExtracted: (data: VoiceFormData) => void;
  eventType?: 'receiving' | 'disposal' | 'physical_count' | 'sale';
  className?: string;
  placeholder?: string;
  disabled?: boolean;
}

interface ProcessingState {
  isRecording: boolean;
  isProcessing: boolean;
  status: 'idle' | 'listening' | 'processing' | 'success' | 'error';
  feedback: string;
  transcript: string;
  confidence: number;
  processingTime: number;
}

export default function EnhancedVoiceFormIntegration({ 
  onDataExtracted, 
  eventType = 'receiving',
  className = '',
  placeholder,
  disabled = false
}: EnhancedVoiceFormIntegrationProps) {
  const [state, setState] = useState<ProcessingState>({
    isRecording: false,
    isProcessing: false,
    status: 'idle',
    feedback: '',
    transcript: '',
    confidence: 0,
    processingTime: 0
  });

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const voiceProcessorRef = useRef(getVoiceProcessor());

  // Start recording
  const startRecording = useCallback(async () => {
    if (disabled) return;

    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 16000
        }
      });

      audioChunksRef.current = [];
      
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm'
      });
      
      mediaRecorderRef.current = mediaRecorder;

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        await processAudio(audioBlob);
        
        // Stop all tracks
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorder.start();
      
      setState(prev => ({
        ...prev,
        isRecording: true,
        status: 'listening',
        feedback: 'Listening for form data...',
        transcript: '',
        confidence: 0
      }));

    } catch (error) {
      console.error('Failed to start recording:', error);
      setState(prev => ({
        ...prev,
        status: 'error',
        feedback: 'Failed to access microphone. Please check permissions.'
      }));
    }
  }, [disabled]);

  // Stop recording
  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && state.isRecording) {
      mediaRecorderRef.current.stop();
      setState(prev => ({
        ...prev,
        isRecording: false,
        isProcessing: true,
        status: 'processing',
        feedback: 'Processing with AI...'
      }));
    }
  }, [state.isRecording]);

  // Process audio with AI
  const processAudio = useCallback(async (audioBlob: Blob) => {
    const startTime = Date.now();

    try {
      const result = await voiceProcessorRef.current.processVoiceCommand(audioBlob);
      const processingTime = Date.now() - startTime;

      setState(prev => ({
        ...prev,
        isProcessing: false,
        processingTime
      }));

      if (!result.success) {
        throw new Error(result.error?.message || 'Processing failed');
      }

      const commandData = result.data!;
      
      // Convert command data to form data
      const formData = convertToFormData(commandData);
      
      setState(prev => ({
        ...prev,
        status: 'success',
        transcript: commandData.raw_transcript || '',
        confidence: commandData.confidence_score,
        feedback: `Data extracted successfully! (${Math.round(commandData.confidence_score * 100)}% confidence)`
      }));

      // Call the callback with extracted data
      onDataExtracted(formData);

      // Clear feedback after delay
      setTimeout(() => {
        setState(prev => ({ ...prev, feedback: '', transcript: '' }));
      }, 4000);

    } catch (error) {
      console.error('Voice processing error:', error);
      
      setState(prev => ({
        ...prev,
        isProcessing: false,
        status: 'error',
        feedback: `Error: ${error instanceof Error ? error.message : 'Processing failed'}`
      }));

      // Clear error after delay
      setTimeout(() => {
        setState(prev => ({ ...prev, feedback: '', status: 'idle' }));
      }, 4000);
    }
  }, [onDataExtracted]);

  // Convert command data to form data
  const convertToFormData = useCallback((commandData: SeafoodCommandData): VoiceFormData => {
    return {
      quantity: commandData.quantity,
      unit: commandData.unit,
      product: commandData.product_name,
      species: commandData.species,
      vendor: commandData.vendor_name,
      customer: commandData.customer_name,
      condition: commandData.condition,
      temperature: commandData.temperature,
      temperatureUnit: commandData.temperature_unit,
      price: commandData.price,
      notes: commandData.notes,
      eventDate: commandData.event_date
    };
  }, []);

  // Get appropriate prompt for the event type
  const getPromptText = useCallback(() => {
    const basePrompts = {
      receiving: 'Say something like: "50 pounds coho salmon from 49th State Seafoods, condition excellent, yesterday"',
      disposal: 'Say something like: "10 pounds expired dungeness crab, spoiled condition"',
      physical_count: 'Say something like: "75 pounds pacific cod in freezer A"',
      sale: 'Say something like: "30 pounds alaskan halibut to Ocean Restaurant at $12 per pound"'
    };

    return placeholder || basePrompts[eventType] || 'Describe the inventory details you want to add';
  }, [eventType, placeholder]);

  // Get status icon
  const getStatusIcon = () => {
    switch (state.status) {
      case 'listening': 
        return <Mic className={`w-4 h-4 ${state.isRecording ? 'animate-pulse text-red-500' : 'text-blue-500'}`} />;
      case 'processing': 
        return <Zap className="w-4 h-4 animate-spin text-yellow-500" />;
      case 'success': 
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error': 
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default: 
        return <Mic className="w-4 h-4 text-gray-500" />;
    }
  };

  // Get status color classes
  const getStatusColorClass = () => {
    switch (state.status) {
      case 'listening': return 'border-blue-300 bg-blue-50';
      case 'processing': return 'border-yellow-300 bg-yellow-50';
      case 'success': return 'border-green-300 bg-green-50';
      case 'error': return 'border-red-300 bg-red-50';
      default: return 'border-gray-200 bg-gray-50';
    }
  };

  if (disabled) {
    return null;
  }

  return (
    <div className={`rounded-lg border p-3 transition-all duration-200 ${getStatusColorClass()} ${className}`}>
      {/* Header */}
      <div className="flex items-center gap-3 mb-2">
        <button
          onClick={state.isRecording ? stopRecording : startRecording}
          disabled={state.isProcessing}
          className={`p-2 rounded-full transition-all ${
            state.isRecording 
              ? 'bg-red-500 hover:bg-red-600 text-white animate-pulse' 
              : state.status === 'success'
              ? 'bg-green-500 hover:bg-green-600 text-white'
              : 'bg-blue-500 hover:bg-blue-600 text-white'
          } ${state.isProcessing ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          {state.isRecording ? <MicOff className="w-4 h-4" /> : <Mic className="w-4 h-4" />}
        </button>
        
        <div className="flex-1">
          <div className="flex items-center gap-2">
            <div className="text-sm font-medium text-gray-900 flex items-center gap-1">
              AI Voice Form Fill
              <Zap className="w-3 h-3 text-blue-500" />
            </div>
            {state.confidence > 0 && (
              <div className="text-xs px-2 py-0.5 rounded-full bg-white border">
                {Math.round(state.confidence * 100)}% confident
              </div>
            )}
          </div>
          <div className="flex items-center gap-2 text-xs text-gray-600">
            {getStatusIcon()}
            <span>
              {state.isRecording ? 'Recording...' : 
               state.isProcessing ? 'Processing with AI...' : 
               state.status === 'success' ? 'Data extracted successfully' :
               state.status === 'error' ? 'Processing failed' :
               'Click to speak'}
            </span>
            {state.processingTime > 0 && (
              <span className="text-gray-500">({state.processingTime}ms)</span>
            )}
          </div>
        </div>
      </div>

      {/* Confidence Bar */}
      {state.confidence > 0 && (
        <div className="mb-2">
          <div className="flex items-center gap-2">
            <div className="flex-1 bg-white bg-opacity-50 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-300 ${
                  state.confidence >= 0.8 ? 'bg-green-500' :
                  state.confidence >= 0.6 ? 'bg-yellow-500' : 'bg-red-500'
                }`}
                style={{ width: `${state.confidence * 100}%` }}
              />
            </div>
          </div>
        </div>
      )}
      
      {/* Transcript Display */}
      {state.transcript && (
        <div className="bg-white bg-opacity-70 rounded p-2 text-sm text-gray-900 mb-2">
          <strong>Heard:</strong> "{state.transcript}"
        </div>
      )}
      
      {/* Feedback */}
      {state.feedback && (
        <div className={`rounded p-2 text-sm mb-2 ${
          state.status === 'success' ? 'bg-green-100 text-green-800' :
          state.status === 'error' ? 'bg-red-100 text-red-800' :
          'bg-blue-100 text-blue-800'
        }`}>
          {state.feedback}
        </div>
      )}
      
      {/* Prompt */}
      <div className="text-xs text-gray-600">
        <strong>Try saying:</strong> {getPromptText()}
      </div>

      {/* AI Features Badge */}
      <div className="mt-2 flex items-center justify-between text-xs">
        <div className="flex items-center gap-1 text-blue-600">
          <Zap className="w-3 h-3" />
          <span>Seafood Industry AI</span>
        </div>
        {state.processingTime > 0 && state.processingTime < 300 && (
          <div className="text-green-600 font-medium">
            Ultra-fast processing!
          </div>
        )}
      </div>
    </div>
  );
}