import React, { useState, useEffect } from 'react';
import { Camera, Save, AlertCircle, AlertTriangle } from 'lucide-react';
import { useDropzone } from 'react-dropzone';
import VoiceInput from './VoiceInput';
import VoiceErrorDisplay from './VoiceErrorDisplay';
import VoiceQueueStatus from './VoiceQueueStatus';
import { supabase } from '../../lib/supabase';
import useVoiceErrorHandling from '../../hooks/useVoiceErrorHandling';
import { setupDatabase, getProductCategories } from '../../lib/setupDatabase';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";

interface ProcessedData {
  product?: string;
  quantity?: number;
  price?: number;
  vendor?: string;
  unit?: string;
  category?: string;  // Changed to string for UUID
  notes?: string;
  metadata?: Record<string, unknown>;
  images?: string[];
}

interface Category {
  id: string;  // Changed to string for UUID
  name: string;
}

export default function VoiceInventory() {
  const [processedData, setProcessedData] = useState<ProcessedData>({
    unit: 'lbs' // Initialize with default unit
  });
  const [error, setError] = useState<string | null>(null);
  const [warning, setWarning] = useState<string | null>(null);
  
  // Voice error handling
  const {
    currentError: voiceError,
    isRetrying,
    clearError: clearVoiceError,
    retryLastOperation
  } = useVoiceErrorHandling();
  const [isSaving, setIsSaving] = useState(false);
  const [images, setImages] = useState<File[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [category, setCategory] = useState('');
  const [isAuthed, setIsAuthed] = useState(false);
  // New dropdown data
  const [products, setProducts] = useState<Array<{ id: string; name: string }>>([]);
  const [vendors, setVendors] = useState<Array<{ id: string; name: string }>>([]);
  const [productCategories, setProductCategories] = useState<Array<{ value: string; label: string }>>([]);
  // Selections
  const [selectedEventTypeId, setSelectedEventTypeId] = useState('');
  const [selectedProductId, setSelectedProductId] = useState('');
  const [selectedVendorId, setSelectedVendorId] = useState('');

  // Track auth state locally to avoid pre-auth DB access
  useEffect(() => {
    let unsub: (() => void) | undefined;
    const initAuth = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setIsAuthed(!!session);
      const { data: { subscription } } = supabase.auth.onAuthStateChange((_e, next) => {
        setIsAuthed(!!next);
      });
      unsub = () => subscription.unsubscribe();
    };
    initAuth();
    return () => { if (unsub) unsub(); };
  }, []);

  // Initialize database and fetch categories only after auth
  useEffect(() => {
    if (!isAuthed) return;
    const init = async () => {
      try {
        console.log('Setting up database...');
        const { categories: dbCategories } = await setupDatabase();
        console.log('Database setup complete');
        setCategories(dbCategories);
        // Fetch dropdown data
        const [{ data: prodData, error: prodErr }, { data: vendData, error: vendErr }] = await Promise.all([
          supabase.from('Products').select('id, name').order('name', { ascending: true }),
          supabase.from('vendors').select('id, name').order('name', { ascending: true })
        ]);
        if (prodErr) console.warn('Error fetching products:', prodErr);
        if (vendErr) console.warn('Error fetching vendors:', vendErr);
        
        // Remove duplicates by name, keeping the first occurrence
        const uniqueProducts = (prodData || []).filter((product, index, array) => 
          array.findIndex(p => p.name === product.name) === index
        );
        const uniqueVendors = (vendData || []).filter((vendor, index, array) => 
          array.findIndex(v => v.name === vendor.name) === index
        );
        
        setProducts(uniqueProducts);
        setVendors(uniqueVendors);
        const { data: pcats } = await getProductCategories();
        setProductCategories((pcats as Array<{ value: string; label: string }>) || []);
      } catch (err) {
        console.error('Initialization error:', err);
        setError('Failed to initialize. Please refresh the page.');
      }
    };
    init();
  }, [isAuthed]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png']
    },
    multiple: true,
    onDrop: (acceptedFiles) => {
      setImages(prev => [...prev, ...acceptedFiles]);
    }
  });

  const handleVoiceData = (data: ProcessedData) => {
    setProcessedData(prev => ({ ...prev, ...data }));
    setError(null);

    // If voice input provided an event type (category) id, preselect it
    if (data.category) {
      const matched = categories.find(c => c.id === data.category);
      if (matched) setSelectedEventTypeId(data.category);
    }

    if (data.metadata?.processing_method === 'basic') {
      setWarning('Using basic voice processing due to AI service limitations.');
    } else {
      setWarning(null);
    }
  };

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
  };

  const handleSave = async () => {
    if (!processedData) return;
    if (!processedData.product) {
      setError('Product name is required');
      return;
    }
    // Product category (for Products table) can be optional; we fall back to 'Uncategorized'

    setIsSaving(true);
    setError(null);

    try {
      // Upload images to Supabase Storage
      let imageUrls: string[] = [];
      if (images.length > 0) {
        try {
          imageUrls = await Promise.all(
            images.map(async (file) => {
              const fileName = `voice-uploads/${Date.now()}-${file.name}`;
              const { error } = await supabase.storage
                .from('inventory-images')
                .upload(fileName, file);

              if (error) {
                console.error('Image upload error:', error);
                return ''; // Return empty string if upload fails
              }

              // Get public URL
              const { data: { publicUrl } } = supabase.storage
                .from('inventory-images')
                .getPublicUrl(fileName);

              return publicUrl;
            })
          );
          // Filter out any failed uploads
          imageUrls = imageUrls.filter(url => url !== '');
        } catch (uploadError) {
          console.error('Image upload error:', uploadError);
          // Continue without images if upload fails
        }
      }

      // Determine final product category label
      const safeCategory = category && category.trim() ? category.trim() : 'Uncategorized';

      // First, resolve product id (prefer dropdown selection)
      let productId = selectedProductId || '';
      if (!productId) {
        const { data: existingProduct } = await supabase
          .from('Products')
          .select('id')
          .eq('name', processedData.product)
          .maybeSingle();
        if (existingProduct?.id) {
          productId = existingProduct.id as string;
          // Update price/category on existing product
          const { data: productData } = await supabase
            .from('Products')
            .select('metadata')
            .eq('id', productId)
            .single();
          const { error: updateError } = await supabase
            .from('Products')
            .update({
              price: processedData.price || 0,
              category: safeCategory,
              metadata: {
                ...(productData?.metadata || {})
              }
            })
            .eq('id', productId);
          if (updateError) throw updateError;
        } else {
          // Create new product
          const { data: newProduct, error: createError } = await supabase
            .from('Products')
            .insert({
              name: processedData.product,
              price: processedData.price || 0,
              category: safeCategory,
              metadata: {}
            })
            .select()
            .single();
          if (createError) throw createError;
          productId = newProduct!.id as string;
        }
      }

      // Calculate total amount
      const quantity = processedData.quantity || 0;
      const unitPrice = processedData.price || 0;
      const totalAmount = quantity * unitPrice;

      // Get the selected event type
      const selectedCategory = categories.find(cat => cat.id === selectedEventTypeId);
      if (!selectedCategory) {
        throw new Error('Selected event type not found');
      }

      console.log('Safe Category:', safeCategory);
      console.log('Price:', processedData.price);

      // Create a unified event record
      const eventPayload = {
        event_type: selectedCategory.name.toLowerCase(),
        product_id: productId,
        name: processedData.product || '',  // Map product name to the name column
        quantity: processedData.quantity || quantity,
        total_amount: totalAmount,
        unit_price: processedData.price || null,
        notes: processedData.notes || '',
        images: imageUrls.length > 0 ? imageUrls : null,
        category: safeCategory,
        vendor_id: selectedVendorId || null,
        metadata: {
          source: 'voice',
          unit: processedData.unit || 'lbs',
          event_category: selectedCategory.name,
          ...processedData.metadata
        },
        created_at: new Date().toISOString()
      };

      console.log('Saving event:', eventPayload);

      const { error: eventError } = await supabase
        .from('inventory_events')
        .insert(eventPayload);

      if (eventError) throw eventError;

      // Reset form but maintain default unit
      setProcessedData({ unit: 'lbs' });
      setImages([]);
      setError(null);
      setWarning(null);
    } catch (error: unknown) {
      console.error('Save error:', error);
      if (error instanceof Error && error.message?.includes('unit')) {
        setError('Database schema is being updated. Please try again in a few seconds.');
      } else {
        setError(error instanceof Error ? error.message : 'Failed to save inventory item');
      }
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Voice Inventory</h1>
      </div>

      {warning && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center gap-2 text-yellow-800">
            <AlertTriangle className="w-5 h-5" />
            <p>{warning}</p>
          </div>
        </div>
      )}

      {voiceError && (
        <VoiceErrorDisplay
          error={voiceError}
          onRetry={retryLastOperation}
          onDismiss={clearVoiceError}
          onUseManualInput={() => {
            clearVoiceError();
            // Focus on first input field for manual entry
            const firstInput = document.querySelector('input[type="text"]') as HTMLInputElement;
            if (firstInput) firstInput.focus();
          }}
          isRetrying={isRetrying}
        />
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold mb-4">Voice Input</h2>
            <VoiceInput
              onTranscriptionComplete={handleVoiceData}
              onError={handleError}
            />
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold mb-4">Images</h2>
            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors
                ${isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-blue-400'}`}
            >
              <input {...getInputProps()} />
              <Camera className="w-12 h-12 mx-auto text-gray-400 mb-4" />
              <p className="text-gray-600">
                {isDragActive
                  ? 'Drop the images here'
                  : 'Drag & drop images, or click to select'}
              </p>
              <p className="text-sm text-gray-500 mt-2">
                Supports JPG, JPEG, PNG
              </p>
            </div>
            {images.length > 0 && (
              <div className="grid grid-cols-2 gap-4 mt-4">
                {images.map((file, index) => (
                  <div key={index} className="relative aspect-square rounded-lg overflow-hidden bg-gray-100">
                    <img
                      src={URL.createObjectURL(file)}
                      alt={`Upload ${index + 1}`}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        console.error('Error loading image:', processedData?.images?.[0]);
                        e.currentTarget.src = '/path/to/fallback-image.jpg'; // Provide a fallback image
                        e.currentTarget.parentElement?.classList.add('error-loading');
                      }}
                    />
                    <button
                      type="button"
                      onClick={(e) => {
                        e.stopPropagation();
                        setImages(images.filter((_, i) => i !== index));
                      }}
                      className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600"
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold mb-4">Processed Data</h2>
            {error && (
              <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center gap-2 text-red-800">
                  <AlertCircle className="w-5 h-5" />
                  <p>{error}</p>
                </div>
              </div>
            )}

            {processedData && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-700">Product</label>
                    <input
                      type="text"
                      value={processedData.product || ''}
                      onChange={(e) => {
                        setProcessedData(prev => ({ ...prev!, product: e.target.value }));
                        setSelectedProductId('');
                      }}
                      aria-label="Product name"
                      placeholder="Enter product name"
                      className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    />
                    <div className="mt-2">
                      <Select
                        value={selectedProductId}
                        onValueChange={(value: string) => {
                          setSelectedProductId(value);
                          const chosen = products.find(p => p.id === value);
                          if (chosen) setProcessedData(prev => ({ ...prev!, product: chosen.name }));
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder={products.length ? 'Select existing product' : 'Loading products...'} />
                        </SelectTrigger>
                        <SelectContent>
                          {products.length ? (
                            products.map(p => (
                              <SelectItem key={p.id} value={p.id}>{p.name}</SelectItem>
                            ))
                          ) : (
                            <div className="px-2 py-1.5 text-sm text-muted-foreground">
                              {error ? error : 'Loading products...'}
                            </div>
                          )}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-700">Quantity</label>
                    <input
                      type="number"
                      value={processedData.quantity || ''}
                      onChange={(e) => setProcessedData(prev => ({ ...prev!, quantity: parseInt(e.target.value) }))}
                      aria-label="Quantity"
                      placeholder="Enter quantity"
                      className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-700">Price</label>
                    <input
                      type="number"
                      step="0.01"
                      value={processedData.price || ''}
                      onChange={(e) => setProcessedData(prev => ({ ...prev!, price: parseFloat(e.target.value) }))}
                      aria-label="Price"
                      placeholder="Enter price"
                      className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-700">Unit</label>
                    <select
                      value={processedData.unit || 'lbs'}
                      onChange={(e) => setProcessedData(prev => ({ ...prev!, unit: e.target.value }))}
                      aria-label="Unit"
                      className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    >
                      <option value="lbs">Pounds (lbs)</option>
                      <option value="kg">Kilograms (kg)</option>
                      <option value="units">Units</option>
                      <option value="cases">Cases</option>
                    </select>
                  </div>
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-700">Product Category</label>
                    <input
                      type="text"
                      value={category}
                      onChange={(e) => {
                        setCategory(e.target.value);
                        console.log('Category changed:', e.target.value);
                      }}
                      aria-label="Product Category"
                      placeholder="Enter product category"
                      className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    />
                    {productCategories.length > 0 && (
                      <div className="mt-2">
                        <Select
                          value={category}
                          onValueChange={(value: string) => setCategory(value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select product category" />
                          </SelectTrigger>
                          <SelectContent>
                            {productCategories.map(pc => (
                              <SelectItem key={pc.value} value={pc.label}>{pc.label}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </div>
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-700">Event Type</label>
                    <Select
                      value={selectedEventTypeId}
                      onValueChange={(value: string) => {
                        console.log('Selected event type:', value);
                        setSelectedEventTypeId(value);
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={categories.length === 4 ? 'Select event type' : 'Loading event types...'} />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.length === 4 ? (
                          categories.map((category) => (
                            <SelectItem key={category.id} value={category.id.toString()}>
                              {category.name}
                            </SelectItem>
                          ))
                        ) : (
                          <div className="px-2 py-1.5 text-sm text-muted-foreground">
                            {error ? error : 'Loading event types...'}
                          </div>
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-700">Vendor</label>
                    <input
                      type="text"
                      value={processedData.vendor || ''}
                      onChange={(e) => {
                        setProcessedData(prev => ({ ...prev!, vendor: e.target.value }));
                        setSelectedVendorId('');
                      }}
                      aria-label="Vendor name"
                      placeholder="Enter vendor name"
                      className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    />
                    <div className="mt-2">
                      <Select
                        value={selectedVendorId}
                        onValueChange={(value: string) => {
                          setSelectedVendorId(value);
                          const v = vendors.find(v => v.id === value);
                          if (v) setProcessedData(prev => ({ ...prev!, vendor: v.name }));
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder={vendors.length ? 'Select vendor' : 'Loading vendors...'} />
                        </SelectTrigger>
                        <SelectContent>
                          {vendors.length ? (
                            vendors.map(v => (
                              <SelectItem key={v.id} value={v.id}>{v.name}</SelectItem>
                            ))
                          ) : (
                            <div className="px-2 py-1.5 text-sm text-muted-foreground">
                              {error ? error : 'Loading vendors...'}
                            </div>
                          )}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-700">Notes</label>
                  <textarea
                    value={processedData.notes || ''}
                    onChange={(e) => setProcessedData(prev => ({ ...prev!, notes: e.target.value }))}
                    rows={3}
                    aria-label="Notes"
                    placeholder="Enter notes"
                    className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>
              </div>
            )}

            <div className="mt-6">
              <button
                type="button"
                onClick={handleSave}
                disabled={!processedData?.product || !selectedEventTypeId || isSaving}
                className="w-full flex items-center justify-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Save className="w-5 h-5" />
                {isSaving ? 'Saving...' : 'Save to Inventory'}
              </button>
            </div>
          </div>
        </div>
      </div>
      
      {/* Voice Queue Status - shows when there are queued or failed voice events */}
      <VoiceQueueStatus />
    </div>
  );
}
