import React, { useState } from 'react';
import { MessageSquare, Play, RotateCcw, CheckCircle } from 'lucide-react';
import ConversationalVoiceInterface from './ConversationalVoiceInterface';

// Demo scenarios for testing conversational workflows
const DEMO_SCENARIOS = [
  {
    id: 'complete_info',
    title: 'Complete Information Provided',
    description: 'All required information is provided in the initial command',
    initialCommand: 'Received 50 pounds fresh dungeness crab from Pacific Seafoods',
    expectedFlow: [
      'Initial processing of complete command',
      'Vendor verification (exists in database)',
      'Batch code generation and confirmation',
      'Final transaction confirmation',
      'Successfully saved to inventory'
    ]
  },
  {
    id: 'missing_vendor',
    title: 'Missing Vendor Information',
    description: 'Product and quantity provided, but vendor is missing',
    initialCommand: 'Got 25 pounds of salmon fillets',
    expectedFlow: [
      'Extract product and quantity',
      'Ask: "Which vendor is this from?"',
      'User provides vendor name',
      'Check if vendor exists in database',
      'If not: Ask to create new vendor',
      'Batch code generation and confirmation',
      'Final transaction confirmation'
    ]
  },
  {
    id: 'missing_quantity',
    title: 'Missing Quantity Information',
    description: 'Product and vendor provided, but quantity is missing',
    initialCommand: 'Received king crab from Alaska Prime',
    expectedFlow: [
      'Extract product and vendor',
      'Ask: "How much did we receive? Please specify quantity and unit"',
      'User provides "30 pounds"',
      'Vendor verification',
      'Batch code generation and confirmation',
      'Final transaction confirmation'
    ]
  },
  {
    id: 'new_vendor',
    title: 'New Vendor Creation Workflow',
    description: 'Vendor not in database, requires creation',
    initialCommand: 'Received 40 pounds halibut from Northern Seafoods',
    expectedFlow: [
      'Extract all information',
      'Check vendor existence: Northern Seafoods not found',
      'Ask: "I don\'t see Northern Seafoods. Add as new vendor?"',
      'User confirms: "Yes"',
      'Ask: "What\'s their location or contact info?"',
      'User provides: "They\'re based in Seattle"',
      'Create vendor with location info',
      'Continue with batch code generation',
      'Final transaction confirmation'
    ]
  },
  {
    id: 'correction_workflow',
    title: 'Correction and Recovery Workflow',
    description: 'User needs to correct information during conversation',
    initialCommand: 'Received 100 pounds cod from Pacific Seafoods',
    expectedFlow: [
      'Process initial command',
      'Ask for batch code confirmation',
      'User says: "Actually, make that 75 pounds"',
      'Process correction and update quantity',
      'Continue with updated information',
      'Final transaction confirmation'
    ]
  },
  {
    id: 'complex_missing',
    title: 'Multiple Missing Fields',
    description: 'Only product mentioned, everything else missing',
    initialCommand: 'Got some dungeness crab',
    expectedFlow: [
      'Extract product: dungeness crab',
      'Ask: "How much did we receive?"',
      'User: "25 pounds"',
      'Ask: "Which vendor is this from?"',
      'User provides vendor',
      'Vendor verification/creation if needed',
      'Batch code generation and confirmation',
      'Final transaction confirmation'
    ]
  }
];

interface ConversationalVoiceDemoProps {
  className?: string;
}

export default function ConversationalVoiceDemo({ className = '' }: ConversationalVoiceDemoProps) {
  const [selectedScenario, setSelectedScenario] = useState<string>('');
  const [isRunningDemo, setIsRunningDemo] = useState(false);
  const [demoResults, setDemoResults] = useState<string[]>([]);
  const [completedScenarios, setCompletedScenarios] = useState<Set<string>>(new Set());

  const handleScenarioStart = (scenarioId: string) => {
    setSelectedScenario(scenarioId);
    setIsRunningDemo(true);
    setDemoResults([]);
  };

  const handleTransactionComplete = (data: any) => {
    setDemoResults(prev => [...prev, `✅ Transaction completed: ${JSON.stringify(data, null, 2)}`]);
    setCompletedScenarios(prev => new Set([...prev, selectedScenario]));
    
    setTimeout(() => {
      setIsRunningDemo(false);
      setSelectedScenario('');
    }, 2000);
  };

  const resetDemo = () => {
    setSelectedScenario('');
    setIsRunningDemo(false);
    setDemoResults([]);
  };

  const resetAllScenarios = () => {
    setCompletedScenarios(new Set());
    resetDemo();
  };

  const selectedScenarioData = DEMO_SCENARIOS.find(s => s.id === selectedScenario);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-50 to-green-50 border border-blue-200 rounded-lg p-6">
        <div className="flex items-start justify-between">
          <div>
            <h2 className="text-xl font-bold text-gray-900 mb-2">
              Conversational Voice Assistant Demo
            </h2>
            <p className="text-sm text-gray-600 mb-4">
              Test intelligent voice workflows that handle missing information, vendor creation, 
              batch code generation, and natural conversation flow.
            </p>
            <div className="flex items-center gap-4 text-sm">
              <span className="flex items-center gap-1 text-green-600">
                <CheckCircle className="w-4 h-4" />
                {completedScenarios.size} of {DEMO_SCENARIOS.length} scenarios completed
              </span>
              <button
                onClick={resetAllScenarios}
                className="flex items-center gap-1 text-blue-600 hover:text-blue-700"
              >
                <RotateCcw className="w-4 h-4" />
                Reset All
              </button>
            </div>
          </div>
          <MessageSquare className="w-8 h-8 text-blue-500" />
        </div>
      </div>

      {/* Scenario Selection */}
      {!isRunningDemo && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {DEMO_SCENARIOS.map((scenario) => (
            <div 
              key={scenario.id}
              className={`border rounded-lg p-4 cursor-pointer transition-all hover:shadow-md ${
                completedScenarios.has(scenario.id) 
                  ? 'bg-green-50 border-green-200' 
                  : 'bg-white border-gray-200 hover:border-blue-300'
              }`}
              onClick={() => handleScenarioStart(scenario.id)}
            >
              <div className="flex items-start justify-between mb-2">
                <h3 className="font-medium text-gray-900">{scenario.title}</h3>
                {completedScenarios.has(scenario.id) ? (
                  <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
                ) : (
                  <Play className="w-5 h-5 text-blue-500 flex-shrink-0" />
                )}
              </div>
              <p className="text-sm text-gray-600 mb-3">{scenario.description}</p>
              <div className="bg-blue-50 rounded p-2 mb-3">
                <div className="text-xs font-medium text-blue-800 mb-1">Initial Command:</div>
                <div className="text-sm italic text-blue-700">"{scenario.initialCommand}"</div>
              </div>
              <button
                className={`w-full px-3 py-2 rounded text-sm font-medium transition-colors ${
                  completedScenarios.has(scenario.id)
                    ? 'bg-green-100 text-green-700 hover:bg-green-200'
                    : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                }`}
              >
                {completedScenarios.has(scenario.id) ? 'Run Again' : 'Start Demo'}
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Active Demo Interface */}
      {isRunningDemo && selectedScenarioData && (
        <div className="space-y-4">
          {/* Scenario Info */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h3 className="font-medium text-blue-900 mb-1">{selectedScenarioData.title}</h3>
                <p className="text-sm text-blue-700 mb-3">{selectedScenarioData.description}</p>
                <div className="bg-white rounded p-3">
                  <div className="text-xs font-medium text-gray-600 mb-1">Expected Flow:</div>
                  <ol className="text-sm text-gray-700 space-y-1">
                    {selectedScenarioData.expectedFlow.map((step, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-blue-500 font-medium">{index + 1}.</span>
                        <span>{step}</span>
                      </li>
                    ))}
                  </ol>
                </div>
              </div>
              <button
                onClick={resetDemo}
                className="ml-4 p-2 text-gray-500 hover:text-gray-700"
                title="Stop demo"
              >
                <RotateCcw className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Conversational Interface */}
          <ConversationalVoiceInterface
            onTransactionComplete={handleTransactionComplete}
            eventType="receiving"
            className="min-h-96"
          />

          {/* Demo Instructions */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 className="font-medium text-yellow-900 mb-2">Demo Instructions:</h4>
            <ol className="text-sm text-yellow-800 space-y-1">
              <li>1. Start by clicking the microphone and saying: <strong>"{selectedScenarioData.initialCommand}"</strong></li>
              <li>2. Follow the conversation flow as the assistant asks follow-up questions</li>
              <li>3. Try saying "help", "start over", "go back", or "skip" to test recovery features</li>
              <li>4. Observe how the assistant handles missing information intelligently</li>
            </ol>
          </div>

          {/* Demo Results */}
          {demoResults.length > 0 && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h4 className="font-medium text-green-900 mb-2">Demo Results:</h4>
              <div className="space-y-2">
                {demoResults.map((result, index) => (
                  <div key={index} className="text-sm text-green-800 font-mono bg-white rounded p-2">
                    {result}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Success Summary */}
      {completedScenarios.size === DEMO_SCENARIOS.length && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
          <CheckCircle className="w-12 h-12 text-green-600 mx-auto mb-3" />
          <h3 className="text-lg font-bold text-green-900 mb-2">
            All Scenarios Completed! 🎉
          </h3>
          <p className="text-sm text-green-700 mb-4">
            You've successfully tested all conversational voice workflows. The system can handle:
          </p>
          <ul className="text-sm text-green-700 text-left max-w-md mx-auto space-y-1">
            <li>• Complete and incomplete voice commands</li>
            <li>• Intelligent follow-up questions</li>
            <li>• Vendor detection and creation</li>
            <li>• Batch code generation and confirmation</li>
            <li>• Error recovery and conversation restart</li>
            <li>• Natural conversation corrections</li>
          </ul>
        </div>
      )}
    </div>
  );
}