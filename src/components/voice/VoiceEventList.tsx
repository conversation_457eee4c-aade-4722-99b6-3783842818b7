import React, { useState, useEffect, useCallback } from 'react';
import { 
  Calendar, 
  Clock, 
  User, 
  Package, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle, 
  Search,
  Filter,
  RefreshCw,
  Volume2,
  Edit,
  Trash2
} from 'lucide-react';
import { supabase } from '../../lib/supabase';
import { voiceEventService } from '../../modules/voice-event-storage';
import { VoiceEvent, VoiceEventFilters } from '../../types/schema';

interface VoiceEventListProps {
  onEventEdit?: (event: VoiceEvent) => void;
  onEventDelete?: (eventId: string) => void;
  showFilters?: boolean;
  maxEvents?: number;
}

interface ConfidenceBadgeProps {
  score: number;
  breakdown?: {
    product_match: number;
    quantity_extraction: number;
    vendor_match: number;
    overall: number;
  };
}

const ConfidenceBadge: React.FC<ConfidenceBadgeProps> = ({ score, breakdown }) => {
  const getConfidenceLevel = (score: number) => {
    if (score >= 0.9) return { level: 'high', color: 'bg-green-100 text-green-800', icon: CheckCircle };
    if (score >= 0.7) return { level: 'medium', color: 'bg-yellow-100 text-yellow-800', icon: TrendingUp };
    return { level: 'low', color: 'bg-red-100 text-red-800', icon: AlertTriangle };
  };

  const { level, color, icon: Icon } = getConfidenceLevel(score);

  return (
    <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${color}`}>
      <Icon className="w-3 h-3 mr-1" />
      {Math.round(score * 100)}% {level}
    </div>
  );
};

interface EventTypeIconProps {
  eventType: string;
}

const EventTypeIcon: React.FC<EventTypeIconProps> = ({ eventType }) => {
  const getEventIcon = (type: string) => {
    switch (type) {
      case 'receiving': return { icon: Package, color: 'text-blue-600' };
      case 'sale': return { icon: TrendingUp, color: 'text-green-600' };
      case 'disposal': return { icon: Trash2, color: 'text-red-600' };
      case 'physical_count': return { icon: CheckCircle, color: 'text-purple-600' };
      default: return { icon: Package, color: 'text-gray-600' };
    }
  };

  const { icon: Icon, color } = getEventIcon(eventType);
  return <Icon className={`w-5 h-5 ${color}`} />;
};

export const VoiceEventList: React.FC<VoiceEventListProps> = ({
  onEventEdit,
  onEventDelete,
  showFilters = true,
  maxEvents = 50
}) => {
  const [events, setEvents] = useState<VoiceEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<VoiceEventFilters>({});
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedEventType, setSelectedEventType] = useState<string>('');
  const [confidenceFilter, setConfidenceFilter] = useState<number>(0);
  const [dateRange, setDateRange] = useState({ start: '', end: '' });

  // Load events
  const loadEvents = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const currentFilters: VoiceEventFilters = {
        searchQuery: searchQuery || undefined,
        eventType: selectedEventType ? [selectedEventType] : undefined,
        confidenceThreshold: confidenceFilter || undefined,
        dateRange: (dateRange.start && dateRange.end) ? dateRange : undefined
      };

      const voiceEvents = await voiceEventService.getVoiceEvents(currentFilters);
      setEvents(voiceEvents.slice(0, maxEvents));
    } catch (err) {
      console.error('Error loading voice events:', err);
      setError('Failed to load voice events. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [searchQuery, selectedEventType, confidenceFilter, dateRange, maxEvents]);

  // Initial load
  useEffect(() => {
    loadEvents();
  }, [loadEvents]);

  // Set up real-time subscriptions
  useEffect(() => {
    const channel = supabase
      .channel('voice-events-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'inventory_events',
          filter: 'created_by_voice=eq.true'
        },
        (payload) => {
          console.log('Real-time voice event change:', payload);
          // Reload events when changes occur
          loadEvents();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [loadEvents]);

  // Handle audio playback
  const playAudio = useCallback(async (audioUrl: string) => {
    try {
      const audio = new Audio(audioUrl);
      await audio.play();
    } catch (err) {
      console.error('Error playing audio:', err);
    }
  }, []);

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return `${date.toLocaleDateString()  } ${  date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
  };

  // Handle filter changes
  const handleFilterChange = useCallback(() => {
    loadEvents();
  }, [loadEvents]);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="w-6 h-6 animate-spin mr-2" />
        <span>Loading voice events...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-center">
          <AlertTriangle className="w-5 h-5 text-red-600 mr-2" />
          <span className="text-red-800">{error}</span>
        </div>
        <button
          onClick={loadEvents}
          className="mt-2 text-red-600 hover:text-red-800 underline"
        >
          Try again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">Voice Events</h2>
        <button
          onClick={loadEvents}
          className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          Refresh
        </button>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="bg-white p-4 rounded-lg border border-gray-200 space-y-4">
          <div className="flex items-center space-x-4">
            <Filter className="w-5 h-5 text-gray-500" />
            <span className="font-medium text-gray-700">Filters</span>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Search */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Search
              </label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onBlur={handleFilterChange}
                  onKeyPress={(e) => e.key === 'Enter' && handleFilterChange()}
                  placeholder="Search products, vendors..."
                  className="pl-10 w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
            </div>

            {/* Event Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Event Type
              </label>
              <select
                value={selectedEventType}
                onChange={(e) => {
                  setSelectedEventType(e.target.value);
                  handleFilterChange();
                }}
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              >
                <option value="">All Types</option>
                <option value="receiving">Receiving</option>
                <option value="sale">Sale</option>
                <option value="disposal">Disposal</option>
                <option value="physical_count">Physical Count</option>
              </select>
            </div>

            {/* Confidence Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Min Confidence
              </label>
              <select
                value={confidenceFilter}
                onChange={(e) => {
                  setConfidenceFilter(Number(e.target.value));
                  handleFilterChange();
                }}
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              >
                <option value={0}>All Confidence</option>
                <option value={0.9}>High (90%+)</option>
                <option value={0.7}>Medium (70%+)</option>
                <option value={0.5}>Low (50%+)</option>
              </select>
            </div>

            {/* Date Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date Range
              </label>
              <div className="flex space-x-2">
                <input
                  type="date"
                  value={dateRange.start}
                  onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                  onBlur={handleFilterChange}
                  className="flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                />
                <input
                  type="date"
                  value={dateRange.end}
                  onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                  onBlur={handleFilterChange}
                  className="flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Events List */}
      {events.length === 0 ? (
        <div className="text-center py-12">
          <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No voice events found</h3>
          <p className="text-gray-500">
            {Object.keys(filters).length > 0 
              ? 'Try adjusting your filters or create some voice events.'
              : 'Start by creating voice events through the voice interface.'
            }
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {events.map((event) => (
            <div
              key={event.id}
              className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4 flex-1">
                  {/* Event Type Icon */}
                  <div className="flex-shrink-0">
                    <EventTypeIcon eventType={event.event_type} />
                  </div>

                  {/* Event Details */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900 capitalize">
                        {event.event_type.replace('_', ' ')}
                      </h3>
                      <ConfidenceBadge 
                        score={event.voice_confidence_score} 
                        breakdown={event.voice_confidence_breakdown}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm text-gray-600">
                      <div className="flex items-center">
                        <Package className="w-4 h-4 mr-2" />
                        <span className="font-medium">{event.product_name}</span>
                      </div>
                      
                      {event.quantity && (
                        <div className="flex items-center">
                          <span className="font-medium">
                            {event.quantity} {event.unit}
                          </span>
                        </div>
                      )}

                      {event.vendor_name && (
                        <div className="flex items-center">
                          <User className="w-4 h-4 mr-2" />
                          <span>From: {event.vendor_name}</span>
                        </div>
                      )}

                      {event.customer_name && (
                        <div className="flex items-center">
                          <User className="w-4 h-4 mr-2" />
                          <span>To: {event.customer_name}</span>
                        </div>
                      )}

                      <div className="flex items-center">
                        <Calendar className="w-4 h-4 mr-2" />
                        <span>{formatDate(event.occurred_at)}</span>
                      </div>

                      <div className="flex items-center">
                        <Clock className="w-4 h-4 mr-2" />
                        <span>Created: {formatDate(event.created_at || '')}</span>
                      </div>
                    </div>

                    {/* Additional Details */}
                    {(event.condition || event.temperature || event.notes) && (
                      <div className="mt-3 pt-3 border-t border-gray-100">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                          {event.condition && (
                            <div>
                              <span className="font-medium">Condition:</span> {event.condition}
                            </div>
                          )}
                          {event.temperature && (
                            <div>
                              <span className="font-medium">Temperature:</span> {event.temperature}°{event.temperature_unit || 'F'}
                            </div>
                          )}
                          {event.notes && (
                            <div className="md:col-span-3">
                              <span className="font-medium">Notes:</span> {event.notes}
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Raw Transcript */}
                    {event.raw_transcript && (
                      <div className="mt-3 pt-3 border-t border-gray-100">
                        <details className="text-sm">
                          <summary className="cursor-pointer text-gray-500 hover:text-gray-700">
                            View original transcript
                          </summary>
                          <div className="mt-2 p-2 bg-gray-50 rounded text-gray-700 italic">
                            "{event.raw_transcript}"
                          </div>
                        </details>
                      </div>
                    )}
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center space-x-2 ml-4">
                  {event.audio_recording_url && (
                    <button
                      onClick={() => playAudio(event.audio_recording_url!)}
                      className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
                      title="Play audio recording"
                    >
                      <Volume2 className="w-4 h-4" />
                    </button>
                  )}
                  
                  {onEventEdit && (
                    <button
                      onClick={() => onEventEdit(event)}
                      className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
                      title="Edit event"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                  )}
                  
                  {onEventDelete && (
                    <button
                      onClick={() => onEventDelete(event.id!)}
                      className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-full transition-colors"
                      title="Delete event"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Load More */}
      {events.length === maxEvents && (
        <div className="text-center">
          <button
            onClick={() => {
              // Implement load more functionality
              console.log('Load more events');
            }}
            className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Load More Events
          </button>
        </div>
      )}
    </div>
  );
};

export default VoiceEventList;