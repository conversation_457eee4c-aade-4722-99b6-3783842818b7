import React, { useState, useEffect } from 'react';
import { 
  Mic, 
  Activity, 
  Clock, 
  TrendingUp, 
  TrendingDown, 
  Minus,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>gle,
  CheckCircle,
  Zap,
  Volume2,
  Wifi,
  WifiOff
} from 'lucide-react';
import { realtimeVoiceService } from '../../services/RealtimeVoiceService';
import { voiceAnalyticsService } from '../../services/VoiceAnalyticsService';

interface PerformanceMetrics {
  totalRequests: number;
  successfulRequests: number;
  averageLatency: number;
  cacheHits: number;
  cacheMisses: number;
  lastProcessingTime: number;
  audioBufferOverruns: number;
  networkFailures: number;
  cacheSize: number;
  isConnected: boolean;
  sessionId: string | null;
  configuredLatencyTarget: number;
}

interface AccuracyTrend {
  trend: 'up' | 'down' | 'stable';
  currentAverage: number;
  previousAverage: number;
  changePercentage: number;
  dailyAverages: Array<{ date: string; average: number; count: number }>;
}

interface ConfidenceDistribution {
  high: number;
  medium: number;
  low: number;
  distribution: Array<{ range: string; count: number; percentage: number }>;
}

export default function EnhancedVoiceAnalyticsDashboard() {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [accuracyTrend, setAccuracyTrend] = useState<AccuracyTrend | null>(null);
  const [confidenceDistribution, setConfidenceDistribution] = useState<ConfidenceDistribution | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTimeframe, setSelectedTimeframe] = useState<number>(7);
  const [realTimeEnabled, setRealTimeEnabled] = useState(false);

  useEffect(() => {
    loadAnalyticsData();
    
    // Set up real-time updates every 5 seconds
    const interval = setInterval(() => {
      if (realTimeEnabled) {
        loadRealTimeMetrics();
      }
    }, 5000);

    return () => clearInterval(interval);
  }, [selectedTimeframe, realTimeEnabled]);

  const loadAnalyticsData = async () => {
    setIsLoading(true);
    try {
      const [metricsData, trendData, distributionData] = await Promise.all([
        loadRealTimeMetrics(),
        voiceAnalyticsService.getAccuracyTrends(selectedTimeframe),
        voiceAnalyticsService.getConfidenceDistribution(selectedTimeframe)
      ]);

      setAccuracyTrend(trendData);
      setConfidenceDistribution(distributionData);
    } catch (error) {
      console.error('Failed to load analytics data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadRealTimeMetrics = async () => {
    try {
      const metricsData = realtimeVoiceService.getMetrics();
      setMetrics(metricsData);
      return metricsData;
    } catch (error) {
      console.error('Failed to load real-time metrics:', error);
      return null;
    }
  };

  const getLatencyStatus = (latency: number, target: number) => {
    if (latency <= target) return { status: 'excellent', color: 'text-green-600', icon: CheckCircle };
    if (latency <= target * 1.5) return { status: 'good', color: 'text-yellow-600', icon: AlertTriangle };
    return { status: 'poor', color: 'text-red-600', icon: AlertTriangle };
  };

  const getSuccessRate = (successful: number, total: number) => {
    return total > 0 ? ((successful / total) * 100).toFixed(1) : '0';
  };

  const getCacheEfficiency = (hits: number, misses: number) => {
    const total = hits + misses;
    return total > 0 ? ((hits / total) * 100).toFixed(1) : '0';
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="w-4 h-4 text-green-600" />;
      case 'down': return <TrendingDown className="w-4 h-4 text-red-600" />;
      default: return <Minus className="w-4 h-4 text-gray-600" />;
    }
  };

  if (isLoading && !metrics) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-64 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="bg-gray-100 h-24 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const latencyStatus = metrics ? getLatencyStatus(metrics.averageLatency, metrics.configuredLatencyTarget) : null;
  const successRate = metrics ? getSuccessRate(metrics.successfulRequests, metrics.totalRequests) : '0';
  const cacheEfficiency = metrics ? getCacheEfficiency(metrics.cacheHits, metrics.cacheMisses) : '0';

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <Activity className="w-6 h-6 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-900">Voice Processing Analytics</h2>
            {metrics?.isConnected ? (
              <div className="flex items-center gap-1 text-green-600">
                <Wifi className="w-4 h-4" />
                <span className="text-sm">Connected</span>
              </div>
            ) : (
              <div className="flex items-center gap-1 text-red-600">
                <WifiOff className="w-4 h-4" />
                <span className="text-sm">Disconnected</span>
              </div>
            )}
          </div>
          
          <div className="flex items-center gap-4">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={realTimeEnabled}
                onChange={(e) => setRealTimeEnabled(e.target.checked)}
                className="rounded"
              />
              <span className="text-sm text-gray-600">Real-time updates</span>
            </label>
            
            <select
              value={selectedTimeframe}
              onChange={(e) => setSelectedTimeframe(Number(e.target.value))}
              className="border rounded px-3 py-1 text-sm"
            >
              <option value={1}>Last 24 hours</option>
              <option value={7}>Last 7 days</option>
              <option value={30}>Last 30 days</option>
            </select>
          </div>
        </div>

        {/* Real-time Status */}
        {metrics && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Clock className="w-5 h-5 text-blue-600" />
                <span className="text-sm font-medium text-blue-900">Latency</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-2xl font-bold text-blue-900">
                  {metrics.averageLatency.toFixed(0)}ms
                </span>
                {latencyStatus && (
                  <latencyStatus.icon className={`w-4 h-4 ${latencyStatus.color}`} />
                )}
              </div>
              <div className="text-xs text-blue-700 mt-1">
                Target: {metrics.configuredLatencyTarget}ms
              </div>
            </div>

            <div className="bg-green-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <span className="text-sm font-medium text-green-900">Success Rate</span>
              </div>
              <div className="text-2xl font-bold text-green-900">
                {successRate}%
              </div>
              <div className="text-xs text-green-700 mt-1">
                {metrics.successfulRequests}/{metrics.totalRequests} requests
              </div>
            </div>

            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Zap className="w-5 h-5 text-purple-600" />
                <span className="text-sm font-medium text-purple-900">Cache Efficiency</span>
              </div>
              <div className="text-2xl font-bold text-purple-900">
                {cacheEfficiency}%
              </div>
              <div className="text-xs text-purple-700 mt-1">
                {metrics.cacheHits} hits, {metrics.cacheMisses} misses
              </div>
            </div>

            <div className="bg-orange-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Volume2 className="w-5 h-5 text-orange-600" />
                <span className="text-sm font-medium text-orange-900">Audio Processing</span>
              </div>
              <div className="text-2xl font-bold text-orange-900">
                {metrics.lastProcessingTime}ms
              </div>
              <div className="text-xs text-orange-700 mt-1">
                {metrics.audioBufferOverruns} overruns
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Accuracy Trends */}
      {accuracyTrend && (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center gap-2 mb-4">
            <BarChart3 className="w-5 h-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900">Accuracy Trends</h3>
            {getTrendIcon(accuracyTrend.trend)}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="text-sm text-gray-600 mb-1">Current Period</div>
              <div className="text-2xl font-bold text-gray-900">
                {(accuracyTrend.currentAverage * 100).toFixed(1)}%
              </div>
            </div>
            
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="text-sm text-gray-600 mb-1">Previous Period</div>
              <div className="text-2xl font-bold text-gray-900">
                {(accuracyTrend.previousAverage * 100).toFixed(1)}%
              </div>
            </div>
            
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="text-sm text-gray-600 mb-1">Change</div>
              <div className={`text-2xl font-bold ${
                accuracyTrend.changePercentage >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {accuracyTrend.changePercentage >= 0 ? '+' : ''}
                {accuracyTrend.changePercentage.toFixed(1)}%
              </div>
            </div>
          </div>

          {/* Daily trend chart would go here */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="text-sm text-gray-600 mb-2">Daily Performance</div>
            <div className="flex items-end gap-1 h-20">
              {accuracyTrend.dailyAverages.slice(-14).map((day, index) => {
                const height = (day.average * 80); // Scale to fit container
                return (
                  <div
                    key={index}
                    className="bg-blue-500 rounded-t flex-1 min-w-[4px] relative group cursor-pointer"
                    style={{ height: `${height}px` }}
                    title={`${day.date}: ${(day.average * 100).toFixed(1)}% (${day.count} events)`}
                  >
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 whitespace-nowrap mb-1">
                      {day.date}: {(day.average * 100).toFixed(1)}%
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}

      {/* Confidence Distribution */}
      {confidenceDistribution && (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center gap-2 mb-4">
            <PieChart className="w-5 h-5 text-green-600" />
            <h3 className="text-lg font-semibold text-gray-900">Confidence Distribution</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-3 h-3 bg-green-500 rounded"></div>
                <span className="text-sm font-medium text-green-900">High Confidence</span>
              </div>
              <div className="text-2xl font-bold text-green-900">
                {confidenceDistribution.high}
              </div>
              <div className="text-xs text-green-700">
                90-100% confidence
              </div>
            </div>

            <div className="bg-yellow-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-3 h-3 bg-yellow-500 rounded"></div>
                <span className="text-sm font-medium text-yellow-900">Medium Confidence</span>
              </div>
              <div className="text-2xl font-bold text-yellow-900">
                {confidenceDistribution.medium}
              </div>
              <div className="text-xs text-yellow-700">
                70-89% confidence
              </div>
            </div>

            <div className="bg-red-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-3 h-3 bg-red-500 rounded"></div>
                <span className="text-sm font-medium text-red-900">Low Confidence</span>
              </div>
              <div className="text-2xl font-bold text-red-900">
                {confidenceDistribution.low}
              </div>
              <div className="text-xs text-red-700">
                Below 70% confidence
              </div>
            </div>
          </div>

          {/* Confidence distribution bar */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="text-sm text-gray-600 mb-2">Distribution Breakdown</div>
            <div className="space-y-2">
              {confidenceDistribution.distribution.map((item, index) => (
                <div key={index} className="flex items-center gap-3">
                  <div className="w-20 text-xs text-gray-600">{item.range}</div>
                  <div className="flex-1 bg-gray-200 rounded-full h-4 relative">
                    <div
                      className={`h-4 rounded-full ${
                        index === 0 ? 'bg-green-500' : 
                        index === 1 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${item.percentage}%` }}
                    ></div>
                  </div>
                  <div className="w-12 text-xs text-gray-600 text-right">
                    {item.percentage.toFixed(1)}%
                  </div>
                  <div className="w-8 text-xs text-gray-600 text-right">
                    {item.count}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Performance Recommendations */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center gap-2 mb-4">
          <AlertTriangle className="w-5 h-5 text-yellow-600" />
          <h3 className="text-lg font-semibold text-gray-900">Performance Recommendations</h3>
        </div>

        <div className="space-y-3">
          {metrics && metrics.averageLatency > metrics.configuredLatencyTarget && (
            <div className="bg-yellow-50 border border-yellow-200 p-3 rounded-lg">
              <div className="font-medium text-yellow-900">High Latency Detected</div>
              <div className="text-sm text-yellow-800 mt-1">
                Average latency ({metrics.averageLatency.toFixed(0)}ms) exceeds target ({metrics.configuredLatencyTarget}ms). 
                Consider checking network connectivity or reducing audio processing complexity.
              </div>
            </div>
          )}

          {metrics && (metrics.cacheMisses / (metrics.cacheHits + metrics.cacheMisses)) > 0.7 && (
            <div className="bg-blue-50 border border-blue-200 p-3 rounded-lg">
              <div className="font-medium text-blue-900">Cache Optimization Opportunity</div>
              <div className="text-sm text-blue-800 mt-1">
                Cache efficiency is below optimal levels. Consider increasing cache size or 
                improving caching strategies for common seafood terms.
              </div>
            </div>
          )}

          {metrics && metrics.audioBufferOverruns > 0 && (
            <div className="bg-red-50 border border-red-200 p-3 rounded-lg">
              <div className="font-medium text-red-900">Audio Buffer Issues</div>
              <div className="text-sm text-red-800 mt-1">
                Audio buffer overruns detected ({metrics.audioBufferOverruns}). 
                This may indicate insufficient processing power or network issues.
              </div>
            </div>
          )}

          {confidenceDistribution && confidenceDistribution.low > confidenceDistribution.high && (
            <div className="bg-orange-50 border border-orange-200 p-3 rounded-lg">
              <div className="font-medium text-orange-900">Accuracy Improvement Needed</div>
              <div className="text-sm text-orange-800 mt-1">
                High number of low-confidence results. Consider retraining voice models with 
                more seafood industry-specific data or improving microphone setup.
              </div>
            </div>
          )}

          {accuracyTrend && accuracyTrend.trend === 'down' && (
            <div className="bg-red-50 border border-red-200 p-3 rounded-lg">
              <div className="font-medium text-red-900">Declining Accuracy Trend</div>
              <div className="text-sm text-red-800 mt-1">
                Voice recognition accuracy has been declining. Review recent changes 
                and consider system maintenance or model updates.
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}