import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Wifi, Wifi<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>f<PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react';
import { VoiceError, VoiceErrorType } from '../../services/VoiceErrorHandler';

interface VoiceErrorDisplayProps {
  error?: VoiceError;
  onRetry?: () => void;
  onDismiss?: () => void;
  onUseManualInput?: () => void;
  isRetrying?: boolean;
}

const VoiceErrorDisplay: React.FC<VoiceErrorDisplayProps> = ({
  error,
  onRetry,
  onDismiss,
  onUseManualInput,
  isRetrying = false
}) => {
  // If no error is provided, don't render anything
  if (!error) {
    return null;
  }
  const getErrorIcon = () => {
    switch (error.type) {
      case VoiceErrorType.NETWORK_ERROR:
        return <WifiOff className="w-6 h-6 text-red-500" />;
      case VoiceErrorType.AUDIO_PROCESSING_ERROR:
      case VoiceErrorType.PERMISSION_ERROR:
        return <MicOff className="w-6 h-6 text-red-500" />;
      case VoiceErrorType.TRANSCRIPTION_ERROR:
        return <Mic className="w-6 h-6 text-yellow-500" />;
      default:
        return <AlertTriangle className="w-6 h-6 text-red-500" />;
    }
  };

  const getErrorColor = () => {
    if (error.recoverable) {
      return error.type === VoiceErrorType.TRANSCRIPTION_ERROR 
        ? 'border-yellow-200 bg-yellow-50' 
        : 'border-orange-200 bg-orange-50';
    }
    return 'border-red-200 bg-red-50';
  };

  const getHeaderColor = () => {
    if (error.recoverable) {
      return error.type === VoiceErrorType.TRANSCRIPTION_ERROR 
        ? 'text-yellow-800' 
        : 'text-orange-800';
    }
    return 'text-red-800';
  };

  const getTextColor = () => {
    if (error.recoverable) {
      return error.type === VoiceErrorType.TRANSCRIPTION_ERROR 
        ? 'text-yellow-700' 
        : 'text-orange-700';
    }
    return 'text-red-700';
  };

  return (
    <div className={`border rounded-lg p-4 ${getErrorColor()}`}>
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3 flex-1">
          <div className="flex-shrink-0 mt-1">
            {getErrorIcon()}
          </div>
          
          <div className="flex-1">
            <h3 className={`font-medium ${getHeaderColor()}`}>
              {error.userMessage}
            </h3>
            
            <div className={`mt-2 text-sm ${getTextColor()}`}>
              <ul className="space-y-1">
                {error.actionSteps.map((step, index) => (
                  <li key={index} className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>{step}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Show retry timer if applicable */}
            {error.retryAfter && (
              <div className={`mt-2 text-xs ${getTextColor()}`}>
                Automatic retry in {error.retryAfter} seconds
              </div>
            )}
          </div>
        </div>

        {onDismiss && (
          <button
            type="button"
            onClick={onDismiss}
            className={`flex-shrink-0 ml-2 p-1 rounded-md hover:bg-gray-100 ${getTextColor()}`}
          >
            <X className="w-4 h-4" />
          </button>
        )}
      </div>

      {/* Action buttons */}
      <div className="mt-4 flex flex-wrap gap-2">
        {error.recoverable && onRetry && (
          <button
            type="button"
            onClick={onRetry}
            disabled={isRetrying}
            className="inline-flex items-center px-3 py-2 text-sm font-medium rounded-md bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isRetrying ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                Retrying...
              </>
            ) : (
              <>
                <RefreshCw className="w-4 h-4 mr-2" />
                Try Again
              </>
            )}
          </button>
        )}

        {onUseManualInput && (
          <button
            type="button"
            onClick={onUseManualInput}
            className="inline-flex items-center px-3 py-2 text-sm font-medium rounded-md bg-blue-600 text-white hover:bg-blue-700"
          >
            Use Manual Input
          </button>
        )}

        {error.type === VoiceErrorType.PERMISSION_ERROR && (
          <button
            type="button"
            onClick={() => window.location.reload()}
            className="inline-flex items-center px-3 py-2 text-sm font-medium rounded-md bg-blue-600 text-white hover:bg-blue-700"
          >
            Refresh Page
          </button>
        )}
      </div>

      {/* Connection status for network errors */}
      {error.type === VoiceErrorType.NETWORK_ERROR && (
        <div className="mt-3 flex items-center text-sm">
          {navigator.onLine ? (
            <>
              <Wifi className="w-4 h-4 text-green-500 mr-2" />
              <span className="text-green-700">Connection restored - voice input will be processed automatically</span>
            </>
          ) : (
            <>
              <WifiOff className="w-4 h-4 text-red-500 mr-2" />
              <span className={`${getTextColor()}`}>Currently offline - voice input will be processed when connection is restored</span>
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default VoiceErrorDisplay;