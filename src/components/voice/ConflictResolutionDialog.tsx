import React from 'react';
import { AlertTriangle } from 'lucide-react';

interface ConflictResolutionDialogProps {
  show: boolean;
  onResolve: () => void;
  onCancel: () => void;
}

export const ConflictResolutionDialog: React.FC<ConflictResolutionDialogProps> = ({
  show,
  onResolve,
  onCancel
}) => {
  if (!show) return null;

  return (
    <div className="mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
      <div className="flex items-start">
        <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5 mr-3" />
        <div className="flex-1">
          <h3 className="text-sm font-medium text-yellow-800">Conflict Detected</h3>
          <p className="mt-1 text-sm text-yellow-700">
            This event has been modified by another user. Please review the changes and resolve conflicts.
          </p>
          <div className="mt-3 flex space-x-3">
            <button
              onClick={onResolve}
              className="text-sm bg-yellow-100 text-yellow-800 px-3 py-1 rounded hover:bg-yellow-200 transition-colors"
            >
              Accept My Changes
            </button>
            <button
              onClick={onCancel}
              className="text-sm text-yellow-700 hover:text-yellow-800 transition-colors"
            >
              Cancel Edit
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConflictResolutionDialog;