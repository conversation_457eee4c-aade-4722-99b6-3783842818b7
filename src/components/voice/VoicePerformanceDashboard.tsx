import React, { useState, useEffect } from 'react';
import { Activity, Database, Zap, Clock, TrendingUp, AlertTriangle, CheckCircle } from 'lucide-react';
import { voicePerformanceMonitor } from '../../services/VoicePerformanceMonitor';
import { voiceEventCache } from '../../services/VoiceEventCache';
import { optimizedVoiceEventService } from '../../services/OptimizedVoiceEventService';

interface PerformanceMetrics {
  voiceProcessing: any;
  transcription: any;
  aiProcessing: any;
  database: any;
  cacheHitRate: number;
  alertCount: number;
  systemHealth: number;
}

const VoicePerformanceDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [cacheStats, setCacheStats] = useState<any>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    // Initial load
    refreshMetrics();

    // Auto-refresh every 5 seconds
    const interval = setInterval(refreshMetrics, 5000);

    return () => clearInterval(interval);
  }, []);

  const refreshMetrics = async () => {
    setIsRefreshing(true);
    try {
      const performanceData = voicePerformanceMonitor.getDashboardData();
      const cacheData = voiceEventCache.getStats();
      
      setMetrics(performanceData);
      setCacheStats(cacheData);
    } catch (error) {
      console.error('Failed to refresh metrics:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const getHealthColor = (health: number): string => {
    if (health >= 90) return 'text-green-600 bg-green-100';
    if (health >= 70) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const getHealthIcon = (health: number) => {
    if (health >= 90) return <CheckCircle className="w-5 h-5" />;
    if (health >= 70) return <TrendingUp className="w-5 h-5" />;
    return <AlertTriangle className="w-5 h-5" />;
  };

  const formatLatency = (ms: number): string => {
    if (ms < 1000) return `${Math.round(ms)}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const formatPercentage = (value: number): string => {
    return `${Math.round(value * 100)}%`;
  };

  if (!metrics || !cacheStats) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Activity className="w-6 h-6 text-blue-600" />
          <h2 className="text-xl font-semibold text-gray-900">Voice Performance</h2>
        </div>
        
        <button
          type="button"
          onClick={refreshMetrics}
          disabled={isRefreshing}
          className="flex items-center space-x-2 px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50"
        >
          <Activity className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
          <span>Refresh</span>
        </button>
      </div>

      {/* System Health Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">System Health</p>
              <p className="text-2xl font-bold text-gray-900">{Math.round(metrics.systemHealth)}</p>
            </div>
            <div className={`p-3 rounded-full ${getHealthColor(metrics.systemHealth)}`}>
              {getHealthIcon(metrics.systemHealth)}
            </div>
          </div>
          <div className="mt-2">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${
                  metrics.systemHealth >= 90 ? 'bg-green-500' :
                  metrics.systemHealth >= 70 ? 'bg-yellow-500' : 'bg-red-500'
                }`}
                style={{ width: `${metrics.systemHealth}%` }}
              ></div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Cache Hit Rate</p>
              <p className="text-2xl font-bold text-gray-900">{formatPercentage(metrics.cacheHitRate)}</p>
            </div>
            <div className="p-3 rounded-full bg-blue-100 text-blue-600">
              <Database className="w-5 h-5" />
            </div>
          </div>
          <p className="text-xs text-gray-500 mt-1">
            {cacheStats.entries} cached entries
          </p>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Latency</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatLatency(metrics.voiceProcessing.averageLatency)}
              </p>
            </div>
            <div className="p-3 rounded-full bg-purple-100 text-purple-600">
              <Clock className="w-5 h-5" />
            </div>
          </div>
          <p className="text-xs text-gray-500 mt-1">
            P95: {formatLatency(metrics.voiceProcessing.p95Latency)}
          </p>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Success Rate</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatPercentage(metrics.voiceProcessing.successRate)}
              </p>
            </div>
            <div className="p-3 rounded-full bg-green-100 text-green-600">
              <CheckCircle className="w-5 h-5" />
            </div>
          </div>
          <p className="text-xs text-gray-500 mt-1">
            {metrics.voiceProcessing.totalOperations} operations
          </p>
        </div>
      </div>

      {/* Detailed Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Latency Breakdown */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Latency Breakdown</h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Transcription</span>
              <div className="flex items-center space-x-2">
                <div className="w-32 bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-500 h-2 rounded-full"
                    style={{ 
                      width: `${Math.min(100, (metrics.transcription.averageLatency / 2000) * 100)}%` 
                    }}
                  ></div>
                </div>
                <span className="text-sm font-medium text-gray-900 w-16 text-right">
                  {formatLatency(metrics.transcription.averageLatency)}
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">AI Processing</span>
              <div className="flex items-center space-x-2">
                <div className="w-32 bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-purple-500 h-2 rounded-full"
                    style={{ 
                      width: `${Math.min(100, (metrics.aiProcessing.averageLatency / 2000) * 100)}%` 
                    }}
                  ></div>
                </div>
                <span className="text-sm font-medium text-gray-900 w-16 text-right">
                  {formatLatency(metrics.aiProcessing.averageLatency)}
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Database</span>
              <div className="flex items-center space-x-2">
                <div className="w-32 bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-green-500 h-2 rounded-full"
                    style={{ 
                      width: `${Math.min(100, (metrics.database.averageLatency / 500) * 100)}%` 
                    }}
                  ></div>
                </div>
                <span className="text-sm font-medium text-gray-900 w-16 text-right">
                  {formatLatency(metrics.database.averageLatency)}
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between pt-2 border-t border-gray-200">
              <span className="text-sm font-medium text-gray-700">Total</span>
              <div className="flex items-center space-x-2">
                <div className="w-32 bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-orange-500 h-2 rounded-full"
                    style={{ 
                      width: `${Math.min(100, (metrics.voiceProcessing.averageLatency / 3000) * 100)}%` 
                    }}
                  ></div>
                </div>
                <span className="text-sm font-bold text-gray-900 w-16 text-right">
                  {formatLatency(metrics.voiceProcessing.averageLatency)}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Cache Performance */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Cache Performance</h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Hit Rate</span>
              <span className="text-lg font-semibold text-green-600">
                {formatPercentage(cacheStats.hitRate)}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Cache Hits</span>
              <span className="text-sm font-medium text-gray-900">
                {cacheStats.hits.toLocaleString()}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Cache Misses</span>
              <span className="text-sm font-medium text-gray-900">
                {cacheStats.misses.toLocaleString()}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Entries</span>
              <span className="text-sm font-medium text-gray-900">
                {cacheStats.entries.toLocaleString()}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Memory Usage</span>
              <span className="text-sm font-medium text-gray-900">
                {(cacheStats.memoryUsage / 1024 / 1024).toFixed(1)} MB
              </span>
            </div>

            {/* Cache Hit Rate Visualization */}
            <div className="pt-2 border-t border-gray-200">
              <div className="flex items-center justify-between mb-2">
                <span className="text-xs text-gray-500">Hit Rate Distribution</span>
              </div>
              <div className="flex rounded-full overflow-hidden h-2">
                <div
                  className="bg-green-500"
                  style={{ width: `${cacheStats.hitRate * 100}%` }}
                ></div>
                <div
                  className="bg-red-500"
                  style={{ width: `${(1 - cacheStats.hitRate) * 100}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Alerts */}
      {metrics.alertCount > 0 && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="w-5 h-5 text-yellow-600" />
            <h4 className="text-sm font-medium text-yellow-800">Performance Alerts</h4>
          </div>
          <p className="text-sm text-yellow-700 mt-1">
            {metrics.alertCount} performance threshold{metrics.alertCount !== 1 ? 's' : ''} exceeded in the last 5 minutes.
            Check the console for detailed information.
          </p>
        </div>
      )}

      {/* Performance Tips */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center space-x-2 mb-2">
          <Zap className="w-5 h-5 text-blue-600" />
          <h4 className="text-sm font-medium text-blue-800">Performance Tips</h4>
        </div>
        <ul className="text-sm text-blue-700 space-y-1">
          {metrics.cacheHitRate < 0.7 && (
            <li>• Consider pre-loading common voice event queries to improve cache hit rate</li>
          )}
          {metrics.voiceProcessing.averageLatency > 2000 && (
            <li>• Voice processing latency is high - check network connectivity and API limits</li>
          )}
          {metrics.database.averageLatency > 200 && (
            <li>• Database queries are slow - consider adding indexes or optimizing queries</li>
          )}
          {metrics.voiceProcessing.successRate < 0.95 && (
            <li>• Voice processing success rate is low - check error handling and retry logic</li>
          )}
        </ul>
      </div>
    </div>
  );
};

export default VoicePerformanceDashboard;