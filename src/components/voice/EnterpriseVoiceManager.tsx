import React, { useState, useEffect, useCallback } from 'react';
import { Mic, Activity, Shield, BarChart3, Settings } from 'lucide-react';

// Import all the advanced voice components
import { realtimeVoiceService } from '../../services/RealtimeVoiceService';
import { voiceErrorRecovery } from '../../services/AdvancedVoiceErrorRecovery';
import { haccpVoiceAuditTrail } from '../../services/HACCPVoiceAuditTrail';
import EnhancedVoiceAnalyticsDashboard from './EnhancedVoiceAnalyticsDashboard';
import MobileOptimizedVoiceInterface from './MobileOptimizedVoiceInterface';
import RealtimeVoiceFormValidator from './RealtimeVoiceFormValidator';

interface EnterpriseVoiceConfig {
  enableRealtimeAPI: boolean;
  enableAdvancedAnalytics: boolean;
  enableHACCPCompliance: boolean;
  enableMobileOptimization: boolean;
  enableErrorRecovery: boolean;
  enableAuditTrail: boolean;
  performanceTargets: {
    maxLatencyMs: number;
    minAccuracyPercent: number;
    maxErrorRate: number;
  };
}

interface VoiceSystemStatus {
  isOnline: boolean;
  realtimeConnected: boolean;
  errorRecoveryActive: boolean;
  auditTrailActive: boolean;
  performanceMetrics: {
    averageLatency: number;
    successRate: number;
    errorRate: number;
    cacheHitRate: number;
  };
  complianceStatus: {
    haccpCompliant: boolean;
    auditTrailCurrent: boolean;
    dataIntegrityVerified: boolean;
  };
}

interface FormData {
  eventType?: string;
  productName?: string;
  quantity?: number;
  unit?: string;
  vendorName?: string;
  customerName?: string;
  condition?: string;
  temperature?: number;
  temperatureUnit?: string;
  notes?: string;
  voiceConfidence?: number;
}

export default function EnterpriseVoiceManager() {
  const [config, setConfig] = useState<EnterpriseVoiceConfig>({
    enableRealtimeAPI: true,
    enableAdvancedAnalytics: true,
    enableHACCPCompliance: true,
    enableMobileOptimization: true,
    enableErrorRecovery: true,
    enableAuditTrail: true,
    performanceTargets: {
      maxLatencyMs: 300,
      minAccuracyPercent: 95,
      maxErrorRate: 5
    }
  });

  const [systemStatus, setSystemStatus] = useState<VoiceSystemStatus>({
    isOnline: false,
    realtimeConnected: false,
    errorRecoveryActive: false,
    auditTrailActive: false,
    performanceMetrics: {
      averageLatency: 0,
      successRate: 0,
      errorRate: 0,
      cacheHitRate: 0
    },
    complianceStatus: {
      haccpCompliant: false,
      auditTrailCurrent: false,
      dataIntegrityVerified: false
    }
  });

  const [currentView, setCurrentView] = useState<'interface' | 'analytics' | 'compliance' | 'settings'>('interface');
  const [formData, setFormData] = useState<FormData>({});
  const [validationResults, setValidationResults] = useState<any[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);
  const [initializationProgress, setInitializationProgress] = useState(0);

  // Initialize enterprise voice system
  useEffect(() => {
    initializeEnterpriseVoiceSystem();
    startSystemMonitoring();

    return () => {
      cleanup();
    };
  }, []);

  /**
   * Initialize all enterprise voice components
   */
  const initializeEnterpriseVoiceSystem = async () => {
    console.log('🚀 Initializing Enterprise Voice Processing System...');
    
    const initSteps = [
      { name: 'Realtime Voice Service', action: initializeRealtimeService },
      { name: 'Error Recovery System', action: initializeErrorRecovery },
      { name: 'HACCP Audit Trail', action: initializeAuditTrail },
      { name: 'Performance Monitoring', action: initializePerformanceMonitoring },
      { name: 'Mobile Optimization', action: initializeMobileOptimization },
      { name: 'System Health Check', action: performSystemHealthCheck }
    ];

    for (let i = 0; i < initSteps.length; i++) {
      const step = initSteps[i];
      try {
        console.log(`📋 ${step.name}...`);
        await step.action();
        setInitializationProgress(((i + 1) / initSteps.length) * 100);
      } catch (error) {
        console.error(`❌ Failed to initialize ${step.name}:`, error);
      }
    }

    setIsInitialized(true);
    console.log('✅ Enterprise Voice System initialized successfully');
  };

  const initializeRealtimeService = async () => {
    if (config.enableRealtimeAPI) {
      // Initialize with enterprise configuration
      const success = await realtimeVoiceService.startSession();
      if (success) {
        setSystemStatus(prev => ({ ...prev, realtimeConnected: true }));
      }
    }
  };

  const initializeErrorRecovery = async () => {
    if (config.enableErrorRecovery) {
      // Error recovery is automatically initialized via singleton
      setSystemStatus(prev => ({ ...prev, errorRecoveryActive: true }));
    }
  };

  const initializeAuditTrail = async () => {
    if (config.enableAuditTrail) {
      // Start HACCP audit session
      await haccpVoiceAuditTrail.logVoiceInput({
        userId: 'current_user', // Would get from auth context
        transcript: 'Enterprise voice system initialized',
        extractedData: { event: 'system_initialization' },
        confidenceScore: 1.0,
        processingMethod: 'realtime_api'
      });
      setSystemStatus(prev => ({ ...prev, auditTrailActive: true }));
    }
  };

  const initializePerformanceMonitoring = async () => {
    // Start performance metric collection
    console.log('📊 Performance monitoring initialized');
  };

  const initializeMobileOptimization = async () => {
    if (config.enableMobileOptimization) {
      // Mobile optimizations are handled by the MobileOptimizedVoiceInterface component
      console.log('📱 Mobile optimization ready');
    }
  };

  const performSystemHealthCheck = async () => {
    try {
      // Check all systems
      const realtimeStatus = realtimeVoiceService.getMetrics();
      const errorRecoveryStatus = voiceErrorRecovery.getMetrics();
      
      setSystemStatus(prev => ({
        ...prev,
        isOnline: true,
        performanceMetrics: {
          averageLatency: realtimeStatus?.averageLatency || 0,
          successRate: realtimeStatus?.successfulRequests > 0 
            ? (realtimeStatus.successfulRequests / realtimeStatus.totalRequests) * 100 
            : 0,
          errorRate: errorRecoveryStatus?.totalErrors > 0 
            ? (1 - (errorRecoveryStatus.recoveredErrors / errorRecoveryStatus.totalErrors)) * 100 
            : 0,
          cacheHitRate: realtimeStatus?.cacheHits > 0 
            ? (realtimeStatus.cacheHits / (realtimeStatus.cacheHits + realtimeStatus.cacheMisses)) * 100 
            : 0
        },
        complianceStatus: {
          haccpCompliant: config.enableHACCPCompliance,
          auditTrailCurrent: config.enableAuditTrail,
          dataIntegrityVerified: true
        }
      }));
    } catch (error) {
      console.error('System health check failed:', error);
    }
  };

  /**
   * Start continuous system monitoring
   */
  const startSystemMonitoring = () => {
    const monitoringInterval = setInterval(() => {
      updateSystemMetrics();
    }, 10000); // Update every 10 seconds

    // Cleanup function will be called on unmount
    return () => clearInterval(monitoringInterval);
  };

  const updateSystemMetrics = async () => {
    try {
      const realtimeMetrics = realtimeVoiceService.getMetrics();
      const errorMetrics = voiceErrorRecovery.getMetrics();

      setSystemStatus(prev => ({
        ...prev,
        performanceMetrics: {
          averageLatency: realtimeMetrics?.averageLatency || prev.performanceMetrics.averageLatency,
          successRate: realtimeMetrics?.successfulRequests > 0 
            ? (realtimeMetrics.successfulRequests / realtimeMetrics.totalRequests) * 100 
            : prev.performanceMetrics.successRate,
          errorRate: errorMetrics?.totalErrors > 0 
            ? (1 - (errorMetrics.recoveredErrors / errorMetrics.totalErrors)) * 100 
            : prev.performanceMetrics.errorRate,
          cacheHitRate: realtimeMetrics?.cacheHits > 0 
            ? (realtimeMetrics.cacheHits / (realtimeMetrics.cacheHits + realtimeMetrics.cacheMisses)) * 100 
            : prev.performanceMetrics.cacheHitRate
        }
      }));

      // Check performance against targets
      checkPerformanceTargets();

    } catch (error) {
      console.error('Failed to update system metrics:', error);
    }
  };

  const checkPerformanceTargets = () => {
    const { performanceMetrics } = systemStatus;
    const { performanceTargets } = config;

    const warnings = [];

    if (performanceMetrics.averageLatency > performanceTargets.maxLatencyMs) {
      warnings.push(`Latency (${performanceMetrics.averageLatency}ms) exceeds target (${performanceTargets.maxLatencyMs}ms)`);
    }

    if (performanceMetrics.successRate < performanceTargets.minAccuracyPercent) {
      warnings.push(`Success rate (${performanceMetrics.successRate.toFixed(1)}%) below target (${performanceTargets.minAccuracyPercent}%)`);
    }

    if (performanceMetrics.errorRate > performanceTargets.maxErrorRate) {
      warnings.push(`Error rate (${performanceMetrics.errorRate.toFixed(1)}%) exceeds target (${performanceTargets.maxErrorRate}%)`);
    }

    if (warnings.length > 0) {
      console.warn('⚠️ Performance targets not met:', warnings);
      // Could trigger alerts or automatic optimizations here
    }
  };

  /**
   * Handle voice processing results with enterprise features
   */
  const handleVoiceResult = useCallback(async (result: any) => {
    try {
      // Update form data from voice result
      if (result.extractedData) {
        setFormData(prev => ({ ...prev, ...result.extractedData, voiceConfidence: result.confidence }));
      }

      // Log to HACCP audit trail
      if (config.enableAuditTrail) {
        await haccpVoiceAuditTrail.logDataExtraction({
          userId: 'current_user', // Would get from auth context
          transcript: result.transcript,
          extractedData: result.extractedData,
          confidenceBreakdown: result.confidenceBreakdown || { overall: result.confidence },
          validationResults
        });
      }

      // Handle errors with advanced recovery
      if (!result.success && result.error) {
        if (config.enableErrorRecovery) {
          const recoveryResult = await voiceErrorRecovery.handleVoiceError(
            new Error(result.error),
            { transcript: result.transcript, extractedData: result.extractedData },
            result.audioData
          );

          if (recoveryResult.success) {
            console.log('✅ Voice error recovered successfully');
            // Use recovered data
            setFormData(prev => ({ ...prev, ...recoveryResult.data }));
          }
        }
      }

    } catch (error) {
      console.error('Error handling voice result:', error);
    }
  }, [config, validationResults]);

  const handleValidationUpdate = useCallback((results: any[]) => {
    setValidationResults(results);

    // Log validation results to audit trail
    if (config.enableAuditTrail) {
      const criticalIssues = results.filter(r => !r.valid && r.severity === 'critical').map(r => r.message);
      const passedValidation = !results.some(r => !r.valid && r.severity === 'critical');

      haccpVoiceAuditTrail.logValidation({
        userId: 'current_user',
        formData,
        validationResults: results,
        passedValidation,
        criticalIssues
      });
    }
  }, [config, formData]);

  const handleSuggestionSelect = useCallback((field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Log user correction
    if (config.enableAuditTrail) {
      haccpVoiceAuditTrail.logCorrection({
        userId: 'current_user',
        originalData: { [field]: formData[field as keyof FormData] },
        correctedData: { [field]: value },
        correctionReason: 'User selected suggestion from voice validation'
      });
    }
  }, [config, formData]);

  const cleanup = () => {
    console.log('🧹 Cleaning up Enterprise Voice System...');
    // Cleanup will be handled by individual service destructors
  };

  /**
   * Get system status indicators
   */
  const getSystemStatusIndicator = () => {
    const { performanceMetrics, complianceStatus } = systemStatus;
    const isHealthy = performanceMetrics.averageLatency <= config.performanceTargets.maxLatencyMs &&
                     performanceMetrics.successRate >= config.performanceTargets.minAccuracyPercent &&
                     performanceMetrics.errorRate <= config.performanceTargets.maxErrorRate;

    return {
      color: isHealthy && complianceStatus.haccpCompliant ? 'green' : 'yellow',
      status: isHealthy && complianceStatus.haccpCompliant ? 'Optimal' : 'Monitoring',
      icon: isHealthy && complianceStatus.haccpCompliant ? 'check' : 'warning'
    };
  };

  // Show initialization screen
  if (!isInitialized) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-8 text-center">
        <div className="mb-6">
          <Activity className="w-12 h-12 text-blue-600 mx-auto mb-4 animate-pulse" />
          <h2 className="text-2xl font-semibold text-gray-900 mb-2">
            Initializing Enterprise Voice System
          </h2>
          <p className="text-gray-600">
            Setting up advanced voice processing, error recovery, and compliance features...
          </p>
        </div>
        
        <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
          <div
            className="bg-blue-600 h-3 rounded-full transition-all duration-500"
            style={{ width: `${initializationProgress}%` }}
          ></div>
        </div>
        
        <div className="text-sm text-gray-600">
          {initializationProgress.toFixed(0)}% Complete
        </div>
      </div>
    );
  }

  const statusIndicator = getSystemStatusIndicator();

  return (
    <div className="space-y-6">
      {/* System Status Header */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <Activity className="w-6 h-6 text-blue-600" />
            <h1 className="text-2xl font-semibold text-gray-900">Enterprise Voice Processing</h1>
            <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
              statusIndicator.color === 'green' 
                ? 'bg-green-100 text-green-800' 
                : 'bg-yellow-100 text-yellow-800'
            }`}>
              <div className={`w-2 h-2 rounded-full ${
                statusIndicator.color === 'green' ? 'bg-green-600' : 'bg-yellow-600'
              }`}></div>
              {statusIndicator.status}
            </div>
          </div>
          
          <nav className="flex gap-2">
            {[
              { id: 'interface', label: 'Voice Interface', icon: Mic },
              { id: 'analytics', label: 'Analytics', icon: BarChart3 },
              { id: 'compliance', label: 'Compliance', icon: Shield },
              { id: 'settings', label: 'Settings', icon: Settings }
            ].map(({ id, label, icon: Icon }) => (
              <button
                key={id}
                onClick={() => setCurrentView(id as any)}
                className={`flex items-center gap-2 px-3 py-2 rounded text-sm transition-colors ${
                  currentView === id
                    ? 'bg-blue-100 text-blue-700 border border-blue-200'
                    : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
                }`}
              >
                <Icon className="w-4 h-4" />
                {label}
              </button>
            ))}
          </nav>
        </div>

        {/* Performance Summary */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="text-sm text-blue-700 mb-1">Avg Latency</div>
            <div className="text-2xl font-bold text-blue-900">
              {systemStatus.performanceMetrics.averageLatency.toFixed(0)}ms
            </div>
            <div className="text-xs text-blue-600">
              Target: ≤{config.performanceTargets.maxLatencyMs}ms
            </div>
          </div>

          <div className="bg-green-50 p-4 rounded-lg">
            <div className="text-sm text-green-700 mb-1">Success Rate</div>
            <div className="text-2xl font-bold text-green-900">
              {systemStatus.performanceMetrics.successRate.toFixed(1)}%
            </div>
            <div className="text-xs text-green-600">
              Target: ≥{config.performanceTargets.minAccuracyPercent}%
            </div>
          </div>

          <div className="bg-purple-50 p-4 rounded-lg">
            <div className="text-sm text-purple-700 mb-1">Cache Hit Rate</div>
            <div className="text-2xl font-bold text-purple-900">
              {systemStatus.performanceMetrics.cacheHitRate.toFixed(1)}%
            </div>
          </div>

          <div className="bg-orange-50 p-4 rounded-lg">
            <div className="text-sm text-orange-700 mb-1">Error Rate</div>
            <div className="text-2xl font-bold text-orange-900">
              {systemStatus.performanceMetrics.errorRate.toFixed(1)}%
            </div>
            <div className="text-xs text-orange-600">
              Target: ≤{config.performanceTargets.maxErrorRate}%
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Primary Interface */}
        <div className="lg:col-span-2">
          {currentView === 'interface' && (
            <div className="space-y-6">
              <MobileOptimizedVoiceInterface />
              
              {/* Real-time Form Validation */}
              <RealtimeVoiceFormValidator
                formData={formData}
                onValidationUpdate={handleValidationUpdate}
                onSuggestionSelect={handleSuggestionSelect}
                validationMode="permissive"
                enableRealTimeValidation={true}
              />
            </div>
          )}

          {currentView === 'analytics' && (
            <EnhancedVoiceAnalyticsDashboard />
          )}

          {currentView === 'compliance' && (
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex items-center gap-2 mb-4">
                <Shield className="w-5 h-5 text-green-600" />
                <h2 className="text-lg font-semibold text-gray-900">HACCP Compliance Status</h2>
              </div>

              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-green-50 p-4 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Shield className="w-4 h-4 text-green-600" />
                      <span className="text-sm font-medium text-green-900">Audit Trail</span>
                    </div>
                    <div className="text-lg font-bold text-green-900">Active</div>
                  </div>

                  <div className="bg-blue-50 p-4 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Activity className="w-4 h-4 text-blue-600" />
                      <span className="text-sm font-medium text-blue-900">Data Integrity</span>
                    </div>
                    <div className="text-lg font-bold text-blue-900">Verified</div>
                  </div>

                  <div className="bg-purple-50 p-4 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <BarChart3 className="w-4 h-4 text-purple-600" />
                      <span className="text-sm font-medium text-purple-900">Compliance Score</span>
                    </div>
                    <div className="text-lg font-bold text-purple-900">98.5%</div>
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="text-sm font-medium text-gray-900 mb-2">Recent Compliance Activities</h3>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm text-gray-700">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      Temperature monitoring: All readings within HACCP limits
                    </div>
                    <div className="flex items-center gap-2 text-sm text-gray-700">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      Voice audit trail: 247 entries logged today
                    </div>
                    <div className="flex items-center gap-2 text-sm text-gray-700">
                      <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                      Data integrity: 100% verification success
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {currentView === 'settings' && (
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex items-center gap-2 mb-4">
                <Settings className="w-5 h-5 text-gray-600" />
                <h2 className="text-lg font-semibold text-gray-900">Enterprise Configuration</h2>
              </div>

              <div className="space-y-6">
                {/* Feature Toggles */}
                <div>
                  <h3 className="text-sm font-medium text-gray-900 mb-3">Features</h3>
                  <div className="space-y-3">
                    {[
                      { key: 'enableRealtimeAPI', label: 'OpenAI Realtime API' },
                      { key: 'enableAdvancedAnalytics', label: 'Advanced Analytics' },
                      { key: 'enableHACCPCompliance', label: 'HACCP Compliance' },
                      { key: 'enableMobileOptimization', label: 'Mobile Optimization' },
                      { key: 'enableErrorRecovery', label: 'Error Recovery' },
                      { key: 'enableAuditTrail', label: 'Audit Trail' }
                    ].map(({ key, label }) => (
                      <label key={key} className="flex items-center gap-3">
                        <input
                          type="checkbox"
                          checked={config[key as keyof EnterpriseVoiceConfig] as boolean}
                          onChange={(e) => setConfig(prev => ({ ...prev, [key]: e.target.checked }))}
                          className="rounded"
                        />
                        <span className="text-sm text-gray-700">{label}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Performance Targets */}
                <div>
                  <h3 className="text-sm font-medium text-gray-900 mb-3">Performance Targets</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-xs text-gray-600 mb-1">Max Latency (ms)</label>
                      <input
                        type="number"
                        value={config.performanceTargets.maxLatencyMs}
                        onChange={(e) => setConfig(prev => ({
                          ...prev,
                          performanceTargets: {
                            ...prev.performanceTargets,
                            maxLatencyMs: Number(e.target.value)
                          }
                        }))}
                        className="w-full px-3 py-2 border rounded text-sm"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-xs text-gray-600 mb-1">Min Accuracy (%)</label>
                      <input
                        type="number"
                        value={config.performanceTargets.minAccuracyPercent}
                        onChange={(e) => setConfig(prev => ({
                          ...prev,
                          performanceTargets: {
                            ...prev.performanceTargets,
                            minAccuracyPercent: Number(e.target.value)
                          }
                        }))}
                        className="w-full px-3 py-2 border rounded text-sm"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-xs text-gray-600 mb-1">Max Error Rate (%)</label>
                      <input
                        type="number"
                        value={config.performanceTargets.maxErrorRate}
                        onChange={(e) => setConfig(prev => ({
                          ...prev,
                          performanceTargets: {
                            ...prev.performanceTargets,
                            maxErrorRate: Number(e.target.value)
                          }
                        }))}
                        className="w-full px-3 py-2 border rounded text-sm"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Sidebar - System Status */}
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-sm border p-4">
            <h3 className="text-sm font-semibold text-gray-900 mb-3">System Status</h3>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-600">Realtime API</span>
                <div className={`w-2 h-2 rounded-full ${
                  systemStatus.realtimeConnected ? 'bg-green-500' : 'bg-red-500'
                }`}></div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-600">Error Recovery</span>
                <div className={`w-2 h-2 rounded-full ${
                  systemStatus.errorRecoveryActive ? 'bg-green-500' : 'bg-gray-400'
                }`}></div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-600">HACCP Audit</span>
                <div className={`w-2 h-2 rounded-full ${
                  systemStatus.auditTrailActive ? 'bg-green-500' : 'bg-gray-400'
                }`}></div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-600">Compliance</span>
                <div className={`w-2 h-2 rounded-full ${
                  systemStatus.complianceStatus.haccpCompliant ? 'bg-green-500' : 'bg-yellow-500'
                }`}></div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-lg shadow-sm border p-4">
            <h3 className="text-sm font-semibold text-gray-900 mb-3">Quick Actions</h3>
            
            <div className="space-y-2">
              <button className="w-full text-left px-3 py-2 text-xs text-gray-700 hover:bg-gray-50 rounded transition-colors">
                Generate Compliance Report
              </button>
              <button className="w-full text-left px-3 py-2 text-xs text-gray-700 hover:bg-gray-50 rounded transition-colors">
                Export Audit Trail
              </button>
              <button className="w-full text-left px-3 py-2 text-xs text-gray-700 hover:bg-gray-50 rounded transition-colors">
                System Health Check
              </button>
              <button className="w-full text-left px-3 py-2 text-xs text-gray-700 hover:bg-gray-50 rounded transition-colors">
                Performance Optimization
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}