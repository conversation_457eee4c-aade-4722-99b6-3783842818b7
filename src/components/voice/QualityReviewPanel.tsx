import React, { useState, useEffect, useCallback } from 'react';
import { 
  Al<PERSON><PERSON>riangle, 
  CheckCircle, 
  X, 
  Volume2, 
  Edit, 
  BarChart3,
  RefreshCw,
  Clock,
  Package,
  TrendingUp,
  TrendingDown,
  Activity
} from 'lucide-react';
import { voiceEventService } from '../../services/VoiceEventService';
import { voiceAnalyticsService } from '../../services/VoiceAnalyticsService';
import { VoiceEvent } from '../../types/schema';

interface QualityReviewPanelProps {
  onEventApprove?: (eventId: string) => void;
  onEventReject?: (eventId: string, reason: string) => void;
  onEventEdit?: (event: VoiceEvent) => void;
  confidenceThreshold?: number;
}

interface ReviewStats {
  totalPendingReview: number;
  averageConfidence: number;
  reviewedToday: number;
  accuracyTrend: 'up' | 'down' | 'stable';
}

export const QualityReviewPanel: React.FC<QualityReviewPanelProps> = ({
  onEventApprove,
  onEventReject,
  onEventEdit,
  confidenceThreshold = 0.7
}) => {
  const [pendingEvents, setPendingEvents] = useState<VoiceEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedEvents, setSelectedEvents] = useState<Set<string>>(new Set());
  const [reviewStats, setReviewStats] = useState<ReviewStats>({
    totalPendingReview: 0,
    averageConfidence: 0,
    reviewedToday: 0,
    accuracyTrend: 'stable'
  });
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [batchReviewReason, setBatchReviewReason] = useState('');
  const [processingBatch, setProcessingBatch] = useState(false);

  // Load events needing review
  const loadPendingEvents = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const events = await voiceEventService.getEventsForQualityReview(confidenceThreshold);
      setPendingEvents(events);

      // Calculate stats
      const [stats, trends, reviewStats] = await Promise.all([
        voiceEventService.getVoiceEventStatistics(),
        voiceAnalyticsService.getAccuracyTrends(30),
        voiceAnalyticsService.getReviewStatistics(1)
      ]);

      setReviewStats({
        totalPendingReview: events.length,
        averageConfidence: events.length > 0 
          ? events.reduce((sum, e) => sum + e.voice_confidence_score, 0) / events.length
          : 0,
        reviewedToday: reviewStats.totalReviewed,
        accuracyTrend: trends.trend
      });
    } catch (err) {
      console.error('Error loading pending events:', err);
      setError('Failed to load events for review. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [confidenceThreshold]);

  // Initial load
  useEffect(() => {
    loadPendingEvents();
  }, [loadPendingEvents]);

  // Handle individual event approval
  const handleApprove = useCallback(async (eventId: string) => {
    try {
      await voiceEventService.approveVoiceEvent(eventId);
      setPendingEvents(prev => prev.filter(e => e.id !== eventId));
      
      if (onEventApprove) {
        onEventApprove(eventId);
      }
    } catch (error) {
      console.error('Error approving event:', error);
      setError('Failed to approve event. Please try again.');
    }
  }, [onEventApprove]);

  // Handle individual event rejection
  const handleReject = useCallback(async (eventId: string, reason: string) => {
    try {
      await voiceEventService.rejectVoiceEvent(eventId, reason);
      setPendingEvents(prev => prev.filter(e => e.id !== eventId));
      
      if (onEventReject) {
        onEventReject(eventId, reason);
      }
    } catch (error) {
      console.error('Error rejecting event:', error);
      setError('Failed to reject event. Please try again.');
    }
  }, [onEventReject]);

  // Handle batch operations
  const handleBatchReview = useCallback(async (action: 'approve' | 'reject') => {
    if (selectedEvents.size === 0) return;

    if (action === 'reject' && !batchReviewReason.trim()) {
      setError('Please provide a reason for batch rejection.');
      return;
    }

    try {
      setProcessingBatch(true);
      setError(null);

      const eventIds = Array.from(selectedEvents);
      
      if (action === 'approve') {
        await voiceEventService.batchApproveVoiceEvents(eventIds);
      } else {
        await voiceEventService.batchRejectVoiceEvents(eventIds, batchReviewReason);
      }

      // Remove processed events from pending list
      setPendingEvents(prev => prev.filter(e => !selectedEvents.has(e.id!)));
      setSelectedEvents(new Set());
      setBatchReviewReason('');

      // Notify parent components
      if (action === 'approve' && onEventApprove) {
        eventIds.forEach(id => onEventApprove(id));
      } else if (action === 'reject' && onEventReject) {
        eventIds.forEach(id => onEventReject(id, batchReviewReason));
      }
    } catch (error) {
      console.error('Error processing batch review:', error);
      setError('Failed to process batch review. Please try again.');
    } finally {
      setProcessingBatch(false);
    }
  }, [selectedEvents, batchReviewReason, onEventApprove, onEventReject]);

  // Handle event selection for batch operations
  const toggleEventSelection = useCallback((eventId: string) => {
    setSelectedEvents(prev => {
      const newSet = new Set(prev);
      if (newSet.has(eventId)) {
        newSet.delete(eventId);
      } else {
        newSet.add(eventId);
      }
      return newSet;
    });
  }, []);

  // Select all events
  const selectAllEvents = useCallback(() => {
    const allIds = pendingEvents.map(e => e.id!).filter(Boolean);
    setSelectedEvents(new Set(allIds));
  }, [pendingEvents]);

  // Clear selection
  const clearSelection = useCallback(() => {
    setSelectedEvents(new Set());
  }, []);

  // Play audio for review
  const playAudio = useCallback(async (audioUrl: string) => {
    try {
      const audio = new Audio(audioUrl);
      await audio.play();
    } catch (error) {
      console.error('Error playing audio:', error);
    }
  }, []);

  // Format confidence score
  const formatConfidence = (score: number) => Math.round(score * 100);

  // Get confidence color
  const getConfidenceColor = (score: number) => {
    if (score >= 0.9) return 'text-green-600 bg-green-50';
    if (score >= 0.7) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="w-6 h-6 animate-spin mr-2" />
        <span>Loading events for review...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Quality Review</h2>
          <p className="text-gray-600">
            Review voice events with confidence below {Math.round(confidenceThreshold * 100)}%
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={() => setShowAnalytics(!showAnalytics)}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <BarChart3 className="w-4 h-4 mr-2" />
            {showAnalytics ? 'Hide' : 'Show'} Analytics
          </button>
          <button
            onClick={loadPendingEvents}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertTriangle className="w-5 h-5 text-red-600 mr-2" />
            <span className="text-red-800">{error}</span>
          </div>
          <button
            onClick={() => setError(null)}
            className="mt-2 text-red-600 hover:text-red-800 underline text-sm"
          >
            Dismiss
          </button>
        </div>
      )}

      {/* Analytics Panel */}
      {showAnalytics && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Review Analytics</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-600">Pending Review</p>
                  <p className="text-2xl font-bold text-blue-900">{reviewStats.totalPendingReview}</p>
                </div>
                <AlertTriangle className="w-8 h-8 text-blue-500" />
              </div>
            </div>
            
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-600">Avg Confidence</p>
                  <p className="text-2xl font-bold text-green-900">
                    {Math.round(reviewStats.averageConfidence * 100)}%
                  </p>
                </div>
                <Activity className="w-8 h-8 text-green-500" />
              </div>
            </div>
            
            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-purple-600">Reviewed Today</p>
                  <p className="text-2xl font-bold text-purple-900">{reviewStats.reviewedToday}</p>
                </div>
                <Clock className="w-8 h-8 text-purple-500" />
              </div>
            </div>
            
            <div className="bg-orange-50 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-orange-600">Accuracy Trend</p>
                  <p className="text-2xl font-bold text-orange-900 capitalize">{reviewStats.accuracyTrend}</p>
                </div>
                {reviewStats.accuracyTrend === 'up' ? (
                  <TrendingUp className="w-8 h-8 text-orange-500" />
                ) : reviewStats.accuracyTrend === 'down' ? (
                  <TrendingDown className="w-8 h-8 text-orange-500" />
                ) : (
                  <Activity className="w-8 h-8 text-orange-500" />
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Batch Actions */}
      {pendingEvents.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={selectedEvents.size === pendingEvents.length && pendingEvents.length > 0}
                  onChange={(e) => e.target.checked ? selectAllEvents() : clearSelection()}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">
                  {selectedEvents.size > 0 
                    ? `${selectedEvents.size} selected`
                    : 'Select all'
                  }
                </span>
              </div>
              
              {selectedEvents.size > 0 && (
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleBatchReview('approve')}
                    disabled={processingBatch}
                    className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                  >
                    <CheckCircle className="w-4 h-4 mr-1" />
                    Approve Selected
                  </button>
                  
                  <div className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={batchReviewReason}
                      onChange={(e) => setBatchReviewReason(e.target.value)}
                      placeholder="Rejection reason..."
                      className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    />
                    <button
                      onClick={() => handleBatchReview('reject')}
                      disabled={processingBatch || !batchReviewReason.trim()}
                      className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                    >
                      <X className="w-4 h-4 mr-1" />
                      Reject Selected
                    </button>
                  </div>
                </div>
              )}
            </div>
            
            {processingBatch && (
              <div className="flex items-center text-sm text-gray-600">
                <RefreshCw className="w-4 h-4 animate-spin mr-2" />
                Processing...
              </div>
            )}
          </div>
        </div>
      )}

      {/* Events List */}
      {pendingEvents.length === 0 ? (
        <div className="text-center py-12">
          <CheckCircle className="w-12 h-12 text-green-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">All caught up!</h3>
          <p className="text-gray-500">
            No voice events require quality review at this time.
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {pendingEvents.map((event) => (
            <div
              key={event.id}
              className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start space-x-4">
                {/* Selection Checkbox */}
                <input
                  type="checkbox"
                  checked={selectedEvents.has(event.id!)}
                  onChange={() => toggleEventSelection(event.id!)}
                  className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />

                {/* Event Details */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <Package className="w-5 h-5 text-gray-400" />
                      <h3 className="text-lg font-semibold text-gray-900">
                        {event.product_name}
                      </h3>
                      <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getConfidenceColor(event.voice_confidence_score)}`}>
                        <AlertTriangle className="w-3 h-3 mr-1" />
                        {formatConfidence(event.voice_confidence_score)}% confidence
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-4">
                    <div>
                      <span className="font-medium">Event:</span> {event.event_type}
                    </div>
                    <div>
                      <span className="font-medium">Quantity:</span> {event.quantity} {event.unit}
                    </div>
                    <div>
                      <span className="font-medium">Date:</span> {new Date(event.occurred_at).toLocaleDateString()}
                    </div>
                  </div>

                  {/* Original Transcript */}
                  {event.raw_transcript && (
                    <div className="bg-gray-50 p-3 rounded-lg mb-4">
                      <p className="text-sm font-medium text-gray-700 mb-1">Original Transcript:</p>
                      <p className="text-sm text-gray-600 italic">"{event.raw_transcript}"</p>
                    </div>
                  )}

                  {/* Confidence Breakdown */}
                  {event.voice_confidence_breakdown && (
                    <div className="mb-4">
                      <p className="text-sm font-medium text-gray-700 mb-2">Confidence Breakdown:</p>
                      <div className="grid grid-cols-3 gap-3 text-xs">
                        <div className="text-center">
                          <div className="font-medium">Product Match</div>
                          <div className={getConfidenceColor(event.voice_confidence_breakdown.product_match)}>
                            {formatConfidence(event.voice_confidence_breakdown.product_match)}%
                          </div>
                        </div>
                        <div className="text-center">
                          <div className="font-medium">Quantity</div>
                          <div className={getConfidenceColor(event.voice_confidence_breakdown.quantity_extraction)}>
                            {formatConfidence(event.voice_confidence_breakdown.quantity_extraction)}%
                          </div>
                        </div>
                        <div className="text-center">
                          <div className="font-medium">Vendor Match</div>
                          <div className={getConfidenceColor(event.voice_confidence_breakdown.vendor_match)}>
                            {formatConfidence(event.voice_confidence_breakdown.vendor_match)}%
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Actions */}
                <div className="flex flex-col space-y-2">
                  {event.audio_recording_url && (
                    <button
                      onClick={() => playAudio(event.audio_recording_url!)}
                      className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
                      title="Play audio recording"
                    >
                      <Volume2 className="w-4 h-4" />
                    </button>
                  )}
                  
                  {onEventEdit && (
                    <button
                      onClick={() => onEventEdit(event)}
                      className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
                      title="Edit event"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                  )}
                  
                  <button
                    onClick={() => handleApprove(event.id!)}
                    className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-full transition-colors"
                    title="Approve event"
                  >
                    <CheckCircle className="w-4 h-4" />
                  </button>
                  
                  <button
                    onClick={() => {
                      const reason = prompt('Please provide a reason for rejection:');
                      if (reason) {
                        handleReject(event.id!, reason);
                      }
                    }}
                    className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-full transition-colors"
                    title="Reject event"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default QualityReviewPanel;