import React from 'react';
import { AlertCircle } from 'lucide-react';

interface FormData {
  event_type: 'receiving' | 'disposal' | 'physical_count' | 'sale';
  product_name: string;
  quantity: number;
  unit: 'lbs' | 'kg' | 'cases' | 'units';
  vendor_name?: string;
  customer_name?: string;
  condition?: 'Excellent' | 'Good' | 'Fair' | 'Poor' | 'Damaged';
  temperature?: number;
  temperature_unit?: 'fahrenheit' | 'celsius';
  processing_method?: string;
  quality_grade?: string;
  market_form?: string;
  notes?: string;
  occurred_at: string;
}

interface ValidationErrors {
  [key: string]: string;
}

interface VoiceEventFormProps {
  formData: FormData;
  errors: ValidationErrors;
  readOnly?: boolean;
  onFieldChange: (field: keyof FormData, value: any) => void;
}

export const VoiceEventForm: React.FC<VoiceEventFormProps> = ({
  formData,
  errors,
  readOnly = false,
  onFieldChange
}) => {
  return (
    <div className="lg:col-span-2 space-y-6">
      {/* General Error */}
      {errors.general && (
        <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="w-5 h-5 text-red-600 mr-2" />
            <span className="text-red-800">{errors.general}</span>
          </div>
        </div>
      )}

      {/* Basic Information */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Event Type *
            </label>
            <select
              value={formData.event_type}
              onChange={(e) => onFieldChange('event_type', e.target.value)}
              disabled={readOnly}
              className={`w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                readOnly ? 'bg-gray-100' : ''
              }`}
            >
              <option value="receiving">Receiving</option>
              <option value="sale">Sale</option>
              <option value="disposal">Disposal</option>
              <option value="physical_count">Physical Count</option>
            </select>
            {errors.event_type && (
              <p className="mt-1 text-sm text-red-600">{errors.event_type}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Product Name *
            </label>
            <input
              type="text"
              value={formData.product_name}
              onChange={(e) => onFieldChange('product_name', e.target.value)}
              disabled={readOnly}
              className={`w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                readOnly ? 'bg-gray-100' : ''
              }`}
            />
            {errors.product_name && (
              <p className="mt-1 text-sm text-red-600">{errors.product_name}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Quantity *
            </label>
            <input
              type="number"
              value={formData.quantity}
              onChange={(e) => onFieldChange('quantity', Number(e.target.value))}
              disabled={readOnly}
              min="0"
              step="0.01"
              className={`w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                readOnly ? 'bg-gray-100' : ''
              }`}
            />
            {errors.quantity && (
              <p className="mt-1 text-sm text-red-600">{errors.quantity}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Unit *
            </label>
            <select
              value={formData.unit}
              onChange={(e) => onFieldChange('unit', e.target.value)}
              disabled={readOnly}
              className={`w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                readOnly ? 'bg-gray-100' : ''
              }`}
            >
              <option value="lbs">Pounds (lbs)</option>
              <option value="kg">Kilograms (kg)</option>
              <option value="cases">Cases</option>
              <option value="units">Units</option>
            </select>
            {errors.unit && (
              <p className="mt-1 text-sm text-red-600">{errors.unit}</p>
            )}
          </div>
        </div>
      </div>

      {/* Vendor/Customer Information */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Vendor/Customer Information</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {(formData.event_type === 'receiving' || formData.vendor_name) && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Vendor {formData.event_type === 'receiving' ? '*' : ''}
              </label>
              <input
                type="text"
                value={formData.vendor_name || ''}
                onChange={(e) => onFieldChange('vendor_name', e.target.value)}
                disabled={readOnly}
                className={`w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                  readOnly ? 'bg-gray-100' : ''
                }`}
              />
              {errors.vendor_name && (
                <p className="mt-1 text-sm text-red-600">{errors.vendor_name}</p>
              )}
            </div>
          )}

          {(formData.event_type === 'sale' || formData.customer_name) && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Customer {formData.event_type === 'sale' ? '*' : ''}
              </label>
              <input
                type="text"
                value={formData.customer_name || ''}
                onChange={(e) => onFieldChange('customer_name', e.target.value)}
                disabled={readOnly}
                className={`w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                  readOnly ? 'bg-gray-100' : ''
                }`}
              />
              {errors.customer_name && (
                <p className="mt-1 text-sm text-red-600">{errors.customer_name}</p>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Additional Details */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Additional Details</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Condition
            </label>
            <select
              value={formData.condition || ''}
              onChange={(e) => onFieldChange('condition', e.target.value || undefined)}
              disabled={readOnly}
              className={`w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                readOnly ? 'bg-gray-100' : ''
              }`}
            >
              <option value="">Select condition</option>
              <option value="Excellent">Excellent</option>
              <option value="Good">Good</option>
              <option value="Fair">Fair</option>
              <option value="Poor">Poor</option>
              <option value="Damaged">Damaged</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Event Date/Time *
            </label>
            <input
              type="datetime-local"
              value={formData.occurred_at}
              onChange={(e) => onFieldChange('occurred_at', e.target.value)}
              disabled={readOnly}
              className={`w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                readOnly ? 'bg-gray-100' : ''
              }`}
            />
            {errors.occurred_at && (
              <p className="mt-1 text-sm text-red-600">{errors.occurred_at}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Temperature
            </label>
            <div className="flex space-x-2">
              <input
                type="number"
                value={formData.temperature || ''}
                onChange={(e) => onFieldChange('temperature', e.target.value ? Number(e.target.value) : undefined)}
                disabled={readOnly}
                placeholder="Temperature"
                className={`flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                  readOnly ? 'bg-gray-100' : ''
                }`}
              />
              <select
                value={formData.temperature_unit || 'fahrenheit'}
                onChange={(e) => onFieldChange('temperature_unit', e.target.value)}
                disabled={readOnly}
                className={`rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                  readOnly ? 'bg-gray-100' : ''
                }`}
              >
                <option value="fahrenheit">°F</option>
                <option value="celsius">°C</option>
              </select>
            </div>
            {errors.temperature && (
              <p className="mt-1 text-sm text-red-600">{errors.temperature}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Processing Method
            </label>
            <input
              type="text"
              value={formData.processing_method || ''}
              onChange={(e) => onFieldChange('processing_method', e.target.value)}
              disabled={readOnly}
              placeholder="e.g., Fresh, Frozen, IQF"
              className={`w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                readOnly ? 'bg-gray-100' : ''
              }`}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Quality Grade
            </label>
            <input
              type="text"
              value={formData.quality_grade || ''}
              onChange={(e) => onFieldChange('quality_grade', e.target.value)}
              disabled={readOnly}
              placeholder="e.g., Premium, Grade A"
              className={`w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                readOnly ? 'bg-gray-100' : ''
              }`}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Market Form
            </label>
            <input
              type="text"
              value={formData.market_form || ''}
              onChange={(e) => onFieldChange('market_form', e.target.value)}
              disabled={readOnly}
              placeholder="e.g., Whole, Fillets, H&G"
              className={`w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                readOnly ? 'bg-gray-100' : ''
              }`}
            />
          </div>
        </div>

        <div className="mt-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Notes
          </label>
          <textarea
            value={formData.notes || ''}
            onChange={(e) => onFieldChange('notes', e.target.value)}
            disabled={readOnly}
            rows={3}
            className={`w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
              readOnly ? 'bg-gray-100' : ''
            }`}
            placeholder="Additional notes or comments..."
          />
        </div>
      </div>
    </div>
  );
};

export default VoiceEventForm;