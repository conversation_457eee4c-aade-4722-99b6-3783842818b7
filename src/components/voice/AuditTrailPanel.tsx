import React, { useState } from 'react';
import { Clock } from 'lucide-react';
import { EventAudit } from '../../types/schema';

interface AuditTrailPanelProps {
  auditTrail: EventAudit[];
  className?: string;
}

export const AuditTrailPanel: React.FC<AuditTrailPanelProps> = ({
  auditTrail,
  className = ''
}) => {
  const [showAuditTrail, setShowAuditTrail] = useState(false);

  if (auditTrail.length === 0) {
    return null;
  }

  return (
    <div className={`bg-gray-50 p-4 rounded-lg ${className}`}>
      <button
        onClick={() => setShowAuditTrail(!showAuditTrail)}
        className="flex items-center justify-between w-full text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors"
      >
        <div className="flex items-center">
          <Clock className="w-4 h-4 mr-2" />
          Change History ({auditTrail.length})
        </div>
        <span className="text-xs">
          {showAuditTrail ? 'Hide' : 'Show'}
        </span>
      </button>
      
      {showAuditTrail && (
        <div className="mt-3 space-y-2 max-h-48 overflow-y-auto">
          {auditTrail.map((audit, index) => (
            <div key={audit.id || index} className="text-xs bg-white p-2 rounded border">
              <div className="font-medium text-gray-700">
                {audit.field_name} changed
              </div>
              <div className="text-gray-500 mt-1">
                {audit.changed_at ? new Date(audit.changed_at).toLocaleString() : 'Unknown time'}
              </div>
              {audit.change_reason && (
                <div className="text-gray-600 mt-1">
                  Reason: {audit.change_reason}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default AuditTrailPanel;