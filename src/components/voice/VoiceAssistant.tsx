import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Mic, MicOff, Volume2, VolumeX } from 'lucide-react';
import { supabase } from '../../lib/supabase';
import { useNavigationContext } from '../../contexts/NavigationContext';
import { voiceEventService } from '../../modules/voice-event-storage';

interface VoiceCommand {
  type: 'receiving' | 'disposal' | 'physical_count' | 'sale' | 'query' | 'navigation';
  action: string;
  data?: Record<string, any>;
  confidence: number;
}

interface VoiceAssistantProps {
  onEventCreated?: (eventType: string) => void;
  className?: string;
}

export default function VoiceAssistant({ onEventCreated, className = '' }: VoiceAssistantProps) {
  const [isListening, setIsListening] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [feedback, setFeedback] = useState('');
  const [isEnabled, setIsEnabled] = useState(true);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [errorCount, setErrorCount] = useState(0);
  const [lastError, setLastError] = useState<string | null>(null);
  
  const recognition = useRef<SpeechRecognition | null>(null);
  const speechSynthesis = useRef<SpeechSynthesis | null>(null);
  const { setActiveView, setViewFilter } = useNavigationContext();

  // Initialize speech recognition
  useEffect(() => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognitionClass = window.webkitSpeechRecognition || window.SpeechRecognition;
      recognition.current = new SpeechRecognitionClass();
      recognition.current.continuous = false;
      recognition.current.interimResults = false;
      recognition.current.lang = 'en-US';
      
      recognition.current.onstart = () => {
        setIsListening(true);
        setFeedback('Listening...');
      };
      
      recognition.current.onend = () => {
        setIsListening(false);
        if (!isProcessing) {
          setFeedback('');
        }
      };
      
      recognition.current.onresult = async (event) => {
        const result = event.results[0][0].transcript;
        setTranscript(result);
        setIsProcessing(true);
        setFeedback('Processing...');
        
        try {
          await processVoiceCommand(result);
        } catch (error) {
          console.error('Voice command error:', error);
          setFeedback('Sorry, I didn\'t understand that command.');
          speak('Sorry, I didn\'t understand that command.');
        } finally {
          setIsProcessing(false);
          setTimeout(() => setFeedback(''), 3000);
        }
      };
      
      recognition.current.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        setIsListening(false);
        setIsProcessing(false);
        setErrorCount(prev => prev + 1);
        setLastError(event.error);
        
        // Provide specific error messages based on error type
        let errorMessage = 'Voice recognition error. Please try again.';
        switch (event.error) {
          case 'no-speech':
            errorMessage = 'No speech detected. Please speak clearly and try again.';
            break;
          case 'audio-capture':
            errorMessage = 'Microphone access denied. Please allow microphone permissions.';
            break;
          case 'not-allowed':
            errorMessage = 'Microphone permission denied. Please enable microphone access in your browser.';
            break;
          case 'network':
            errorMessage = 'Network error. Please check your connection and try again.';
            break;
          case 'service-not-allowed':
            errorMessage = 'Speech recognition service is not available. Please try manual input.';
            break;
          default:
            errorMessage = `Voice recognition error (${event.error}). Please try again.`;
        }
        
        setFeedback(errorMessage);
        speak(errorMessage);
        
        // Disable voice assistant temporarily if too many errors
        if (errorCount >= 3) {
          setIsEnabled(false);
          setFeedback('Voice assistant disabled due to repeated errors. Please refresh the page to re-enable.');
        }
      };
    } else {
      setIsEnabled(false);
      setFeedback('Voice recognition not supported in this browser.');
    }

    // Initialize speech synthesis
    if ('speechSynthesis' in window) {
      speechSynthesis.current = window.speechSynthesis;
    }

    return () => {
      if (recognition.current) {
        recognition.current.abort();
      }
    };
  }, []);

  const speak = useCallback((text: string) => {
    if (speechSynthesis.current && isEnabled) {
      speechSynthesis.current.cancel();
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.rate = 0.9;
      utterance.pitch = 1;
      utterance.volume = 0.8;
      
      utterance.onstart = () => setIsSpeaking(true);
      utterance.onend = () => setIsSpeaking(false);
      utterance.onerror = () => setIsSpeaking(false);
      
      speechSynthesis.current.speak(utterance);
    }
  }, [isEnabled]);

  const startListening = useCallback(() => {
    if (recognition.current && isEnabled && !isListening) {
      setTranscript('');
      setFeedback('');
      recognition.current.start();
    }
  }, [isEnabled, isListening]);

  const stopListening = useCallback(() => {
    if (recognition.current && isListening) {
      recognition.current.stop();
    }
  }, [isListening]);

  const parseVoiceCommand = (text: string): VoiceCommand | null => {
    const lowerText = text.toLowerCase();
    
    // Receiving events
    if (lowerText.includes('add receiving') || lowerText.includes('received') || lowerText.includes('log incoming')) {
      return parseReceivingCommand(text);
    }
    
    // Disposal events
    if (lowerText.includes('dispose') || lowerText.includes('disposal') || lowerText.includes('waste')) {
      return parseDisposalCommand(text);
    }
    
    // Physical count events
    if (lowerText.includes('physical count') || lowerText.includes('inventory count') || lowerText.includes('count')) {
      return parsePhysicalCountCommand(text);
    }
    
    // Sales events
    if (lowerText.includes('sale') || lowerText.includes('sell') || lowerText.includes('sold')) {
      return parseSaleCommand(text);
    }
    
    // Information queries
    if (lowerText.includes('how much') || lowerText.includes('what') || lowerText.includes('show')) {
      return parseQueryCommand(text);
    }
    
    // Navigation commands
    if (lowerText.includes('go to') || lowerText.includes('open') || lowerText.includes('show me')) {
      return parseNavigationCommand(text);
    }
    
    return null;
  };

  const parseReceivingCommand = (text: string): VoiceCommand => {
    // Enhanced seafood species recognition
    const seafoodPatterns = [
      // Finfish
      /(atlantic salmon|king salmon|coho salmon|sockeye salmon|salmon)/i,
      /(pacific cod|atlantic cod|black cod|cod)/i,
      /(pacific halibut|california halibut|halibut)/i,
      /(yellowfin tuna|bluefin tuna|albacore tuna|ahi tuna|tuna)/i,
      /(red snapper|yellow snapper|snapper)/i,
      /(sea bass|striped bass|black bass|bass)/i,
      /(dover sole|petrale sole|sole)/i,
      /(lingcod|rockfish|mahi mahi|swordfish)/i,
      
      // Shellfish  
      /(dungeness crab|king crab|snow crab|blue crab|crab)/i,
      /(maine lobster|spiny lobster|lobster tail|lobster)/i,
      /(tiger shrimp|white shrimp|gulf shrimp|spot prawns|prawns|shrimp)/i,
      /(bay scallops|sea scallops|diver scallops|scallops)/i,
      
      // Mollusks
      /(pacific oysters|kumamoto oysters|blue point oysters|oysters)/i,
      /(manila clams|littleneck clams|razor clams|clams)/i,
      /(penn cove mussels|blue mussels|mussels)/i,
      /(sea urchin|uni|abalone|squid|octopus)/i
    ];

    // Extract quantity, unit, product, and vendor from text
    const quantityMatch = text.match(/(\d+(?:\.\d+)?)\s*(pounds?|lbs?|kilograms?|kg|cases?|units?|each)/i);
    
    let productMatch = null;
    let matchedProduct = null;
    
    // Find the best seafood match
    for (const pattern of seafoodPatterns) {
      const match = text.match(pattern);
      if (match) {
        productMatch = match;
        matchedProduct = match[1] || match[0];
        break;
      }
    }
    
    const vendorMatch = text.match(/from\s+([^,]+?)(?:\s|,|$)/i);
    const conditionMatch = text.match(/condition\s+(excellent|good|fair|poor|damaged)/i);
    const temperatureMatch = text.match(/(\d+)\s*degrees?\s*(fahrenheit|celsius|f|c)?/i);
    
    // Calculate confidence based on matches
    let confidence = 0.5;
    if (quantityMatch) confidence += 0.2;
    if (productMatch) confidence += 0.2;
    if (vendorMatch) confidence += 0.1;
    
    return {
      type: 'receiving',
      action: 'create_event',
      data: {
        quantity: quantityMatch ? parseFloat(quantityMatch[1]) : null,
        unit: quantityMatch ? quantityMatch[2].toLowerCase().replace(/s$/, '') : 'lbs',
        product: matchedProduct || 'unknown seafood',
        vendor: vendorMatch ? vendorMatch[1].trim() : null,
        condition: conditionMatch ? conditionMatch[1].toLowerCase() : 'good',
        temperature: temperatureMatch ? parseFloat(temperatureMatch[1]) : null,
        temperatureUnit: temperatureMatch && temperatureMatch[2] ? temperatureMatch[2].toLowerCase() : 'fahrenheit',
        notes: `Created via voice command: "${text}"`
      },
      confidence: Math.min(confidence, 1.0)
    };
  };

  const parseDisposalCommand = (text: string): VoiceCommand => {
    const quantityMatch = text.match(/(\d+(?:\.\d+)?)\s*(pounds?|lbs?|kilograms?|kg|cases?|units?)/i);
    const productMatch = text.match(/(cod|salmon|halibut|crab|tuna|shrimp|lobster|scallops)/i);
    const reasonMatch = text.match(/reason\s+([^,]+?)(?:\s|,|$)/i);
    
    return {
      type: 'disposal',
      action: 'create_event',
      data: {
        quantity: quantityMatch ? parseFloat(quantityMatch[1]) : null,
        unit: quantityMatch ? quantityMatch[2].toLowerCase().replace(/s$/, '') : null,
        product: productMatch ? productMatch[1].toLowerCase() : null,
        reason: reasonMatch ? reasonMatch[1].trim() : 'disposal',
        notes: `Created via voice command: "${text}"`
      },
      confidence: 0.8
    };
  };

  const parsePhysicalCountCommand = (text: string): VoiceCommand => {
    const quantityMatch = text.match(/(\d+(?:\.\d+)?)\s*(pounds?|lbs?|kilograms?|kg|cases?|units?)/i);
    const productMatch = text.match(/(cod|salmon|halibut|crab|tuna|shrimp|lobster|scallops)/i);
    
    return {
      type: 'physical_count',
      action: 'create_event',
      data: {
        quantity: quantityMatch ? parseFloat(quantityMatch[1]) : null,
        unit: quantityMatch ? quantityMatch[2].toLowerCase().replace(/s$/, '') : null,
        product: productMatch ? productMatch[1].toLowerCase() : null,
        notes: `Created via voice command: "${text}"`
      },
      confidence: 0.8
    };
  };

  const parseSaleCommand = (text: string): VoiceCommand => {
    const quantityMatch = text.match(/(\d+(?:\.\d+)?)\s*(pounds?|lbs?|kilograms?|kg|cases?|units?)/i);
    const productMatch = text.match(/(cod|salmon|halibut|crab|tuna|shrimp|lobster|scallops)/i);
    const customerMatch = text.match(/to\s+([^,]+?)(?:\s|,|\$|$)/i);
    const priceMatch = text.match(/\$(\d+(?:\.\d+)?)/);
    
    return {
      type: 'sale',
      action: 'create_event',
      data: {
        quantity: quantityMatch ? parseFloat(quantityMatch[1]) : null,
        unit: quantityMatch ? quantityMatch[2].toLowerCase().replace(/s$/, '') : null,
        product: productMatch ? productMatch[1].toLowerCase() : null,
        customer: customerMatch ? customerMatch[1].trim() : null,
        unitPrice: priceMatch ? parseFloat(priceMatch[1]) : null,
        notes: `Created via voice command: "${text}"`
      },
      confidence: 0.8
    };
  };

  const parseQueryCommand = (text: string): VoiceCommand => {
    const productMatch = text.match(/(cod|salmon|halibut|crab|tuna|shrimp|lobster|scallops)/i);
    
    return {
      type: 'query',
      action: 'get_inventory',
      data: {
        product: productMatch ? productMatch[1].toLowerCase() : null,
        query: text
      },
      confidence: 0.7
    };
  };

  const parseNavigationCommand = (text: string): VoiceCommand => {
    let destination = 'Dashboard';
    
    if (text.toLowerCase().includes('events')) destination = 'Events';
    else if (text.toLowerCase().includes('inventory')) destination = 'Inventory';
    else if (text.toLowerCase().includes('dashboard')) destination = 'Dashboard';
    else if (text.toLowerCase().includes('analytics')) destination = 'Analytics';
    
    return {
      type: 'navigation',
      action: 'navigate',
      data: { destination },
      confidence: 0.9
    };
  };

  const processVoiceCommand = async (text: string) => {
    const command = parseVoiceCommand(text);
    
    if (!command) {
      setFeedback('Command not recognized. Try saying "Add receiving" or "Show me events".');
      speak('Command not recognized. Try saying Add receiving or Show me events.');
      return;
    }

    try {
      switch (command.type) {
        case 'receiving':
        case 'disposal':
        case 'physical_count':
        case 'sale':
          await createEventFromVoice(command);
          break;
        case 'query':
          await handleQuery(command);
          break;
        case 'navigation':
          handleNavigation(command);
          break;
      }
    } catch (error) {
      console.error('Error processing voice command:', error);
      setFeedback('Error processing command. Please try again.');
      speak('Error processing command. Please try again.');
    }
  };

  const createEventFromVoice = async (command: VoiceCommand) => {
    const { data } = command;
    
    if (!data?.quantity || !data?.product) {
      setFeedback('Please specify both quantity and product name.');
      speak('Please specify both quantity and product name.');
      return;
    }

    // Find or create product
    const productId = await findOrCreateProduct(data.product);
    
    // Create the event using the voice event service
    const voiceEventData = {
      event_type: command.type as 'receiving' | 'disposal' | 'physical_count' | 'sale',
      product_name: data.product,
      quantity: data.quantity,
      unit: data.unit || 'lbs',
      vendor_name: data.vendor,
      customer_name: data.customer,
      condition: data.condition,
      temperature: data.temperature,
      temperature_unit: data.temperatureUnit || 'fahrenheit',
      notes: data.notes,
      occurred_at: new Date().toISOString(),
      
      // Voice fields
      voice_confidence_score: command.confidence,
      voice_confidence_breakdown: {
        product_match: command.confidence,
        quantity_extraction: command.confidence,
        vendor_match: data.vendor ? command.confidence : 0,
        overall: command.confidence
      },
      raw_transcript: transcript
    };

    const createdEvent = await voiceEventService.createVoiceEvent(voiceEventData);

    const successMessage = `${command.type} event created: ${data.quantity} ${data.unit || 'units'} of ${data.product}`;
    setFeedback(successMessage);
    speak(successMessage);
    
    if (onEventCreated) {
      onEventCreated(command.type);
    }
  };

  const findOrCreateProduct = async (productName: string): Promise<string> => {
    // First try to find existing product
    const { data: existingProducts } = await supabase
      .from('Products')
      .select('id, name')
      .ilike('name', `%${productName}%`);
    
    if (existingProducts && existingProducts.length > 0) {
      return existingProducts[0].id;
    }
    
    // Create new product if not found
    const { data: newProduct, error } = await supabase
      .from('Products')
      .insert({ 
        name: productName.charAt(0).toUpperCase() + productName.slice(1),
        category: 'Seafood',
        status: 'active'
      })
      .select('id')
      .single();
    
    if (error) {
      throw error;
    }
    
    return newProduct.id;
  };

  const handleQuery = async (command: VoiceCommand) => {
    const { data } = command;
    
    if (data?.product) {
      // Get inventory for specific product
      const { data: events, error } = await supabase
        .from('inventory_events')
        .select('quantity, event_type, created_at')
        .eq('product_id', await findOrCreateProduct(data.product))
        .order('created_at', { ascending: false })
        .limit(10);
      
      if (error) {
        throw error;
      }
      
      const totalReceived = events?.filter(e => e.event_type === 'receiving').reduce((sum, e) => sum + e.quantity, 0) || 0;
      const totalSold = events?.filter(e => e.event_type === 'sale').reduce((sum, e) => sum + e.quantity, 0) || 0;
      const totalDisposed = events?.filter(e => e.event_type === 'disposal').reduce((sum, e) => sum + e.quantity, 0) || 0;
      const currentInventory = totalReceived - totalSold - totalDisposed;
      
      const message = `Current ${data.product} inventory: ${currentInventory} units`;
      setFeedback(message);
      speak(message);
    } else {
      setFeedback('Please specify which product you want to check.');
      speak('Please specify which product you want to check.');
    }
  };

  const handleNavigation = (command: VoiceCommand) => {
    const destination = command.data?.destination;
    if (destination) {
      setActiveView(destination);
      const message = `Navigating to ${destination}`;
      setFeedback(message);
      speak(message);
    }
  };

  if (!isEnabled) {
    return (
      <div className={`bg-gray-100 rounded-lg p-4 text-center ${className}`}>
        <p className="text-sm text-gray-600">Voice recognition not available</p>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border p-4 ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-gray-900">Voice Assistant</h3>
        <div className="flex items-center gap-2">
          {isSpeaking && <Volume2 className="w-4 h-4 text-blue-500 animate-pulse" />}
          {!isSpeaking && <VolumeX className="w-4 h-4 text-gray-400" />}
        </div>
      </div>
      
      <div className="flex items-center gap-3 mb-3">
        <button
          onClick={isListening ? stopListening : startListening}
          disabled={isProcessing}
          className={`p-3 rounded-full transition-all ${
            isListening 
              ? 'bg-red-500 hover:bg-red-600 text-white animate-pulse' 
              : 'bg-blue-500 hover:bg-blue-600 text-white'
          } ${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          {isListening ? <MicOff className="w-5 h-5" /> : <Mic className="w-5 h-5" />}
        </button>
        
        <div className="flex-1">
          <div className="text-xs text-gray-500 mb-1">
            {isListening ? 'Listening...' : isProcessing ? 'Processing...' : 'Click to speak'}
          </div>
          {transcript && (
            <div className="text-sm text-gray-900 font-medium">{transcript}</div>
          )}
        </div>
      </div>
      
      {feedback && (
        <div className="bg-gray-50 rounded p-2 text-sm text-gray-700">
          {feedback}
        </div>
      )}
      
      <div className="mt-3 text-xs text-gray-500">
        <p><strong>Try saying:</strong></p>
        <ul className="list-disc list-inside space-y-1 mt-1">
          <li>"Add receiving 50 pounds cod from Pacific Seafoods"</li>
          <li>"Dispose 10 pounds expired salmon"</li>
          <li>"How much cod do we have?"</li>
          <li>"Show me events"</li>
        </ul>
      </div>
    </div>
  );
}