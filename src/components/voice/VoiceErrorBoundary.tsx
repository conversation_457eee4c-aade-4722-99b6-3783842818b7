import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Refresh<PERSON><PERSON>, Mic, MicOff } from 'lucide-react';

interface VoiceErrorBoundaryProps {
  children: ReactNode;
  fallbackComponent?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  enableVoiceRecovery?: boolean;
}

interface VoiceErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  isRetrying: boolean;
  retryCount: number;
  voiceSupported: boolean;
}

/**
 * Error boundary specifically designed for voice processing components
 * Provides graceful fallbacks and recovery mechanisms for voice failures
 */
export class VoiceErrorBoundary extends Component<VoiceErrorBoundaryProps, VoiceErrorBoundaryState> {
  private maxRetries = 3;
  private retryTimeout: NodeJS.Timeout | null = null;

  constructor(props: VoiceErrorBoundaryProps) {
    super(props);
    
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      isRetrying: false,
      retryCount: 0,
      voiceSupported: this.checkVoiceSupport()
    };
  }

  private checkVoiceSupport(): boolean {
    return !!(
      (window as any).webkitSpeechRecognition || 
      (window as any).SpeechRecognition ||
      (window as any).speechSynthesis
    );
  }

  static getDerivedStateFromError(error: Error): Partial<VoiceErrorBoundaryState> {
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('VoiceErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // Call the onError callback if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log to external service (could be enhanced with proper error reporting)
    this.logErrorToService(error, errorInfo);
  }

  private logErrorToService(error: Error, errorInfo: ErrorInfo) {
    // Enhanced error logging for voice processing issues
    const errorReport = {
      type: 'voice_processing_error',
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      voiceSupported: this.state.voiceSupported,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
      url: window.location.href
    };

    // Log to console for development
    console.error('Voice Processing Error Report:', errorReport);

    // In production, this could send to an error monitoring service
    // Example: Sentry, LogRocket, or custom analytics endpoint
  }

  private handleRetry = () => {
    if (this.state.retryCount >= this.maxRetries) {
      console.warn('Max retry attempts reached for voice component');
      return;
    }

    this.setState({ 
      isRetrying: true,
      retryCount: this.state.retryCount + 1
    });

    // Clear any existing timeout
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout);
    }

    // Retry after a delay (exponential backoff)
    const delay = Math.pow(2, this.state.retryCount) * 1000; // 1s, 2s, 4s...
    
    this.retryTimeout = setTimeout(() => {
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        isRetrying: false
      });
    }, delay);
  };

  private handleManualFallback = () => {
    // Force fallback to manual input
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });

    // Emit event to parent components to switch to manual mode
    window.dispatchEvent(new CustomEvent('voice:fallback-to-manual', {
      detail: { reason: 'user_initiated', error: this.state.error }
    }));
  };

  componentWillUnmount() {
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout);
    }
  }

  render() {
    if (this.state.hasError) {
      // Render custom fallback if provided
      if (this.props.fallbackComponent) {
        return this.props.fallbackComponent;
      }

      // Determine error type and appropriate message
      const isVoiceError = this.state.error?.message.includes('voice') || 
                          this.state.error?.message.includes('speech') ||
                          this.state.error?.message.includes('recognition');

      const isPermissionError = this.state.error?.message.includes('permission') ||
                               this.state.error?.message.includes('denied');

      const isNetworkError = this.state.error?.message.includes('network') ||
                            this.state.error?.message.includes('fetch');

      // Default error boundary UI with voice-specific messaging
      return (
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            
            <div className="ml-3 flex-1">
              <h3 className="text-lg font-medium text-red-800 mb-2">
                Voice Processing Error
              </h3>
              
              {/* Specific error messages based on error type */}
              {!this.state.voiceSupported && (
                <div className="mb-4">
                  <p className="text-red-700 mb-2">
                    Voice recognition is not supported in your browser.
                  </p>
                  <p className="text-sm text-red-600">
                    Please use Chrome, Edge, or Safari for voice features.
                  </p>
                </div>
              )}
              
              {isPermissionError && (
                <div className="mb-4">
                  <p className="text-red-700 mb-2">
                    Microphone permission was denied.
                  </p>
                  <p className="text-sm text-red-600">
                    Please allow microphone access in your browser settings and refresh the page.
                  </p>
                </div>
              )}
              
              {isNetworkError && (
                <div className="mb-4">
                  <p className="text-red-700 mb-2">
                    Network error during voice processing.
                  </p>
                  <p className="text-sm text-red-600">
                    Please check your internet connection and try again.
                  </p>
                </div>
              )}
              
              {isVoiceError && !isPermissionError && !isNetworkError && (
                <div className="mb-4">
                  <p className="text-red-700 mb-2">
                    Voice recognition service is temporarily unavailable.
                  </p>
                  <p className="text-sm text-red-600">
                    You can continue using manual input while we resolve this issue.
                  </p>
                </div>
              )}
              
              {/* Generic error message for unknown errors */}
              {!isVoiceError && !isPermissionError && !isNetworkError && (
                <div className="mb-4">
                  <p className="text-red-700 mb-2">
                    Something went wrong with the voice interface.
                  </p>
                  <p className="text-sm text-red-600">
                    Error: {this.state.error?.message}
                  </p>
                </div>
              )}

              {/* Action buttons */}
              <div className="flex flex-wrap gap-3 mt-4">
                {/* Retry button - only show if we haven't exceeded max retries */}
                {this.state.retryCount < this.maxRetries && this.state.voiceSupported && (
                  <button
                    onClick={this.handleRetry}
                    disabled={this.state.isRetrying}
                    className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {this.state.isRetrying ? (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                        Retrying...
                      </>
                    ) : (
                      <>
                        <Mic className="w-4 h-4 mr-2" />
                        Try Voice Again
                      </>
                    )}
                  </button>
                )}

                {/* Manual fallback button */}
                <button
                  onClick={this.handleManualFallback}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <MicOff className="w-4 h-4 mr-2" />
                  Use Manual Input
                </button>

                {/* Reload page button for persistent issues */}
                {this.state.retryCount >= this.maxRetries && (
                  <button
                    onClick={() => window.location.reload()}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Reload Page
                  </button>
                )}
              </div>

              {/* Development error details - only show in dev mode */}
              {process.env.NODE_ENV === 'development' && (
                <details className="mt-4">
                  <summary className="cursor-pointer text-sm text-red-600 hover:text-red-800">
                    Show Error Details (Development)
                  </summary>
                  <div className="mt-2 p-2 bg-red-100 rounded text-xs text-red-800 font-mono overflow-auto">
                    <div className="mb-2">
                      <strong>Error:</strong> {this.state.error?.message}
                    </div>
                    <div className="mb-2">
                      <strong>Stack:</strong>
                      <pre className="whitespace-pre-wrap mt-1">{this.state.error?.stack}</pre>
                    </div>
                    {this.state.errorInfo && (
                      <div>
                        <strong>Component Stack:</strong>
                        <pre className="whitespace-pre-wrap mt-1">{this.state.errorInfo.componentStack}</pre>
                      </div>
                    )}
                  </div>
                </details>
              )}
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default VoiceErrorBoundary;