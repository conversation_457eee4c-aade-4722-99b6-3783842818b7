import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Wifi, WifiOff, <PERSON>, Brain } from 'lucide-react';
import { REALTIME_ASSISTANT_TOOLS, executeRealtimeTool } from '../../services/RealtimeAssistantTools';

interface RealtimeVoiceAssistantProps {
  userId?: string;
  onEventCreated?: (event: any) => void;
  onError?: (error: string) => void;
}

interface ConnectionStatus {
  isConnected: boolean;
  isRecording: boolean;
  isProcessing: boolean;
  lastActivity: string | null;
}

interface RealtimeSession {
  id: string;
  created_at: string;
  model: string;
}

const RealtimeVoiceAssistant: React.FC<RealtimeVoiceAssistantProps> = ({
  userId = 'seafood-user',
  onEventCreated,
  onError
}) => {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    isConnected: false,
    isRecording: false,
    isProcessing: false,
    lastActivity: null
  });

  const [session, setSession] = useState<RealtimeSession | null>(null);
  const [recentActivities, setRecentActivities] = useState<string[]>([]);
  const [isInitializing, setIsInitializing] = useState(false);
  
  const wsRef = useRef<WebSocket | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const streamRef = useRef<MediaStream | null>(null);

  // Generate session ID for this conversation
  const sessionId = useRef(`session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);

  /**
   * Initialize OpenAI Realtime API connection
   */
  const initializeRealtimeConnection = async () => {
    if (isInitializing || connectionStatus.isConnected) return;
    
    setIsInitializing(true);
    
    try {
      const openaiApiKey = import.meta.env.VITE_OPENAI_API_KEY;
      if (!openaiApiKey) {
        throw new Error('OpenAI API key not configured');
      }

      // Create WebSocket connection to OpenAI Realtime API
      const ws = new WebSocket(
        'wss://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview-2024-10-01',
        ['realtime', `Bearer ${openaiApiKey}`]
      );

      ws.onopen = () => {
        console.log('Connected to OpenAI Realtime API');
        
        // Send session configuration
        ws.send(JSON.stringify({
          type: 'session.update',
          session: {
            modalities: ['text', 'audio'],
            instructions: `You are a seafood inventory management assistant for Pacific Cloud Seafoods. 

Use the provided tools to:
- Create inventory events (receiving, sales, disposal, physical counts)
- Search for products, vendors, and customers in the database
- Store and retrieve conversation memory for personalized assistance
- Get inventory status and recent activity

Always confirm details before creating events and provide helpful context about inventory levels.

Key Guidelines:
- Be professional but friendly
- Use proper seafood industry terminology
- Ask for clarification when needed
- Confirm important transactions
- Provide helpful inventory insights

You have access to:
- Supabase database for inventory, products, vendors, customers
- Zep memory database for conversation context and user preferences
- Real-time voice processing capabilities`,
            voice: 'alloy',
            turn_detection: {
              type: 'server_vad',
              threshold: 0.5,
              prefix_padding_ms: 300,
              silence_duration_ms: 200
            },
            tools: REALTIME_ASSISTANT_TOOLS,
            tool_choice: 'auto',
            temperature: 0.6
          }
        }));

        setConnectionStatus(prev => ({ ...prev, isConnected: true }));
        setSession({
          id: sessionId.current,
          created_at: new Date().toISOString(),
          model: 'gpt-4o-realtime-preview-2024-10-01'
        });
      };

      ws.onmessage = async (event) => {
        const message = JSON.parse(event.data);
        await handleRealtimeMessage(message);
      };

      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        onError?.('Connection to voice assistant failed');
        setConnectionStatus(prev => ({ ...prev, isConnected: false }));
      };

      ws.onclose = () => {
        console.log('WebSocket connection closed');
        setConnectionStatus(prev => ({ 
          ...prev, 
          isConnected: false, 
          isRecording: false,
          isProcessing: false 
        }));
      };

      wsRef.current = ws;

    } catch (error) {
      console.error('Failed to initialize realtime connection:', error);
      onError?.(error instanceof Error ? error.message : 'Connection failed');
    } finally {
      setIsInitializing(false);
    }
  };

  /**
   * Handle messages from OpenAI Realtime API
   */
  const handleRealtimeMessage = async (message: any) => {
    console.log('Realtime message:', message.type, message);

    switch (message.type) {
      case 'session.created':
        console.log('Session created:', message.session);
        addActivity('Voice assistant session started');
        break;

      case 'conversation.item.created':
        if (message.item.type === 'message' && message.item.role === 'user') {
          setConnectionStatus(prev => ({ ...prev, isProcessing: true }));
          addActivity(`User: ${message.item.content?.[0]?.transcript || 'Voice input received'}`);
        }
        break;

      case 'response.audio_transcript.delta':
        // Assistant is speaking
        setConnectionStatus(prev => ({ ...prev, lastActivity: 'Assistant speaking...' }));
        break;

      case 'response.function_call_arguments.delta':
        // Function call in progress
        setConnectionStatus(prev => ({ ...prev, lastActivity: 'Processing request...' }));
        break;

      case 'response.function_call_arguments.done':
        // Execute the function call
        if (message.name && message.arguments) {
          try {
            const args = JSON.parse(message.arguments);
            const result = await executeRealtimeTool(message.name, args);
            
            // Send function result back to assistant
            wsRef.current?.send(JSON.stringify({
              type: 'conversation.item.create',
              item: {
                type: 'function_call_output',
                call_id: message.call_id,
                output: JSON.stringify(result)
              }
            }));

            // Handle specific tool results
            if (message.name === 'create_inventory_event' && result.success) {
              onEventCreated?.(result.data);
              addActivity(`Created ${args.event_type} event: ${args.quantity} ${args.unit} ${args.product_name}`);
            }

            addActivity(`Tool executed: ${message.name}`);
            
          } catch (error) {
            console.error('Tool execution error:', error);
            wsRef.current?.send(JSON.stringify({
              type: 'conversation.item.create',
              item: {
                type: 'function_call_output',
                call_id: message.call_id,
                output: JSON.stringify({
                  success: false,
                  error: error instanceof Error ? error.message : 'Unknown error'
                })
              }
            }));
          }
        }
        break;

      case 'response.done':
        setConnectionStatus(prev => ({ 
          ...prev, 
          isProcessing: false,
          lastActivity: 'Ready'
        }));
        break;

      case 'input_audio_buffer.speech_started':
        setConnectionStatus(prev => ({ ...prev, isRecording: true }));
        break;

      case 'input_audio_buffer.speech_stopped':
        setConnectionStatus(prev => ({ ...prev, isRecording: false }));
        break;

      case 'error':
        console.error('Realtime API error:', message.error);
        onError?.(message.error.message || 'Voice processing error');
        break;
    }
  };

  /**
   * Start audio input
   */
  const startAudioInput = async () => {
    try {
      if (!connectionStatus.isConnected) {
        await initializeRealtimeConnection();
        return;
      }

      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 24000
        } 
      });
      
      streamRef.current = stream;

      const audioContext = new AudioContext({ sampleRate: 24000 });
      audioContextRef.current = audioContext;

      const source = audioContext.createMediaStreamSource(stream);
      const processor = audioContext.createScriptProcessor(4096, 1, 1);

      processor.onaudioprocess = (event) => {
        if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) return;

        const inputBuffer = event.inputBuffer;
        const inputData = inputBuffer.getChannelData(0);
        
        // Convert to 16-bit PCM
        const outputBuffer = new Int16Array(inputData.length);
        for (let i = 0; i < inputData.length; i++) {
          outputBuffer[i] = Math.max(-32768, Math.min(32767, inputData[i] * 32767));
        }

        // Send audio data to realtime API
        wsRef.current.send(JSON.stringify({
          type: 'input_audio_buffer.append',
          audio: btoa(String.fromCharCode(...new Uint8Array(outputBuffer.buffer)))
        }));
      };

      source.connect(processor);
      processor.connect(audioContext.destination);

      addActivity('Microphone started - listening for voice input');

    } catch (error) {
      console.error('Failed to start audio input:', error);
      onError?.('Microphone access denied or failed');
    }
  };

  /**
   * Stop audio input
   */
  const stopAudioInput = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }

    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }

    addActivity('Microphone stopped');
  };

  /**
   * Add activity to recent activities list
   */
  const addActivity = (activity: string) => {
    const timestamp = new Date().toLocaleTimeString();
    const activityWithTime = `${timestamp}: ${activity}`;
    
    setRecentActivities(prev => [activityWithTime, ...prev.slice(0, 4)]);
    setConnectionStatus(prev => ({ ...prev, lastActivity: activity }));
  };

  /**
   * Toggle microphone
   */
  const toggleMicrophone = async () => {
    if (streamRef.current) {
      stopAudioInput();
    } else {
      await startAudioInput();
    }
  };

  /**
   * Disconnect from realtime API
   */
  const disconnect = () => {
    stopAudioInput();
    wsRef.current?.close();
    wsRef.current = null;
    setSession(null);
    addActivity('Disconnected from voice assistant');
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, []);

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <Brain className="w-6 h-6 text-blue-600" />
          <h2 className="text-xl font-semibold text-gray-900">Voice Assistant</h2>
        </div>
        
        <div className="flex items-center space-x-2">
          {connectionStatus.isConnected ? (
            <Wifi className="w-5 h-5 text-green-500" />
          ) : (
            <WifiOff className="w-5 h-5 text-red-500" />
          )}
          <Database className="w-5 h-5 text-blue-500" />
        </div>
      </div>

      {/* Connection Status */}
      <div className="mb-4 p-3 bg-gray-50 rounded-lg">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">Status:</span>
          <span className={`font-medium ${
            connectionStatus.isConnected ? 'text-green-600' : 'text-red-600'
          }`}>
            {connectionStatus.isConnected ? 'Connected' : 'Disconnected'}
          </span>
        </div>
        
        {connectionStatus.lastActivity && (
          <div className="flex items-center justify-between text-sm mt-1">
            <span className="text-gray-600">Activity:</span>
            <span className="text-gray-800 font-medium">{connectionStatus.lastActivity}</span>
          </div>
        )}

        {session && (
          <div className="flex items-center justify-between text-sm mt-1">
            <span className="text-gray-600">Session:</span>
            <span className="text-blue-600 font-mono text-xs">{session.id.slice(-8)}</span>
          </div>
        )}
      </div>

      {/* Control Buttons */}
      <div className="flex space-x-3 mb-4">
        {!connectionStatus.isConnected ? (
          <button
            type="button"
            onClick={initializeRealtimeConnection}
            disabled={isInitializing}
            className="flex-1 flex items-center justify-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            <Wifi className="w-4 h-4" />
            <span>{isInitializing ? 'Connecting...' : 'Connect'}</span>
          </button>
        ) : (
          <>
            <button
              type="button"
              onClick={toggleMicrophone}
              className={`flex-1 flex items-center justify-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                connectionStatus.isRecording
                  ? 'bg-red-600 text-white hover:bg-red-700'
                  : streamRef.current
                  ? 'bg-green-600 text-white hover:bg-green-700'
                  : 'bg-gray-600 text-white hover:bg-gray-700'
              }`}
            >
              {connectionStatus.isRecording ? (
                <>
                  <MicOff className="w-4 h-4" />
                  <span>Recording...</span>
                </>
              ) : streamRef.current ? (
                <>
                  <Mic className="w-4 h-4" />
                  <span>Listening</span>
                </>
              ) : (
                <>
                  <Mic className="w-4 h-4" />
                  <span>Start Mic</span>
                </>
              )}
            </button>

            <button
              type="button"
              onClick={disconnect}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
            >
              Disconnect
            </button>
          </>
        )}
      </div>

      {/* Processing Status */}
      {connectionStatus.isProcessing && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <span className="text-blue-800 text-sm">Processing your request...</span>
          </div>
        </div>
      )}

      {/* Recent Activities */}
      {recentActivities.length > 0 && (
        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-2">Recent Activity</h3>
          <div className="space-y-1">
            {recentActivities.map((activity, index) => (
              <div
                key={index}
                className="text-xs text-gray-600 bg-gray-50 px-2 py-1 rounded"
              >
                {activity}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Usage Instructions */}
      {!connectionStatus.isConnected && (
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="text-sm font-medium text-blue-800 mb-1">Quick Start</h4>
          <ul className="text-xs text-blue-700 space-y-1">
            <li>• Click "Connect" to start the voice assistant</li>
            <li>• Click "Start Mic" to begin voice input</li>
            <li>• Say things like "Received 50 pounds of salmon from Pacific Seafoods"</li>
            <li>• The assistant will create inventory events and provide confirmations</li>
          </ul>
        </div>
      )}
    </div>
  );
};

export default RealtimeVoiceAssistant;