import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Mic, MicOff } from 'lucide-react';

interface VoiceFormData {
  quantity?: number;
  unit?: string;
  product?: string;
  vendor?: string;
  customer?: string;
  condition?: string;
  temperature?: number;
  temperatureUnit?: string;
  notes?: string;
  // Enhanced seafood-specific fields
  processing_method?: string; // Fresh, Frozen, IQF, etc.
  quality_grade?: string; // Premium, Grade A, etc.
  market_form?: string; // Whole, Fillets, H&G, etc.
  occurred_at?: string; // When the event actually occurred
}

type EventTypeAll =
  | 'receiving'
  | 'disposal'
  | 'physical_count'
  | 'sale'
  | 'order_placed'
  | 'shipping_arrival'
  | 'shipping_departure'
  | 'customer_delivery'
  | 'quality_check'
  | 'temperature_log'
  | 'maintenance';

interface VoiceFormIntegrationProps {
  onDataExtracted: (data: VoiceFormData) => void;
  eventType?: EventTypeAll;
  className?: string;
}

export default function VoiceFormIntegration({ 
  onDataExtracted, 
  eventType = 'receiving',
  className = '' 
}: VoiceFormIntegrationProps) {
  const [isListening, setIsListening] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [feedback, setFeedback] = useState('');
  const [isEnabled, setIsEnabled] = useState(true);
  
  const recognition = useRef<SpeechRecognition | null>(null);

  useEffect(() => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognitionClass = window.webkitSpeechRecognition || window.SpeechRecognition;
      recognition.current = new SpeechRecognitionClass();
      recognition.current.continuous = false;
      recognition.current.interimResults = false;
      recognition.current.lang = 'en-US';
      
      recognition.current.onstart = () => {
        setIsListening(true);
        setFeedback('Listening for form data...');
      };
      
      recognition.current.onend = () => {
        setIsListening(false);
        if (!isProcessing) {
          setFeedback('');
        }
      };
      
      recognition.current.onresult = async (event) => {
        const result = event.results[0][0].transcript;
        setTranscript(result);
        setIsProcessing(true);
        setFeedback('Extracting data...');
        
        try {
          const extractedData = extractFormData(result);
          onDataExtracted(extractedData);
          setFeedback('Form data extracted successfully!');
        } catch (error) {
          console.error('Voice extraction error:', error);
          setFeedback('Error extracting form data. Please try again.');
        } finally {
          setIsProcessing(false);
          setTimeout(() => {
            setFeedback('');
            setTranscript('');
          }, 3000);
        }
      };
      
      recognition.current.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        setIsListening(false);
        setIsProcessing(false);
        setFeedback('Voice recognition error. Please try again.');
      };
    } else {
      setIsEnabled(false);
    }

    return () => {
      if (recognition.current) {
        recognition.current.abort();
      }
    };
  }, [onDataExtracted]);

  const extractFormData = (text: string): VoiceFormData => {
    const lowerText = text.toLowerCase();
    const data: VoiceFormData = {};

    // Extract quantity and unit
    const quantityMatch = text.match(/(\d+(?:\.\d+)?)\s*(pounds?|lbs?|kilograms?|kg|cases?|units?)/i);
    if (quantityMatch) {
      data.quantity = parseFloat(quantityMatch[1]);
      data.unit = normalizeUnit(quantityMatch[2]);
    }

    // Enhanced seafood product detection with voice corrections
    const voiceCorrections: Record<string, string> = {
      'dangerous grab': 'dungeness crab',
      'dangerous crab': 'dungeness crab',
      'king grab': 'king crab',
      'snow grab': 'snow crab',
      'dover soul': 'dover sole',
      'petrel sole': 'petrale sole',
      'see scallops': 'sea scallops',
      'silver salmon': 'coho salmon',
      'king salmon': 'chinook salmon',
      'age and g': 'H&G',
      'head and gutted': 'H&G',
      'i q f': 'IQF',
      'individually quick frozen': 'IQF'
    };

    // Apply voice corrections
    let correctedText = lowerText;
    Object.entries(voiceCorrections).forEach(([wrong, right]) => {
      correctedText = correctedText.replace(new RegExp(wrong, 'gi'), right);
    });

    // Comprehensive seafood product database
    const seafoodProducts = [
      // Finfish
      'atlantic salmon', 'coho salmon', 'chinook salmon', 'sockeye salmon', 'pink salmon',
      'atlantic cod', 'pacific cod', 'black cod', 'sablefish', 'alaska pollock',
      'pacific halibut', 'atlantic halibut', 'dover sole', 'petrale sole', 'english sole',
      'yellowfin tuna', 'bluefin tuna', 'albacore tuna', 'skipjack tuna',
      'sea bass', 'striped bass', 'red snapper', 'grouper', 'mahi mahi', 'swordfish',
      // Shellfish
      'pacific oysters', 'eastern oysters', 'blue point oysters', 'kumamoto oysters',
      'manila clams', 'littleneck clams', 'razor clams', 'geoduck clams',
      'blue mussels', 'mediterranean mussels', 'green mussels',
      'sea scallops', 'bay scallops', 'diver scallops',
      // Crustaceans
      'maine lobster', 'spiny lobster', 'rock lobster',
      'dungeness crab', 'king crab', 'snow crab', 'blue crab', 'jonah crab',
      'tiger prawns', 'spot prawns', 'white shrimp', 'pink shrimp',
      // Generic terms (for backwards compatibility)
      'cod', 'salmon', 'halibut', 'crab', 'tuna', 'shrimp', 'lobster', 'scallops', 'oysters', 'clams', 'mussels'
    ];
    
    for (const product of seafoodProducts) {
      if (correctedText.includes(product)) {
        data.product = product;
        break;
      }
    }

    // Extract processing method
    const processingMethods = ['fresh', 'frozen', 'previously frozen', 'live', 'iqf', 'h&g', 'smoked', 'cured'];
    for (const method of processingMethods) {
      if (correctedText.includes(method)) {
        data.processing_method = method;
        break;
      }
    }

    // Extract market form
    const marketForms = ['whole', 'fillets', 'portions', 'steaks', 'skin-on', 'skinless', 'boneless'];
    for (const form of marketForms) {
      if (correctedText.includes(form)) {
        data.market_form = form;
        break;
      }
    }

    // Extract quality grade
    const qualityGrades = ['premium', 'grade a', 'grade b', 'sashimi grade', 'restaurant quality'];
    for (const grade of qualityGrades) {
      if (correctedText.includes(grade)) {
        data.quality_grade = grade;
        break;
      }
    }

    // Extract vendor/supplier (for receiving events)
    if (eventType === 'receiving') {
      const vendorMatch = text.match(/from\s+([^,]+?)(?:\s|,|$)/i);
      if (vendorMatch) {
        data.vendor = vendorMatch[1].trim();
      }
    }

    // Extract customer (for sales events)
    if (eventType === 'sale') {
      const customerMatch = text.match(/(?:to|for)\s+([^,]+?)(?:\s|,|\$|$)/i);
      if (customerMatch) {
        data.customer = customerMatch[1].trim();
      }
    }

    // Extract condition
    const conditionMatch = text.match(/condition\s+(excellent|good|fair|poor|damaged)/i);
    if (conditionMatch) {
      data.condition = conditionMatch[1].toLowerCase();
    }

    // Extract temperature
    const tempMatch = text.match(/(\d+(?:\.\d+)?)\s*degrees?\s*(fahrenheit|celsius|f|c)/i);
    if (tempMatch) {
      data.temperature = parseFloat(tempMatch[1]);
      data.temperatureUnit = tempMatch[2].toLowerCase().charAt(0) === 'f' ? 'fahrenheit' : 'celsius';
    }

    // Extract notes/comments
    const notesKeywords = ['note', 'comment', 'remark', 'observation'];
    for (const keyword of notesKeywords) {
      const notesMatch = text.match(new RegExp(`${keyword}[s:]?\\s+(.+)`, 'i'));
      if (notesMatch) {
        data.notes = notesMatch[1].trim();
        break;
      }
    }

    return data;
  };

  const normalizeUnit = (unit: string): string => {
    const normalized = unit.toLowerCase().replace(/s$/, ''); // Remove plural 's'
    switch (normalized) {
      case 'lb':
      case 'pound':
        return 'lbs';
      case 'kilogram':
        return 'kg';
      case 'case':
        return 'cases';
      case 'unit':
        return 'units';
      default:
        return normalized;
    }
  };

  const startListening = useCallback(() => {
    if (recognition.current && isEnabled && !isListening) {
      setTranscript('');
      setFeedback('');
      recognition.current.start();
    }
  }, [isEnabled, isListening]);

  const stopListening = useCallback(() => {
    if (recognition.current && isListening) {
      recognition.current.stop();
    }
  }, [isListening]);

  if (!isEnabled) {
    return null; // Don't show anything if voice is not supported
  }

  const getPromptText = () => {
    switch (eventType) {
      case 'receiving':
        return 'Say something like: "50 pounds salmon from Pacific Seafoods, condition excellent"';
      case 'disposal':
        return 'Say something like: "10 pounds expired cod, reason spoilage"';
      case 'physical_count':
        return 'Say something like: "75 pounds cod in freezer"';
      case 'sale':
        return 'Say something like: "30 pounds salmon to Restaurant ABC"';
      default:
        return 'Describe the inventory details you want to add';
    }
  };

  return (
    <div className={`bg-blue-50 border border-blue-200 rounded-lg p-3 ${className}`}>
      <div className="flex items-center gap-3 mb-2">
        <button
          onClick={isListening ? stopListening : startListening}
          disabled={isProcessing}
          className={`p-2 rounded-full transition-all ${
            isListening 
              ? 'bg-red-500 hover:bg-red-600 text-white animate-pulse' 
              : 'bg-blue-500 hover:bg-blue-600 text-white'
          } ${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          {isListening ? <MicOff className="w-4 h-4" /> : <Mic className="w-4 h-4" />}
        </button>
        
        <div className="flex-1">
          <div className="text-sm font-medium text-blue-900">
            Voice Form Fill
          </div>
          <div className="text-xs text-blue-700">
            {isListening ? 'Listening...' : isProcessing ? 'Processing...' : 'Click to speak'}
          </div>
        </div>
      </div>
      
      {transcript && (
        <div className="bg-white rounded p-2 text-sm text-gray-900 mb-2">
          <strong>Heard:</strong> "{transcript}"
        </div>
      )}
      
      {feedback && (
        <div className="bg-blue-100 rounded p-2 text-sm text-blue-800 mb-2">
          {feedback}
        </div>
      )}
      
      <div className="text-xs text-blue-600">
        <strong>Try saying:</strong> {getPromptText()}
      </div>
    </div>
  );
}