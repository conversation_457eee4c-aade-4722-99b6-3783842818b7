import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Mic,
  MicOff,
  Volume2,
  VolumeX,
  Smartphone,
  Tablet,
  Monitor,
  Wifi,
  WifiOff,
  Battery,
  Signal,
  Vibrate,
  RotateCw,
  Maximize2,
  Minimize2,
  <PERSON><PERSON><PERSON>,
  CheckCircle,
  AlertTriangle
} from 'lucide-react';
import { realtimeVoiceService } from '../../services/RealtimeVoiceService';
import { voiceErrorRecovery } from '../../services/AdvancedVoiceErrorRecovery';

interface DeviceInfo {
  type: 'mobile' | 'tablet' | 'desktop';
  orientation: 'portrait' | 'landscape';
  screenSize: { width: number; height: number };
  touchCapable: boolean;
  batteryLevel?: number;
  connectionType: string;
  isLowPowerMode: boolean;
}

interface MobileVoiceConfig {
  adaptiveUI: boolean;
  hapticFeedback: boolean;
  wakeWordEnabled: boolean;
  voiceActivation: boolean;
  powerOptimization: boolean;
  offlineCapable: boolean;
  adaptiveQuality: boolean;
}

interface VoiceSession {
  isActive: boolean;
  isListening: boolean;
  isProcessing: boolean;
  transcript: string;
  confidence: number;
  error?: string;
  networkQuality: 'excellent' | 'good' | 'poor' | 'offline';
}

export default function MobileOptimizedVoiceInterface() {
  // Device and interface state
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [config, setConfig] = useState<MobileVoiceConfig>({
    adaptiveUI: true,
    hapticFeedback: true,
    wakeWordEnabled: false, // Disabled by default for battery
    voiceActivation: true,
    powerOptimization: true,
    offlineCapable: true,
    adaptiveQuality: true
  });

  // Voice processing state
  const [voiceSession, setVoiceSession] = useState<VoiceSession>({
    isActive: false,
    isListening: false,
    isProcessing: false,
    transcript: '',
    confidence: 0,
    networkQuality: 'good'
  });

  // Mobile-specific state
  const [batteryLevel, setBatteryLevel] = useState<number | null>(null);
  const [connectionType, setConnectionType] = useState<string>('unknown');
  const [vibrationSupported, setVibrationSupported] = useState(false);
  const [wakeLockSupported, setWakeLockSupported] = useState(false);
  const [wakeLock, setWakeLock] = useState<WakeLockSentinel | null>(null);

  // Refs for mobile optimization
  const touchStartTime = useRef<number>(0);
  const lastTapTime = useRef<number>(0);
  const voiceButtonRef = useRef<HTMLButtonElement>(null);
  const sessionTimeoutRef = useRef<NodeJS.Timeout>();

  // Initialize device detection and mobile optimizations
  useEffect(() => {
    detectDevice();
    initializeMobileFeatures();
    setupEventListeners();
    startNetworkMonitoring();

    return () => {
      cleanup();
    };
  }, []);

  // Monitor device orientation changes
  useEffect(() => {
    const handleOrientationChange = () => {
      setTimeout(() => {
        detectDevice();
        if (config.adaptiveUI) {
          adaptUIToOrientation();
        }
      }, 100); // Delay to allow screen rotation to complete
    };

    window.addEventListener('orientationchange', handleOrientationChange);
    window.addEventListener('resize', handleOrientationChange);

    return () => {
      window.removeEventListener('orientationchange', handleOrientationChange);
      window.removeEventListener('resize', handleOrientationChange);
    };
  }, [config.adaptiveUI]);

  /**
   * Device detection and capability assessment
   */
  const detectDevice = useCallback(() => {
    const width = window.innerWidth;
    const height = window.innerHeight;
    const userAgent = navigator.userAgent;

    let deviceType: DeviceInfo['type'] = 'desktop';
    if (width <= 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)) {
      deviceType = width <= 480 ? 'mobile' : 'tablet';
    }

    const orientation: DeviceInfo['orientation'] = width > height ? 'landscape' : 'portrait';

    const newDeviceInfo: DeviceInfo = {
      type: deviceType,
      orientation,
      screenSize: { width, height },
      touchCapable: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
      connectionType: getConnectionType(),
      isLowPowerMode: isLowPowerMode(),
      batteryLevel
    };

    setDeviceInfo(newDeviceInfo);

    // Adapt configuration based on device capabilities
    if (newDeviceInfo.type === 'mobile' && config.powerOptimization) {
      optimizeForMobile(newDeviceInfo);
    }
  }, [batteryLevel, config.powerOptimization]);

  /**
   * Initialize mobile-specific features
   */
  const initializeMobileFeatures = async () => {
    // Check for vibration support
    setVibrationSupported('vibrate' in navigator);

    // Check for wake lock support
    setWakeLockSupported('wakeLock' in navigator);

    // Initialize battery monitoring
    if ('getBattery' in navigator) {
      try {
        const battery = await (navigator as any).getBattery();
        setBatteryLevel(Math.round(battery.level * 100));
        
        battery.addEventListener('levelchange', () => {
          setBatteryLevel(Math.round(battery.level * 100));
        });
      } catch (error) {
        console.warn('Battery monitoring not available:', error);
      }
    }

    // Initialize connection monitoring
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      setConnectionType(connection.effectiveType || 'unknown');
      
      connection.addEventListener('change', () => {
        setConnectionType(connection.effectiveType || 'unknown');
      });
    }
  };

  /**
   * Setup mobile-optimized event listeners
   */
  const setupEventListeners = () => {
    // Prevent zoom on double-tap for voice button
    const preventZoom = (e: Event) => {
      e.preventDefault();
    };

    if (voiceButtonRef.current) {
      voiceButtonRef.current.addEventListener('touchend', preventZoom, { passive: false });
    }

    // Handle app visibility changes for power optimization
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Handle page unload
    window.addEventListener('beforeunload', handleBeforeUnload);
  };

  /**
   * Optimize configuration for mobile devices
   */
  const optimizeForMobile = (device: DeviceInfo) => {
    const optimizations: Partial<MobileVoiceConfig> = {};

    // Low battery optimizations
    if (device.batteryLevel && device.batteryLevel < 20) {
      optimizations.wakeWordEnabled = false;
      optimizations.adaptiveQuality = true;
      optimizations.powerOptimization = true;
    }

    // Poor connection optimizations
    if (device.connectionType === 'slow-2g' || device.connectionType === '2g') {
      optimizations.adaptiveQuality = true;
      optimizations.offlineCapable = true;
    }

    // Low power mode optimizations
    if (device.isLowPowerMode) {
      optimizations.wakeWordEnabled = false;
      optimizations.hapticFeedback = false;
    }

    if (Object.keys(optimizations).length > 0) {
      setConfig(prev => ({ ...prev, ...optimizations }));
    }
  };

  /**
   * Adaptive UI based on device orientation and size
   */
  const adaptUIToOrientation = () => {
    if (!deviceInfo) return;

    if (deviceInfo.type === 'mobile' && deviceInfo.orientation === 'landscape') {
      // Compact horizontal layout for landscape mobile
      document.documentElement.style.setProperty('--voice-button-size', '60px');
      document.documentElement.style.setProperty('--voice-interface-height', 'auto');
    } else if (deviceInfo.type === 'mobile') {
      // Full-height layout for portrait mobile
      document.documentElement.style.setProperty('--voice-button-size', '80px');
      document.documentElement.style.setProperty('--voice-interface-height', '100vh');
    } else {
      // Default desktop layout
      document.documentElement.style.setProperty('--voice-button-size', '64px');
      document.documentElement.style.setProperty('--voice-interface-height', 'auto');
    }
  };

  /**
   * Start or stop voice session with mobile optimizations
   */
  const toggleVoiceSession = async () => {
    if (voiceSession.isActive) {
      await stopVoiceSession();
    } else {
      await startVoiceSession();
    }
  };

  const startVoiceSession = async () => {
    try {
      // Request wake lock to prevent screen from turning off during voice input
      if (wakeLockSupported && !wakeLock) {
        try {
          const lock = await navigator.wakeLock.request('screen');
          setWakeLock(lock);
        } catch (error) {
          console.warn('Wake lock failed:', error);
        }
      }

      // Haptic feedback for session start
      if (config.hapticFeedback && vibrationSupported) {
        navigator.vibrate([50, 50, 50]); // Triple short vibration
      }

      // Initialize voice service with mobile-optimized settings
      const success = await realtimeVoiceService.startSession();
      
      if (success) {
        setVoiceSession(prev => ({
          ...prev,
          isActive: true,
          isListening: true,
          error: undefined
        }));

        // Set session timeout for battery optimization
        if (config.powerOptimization) {
          sessionTimeoutRef.current = setTimeout(() => {
            stopVoiceSession();
          }, 300000); // 5 minutes max session
        }
      } else {
        throw new Error('Failed to start voice session');
      }

    } catch (error) {
      console.error('Voice session start failed:', error);
      
      // Haptic feedback for error
      if (config.hapticFeedback && vibrationSupported) {
        navigator.vibrate([200, 100, 200]); // Error pattern
      }

      setVoiceSession(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Unknown error'
      }));
    }
  };

  const stopVoiceSession = async () => {
    try {
      // Clear session timeout
      if (sessionTimeoutRef.current) {
        clearTimeout(sessionTimeoutRef.current);
      }

      // Release wake lock
      if (wakeLock) {
        await wakeLock.release();
        setWakeLock(null);
      }

      // Haptic feedback for session end
      if (config.hapticFeedback && vibrationSupported) {
        navigator.vibrate(100); // Single vibration
      }

      await realtimeVoiceService.stopSession();
      
      setVoiceSession(prev => ({
        ...prev,
        isActive: false,
        isListening: false,
        isProcessing: false,
        transcript: '',
        error: undefined
      }));

    } catch (error) {
      console.error('Voice session stop failed:', error);
    }
  };

  /**
   * Handle voice button touch interactions with mobile optimizations
   */
  const handleTouchStart = (e: React.TouchEvent) => {
    e.preventDefault();
    touchStartTime.current = Date.now();
    
    // Immediate haptic feedback on touch
    if (config.hapticFeedback && vibrationSupported) {
      navigator.vibrate(25); // Light tap feedback
    }
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    e.preventDefault();
    const touchDuration = Date.now() - touchStartTime.current;
    const currentTime = Date.now();
    
    // Handle double-tap for fullscreen
    if (currentTime - lastTapTime.current < 300) {
      toggleFullscreen();
      return;
    }
    lastTapTime.current = currentTime;

    // Long press (>500ms) for continuous listening
    if (touchDuration > 500) {
      if (!voiceSession.isActive) {
        startVoiceSession();
      }
    } else {
      // Short tap to toggle
      toggleVoiceSession();
    }
  };

  /**
   * Network quality monitoring for mobile
   */
  const startNetworkMonitoring = () => {
    const updateNetworkQuality = () => {
      const connection = (navigator as any).connection;
      let quality: VoiceSession['networkQuality'] = 'good';
      
      if (!navigator.onLine) {
        quality = 'offline';
      } else if (connection) {
        switch (connection.effectiveType) {
          case 'slow-2g':
          case '2g':
            quality = 'poor';
            break;
          case '3g':
            quality = 'good';
            break;
          case '4g':
          default:
            quality = 'excellent';
            break;
        }
      }
      
      setVoiceSession(prev => ({ ...prev, networkQuality: quality }));
    };

    updateNetworkQuality();
    
    // Monitor network changes
    window.addEventListener('online', updateNetworkQuality);
    window.addEventListener('offline', updateNetworkQuality);
    
    if ('connection' in navigator) {
      (navigator as any).connection.addEventListener('change', updateNetworkQuality);
    }
  };

  /**
   * Toggle fullscreen for immersive voice interface
   */
  const toggleFullscreen = async () => {
    if (!document.fullscreenElement) {
      try {
        await document.documentElement.requestFullscreen();
        setIsFullscreen(true);
        
        // Haptic confirmation
        if (config.hapticFeedback && vibrationSupported) {
          navigator.vibrate([50, 25, 50]);
        }
      } catch (error) {
        console.warn('Fullscreen request failed:', error);
      }
    } else {
      await document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  /**
   * Handle visibility changes for power optimization
   */
  const handleVisibilityChange = () => {
    if (document.hidden && voiceSession.isActive) {
      // App is in background, stop voice session to save battery
      if (config.powerOptimization) {
        stopVoiceSession();
      }
    }
  };

  /**
   * Handle page unload
   */
  const handleBeforeUnload = () => {
    if (voiceSession.isActive) {
      stopVoiceSession();
    }
  };

  /**
   * Cleanup resources
   */
  const cleanup = () => {
    if (sessionTimeoutRef.current) {
      clearTimeout(sessionTimeoutRef.current);
    }
    
    if (wakeLock) {
      wakeLock.release();
    }
    
    if (voiceSession.isActive) {
      realtimeVoiceService.stopSession();
    }
  };

  /**
   * Utility functions
   */
  const getConnectionType = (): string => {
    const connection = (navigator as any).connection;
    return connection?.effectiveType || 'unknown';
  };

  const isLowPowerMode = (): boolean => {
    // Heuristic detection of low power mode
    const connection = (navigator as any).connection;
    return connection?.saveData === true || false;
  };

  const getDeviceIcon = () => {
    if (!deviceInfo) return <Monitor className="w-5 h-5" />;
    
    switch (deviceInfo.type) {
      case 'mobile': return <Smartphone className="w-5 h-5" />;
      case 'tablet': return <Tablet className="w-5 h-5" />;
      default: return <Monitor className="w-5 h-5" />;
    }
  };

  const getNetworkIcon = () => {
    switch (voiceSession.networkQuality) {
      case 'excellent': return <Signal className="w-4 h-4 text-green-600" />;
      case 'good': return <Signal className="w-4 h-4 text-yellow-600" />;
      case 'poor': return <Signal className="w-4 h-4 text-red-600" />;
      case 'offline': return <WifiOff className="w-4 h-4 text-gray-600" />;
    }
  };

  // Dynamic CSS classes based on device type and state
  const containerClasses = `
    ${deviceInfo?.type === 'mobile' ? 'mobile-voice-interface' : ''}
    ${deviceInfo?.orientation === 'landscape' ? 'landscape-mode' : 'portrait-mode'}
    ${isFullscreen ? 'fullscreen-mode' : ''}
    bg-white rounded-lg shadow-sm border p-4 transition-all duration-300
  `;

  const voiceButtonClasses = `
    relative flex items-center justify-center rounded-full transition-all duration-200 touch-manipulation
    ${voiceSession.isActive 
      ? voiceSession.isListening
        ? 'bg-red-500 hover:bg-red-600 text-white animate-pulse scale-110'
        : 'bg-blue-500 hover:bg-blue-600 text-white'
      : 'bg-gray-500 hover:bg-gray-600 text-white'
    }
    ${deviceInfo?.type === 'mobile' ? 'w-20 h-20' : 'w-16 h-16'}
    active:scale-95 select-none
  `;

  return (
    <div className={containerClasses}>
      {/* Device and connection status bar */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          {getDeviceIcon()}
          <span className="text-sm text-gray-600">
            {deviceInfo?.type} • {deviceInfo?.orientation}
          </span>
          {deviceInfo?.screenSize && (
            <span className="text-xs text-gray-500">
              {deviceInfo.screenSize.width}×{deviceInfo.screenSize.height}
            </span>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {getNetworkIcon()}
          {batteryLevel !== null && (
            <div className="flex items-center gap-1">
              <Battery className={`w-4 h-4 ${
                batteryLevel < 20 ? 'text-red-600' : 
                batteryLevel < 50 ? 'text-yellow-600' : 'text-green-600'
              }`} />
              <span className="text-xs text-gray-600">{batteryLevel}%</span>
            </div>
          )}
          {isFullscreen ? (
            <Minimize2 className="w-4 h-4 text-gray-600" />
          ) : (
            <Maximize2 className="w-4 h-4 text-gray-600" />
          )}
        </div>
      </div>

      {/* Main voice interface */}
      <div className={`text-center ${deviceInfo?.type === 'mobile' ? 'py-8' : 'py-4'}`}>
        {/* Voice button */}
        <button
          ref={voiceButtonRef}
          className={voiceButtonClasses}
          onTouchStart={handleTouchStart}
          onTouchEnd={handleTouchEnd}
          onClick={deviceInfo?.touchCapable ? undefined : toggleVoiceSession}
          aria-label={voiceSession.isActive ? 'Stop voice input' : 'Start voice input'}
        >
          {voiceSession.isProcessing ? (
            <RotateCw className="w-8 h-8 animate-spin" />
          ) : voiceSession.isListening ? (
            <MicOff className="w-8 h-8" />
          ) : (
            <Mic className="w-8 h-8" />
          )}
          
          {/* Pulse animation for active listening */}
          {voiceSession.isListening && (
            <div className="absolute inset-0 rounded-full bg-red-400 opacity-30 animate-ping" />
          )}
        </button>

        {/* Status text */}
        <div className="mt-4 space-y-2">
          <div className={`text-sm font-medium ${
            voiceSession.isActive ? 'text-blue-600' : 'text-gray-600'
          }`}>
            {voiceSession.isProcessing ? 'Processing...' :
             voiceSession.isListening ? 'Listening...' :
             voiceSession.isActive ? 'Ready' : 'Tap to start'}
          </div>
          
          {voiceSession.transcript && (
            <div className="text-gray-900 font-medium min-h-[2rem] p-2 bg-gray-50 rounded border">
              {voiceSession.transcript}
            </div>
          )}
          
          {voiceSession.error && (
            <div className="text-red-600 text-sm bg-red-50 p-2 rounded border border-red-200">
              {voiceSession.error}
            </div>
          )}
        </div>

        {/* Mobile-specific instructions */}
        {deviceInfo?.type === 'mobile' && (
          <div className="mt-6 text-xs text-gray-500 space-y-1">
            <p>• Tap to start/stop voice input</p>
            <p>• Long press for continuous listening</p>
            <p>• Double-tap for fullscreen mode</p>
            {config.hapticFeedback && <p>• Haptic feedback enabled</p>}
          </div>
        )}
      </div>

      {/* Advanced controls for tablets/desktop */}
      {deviceInfo?.type !== 'mobile' && (
        <div className="mt-4 pt-4 border-t">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <label className="flex items-center gap-2 text-sm">
                <input
                  type="checkbox"
                  checked={config.hapticFeedback}
                  onChange={(e) => setConfig(prev => ({ ...prev, hapticFeedback: e.target.checked }))}
                  className="rounded"
                />
                Haptic feedback
              </label>
              
              <label className="flex items-center gap-2 text-sm">
                <input
                  type="checkbox"
                  checked={config.powerOptimization}
                  onChange={(e) => setConfig(prev => ({ ...prev, powerOptimization: e.target.checked }))}
                  className="rounded"
                />
                Power optimization
              </label>
            </div>
            
            <button
              onClick={toggleFullscreen}
              className="flex items-center gap-1 px-3 py-1 text-sm text-gray-600 hover:text-gray-800 border rounded"
            >
              {isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
              {isFullscreen ? 'Exit' : 'Fullscreen'}
            </button>
          </div>
        </div>
      )}

      {/* Connection quality indicator */}
      <div className="mt-4 flex items-center justify-center gap-2 text-xs text-gray-500">
        <span>Connection:</span>
        <span className={`font-medium ${
          voiceSession.networkQuality === 'excellent' ? 'text-green-600' :
          voiceSession.networkQuality === 'good' ? 'text-yellow-600' :
          voiceSession.networkQuality === 'poor' ? 'text-red-600' :
          'text-gray-600'
        }`}>
          {voiceSession.networkQuality.toUpperCase()}
        </span>
        {voiceSession.networkQuality === 'offline' && config.offlineCapable && (
          <span className="text-blue-600">• Offline mode available</span>
        )}
      </div>
    </div>
  );
}

// Add mobile-specific CSS classes
const mobileStyles = `
  .mobile-voice-interface {
    min-height: 50vh;
    touch-action: manipulation;
    user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
  }

  .landscape-mode {
    max-height: 80vh;
  }

  .portrait-mode {
    min-height: 60vh;
  }

  .fullscreen-mode {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    border-radius: 0;
    border: none;
    display: flex;
    flex-direction: column;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }

  .touch-manipulation {
    touch-action: manipulation;
  }

  @media (max-width: 768px) {
    .mobile-voice-interface {
      padding: 1rem;
      margin: 0;
      border-radius: 0.5rem;
    }
    
    .mobile-voice-interface button {
      min-height: 44px; /* iOS touch target minimum */
    }
  }

  @media (orientation: landscape) and (max-height: 500px) {
    .landscape-mode .mobile-voice-interface {
      padding: 0.5rem;
    }
    
    .landscape-mode .text-center {
      padding: 1rem 0;
    }
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = mobileStyles;
  document.head.appendChild(styleSheet);
}