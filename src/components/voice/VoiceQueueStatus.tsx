import React, { useState, useEffect } from 'react';
import { WifiOff, Wifi, Clock, CheckCircle, AlertCircle, RefreshCw } from 'lucide-react';
import { voiceErrorHandler } from '../../services/VoiceErrorHandler';

interface QueueStatus {
  isOnline: boolean;
  queuedEvents: number;
  failedEvents: number;
}

const VoiceQueueStatus: React.FC = () => {
  const [queueStatus, setQueueStatus] = useState<QueueStatus>({
    isOnline: true,
    queuedEvents: 0,
    failedEvents: 0
  });
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    const updateStatus = () => {
      setQueueStatus(voiceErrorHandler.getQueueStatus());
    };

    // Initial status
    updateStatus();

    // Update status every 5 seconds
    const interval = setInterval(updateStatus, 5000);

    // Listen for online/offline events
    const handleOnline = () => {
      updateStatus();
      setIsProcessing(true);
      // Stop processing indicator after a delay
      setTimeout(() => setIsProcessing(false), 3000);
    };

    const handleOffline = () => {
      updateStatus();
      setIsProcessing(false);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      clearInterval(interval);
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const handleClearQueue = () => {
    voiceErrorHandler.clearQueue();
    setQueueStatus(voiceErrorHandler.getQueueStatus());
  };

  const handleRetryQueue = async () => {
    setIsProcessing(true);
    try {
      await voiceErrorHandler.processQueuedEvents();
    } catch (error) {
      console.error('Failed to process queue:', error);
    } finally {
      setIsProcessing(false);
      setQueueStatus(voiceErrorHandler.getQueueStatus());
    }
  };

  // Don't show if there are no queued events and we're online
  if (queueStatus.isOnline && queueStatus.queuedEvents === 0 && queueStatus.failedEvents === 0) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-sm">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            {queueStatus.isOnline ? (
              <Wifi className="w-5 h-5 text-green-500" />
            ) : (
              <WifiOff className="w-5 h-5 text-red-500" />
            )}
            <span className="font-medium text-gray-900">
              Voice Queue
            </span>
          </div>

          {isProcessing && (
            <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />
          )}
        </div>

        <div className="space-y-2 text-sm">
          {/* Connection status */}
          <div className="flex items-center justify-between">
            <span className="text-gray-600">Status:</span>
            <span className={queueStatus.isOnline ? 'text-green-600' : 'text-red-600'}>
              {queueStatus.isOnline ? 'Online' : 'Offline'}
            </span>
          </div>

          {/* Queued events */}
          {queueStatus.queuedEvents > 0 && (
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-1">
                <Clock className="w-4 h-4 text-yellow-500" />
                <span className="text-gray-600">Queued:</span>
              </div>
              <span className="text-yellow-600 font-medium">
                {queueStatus.queuedEvents} events
              </span>
            </div>
          )}

          {/* Failed events */}
          {queueStatus.failedEvents > 0 && (
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-1">
                <AlertCircle className="w-4 h-4 text-red-500" />
                <span className="text-gray-600">Failed:</span>
              </div>
              <span className="text-red-600 font-medium">
                {queueStatus.failedEvents} events
              </span>
            </div>
          )}

          {/* Status message */}
          <div className="text-xs text-gray-500 mt-2 pt-2 border-t border-gray-100">
            {!queueStatus.isOnline ? (
              'Voice inputs will be processed when connection is restored'
            ) : queueStatus.queuedEvents > 0 ? (
              isProcessing ? 'Processing queued voice inputs...' : 'Voice inputs are being processed'
            ) : queueStatus.failedEvents > 0 ? (
              'Some voice inputs failed to process'
            ) : (
              'All voice inputs have been processed'
            )}
          </div>
        </div>

        {/* Action buttons */}
        {(queueStatus.queuedEvents > 0 || queueStatus.failedEvents > 0) && (
          <div className="flex space-x-2 mt-3 pt-3 border-t border-gray-100">
            {queueStatus.isOnline && queueStatus.queuedEvents > 0 && (
              <button
                type="button"
                onClick={handleRetryQueue}
                disabled={isProcessing}
                className="flex-1 inline-flex items-center justify-center px-3 py-2 text-xs font-medium rounded-md bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isProcessing ? (
                  <>
                    <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <RefreshCw className="w-3 h-3 mr-1" />
                    Retry Now
                  </>
                )}
              </button>
            )}

            <button
              type="button"
              onClick={handleClearQueue}
              className="flex-1 inline-flex items-center justify-center px-3 py-2 text-xs font-medium rounded-md border border-gray-300 text-gray-700 bg-white hover:bg-gray-50"
            >
              Clear Queue
            </button>
          </div>
        )}

        {/* Success indicator */}
        {queueStatus.isOnline && queueStatus.queuedEvents === 0 && queueStatus.failedEvents === 0 && (
          <div className="flex items-center justify-center text-green-600 text-sm font-medium mt-2">
            <CheckCircle className="w-4 h-4 mr-1" />
            All processed
          </div>
        )}
      </div>
    </div>
  );
};

export default VoiceQueueStatus;