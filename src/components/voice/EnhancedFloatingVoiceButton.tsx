import React, { useState } from 'react';
import { Mic, X, Zap, Settings } from 'lucide-react';
import EnhancedVoiceAssistant from './EnhancedVoiceAssistant';

interface EnhancedFloatingVoiceButtonProps {
  onEventCreated?: (eventData: any) => void;
}

export default function EnhancedFloatingVoiceButton({ onEventCreated }: EnhancedFloatingVoiceButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [settings, setSettings] = useState({
    autoExecute: true,
    showTranscript: true,
    showMetrics: false,
    voiceSpeed: 0.9
  });

  const handleEventCreated = (eventData: any) => {
    onEventCreated?.(eventData);
    // Auto-close after successful event creation (with delay for user to see feedback)
    setTimeout(() => setIsOpen(false), 3000);
  };

  const toggleSetting = (key: keyof typeof settings) => {
    setSettings(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  return (
    <>
      {/* Floating Button */}
      <div className="fixed bottom-6 right-6 z-50">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className={`p-4 rounded-full shadow-lg transition-all duration-200 ${
            isOpen 
              ? 'bg-red-500 hover:bg-red-600' 
              : 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700'
          } text-white group`}
          title={isOpen ? "Close AI Voice Assistant" : "Open AI Voice Assistant"}
        >
          <div className="relative">
            {isOpen ? (
              <X className="w-6 h-6" />
            ) : (
              <>
                <Mic className="w-6 h-6" />
                <Zap className="w-3 h-3 absolute -top-1 -right-1 text-yellow-300" />
              </>
            )}
          </div>
          
          {/* Pulse effect when closed */}
          {!isOpen && (
            <div className="absolute inset-0 rounded-full bg-blue-400 animate-ping opacity-20" />
          )}
        </button>

        {/* Settings Button */}
        {isOpen && (
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="absolute top-0 left-0 transform -translate-x-12 p-2 bg-gray-600 hover:bg-gray-700 text-white rounded-full shadow-lg transition-all duration-200"
            title="Voice Settings"
          >
            <Settings className="w-4 h-4" />
          </button>
        )}
      </div>

      {/* Voice Assistant Panel */}
      {isOpen && (
        <div className="fixed bottom-24 right-6 z-40 w-96 max-w-[calc(100vw-3rem)]">
          {/* Settings Panel */}
          {showSettings && (
            <div className="mb-3 bg-white rounded-lg shadow-xl border p-4">
              <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                <Settings className="w-4 h-4" />
                Voice Settings
              </h4>
              
              <div className="space-y-3">
                <label className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Auto-execute high confidence commands</span>
                  <input
                    type="checkbox"
                    checked={settings.autoExecute}
                    onChange={() => toggleSetting('autoExecute')}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </label>
                
                <label className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Show transcript</span>
                  <input
                    type="checkbox"
                    checked={settings.showTranscript}
                    onChange={() => toggleSetting('showTranscript')}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </label>
                
                <label className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Show performance metrics</span>
                  <input
                    type="checkbox"
                    checked={settings.showMetrics}
                    onChange={() => toggleSetting('showMetrics')}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </label>
              </div>
              
              <div className="mt-3 pt-3 border-t">
                <div className="text-xs text-gray-500">
                  <div className="flex items-center justify-between">
                    <span>AI Model:</span>
                    <span className="font-medium text-blue-600">GPT-4 + Whisper</span>
                  </div>
                  <div className="flex items-center justify-between mt-1">
                    <span>Target Latency:</span>
                    <span className="font-medium text-green-600">&lt;300ms</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Main Assistant Panel */}
          <EnhancedVoiceAssistant
            onEventCreated={handleEventCreated}
            autoExecute={settings.autoExecute}
            showTranscript={settings.showTranscript}
            showMetrics={settings.showMetrics}
            className="shadow-xl"
          />
          
          {/* Quick Tips */}
          <div className="mt-3 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200 p-3">
            <div className="text-xs text-blue-700">
              <div className="font-medium mb-1 flex items-center gap-1">
                <Zap className="w-3 h-3" />
                AI-Powered Recognition
              </div>
              <div className="text-blue-600">
                • Specialized for seafood terminology<br/>
                • Understands vendor names like "49th State Seafoods"<br/>
                • Processes natural dates like "yesterday"<br/>
                • High accuracy for industry-specific commands
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 z-30 bg-black bg-opacity-20 backdrop-blur-sm"
          onClick={() => {
            setIsOpen(false);
            setShowSettings(false);
          }}
        />
      )}
    </>
  );
}