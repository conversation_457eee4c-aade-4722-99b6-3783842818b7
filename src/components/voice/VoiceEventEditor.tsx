import React, { useState, useEffect, useCallback } from 'react';
import { 
  Save, 
  X, 
  AlertTriangle, 
  CheckCircle, 
  Volume2, 
  Clock,
  User,
  TrendingUp,
  Package,
  Thermometer,
  FileText,
  AlertCircle
} from 'lucide-react';
import { voiceEventService } from '../../services/VoiceEventService';
import { VoiceEvent, EventAudit } from '../../types/schema';

interface VoiceEventEditorProps {
  event: VoiceEvent;
  onSave: (updatedEvent: VoiceEvent) => Promise<void>;
  onCancel: () => void;
  readOnly?: boolean;
  userId?: string;
}

interface FormData {
  event_type: 'receiving' | 'disposal' | 'physical_count' | 'sale';
  product_name: string;
  quantity: number;
  unit: 'lbs' | 'kg' | 'cases' | 'units';
  vendor_name?: string;
  customer_name?: string;
  condition?: 'Excellent' | 'Good' | 'Fair' | 'Poor' | 'Damaged';
  temperature?: number;
  temperature_unit?: 'fahrenheit' | 'celsius';
  processing_method?: string;
  quality_grade?: string;
  market_form?: string;
  notes?: string;
  occurred_at: string;
}

interface ValidationErrors {
  [key: string]: string;
}

interface ConfidenceDisplayProps {
  score: number;
  breakdown?: {
    product_match: number;
    quantity_extraction: number;
    vendor_match: number;
    overall: number;
  };
}

const ConfidenceDisplay: React.FC<ConfidenceDisplayProps> = ({ score, breakdown }) => {
  const getConfidenceColor = (score: number) => {
    if (score >= 0.9) return 'text-green-600 bg-green-50';
    if (score >= 0.7) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  const formatScore = (score: number) => Math.round(score * 100);

  return (
    <div className="bg-gray-50 p-4 rounded-lg">
      <h4 className="text-sm font-medium text-gray-700 mb-3">Voice Processing Confidence</h4>
      
      <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getConfidenceColor(score)}`}>
        <TrendingUp className="w-4 h-4 mr-1" />
        Overall: {formatScore(score)}%
      </div>

      {breakdown && (
        <div className="mt-3 grid grid-cols-2 gap-3 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">Product Match:</span>
            <span className={`font-medium ${getConfidenceColor(breakdown.product_match)}`}>
              {formatScore(breakdown.product_match)}%
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Quantity:</span>
            <span className={`font-medium ${getConfidenceColor(breakdown.quantity_extraction)}`}>
              {formatScore(breakdown.quantity_extraction)}%
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Vendor Match:</span>
            <span className={`font-medium ${getConfidenceColor(breakdown.vendor_match)}`}>
              {formatScore(breakdown.vendor_match)}%
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export const VoiceEventEditor: React.FC<VoiceEventEditorProps> = ({
  event,
  onSave,
  onCancel,
  readOnly = false,
  userId
}) => {
  const [formData, setFormData] = useState<FormData>({
    event_type: event.event_type,
    product_name: event.product_name,
    quantity: event.quantity,
    unit: event.unit,
    vendor_name: event.vendor_name,
    customer_name: event.customer_name,
    condition: event.condition,
    temperature: event.temperature,
    temperature_unit: event.temperature_unit || 'fahrenheit',
    processing_method: event.processing_method,
    quality_grade: event.quality_grade,
    market_form: event.market_form,
    notes: event.notes,
    occurred_at: event.occurred_at ? new Date(event.occurred_at).toISOString().slice(0, 16) : ''
  });

  const [errors, setErrors] = useState<ValidationErrors>({});
  const [saving, setSaving] = useState(false);
  const [auditTrail, setAuditTrail] = useState<EventAudit[]>([]);
  const [showAuditTrail, setShowAuditTrail] = useState(false);
  const [conflictDetected, setConflictDetected] = useState(false);
  const [lastModified, setLastModified] = useState<string | undefined>(event.updated_at);

  // Load audit trail
  useEffect(() => {
    const loadAuditTrail = async () => {
      if (event.id) {
        try {
          const trail = await voiceEventService.getEventAuditTrail(event.id);
          setAuditTrail(trail);
        } catch (error) {
          console.error('Error loading audit trail:', error);
        }
      }
    };

    loadAuditTrail();
  }, [event.id]);

  // Validate form data
  const validateForm = useCallback((): ValidationErrors => {
    const newErrors: ValidationErrors = {};

    if (!formData.event_type) {
      newErrors.event_type = 'Event type is required';
    }

    if (!formData.product_name.trim()) {
      newErrors.product_name = 'Product name is required';
    }

    if (!formData.quantity || formData.quantity <= 0) {
      newErrors.quantity = 'Quantity must be greater than 0';
    }

    if (!formData.unit) {
      newErrors.unit = 'Unit is required';
    }

    if (formData.event_type === 'receiving' && !formData.vendor_name?.trim()) {
      newErrors.vendor_name = 'Vendor is required for receiving events';
    }

    if (formData.event_type === 'sale' && !formData.customer_name?.trim()) {
      newErrors.customer_name = 'Customer is required for sale events';
    }

    if (formData.temperature && (formData.temperature < -50 || formData.temperature > 200)) {
      newErrors.temperature = 'Temperature must be between -50 and 200';
    }

    if (!formData.occurred_at) {
      newErrors.occurred_at = 'Event date/time is required';
    }

    return newErrors;
  }, [formData]);

  // Handle form field changes
  const handleFieldChange = useCallback((field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  }, [errors]);

  // Check for conflicts before saving
  const checkForConflicts = useCallback(async (): Promise<boolean> => {
    if (!event.id) return false;

    try {
      // In a real implementation, you would fetch the current version of the event
      // and compare timestamps to detect concurrent modifications
      // For now, we'll simulate this check
      const currentTime = new Date().toISOString();
      const timeDiff = lastModified ? 
        new Date(currentTime).getTime() - new Date(lastModified).getTime() : 0;
      
      // If more than 5 minutes have passed, check for conflicts
      if (timeDiff > 5 * 60 * 1000) {
        // Simulate conflict detection
        const hasConflict = Math.random() < 0.1; // 10% chance of conflict for demo
        setConflictDetected(hasConflict);
        return hasConflict;
      }

      return false;
    } catch (error) {
      console.error('Error checking for conflicts:', error);
      return false;
    }
  }, [event.id, lastModified]);

  // Handle form submission
  const handleSave = useCallback(async () => {
    if (readOnly) return;

    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    // Check for conflicts
    const hasConflict = await checkForConflicts();
    if (hasConflict) {
      return; // User needs to resolve conflict
    }

    setSaving(true);
    try {
      const updatedEvent: Partial<VoiceEvent> = {
        event_type: formData.event_type,
        product_name: formData.product_name,
        quantity: formData.quantity,
        unit: formData.unit,
        vendor_name: formData.vendor_name,
        customer_name: formData.customer_name,
        condition: formData.condition,
        temperature: formData.temperature,
        temperature_unit: formData.temperature_unit,
        processing_method: formData.processing_method,
        quality_grade: formData.quality_grade,
        market_form: formData.market_form,
        notes: formData.notes,
        occurred_at: formData.occurred_at ? new Date(formData.occurred_at).toISOString() : event.occurred_at
      };

      const savedEvent = await voiceEventService.updateVoiceEvent(
        event.id!,
        updatedEvent,
        userId,
        'Manual edit via voice event editor'
      );

      await onSave(savedEvent);
    } catch (error) {
      console.error('Error saving event:', error);
      setErrors({ general: 'Failed to save event. Please try again.' });
    } finally {
      setSaving(false);
    }
  }, [formData, readOnly, validateForm, checkForConflicts, event.id, event.occurred_at, userId, onSave]);

  // Handle audio playback
  const playAudio = useCallback(async () => {
    if (event.audio_recording_url) {
      try {
        const audio = new Audio(event.audio_recording_url);
        await audio.play();
      } catch (error) {
        console.error('Error playing audio:', error);
      }
    }
  }, [event.audio_recording_url]);

  // Resolve conflict by accepting current changes
  const resolveConflict = useCallback(() => {
    setConflictDetected(false);
    setLastModified(new Date().toISOString());
  }, []);

  return (
    <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Package className="w-6 h-6 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-900">
              {readOnly ? 'View' : 'Edit'} Voice Event
            </h2>
          </div>
          <div className="flex items-center space-x-2">
            {event.audio_recording_url && (
              <button
                onClick={playAudio}
                className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
                title="Play original audio"
              >
                <Volume2 className="w-5 h-5" />
              </button>
            )}
            <button
              onClick={onCancel}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-full transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* Conflict Warning */}
        {conflictDetected && (
          <div className="mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start">
              <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5 mr-3" />
              <div className="flex-1">
                <h3 className="text-sm font-medium text-yellow-800">Conflict Detected</h3>
                <p className="mt-1 text-sm text-yellow-700">
                  This event has been modified by another user. Please review the changes and resolve conflicts.
                </p>
                <div className="mt-3 flex space-x-3">
                  <button
                    onClick={resolveConflict}
                    className="text-sm bg-yellow-100 text-yellow-800 px-3 py-1 rounded hover:bg-yellow-200"
                  >
                    Accept My Changes
                  </button>
                  <button
                    onClick={onCancel}
                    className="text-sm text-yellow-700 hover:text-yellow-800"
                  >
                    Cancel Edit
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* General Error */}
        {errors.general && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center">
              <AlertCircle className="w-5 h-5 text-red-600 mr-2" />
              <span className="text-red-800">{errors.general}</span>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Event Type *
                  </label>
                  <select
                    value={formData.event_type}
                    onChange={(e) => handleFieldChange('event_type', e.target.value)}
                    disabled={readOnly}
                    className={`w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                      readOnly ? 'bg-gray-100' : ''
                    }`}
                  >
                    <option value="receiving">Receiving</option>
                    <option value="sale">Sale</option>
                    <option value="disposal">Disposal</option>
                    <option value="physical_count">Physical Count</option>
                  </select>
                  {errors.event_type && (
                    <p className="mt-1 text-sm text-red-600">{errors.event_type}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Product Name *
                  </label>
                  <input
                    type="text"
                    value={formData.product_name}
                    onChange={(e) => handleFieldChange('product_name', e.target.value)}
                    disabled={readOnly}
                    className={`w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                      readOnly ? 'bg-gray-100' : ''
                    }`}
                  />
                  {errors.product_name && (
                    <p className="mt-1 text-sm text-red-600">{errors.product_name}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Quantity *
                  </label>
                  <input
                    type="number"
                    value={formData.quantity}
                    onChange={(e) => handleFieldChange('quantity', Number(e.target.value))}
                    disabled={readOnly}
                    min="0"
                    step="0.01"
                    className={`w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                      readOnly ? 'bg-gray-100' : ''
                    }`}
                  />
                  {errors.quantity && (
                    <p className="mt-1 text-sm text-red-600">{errors.quantity}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Unit *
                  </label>
                  <select
                    value={formData.unit}
                    onChange={(e) => handleFieldChange('unit', e.target.value)}
                    disabled={readOnly}
                    className={`w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                      readOnly ? 'bg-gray-100' : ''
                    }`}
                  >
                    <option value="lbs">Pounds (lbs)</option>
                    <option value="kg">Kilograms (kg)</option>
                    <option value="cases">Cases</option>
                    <option value="units">Units</option>
                  </select>
                  {errors.unit && (
                    <p className="mt-1 text-sm text-red-600">{errors.unit}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Vendor/Customer Information */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Vendor/Customer Information</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {(formData.event_type === 'receiving' || formData.vendor_name) && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Vendor {formData.event_type === 'receiving' ? '*' : ''}
                    </label>
                    <input
                      type="text"
                      value={formData.vendor_name || ''}
                      onChange={(e) => handleFieldChange('vendor_name', e.target.value)}
                      disabled={readOnly}
                      className={`w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                        readOnly ? 'bg-gray-100' : ''
                      }`}
                    />
                    {errors.vendor_name && (
                      <p className="mt-1 text-sm text-red-600">{errors.vendor_name}</p>
                    )}
                  </div>
                )}

                {(formData.event_type === 'sale' || formData.customer_name) && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Customer {formData.event_type === 'sale' ? '*' : ''}
                    </label>
                    <input
                      type="text"
                      value={formData.customer_name || ''}
                      onChange={(e) => handleFieldChange('customer_name', e.target.value)}
                      disabled={readOnly}
                      className={`w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                        readOnly ? 'bg-gray-100' : ''
                      }`}
                    />
                    {errors.customer_name && (
                      <p className="mt-1 text-sm text-red-600">{errors.customer_name}</p>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* Additional Details */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Additional Details</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Condition
                  </label>
                  <select
                    value={formData.condition || ''}
                    onChange={(e) => handleFieldChange('condition', e.target.value || undefined)}
                    disabled={readOnly}
                    className={`w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                      readOnly ? 'bg-gray-100' : ''
                    }`}
                  >
                    <option value="">Select condition</option>
                    <option value="Excellent">Excellent</option>
                    <option value="Good">Good</option>
                    <option value="Fair">Fair</option>
                    <option value="Poor">Poor</option>
                    <option value="Damaged">Damaged</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Event Date/Time *
                  </label>
                  <input
                    type="datetime-local"
                    value={formData.occurred_at}
                    onChange={(e) => handleFieldChange('occurred_at', e.target.value)}
                    disabled={readOnly}
                    className={`w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                      readOnly ? 'bg-gray-100' : ''
                    }`}
                  />
                  {errors.occurred_at && (
                    <p className="mt-1 text-sm text-red-600">{errors.occurred_at}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Temperature
                  </label>
                  <div className="flex space-x-2">
                    <input
                      type="number"
                      value={formData.temperature || ''}
                      onChange={(e) => handleFieldChange('temperature', e.target.value ? Number(e.target.value) : undefined)}
                      disabled={readOnly}
                      placeholder="Temperature"
                      className={`flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                        readOnly ? 'bg-gray-100' : ''
                      }`}
                    />
                    <select
                      value={formData.temperature_unit || 'fahrenheit'}
                      onChange={(e) => handleFieldChange('temperature_unit', e.target.value)}
                      disabled={readOnly}
                      className={`rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                        readOnly ? 'bg-gray-100' : ''
                      }`}
                    >
                      <option value="fahrenheit">°F</option>
                      <option value="celsius">°C</option>
                    </select>
                  </div>
                  {errors.temperature && (
                    <p className="mt-1 text-sm text-red-600">{errors.temperature}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Processing Method
                  </label>
                  <input
                    type="text"
                    value={formData.processing_method || ''}
                    onChange={(e) => handleFieldChange('processing_method', e.target.value)}
                    disabled={readOnly}
                    placeholder="e.g., Fresh, Frozen, IQF"
                    className={`w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                      readOnly ? 'bg-gray-100' : ''
                    }`}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Quality Grade
                  </label>
                  <input
                    type="text"
                    value={formData.quality_grade || ''}
                    onChange={(e) => handleFieldChange('quality_grade', e.target.value)}
                    disabled={readOnly}
                    placeholder="e.g., Premium, Grade A"
                    className={`w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                      readOnly ? 'bg-gray-100' : ''
                    }`}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Market Form
                  </label>
                  <input
                    type="text"
                    value={formData.market_form || ''}
                    onChange={(e) => handleFieldChange('market_form', e.target.value)}
                    disabled={readOnly}
                    placeholder="e.g., Whole, Fillets, H&G"
                    className={`w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                      readOnly ? 'bg-gray-100' : ''
                    }`}
                  />
                </div>
              </div>

              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Notes
                </label>
                <textarea
                  value={formData.notes || ''}
                  onChange={(e) => handleFieldChange('notes', e.target.value)}
                  disabled={readOnly}
                  rows={3}
                  className={`w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                    readOnly ? 'bg-gray-100' : ''
                  }`}
                  placeholder="Additional notes or comments..."
                />
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Confidence Display */}
            <ConfidenceDisplay 
              score={event.voice_confidence_score} 
              breakdown={event.voice_confidence_breakdown}
            />

            {/* Original Transcript */}
            {event.raw_transcript && (
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <FileText className="w-4 h-4 mr-2" />
                  Original Transcript
                </h4>
                <div className="text-sm text-gray-600 italic bg-white p-3 rounded border">
                  "{event.raw_transcript}"
                </div>
              </div>
            )}

            {/* Audit Trail */}
            {auditTrail.length > 0 && (
              <div className="bg-gray-50 p-4 rounded-lg">
                <button
                  onClick={() => setShowAuditTrail(!showAuditTrail)}
                  className="flex items-center justify-between w-full text-sm font-medium text-gray-700 hover:text-gray-900"
                >
                  <div className="flex items-center">
                    <Clock className="w-4 h-4 mr-2" />
                    Change History ({auditTrail.length})
                  </div>
                  <span className="text-xs">
                    {showAuditTrail ? 'Hide' : 'Show'}
                  </span>
                </button>
                
                {showAuditTrail && (
                  <div className="mt-3 space-y-2 max-h-48 overflow-y-auto">
                    {auditTrail.map((audit, index) => (
                      <div key={audit.id || index} className="text-xs bg-white p-2 rounded border">
                        <div className="font-medium text-gray-700">
                          {audit.field_name} changed
                        </div>
                        <div className="text-gray-500 mt-1">
                          {audit.changed_at ? new Date(audit.changed_at).toLocaleString() : 'Unknown time'}
                        </div>
                        {audit.change_reason && (
                          <div className="text-gray-600 mt-1">
                            Reason: {audit.change_reason}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Metadata */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-gray-700 mb-3">Event Metadata</h4>
              <div className="space-y-2 text-sm text-gray-600">
                <div className="flex justify-between">
                  <span>Created:</span>
                  <span>{event.created_at ? new Date(event.created_at).toLocaleString() : 'Unknown'}</span>
                </div>
                {event.updated_at && event.updated_at !== event.created_at && (
                  <div className="flex justify-between">
                    <span>Modified:</span>
                    <span>{new Date(event.updated_at).toLocaleString()}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span>Source:</span>
                  <span className="flex items-center">
                    <Volume2 className="w-3 h-3 mr-1" />
                    Voice
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        {!readOnly && (
          <div className="mt-8 flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              onClick={onCancel}
              disabled={saving}
              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              disabled={saving || conflictDetected}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Save Changes
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default VoiceEventEditor;