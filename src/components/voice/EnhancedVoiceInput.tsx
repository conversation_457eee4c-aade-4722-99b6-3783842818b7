import React, { useEffect, useState, useRef, useCallback } from 'react';
import SpeechRecognition, { useSpeechRecognition } from 'react-speech-recognition';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ader2, <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, Volume2, RotateCcw } from 'lucide-react';
import { format } from 'date-fns';
import { processVoiceInput } from '../../lib/ai';
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Alert } from '../ui/alert';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../ui/select';
import { getProductCategories } from '../../lib/setupDatabase';

export interface TranscriptionData {
  product?: string;
  quantity?: number;
  category?: string;
  vendor?: string;
  unit?: string;
  price?: number;
  confidence?: number;
  timestamp: string;
  // Enhanced seafood-specific fields
  processing_method?: string; // Fresh, Frozen, IQF, etc.
  quality_grade?: string; // Premium, Grade A, etc.
  market_form?: string; // Whole, Fillets, H&G, etc.
  occurred_at?: string; // ISO timestamp for when event occurred
  metadata?: {
    processing_method: string;
    voice_correction_applied?: boolean;
    seafood_database_match?: boolean;
    confidence_breakdown?: {
      product_match: number;
      quantity_extraction: number;
      vendor_match: number;
      overall: number;
    };
  };
}

interface VoiceInputProps {
  onTranscriptionComplete: (data: TranscriptionData) => void;
  onError: (error: string) => void;
  placeholder?: string;
  className?: string;
}

interface VoiceState {
  status: 'idle' | 'listening' | 'processing' | 'complete' | 'error';
  confidence: number;
  processingTime: number;
  retryCount: number;
}

export default function EnhancedVoiceInput({ 
  onTranscriptionComplete, 
  onError, 
  placeholder = "Click microphone and speak your inventory item...",
  className = ""
}: VoiceInputProps) {
  const [state, setState] = useState<VoiceState>({
    status: 'idle',
    confidence: 0,
    processingTime: 0,
    retryCount: 0
  });
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [categories, setCategories] = useState<Array<{value: string, label: string}>>([]);
  const [lastResult, setLastResult] = useState<TranscriptionData | null>(null);
  const [processingStartTime, setProcessingStartTime] = useState<number | null>(null);
  const [audioLevel, setAudioLevel] = useState<number>(0);
  
  const processingTimeoutRef = useRef<NodeJS.Timeout>();
  const abortControllerRef = useRef<AbortController>();
  const animationFrameRef = useRef<number>();

  const {
    transcript,
    listening,
    resetTranscript,
    browserSupportsSpeechRecognition
  } = useSpeechRecognition();

  // Load categories on mount
  useEffect(() => {
    const loadCategories = async () => {
      try {
        const categoriesData = await getProductCategories();
        const categoryOptions = categoriesData.map(cat => ({
          value: cat.name,
          label: cat.name
        }));
        setCategories(categoryOptions);
      } catch (error) {
        console.error('Failed to load categories:', error);
      }
    };
    loadCategories();
  }, []);

  // Simulate audio level animation when listening
  useEffect(() => {
    if (listening) {
      const animateAudioLevel = () => {
        setAudioLevel(Math.random() * 100);
        animationFrameRef.current = requestAnimationFrame(animateAudioLevel);
      };
      animateAudioLevel();
    } else {
      setAudioLevel(0);
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [listening]);

  // Update processing time
  useEffect(() => {
    if (state.status === 'processing' && processingStartTime) {
      const interval = setInterval(() => {
        setState(prev => ({
          ...prev,
          processingTime: Math.round((Date.now() - processingStartTime) / 100) / 10
        }));
      }, 100);
      return () => clearInterval(interval);
    }
  }, [state.status, processingStartTime]);

  const startListening = useCallback(() => {
    if (!browserSupportsSpeechRecognition) {
      onError('Speech recognition is not supported in your browser');
      return;
    }

    setState(prev => ({ ...prev, status: 'listening', retryCount: 0 }));
    resetTranscript();
    SpeechRecognition.startListening({ continuous: false, language: 'en-US' });
  }, [browserSupportsSpeechRecognition, onError, resetTranscript]);

  const stopListening = useCallback(() => {
    SpeechRecognition.stopListening();
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    setState(prev => ({ ...prev, status: 'idle' }));
  }, []);

  const processTranscript = useCallback(async (transcript: string) => {
    if (!transcript.trim()) return;

    setState(prev => ({ ...prev, status: 'processing' }));
    setProcessingStartTime(Date.now());

    try {
      abortControllerRef.current = new AbortController();
      
      // Set processing timeout
      processingTimeoutRef.current = setTimeout(() => {
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
        }
      }, 10000); // 10 second timeout

      const result = await processVoiceInput(transcript, {
        model: 'gpt-3.5-turbo',
        timeout: 8000,
        retryAttempts: 2
      });

      // Clear timeout
      if (processingTimeoutRef.current) {
        clearTimeout(processingTimeoutRef.current);
      }

      const transcriptionData: TranscriptionData = {
        ...result,
        category: selectedCategory || result.category,
        timestamp: new Date().toISOString(),
        confidence: result.metadata?.confidence_score || 0.7,
        processing_method: result.processing_method,
        quality_grade: result.quality_grade,
        market_form: result.market_form,
        occurred_at: result.occurred_at
      };

      setLastResult(transcriptionData);
      setState(prev => ({ 
        ...prev, 
        status: 'complete',
        confidence: transcriptionData.confidence || 0.7
      }));

      onTranscriptionComplete(transcriptionData);

    } catch (error) {
      console.error('Voice processing error:', error);
      setState(prev => ({ 
        ...prev, 
        status: 'error',
        retryCount: prev.retryCount + 1
      }));
      
      const errorMessage = error instanceof Error ? error.message : 'Failed to process voice input';
      onError(errorMessage);
    } finally {
      setProcessingStartTime(null);
    }
  }, [selectedCategory, onTranscriptionComplete, onError]);

  // Auto-process when transcript changes and listening stops
  useEffect(() => {
    if (!listening && transcript && state.status === 'listening') {
      processTranscript(transcript);
    }
  }, [listening, transcript, state.status, processTranscript]);

  const retryProcessing = useCallback(() => {
    if (transcript) {
      processTranscript(transcript);
    } else {
      startListening();
    }
  }, [transcript, processTranscript, startListening]);

  const getStatusColor = () => {
    switch (state.status) {
      case 'listening': return 'border-blue-500 bg-blue-50';
      case 'processing': return 'border-yellow-500 bg-yellow-50';
      case 'complete': return 'border-green-500 bg-green-50';
      case 'error': return 'border-red-500 bg-red-50';
      default: return 'border-gray-200 bg-white';
    }
  };

  const getStatusMessage = () => {
    switch (state.status) {
      case 'listening': return 'Listening... Speak clearly about your seafood inventory';
      case 'processing': return `Processing voice input... (${state.processingTime}s)`;
      case 'complete': return `Voice input processed successfully (${state.confidence * 100}% confidence)`;
      case 'error': return 'Error processing voice input. Please try again.';
      default: return placeholder;
    }
  };

  if (!browserSupportsSpeechRecognition) {
    return (
      <Alert className="border-yellow-200 bg-yellow-50">
        <AlertTriangle className="h-4 w-4" />
        <div>
          <p className="font-medium">Speech Recognition Not Supported</p>
          <p className="text-sm">Your browser doesn't support speech recognition. Please use Chrome, Edge, or Safari.</p>
        </div>
      </Alert>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Main Voice Input Card */}
      <Card className={`p-6 transition-all duration-200 ${getStatusColor()}`}>
        <div className="flex items-center space-x-4">
          {/* Microphone Button */}
          <div className="relative">
            <Button
              onClick={state.status === 'idle' ? startListening : stopListening}
              disabled={state.status === 'processing'}
              className={`w-16 h-16 rounded-full relative overflow-hidden ${
                listening 
                  ? 'bg-red-500 hover:bg-red-600' 
                  : state.status === 'processing'
                  ? 'bg-yellow-500'
                  : 'bg-blue-500 hover:bg-blue-600'
              }`}
            >
              {state.status === 'processing' ? (
                <Loader2 className="w-6 h-6 animate-spin text-white" />
              ) : listening ? (
                <MicOff className="w-6 h-6 text-white" />
              ) : (
                <Mic className="w-6 h-6 text-white" />
              )}
            </Button>
            
            {/* Audio Level Indicator */}
            {listening && (
              <div className="absolute inset-0 rounded-full border-4 border-red-300 animate-pulse" 
                   style={{ 
                     transform: `scale(${1 + audioLevel / 200})`,
                     opacity: 0.6 
                   }} />
            )}
          </div>

          {/* Status and Controls */}
          <div className="flex-1 space-y-2">
            <div className="flex items-center justify-between">
              <p className="text-sm font-medium text-gray-700">
                {getStatusMessage()}
              </p>
              {state.status === 'error' && state.retryCount < 3 && (
                <Button variant="outline" size="sm" onClick={retryProcessing}>
                  <RotateCcw className="w-4 h-4 mr-1" />
                  Retry
                </Button>
              )}
            </div>

            {/* Progress Bar */}
            {state.status === 'processing' && (
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-yellow-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${Math.min((state.processingTime / 10) * 100, 100)}%` }}
                />
              </div>
            )}

            {/* Confidence Indicator */}
            {state.status === 'complete' && (
              <div className="flex items-center space-x-2 text-sm text-green-600">
                <CheckCircle className="w-4 h-4" />
                <span>Confidence: {Math.round(state.confidence * 100)}%</span>
              </div>
            )}
          </div>
        </div>

        {/* Transcript Display */}
        {transcript && (
          <div className="mt-4 p-3 bg-gray-50 rounded-lg border">
            <p className="text-sm text-gray-600 mb-1">Transcript:</p>
            <p className="font-medium">{transcript}</p>
          </div>
        )}

        {/* Category Override */}
        <div className="mt-4 flex items-center space-x-4">
          <label className="text-sm font-medium text-gray-700 whitespace-nowrap">
            Category Override:
          </label>
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="flex-1">
              <SelectValue placeholder="Auto-detect category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Auto-detect</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category.value} value={category.value}>
                  {category.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </Card>

      {/* Last Result Preview */}
      {lastResult && state.status === 'complete' && (
        <Card className="p-4 bg-green-50 border-green-200">
          <div className="flex items-start justify-between">
            <div>
              <p className="font-medium text-green-800">Last Processing Result:</p>
              <div className="mt-2 space-y-1 text-sm text-green-700">
                {lastResult.product && <p><span className="font-medium">Product:</span> {lastResult.product}</p>}
                {lastResult.quantity && <p><span className="font-medium">Quantity:</span> {lastResult.quantity} {lastResult.unit || 'units'}</p>}
                {lastResult.category && <p><span className="font-medium">Category:</span> {lastResult.category}</p>}
                {lastResult.vendor && <p><span className="font-medium">Vendor:</span> {lastResult.vendor}</p>}
                {lastResult.price && <p><span className="font-medium">Price:</span> ${lastResult.price}</p>}
                {lastResult.processing_method && <p><span className="font-medium">Processing:</span> {lastResult.processing_method}</p>}
                {lastResult.quality_grade && <p><span className="font-medium">Quality:</span> {lastResult.quality_grade}</p>}
                {lastResult.market_form && <p><span className="font-medium">Form:</span> {lastResult.market_form}</p>}
              </div>
              {lastResult.metadata && (
                <div className="mt-2 text-xs text-green-600">
                  <p>Method: {lastResult.metadata.processing_method}</p>
                  {lastResult.metadata.voice_correction_applied && <p>Voice corrections applied ✓</p>}
                  {lastResult.metadata.seafood_database_match && <p>Seafood database match ✓</p>}
                </div>
              )}
            </div>
            <Volume2 className="w-5 h-5 text-green-600" />
          </div>
        </Card>
      )}

      {/* Voice Input Tips */}
      <Card className="p-4 bg-blue-50 border-blue-200">
        <p className="text-sm font-medium text-blue-800 mb-2">💡 Voice Input Tips:</p>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• Speak clearly: "Received 50 pounds fresh coho salmon fillets from Pacific Seafoods"</li>
          <li>• Include quantities, species, processing method, and supplier</li>
          <li>• Processing: fresh, frozen, IQF, H&G (head and gutted), fillets, whole</li>
          <li>• Species examples: coho salmon, dungeness crab, pacific halibut, sea scallops</li>
          <li>• Quality grades: premium, grade A, sashimi grade, restaurant quality</li>
          <li>• Works best in quiet environments with clear pronunciation</li>
        </ul>
      </Card>
    </div>
  );
}