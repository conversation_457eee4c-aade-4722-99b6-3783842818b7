import React, { useState } from 'react';
import { Mic, X } from 'lucide-react';
import EnhancedVoiceAssistant from './EnhancedVoiceAssistant';

interface FloatingVoiceButtonProps {
  onEventCreated?: (eventType: string) => void;
}

export default function FloatingVoiceButton({ onEventCreated }: FloatingVoiceButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  
  // Enhanced cleanup and state management
  React.useEffect(() => {
    // Force cleanup on mount to prevent stuck states
    setIsOpen(false);
    
    return () => {
      // Ensure cleanup on unmount
      setIsOpen(false);
    };
  }, []);
  
  // Global error handling to prevent stuck overlays
  React.useEffect(() => {
    const handleError = () => {
      console.warn('Error detected, closing voice assistant overlay');
      setIsOpen(false);
    };
    
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        setIsOpen(false);
      }
    };

    // Add escape key handler
    document.addEventListener('keydown', handleKeyDown);
    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleError);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleError);
    };
  }, [isOpen]);

  // Force close on navigation or route changes
  React.useEffect(() => {
    const handleRouteChange = () => {
      setIsOpen(false);
    };
    
    // Listen for potential navigation events
    window.addEventListener('beforeunload', handleRouteChange);
    window.addEventListener('popstate', handleRouteChange);
    
    return () => {
      window.removeEventListener('beforeunload', handleRouteChange);
      window.removeEventListener('popstate', handleRouteChange);
    };
  }, []);

  const handleToggle = () => {
    setIsOpen(prev => !prev);
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsOpen(false);
  };

  return (
    <>
      {/* Floating Button - Increased z-index to ensure it's always clickable */}
      <button
        onClick={handleToggle}
        className={`fixed bottom-6 right-6 z-[60] p-4 rounded-full shadow-lg transition-all ${
          isOpen 
            ? 'bg-red-500 hover:bg-red-600' 
            : 'bg-blue-500 hover:bg-blue-600'
        } text-white`}
        title={isOpen ? "Close Voice Assistant" : "Open Voice Assistant"}
        aria-label={isOpen ? "Close Voice Assistant" : "Open Voice Assistant"}
      >
        {isOpen ? <X className="w-6 h-6" /> : <Mic className="w-6 h-6" />}
      </button>

      {/* Enhanced Voice Assistant Panel - Fixed positioning and constraints */}
      {isOpen && (
        <div className="fixed bottom-24 right-6 z-[50] w-96 max-w-[calc(100vw-3rem)] max-h-[calc(100vh-8rem)] overflow-hidden">
          <EnhancedVoiceAssistant
            onEventCreated={(eventType) => {
              if (onEventCreated) {
                onEventCreated(eventType);
              }
              // Auto-close after successful event creation with delay
              setTimeout(() => setIsOpen(false), 2000);
            }}
            className="shadow-xl border-2 border-gray-200"
          />
        </div>
      )}

      {/* Improved Backdrop - Better positioning and cleanup */}
      {isOpen && (
        <div
          className="fixed inset-0 z-[40] bg-black bg-opacity-20 backdrop-blur-sm"
          onClick={handleBackdropClick}
          onKeyDown={(e) => {
            if (e.key === 'Escape') {
              setIsOpen(false);
            }
          }}
          role="button"
          tabIndex={0}
          aria-label="Close voice assistant overlay"
          style={{
            // Ensure the backdrop doesn't interfere with sidebar
            left: 'var(--sidebar-width, 0px)'
          }}
        />
      )}
    </>
  );
}