import React, { useState, useRef, useCallback } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>2, <PERSON>ert<PERSON>ircle, CheckCircle } from 'lucide-react';
import VoiceProcessor, { VoiceCommand } from '../../lib/voice-processor';
import { supabase } from '../../lib/supabase';

interface EnhancedVoiceAssistantProps {
  onEventCreated?: (eventType: string) => void;
  className?: string;
}

export default function EnhancedVoiceAssistant({ 
  onEventCreated, 
  className = '' 
}: EnhancedVoiceAssistantProps) {
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [feedback, setFeedback] = useState('');
  const [lastCommand, setLastCommand] = useState<VoiceCommand | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  const voiceProcessor = useRef<VoiceProcessor | null>(null);
  const mediaRecorder = useRef<MediaRecorder | null>(null);
  const audioChunks = useRef<Blob[]>([]);

  // Initialize voice processor
  React.useEffect(() => {
    try {
      voiceProcessor.current = new VoiceProcessor();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to initialize voice processor');
    }
  }, []);

  const startRecording = useCallback(async () => {
    if (!voiceProcessor.current || isRecording || isProcessing) {
      return;
    }

    setError(null);
    setFeedback('');
    setLastCommand(null);
    audioChunks.current = [];

    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaRecorder.current = new MediaRecorder(stream);
      
      mediaRecorder.current.ondataavailable = (event) => {
        audioChunks.current.push(event.data);
      };

      mediaRecorder.current.onstop = async () => {
        stream.getTracks().forEach(track => track.stop());
        await processRecording();
      };

      mediaRecorder.current.start();
      setIsRecording(true);
      setFeedback('🎤 Listening... (speak your inventory command)');
      
      // Auto-stop after 10 seconds
      setTimeout(() => {
        if (mediaRecorder.current && mediaRecorder.current.state === 'recording') {
          mediaRecorder.current.stop();
        }
      }, 10000);

    } catch (err) {
      setError('Failed to access microphone. Please check permissions.');
      console.error('Microphone access error:', err);
    }
  }, [isRecording, isProcessing]);

  const stopRecording = useCallback(() => {
    if (mediaRecorder.current && mediaRecorder.current.state === 'recording') {
      mediaRecorder.current.stop();
      setIsRecording(false);
    }
  }, []);

  const processRecording = async () => {
    if (!voiceProcessor.current || audioChunks.current.length === 0) {
      return;
    }

    setIsProcessing(true);
    setFeedback('🔄 Processing with OpenAI Whisper + GPT-4...');

    try {
      const audioBlob = new Blob(audioChunks.current, { type: 'audio/webm' });
      const command = await voiceProcessor.current.processAudioBlob(audioBlob);
      
      setLastCommand(command);
      console.log('Processed command:', command);

      if (command.confidence_score < 0.5) {
        setFeedback(`⚠️ Low confidence (${Math.round(command.confidence_score * 100)}%). Please try again with clearer speech.`);
        return;
      }

      if (command.action_type === 'create_event') {
        await createInventoryEvent(command);
      } else {
        setFeedback(`✅ Command processed: ${command.raw_transcript}`);
      }

    } catch (err) {
      console.error('Voice processing error:', err);
      setError(err instanceof Error ? err.message : 'Failed to process voice command');
      setFeedback('❌ Processing failed. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const createInventoryEvent = async (command: VoiceCommand) => {
    if (!command.event_type || !command.product_name || !command.quantity || !command.unit) {
      setFeedback('❌ Missing required information. Please specify event type, product, quantity, and unit.');
      return;
    }

    try {
      setFeedback('💾 Saving to database...');

      // Find matching product
      const { data: products } = await supabase
        .from('Products')
        .select('id, name')
        .ilike('name', `%${command.product_name}%`)
        .limit(1);

      if (!products || products.length === 0) {
        setFeedback(`❌ Product "${command.product_name}" not found. Please add it to inventory first.`);
        return;
      }

      const productId = products[0].id;

      // Find matching vendor for receiving events
      let vendorId = null;
      if (command.event_type === 'receiving' && command.vendor_name) {
        const { data: vendors } = await supabase
          .from('vendors')
          .select('id, name')
          .ilike('name', `%${command.vendor_name}%`)
          .limit(1);

        if (vendors && vendors.length > 0) {
          vendorId = vendors[0].id;
        }
      }

      // Create inventory event
      const eventData: any = {
        event_type: command.event_type,
        product_id: productId,
        quantity: command.quantity,
        unit_price: null,
        total_amount: null,
        notes: command.notes || `Voice command: ${command.raw_transcript}`,
        metadata: {
          source: 'enhanced-voice-assistant',
          unit: command.unit,
          confidence_score: command.confidence_score,
          vendor_name: command.vendor_name,
          customer_name: command.customer_name,
          condition: command.condition,
          temperature: command.temperature,
          temperature_unit: command.temperature_unit,
          parsed_date: command.event_date,
        },
        created_at: command.event_date ? `${command.event_date}T12:00:00Z` : new Date().toISOString(),
      };

      if (vendorId) {
        eventData.metadata.vendor_id = vendorId;
      }

      const { error } = await supabase
        .from('inventory_events')
        .insert(eventData);

      if (error) throw error;

      setFeedback(`✅ ${command.event_type} event saved! ${command.quantity} ${command.unit} ${command.product_name}${command.vendor_name ? ` from ${command.vendor_name}` : ''}`);
      
      if (onEventCreated) {
        onEventCreated(command.event_type);
      }

    } catch (err) {
      console.error('Database error:', err);
      setFeedback('❌ Failed to save event. Please try again or use manual entry.');
    }
  };

  const getStatusIcon = () => {
    if (isRecording) return <Mic className="w-5 h-5 animate-pulse" />;
    if (isProcessing) return <Loader2 className="w-5 h-5 animate-spin" />;
    if (error) return <AlertCircle className="w-5 h-5" />;
    if (lastCommand && lastCommand.confidence_score > 0.5) return <CheckCircle className="w-5 h-5" />;
    return <MicOff className="w-5 h-5" />;
  };

  const getStatusColor = () => {
    if (isRecording) return 'bg-red-500 hover:bg-red-600';
    if (isProcessing) return 'bg-yellow-500 hover:bg-yellow-600';
    if (error) return 'bg-red-500 hover:bg-red-600';
    if (lastCommand && lastCommand.confidence_score > 0.5) return 'bg-green-500 hover:bg-green-600';
    return 'bg-blue-500 hover:bg-blue-600';
  };

  if (error && error.includes('OpenAI API key')) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
        <div className="flex items-center gap-2 text-red-800">
          <AlertCircle className="w-5 h-5" />
          <div className="text-sm font-medium">Voice Assistant Unavailable</div>
        </div>
        <div className="text-sm text-red-700 mt-1">
          OpenAI API key not found. Add VITE_OPENAI_API_KEY to your .env file.
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-lg max-w-full max-h-full overflow-hidden ${className}`}>
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">AI Voice Assistant</h3>
            <p className="text-sm text-gray-600">Powered by OpenAI Whisper + GPT-4</p>
          </div>
          
          <button
            onClick={isRecording ? stopRecording : startRecording}
            disabled={isProcessing}
            className={`p-3 rounded-full transition-all ${getStatusColor()} text-white disabled:opacity-50`}
            title={isRecording ? 'Stop recording' : 'Start recording'}
          >
            {getStatusIcon()}
          </button>
        </div>

        {/* Feedback Display */}
        {feedback && (
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="text-sm text-blue-800">{feedback}</div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <div className="text-sm text-red-800">{error}</div>
          </div>
        )}

        {/* Last Command Display */}
        {lastCommand && (
          <div className="mb-4 p-3 bg-gray-50 border border-gray-200 rounded-lg">
            <div className="text-sm font-medium text-gray-900 mb-2">Last Command:</div>
            <div className="text-sm text-gray-700 mb-1">
              <strong>Said:</strong> "{lastCommand.raw_transcript}"
            </div>
            <div className="text-sm text-gray-700">
              <strong>Parsed:</strong> {lastCommand.event_type} {lastCommand.quantity} {lastCommand.unit} {lastCommand.product_name}
              {lastCommand.vendor_name && ` from ${lastCommand.vendor_name}`}
              <span className="ml-2 text-blue-600">
                ({Math.round(lastCommand.confidence_score * 100)}% confidence)
              </span>
            </div>
          </div>
        )}

        {/* Usage Examples */}
        <div className="text-xs text-gray-500">
          <div className="font-medium mb-1">Try saying:</div>
          <ul className="space-y-1">
            <li>• "Receive 10 lb of coho salmon from 49th state Seafoods yesterday"</li>
            <li>• "Dispose 5 pounds expired cod, condition poor"</li>
            <li>• "Physical count 50 pounds halibut in freezer"</li>
            <li>• "Sale 20 pounds dungeness crab to Restaurant ABC"</li>
          </ul>
        </div>
      </div>
    </div>
  );
}