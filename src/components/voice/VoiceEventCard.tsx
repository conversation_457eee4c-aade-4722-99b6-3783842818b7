import React from 'react';
import { 
  Calendar, 
  Clock, 
  User, 
  Package, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle, 
  Volume2,
  Edit,
  Trash2
} from 'lucide-react';
import { VoiceEvent } from '../../types/schema';

interface ConfidenceBadgeProps {
  score: number;
  breakdown?: {
    product_match: number;
    quantity_extraction: number;
    vendor_match: number;
    overall: number;
  };
}

const ConfidenceBadge: React.FC<ConfidenceBadgeProps> = ({ score }) => {
  const getConfidenceLevel = (score: number) => {
    if (score >= 0.9) return { level: 'high', color: 'bg-green-100 text-green-800', icon: CheckCircle };
    if (score >= 0.7) return { level: 'medium', color: 'bg-yellow-100 text-yellow-800', icon: TrendingUp };
    return { level: 'low', color: 'bg-red-100 text-red-800', icon: AlertTriangle };
  };

  const { level, color, icon: Icon } = getConfidenceLevel(score);

  return (
    <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${color}`}>
      <Icon className="w-3 h-3 mr-1" />
      {Math.round(score * 100)}% {level}
    </div>
  );
};

interface EventTypeIconProps {
  eventType: string;
}

const EventTypeIcon: React.FC<EventTypeIconProps> = ({ eventType }) => {
  const getEventIcon = (type: string) => {
    switch (type) {
      case 'receiving': return { icon: Package, color: 'text-blue-600' };
      case 'sale': return { icon: TrendingUp, color: 'text-green-600' };
      case 'disposal': return { icon: Trash2, color: 'text-red-600' };
      case 'physical_count': return { icon: CheckCircle, color: 'text-purple-600' };
      default: return { icon: Package, color: 'text-gray-600' };
    }
  };

  const { icon: Icon, color } = getEventIcon(eventType);
  return <Icon className={`w-5 h-5 ${color}`} />;
};

interface VoiceEventCardProps {
  event: VoiceEvent;
  onEdit?: (event: VoiceEvent) => void;
  onDelete?: (eventId: string) => void;
  onPlayAudio?: (audioUrl: string) => void;
}

export const VoiceEventCard: React.FC<VoiceEventCardProps> = ({
  event,
  onEdit,
  onDelete,
  onPlayAudio
}) => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return `${date.toLocaleDateString()} ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-4 flex-1">
          {/* Event Type Icon */}
          <div className="flex-shrink-0">
            <EventTypeIcon eventType={event.event_type} />
          </div>

          {/* Event Details */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-3 mb-2">
              <h3 className="text-lg font-semibold text-gray-900 capitalize">
                {event.event_type.replace('_', ' ')}
              </h3>
              <ConfidenceBadge 
                score={event.voice_confidence_score} 
                breakdown={event.voice_confidence_breakdown}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm text-gray-600">
              <div className="flex items-center">
                <Package className="w-4 h-4 mr-2" />
                <span className="font-medium">{event.product_name}</span>
              </div>
              
              {event.quantity && (
                <div className="flex items-center">
                  <span className="font-medium">
                    {event.quantity} {event.unit}
                  </span>
                </div>
              )}

              {event.vendor_name && (
                <div className="flex items-center">
                  <User className="w-4 h-4 mr-2" />
                  <span>From: {event.vendor_name}</span>
                </div>
              )}

              {event.customer_name && (
                <div className="flex items-center">
                  <User className="w-4 h-4 mr-2" />
                  <span>To: {event.customer_name}</span>
                </div>
              )}

              <div className="flex items-center">
                <Calendar className="w-4 h-4 mr-2" />
                <span>{formatDate(event.occurred_at)}</span>
              </div>

              <div className="flex items-center">
                <Clock className="w-4 h-4 mr-2" />
                <span>Created: {formatDate(event.created_at || '')}</span>
              </div>
            </div>

            {/* Additional Details */}
            {(event.condition || event.temperature || event.notes) && (
              <div className="mt-3 pt-3 border-t border-gray-100">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                  {event.condition && (
                    <div>
                      <span className="font-medium">Condition:</span> {event.condition}
                    </div>
                  )}
                  {event.temperature && (
                    <div>
                      <span className="font-medium">Temperature:</span> {event.temperature}°{event.temperature_unit || 'F'}
                    </div>
                  )}
                  {event.notes && (
                    <div className="md:col-span-3">
                      <span className="font-medium">Notes:</span> {event.notes}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Raw Transcript */}
            {event.raw_transcript && (
              <div className="mt-3 pt-3 border-t border-gray-100">
                <details className="text-sm">
                  <summary className="cursor-pointer text-gray-500 hover:text-gray-700">
                    View original transcript
                  </summary>
                  <div className="mt-2 p-2 bg-gray-50 rounded text-gray-700 italic">
                    "{event.raw_transcript}"
                  </div>
                </details>
              </div>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-2 ml-4">
          {event.audio_recording_url && onPlayAudio && (
            <button
              onClick={() => onPlayAudio(event.audio_recording_url!)}
              className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
              title="Play audio recording"
            >
              <Volume2 className="w-4 h-4" />
            </button>
          )}
          
          {onEdit && (
            <button
              onClick={() => onEdit(event)}
              className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
              title="Edit event"
            >
              <Edit className="w-4 h-4" />
            </button>
          )}
          
          {onDelete && (
            <button
              onClick={() => onDelete(event.id!)}
              className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-full transition-colors"
              title="Delete event"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default VoiceEventCard;