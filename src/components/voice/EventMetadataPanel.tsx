import React from 'react';
import { Volume2, FileText, TrendingUp } from 'lucide-react';
import { VoiceEvent } from '../../types/schema';

interface ConfidenceDisplayProps {
  score: number;
  breakdown?: {
    product_match: number;
    quantity_extraction: number;
    vendor_match: number;
    overall: number;
  };
}

const ConfidenceDisplay: React.FC<ConfidenceDisplayProps> = ({ score, breakdown }) => {
  const getConfidenceColor = (score: number) => {
    if (score >= 0.9) return 'text-green-600 bg-green-50';
    if (score >= 0.7) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  const formatScore = (score: number) => Math.round(score * 100);

  return (
    <div className="bg-gray-50 p-4 rounded-lg">
      <h4 className="text-sm font-medium text-gray-700 mb-3">Voice Processing Confidence</h4>
      
      <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getConfidenceColor(score)}`}>
        <TrendingUp className="w-4 h-4 mr-1" />
        Overall: {formatScore(score)}%
      </div>

      {breakdown && (
        <div className="mt-3 grid grid-cols-2 gap-3 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">Product Match:</span>
            <span className={`font-medium ${getConfidenceColor(breakdown.product_match)}`}>
              {formatScore(breakdown.product_match)}%
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Quantity:</span>
            <span className={`font-medium ${getConfidenceColor(breakdown.quantity_extraction)}`}>
              {formatScore(breakdown.quantity_extraction)}%
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Vendor Match:</span>
            <span className={`font-medium ${getConfidenceColor(breakdown.vendor_match)}`}>
              {formatScore(breakdown.vendor_match)}%
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

interface EventMetadataPanelProps {
  event: VoiceEvent;
  className?: string;
}

export const EventMetadataPanel: React.FC<EventMetadataPanelProps> = ({
  event,
  className = ''
}) => {
  return (
    <div className={`space-y-6 ${className}`}>
      {/* Confidence Display */}
      <ConfidenceDisplay 
        score={event.voice_confidence_score} 
        breakdown={event.voice_confidence_breakdown}
      />

      {/* Original Transcript */}
      {event.raw_transcript && (
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
            <FileText className="w-4 h-4 mr-2" />
            Original Transcript
          </h4>
          <div className="text-sm text-gray-600 italic bg-white p-3 rounded border">
            "{event.raw_transcript}"
          </div>
        </div>
      )}

      {/* Metadata */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-gray-700 mb-3">Event Metadata</h4>
        <div className="space-y-2 text-sm text-gray-600">
          <div className="flex justify-between">
            <span>Created:</span>
            <span>{event.created_at ? new Date(event.created_at).toLocaleString() : 'Unknown'}</span>
          </div>
          {event.updated_at && event.updated_at !== event.created_at && (
            <div className="flex justify-between">
              <span>Modified:</span>
              <span>{new Date(event.updated_at).toLocaleString()}</span>
            </div>
          )}
          <div className="flex justify-between">
            <span>Source:</span>
            <span className="flex items-center">
              <Volume2 className="w-3 h-3 mr-1" />
              Voice
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EventMetadataPanel;