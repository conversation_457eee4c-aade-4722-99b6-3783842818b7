import { useCallback, useMemo, useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';

export type BatchPattern = 'YYYYMMDD-PROD-SEQ2' | 'YYDOY-PROD-SEQ2' | 'YYYYMMDD-VEND-PROD-SEQ2';

interface BatchNumberGeneratorProps {
  date?: string; // YYYY-MM-DD
  productId?: string;
  vendorId?: string;
  productNameById?: Record<string, string>;
  vendorNameById?: Record<string, string>;
  pattern?: BatchPattern;
  onGenerate: (value: string) => void;
  disabled?: boolean;
}

// Minimal row type for selecting only `metadata` from `inventory_events`
type MetaRow = { metadata?: Record<string, unknown> | null };
type LotRow = { tlc?: string | null };

function pad(num: number, size: number) {
  let s = String(num);
  while (s.length < size) s = `0${  s}`;
  return s;
}

function toYYYYMMDD(dateStr?: string) {
  if (!dateStr) return '';
  return dateStr.replace(/-/g, '');
}

function toYYDOY(dateStr?: string) {
  if (!dateStr) return '';
  const d = new Date(`${dateStr  }T12:00:00`);
  const year = String(d.getFullYear()).slice(-2);
  const start = new Date(d.getFullYear(), 0, 0);
  const diff = d.getTime() - start.getTime();
  const oneDay = 1000 * 60 * 60 * 24;
  const doy = Math.floor(diff / oneDay);
  return `${year}${pad(doy, 3)}`;
}

function codeFromName(name?: string, len = 4) {
  if (!name) return '';
  const alnum = name.replace(/[^a-zA-Z0-9]/g, '');
  return alnum.toUpperCase().slice(0, len);
}

export default function BatchNumberGenerator({
  date,
  productId,
  vendorId,
  productNameById,
  vendorNameById,
  pattern = 'YYYYMMDD-PROD-SEQ2',
  onGenerate,
  disabled,
}: BatchNumberGeneratorProps) {
  const [currentPattern, setCurrentPattern] = useState<BatchPattern>(pattern);
  const [loading, setLoading] = useState(false);
  const [preview, setPreview] = useState('');
  const [error, setError] = useState<string | null>(null);

  const productCode = useMemo(() => codeFromName(productNameById?.[productId ?? '']), [productId, productNameById]);
  const vendorCode = useMemo(() => codeFromName(vendorNameById?.[vendorId ?? ''], 3), [vendorId, vendorNameById]);

  // Clear stale preview whenever inputs affecting the pattern change
  useEffect(() => {
    setPreview('');
  }, [date, productId, vendorId, currentPattern]);

  const buildBase = useCallback((seq: number) => {
    const seq2 = pad(seq, 2);
    switch (currentPattern) {
      case 'YYDOY-PROD-SEQ2':
        return `${toYYDOY(date)}-${productCode}-${seq2}`;
      case 'YYYYMMDD-VEND-PROD-SEQ2':
        return `${toYYYYMMDD(date)}-${vendorCode}-${productCode}-${seq2}`;
      case 'YYYYMMDD-PROD-SEQ2':
      default:
        return `${toYYYYMMDD(date)}-${productCode}-${seq2}`;
    }
  }, [currentPattern, date, productCode, vendorCode]);

  const buildPrefix = useCallback(() => {
    console.log('buildPrefix inputs:', { currentPattern, date, productCode, vendorCode });
    const result = (() => {
      switch (currentPattern) {
        case 'YYDOY-PROD-SEQ2':
          return `${toYYDOY(date)}-${productCode}-`;
        case 'YYYYMMDD-VEND-PROD-SEQ2':
          return `${toYYYYMMDD(date)}-${vendorCode}-${productCode}-`;
        case 'YYYYMMDD-PROD-SEQ2':
        default:
          return `${toYYYYMMDD(date)}-${productCode}-`;
      }
    })();
    console.log('buildPrefix result:', { currentPattern, date, productCode, vendorCode, result });
    return result;
  }, [currentPattern, date, productCode, vendorCode]);

  const estimateNextSeq = useCallback(async (): Promise<number> => {
    if (!date) return 1;
    const prefix = buildPrefix();
    if (!prefix) return 1;

    console.log('estimateNextSeq with prefix:', prefix);

    // Query existing TLC and batch_number values that start with the prefix
    const [tlcRes, batchRes, lotsRes] = await Promise.all([
      supabase.from('inventory_events').select('metadata').ilike('metadata->>tlc', `${prefix}%`).limit(200),
      supabase.from('inventory_events').select('metadata').ilike('metadata->>batch_number', `${prefix}%`).limit(200),
      supabase.from('lots').select('tlc').ilike('tlc', `${prefix}%`).limit(200),
    ]);

    console.log('Database query results:', { 
      tlcRes: tlcRes.error ? `Error: ${tlcRes.error.message}` : `${tlcRes.data?.length} rows`,
      batchRes: batchRes.error ? `Error: ${batchRes.error.message}` : `${batchRes.data?.length} rows`,
      lotsRes: lotsRes.error ? `Error: ${lotsRes.error.message}` : `${lotsRes.data?.length} rows`
    });

    const parseMax = (rows?: MetaRow[], key?: 'tlc' | 'batch_number') => {
      let max = 0;
      if (!rows) return 0;
      for (const r of rows) {
        const v = (r?.metadata as Record<string, unknown> | undefined | null)?.[key ?? 'tlc'];
        if (typeof v === 'string' && v.startsWith(prefix)) {
          const num = parseInt(v.slice(prefix.length), 10);
          if (!Number.isNaN(num) && num > max) max = num;
        }
      }
      return max;
    };

    if (tlcRes.error && batchRes.error && lotsRes.error) {
      // Fallback to old heuristic: count receiving events on date
      const start = new Date(`${date  }T00:00:00`);
      const end = new Date(`${date  }T23:59:59.999`);
      const { count } = await supabase
        .from('inventory_events')
        .select('id', { count: 'exact', head: true })
        .eq('event_type', 'receiving')
        .gte('created_at', start.toISOString())
        .lte('created_at', end.toISOString());
      return (count ?? 0) + 1;
    }

    const tlcRows: MetaRow[] = (tlcRes.error ? [] : ((tlcRes.data as unknown) as MetaRow[])) ?? [];
    const batchRows: MetaRow[] = (batchRes.error ? [] : ((batchRes.data as unknown) as MetaRow[])) ?? [];
    const lotRows: LotRow[] = (lotsRes.error ? [] : ((lotsRes.data as unknown) as LotRow[])) ?? [];

    // Parse max from lots (tlc string)
    let lotMax = 0;
    for (const r of lotRows) {
      const v = r?.tlc ?? undefined;
      if (typeof v === 'string' && v.startsWith(prefix)) {
        const num = parseInt(v.slice(prefix.length), 10);
        if (!Number.isNaN(num) && num > lotMax) lotMax = num;
      }
    }

    const maxSeq = Math.max(parseMax(tlcRows, 'tlc'), parseMax(batchRows, 'batch_number'), lotMax);
    console.log('estimateNextSeq:', { prefix, maxSeq, tlcCount: tlcRows.length, batchCount: batchRows.length, lotCount: lotRows.length });

    // Ensure we return at least 1, and cap at 99 for SEQ2 patterns
    const nextSeq = Math.max(1, maxSeq + 1);
    return Math.min(nextSeq, 99);
  }, [date, buildPrefix]);

  const checkUnique = useCallback(async (candidate: string) => {
    console.log('checkUnique called with candidate:', candidate);
    
    // Use JSONB containment which is well supported in PostgREST/Supabase
    const byTlc = await supabase
      .from('inventory_events')
      .select('id')
      .contains('metadata', { tlc: candidate })
      .limit(1);
    console.log('byTlc check:', byTlc.error ? `Error: ${byTlc.error.message}` : `${byTlc.data?.length} matches`);
    if (!byTlc.error && byTlc.data && byTlc.data.length > 0) return false;

    const byBatch = await supabase
      .from('inventory_events')
      .select('id')
      .contains('metadata', { batch_number: candidate })
      .limit(1);
    console.log('byBatch check:', byBatch.error ? `Error: ${byBatch.error.message}` : `${byBatch.data?.length} matches`);
    if (!byBatch.error && byBatch.data && byBatch.data.length > 0) return false;

    // Also ensure uniqueness across lots table (TLCs)
    const byLots = await supabase
      .from('lots')
      .select('id')
      .eq('tlc', candidate)
      .limit(1);
    console.log('byLots check:', byLots.error ? `Error: ${byLots.error.message}` : `${byLots.data?.length} matches`);
    if (!byLots.error && byLots.data && byLots.data.length > 0) return false;

    console.log('candidate is unique:', candidate);
    // If checks error out, default to allowing generation; creation path still stores TLC to prevent future duplicates
    return true;
  }, []);

  const doGenerate = useCallback(async () => {
    setError(null);
    console.log('doGenerate called with:', { 
      date, 
      productId, 
      vendorId, 
      productCode, 
      vendorCode, 
      productName: productNameById?.[productId ?? ''],
      vendorName: vendorNameById?.[vendorId ?? ''],
      currentPattern 
    });
    
    // Basic preconditions to avoid generating invalid/ambiguous patterns
    if (!date) {
      setError('Please select a date.');
      return;
    }
    if (!productCode) {
      setError(`Please select a product. Product ID: ${productId}, Product Name: ${productNameById?.[productId ?? '']}`);
      return;
    }
    if (currentPattern === 'YYYYMMDD-VEND-PROD-SEQ2' && !vendorCode) {
      setError('Please select a vendor for this pattern.');
      return;
    }
    setLoading(true);
    try {
      const seq = await estimateNextSeq();
      console.log('Generated sequence:', seq, 'for date:', date, 'product:', productCode, 'vendor:', vendorCode);
      
      // Guard against exceeding 2 digits for SEQ2 patterns
      if (seq >= 100) {
        setError('Sequence exceeds 2 digits for this pattern/date. Consider changing date or pattern.');
        return;
      }
      let n = seq;
      let candidate = buildBase(n);
      console.log('Built candidate:', candidate, 'from sequence:', n);
      
      let unique = await checkUnique(candidate);
      // Try incrementing until a unique candidate is found or we hit 99
      while (!unique && n < 99) {
        n += 1;
        candidate = buildBase(n);
        unique = await checkUnique(candidate);
      }
      if (!unique) {
        setError('Unable to find a unique batch for this pattern/date. Try a different date or pattern.');
        return;
      }
      setPreview(candidate);
    } catch (e: unknown) {
      console.error('Batch generation error:', e);
      setError(e instanceof Error ? e.message : 'Failed to generate batch number');
    } finally {
      setLoading(false);
    }
  }, [estimateNextSeq, buildBase, checkUnique, date, productId, productCode, vendorId, vendorCode, currentPattern, productNameById, vendorNameById]);

  // Clear preview when inputs change
  useEffect(() => {
    setPreview('');
  }, [date, productId, vendorId, currentPattern]);


  return (
    <div className="space-y-3">
      <div className="flex items-center gap-2">
        <select
          value={currentPattern}
          onChange={(e) => setCurrentPattern(e.target.value as BatchPattern)}
          className="text-sm rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          disabled={disabled}
        >
          <option value="YYYYMMDD-PROD-SEQ2">Date-Product-Seq</option>
          <option value="YYDOY-PROD-SEQ2">Julian-Product-Seq</option>
          <option value="YYYYMMDD-VEND-PROD-SEQ2">Date-Vendor-Product-Seq</option>
        </select>
        
        <button
          onClick={doGenerate}
          // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
          disabled={disabled || loading || !date || !productCode}
          className="px-4 py-2 text-sm font-medium bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {loading ? 'Generating...' : 'Generate Batch #'}
        </button>
      </div>
      
      {date && productCode && (
        <div className="text-xs text-gray-500 bg-gray-50 px-2 py-1 rounded">
          Format: {currentPattern === 'YYDOY-PROD-SEQ2' ? toYYDOY(date) : toYYYYMMDD(date)}-
          {currentPattern === 'YYYYMMDD-VEND-PROD-SEQ2' && vendorCode ? `${vendorCode}-` : ''}
          {productCode}-XX
        </div>
      )}
      
      {error && <div className="text-sm text-red-600 bg-red-50 px-3 py-2 rounded">{error}</div>}
      
      {preview && (
        <div className="flex items-center gap-2">
          <input
            type="text"
            readOnly
            value={preview}
            className="flex-1 text-sm rounded-lg border-gray-300 bg-gray-50 font-mono"
          />
          <button
            type="button"
            onClick={() => onGenerate(preview)}
            disabled={disabled}
            className="px-3 py-2 text-sm font-medium bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors"
          >
            Use This
          </button>
        </div>
      )}
    </div>
  );
}
