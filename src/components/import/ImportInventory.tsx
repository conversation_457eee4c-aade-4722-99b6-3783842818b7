import { useMemo, useState } from 'react';
import { VoiceInput } from './VoiceInput';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '../ui/tabs';
import { supabase } from '../../lib/supabase';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Alert, AlertDescription, AlertTitle } from '../ui/alert';
import { Info } from 'lucide-react';
import Papa from 'papaparse';

type FieldKey = 'name' | 'category' | 'supplier' | 'amount' | 'unit' | 'price' | 'cost' | 'origin' | 'notes';

const FIELD_OPTIONS: Array<{ key: FieldKey; label: string; required?: boolean; synonyms: string[] }> = [
  { key: 'name', label: 'Name', required: true, synonyms: ['name', 'product', 'item', 'product_name'] },
  { key: 'category', label: 'Category', required: true, synonyms: ['category', 'product_category', 'cat'] },
  { key: 'supplier', label: 'Supplier/Vendor', synonyms: ['supplier', 'vendor'] },
  { key: 'amount', label: 'Amount/Quantity', required: true, synonyms: ['amount', 'qty', 'quantity', 'count'] },
  { key: 'unit', label: 'Unit', required: true, synonyms: ['unit', 'uom', 'units'] },
  { key: 'price', label: 'Price (sell)', synonyms: ['price', 'sell_price', 'sales_price'] },
  { key: 'cost', label: 'Cost (buy)', synonyms: ['cost', 'buy_price', 'purchase_price'] },
  { key: 'origin', label: 'Origin', synonyms: ['origin', 'country', 'location'] },
  { key: 'notes', label: 'Notes', synonyms: ['notes', 'desc', 'description'] },
];

type CsvRow = Record<string, string>;
type Mapping = Record<string, string | null>;

const ImportInventory = () => {
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [fileImportData, setFileImportData] = useState<CsvRow[] | null>(null);
  const [headers, setHeaders] = useState<string[]>([]);
  const [mapping, setMapping] = useState<Mapping>({});
  const [rawCsvText, setRawCsvText] = useState<string>('');
  const [isUploading, setIsUploading] = useState(false);
  // Voice import handled internally by <VoiceInput />

  const autoMapHeaders = (cols: string[]): Mapping => {
    const m: Mapping = {};
    const lowerCols = cols.map((c) => c.toLowerCase());
    FIELD_OPTIONS.forEach((f) => {
      let chosen: string | null = null;
      for (const syn of f.synonyms) {
        const idx = lowerCols.indexOf(syn.toLowerCase());
        if (idx !== -1) {
          chosen = cols[idx];
          break;
        }
      }
      // If exact key exists
      if (!chosen) {
        const idx = lowerCols.indexOf(String(f.key).toLowerCase());
        if (idx !== -1) chosen = cols[idx];
      }
      m[String(f.key)] = chosen;
    });
    return m;
  };

  const requiredUnmapped = useMemo(() => {
    return FIELD_OPTIONS.filter((f) => f.required).filter((f) => !mapping[String(f.key)]);
  }, [mapping]);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsUploading(true);
    setError(null);
    setSuccess(null);

    try {
      // For CSV files
      if (file.type === 'text/csv' || file.name.endsWith('.csv')) {
        const text = await file.text();
        setRawCsvText(text);
        const result = Papa.parse<CsvRow>(text, { header: true, skipEmptyLines: true, dynamicTyping: false });
        if (result.errors?.length) {
          throw new Error(`CSV parse error: ${result.errors[0].message}`);
        }
        const cols = (result.meta.fields || []).filter(Boolean) as string[];
        setHeaders(cols);
        setMapping(autoMapHeaders(cols));
        setFileImportData(result.data || []);
        setSuccess(`Successfully processed ${result.data?.length || 0} rows from CSV file`);
      } else {
        setError('Unsupported file format. Please upload a CSV file.');
      }
    } catch (err: unknown) {
      const message = err instanceof Error ? err.message : String(err);
      setError(`Error processing file: ${message}`);
    } finally {
      setIsUploading(false);
    }
  };

  type ProductInsert = {
    name: string;
    category: string;
    supplier?: string | null;
    amount: number;
    unit: string;
    price: number | null;
    cost: number | null;
    origin?: string | null;
    notes?: string | null;
  };

  const buildProductsFromRows = (rows: CsvRow[]): ProductInsert[] => {
    if (!rows?.length) return [];
    const hasMapping = headers.length > 0 && Object.values(mapping).some(Boolean);
    const get = (row: CsvRow, targetKey: string, fallbacks: string[], def?: string) => {
      if (hasMapping && mapping[targetKey]) return row[mapping[targetKey] as string] ?? def;
      for (const fb of fallbacks) {
        if (row[fb] != null && row[fb] !== '') return row[fb];
      }
      return def;
    };
    return rows.map((row) => {
      const amountRaw = get(row, 'amount', ['amount', 'qty', 'quantity', 'count'], '0');
      const priceRaw = get(row, 'price', ['price', 'sell_price', 'sales_price']);
      const costRaw = get(row, 'cost', ['cost', 'buy_price', 'purchase_price']);
      return {
        name: get(row, 'name', ['name', 'product', 'item', 'product_name'], '') ?? '',
        category: get(row, 'category', ['category', 'product_category', 'cat'], '') ?? '',
        supplier: get(row, 'supplier', ['supplier', 'vendor'], '') ?? '',
        amount: parseFloat(String(amountRaw ?? '0')) || 0,
        unit: String(get(row, 'unit', ['unit', 'uom', 'units'], 'lb') || 'lb'),
        price: priceRaw != null && priceRaw !== '' ? parseFloat(String(priceRaw)) : null,
        cost: costRaw != null && costRaw !== '' ? parseFloat(String(costRaw)) : null,
        origin: get(row, 'origin', ['origin', 'country', 'location']) || null,
        notes: get(row, 'notes', ['notes', 'desc', 'description']) || null,
      };
    });
  };

  const handleImportFileData = async () => {
    if (!fileImportData || fileImportData.length === 0) {
      setError('No data to import');
      return;
    }

    setIsUploading(true);
    setError(null);

    try {
      // Convert using mapping
      const productsData = buildProductsFromRows(fileImportData);

      // Validate required mapped fields
      const missing = productsData.filter((p) => !p.name || !p.category || !p.unit);
      if (missing.length > 0) {
        throw new Error('Required fields missing after mapping (name, category, unit). Please update column mappings.');
      }

      // Insert into products table
      const { data, error: insertError } = await supabase
        .from('Products')
        .insert(productsData)
        .select();

      if (insertError) {
        throw new Error(insertError.message);
      }

      setSuccess(`Successfully imported ${data?.length ?? 0} products`);
      setFileImportData(null);
    } catch (err: unknown) {
      const message = err instanceof Error ? err.message : String(err);
      setError(`Import failed: ${message}`);
    } finally {
      setIsUploading(false);
    }
  };

  // Note: Voice import is managed by VoiceInput; CSV import mapping is below.

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Import Inventory</CardTitle>
        <CardDescription>
          Add new products to your inventory using voice or file import
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="voice" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="voice">Voice Import</TabsTrigger>
            <TabsTrigger value="file">File Import</TabsTrigger>
          </TabsList>

          <TabsContent value="voice" className="mt-4 space-y-4">
            <Alert>
              <Info className="h-4 w-4" />
              <AlertTitle>Voice Import</AlertTitle>
              <AlertDescription>
                Speak clearly to describe the seafood products you want to add to
                inventory. Include product name, category, quantity, and price if
                possible.
              </AlertDescription>
            </Alert>
            <VoiceInput />
          </TabsContent>

          <TabsContent value="file" className="mt-4 space-y-4">
            <Alert>
              <Info className="h-4 w-4" />
              <AlertTitle>File Import</AlertTitle>
              <AlertDescription>
                Upload a CSV file with your product data. The file should include
                columns for name, category, amount, unit, and optionally price,
                cost, supplier, and origin.
              </AlertDescription>
            </Alert>

            <div className="grid gap-4">
              <div className="flex items-center gap-4">
                <label htmlFor="csv-file" className="text-sm font-medium">
                  Upload CSV:
                </label>
                <input
                  id="csv-file"
                  type="file"
                  accept=".csv"
                  onChange={handleFileUpload}
                  disabled={isUploading}
                  className="file:mr-4 file:rounded-md file:border-0 file:bg-primary file:px-4 file:py-2 file:text-sm file:font-medium file:text-primary-foreground hover:file:bg-primary/90"
                />
                {fileImportData && (
                  <button
                    onClick={handleImportFileData}
                    disabled={isUploading}
                    className="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-white hover:bg-primary/90 disabled:opacity-50"
                  >
                    {isUploading ? 'Importing...' : 'Import Data'}
                  </button>
                )}
              </div>

              {/* Optional: Paste CSV fallback for testing */}
              <div className="grid gap-2">
                <label htmlFor="csv-paste" className="text-sm font-medium">
                  Or paste CSV (for quick testing):
                </label>
                <textarea
                  id="csv-paste"
                  className="min-h-[120px] w-full rounded-md border p-2 text-sm"
                  placeholder="name,category,amount,unit,price\nSalmon,Finfish,10,lb,12.5"
                  value={rawCsvText}
                  onChange={(e) => setRawCsvText(e.target.value)}
                />
                <div>
                  <button
                    onClick={() => {
                      setError(null);
                      setSuccess(null);
                      const result = Papa.parse<CsvRow>(rawCsvText, { header: true, skipEmptyLines: true, dynamicTyping: false });
                      if (result.errors?.length) {
                        setError(`CSV parse error: ${result.errors[0].message}`);
                        return;
                      }
                      const cols = (result.meta.fields || []).filter(Boolean) as string[];
                      setHeaders(cols);
                      setMapping(autoMapHeaders(cols));
                      setFileImportData(result.data || []);
                      setSuccess(`Parsed ${result.data?.length || 0} rows.`);
                    }}
                    className="rounded-md bg-secondary px-3 py-1 text-sm"
                  >
                    Parse Pasted CSV
                  </button>
                </div>
              </div>

              {fileImportData && (
                <div className="rounded-md border p-4">
                  <p className="font-medium">Preview ({fileImportData.length} items):</p>
                  <div className="mt-2 max-h-[300px] overflow-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b">
                          {Object.keys(fileImportData[0] || { '': '' }).map((key) => (
                            <th key={key} className="p-2 text-left">
                              {key}
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody>
                        {fileImportData.slice(0, 5).map((item, i) => (
                          <tr key={i} className="border-b">
                            {Object.values(item).map((value, j) => (
                              <td key={j} className="p-2">
                                {value || '-'}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                    {fileImportData.length > 5 && (
                      <p className="mt-2 text-sm text-gray-500">
                        ... and {fileImportData.length - 5} more items
                      </p>
                    )}
                  </div>
                </div>
              )}

              {/* Mapping UI */}
              {headers.length > 0 && (
                <div className="rounded-md border p-4">
                  <p className="font-medium">Column Mapping</p>
                  <p className="mb-2 text-xs text-gray-500">Map your CSV columns to product fields.</p>
                  <div className="grid gap-3 sm:grid-cols-2">
                    {FIELD_OPTIONS.map((f) => (
                      <div key={String(f.key)} className="flex items-center gap-2">
                        <label className="w-40 text-sm">{f.label}{f.required ? ' *' : ''}</label>
                        <select
                          className="flex-1 rounded-md border p-2 text-sm"
                          aria-label={`Map to ${f.label}`}
                          value={mapping[String(f.key)] || ''}
                          onChange={(e) => setMapping((m) => ({ ...m, [String(f.key)]: e.target.value || null }))}
                        >
                          <option value="">(Ignore)</option>
                          {headers.map((h) => (
                            <option key={h} value={h}>{h}</option>
                          ))}
                        </select>
                      </div>
                    ))}
                  </div>
                  {requiredUnmapped.length > 0 && (
                    <p className="mt-2 text-xs text-red-600">Required unmapped: {requiredUnmapped.map((f) => f.label).join(', ')}</p>
                  )}
                  <div className="mt-3">
                    <p className="mb-1 text-sm font-medium">Mapped preview (first 3 rows)</p>
                    <pre className="max-h-48 overflow-auto rounded bg-muted p-2 text-xs">
{JSON.stringify(buildProductsFromRows(fileImportData || []).slice(0, 3), null, 2)}
                    </pre>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>

        {error && (
          <div className="mt-4 rounded-md bg-red-50 p-4 text-sm text-red-500">
            {error}
          </div>
        )}
        {success && (
          <div className="mt-4 rounded-md bg-green-50 p-4 text-sm text-green-500">
            {success}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ImportInventory;
