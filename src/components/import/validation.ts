import type { ImportRow, ValidationError } from './types';
import { validateWithAI } from './aiProcessor';

export async function validateImportData(data: ImportRow[]): Promise<ValidationError[]> {
  const errors: ValidationError[] = [];
  
  // First run basic validation
  const basicErrors = validateBasicData(data);
  errors.push(...basicErrors);

  // Only run AI validation if basic validation passes
  if (errors.length === 0) {
    const aiErrors = await validateWithAI(data);
    errors.push(...aiErrors);
  }

  return errors;
}

function validateBasicData(data: ImportRow[]): ValidationError[] {
  const errors: ValidationError[] = [];

  data.forEach((row, index) => {
    const rowNumber = index + 2; // Adding 2 to account for header row and 0-based index

    // Required fields
    if (!row.date) {
      errors.push({ row: rowNumber, message: 'Date is required' });
    } else {
      const date = new Date(row.date);
      if (isNaN(date.getTime())) {
        errors.push({ row: rowNumber, message: 'Invalid date format' });
      }
    }

    if (!row.name) {
      errors.push({ row: rowNumber, message: 'Product name is required' });
    }

    if (!row.sku) {
      errors.push({ row: rowNumber, message: 'SKU is required' });
    }

    if (!row.category) {
      errors.push({ row: rowNumber, message: 'Category is required' });
    }

    if (!row.unit) {
      errors.push({ row: rowNumber, message: 'Unit is required' });
    }

    // Numeric validations with type coercion
    const stock = typeof row.stock === 'string' ? parseFloat(row.stock) : row.stock;
    if (typeof stock !== 'number' || isNaN(stock)) {
      errors.push({ row: rowNumber, message: 'Stock must be a valid number' });
    }

    if (row.cost !== undefined) {
      const cost = typeof row.cost === 'string' ? parseFloat(row.cost) : row.cost;
      if (typeof cost !== 'number' || isNaN(cost)) {
        errors.push({ row: rowNumber, message: 'Cost must be a valid number' });
      }
    }

    if (row.price !== undefined) {
      const price = typeof row.price === 'string' ? parseFloat(row.price) : row.price;
      if (typeof price !== 'number' || isNaN(price)) {
        errors.push({ row: rowNumber, message: 'Price must be a valid number' });
      }
    }

    if (row.min_stock !== undefined) {
      const minStock = typeof row.min_stock === 'string' ? parseFloat(row.min_stock) : row.min_stock;
      if (typeof minStock !== 'number' || isNaN(minStock)) {
        errors.push({ row: rowNumber, message: 'Minimum stock must be a valid number' });
      }
    }

    // Sales amount validations
    if (row.gross_amount !== undefined) {
      const grossAmount = typeof row.gross_amount === 'string' ? parseFloat(row.gross_amount) : row.gross_amount;
      if (typeof grossAmount !== 'number' || isNaN(grossAmount)) {
        errors.push({ row: rowNumber, message: 'Gross sales amount must be a valid number' });
      }
    }

    if (row.net_amount !== undefined) {
      const netAmount = typeof row.net_amount === 'string' ? parseFloat(row.net_amount) : row.net_amount;
      if (typeof netAmount !== 'number' || isNaN(netAmount)) {
        errors.push({ row: rowNumber, message: 'Net sales amount must be a valid number' });
      }
    }

    // Validate net amount is not greater than gross amount if both are provided
    if (row.gross_amount !== undefined && row.net_amount !== undefined) {
      const grossAmount = typeof row.gross_amount === 'string' ? parseFloat(row.gross_amount) : row.gross_amount;
      const netAmount = typeof row.net_amount === 'string' ? parseFloat(row.net_amount) : row.net_amount;
      if (!isNaN(grossAmount) && !isNaN(netAmount) && netAmount > grossAmount) {
        errors.push({ row: rowNumber, message: 'Net sales amount cannot be greater than gross sales amount' });
      }
    }

    // Date validations
    if (row.expiry_date) {
      try {
        const date = new Date(row.expiry_date);
        if (isNaN(date.getTime())) {
          errors.push({ row: rowNumber, message: 'Invalid expiry date format' });
        }
      } catch {
        errors.push({ row: rowNumber, message: 'Invalid expiry date format' });
      }
    }
  });

  return errors;
}
