/**
 * Enterprise Import Progress Dashboard
 * Real-time monitoring and analytics for large file processing
 * Optimized for seafood industry import operations
 */

import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Progress } from '../ui/progress';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription } from '../ui/alert';
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Database,
  FileText,
  Gauge,
  Pause,
  Play,
  Square,
  TrendingUp,
  Zap
} from 'lucide-react';
import { ProcessingProgress, ProcessingResult } from '../../workers/csvProcessor.worker';

interface ImportProgressDashboardProps {
  isProcessing: boolean;
  progress?: ProcessingProgress;
  result?: ProcessingResult;
  onCancel?: () => void;
  onRetry?: () => void;
  onPause?: () => void;
  onResume?: () => void;
  canPause?: boolean;
  isPaused?: boolean;
  className?: string;
}

interface PerformanceMetric {
  label: string;
  value: string;
  trend?: 'up' | 'down' | 'stable';
  icon?: React.ReactNode;
}

interface ProcessingStats {
  rowsPerSecond: number;
  bytesPerSecond: number;
  memoryUsage: number;
  errorRate: number;
  estimatedCompletion: Date | null;
  efficiency: 'excellent' | 'good' | 'fair' | 'poor';
}

export const ImportProgressDashboard: React.FC<ImportProgressDashboardProps> = ({
  isProcessing,
  progress,
  result,
  onCancel,
  onRetry,
  onPause,
  onResume,
  canPause = false,
  isPaused = false,
  className = ''
}) => {
  const [stats, setStats] = useState<ProcessingStats>({
    rowsPerSecond: 0,
    bytesPerSecond: 0,
    memoryUsage: 0,
    errorRate: 0,
    estimatedCompletion: null,
    efficiency: 'good'
  });

  const previousProgress = useRef<ProcessingProgress | null>(null);
  const statsHistory = useRef<ProcessingStats[]>([]);
  const updateInterval = useRef<NodeJS.Timeout | null>(null);

  // Calculate real-time statistics
  useEffect(() => {
    if (!progress || !isProcessing) return;

    const calculateStats = () => {
      const current = progress;
      const previous = previousProgress.current;

      if (!previous) {
        previousProgress.current = current;
        return;
      }

      const timeDiff = Date.now() - (previous.estimatedTimeRemaining || 0);
      const rowDiff = current.rowsProcessed - previous.rowsProcessed;
      const byteDiff = current.bytesProcessed - previous.bytesProcessed;

      if (timeDiff > 0) {
        const rowsPerSecond = (rowDiff / timeDiff) * 1000;
        const bytesPerSecond = (byteDiff / timeDiff) * 1000;
        const errorRate = current.errorsFound / Math.max(current.rowsProcessed, 1);
        
        const estimatedCompletion = current.estimatedTimeRemaining > 0 
          ? new Date(Date.now() + current.estimatedTimeRemaining)
          : null;

        let efficiency: ProcessingStats['efficiency'] = 'good';
        if (rowsPerSecond > 2000) efficiency = 'excellent';
        else if (rowsPerSecond > 1000) efficiency = 'good';
        else if (rowsPerSecond > 500) efficiency = 'fair';
        else efficiency = 'poor';

        const newStats: ProcessingStats = {
          rowsPerSecond: Math.round(rowsPerSecond),
          bytesPerSecond: Math.round(bytesPerSecond),
          memoryUsage: current.currentMemoryUsage,
          errorRate: Math.round(errorRate * 100) / 100,
          estimatedCompletion,
          efficiency
        };

        setStats(newStats);
        statsHistory.current.push(newStats);
        
        // Keep only last 10 entries for trend analysis
        if (statsHistory.current.length > 10) {
          statsHistory.current.shift();
        }
      }

      previousProgress.current = current;
    };

    updateInterval.current = setInterval(calculateStats, 2000); // Update every 2 seconds

    return () => {
      if (updateInterval.current) {
        clearInterval(updateInterval.current);
      }
    };
  }, [progress, isProcessing]);

  // Reset when processing starts/stops
  useEffect(() => {
    if (!isProcessing) {
      previousProgress.current = null;
      statsHistory.current = [];
    }
  }, [isProcessing]);

  const formatBytes = (bytes: number): string => {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${Math.round(bytes / Math.pow(1024, i) * 100) / 100} ${sizes[i]}`;
  };

  const formatDuration = (ms: number): string => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  };

  const getStageIcon = (stage: string) => {
    switch (stage) {
      case 'parsing': return <FileText className="h-4 w-4" />;
      case 'validating': return <CheckCircle className="h-4 w-4" />;
      case 'transforming': return <Zap className="h-4 w-4" />;
      case 'complete': return <CheckCircle className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  const getPerformanceMetrics = (): PerformanceMetric[] => {
    const metrics: PerformanceMetric[] = [
      {
        label: 'Processing Speed',
        value: `${stats.rowsPerSecond.toLocaleString()} rows/sec`,
        icon: <TrendingUp className="h-4 w-4" />
      },
      {
        label: 'Data Throughput',
        value: `${formatBytes(stats.bytesPerSecond)}/sec`,
        icon: <Database className="h-4 w-4" />
      },
      {
        label: 'Memory Usage',
        value: formatBytes(stats.memoryUsage),
        icon: <Gauge className="h-4 w-4" />
      },
      {
        label: 'Error Rate',
        value: `${stats.errorRate}%`,
        icon: <AlertTriangle className="h-4 w-4" />
      }
    ];

    if (stats.estimatedCompletion) {
      metrics.push({
        label: 'Estimated Completion',
        value: stats.estimatedCompletion.toLocaleTimeString(),
        icon: <Clock className="h-4 w-4" />
      });
    }

    return metrics;
  };

  const getEfficiencyColor = (efficiency: string) => {
    switch (efficiency) {
      case 'excellent': return 'bg-green-100 text-green-800 border-green-200';
      case 'good': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'fair': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'poor': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Function to get progress color based on current state
  // const getProgressColor = () => {
  //   if (!progress) return 'bg-blue-500';
  //   if (progress.errorsFound > 0) return 'bg-yellow-500';
  //   if (progress.stage === 'complete') return 'bg-green-500';
  //   return 'bg-blue-500';
  // };

  if (!isProcessing && !result) {
    return (
      <Card className={`w-full ${className}`}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Import Progress Monitor
          </CardTitle>
          <CardDescription>
            Real-time monitoring and analytics for seafood data imports
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8 text-muted-foreground">
            <div className="text-center">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No import operation in progress</p>
              <p className="text-sm">Start an import to see real-time progress</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (result && !isProcessing) {
    return (
      <Card className={`w-full ${className}`}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-500" />
            Import Complete
          </CardTitle>
          <CardDescription>
            Processing finished with {result.processedRows.toLocaleString()} rows
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Results Summary */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 rounded-lg bg-green-50 border border-green-200">
              <div className="text-2xl font-bold text-green-700">{result.processedRows.toLocaleString()}</div>
              <div className="text-sm text-green-600">Rows Processed</div>
            </div>
            <div className="text-center p-3 rounded-lg bg-red-50 border border-red-200">
              <div className="text-2xl font-bold text-red-700">{result.errors.length}</div>
              <div className="text-sm text-red-600">Errors Found</div>
            </div>
            <div className="text-center p-3 rounded-lg bg-yellow-50 border border-yellow-200">
              <div className="text-2xl font-bold text-yellow-700">{result.warnings.length}</div>
              <div className="text-sm text-yellow-600">Warnings</div>
            </div>
            <div className="text-center p-3 rounded-lg bg-blue-50 border border-blue-200">
              <div className="text-2xl font-bold text-blue-700">
                {formatDuration(result.metadata.processingEndTime - result.metadata.processingStartTime)}
              </div>
              <div className="text-sm text-blue-600">Duration</div>
            </div>
          </div>

          {/* Performance Metrics */}
          {result.performanceMetrics && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-3 rounded-lg border">
                <div className="text-sm text-muted-foreground">Processing Speed</div>
                <div className="text-lg font-semibold">{result.performanceMetrics.rowsPerSecond.toLocaleString()} rows/sec</div>
              </div>
              <div className="p-3 rounded-lg border">
                <div className="text-sm text-muted-foreground">Data Throughput</div>
                <div className="text-lg font-semibold">{formatBytes(result.performanceMetrics.bytesPerSecond)}/sec</div>
              </div>
              <div className="p-3 rounded-lg border">
                <div className="text-sm text-muted-foreground">Memory Efficiency</div>
                <div className="text-lg font-semibold capitalize">{result.performanceMetrics.memoryEfficiency}</div>
              </div>
            </div>
          )}

          {/* Error/Warning Summary */}
          {(result.errors.length > 0 || result.warnings.length > 0) && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                {result.errors.length > 0 && (
                  <span className="text-red-600 font-medium">
                    {result.errors.length} errors found. 
                  </span>
                )}
                {result.errors.length > 0 && result.warnings.length > 0 && ' '}
                {result.warnings.length > 0 && (
                  <span className="text-yellow-600 font-medium">
                    {result.warnings.length} warnings detected.
                  </span>
                )}
                <span className="ml-2">Review the detailed report for more information.</span>
              </AlertDescription>
            </Alert>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-2">
            {onRetry && result.errors.length > 0 && (
              <Button onClick={onRetry} variant="outline">
                Retry Import
              </Button>
            )}
            <Button onClick={() => window.print()} variant="outline">
              Print Report
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getStageIcon(progress?.stage || 'parsing')}
            Processing Seafood Import
            {isPaused && (
              <Badge variant="secondary" className="ml-2">
                <Pause className="h-3 w-3 mr-1" />
                Paused
              </Badge>
            )}
          </div>
          <Badge className={getEfficiencyColor(stats.efficiency)}>
            {stats.efficiency.toUpperCase()}
          </Badge>
        </CardTitle>
        <CardDescription>
          {progress?.stage && (
            <span className="capitalize">{progress.stage.replace('_', ' ')}</span>
          )}
          {progress && ` • Chunk ${progress.currentChunk} of ${progress.totalChunks || '?'}`}
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Main Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>
              {progress?.rowsProcessed?.toLocaleString() || 0} of {progress?.totalRows?.toLocaleString() || '?'} rows
            </span>
            <span>{Math.round(progress?.percentage || 0)}%</span>
          </div>
          <Progress 
            value={progress?.percentage || 0} 
            className="h-3"
          />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>{formatBytes(progress?.bytesProcessed || 0)} processed</span>
            <span>
              {progress?.estimatedTimeRemaining ? 
                `${formatDuration(progress.estimatedTimeRemaining)} remaining` : 
                'Calculating...'
              }
            </span>
          </div>
        </div>

        {/* Performance Metrics Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
          {getPerformanceMetrics().map((metric, index) => (
            <div key={index} className="text-center p-3 rounded-lg border">
              <div className="flex items-center justify-center mb-2">
                {metric.icon}
              </div>
              <div className="text-lg font-semibold">{metric.value}</div>
              <div className="text-xs text-muted-foreground">{metric.label}</div>
            </div>
          ))}
        </div>

        {/* Current Status */}
        <div className="flex items-center justify-between p-4 rounded-lg bg-muted/50">
          <div className="flex items-center gap-3">
            <div className="h-2 w-2 rounded-full bg-green-500 animate-pulse" />
            <span className="text-sm font-medium">
              {isPaused ? 'Import Paused' : 'Processing in progress...'}
            </span>
          </div>
          
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            {progress?.errorsFound !== undefined && progress.errorsFound > 0 && (
              <span className="flex items-center gap-1 text-red-600">
                <AlertTriangle className="h-3 w-3" />
                {progress.errorsFound} errors
              </span>
            )}
            {progress?.warningsFound !== undefined && progress.warningsFound > 0 && (
              <span className="flex items-center gap-1 text-yellow-600">
                <AlertTriangle className="h-3 w-3" />
                {progress.warningsFound} warnings
              </span>
            )}
          </div>
        </div>

        {/* Control Buttons */}
        <div className="flex justify-end gap-2">
          {canPause && onPause && onResume && (
            <Button
              variant="outline"
              size="sm"
              onClick={isPaused ? onResume : onPause}
              disabled={!isProcessing}
            >
              {isPaused ? (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  Resume
                </>
              ) : (
                <>
                  <Pause className="h-4 w-4 mr-2" />
                  Pause
                </>
              )}
            </Button>
          )}
          
          {onCancel && (
            <Button
              variant="destructive"
              size="sm"
              onClick={onCancel}
              disabled={!isProcessing}
            >
              <Square className="h-4 w-4 mr-2" />
              Cancel
            </Button>
          )}
        </div>

        {/* Memory Usage Warning */}
        {stats.memoryUsage > 200 * 1024 * 1024 && (
          <Alert>
            <Gauge className="h-4 w-4" />
            <AlertDescription>
              High memory usage detected ({formatBytes(stats.memoryUsage)}). 
              Consider using smaller chunk sizes for better performance.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};

export default ImportProgressDashboard;