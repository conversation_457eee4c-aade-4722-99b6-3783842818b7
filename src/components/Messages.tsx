import React, { useState, useEffect, useRef } from 'react';
import { Bell, MessageSquare, User, Clock, AlertTriangle, CheckCircle, Info, X, Send, Plus, Trash2, Search } from 'lucide-react';
import { supabase } from '../lib/supabase';
import { format, formatDistanceToNow } from 'date-fns';

interface Message {
  id: string;
  title: string;
  content: string;
  type: 'notification' | 'alert' | 'reminder' | 'system' | 'user_message';
  priority: 'low' | 'medium' | 'high' | 'critical';
  sender_id?: string;
  sender_name?: string;
  recipient_id?: string;
  recipient_name?: string;
  read: boolean;
  read_at?: string;
  created_at: string;
  expires_at?: string;
  related_entity_type?: string;
  related_entity_id?: string;
  metadata?: Record<string, unknown>;
}

interface NotificationSettings {
  email_notifications: boolean;
  push_notifications: boolean;
  inventory_alerts: boolean;
  quality_alerts: boolean;
  expiration_warnings: boolean;
  daily_summaries: boolean;
}

const MESSAGE_TYPES = {
  notification: { icon: Bell, color: 'text-blue-600', bg: 'bg-blue-50', border: 'border-blue-200' },
  alert: { icon: AlertTriangle, color: 'text-red-600', bg: 'bg-red-50', border: 'border-red-200' },
  reminder: { icon: Clock, color: 'text-yellow-600', bg: 'bg-yellow-50', border: 'border-yellow-200' },
  system: { icon: Info, color: 'text-gray-600', bg: 'bg-gray-50', border: 'border-gray-200' },
  user_message: { icon: MessageSquare, color: 'text-green-600', bg: 'bg-green-50', border: 'border-green-200' }
};

const PRIORITY_COLORS = {
  low: 'text-gray-500',
  medium: 'text-blue-500',
  high: 'text-orange-500',
  critical: 'text-red-500'
};

export default function Messages() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [filteredMessages, setFilteredMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedMessage, setSelectedMessage] = useState<Message | null>(null);
  const [showCompose, setShowCompose] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [filterPriority, setFilterPriority] = useState<string>('all');
  const [showUnreadOnly, setShowUnreadOnly] = useState(false);
  const [_settings, _setSettings] = useState<NotificationSettings>({
    email_notifications: true,
    push_notifications: true,
    inventory_alerts: true,
    quality_alerts: true,
    expiration_warnings: true,
    daily_summaries: false
  });
  const [newMessage, setNewMessage] = useState({
    title: '',
    content: '',
    type: 'user_message' as Message['type'],
    priority: 'medium' as Message['priority'],
    recipient_id: ''
  });

  const _messagesEndRef = useRef<HTMLDivElement>(null);

  // Load messages from database
  useEffect(() => {
    loadMessages();
    // Set up real-time subscription for new messages
    const subscription = supabase
      .channel('messages_channel')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'messages' },
        () => loadMessages()
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  // Filter messages based on search and filters
  useEffect(() => {
    let filtered = messages;

    // Search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(msg => 
        msg.title.toLowerCase().includes(query) ||
        msg.content.toLowerCase().includes(query) ||
        msg.sender_name?.toLowerCase().includes(query)
      );
    }

    // Type filter
    if (filterType !== 'all') {
      filtered = filtered.filter(msg => msg.type === filterType);
    }

    // Priority filter
    if (filterPriority !== 'all') {
      filtered = filtered.filter(msg => msg.priority === filterPriority);
    }

    // Unread filter
    if (showUnreadOnly) {
      filtered = filtered.filter(msg => !msg.read);
    }

    setFilteredMessages(filtered);
  }, [messages, searchQuery, filterType, filterPriority, showUnreadOnly]);

  const loadMessages = async () => {
    try {
      setLoading(true);
      setError(null);

      // For now, create sample messages since the table doesn't exist yet
      // In production, this would be: const { data, error } = await supabase.from('messages').select('*').order('created_at', { ascending: false });
      
      const sampleMessages: Message[] = [
        {
          id: '1',
          title: 'Low Inventory Alert: Atlantic Cod',
          content: 'Current stock of Atlantic Cod has fallen below minimum threshold (50 lbs). Current level: 23 lbs. Consider placing new order.',
          type: 'alert',
          priority: 'high',
          sender_name: 'System',
          read: false,
          created_at: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
          related_entity_type: 'product',
          related_entity_id: 'atlantic-cod-001',
          metadata: { current_stock: 23, minimum_threshold: 50, unit: 'lbs' }
        },
        {
          id: '2',
          title: 'Quality Check Reminder',
          content: 'Quality inspection due for Batch #PCK-2024-001 (Pacific Salmon). Please complete temperature and freshness checks by end of day.',
          type: 'reminder',
          priority: 'medium',
          sender_name: 'HACCP System',
          read: false,
          created_at: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
          related_entity_type: 'batch',
          related_entity_id: 'PCK-2024-001'
        },
        {
          id: '3',
          title: 'Shipment Arrived',
          content: 'Shipment from Ocean Fresh Seafoods has arrived and been processed. 150 lbs Pacific Halibut, 200 lbs Dungeness Crab received in excellent condition.',
          type: 'notification',
          priority: 'low',
          sender_name: 'Receiving Department',
          read: true,
          read_at: new Date(Date.now() - 1000 * 60 * 60).toISOString(),
          created_at: new Date(Date.now() - 1000 * 60 * 60 * 4).toISOString(), // 4 hours ago
          related_entity_type: 'shipment',
          related_entity_id: 'ship-001'
        },
        {
          id: '4',
          title: 'Temperature Monitoring Alert',
          content: 'Cold storage unit #2 temperature exceeded threshold. Current: 38°F (Threshold: 36°F). Immediate action required.',
          type: 'alert',
          priority: 'critical',
          sender_name: 'Temperature Monitor',
          read: false,
          created_at: new Date(Date.now() - 1000 * 60 * 15).toISOString(), // 15 minutes ago
          related_entity_type: 'equipment',
          related_entity_id: 'cold-storage-2',
          metadata: { current_temp: 38, threshold: 36, unit: 'fahrenheit' }
        },
        {
          id: '5',
          title: 'New Order Received',
          content: 'Restaurant Blue Ocean has placed order #ORD-2024-0156 for 75 lbs assorted seafood. Delivery requested for tomorrow 9 AM.',
          type: 'notification',
          priority: 'medium',
          sender_name: 'Order System',
          read: true,
          read_at: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
          created_at: new Date(Date.now() - 1000 * 60 * 60 * 6).toISOString(), // 6 hours ago
          related_entity_type: 'order',
          related_entity_id: 'ORD-2024-0156'
        }
      ];

      setMessages(sampleMessages);
    } catch (err) {
      console.error('Error loading messages:', err);
      setError('Failed to load messages');
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = async (messageId: string) => {
    try {
      setMessages(prev => prev.map(msg => 
        msg.id === messageId 
          ? { ...msg, read: true, read_at: new Date().toISOString() }
          : msg
      ));
      
      // In production: await supabase.from('messages').update({ read: true, read_at: new Date().toISOString() }).eq('id', messageId);
    } catch (err) {
      console.error('Error marking message as read:', err);
    }
  };

  const deleteMessage = async (messageId: string) => {
    try {
      setMessages(prev => prev.filter(msg => msg.id !== messageId));
      if (selectedMessage?.id === messageId) {
        setSelectedMessage(null);
      }
      
      // In production: await supabase.from('messages').delete().eq('id', messageId);
    } catch (err) {
      console.error('Error deleting message:', err);
    }
  };

  const sendMessage = async () => {
    if (!newMessage.title.trim() || !newMessage.content.trim()) {
      return;
    }

    try {
      const message: Message = {
        id: Date.now().toString(),
        ...newMessage,
        sender_name: 'You',
        read: true,
        created_at: new Date().toISOString()
      };

      setMessages(prev => [message, ...prev]);
      setNewMessage({
        title: '',
        content: '',
        type: 'user_message',
        priority: 'medium',
        recipient_id: ''
      });
      setShowCompose(false);

      // In production: await supabase.from('messages').insert(message);
    } catch (err) {
      console.error('Error sending message:', err);
    }
  };

  const markAllAsRead = async () => {
    try {
      setMessages(prev => prev.map(msg => ({ 
        ...msg, 
        read: true, 
        read_at: msg.read_at ?? new Date().toISOString() 
      })));
      
      // In production: await supabase.from('messages').update({ read: true, read_at: new Date().toISOString() }).eq('read', false);
    } catch (err) {
      console.error('Error marking all messages as read:', err);
    }
  };

  const unreadCount = messages.filter(msg => !msg.read).length;

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p className="text-center text-gray-600 mt-4">Loading messages...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex bg-white">
      {/* Messages List Sidebar */}
      <div className="w-1/3 border-r border-gray-200 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
              <MessageSquare className="w-6 h-6" />
              Messages
              {unreadCount > 0 && (
                <span className="bg-red-600 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                  {unreadCount}
                </span>
              )}
            </h1>
            <div className="flex items-center gap-2">
              <button
                onClick={() => setShowCompose(true)}
                className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                title="Compose new message"
              >
                <Plus className="w-5 h-5" />
              </button>
              {unreadCount > 0 && (
                <button
                  onClick={markAllAsRead}
                  className="p-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors"
                  title="Mark all as read"
                >
                  <CheckCircle className="w-5 h-5" />
                </button>
              )}
            </div>
          </div>

          {/* Search */}
          <div className="relative mb-4">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search messages..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Filters */}
          <div className="flex flex-wrap gap-2 mb-4">
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="text-sm border border-gray-300 rounded-lg px-2 py-1"
            >
              <option value="all">All Types</option>
              <option value="notification">Notifications</option>
              <option value="alert">Alerts</option>
              <option value="reminder">Reminders</option>
              <option value="system">System</option>
              <option value="user_message">Messages</option>
            </select>
            
            <select
              value={filterPriority}
              onChange={(e) => setFilterPriority(e.target.value)}
              className="text-sm border border-gray-300 rounded-lg px-2 py-1"
            >
              <option value="all">All Priorities</option>
              <option value="critical">Critical</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>
          </div>

          <label className="flex items-center gap-2 text-sm">
            <input
              type="checkbox"
              checked={showUnreadOnly}
              onChange={(e) => setShowUnreadOnly(e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            Unread only
          </label>
        </div>

        {/* Messages List */}
        <div className="flex-1 overflow-y-auto">
          {filteredMessages.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              {searchQuery || filterType !== 'all' || filterPriority !== 'all' || showUnreadOnly
                ? 'No messages match your filters'
                : 'No messages yet'
              }
            </div>
          ) : (
            filteredMessages.map((message) => {
              const MessageIcon = MESSAGE_TYPES[message.type].icon;
              return (
                <div
                  key={message.id}
                  onClick={() => {
                    setSelectedMessage(message);
                    if (!message.read) {
                      markAsRead(message.id);
                    }
                  }}
                  className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 transition-colors ${
                    selectedMessage?.id === message.id ? 'bg-blue-50 border-blue-200' : ''
                  } ${!message.read ? 'bg-blue-25' : ''}`}
                >
                  <div className="flex items-start gap-3">
                    <div className={`p-2 rounded-lg ${MESSAGE_TYPES[message.type].bg}`}>
                      <MessageIcon className={`w-4 h-4 ${MESSAGE_TYPES[message.type].color}`} />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <h3 className={`text-sm font-medium truncate ${!message.read ? 'text-gray-900' : 'text-gray-700'}`}>
                          {message.title}
                        </h3>
                        <div className="flex items-center gap-1 ml-2">
                          <span className={`text-xs ${PRIORITY_COLORS[message.priority]}`}>
                            {message.priority}
                          </span>
                          {!message.read && (
                            <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                          )}
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 truncate mb-1">
                        {message.content}
                      </p>
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>{message.sender_name}</span>
                        <span>{formatDistanceToNow(new Date(message.created_at), { addSuffix: true })}</span>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })
          )}
        </div>
      </div>

      {/* Message Detail */}
      <div className="flex-1 flex flex-col">
        {selectedMessage ? (
          <>
            {/* Message Header */}
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-4">
                  <div className={`p-3 rounded-lg ${MESSAGE_TYPES[selectedMessage.type].bg}`}>
                    {React.createElement(MESSAGE_TYPES[selectedMessage.type].icon, {
                      className: `w-6 h-6 ${MESSAGE_TYPES[selectedMessage.type].color}`
                    })}
                  </div>
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900 mb-2">
                      {selectedMessage.title}
                    </h2>
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <div className="flex items-center gap-1">
                        <User className="w-4 h-4" />
                        {selectedMessage.sender_name}
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        {format(new Date(selectedMessage.created_at), 'MMM d, yyyy h:mm a')}
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        selectedMessage.priority === 'critical' ? 'bg-red-100 text-red-800' :
                        selectedMessage.priority === 'high' ? 'bg-orange-100 text-orange-800' :
                        selectedMessage.priority === 'medium' ? 'bg-blue-100 text-blue-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {selectedMessage.priority} priority
                      </span>
                    </div>
                  </div>
                </div>
                <button
                  onClick={() => deleteMessage(selectedMessage.id)}
                  className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                  title="Delete message"
                >
                  <Trash2 className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* Message Content */}
            <div className="flex-1 p-6">
              <div className="prose max-w-none">
                <p className="text-gray-800 leading-relaxed">
                  {selectedMessage.content}
                </p>
              </div>

              {/* Related Entity Info */}
              {selectedMessage.related_entity_type && (
                <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                  <h3 className="text-sm font-medium text-gray-900 mb-2">Related Information</h3>
                  <div className="text-sm text-gray-600">
                    <p><strong>Type:</strong> {selectedMessage.related_entity_type}</p>
                    <p><strong>ID:</strong> {selectedMessage.related_entity_id}</p>
                    {selectedMessage.metadata && (
                      <div className="mt-2">
                        <strong>Details:</strong>
                        <pre className="text-xs mt-1 bg-gray-100 p-2 rounded overflow-x-auto">
                          {JSON.stringify(selectedMessage.metadata, null, 2)}
                        </pre>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center text-gray-500">
            <div className="text-center">
              <MessageSquare className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>Select a message to view details</p>
            </div>
          </div>
        )}
      </div>

      {/* Compose Message Modal */}
      {showCompose && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Compose Message</h3>
              <button
                onClick={() => setShowCompose(false)}
                className="p-2 text-gray-400 hover:text-gray-600 rounded-lg transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Title</label>
                <input
                  type="text"
                  value={newMessage.title}
                  onChange={(e) => setNewMessage(prev => ({ ...prev, title: e.target.value }))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Message title..."
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Type</label>
                  <select
                    value={newMessage.type}
                    onChange={(e) => setNewMessage(prev => ({ ...prev, type: e.target.value as Message['type'] }))}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="user_message">Message</option>
                    <option value="notification">Notification</option>
                    <option value="reminder">Reminder</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Priority</label>
                  <select
                    value={newMessage.priority}
                    onChange={(e) => setNewMessage(prev => ({ ...prev, priority: e.target.value as Message['priority'] }))}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                    <option value="critical">Critical</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Content</label>
                <textarea
                  value={newMessage.content}
                  onChange={(e) => setNewMessage(prev => ({ ...prev, content: e.target.value }))}
                  rows={4}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Type your message..."
                />
              </div>

              <div className="flex justify-end gap-3 pt-4">
                <button
                  onClick={() => setShowCompose(false)}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={sendMessage}
                  disabled={!newMessage.title.trim() || !newMessage.content.trim()}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
                >
                  <Send className="w-4 h-4" />
                  Send
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}