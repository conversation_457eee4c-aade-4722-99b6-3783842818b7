import { Product, Category, Supplier } from '../types';

export const categories: Category[] = [
  {
    id: '1',
    name: 'Finfish',
    subCategories: ['Sockeye Salmon', 'King Salmon', 'Black Cod', 'Halibut'],
    description: 'Premium quality fresh and frozen fish selections',
    image: 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?auto=format&fit=crop&q=80&w=400'
  },
  {
    id: '2',
    name: 'Shellfish',
    subCategories: ['Oysters', 'Mussels', 'Clams', 'Scallops'],
    description: 'Fresh shellfish sourced from pristine waters',
    image: 'https://images.unsplash.com/photo-1615141982883-c7ad0e69fd62?auto=format&fit=crop&q=80&w=400'
  },
  {
    id: '3',
    name: 'Specialty',
    subCategories: ['Miso Sablefish', 'Smoked Salmon', 'Caviar'],
    description: 'Specialty seafood products and preparations',
    image: 'https://images.unsplash.com/photo-1585545335512-1e43f40d4999?auto=format&fit=crop&q=80&w=400'
  }
];

export const products: Product[] = [
  {
    id: '1',
    name: 'Sockeye Salmon',
    category: 'Finfish',
    subCategory: 'Salmon',
    stock: 150,
    minStock: 50,
    price: 29.99,
    cost: 22.50,
    supplier: 'Ocean Fresh Ltd',
    expiryDate: '2024-03-25',
    image: 'https://images.unsplash.com/photo-1574781330855-d0db8cc6a79c?auto=format&fit=crop&q=80&w=400',
    storageTemp: '0-4°C',
    handlingInstructions: 'Keep refrigerated. Handle with clean, dry hands.',
    seasonalAvailability: ['Summer', 'Fall'],
    origin: 'Alaska',
    speciesDetails: {
      scientificName: 'Oncorhynchus nerka',
      habitat: 'North Pacific Ocean',
      sustainabilityRating: 'A'
    },
    marketPriceHistory: [
      { date: '2024-03-01', price: 28.99 },
      { date: '2024-03-05', price: 29.99 }
    ]
  },
  {
    id: '2',
    name: 'King Salmon',
    category: 'Finfish',
    subCategory: 'Salmon',
    stock: 75,
    minStock: 30,
    price: 39.99,
    cost: 31.75,
    supplier: 'Ocean Fresh Ltd',
    expiryDate: '2024-03-23',
    image: 'https://images.unsplash.com/photo-1599084993091-1cb5c0721cc6?auto=format&fit=crop&q=80&w=400',
    storageTemp: '0-4°C',
    handlingInstructions: 'Keep refrigerated. Best served fresh.',
    seasonalAvailability: ['Spring', 'Summer'],
    origin: 'Pacific Northwest',
    speciesDetails: {
      scientificName: 'Oncorhynchus tshawytscha',
      habitat: 'Pacific Ocean',
      sustainabilityRating: 'A'
    },
    marketPriceHistory: [
      { date: '2024-03-01', price: 38.99 },
      { date: '2024-03-05', price: 39.99 }
    ]
  },
  {
    id: '3',
    name: 'Black Cod',
    category: 'Finfish',
    subCategory: 'Cod',
    stock: 100,
    minStock: 40,
    price: 34.99,
    cost: 26.50,
    supplier: 'Sea Harvest Co',
    expiryDate: '2024-03-24',
    image: 'https://images.unsplash.com/photo-1578885136359-16c8bd4d3a8e?auto=format&fit=crop&q=80&w=400',
    storageTemp: '0-4°C',
    handlingInstructions: 'Keep refrigerated. Ideal for grilling or smoking.',
    seasonalAvailability: ['Year-round'],
    origin: 'Alaska',
    speciesDetails: {
      scientificName: 'Anoplopoma fimbria',
      habitat: 'North Pacific',
      sustainabilityRating: 'A'
    },
    marketPriceHistory: [
      { date: '2024-03-01', price: 33.99 },
      { date: '2024-03-05', price: 34.99 }
    ]
  },
  {
    id: '4',
    name: 'Miso Sablefish',
    category: 'Specialty',
    subCategory: 'Prepared',
    stock: 50,
    minStock: 20,
    price: 42.99,
    cost: 32.75,
    supplier: 'Sea Harvest Co',
    expiryDate: '2024-03-22',
    image: 'https://images.unsplash.com/photo-1599084993091-1cb5c0721cc6?auto=format&fit=crop&q=80&w=400',
    storageTemp: '0-4°C',
    handlingInstructions: 'Keep refrigerated. Pre-marinated in miso.',
    seasonalAvailability: ['Year-round'],
    origin: 'Japan',
    speciesDetails: {
      scientificName: 'Anoplopoma fimbria',
      habitat: 'North Pacific',
      sustainabilityRating: 'A'
    },
    marketPriceHistory: [
      { date: '2024-03-01', price: 41.99 },
      { date: '2024-03-05', price: 42.99 }
    ]
  }
];

export const suppliers: Supplier[] = [
  {
    id: '1',
    name: 'Ocean Fresh Ltd',
    contact: 'John Peterson',
    email: '<EMAIL>',
    phone: '******-0123',
    rating: 4.8,
    specialties: ['Salmon', 'Cod', 'Halibut'],
    certifications: ['MSC', 'ASC', 'ISO 22000']
  },
  {
    id: '2',
    name: 'Sea Harvest Co',
    contact: 'Mary Johnson',
    email: '<EMAIL>',
    phone: '******-0124',
    rating: 4.6,
    specialties: ['Black Cod', 'Specialty Products'],
    certifications: ['BAP', 'ISO 9001']
  }
];