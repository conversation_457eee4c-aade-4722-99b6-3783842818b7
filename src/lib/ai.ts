// Secure voice processing client - no API keys exposed
interface ProcessedVoiceData {
  product?: string;
  category?: string;
  subCategory?: string;
  quantity?: number;
  price?: number;
  vendor?: string;
  origin?: string;
  storageTemp?: string;
  handlingInstructions?: string;
  unit?: string;
  notes?: string;
  metadata?: {
    processing_method: string;
    original_text: string;
    confidence_score: number;
    model?: string;
    processing_time_ms?: number;
    seafood_database_match?: boolean;
    voice_correction_applied?: boolean;
    error_reason?: string;
  };
}

// Voice processing configuration
interface VoiceProcessingOptions {
  model?: 'gpt-3.5-turbo' | 'gpt-4';
  timeout?: number;
  retryAttempts?: number;
}

export async function processVoiceInput(
  transcript: string, 
  options: VoiceProcessingOptions = {}
): Promise<ProcessedVoiceData> {
  const {
    model = 'gpt-3.5-turbo',
    timeout = 10000,
    retryAttempts = 2
  } = options;

  let lastError: Error | null = null;

  for (let attempt = 0; attempt <= retryAttempts; attempt++) {
    try {
      // Call secure server-side endpoint
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const response = await fetch('/api/voice-process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transcript,
          options: { model }
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Voice processing failed: ${errorData.error || response.statusText}`);
      }

      const processedData = await response.json();
      
      // Validate response structure
      if (!processedData || typeof processedData !== 'object') {
        throw new Error('Invalid response format from voice processing');
      }

      return processedData;

    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Unknown error');
      console.error(`Voice processing attempt ${attempt + 1} failed:`, lastError);

      // If this was the last attempt or a non-retryable error, break
      if (attempt === retryAttempts || lastError.name === 'AbortError') {
        break;
      }

      // Exponential backoff for retries
      if (attempt < retryAttempts) {
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }
  }

  // Fallback to enhanced local processing
  console.warn('Falling back to local processing after API failures');
  return enhancedTextProcessing(transcript, lastError?.message);
}

// Enhanced local processing with comprehensive seafood database
function enhancedTextProcessing(text: string, errorReason?: string): ProcessedVoiceData {
  const data: ProcessedVoiceData = {};
  const lowerText = text.toLowerCase();

  // Comprehensive seafood database for local matching
  const SEAFOOD_DATABASE = {
    finfish: [
      'Atlantic Salmon', 'Pacific King Salmon', 'Coho Salmon', 'Sockeye Salmon', 'Chinook Salmon',
      'Bluefin Tuna', 'Yellowfin Tuna', 'Albacore Tuna', 'Skipjack Tuna',
      'Atlantic Cod', 'Pacific Cod', 'Lingcod', 'Black Cod', 'Sablefish',
      'Pacific Halibut', 'Atlantic Halibut', 'Dover Sole', 'Petrale Sole', 'English Sole',
      'Sea Bass', 'Striped Bass', 'Red Snapper', 'Grouper', 'Mahi Mahi', 'Swordfish', 'Rockfish'
    ],
    shellfish: [
      'Pacific Oysters', 'Eastern Oysters', 'Kumamoto Oysters', 'Belon Oysters', 'Blue Point Oysters',
      'Manila Clams', 'Littleneck Clams', 'Razor Clams', 'Geoduck Clams', 'Steamer Clams',
      'Blue Mussels', 'Mediterranean Mussels', 'Green Mussels',
      'Sea Scallops', 'Bay Scallops', 'Diver Scallops'
    ],
    crustaceans: [
      'Maine Lobster', 'Spiny Lobster', 'Rock Lobster', 'Langostino',
      'Dungeness Crab', 'King Crab', 'Snow Crab', 'Blue Crab', 'Jonah Crab', 'Stone Crab',
      'Tiger Prawns', 'Spot Prawns', 'White Shrimp', 'Pink Shrimp', 'Rock Shrimp'
    ],
    specialty: [
      'Caviar', 'Salmon Roe', 'Sea Urchin', 'Uni', 'Octopus', 'Squid', 'Cuttlefish', 'Abalone'
    ]
  };

  // Voice recognition corrections
  const VOICE_CORRECTIONS = {
    'dangerous grab': 'Dungeness Crab',
    'dangerous crab': 'Dungeness Crab', 
    'king grab': 'King Crab',
    'dover soul': 'Dover Sole',
    'petrel sole': 'Petrale Sole',
    'blue point': 'Blue Point Oysters'
  };

  // Apply voice corrections
  let correctedText = text;
  Object.entries(VOICE_CORRECTIONS).forEach(([wrong, right]) => {
    correctedText = correctedText.replace(new RegExp(wrong, 'gi'), right);
  });

  // Product and category detection with fuzzy matching
  let detectedProduct = null;
  let detectedCategory = null;
  let matchConfidence = 0;

  for (const [category, products] of Object.entries(SEAFOOD_DATABASE)) {
    for (const product of products) {
      // Exact match (highest confidence)
      if (correctedText.toLowerCase().includes(product.toLowerCase())) {
        detectedProduct = product;
        detectedCategory = category.charAt(0).toUpperCase() + category.slice(1);
        matchConfidence = 0.9;
        break;
      }
      
      // Fuzzy match for partial names
      const productWords = product.toLowerCase().split(' ');
      const textWords = correctedText.toLowerCase().split(' ');
      const matches = productWords.filter(word => textWords.includes(word));
      
      if (matches.length >= Math.ceil(productWords.length / 2) && matchConfidence < 0.7) {
        detectedProduct = product;
        detectedCategory = category.charAt(0).toUpperCase() + category.slice(1);
        matchConfidence = 0.7;
      }
    }
    if (matchConfidence >= 0.9) break;
  }

  if (detectedProduct) {
    data.product = detectedProduct;
    data.category = detectedCategory;
    data.subCategory = detectedProduct;
  }

  // Enhanced quantity extraction with decimal support
  const quantityPatterns = [
    /(\d+(?:\.\d+)?)\s*(kg|kilograms?|pounds?|lbs?|units?|pieces?|pcs|cases?)/gi,
    /quantity(?:\s+of)?\s+(\d+(?:\.\d+)?)/gi,
    /(\d+(?:\.\d+)?)\s*(?:units?|boxes?|cases?)/gi,
    /received\s+(\d+(?:\.\d+)?)/gi,
    /got\s+(\d+(?:\.\d+)?)/gi
  ];

  for (const pattern of quantityPatterns) {
    const match = pattern.exec(correctedText);
    if (match) {
      data.quantity = parseFloat(match[1]);
      if (match[2]) {
        const unitMap: { [key: string]: string } = {
          'kg': 'kg', 'kilogram': 'kg', 'kilograms': 'kg',
          'pound': 'lbs', 'pounds': 'lbs', 'lbs': 'lbs', 'lb': 'lbs',
          'piece': 'units', 'pieces': 'units', 'pcs': 'units', 'units': 'units',
          'case': 'cases', 'cases': 'cases'
        };
        data.unit = unitMap[match[2].toLowerCase()] || 'lbs';
      }
      break;
    }
  }

  // Enhanced price extraction
  const pricePatterns = [
    /\$?(\d+(?:\.\d{2})?)\s*(?:per\s+(?:kg|pound|lb|unit|case))?/gi,
    /(?:price|cost)(?:\s+of)?\s+\$?(\d+(?:\.\d{2})?)/gi,
    /at\s+\$?(\d+(?:\.\d{2})?)/gi,
    /for\s+\$?(\d+(?:\.\d{2})?)/gi
  ];

  for (const pattern of pricePatterns) {
    const match = pattern.exec(correctedText);
    if (match) {
      data.price = parseFloat(match[1]);
      break;
    }
  }

  // Enhanced vendor extraction
  const vendorPatterns = [
    /from\s+([a-zA-Z\s&.-]+?)(?:\s+(?:at|for|received|delivered|$))/gi,
    /vendor\s+([a-zA-Z\s&.-]+)/gi,
    /supplied\s+by\s+([a-zA-Z\s&.-]+)/gi,
    /delivered\s+by\s+([a-zA-Z\s&.-]+)/gi
  ];

  for (const pattern of vendorPatterns) {
    const match = pattern.exec(correctedText);
    if (match) {
      data.vendor = match[1].trim();
      break;
    }
  }

  // Calculate confidence score
  let confidence = 0.3; // Base score for local processing
  if (detectedProduct) confidence += matchConfidence * 0.3;
  if (data.quantity) confidence += 0.2;
  if (data.price) confidence += 0.15;
  if (data.vendor) confidence += 0.1;
  confidence = Math.min(confidence, 0.85); // Cap at 85% for local processing

  return {
    ...data,
    metadata: {
      processing_method: 'enhanced_local',
      original_text: text,
      confidence_score: confidence,
      seafood_database_match: !!detectedProduct,
      voice_correction_applied: Object.keys(VOICE_CORRECTIONS).some(wrong => 
        lowerText.includes(wrong)
      ),
      error_reason: errorReason,
      processing_time_ms: 0
    }
  };
}

// Legacy function for backward compatibility
function basicTextProcessing(text: string): ProcessedVoiceData {
  return enhancedTextProcessing(text);
}
