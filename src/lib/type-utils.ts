/**
 * Type utilities for enhanced type safety in the Seafood Manager application
 * Provides runtime validation, type guards, and utility types
 */

import { VendorInteraction, VendorMetrics, VendorCompliance, VendorPerformanceAlert } from '../types/schema';

// ===== RUNTIME TYPE VALIDATION =====

/**
 * Type guard for checking if a value is a non-null object
 */
export function isObject(value: unknown): value is Record<string, unknown> {
  return typeof value === 'object' && value !== null && !Array.isArray(value);
}

/**
 * Type guard for checking if a value is a non-empty string
 */
export function isNonEmptyString(value: unknown): value is string {
  return typeof value === 'string' && value.trim().length > 0;
}

/**
 * Type guard for checking if a value is a positive number
 */
export function isPositiveNumber(value: unknown): value is number {
  return typeof value === 'number' && !isNaN(value) && value > 0;
}

/**
 * Type guard for vendor interaction delivery status
 */
export function isValidDeliveryStatus(value: unknown): value is VendorInteraction['delivery_status'] {
  return typeof value === 'string' && ['pending', 'partial', 'complete', 'rejected'].includes(value);
}

/**
 * Type guard for letter grades
 */
export function isValidLetterGrade(value: unknown): value is 'A' | 'B' | 'C' | 'D' | 'F' {
  return typeof value === 'string' && ['A', 'B', 'C', 'D', 'F'].includes(value);
}

/**
 * Type guard for vendor interaction
 */
export function isValidVendorInteraction(obj: unknown): obj is VendorInteraction {
  if (!isObject(obj)) return false;
  
  return isNonEmptyString(obj.vendor_id) &&
         isNonEmptyString(obj.interaction_type) &&
         isValidDeliveryStatus(obj.delivery_status);
}

/**
 * Type guard for vendor metrics
 */
export function isValidVendorMetrics(obj: unknown): obj is VendorMetrics {
  if (!isObject(obj)) return false;
  
  return isNonEmptyString(obj.vendor_id) &&
         typeof obj.total_orders === 'number' &&
         typeof obj.completed_orders === 'number';
}

/**
 * Type guard for vendor compliance
 */
export function isValidVendorCompliance(obj: unknown): obj is VendorCompliance {
  if (!isObject(obj)) return false;
  
  return isNonEmptyString(obj.vendor_id) &&
         typeof obj.haccp_certified === 'boolean' &&
         typeof obj.traceability_complete === 'boolean';
}

/**
 * Type guard for performance alerts
 */
export function isValidPerformanceAlert(obj: unknown): obj is VendorPerformanceAlert {
  if (!isObject(obj)) return false;
  
  return isNonEmptyString(obj.vendor_id) &&
         isNonEmptyString(obj.alert_type) &&
         isNonEmptyString(obj.severity) &&
         isNonEmptyString(obj.title);
}

// ===== UTILITY TYPES =====

/**
 * Make specific properties of a type required
 */
export type RequireFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

/**
 * Make specific properties of a type optional
 */
export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/**
 * Extract only the primitive fields from a type (no objects or arrays)
 */
export type PrimitiveFields<T> = {
  [K in keyof T]: T[K] extends string | number | boolean | null | undefined ? T[K] : never;
};

/**
 * Create a type that represents the API response format
 */
export type ApiResponse<T> = {
  data: T | null;
  error: {
    message: string;
    code?: string;
  } | null;
};

/**
 * Create a type for paginated responses
 */
export type PaginatedResponse<T> = ApiResponse<T[]> & {
  pagination?: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };
};

/**
 * Create a type for form data (removing readonly/generated fields)
 */
export type FormData<T> = Omit<T, 'id' | 'created_at' | 'updated_at' | 'calculated_at'>;

// ===== ERROR HANDLING UTILITIES =====

/**
 * Custom error class for vendor API operations
 */
export class VendorAPIError extends Error {
  constructor(
    message: string,
    public readonly operation: string,
    public readonly vendorId?: string,
    public readonly originalError?: Error
  ) {
    super(message);
    this.name = 'VendorAPIError';
  }
}

/**
 * Custom error class for validation failures
 */
export class ValidationError extends Error {
  constructor(
    message: string,
    public readonly field: string,
    public readonly value: unknown
  ) {
    super(message);
    this.name = 'ValidationError';
  }
}

/**
 * Safe error message extraction with fallback
 */
export function extractErrorMessage(error: unknown, fallback = 'An unexpected error occurred'): string {
  if (error instanceof Error) {
    return error.message;
  }
  
  if (isObject(error) && typeof error.message === 'string') {
    return error.message;
  }
  
  return fallback;
}

/**
 * Creates a result type for operations that can fail
 */
export type Result<T, E = Error> = 
  | { success: true; data: T; error: null }
  | { success: false; data: null; error: E };

/**
 * Creates a successful result
 */
export function createSuccessResult<T>(data: T): Result<T, never> {
  return { success: true, data, error: null };
}

/**
 * Creates a failed result
 */
export function createErrorResult<E extends Error>(error: E): Result<never, E> {
  return { success: false, data: null, error };
}

// ===== DATA TRANSFORMATION UTILITIES =====

/**
 * Safely parse a date string
 */
export function parseDate(dateStr: unknown): Date | null {
  if (!isNonEmptyString(dateStr)) return null;
  
  const date = new Date(dateStr);
  return isNaN(date.getTime()) ? null : date;
}

/**
 * Format a number as currency
 */
export function formatCurrency(amount: number | null | undefined): string {
  if (typeof amount !== 'number') return '$0.00';
  
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
}

/**
 * Format a percentage with specified decimal places
 */
export function formatPercentage(value: number | null | undefined, decimals = 1): string {
  if (typeof value !== 'number') return 'N/A';
  
  return `${value.toFixed(decimals)}%`;
}

/**
 * Capitalize the first letter of a string
 */
export function capitalize(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

/**
 * Convert kebab-case or snake_case to Title Case
 */
export function toTitleCase(str: string): string {
  return str
    .replace(/[-_]/g, ' ')
    .split(' ')
    .map(word => capitalize(word))
    .join(' ');
}

/**
 * Deep clone an object (simple implementation)
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime()) as T;
  if (Array.isArray(obj)) return obj.map(item => deepClone(item)) as T;
  
  const cloned = {} as T;
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      cloned[key] = deepClone(obj[key]);
    }
  }
  
  return cloned;
}

// ===== ASYNC UTILITIES =====

/**
 * Creates a promise that resolves after a specified delay
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Retry an async operation with exponential backoff
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries = 3,
  baseDelay = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      if (attempt < maxRetries) {
        const delayMs = baseDelay * Math.pow(2, attempt);
        await delay(delayMs);
      }
    }
  }
  
  throw lastError!;
}

/**
 * Race a promise against a timeout
 */
export async function withTimeout<T>(
  promise: Promise<T>,
  timeoutMs: number,
  timeoutMessage = 'Operation timed out'
): Promise<T> {
  const timeout = new Promise<never>((_, reject) => {
    setTimeout(() => reject(new Error(timeoutMessage)), timeoutMs);
  });
  
  return Promise.race([promise, timeout]);
}