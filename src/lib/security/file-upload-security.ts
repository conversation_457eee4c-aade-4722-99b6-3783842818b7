// File upload security validation for CSV imports and other file operations
export interface FileValidationConfig {
  maxFileSize: number; // bytes
  allowedMimeTypes: string[];
  allowedExtensions: string[];
  scanForMalware: boolean;
  quarantineDirectory?: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  sanitizedFile?: File;
}

// Security configurations for different file types
export const FILE_SECURITY_CONFIGS: Record<string, FileValidationConfig> = {
  csv: {
    maxFileSize: 100 * 1024 * 1024, // 100MB for large inventory files
    allowedMimeTypes: ['text/csv', 'application/csv', 'text/plain'],
    allowedExtensions: ['.csv', '.txt'],
    scanForMalware: true
  },
  excel: {
    maxFileSize: 50 * 1024 * 1024, // 50MB
    allowedMimeTypes: [
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ],
    allowedExtensions: ['.xls', '.xlsx'],
    scanForMalware: true
  },
  images: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp'],
    allowedExtensions: ['.jpg', '.jpeg', '.png', '.webp'],
    scanForMalware: true
  },
  documents: {
    maxFileSize: 25 * 1024 * 1024, // 25MB
    allowedMimeTypes: ['application/pdf', 'text/plain'],
    allowedExtensions: ['.pdf', '.txt'],
    scanForMalware: true
  }
};

export class FileUploadSecurity {
  private static instance: FileUploadSecurity;

  static getInstance(): FileUploadSecurity {
    if (!FileUploadSecurity.instance) {
      FileUploadSecurity.instance = new FileUploadSecurity();
    }
    return FileUploadSecurity.instance;
  }

  // Main validation function
  async validateFile(file: File, configType: keyof typeof FILE_SECURITY_CONFIGS): Promise<ValidationResult> {
    const config = FILE_SECURITY_CONFIGS[configType];
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: []
    };

    // File size validation
    if (file.size > config.maxFileSize) {
      result.errors.push(`File size (${this.formatFileSize(file.size)}) exceeds maximum allowed size (${this.formatFileSize(config.maxFileSize)})`);
      result.isValid = false;
    }

    // MIME type validation
    if (!config.allowedMimeTypes.includes(file.type)) {
      result.errors.push(`File type '${file.type}' is not allowed. Allowed types: ${config.allowedMimeTypes.join(', ')}`);
      result.isValid = false;
    }

    // File extension validation
    const extension = this.getFileExtension(file.name);
    if (!config.allowedExtensions.includes(extension)) {
      result.errors.push(`File extension '${extension}' is not allowed. Allowed extensions: ${config.allowedExtensions.join(', ')}`);
      result.isValid = false;
    }

    // File name validation
    const nameValidation = this.validateFileName(file.name);
    if (!nameValidation.isValid) {
      result.errors.push(...nameValidation.errors);
      result.isValid = false;
    }

    // Content validation for CSV files
    if (configType === 'csv' && result.isValid) {
      const contentValidation = await this.validateCSVContent(file);
      result.errors.push(...contentValidation.errors);
      result.warnings.push(...contentValidation.warnings);
      if (contentValidation.errors.length > 0) {
        result.isValid = false;
      }
    }

    // Malware scanning (basic implementation)
    if (config.scanForMalware && result.isValid) {
      const malwareCheck = await this.scanForMalware(file);
      if (!malwareCheck.isClean) {
        result.errors.push('File failed malware scan');
        result.isValid = false;
      }
    }

    // File sanitization if valid
    if (result.isValid) {
      result.sanitizedFile = await this.sanitizeFile(file);
    }

    return result;
  }

  // Validate file name for security issues
  private validateFileName(fileName: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check for dangerous characters
    const dangerousChars = /[<>:"|?*\x00-\x1f]/;
    if (dangerousChars.test(fileName)) {
      errors.push('File name contains dangerous characters');
    }

    // Check for path traversal attempts
    if (fileName.includes('..') || fileName.includes('/') || fileName.includes('\\')) {
      errors.push('File name contains path traversal characters');
    }

    // Check for reserved names (Windows)
    const reservedNames = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'];
    const nameWithoutExt = fileName.split('.')[0].toUpperCase();
    if (reservedNames.includes(nameWithoutExt)) {
      errors.push('File name uses reserved system name');
    }

    // Check length
    if (fileName.length > 255) {
      errors.push('File name is too long (max 255 characters)');
    }

    if (fileName.length === 0) {
      errors.push('File name is empty');
    }

    return { isValid: errors.length === 0, errors };
  }

  // Validate CSV content for security issues
  private async validateCSVContent(file: File): Promise<{ errors: string[]; warnings: string[] }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Read first 1MB of file for analysis
      const chunk = file.slice(0, 1024 * 1024);
      const text = await chunk.text();

      // Check for suspicious content
      const suspiciousPatterns = [
        /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, // Script tags
        /javascript:/gi, // JavaScript protocol
        /data:text\/html/gi, // Data URLs
        /vbscript:/gi, // VBScript
        /on\w+\s*=/gi, // Event handlers
        /@import/gi, // CSS imports
        /document\./gi, // DOM access
        /window\./gi, // Window object access
        /eval\s*\(/gi, // Eval function
        /setTimeout|setInterval/gi // Timers
      ];

      for (const pattern of suspiciousPatterns) {
        if (pattern.test(text)) {
          errors.push('File contains potentially malicious content');
          break;
        }
      }

      // Check for binary content in text file
      const binaryPattern = /[\x00-\x08\x0E-\x1F\x7F-\xFF]/g;
      const binaryMatches = text.match(binaryPattern);
      if (binaryMatches && binaryMatches.length > text.length * 0.01) {
        errors.push('File appears to contain binary data');
      }

      // Validate CSV structure
      const lines = text.split('\n').slice(0, 100); // Check first 100 lines
      if (lines.length > 0) {
        const headerCols = lines[0].split(',').length;
        
        // Check for consistency in column count
        let inconsistentRows = 0;
        for (let i = 1; i < Math.min(lines.length, 20); i++) {
          if (lines[i].trim()) {
            const cols = lines[i].split(',').length;
            if (Math.abs(cols - headerCols) > 2) { // Allow some variance for quoted fields
              inconsistentRows++;
            }
          }
        }
        
        if (inconsistentRows > 5) {
          warnings.push('CSV file may have inconsistent column structure');
        }

        // Check for extremely large cells (potential DoS)
        for (const line of lines.slice(0, 10)) {
          for (const cell of line.split(',')) {
            if (cell.length > 10000) {
              warnings.push('CSV contains unusually large cell values');
              break;
            }
          }
        }
      }

    } catch (error) {
      errors.push('Failed to validate file content');
    }

    return { errors, warnings };
  }

  // Basic malware scanning (placeholder for integration with security services)
  private async scanForMalware(file: File): Promise<{ isClean: boolean; threats?: string[] }> {
    // This is a basic implementation
    // In production, you would integrate with services like:
    // - VirusTotal API
    // - Microsoft Defender ATP
    // - ClamAV
    // - AWS GuardDuty Malware Detection

    const suspiciousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.vbs', '.js'];
    const extension = this.getFileExtension(file.name);
    
    if (suspiciousExtensions.includes(extension)) {
      return { isClean: false, threats: ['Suspicious file extension'] };
    }

    // Check file signature/magic bytes
    try {
      const header = await file.slice(0, 512).arrayBuffer();
      const bytes = new Uint8Array(header);
      
      // Check for executable signatures
      const executableSignatures = [
        [0x4D, 0x5A], // PE/DOS executable
        [0x7F, 0x45, 0x4C, 0x46], // ELF executable
        [0xCA, 0xFE, 0xBA, 0xBE], // Mach-O binary
        [0xFE, 0xED, 0xFA, 0xCE], // Mach-O binary (reverse)
      ];

      for (const signature of executableSignatures) {
        if (this.matchesSignature(bytes, signature)) {
          return { isClean: false, threats: ['Executable file detected'] };
        }
      }

    } catch (error) {
      console.warn('Error checking file signature:', error);
    }

    return { isClean: true };
  }

  // File sanitization
  private async sanitizeFile(file: File): Promise<File> {
    // For text files, we can sanitize content
    if (file.type.startsWith('text/') || file.type === 'application/csv') {
      try {
        const text = await file.text();
        
        // Remove potentially dangerous content
        const sanitized = text
          .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
          .replace(/javascript:/gi, '') // Remove javascript: protocols
          .replace(/vbscript:/gi, '') // Remove vbscript: protocols
          .replace(/on\w+\s*=/gi, '') // Remove event handlers
          .replace(/[\x00-\x08\x0E-\x1F\x7F]/g, ''); // Remove control characters

        // Create new sanitized file
        const sanitizedBlob = new Blob([sanitized], { type: file.type });
        return new File([sanitizedBlob], file.name, { type: file.type, lastModified: file.lastModified });
      } catch (error) {
        console.warn('Error sanitizing file:', error);
      }
    }

    return file; // Return original if sanitization not applicable
  }

  // Helper methods
  private getFileExtension(fileName: string): string {
    const lastDot = fileName.lastIndexOf('.');
    return lastDot !== -1 ? fileName.substring(lastDot).toLowerCase() : '';
  }

  private formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${Math.round(size * 100) / 100} ${units[unitIndex]}`;
  }

  private matchesSignature(bytes: Uint8Array, signature: number[]): boolean {
    if (bytes.length < signature.length) return false;
    
    for (let i = 0; i < signature.length; i++) {
      if (bytes[i] !== signature[i]) return false;
    }
    
    return true;
  }

  // Public utility functions
  generateSecureFileName(originalName: string): string {
    // Remove dangerous characters and generate safe name
    const extension = this.getFileExtension(originalName);
    const baseName = originalName.replace(/[<>:"|?*\x00-\x1f]/g, '').replace(/\.[^.]*$/, '');
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    
    return `${baseName}_${timestamp}_${random}${extension}`;
  }

  // Validate multiple files (for batch uploads)
  async validateFiles(files: FileList, configType: keyof typeof FILE_SECURITY_CONFIGS): Promise<ValidationResult[]> {
    const results: ValidationResult[] = [];
    
    for (let i = 0; i < files.length; i++) {
      const result = await this.validateFile(files[i], configType);
      results.push(result);
    }
    
    return results;
  }
}

// Create and export singleton instance
export const fileUploadSecurity = FileUploadSecurity.getInstance();

// Export utility functions
export function validateCSVFile(file: File): Promise<ValidationResult> {
  return fileUploadSecurity.validateFile(file, 'csv');
}

export function validateImageFile(file: File): Promise<ValidationResult> {
  return fileUploadSecurity.validateFile(file, 'images');
}

export function validateDocumentFile(file: File): Promise<ValidationResult> {
  return fileUploadSecurity.validateFile(file, 'documents');
}