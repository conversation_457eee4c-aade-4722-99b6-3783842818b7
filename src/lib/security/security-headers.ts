// Security headers configuration for production deployment
export interface SecurityHeadersConfig {
  contentSecurityPolicy: string;
  xssProtection: string;
  contentTypeOptions: string;
  frameOptions: string;
  strictTransportSecurity: string;
  referrerPolicy: string;
  permissionsPolicy: string;
}

export const PRODUCTION_SECURITY_HEADERS: SecurityHeadersConfig = {
  // Content Security Policy - Strict but functional for seafood app
  contentSecurityPolicy: [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.stripe.com https://checkout.stripe.com", // Stripe for payments
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com", // Google Fonts
    "font-src 'self' https://fonts.gstatic.com data:",
    "img-src 'self' data: https: blob:", // Images from various sources including Supabase storage
    "media-src 'self' blob:", // Audio/video for voice processing
    "connect-src 'self' https://*.supabase.co wss://*.supabase.co https://api.openai.com", // API connections
    "frame-src 'self' https://js.stripe.com https://checkout.stripe.com", // Payment frames
    "worker-src 'self' blob:", // Web Workers for CSV processing
    "manifest-src 'self'",
    "base-uri 'self'",
    "form-action 'self'",
    "upgrade-insecure-requests"
  ].join("; "),

  // XSS Protection
  xssProtection: "1; mode=block",

  // Content Type Options
  contentTypeOptions: "nosniff",

  // Frame Options  
  frameOptions: "SAMEORIGIN",

  // HSTS for HTTPS enforcement (1 year)
  strictTransportSecurity: "max-age=31536000; includeSubDomains; preload",

  // Referrer Policy
  referrerPolicy: "strict-origin-when-cross-origin",

  // Permissions Policy (formerly Feature Policy)
  permissionsPolicy: [
    "camera=(), microphone=(self)", // Microphone for voice input
    "geolocation=()", 
    "payment=(self)", // For payment processing
    "usb=()",
    "magnetometer=()",
    "gyroscope=()",
    "accelerometer=()",
    "ambient-light-sensor=()",
    "autoplay=()",
    "encrypted-media=()",
    "fullscreen=(self)",
    "picture-in-picture=()"
  ].join(", ")
};

// Security headers for Vite development
export const DEVELOPMENT_SECURITY_HEADERS: SecurityHeadersConfig = {
  contentSecurityPolicy: [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'", // More permissive for dev
    "style-src 'self' 'unsafe-inline'",
    "font-src 'self' data:",
    "img-src 'self' data: https: blob:",
    "media-src 'self' blob:",
    "connect-src 'self' ws: wss: https: http://localhost:*",
    "frame-src 'self'",
    "worker-src 'self' blob:"
  ].join("; "),
  xssProtection: "1; mode=block",
  contentTypeOptions: "nosniff",
  frameOptions: "SAMEORIGIN",
  strictTransportSecurity: "", // Not needed for dev
  referrerPolicy: "strict-origin-when-cross-origin",
  permissionsPolicy: "microphone=(self)"
};

// Helper function to apply security headers
export function applySecurityHeaders(headers: Headers, isProd: boolean = false): Headers {
  const config = isProd ? PRODUCTION_SECURITY_HEADERS : DEVELOPMENT_SECURITY_HEADERS;
  
  headers.set('Content-Security-Policy', config.contentSecurityPolicy);
  headers.set('X-XSS-Protection', config.xssProtection);
  headers.set('X-Content-Type-Options', config.contentTypeOptions);
  headers.set('X-Frame-Options', config.frameOptions);
  headers.set('Referrer-Policy', config.referrerPolicy);
  headers.set('Permissions-Policy', config.permissionsPolicy);
  
  if (isProd && config.strictTransportSecurity) {
    headers.set('Strict-Transport-Security', config.strictTransportSecurity);
  }
  
  return headers;
}

// Vite plugin for applying security headers in development
export function securityHeadersPlugin() {
  return {
    name: 'security-headers',
    configureServer(server: any) {
      server.middlewares.use((req: any, res: any, next: any) => {
        const headers = new Headers();
        applySecurityHeaders(headers, false);
        
        headers.forEach((value, key) => {
          res.setHeader(key, value);
        });
        
        next();
      });
    }
  };
}

// Security headers for API routes
export function getAPISecurityHeaders(isProd: boolean = true): Record<string, string> {
  const headers = new Headers();
  applySecurityHeaders(headers, isProd);
  
  const result: Record<string, string> = {};
  headers.forEach((value, key) => {
    result[key] = value;
  });
  
  return result;
}