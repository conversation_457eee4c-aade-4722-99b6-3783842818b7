// Authentication bypass prevention and security hardening
import { supabase } from '../supabase';
import type { User, Session } from '@supabase/supabase-js';

export interface AuthState {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

export interface SecurityConfig {
  sessionTimeout: number; // minutes
  maxLoginAttempts: number;
  lockoutDuration: number; // minutes
  requireStrongPasswords: boolean;
  enable2FA: boolean;
}

export const DEFAULT_SECURITY_CONFIG: SecurityConfig = {
  sessionTimeout: 480, // 8 hours
  maxLoginAttempts: 5,
  lockoutDuration: 15,
  requireStrongPasswords: true,
  enable2FA: false // Can be enabled per tenant
};

// Authentication bypass prevention
export class AuthProtection {
  private static instance: AuthProtection;
  private loginAttempts: Map<string, { count: number; lastAttempt: Date; lockedUntil?: Date }> = new Map();
  private config: SecurityConfig = DEFAULT_SECURITY_CONFIG;

  static getInstance(): AuthProtection {
    if (!AuthProtection.instance) {
      AuthProtection.instance = new AuthProtection();
    }
    return AuthProtection.instance;
  }

  // Check if IP/email is currently locked out
  isLockedOut(identifier: string): boolean {
    const attempts = this.loginAttempts.get(identifier);
    if (!attempts?.lockedUntil) return false;
    
    if (new Date() > attempts.lockedUntil) {
      // Lockout expired, clear it
      attempts.lockedUntil = undefined;
      attempts.count = 0;
      return false;
    }
    
    return true;
  }

  // Record failed login attempt
  recordFailedAttempt(identifier: string): void {
    const now = new Date();
    const attempts = this.loginAttempts.get(identifier) || { count: 0, lastAttempt: now };
    
    attempts.count++;
    attempts.lastAttempt = now;
    
    if (attempts.count >= this.config.maxLoginAttempts) {
      attempts.lockedUntil = new Date(now.getTime() + this.config.lockoutDuration * 60000);
      console.warn(`Account locked: ${identifier} - too many failed attempts`);
    }
    
    this.loginAttempts.set(identifier, attempts);
  }

  // Clear failed attempts on successful login
  clearFailedAttempts(identifier: string): void {
    this.loginAttempts.delete(identifier);
  }

  // Get remaining lockout time in minutes
  getLockoutTimeRemaining(identifier: string): number {
    const attempts = this.loginAttempts.get(identifier);
    if (!attempts?.lockedUntil) return 0;
    
    const remaining = attempts.lockedUntil.getTime() - new Date().getTime();
    return Math.max(0, Math.ceil(remaining / 60000));
  }
}

// Secure authentication wrapper
export async function secureSignIn(email: string, password: string): Promise<{
  success: boolean;
  error?: string;
  lockoutTimeRemaining?: number;
}> {
  const authProtection = AuthProtection.getInstance();
  const identifier = email.toLowerCase();

  // Check if account is locked
  if (authProtection.isLockedOut(identifier)) {
    const timeRemaining = authProtection.getLockoutTimeRemaining(identifier);
    return {
      success: false,
      error: `Account temporarily locked. Try again in ${timeRemaining} minutes.`,
      lockoutTimeRemaining: timeRemaining
    };
  }

  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (error) {
      // Record failed attempt
      authProtection.recordFailedAttempt(identifier);
      
      // Check if this caused a lockout
      if (authProtection.isLockedOut(identifier)) {
        const timeRemaining = authProtection.getLockoutTimeRemaining(identifier);
        return {
          success: false,
          error: `Too many failed attempts. Account locked for ${timeRemaining} minutes.`,
          lockoutTimeRemaining: timeRemaining
        };
      }

      return {
        success: false,
        error: error.message
      };
    }

    // Clear failed attempts on successful login
    authProtection.clearFailedAttempts(identifier);

    return { success: true };

  } catch (error) {
    console.error('Authentication error:', error);
    return {
      success: false,
      error: 'Authentication service unavailable. Please try again.'
    };
  }
}

// Session validation and timeout handling
export class SessionManager {
  private sessionCheckInterval: NodeJS.Timeout | null = null;
  private config: SecurityConfig = DEFAULT_SECURITY_CONFIG;

  startSessionMonitoring(): void {
    // Check session every 5 minutes
    this.sessionCheckInterval = setInterval(async () => {
      await this.validateSession();
    }, 5 * 60 * 1000);
  }

  stopSessionMonitoring(): void {
    if (this.sessionCheckInterval) {
      clearInterval(this.sessionCheckInterval);
      this.sessionCheckInterval = null;
    }
  }

  async validateSession(): Promise<boolean> {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error || !session) {
        console.log('Invalid session detected, redirecting to login');
        await this.forceSignOut();
        return false;
      }

      // Check session timeout
      const sessionAge = Date.now() - new Date(session.created_at).getTime();
      const maxAge = this.config.sessionTimeout * 60 * 1000;
      
      if (sessionAge > maxAge) {
        console.log('Session expired due to timeout');
        await this.forceSignOut();
        return false;
      }

      return true;
    } catch (error) {
      console.error('Session validation error:', error);
      await this.forceSignOut();
      return false;
    }
  }

  async forceSignOut(): Promise<void> {
    try {
      await supabase.auth.signOut();
      // Redirect to login page
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }
    } catch (error) {
      console.error('Error during forced sign out:', error);
    }
  }

  async refreshSession(): Promise<boolean> {
    try {
      const { data, error } = await supabase.auth.refreshSession();
      return !error && !!data.session;
    } catch (error) {
      console.error('Session refresh error:', error);
      return false;
    }
  }
}

// Password strength validation
export function validatePasswordStrength(password: string): {
  isValid: boolean;
  score: number;
  feedback: string[];
} {
  const feedback: string[] = [];
  let score = 0;

  if (password.length < 8) {
    feedback.push('Password must be at least 8 characters long');
  } else if (password.length >= 12) {
    score += 2;
  } else {
    score += 1;
  }

  if (!/[a-z]/.test(password)) {
    feedback.push('Password must contain lowercase letters');
  } else {
    score += 1;
  }

  if (!/[A-Z]/.test(password)) {
    feedback.push('Password must contain uppercase letters');
  } else {
    score += 1;
  }

  if (!/\d/.test(password)) {
    feedback.push('Password must contain numbers');
  } else {
    score += 1;
  }

  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    feedback.push('Password must contain special characters');
  } else {
    score += 2;
  }

  // Check for common patterns
  const commonPatterns = [
    /(.)\1{2,}/, // Repeated characters
    /123456|abcdef|qwerty/i, // Common sequences
    /password|admin|user/i // Common words
  ];

  for (const pattern of commonPatterns) {
    if (pattern.test(password)) {
      feedback.push('Password contains common patterns');
      score = Math.max(0, score - 2);
      break;
    }
  }

  return {
    isValid: feedback.length === 0 && score >= 5,
    score: Math.min(score, 5),
    feedback
  };
}

// Input sanitization for XSS prevention
export function sanitizeUserInput(input: string): string {
  if (typeof input !== 'string') return '';
  
  return input
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
}

// CSRF token generation and validation
export function generateCSRFToken(): string {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
}

export function setCSRFToken(): string {
  const token = generateCSRFToken();
  if (typeof document !== 'undefined') {
    const meta = document.createElement('meta');
    meta.name = 'csrf-token';
    meta.content = token;
    document.head.appendChild(meta);
  }
  return token;
}

export function getCSRFToken(): string | null {
  if (typeof document !== 'undefined') {
    const meta = document.querySelector('meta[name="csrf-token"]');
    return meta?.getAttribute('content') || null;
  }
  return null;
}

// Export singleton instances
export const authProtection = AuthProtection.getInstance();
export const sessionManager = new SessionManager();