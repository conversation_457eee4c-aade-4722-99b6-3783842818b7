// GDPR Compliance Framework for Seafood Manager
import { supabase } from '../supabase';

export interface DataProcessingPurpose {
  id: string;
  name: string;
  description: string;
  legalBasis: 'consent' | 'contract' | 'legal_obligation' | 'vital_interests' | 'public_task' | 'legitimate_interests';
  dataTypes: string[];
  retentionPeriod: number; // days
  isRequired: boolean;
}

export interface ConsentRecord {
  user_id: string;
  purpose_id: string;
  granted: boolean;
  timestamp: Date;
  ip_address?: string;
  user_agent?: string;
  method: 'explicit' | 'implicit' | 'pre_checked' | 'opt_out';
}

export interface DataSubjectRequest {
  id: string;
  user_id: string;
  request_type: 'access' | 'rectification' | 'erasure' | 'portability' | 'restriction' | 'objection';
  status: 'pending' | 'in_progress' | 'completed' | 'rejected';
  submitted_at: Date;
  completed_at?: Date;
  notes?: string;
}

// Standard data processing purposes for seafood industry
export const SEAFOOD_DATA_PURPOSES: DataProcessingPurpose[] = [
  {
    id: 'inventory_management',
    name: 'Inventory Management',
    description: 'Processing seafood inventory data for business operations',
    legalBasis: 'contract',
    dataTypes: ['product_data', 'quantity_data', 'pricing_data', 'vendor_data'],
    retentionPeriod: 2555, // 7 years for tax/audit purposes
    isRequired: true
  },
  {
    id: 'haccp_compliance',
    name: 'HACCP Compliance',
    description: 'Food safety monitoring and regulatory compliance',
    legalBasis: 'legal_obligation',
    dataTypes: ['temperature_data', 'batch_data', 'quality_data', 'audit_logs'],
    retentionPeriod: 2555, // 7 years regulatory requirement
    isRequired: true
  },
  {
    id: 'traceability',
    name: 'Supply Chain Traceability',
    description: 'Product traceability for food safety and recalls',
    legalBasis: 'legal_obligation',
    dataTypes: ['supplier_data', 'origin_data', 'transport_data', 'customer_data'],
    retentionPeriod: 1825, // 5 years for traceability
    isRequired: true
  },
  {
    id: 'analytics',
    name: 'Business Analytics',
    description: 'Business intelligence and performance optimization',
    legalBasis: 'legitimate_interests',
    dataTypes: ['usage_data', 'performance_metrics', 'trend_data'],
    retentionPeriod: 1095, // 3 years
    isRequired: false
  },
  {
    id: 'marketing',
    name: 'Marketing Communications',
    description: 'Product updates and promotional communications',
    legalBasis: 'consent',
    dataTypes: ['contact_data', 'preference_data', 'engagement_data'],
    retentionPeriod: 1095, // 3 years or until consent withdrawn
    isRequired: false
  }
];

export class GDPRComplianceManager {
  private static instance: GDPRComplianceManager;

  static getInstance(): GDPRComplianceManager {
    if (!GDPRComplianceManager.instance) {
      GDPRComplianceManager.instance = new GDPRComplianceManager();
    }
    return GDPRComplianceManager.instance;
  }

  // Record user consent
  async recordConsent(
    userId: string, 
    purposeId: string, 
    granted: boolean, 
    method: ConsentRecord['method'] = 'explicit'
  ): Promise<void> {
    const consentRecord: ConsentRecord = {
      user_id: userId,
      purpose_id: purposeId,
      granted,
      timestamp: new Date(),
      method,
      ip_address: await this.getClientIP(),
      user_agent: navigator.userAgent
    };

    const { error } = await supabase
      .from('gdpr_consent_records')
      .insert(consentRecord);

    if (error) {
      console.error('Failed to record consent:', error);
      throw new Error('Failed to record consent');
    }
  }

  // Get current consent status for user
  async getConsentStatus(userId: string): Promise<Map<string, boolean>> {
    const { data, error } = await supabase
      .from('gdpr_consent_records')
      .select('purpose_id, granted, timestamp')
      .eq('user_id', userId)
      .order('timestamp', { ascending: false });

    if (error) {
      console.error('Failed to get consent status:', error);
      return new Map();
    }

    // Get latest consent for each purpose
    const consentMap = new Map<string, boolean>();
    const processedPurposes = new Set<string>();

    for (const record of data) {
      if (!processedPurposes.has(record.purpose_id)) {
        consentMap.set(record.purpose_id, record.granted);
        processedPurposes.add(record.purpose_id);
      }
    }

    return consentMap;
  }

  // Check if user has given required consents
  async hasRequiredConsents(userId: string): Promise<boolean> {
    const consentStatus = await this.getConsentStatus(userId);
    
    for (const purpose of SEAFOOD_DATA_PURPOSES) {
      if (purpose.isRequired && !consentStatus.get(purpose.id)) {
        return false;
      }
    }
    
    return true;
  }

  // Submit data subject request
  async submitDataSubjectRequest(
    userId: string, 
    requestType: DataSubjectRequest['request_type'],
    notes?: string
  ): Promise<string> {
    const request: Omit<DataSubjectRequest, 'id'> = {
      user_id: userId,
      request_type: requestType,
      status: 'pending',
      submitted_at: new Date(),
      notes
    };

    const { data, error } = await supabase
      .from('gdpr_data_requests')
      .insert(request)
      .select('id')
      .single();

    if (error) {
      console.error('Failed to submit data subject request:', error);
      throw new Error('Failed to submit request');
    }

    // Notify administrators
    await this.notifyAdministrators(requestType, userId);
    
    return data.id;
  }

  // Export user data (Right to Data Portability)
  async exportUserData(userId: string): Promise<any> {
    const userData: any = {};

    // Get all user tables and export data
    const tables = [
      'profiles',
      'inventory_events', 
      'products',
      'vendors',
      'customers',
      'haccp_logs',
      'traceability_events'
    ];

    for (const table of tables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('*')
          .eq('created_by', userId);

        if (!error && data) {
          userData[table] = data;
        }
      } catch (error) {
        console.warn(`Failed to export data from ${table}:`, error);
      }
    }

    // Add consent records
    const { data: consentData } = await supabase
      .from('gdpr_consent_records')
      .select('*')
      .eq('user_id', userId);

    userData.consent_records = consentData;

    return userData;
  }

  // Anonymize or delete user data (Right to Erasure)
  async eraseUserData(userId: string, anonymize: boolean = true): Promise<void> {
    if (anonymize) {
      // Anonymize data while preserving business records
      await this.anonymizeUserData(userId);
    } else {
      // Complete deletion (only if legally permissible)
      await this.deleteUserData(userId);
    }
  }

  private async anonymizeUserData(userId: string): Promise<void> {
    const anonymousId = `anonymous_${Date.now()}`;
    
    // Update user profile
    await supabase
      .from('profiles')
      .update({
        email: `${anonymousId}@anonymized.local`,
        first_name: 'Anonymous',
        last_name: 'User',
        phone: null,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    // Anonymize other personal data while keeping business records
    const businessTables = ['inventory_events', 'haccp_logs', 'traceability_events'];
    
    for (const table of businessTables) {
      await supabase
        .from(table)
        .update({ 
          notes: null,
          metadata: supabase.rpc('anonymize_metadata', { user_id: userId })
        })
        .eq('created_by', userId);
    }
  }

  private async deleteUserData(userId: string): Promise<void> {
    // Check if user has active business records that must be retained
    const retentionCheck = await this.checkRetentionRequirements(userId);
    
    if (retentionCheck.hasActiveRecords) {
      throw new Error('Cannot delete data: active business records require retention for compliance');
    }

    // Delete user data from all tables
    const tables = [
      'gdpr_consent_records',
      'gdpr_data_requests', 
      'inventory_events',
      'products',
      'vendors',
      'customers',
      'haccp_logs',
      'traceability_events',
      'profiles'
    ];

    for (const table of tables) {
      await supabase
        .from(table)
        .delete()
        .eq(table === 'profiles' ? 'id' : 'created_by', userId);
    }
  }

  private async checkRetentionRequirements(userId: string): Promise<{
    hasActiveRecords: boolean;
    reasons: string[];
  }> {
    const reasons: string[] = [];
    let hasActiveRecords = false;

    // Check for recent HACCP records (must retain for 7 years)
    const sevenYearsAgo = new Date();
    sevenYearsAgo.setFullYear(sevenYearsAgo.getFullYear() - 7);

    const { data: haccpRecords } = await supabase
      .from('haccp_logs')
      .select('id')
      .eq('created_by', userId)
      .gte('created_at', sevenYearsAgo.toISOString())
      .limit(1);

    if (haccpRecords && haccpRecords.length > 0) {
      hasActiveRecords = true;
      reasons.push('HACCP compliance records must be retained for 7 years');
    }

    // Check for traceability records (must retain for 5 years)
    const fiveYearsAgo = new Date();
    fiveYearsAgo.setFullYear(fiveYearsAgo.getFullYear() - 5);

    const { data: traceabilityRecords } = await supabase
      .from('traceability_events')
      .select('id')
      .eq('created_by', userId)
      .gte('created_at', fiveYearsAgo.toISOString())
      .limit(1);

    if (traceabilityRecords && traceabilityRecords.length > 0) {
      hasActiveRecords = true;
      reasons.push('Traceability records must be retained for 5 years');
    }

    return { hasActiveRecords, reasons };
  }

  private async getClientIP(): Promise<string | undefined> {
    try {
      const response = await fetch('https://api.ipify.org?format=json');
      const data = await response.json();
      return data.ip;
    } catch (error) {
      console.warn('Failed to get client IP:', error);
      return undefined;
    }
  }

  private async notifyAdministrators(requestType: string, userId: string): Promise<void> {
    // Implementation would depend on notification system
    console.log(`Data subject request submitted: ${requestType} for user ${userId}`);
    // Could integrate with email, Slack, or other notification systems
  }

  // Generate privacy notice text
  generatePrivacyNotice(): string {
    return `
# Privacy Notice - Pacific Cloud Seafoods Manager

## Data Controller
Pacific Cloud Seafoods is the data controller for your personal data processed through this application.

## Data Processing Purposes
We process your personal data for the following purposes:

${SEAFOOD_DATA_PURPOSES.map(purpose => `
### ${purpose.name}
- **Purpose**: ${purpose.description}
- **Legal Basis**: ${purpose.legalBasis.replace('_', ' ')}
- **Data Types**: ${purpose.dataTypes.join(', ')}
- **Retention Period**: ${Math.floor(purpose.retentionPeriod / 365)} years
- **Required**: ${purpose.isRequired ? 'Yes' : 'No'}
`).join('')}

## Your Rights
Under GDPR, you have the following rights:
- Right to access your personal data
- Right to rectification of inaccurate data
- Right to erasure ("right to be forgotten")
- Right to data portability
- Right to restriction of processing
- Right to object to processing
- Rights related to automated decision-making

## Contact
For privacy-related inquiries or to exercise your rights, please contact our Data Protection Officer.

Last updated: ${new Date().toISOString().split('T')[0]}
    `.trim();
  }
}

// Cookie consent management
export interface CookieCategory {
  id: string;
  name: string;
  description: string;
  required: boolean;
  cookies: string[];
}

export const COOKIE_CATEGORIES: CookieCategory[] = [
  {
    id: 'essential',
    name: 'Essential Cookies',
    description: 'Required for basic website functionality and security',
    required: true,
    cookies: ['session_token', 'csrf_token', 'auth_state']
  },
  {
    id: 'functional',
    name: 'Functional Cookies',
    description: 'Remember your preferences and improve your experience',
    required: false,
    cookies: ['user_preferences', 'ui_settings', 'dashboard_layout']
  },
  {
    id: 'analytics',
    name: 'Analytics Cookies',
    description: 'Help us understand how you use the application',
    required: false,
    cookies: ['analytics_id', 'session_tracking', 'performance_metrics']
  }
];

export class CookieConsentManager {
  private consentKey = 'gdpr_cookie_consent';

  getConsent(): Record<string, boolean> {
    try {
      const stored = localStorage.getItem(this.consentKey);
      return stored ? JSON.parse(stored) : {};
    } catch {
      return {};
    }
  }

  setConsent(consents: Record<string, boolean>): void {
    localStorage.setItem(this.consentKey, JSON.stringify({
      ...consents,
      timestamp: new Date().toISOString()
    }));
  }

  hasValidConsent(): boolean {
    const consent = this.getConsent();
    return Object.keys(consent).length > 0 && !!consent.timestamp;
  }

  clearNonConsentedCookies(consents: Record<string, boolean>): void {
    for (const category of COOKIE_CATEGORIES) {
      if (!category.required && !consents[category.id]) {
        // Clear cookies for non-consented categories
        for (const cookieName of category.cookies) {
          document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`;
        }
      }
    }
  }
}

// Export singleton instances
export const gdprManager = GDPRComplianceManager.getInstance();
export const cookieManager = new CookieConsentManager();