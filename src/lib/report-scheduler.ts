/**
 * Report Scheduler Service
 * 
 * Handles automated scheduling and generation of temperature reports
 * with configurable frequency and email delivery.
 */

import { getEmailService } from './email-service';
import { tempStickService } from './tempstick-service';
import { supabase } from './supabase';
import type {
  EmailReportConfig,
  TemperatureReportData,
  TemperatureReportParams
} from '../types/tempstick';

interface ScheduledReport {
  id: string;
  name: string;
  config: EmailReportConfig;
  lastRun?: Date;
  nextRun: Date;
  enabled: boolean;
  isRunning: boolean;
}

interface ReportJob {
  id: string;
  reportId: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startTime: Date;
  endTime?: Date;
  error?: string;
  recipientCount: number;
}

/**
 * Automated Report Scheduling Service
 */
export class ReportScheduler {
  private static instance: ReportScheduler;
  private scheduledReports: Map<string, ScheduledReport> = new Map();
  private reportJobs: ReportJob[] = [];
  private timers: Map<string, NodeJS.Timeout> = new Map();

  static getInstance(): ReportScheduler {
    if (!ReportScheduler.instance) {
      ReportScheduler.instance = new ReportScheduler();
    }
    return ReportScheduler.instance;
  }

  /**
   * Initialize report scheduler
   */
  initialize(): void {
    console.log('📊 Initializing report scheduler...');
    
    // Load saved report configurations
    this.loadScheduledReports();
    
    // Start scheduling system
    this.startScheduler();
  }

  /**
   * Add a scheduled report
   */
  scheduleReport(config: EmailReportConfig): string {
    const reportId = `report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const nextRun = this.calculateNextRun(config.frequency, config.timeOfDay);
    
    const scheduledReport: ScheduledReport = {
      id: reportId,
      name: `${config.frequency} Temperature Report`,
      config,
      nextRun,
      enabled: true,
      isRunning: false
    };

    this.scheduledReports.set(reportId, scheduledReport);
    this.scheduleReportExecution(scheduledReport);
    this.saveScheduledReports();

    console.log(`📅 Scheduled ${config.frequency} report: ${reportId}, next run: ${nextRun.toLocaleString()}`);
    return reportId;
  }

  /**
   * Update a scheduled report
   */
  updateScheduledReport(reportId: string, config: Partial<EmailReportConfig>): void {
    const report = this.scheduledReports.get(reportId);
    if (!report) {
      throw new Error(`Report not found: ${reportId}`);
    }

    // Update configuration
    report.config = { ...report.config, ...config };
    
    // Recalculate next run if frequency or time changed
    if (config.frequency || config.timeOfDay) {
      report.nextRun = this.calculateNextRun(report.config.frequency, report.config.timeOfDay);
      
      // Reschedule execution
      this.clearTimer(reportId);
      this.scheduleReportExecution(report);
    }

    this.saveScheduledReports();
    console.log(`📊 Updated scheduled report: ${reportId}`);
  }

  /**
   * Enable/disable a scheduled report
   */
  setReportEnabled(reportId: string, enabled: boolean): void {
    const report = this.scheduledReports.get(reportId);
    if (!report) {
      throw new Error(`Report not found: ${reportId}`);
    }

    report.enabled = enabled;
    
    if (enabled) {
      this.scheduleReportExecution(report);
    } else {
      this.clearTimer(reportId);
    }

    this.saveScheduledReports();
    console.log(`📊 ${enabled ? 'Enabled' : 'Disabled'} scheduled report: ${reportId}`);
  }

  /**
   * Remove a scheduled report
   */
  removeScheduledReport(reportId: string): void {
    const report = this.scheduledReports.get(reportId);
    if (!report) {
      throw new Error(`Report not found: ${reportId}`);
    }

    this.clearTimer(reportId);
    this.scheduledReports.delete(reportId);
    this.saveScheduledReports();

    console.log(`🗑️ Removed scheduled report: ${reportId}`);
  }

  /**
   * Trigger a report manually
   */
  async triggerReport(reportId: string): Promise<void> {
    const report = this.scheduledReports.get(reportId);
    if (!report) {
      throw new Error(`Report not found: ${reportId}`);
    }

    await this.executeReport(report);
  }

  /**
   * Calculate next run time based on frequency and time of day
   */
  private calculateNextRun(frequency: 'daily' | 'weekly' | 'monthly', timeOfDay: string): Date {
    const now = new Date();
    const [hours, minutes] = timeOfDay.split(':').map(Number);
    
    const nextRun = new Date(now);
    nextRun.setHours(hours, minutes, 0, 0);

    switch (frequency) {
      case 'daily':
        // If time has passed today, schedule for tomorrow
        if (nextRun <= now) {
          nextRun.setDate(nextRun.getDate() + 1);
        }
        break;
        
      case 'weekly':
        // Schedule for next Monday at the specified time
        const daysUntilMonday = (8 - nextRun.getDay()) % 7 || 7;
        if (nextRun.getDay() === 1 && nextRun > now) {
          // It's Monday and time hasn't passed yet
        } else {
          nextRun.setDate(nextRun.getDate() + daysUntilMonday);
        }
        break;
        
      case 'monthly':
        // Schedule for first day of next month
        nextRun.setMonth(nextRun.getMonth() + 1, 1);
        break;
    }

    return nextRun;
  }

  /**
   * Schedule report execution
   */
  private scheduleReportExecution(report: ScheduledReport): void {
    if (!report.enabled || report.isRunning) return;

    const timeUntilRun = report.nextRun.getTime() - Date.now();
    
    if (timeUntilRun <= 0) {
      // Should run immediately
      this.executeReport(report);
      return;
    }

    const timer = setTimeout(async () => {
      await this.executeReport(report);
    }, Math.min(timeUntilRun, 2147483647)); // Max 32-bit signed integer

    this.timers.set(report.id, timer);
  }

  /**
   * Execute a scheduled report
   */
  private async executeReport(report: ScheduledReport): Promise<void> {
    if (report.isRunning) {
      console.log(`📊 Report already running: ${report.id}`);
      return;
    }

    const job: ReportJob = {
      id: `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      reportId: report.id,
      status: 'running',
      startTime: new Date(),
      recipientCount: report.config.recipients.length
    };

    this.reportJobs.push(job);
    report.isRunning = true;

    try {
      console.log(`📊 Executing report: ${report.name}`);
      
      // Generate report data
      const reportData = await this.generateReportData(report.config);
      
      // Check if we should skip sending (alerts only mode)
      if (report.config.alertsOnly && reportData.summary.totalAlerts === 0) {
        console.log(`📊 Skipping report (no alerts): ${report.name}`);
        job.status = 'completed';
        job.endTime = new Date();
        return;
      }

      // Send report via email
      const emailService = getEmailService();
      await emailService.sendScheduledReport(reportData, report.config);

      // Update report status
      report.lastRun = new Date();
      report.nextRun = this.calculateNextRun(report.config.frequency, report.config.timeOfDay);
      
      // Schedule next execution
      this.scheduleReportExecution(report);

      job.status = 'completed';
      job.endTime = new Date();
      
      console.log(`✅ Report completed: ${report.name}, next run: ${report.nextRun.toLocaleString()}`);

    } catch (error) {
      job.status = 'failed';
      job.endTime = new Date();
      job.error = error instanceof Error ? error.message : 'Unknown error';
      
      console.error(`❌ Report failed: ${report.name}`, error);
      
      // Retry in 1 hour for failed reports
      report.nextRun = new Date(Date.now() + 60 * 60 * 1000);
      this.scheduleReportExecution(report);
      
    } finally {
      report.isRunning = false;
      this.saveScheduledReports();
    }
  }

  /**
   * Generate report data for the specified time period
   */
  private async generateReportData(config: EmailReportConfig): Promise<TemperatureReportData> {
    const endDate = new Date();
    const startDate = new Date();
    
    // Calculate date range based on frequency
    switch (config.frequency) {
      case 'daily':
        startDate.setDate(endDate.getDate() - 1);
        break;
      case 'weekly':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case 'monthly':
        startDate.setMonth(endDate.getMonth() - 1);
        break;
    }

    // Get temperature readings for the period
    const readings = await tempStickService.getReadingsForDateRange(
      startDate,
      endDate,
      config.sensorIds.length > 0 ? config.sensorIds : undefined
    );

    // Get alerts for the period
    const { data: alerts } = await supabase
      .from('temperature_alerts')
      .select(`
        *,
        sensors (
          name,
          location,
          storage_area_id
        )
      `)
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString())
      .order('created_at', { ascending: false });

    // Get sensors data
    let sensorQuery = supabase
      .from('sensors')
      .select(`
        *
          name,
          area_type,
          haccp_control_point
        )
      `);

    if (config.sensorIds.length > 0) {
      sensorQuery = sensorQuery.in('id', config.sensorIds);
    }

    const { data: sensors } = await sensorQuery;

    // Process data into report format
    const sensorData = (sensors || []).map(sensor => {
      const sensorReadings = readings.filter(r => r.sensor_id === sensor.id);
      const sensorAlerts = (alerts || []).filter(a => a.sensor_id === sensor.id);
      
      const temperatures = sensorReadings.map(r => r.temperature);
      const avgTemp = temperatures.length > 0 
        ? temperatures.reduce((sum, t) => sum + t, 0) / temperatures.length
        : 0;
      const minTemp = temperatures.length > 0 ? Math.min(...temperatures) : 0;
      const maxTemp = temperatures.length > 0 ? Math.max(...temperatures) : 0;

      return {
        sensor,
        readings: sensorReadings,
        alerts: sensorAlerts,
        statistics: {
          avgTemp,
          minTemp,
          maxTemp,
          readingsCount: sensorReadings.length,
          alertsCount: sensorAlerts.length
        }
      };
    });

    // Calculate HACCP compliance
    const haccpCompliance = sensorData
      .filter(s => s.sensor.storage_areas?.haccp_control_point)
      .map(sensorInfo => {
        const violations = sensorInfo.alerts.filter(a => a.alert_type === 'haccp_violation');
        const criticalViolations = violations.filter(v => v.severity === 'critical');
        const complianceRate = sensorInfo.readings.length > 0
          ? ((sensorInfo.readings.length - violations.length) / sensorInfo.readings.length) * 100
          : 100;

        return {
          storageArea: sensorInfo.sensor.storage_areas!,
          violationsCount: violations.length,
          complianceRate,
          criticalViolations
        };
      });

    // Calculate overall compliance rate
    const totalReadings = readings.length;
    const totalViolations = (alerts || []).filter(a => a.alert_type === 'haccp_violation').length;
    const overallComplianceRate = totalReadings > 0 
      ? ((totalReadings - totalViolations) / totalReadings) * 100
      : 100;

    const reportData: TemperatureReportData = {
      summary: {
        reportPeriod: {
          start: startDate.toISOString(),
          end: endDate.toISOString()
        },
        sensorsIncluded: sensorData.length,
        totalReadings,
        totalAlerts: (alerts || []).length,
        complianceRate: overallComplianceRate
      },
      sensorData,
      alerts: alerts || [],
      haccpCompliance
    };

    return reportData;
  }

  /**
   * Clear timer for a report
   */
  private clearTimer(reportId: string): void {
    const timer = this.timers.get(reportId);
    if (timer) {
      clearTimeout(timer);
      this.timers.delete(reportId);
    }
  }

  /**
   * Start the scheduling system
   */
  private startScheduler(): void {
    // Schedule all enabled reports
    for (const report of this.scheduledReports.values()) {
      if (report.enabled) {
        this.scheduleReportExecution(report);
      }
    }

    console.log(`📅 Started scheduler with ${this.scheduledReports.size} reports`);
  }

  /**
   * Stop the scheduling system
   */
  stop(): void {
    for (const reportId of this.timers.keys()) {
      this.clearTimer(reportId);
    }
    
    console.log('⏹️ Stopped report scheduler');
  }

  /**
   * Save scheduled reports to localStorage
   */
  private saveScheduledReports(): void {
    if (typeof window === 'undefined') return;
    
    try {
      const reportsData = Array.from(this.scheduledReports.entries()).map(([id, report]) => ({
        id,
        ...report,
        nextRun: report.nextRun.toISOString(),
        lastRun: report.lastRun?.toISOString()
      }));
      
      localStorage.setItem('scheduledReports', JSON.stringify(reportsData));
    } catch (error) {
      console.error('Failed to save scheduled reports:', error);
    }
  }

  /**
   * Load scheduled reports from localStorage
   */
  private loadScheduledReports(): void {
    if (typeof window === 'undefined') return;
    
    try {
      const saved = localStorage.getItem('scheduledReports');
      if (!saved) return;
      
      const reportsData = JSON.parse(saved);
      
      for (const reportData of reportsData) {
        const report: ScheduledReport = {
          ...reportData,
          nextRun: new Date(reportData.nextRun),
          lastRun: reportData.lastRun ? new Date(reportData.lastRun) : undefined,
          isRunning: false // Reset running state on load
        };
        
        this.scheduledReports.set(report.id, report);
      }
      
      console.log(`📊 Loaded ${this.scheduledReports.size} scheduled reports`);
    } catch (error) {
      console.error('Failed to load scheduled reports:', error);
    }
  }

  /**
   * Get all scheduled reports
   */
  getScheduledReports(): ScheduledReport[] {
    return Array.from(this.scheduledReports.values());
  }

  /**
   * Get recent report jobs
   */
  getRecentJobs(limit: number = 20): ReportJob[] {
    return this.reportJobs
      .slice(-limit)
      .sort((a, b) => b.startTime.getTime() - a.startTime.getTime());
  }

  /**
   * Get scheduler status
   */
  getStatus(): {
    totalReports: number;
    enabledReports: number;
    runningReports: number;
    nextRun: Date | null;
    recentJobs: ReportJob[];
  } {
    const reports = Array.from(this.scheduledReports.values());
    const enabledReports = reports.filter(r => r.enabled);
    const runningReports = reports.filter(r => r.isRunning);
    
    const nextRuns = enabledReports
      .filter(r => !r.isRunning)
      .map(r => r.nextRun)
      .sort((a, b) => a.getTime() - b.getTime());

    return {
      totalReports: reports.length,
      enabledReports: enabledReports.length,
      runningReports: runningReports.length,
      nextRun: nextRuns.length > 0 ? nextRuns[0] : null,
      recentJobs: this.getRecentJobs(10)
    };
  }
}

// Export singleton instance
export const reportScheduler = ReportScheduler.getInstance();

// Auto-initialize in browser environment
if (typeof window !== 'undefined') {
  setTimeout(() => {
    reportScheduler.initialize();
  }, 2000); // Start after other services

  // Stop scheduler on page unload
  window.addEventListener('beforeunload', () => {
    reportScheduler.stop();
  });
}

// React hook for monitoring scheduler status
import { useState, useEffect } from 'react';

export function useReportScheduler() {
  const [status, setStatus] = useState(reportScheduler.getStatus());
  const [reports, setReports] = useState(reportScheduler.getScheduledReports());

  useEffect(() => {
    const interval = setInterval(() => {
      setStatus(reportScheduler.getStatus());
      setReports(reportScheduler.getScheduledReports());
    }, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, []);

  return {
    ...status,
    reports,
    scheduleReport: reportScheduler.scheduleReport.bind(reportScheduler),
    updateReport: reportScheduler.updateScheduledReport.bind(reportScheduler),
    setReportEnabled: reportScheduler.setReportEnabled.bind(reportScheduler),
    removeReport: reportScheduler.removeScheduledReport.bind(reportScheduler),
    triggerReport: reportScheduler.triggerReport.bind(reportScheduler)
  };
}