/**
 * Comprehensive Import Manager
 * Orchestrates all import/export systems for seamless operation
 * Integrates streaming processing, validation, mapping, error handling, and monitoring
 */

import { StreamingProcessor, type StreamingProcessorOptions } from './streamingProcessor';
import { MultiFormatProcessor, type FormatProcessingOptions, type ProcessedFileData } from './multiFormatProcessor';
import { SeafoodColumnMapper, type SeafoodColumnAnalysis, type SeafoodMappingContext } from './seafoodMapper';
import { SeafoodValidator, type ValidationResult, type SeafoodValidationConfig } from './seafoodValidator';
import { TemplateSystem, type ImportTemplate, type TemplateMatchResult } from './templateSystem';
import { ImportErrorHandler, type ImportError, type RecoveryResult } from './errorHandler';
import { PerformanceMonitor, type ImportSession } from '../monitoring/performanceMonitor';
import { GDSTExporter, type ExportOptions } from '../export/gdstExporter';

export interface ImportManagerConfig {
  streaming: StreamingProcessorOptions;
  validation: SeafoodValidationConfig;
  errorHandling: {
    maxRetries: number;
    autoRecovery: boolean;
    strictMode: boolean;
  };
  performance: {
    enableMonitoring: boolean;
    trackUserExperience: boolean;
    generateReports: boolean;
  };
  templates: {
    autoSuggest: boolean;
    learnFromImports: boolean;
  };
}

export interface ImportPipeline {
  id: string;
  status: 'pending' | 'processing' | 'validating' | 'uploading' | 'completed' | 'failed' | 'cancelled';
  progress: {
    currentPhase: string;
    percentage: number;
    processedRecords: number;
    totalRecords: number;
    estimatedTimeRemaining: number;
  };
  file: {
    name: string;
    size: number;
    format: string;
  };
  results?: ImportResults;
  errors: ImportError[];
  canCancel: boolean;
  canRetry: boolean;
}

export interface ImportResults {
  sessionId: string;
  totalRecords: number;
  successfulRecords: number;
  failedRecords: number;
  warnings: number;
  processingTime: number;
  dataQualityScore: number;
  templateUsed?: string;
  suggestedImprovements: string[];
  exportOptions: {
    gdst: boolean;
    haccp: boolean;
    regulatory: boolean;
  };
}

export interface ImportOptions {
  template?: string;
  mappingContext?: SeafoodMappingContext;
  validationLevel?: 'basic' | 'standard' | 'comprehensive';
  processingMode?: 'auto' | 'streaming' | 'standard';
  errorHandling?: 'strict' | 'permissive' | 'interactive';
  generateReport?: boolean;
}

export class ImportManager {
  private streamingProcessor: StreamingProcessor;
  private multiFormatProcessor = MultiFormatProcessor;
  private columnMapper = SeafoodColumnMapper;
  private validator: SeafoodValidator;
  private templateSystem: TemplateSystem;
  private errorHandler: ImportErrorHandler;
  private performanceMonitor: PerformanceMonitor;
  private gdstExporter = GDSTExporter;
  
  private activePipelines: Map<string, ImportPipeline> = new Map();
  private config: ImportManagerConfig;

  constructor(config: Partial<ImportManagerConfig> = {}) {
    this.config = {
      streaming: {
        chunkSize: 1000,
        maxFileSize: 500 * 1024 * 1024, // 500MB
        timeout: 10 * 60 * 1000, // 10 minutes
      },
      validation: {
        enableSpeciesValidation: true,
        enablePriceValidation: true,
        enableDateValidation: true,
        enableTemperatureValidation: true,
        enableHACCPValidation: true,
        enableTraceabilityValidation: true,
        strictMode: false,
        validationLevel: 'standard'
      },
      errorHandling: {
        maxRetries: 3,
        autoRecovery: true,
        strictMode: false
      },
      performance: {
        enableMonitoring: true,
        trackUserExperience: true,
        generateReports: true
      },
      templates: {
        autoSuggest: true,
        learnFromImports: true
      },
      ...config
    };

    this.initializeComponents();
  }

  /**
   * Main import method - processes a file through the complete pipeline
   */
  async importFile(
    file: File, 
    options: ImportOptions = {}
  ): Promise<ImportPipeline> {
    const pipelineId = this.generatePipelineId();
    
    try {
      // Initialize pipeline
      const pipeline = this.createPipeline(pipelineId, file);
      this.activePipelines.set(pipelineId, pipeline);

      // Start performance monitoring
      let sessionId: string | undefined;
      if (this.config.performance.enableMonitoring) {
        sessionId = this.performanceMonitor.startSession({
          name: file.name,
          size: file.size,
          format: this.detectFileFormat(file.name),
          recordCount: 0 // Will be updated later
        });
        pipeline.results = { ...pipeline.results, sessionId } as ImportResults;
      }

      // Phase 1: File Processing and Format Detection
      await this.updatePipelineStatus(pipelineId, 'processing', 'Analyzing file format...');
      const processedData = await this.processFileFormat(file, options, pipelineId);
      
      // Phase 2: Template Matching (if enabled)
      let selectedTemplate: ImportTemplate | undefined;
      if (this.config.templates.autoSuggest && !options.template) {
        await this.updatePipelineStatus(pipelineId, 'processing', 'Finding matching template...');
        const templateMatches = this.templateSystem.findMatchingTemplates(
          processedData.headers,
          processedData.data.slice(0, 10),
          { format: processedData.format, supplier: this.extractSupplier(file.name) }
        );
        
        if (templateMatches.length > 0 && templateMatches[0].confidence > 0.7) {
          selectedTemplate = templateMatches[0].template;
          pipeline.results = { ...pipeline.results, templateUsed: selectedTemplate.id } as ImportResults;
        }
      } else if (options.template) {
        selectedTemplate = this.templateSystem.getTemplate(options.template);
      }

      // Phase 3: Column Mapping
      await this.updatePipelineStatus(pipelineId, 'processing', 'Mapping columns...');
      const columnAnalysis = this.columnMapper.analyzeColumns(
        processedData.headers,
        processedData.data.slice(0, 100), // Sample for analysis
        options.mappingContext
      );

      // Apply template mappings if available
      let mappedData = processedData.data;
      if (selectedTemplate) {
        const templateResult = this.templateSystem.applyTemplate(selectedTemplate, processedData.data);
        mappedData = processedData.data; // Template application would modify this
        
        // Add template validation results to pipeline
        pipeline.errors.push(...templateResult.errors.map(e => this.convertToImportError(e, pipelineId)));
      }

      // Phase 4: Data Validation
      await this.updatePipelineStatus(pipelineId, 'validating', 'Validating data quality...');
      const validationResults = await this.validator.validateBatch(mappedData);
      
      // Process validation results
      const allErrors: ImportError[] = [];
      const allWarnings: ImportError[] = [];
      let successfulRecords = 0;
      let failedRecords = 0;
      let warningCount = 0;

      for (let i = 0; i < validationResults.length; i++) {
        const result = validationResults[i];
        if (result.isValid) {
          successfulRecords++;
        } else {
          failedRecords++;
        }
        
        warningCount += result.warnings.length;
        
        // Convert validation errors to import errors
        for (const error of result.errors) {
          allErrors.push(this.convertValidationErrorToImportError(error, pipelineId, i + 1));
        }
      }

      // Phase 5: Error Handling and Recovery
      if (allErrors.length > 0) {
        await this.updatePipelineStatus(pipelineId, 'processing', 'Handling errors...');
        
        if (this.config.errorHandling.autoRecovery) {
          const recoveryResults = await this.errorHandler.handleBatchErrors(
            allErrors.map(error => ({ error: error.message, context: error.context }))
          );
          
          // Update success/failure counts based on recovery
          const recoveredCount = recoveryResults.filter(r => r.success).length;
          successfulRecords += recoveredCount;
          failedRecords -= recoveredCount;
        }
      }

      // Phase 6: Data Upload (if validation passes or in permissive mode)
      const shouldUpload = this.shouldProceedWithUpload(allErrors, options);
      if (shouldUpload) {
        await this.updatePipelineStatus(pipelineId, 'uploading', 'Uploading data...');
        await this.uploadData(mappedData, pipelineId);
      }

      // Phase 7: Completion and Results
      const processingTime = Date.now() - (pipeline as any).startTime;
      const dataQualityScore = this.calculateDataQualityScore(successfulRecords, failedRecords, warningCount);
      
      pipeline.results = {
        sessionId: sessionId || '',
        totalRecords: processedData.data.length,
        successfulRecords,
        failedRecords,
        warnings: warningCount,
        processingTime,
        dataQualityScore,
        templateUsed: selectedTemplate?.id,
        suggestedImprovements: this.generateImprovementSuggestions(columnAnalysis, allErrors),
        exportOptions: {
          gdst: this.canExportGDST(mappedData),
          haccp: this.canExportHACCP(mappedData),
          regulatory: this.canExportRegulatory(mappedData)
        }
      };

      // Update performance monitoring
      if (sessionId) {
        this.performanceMonitor.endSession(sessionId, 'completed', {
          successfulRecords,
          failedRecords,
          warnings: warningCount,
          importValue: this.calculateImportValue(mappedData)
        });
      }

      // Update template usage statistics
      if (selectedTemplate && this.config.templates.learnFromImports) {
        this.templateSystem.updateTemplateUsage(
          selectedTemplate.id,
          successfulRecords + failedRecords,
          allErrors.map(e => e.message)
        );
      }

      await this.updatePipelineStatus(pipelineId, 'completed');
      return pipeline;

    } catch (error) {
      return await this.handlePipelineFailure(pipelineId, error);
    }
  }

  /**
   * Export data in various compliance formats
   */
  async exportData(
    data: Record<string, unknown>[],
    format: 'gdst_json' | 'gdst_csv' | 'haccp_report' | 'regulatory_export',
    options: ExportOptions = { format }
  ): Promise<string | Blob> {
    const sessionId = this.performanceMonitor.startSession({
      name: `Export_${format}`,
      size: JSON.stringify(data).length,
      format: 'export',
      recordCount: data.length
    });

    try {
      let result: string | Blob;

      switch (format) {
        case 'gdst_json':
        case 'gdst_csv':
          result = await this.gdstExporter.exportGDST(data, [], options);
          break;
        
        case 'haccp_report':
          result = await this.gdstExporter.exportHACCP(data, [], options);
          break;
        
        case 'regulatory_export':
          result = await this.gdstExporter.exportRegulatory(data, options);
          break;
        
        default:
          throw new Error(`Unsupported export format: ${format}`);
      }

      this.performanceMonitor.endSession(sessionId, 'completed', {
        successfulRecords: data.length,
        failedRecords: 0,
        warnings: 0
      });

      return result;

    } catch (error) {
      this.performanceMonitor.endSession(sessionId, 'failed');
      throw error;
    }
  }

  /**
   * Get active pipeline status
   */
  getPipeline(pipelineId: string): ImportPipeline | undefined {
    return this.activePipelines.get(pipelineId);
  }

  /**
   * Get all active pipelines
   */
  getActivePipelines(): ImportPipeline[] {
    return Array.from(this.activePipelines.values());
  }

  /**
   * Cancel a pipeline
   */
  async cancelPipeline(pipelineId: string): Promise<boolean> {
    const pipeline = this.activePipelines.get(pipelineId);
    if (!pipeline?.canCancel) {
      return false;
    }

    pipeline.status = 'cancelled';
    
    // Cancel any ongoing processing
    if (this.streamingProcessor) {
      this.streamingProcessor.cancelProcessing();
    }

    // End monitoring session
    if (pipeline.results?.sessionId) {
      this.performanceMonitor.endSession(pipeline.results.sessionId, 'cancelled');
    }

    return true;
  }

  /**
   * Retry a failed pipeline
   */
  async retryPipeline(pipelineId: string): Promise<ImportPipeline> {
    const pipeline = this.activePipelines.get(pipelineId);
    if (!pipeline?.canRetry) {
      throw new Error('Pipeline cannot be retried');
    }

    // Reset pipeline status
    pipeline.status = 'pending';
    pipeline.progress = {
      currentPhase: 'Initializing',
      percentage: 0,
      processedRecords: 0,
      totalRecords: 0,
      estimatedTimeRemaining: 0
    };
    pipeline.errors = [];

    // This would re-run the import process
    // For now, we'll just update the status
    await this.updatePipelineStatus(pipelineId, 'processing');
    
    return pipeline;
  }

  /**
   * Get import statistics and performance metrics
   */
  getImportStatistics(): {
    totalImports: number;
    successfulImports: number;
    failedImports: number;
    averageProcessingTime: number;
    commonIssues: string[];
  } {
    const pipelines = Array.from(this.activePipelines.values());
    const completedPipelines = pipelines.filter(p => p.status === 'completed');
    const failedPipelines = pipelines.filter(p => p.status === 'failed');
    
    const avgProcessingTime = completedPipelines.length > 0
      ? completedPipelines.reduce((sum, p) => sum + (p.results?.processingTime || 0), 0) / completedPipelines.length
      : 0;

    // Analyze common issues
    const allErrors = pipelines.flatMap(p => p.errors);
    const errorCounts = new Map<string, number>();
    
    for (const error of allErrors) {
      const count = errorCounts.get(error.code) || 0;
      errorCounts.set(error.code, count + 1);
    }
    
    const commonIssues = Array.from(errorCounts.entries())
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([code]) => code);

    return {
      totalImports: pipelines.length,
      successfulImports: completedPipelines.length,
      failedImports: failedPipelines.length,
      averageProcessingTime: avgProcessingTime,
      commonIssues
    };
  }

  // Private methods

  private initializeComponents(): void {
    this.streamingProcessor = new StreamingProcessor(this.config.streaming);
    this.validator = new SeafoodValidator(this.config.validation);
    this.templateSystem = new TemplateSystem();
    this.errorHandler = new ImportErrorHandler(
      { maxAttempts: this.config.errorHandling.maxRetries },
      (error) => this.handleError(error),
      (error, result) => this.handleRecovery(error, result)
    );
    this.performanceMonitor = new PerformanceMonitor();
  }

  private createPipeline(id: string, file: File): ImportPipeline {
    return {
      id,
      status: 'pending',
      progress: {
        currentPhase: 'Initializing',
        percentage: 0,
        processedRecords: 0,
        totalRecords: 0,
        estimatedTimeRemaining: 0
      },
      file: {
        name: file.name,
        size: file.size,
        format: this.detectFileFormat(file.name)
      },
      errors: [],
      canCancel: true,
      canRetry: true
    };
  }

  private async processFileFormat(
    file: File, 
    options: ImportOptions, 
    pipelineId: string
  ): Promise<ProcessedFileData> {
    const processingMode = options.processingMode || 'auto';
    
    // Determine if streaming is needed
    const useStreaming = processingMode === 'streaming' || 
      (processingMode === 'auto' && StreamingProcessor.shouldUseStreaming(file));

    if (useStreaming) {
      // Use streaming processor
      const result = await this.streamingProcessor.processFile(file, {
        chunkSize: this.config.streaming.chunkSize,
        hasHeader: true,
        onProgress: (progress) => {
          this.updateProgressFromStreaming(pipelineId, progress);
        }
      });

      return {
        data: result.data,
        headers: result.headers,
        format: 'csv', // Streaming currently only supports CSV
        metadata: {
          fileName: file.name,
          fileSize: file.size,
          originalFormat: 'csv',
          rowCount: result.totalRows,
          columnCount: result.headers.length,
          hasHeader: true,
          lastModified: file.lastModified
        },
        processingStats: {
          startTime: 0,
          endTime: 0,
          processingTime: result.processingTime,
          rowsPerSecond: 0,
          bytesProcessed: file.size,
          errorsEncountered: result.errors.length,
          warningsGenerated: 0,
          memoryUsage: result.memoryUsage
        }
      };
    } else {
      // Use multi-format processor
      return await this.multiFormatProcessor.processFile(file);
    }
  }

  private async updatePipelineStatus(
    pipelineId: string, 
    status: ImportPipeline['status'], 
    phase?: string
  ): Promise<void> {
    const pipeline = this.activePipelines.get(pipelineId);
    if (!pipeline) return;

    pipeline.status = status;
    
    if (phase) {
      pipeline.progress.currentPhase = phase;
    }
    
    // Update percentage based on status
    const statusToPercentage: Record<ImportPipeline['status'], number> = {
      'pending': 0,
      'processing': 25,
      'validating': 50,
      'uploading': 75,
      'completed': 100,
      'failed': 0,
      'cancelled': 0
    };
    
    pipeline.progress.percentage = statusToPercentage[status] || 0;
  }

  private updateProgressFromStreaming(pipelineId: string, progress: any): void {
    const pipeline = this.activePipelines.get(pipelineId);
    if (!pipeline) return;

    pipeline.progress = {
      ...pipeline.progress,
      percentage: progress.percentage,
      processedRecords: progress.processed,
      totalRecords: progress.total
    };

    // Update performance monitoring
    if (pipeline.results?.sessionId) {
      this.performanceMonitor.updateProgress(pipeline.results.sessionId, {
        processedRecords: progress.processed,
        currentPhase: 'parsing',
        memoryUsage: progress.memoryUsage,
      });
    }
  }

  private convertToImportError(error: any, pipelineId: string): ImportError {
    return {
      id: `${pipelineId}_${Date.now()}_${Math.random()}`,
      code: error.code || 'UNKNOWN_ERROR',
      category: 'validation',
      severity: error.severity || 'medium',
      message: error.message,
      field: error.field,
      row: error.row,
      timestamp: new Date().toISOString(),
      context: {
        operation: 'import',
        fileName: this.activePipelines.get(pipelineId)?.file.name
      },
      recovery: {
        strategy: 'manual',
        attempts: 0,
        maxAttempts: 3,
        canRetry: true,
        canSkip: error.severity !== 'critical',
        suggested: []
      },
      userGuidance: {
        explanation: error.message,
        actions: ['Review the data and correct the issue'],
        prevention: ['Validate data before import']
      }
    };
  }

  private convertValidationErrorToImportError(
    error: any, 
    pipelineId: string, 
    rowNumber: number
  ): ImportError {
    return this.convertToImportError({
      ...error,
      row: rowNumber
    }, pipelineId);
  }

  private shouldProceedWithUpload(errors: ImportError[], options: ImportOptions): boolean {
    const criticalErrors = errors.filter(e => e.severity === 'critical');
    
    if (options.errorHandling === 'strict' && errors.length > 0) {
      return false;
    }
    
    if (options.errorHandling === 'permissive') {
      return criticalErrors.length === 0;
    }
    
    // Default: proceed if no critical errors
    return criticalErrors.length === 0;
  }

  private async uploadData(data: Record<string, unknown>[], pipelineId: string): Promise<void> {
    // This would integrate with the existing Supabase upload logic
    // For now, we'll simulate the upload
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  private async handlePipelineFailure(pipelineId: string, error: unknown): Promise<ImportPipeline> {
    const pipeline = this.activePipelines.get(pipelineId);
    if (pipeline) {
      pipeline.status = 'failed';
      
      const importError = await this.errorHandler.handleError(error, {
        operation: 'import',
        fileName: pipeline.file.name,
        fileSize: pipeline.file.size
      });
      
      pipeline.errors.push(this.convertToImportError(importError, pipelineId));
    }
    
    return pipeline!;
  }

  // Utility methods
  private detectFileFormat(filename: string): string {
    const ext = filename.toLowerCase().split('.').pop() || '';
    const formatMap: Record<string, string> = {
      'csv': 'CSV',
      'xlsx': 'Excel',
      'xls': 'Excel (Legacy)',
      'json': 'JSON',
      'xml': 'XML',
      'tsv': 'TSV'
    };
    return formatMap[ext] || 'Unknown';
  }

  private extractSupplier(filename: string): string | undefined {
    // Simple supplier extraction from filename
    const suppliers = ['pacific', 'trident', 'seafood', 'foods', 'processing'];
    const nameLower = filename.toLowerCase();
    return suppliers.find(supplier => nameLower.includes(supplier));
  }

  private calculateDataQualityScore(successful: number, failed: number, warnings: number): number {
    const total = successful + failed;
    if (total === 0) return 0;
    
    const successRate = successful / total;
    const warningPenalty = Math.min(warnings / total * 0.1, 0.3);
    
    return Math.max(0, Math.min(10, (successRate - warningPenalty) * 10));
  }

  private generateImprovementSuggestions(
    columnAnalysis: SeafoodColumnAnalysis[], 
    errors: ImportError[]
  ): string[] {
    const suggestions: string[] = [];
    
    // Column mapping suggestions
    const lowConfidenceMappings = columnAnalysis.filter(c => c.confidence < 0.7);
    if (lowConfidenceMappings.length > 0) {
      suggestions.push('Consider creating a custom template for better column mapping');
    }
    
    // Error pattern suggestions
    const speciesErrors = errors.filter(e => e.code.includes('SPECIES'));
    if (speciesErrors.length > 5) {
      suggestions.push('Standardize species names using our reference guide');
    }
    
    const priceErrors = errors.filter(e => e.code.includes('PRICE'));
    if (priceErrors.length > 3) {
      suggestions.push('Verify price units and currency consistency');
    }
    
    return suggestions;
  }

  private calculateImportValue(data: Record<string, unknown>[]): number {
    return data.reduce((sum, row) => {
      const price = Number(row.price) || 0;
      const quantity = Number(row.quantity) || 0;
      return sum + (price * quantity);
    }, 0);
  }

  private canExportGDST(data: Record<string, unknown>[]): boolean {
    return data.some(row => 
      row.species || row.catch_date || row.origin || row.vessel_id
    );
  }

  private canExportHACCP(data: Record<string, unknown>[]): boolean {
    return data.some(row => 
      row.temperature || row.storage_temp || row.supplier
    );
  }

  private canExportRegulatory(data: Record<string, unknown>[]): boolean {
    return data.length > 0; // All data can potentially be exported for regulatory purposes
  }

  private handleError(error: ImportError): void {
    console.warn('Import error handled:', error);
  }

  private handleRecovery(error: ImportError, result: RecoveryResult): void {
    console.log('Error recovery attempted:', { error: error.code, result: result.action });
  }

  private generatePipelineId(): string {
    return `pipeline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}