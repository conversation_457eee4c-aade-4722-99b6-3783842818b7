/**
 * Seafood Industry-Specific Column Mapping System
 * Intelligent auto-detection with fuzzy matching and semantic analysis
 * Optimized for seafood terminology, species variations, and industry standards
 */

export interface SeafoodMappingRule {
  targetField: string;
  sourceColumn: string;
  confidence: number; // 0-1 confidence score
  mappingType: 'exact' | 'fuzzy' | 'semantic' | 'pattern' | 'manual';
  transformFunction?: string; // Optional transformation function name
  validationRules?: string[]; // Associated validation rules
}

export interface SeafoodColumnAnalysis {
  column: string;
  detectedType: 'species' | 'weight' | 'price' | 'date' | 'location' | 'supplier' | 'batch' | 'temperature' | 'unknown';
  confidence: number;
  suggestedMappings: SeafoodMappingRule[];
  sampleValues: string[];
  uniqueValueCount: number;
  nullCount: number;
  patterns: string[];
}

export interface SeafoodMappingContext {
  fileSource?: 'msc' | 'asc' | 'gdst' | 'haccp' | 'custom';
  region?: 'pacific' | 'atlantic' | 'global';
  species?: string[];
  supplierType?: 'processor' | 'distributor' | 'retailer' | 'producer';
}

export class SeafoodColumnMapper {
  private static readonly SPECIES_PATTERNS = {
    // Common seafood species patterns
    salmon: /salmon|coho|chinook|sockeye|pink|chum|king|silver/i,
    crab: /crab|dungeness|king\s*crab|snow\s*crab|opilio|bairdi/i,
    halibut: /halibut|hippo/i,
    cod: /cod|pacific\s*cod|atlantic\s*cod|lingcod/i,
    tuna: /tuna|albacore|yellowfin|bigeye|bluefin/i,
    shrimp: /shrimp|prawns?|spot\s*prawns?/i,
    rockfish: /rockfish|snapper|yelloweye|canary/i,
    flounder: /flounder|sole|petrale|dover|english/i,
    sablefish: /sablefish|black\s*cod|butterfish/i,
    sardine: /sardine|pilchard/i,
    anchovy: /anchovy|anchovies/i,
    mackerel: /mackerel|horse\s*mackerel/i
  };

  private static readonly FIELD_MAPPINGS = {
    // Product identification
    name: {
      patterns: [/^(product[_\s]*)?name$/i, /^item$/i, /^description$/i, /^product$/i, /^fish$/i, /^species$/i],
      keywords: ['name', 'product', 'item', 'description', 'fish', 'species', 'seafood'],
      semanticFields: ['title', 'label', 'commodity']
    },
    
    sku: {
      patterns: [/^sku$/i, /^item[_\s]*code$/i, /^product[_\s]*code$/i, /^code$/i, /^id$/i],
      keywords: ['sku', 'code', 'item_code', 'product_code', 'upc', 'barcode'],
      semanticFields: ['identifier', 'ref', 'reference']
    },

    // Weights and measurements
    weight: {
      patterns: [/^(net[_\s]*)?weight$/i, /^(gross[_\s]*)?weight$/i, /^pounds?$/i, /^lbs?$/i, /^kg$/i, /^kilograms?$/i],
      keywords: ['weight', 'pounds', 'lbs', 'kg', 'kilograms', 'mass', 'net_weight', 'gross_weight'],
      semanticFields: ['quantity', 'amount', 'volume']
    },

    quantity: {
      patterns: [/^qty$/i, /^quantity$/i, /^count$/i, /^pieces?$/i, /^units?$/i, /^stock$/i],
      keywords: ['qty', 'quantity', 'count', 'pieces', 'units', 'stock', 'inventory'],
      semanticFields: ['amount', 'number', 'total']
    },

    // Pricing
    price: {
      patterns: [/^(unit[_\s]*)?price$/i, /^cost$/i, /^rate$/i, /^\$?price$/i, /^amount$/i],
      keywords: ['price', 'cost', 'rate', 'unit_price', 'selling_price', 'retail_price'],
      semanticFields: ['value', 'charge', 'fee']
    },

    // Location and origin
    origin: {
      patterns: [/^origin$/i, /^source$/i, /^location$/i, /^region$/i, /^area$/i, /^zone$/i],
      keywords: ['origin', 'source', 'location', 'region', 'area', 'fishing_area', 'catch_area'],
      semanticFields: ['place', 'locality', 'geography']
    },

    // Supplier information
    supplier: {
      patterns: [/^supplier$/i, /^vendor$/i, /^processor$/i, /^company$/i, /^source$/i],
      keywords: ['supplier', 'vendor', 'processor', 'company', 'distributor', 'manufacturer'],
      semanticFields: ['provider', 'seller', 'producer']
    },

    // Dates
    date: {
      patterns: [/^date$/i, /^received[_\s]*date$/i, /^catch[_\s]*date$/i, /^harvest[_\s]*date$/i, /^processed[_\s]*date$/i],
      keywords: ['date', 'received_date', 'catch_date', 'harvest_date', 'processed_date', 'timestamp'],
      semanticFields: ['when', 'time', 'datetime']
    },

    expiry_date: {
      patterns: [/^expir(y|ation)[_\s]*date$/i, /^best[_\s]*by$/i, /^use[_\s]*by$/i, /^sell[_\s]*by$/i],
      keywords: ['expiry_date', 'expiration_date', 'best_by', 'use_by', 'sell_by', 'shelf_life'],
      semanticFields: ['expires', 'valid_until', 'lifespan']
    },

    // Batch and traceability
    batch_number: {
      patterns: [/^batch$/i, /^lot$/i, /^batch[_\s]*number$/i, /^lot[_\s]*number$/i, /^trace[_\s]*code$/i],
      keywords: ['batch', 'lot', 'batch_number', 'lot_number', 'trace_code', 'tracking_number'],
      semanticFields: ['batch_id', 'lot_id', 'serial']
    },

    // Temperature and storage
    storage_temp: {
      patterns: [/^temp(erature)?$/i, /^storage[_\s]*temp$/i, /^°[cf]$/i, /^degrees?$/i],
      keywords: ['temperature', 'temp', 'storage_temp', 'degrees', 'celsius', 'fahrenheit'],
      semanticFields: ['thermal', 'heat', 'cold']
    },

    // Categories
    category: {
      patterns: [/^categor(y|ies)$/i, /^type$/i, /^class$/i, /^group$/i, /^family$/i],
      keywords: ['category', 'type', 'class', 'group', 'family', 'classification'],
      semanticFields: ['kind', 'sort', 'genre']
    },

    // Units
    unit: {
      patterns: [/^units?$/i, /^measure$/i, /^uom$/i, /^unit[_\s]*of[_\s]*measure$/i],
      keywords: ['unit', 'units', 'measure', 'uom', 'unit_of_measure'],
      semanticFields: ['measurement', 'scale', 'metric']
    }
  };

  private static readonly TRANSFORMATION_FUNCTIONS = {
    // Common transformations for seafood data
    normalizeSpeciesName: (value: string): string => {
      return value?.toString().trim()
        .replace(/\b(pacific|atlantic|wild|farm|farmed)\s+/i, '')
        .toLowerCase();
    },

    parseWeight: (value: string): number => {
      const num = parseFloat(value?.toString().replace(/[^\d.-]/g, '') || '0');
      // Convert common seafood weights to standard unit (pounds)
      const str = value?.toString().toLowerCase() || '';
      if (str.includes('kg') || str.includes('kilo')) return num * 2.20462;
      if (str.includes('oz') || str.includes('ounce')) return num / 16;
      if (str.includes('ton')) return num * 2000;
      return num; // Assume pounds by default
    },

    parseTemperature: (value: string): number => {
      const num = parseFloat(value?.toString().replace(/[^\d.-]/g, '') || '0');
      const str = value?.toString().toLowerCase() || '';
      // Convert to Fahrenheit if Celsius
      if (str.includes('c') || str.includes('celsius')) {
        return (num * 9/5) + 32;
      }
      return num; // Assume Fahrenheit
    },

    standardizeBatchNumber: (value: string): string => {
      return value?.toString().trim().toUpperCase() || '';
    },

    parseDate: (value: string): string => {
      if (!value) return '';
      try {
        const date = new Date(value);
        return date.toISOString().split('T')[0]; // Return YYYY-MM-DD format
      } catch {
        return value?.toString() || '';
      }
    }
  };

  /**
   * Analyze columns and suggest mappings
   */
  static analyzeColumns(
    headers: string[],
    sampleData: Record<string, unknown>[],
    context?: SeafoodMappingContext
  ): SeafoodColumnAnalysis[] {
    return headers.map(header => this.analyzeColumn(header, sampleData, context));
  }

  /**
   * Analyze a single column for seafood-specific patterns
   */
  private static analyzeColumn(
    header: string,
    sampleData: Record<string, unknown>[],
    context?: SeafoodMappingContext
  ): SeafoodColumnAnalysis {
    const columnValues = sampleData
      .map(row => row[header])
      .filter(val => val !== null && val !== undefined)
      .map(val => String(val).trim())
      .filter(val => val.length > 0);

    const uniqueValues = [...new Set(columnValues)];
    const sampleValues = uniqueValues.slice(0, 10);
    const nullCount = sampleData.length - columnValues.length;

    // Detect column type and generate mappings
    const typeAnalysis = this.detectColumnType(header, sampleValues, context);
    const suggestedMappings = this.generateMappings(header, sampleValues, typeAnalysis.type, context);

    return {
      column: header,
      detectedType: typeAnalysis.type,
      confidence: typeAnalysis.confidence,
      suggestedMappings,
      sampleValues,
      uniqueValueCount: uniqueValues.length,
      nullCount,
      patterns: this.extractPatterns(sampleValues)
    };
  }

  /**
   * Detect column type based on header and sample data
   */
  private static detectColumnType(
    header: string,
    sampleValues: string[],
    context?: SeafoodMappingContext
  ): { type: SeafoodColumnAnalysis['detectedType'], confidence: number } {
    // Check for species patterns
    const speciesScore = this.checkSpeciesPatterns(header, sampleValues);
    if (speciesScore > 0.7) return { type: 'species', confidence: speciesScore };

    // Check for numeric patterns (weight, price)
    const numericScore = this.checkNumericPatterns(header, sampleValues);
    if (numericScore > 0.8) {
      if (this.isWeightField(header)) return { type: 'weight', confidence: numericScore };
      if (this.isPriceField(header)) return { type: 'price', confidence: numericScore };
    }

    // Check for date patterns
    const dateScore = this.checkDatePatterns(header, sampleValues);
    if (dateScore > 0.7) return { type: 'date', confidence: dateScore };

    // Check for temperature patterns
    const tempScore = this.checkTemperaturePatterns(header, sampleValues);
    if (tempScore > 0.7) return { type: 'temperature', confidence: tempScore };

    // Check for location patterns
    const locationScore = this.checkLocationPatterns(header, sampleValues, context);
    if (locationScore > 0.6) return { type: 'location', confidence: locationScore };

    // Check for supplier patterns
    const supplierScore = this.checkSupplierPatterns(header, sampleValues);
    if (supplierScore > 0.6) return { type: 'supplier', confidence: supplierScore };

    // Check for batch patterns
    const batchScore = this.checkBatchPatterns(header, sampleValues);
    if (batchScore > 0.7) return { type: 'batch', confidence: batchScore };

    return { type: 'unknown', confidence: 0.1 };
  }

  /**
   * Generate mapping suggestions for a column
   */
  private static generateMappings(
    header: string,
    sampleValues: string[],
    detectedType: SeafoodColumnAnalysis['detectedType'],
    context?: SeafoodMappingContext
  ): SeafoodMappingRule[] {
    const mappings: SeafoodMappingRule[] = [];

    // Generate mappings based on field definitions
    for (const [fieldName, fieldDef] of Object.entries(this.FIELD_MAPPINGS)) {
      const confidence = this.calculateMappingConfidence(header, sampleValues, fieldDef);
      
      if (confidence > 0.3) {
        mappings.push({
          targetField: fieldName,
          sourceColumn: header,
          confidence,
          mappingType: confidence > 0.8 ? 'exact' : confidence > 0.6 ? 'fuzzy' : 'semantic',
          transformFunction: this.suggestTransformation(fieldName, sampleValues),
          validationRules: this.suggestValidationRules(fieldName, detectedType)
        });
      }
    }

    // Sort by confidence and return top suggestions
    return mappings
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, 3);
  }

  /**
   * Calculate confidence score for field mapping
   */
  private static calculateMappingConfidence(
    header: string,
    sampleValues: string[],
    fieldDef: { patterns: RegExp[], keywords: string[], semanticFields: string[] }
  ): number {
    let confidence = 0;

    // Check exact pattern matches
    for (const pattern of fieldDef.patterns) {
      if (pattern.test(header)) {
        confidence = Math.max(confidence, 0.9);
        break;
      }
    }

    // Check keyword matches with fuzzy matching
    for (const keyword of fieldDef.keywords) {
      const similarity = this.calculateStringSimilarity(header.toLowerCase(), keyword);
      confidence = Math.max(confidence, similarity * 0.8);
    }

    // Check semantic field matches
    for (const semanticField of fieldDef.semanticFields) {
      const similarity = this.calculateStringSimilarity(header.toLowerCase(), semanticField);
      confidence = Math.max(confidence, similarity * 0.6);
    }

    // Boost confidence based on sample data analysis
    const dataBoost = this.analyzeDataPatterns(sampleValues, fieldDef);
    confidence = Math.min(1.0, confidence + dataBoost);

    return confidence;
  }

  /**
   * Calculate string similarity using Levenshtein distance
   */
  private static calculateStringSimilarity(str1: string, str2: string): number {
    const matrix: number[][] = [];
    const len1 = str1.length;
    const len2 = str2.length;

    for (let i = 0; i <= len1; i++) matrix[i] = [i];
    for (let j = 0; j <= len2; j++) matrix[0][j] = j;

    for (let i = 1; i <= len1; i++) {
      for (let j = 1; j <= len2; j++) {
        const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j - 1] + cost
        );
      }
    }

    const maxLen = Math.max(len1, len2);
    return maxLen === 0 ? 1 : (maxLen - matrix[len1][len2]) / maxLen;
  }

  // Helper methods for pattern detection
  private static checkSpeciesPatterns(header: string, sampleValues: string[]): number {
    let score = 0;

    // Check header for species-related terms
    if (/species|fish|seafood|product|name/i.test(header)) score += 0.3;

    // Check sample values for known species patterns
    let speciesMatches = 0;
    for (const value of sampleValues) {
      for (const pattern of Object.values(this.SPECIES_PATTERNS)) {
        if (pattern.test(value)) {
          speciesMatches++;
          break;
        }
      }
    }

    score += (speciesMatches / sampleValues.length) * 0.7;
    return Math.min(1, score);
  }

  private static checkNumericPatterns(header: string, sampleValues: string[]): number {
    const numericCount = sampleValues.filter(val => 
      /^\$?\d+\.?\d*$/.test(val.replace(/[,\s]/g, ''))
    ).length;

    return sampleValues.length > 0 ? numericCount / sampleValues.length : 0;
  }

  private static isWeightField(header: string): boolean {
    return /weight|pounds?|lbs?|kg|kilograms?|mass/i.test(header);
  }

  private static isPriceField(header: string): boolean {
    return /price|cost|\$|amount|rate|value/i.test(header);
  }

  private static checkDatePatterns(header: string, sampleValues: string[]): number {
    let score = 0;

    if (/date|time|when/i.test(header)) score += 0.5;

    const dateCount = sampleValues.filter(val => {
      try {
        const date = new Date(val);
        return !isNaN(date.getTime()) && date.getFullYear() > 1900 && date.getFullYear() < 2050;
      } catch {
        return false;
      }
    }).length;

    score += sampleValues.length > 0 ? (dateCount / sampleValues.length) * 0.5 : 0;
    return Math.min(1, score);
  }

  private static checkTemperaturePatterns(header: string, sampleValues: string[]): number {
    let score = 0;

    if (/temp|°|degrees?|celsius|fahrenheit/i.test(header)) score += 0.6;

    const tempCount = sampleValues.filter(val => 
      /^-?\d+\.?\d*\s*°?[cf]?$/i.test(val) || 
      (parseFloat(val) >= -40 && parseFloat(val) <= 200)
    ).length;

    score += sampleValues.length > 0 ? (tempCount / sampleValues.length) * 0.4 : 0;
    return Math.min(1, score);
  }

  private static checkLocationPatterns(header: string, sampleValues: string[], context?: SeafoodMappingContext): number {
    let score = 0;

    if (/origin|location|region|area|zone|place/i.test(header)) score += 0.4;

    // Check for common fishing regions/locations
    const locationPatterns = [
      /pacific|atlantic|gulf|bering|alaska|washington|oregon|california|canada|japan/i,
      /fao\s*\d+/i, // FAO fishing areas
      /area\s*\d+/i,
      /zone\s*[a-z]/i
    ];

    let locationMatches = 0;
    for (const value of sampleValues) {
      for (const pattern of locationPatterns) {
        if (pattern.test(value)) {
          locationMatches++;
          break;
        }
      }
    }

    score += sampleValues.length > 0 ? (locationMatches / sampleValues.length) * 0.6 : 0;
    return Math.min(1, score);
  }

  private static checkSupplierPatterns(header: string, sampleValues: string[]): number {
    let score = 0;

    if (/supplier|vendor|company|processor|distributor/i.test(header)) score += 0.5;

    // Check for company-like patterns (contains spaces, proper case, etc.)
    const companyCount = sampleValues.filter(val => 
      val.length > 3 && 
      /[A-Z]/.test(val) && 
      (val.includes(' ') || val.includes('LLC') || val.includes('Inc') || val.includes('Corp'))
    ).length;

    score += sampleValues.length > 0 ? (companyCount / sampleValues.length) * 0.5 : 0;
    return Math.min(1, score);
  }

  private static checkBatchPatterns(header: string, sampleValues: string[]): number {
    let score = 0;

    if (/batch|lot|trace|tracking|serial/i.test(header)) score += 0.6;

    // Check for batch-like patterns (alphanumeric codes)
    const batchCount = sampleValues.filter(val => 
      /^[A-Z0-9-]{3,20}$/i.test(val.replace(/\s/g, ''))
    ).length;

    score += sampleValues.length > 0 ? (batchCount / sampleValues.length) * 0.4 : 0;
    return Math.min(1, score);
  }

  private static analyzeDataPatterns(sampleValues: string[], fieldDef: any): number {
    // Additional boost based on data pattern analysis
    // This is a simplified implementation - could be expanded
    return 0.1; // Small boost for now
  }

  private static extractPatterns(sampleValues: string[]): string[] {
    const patterns: string[] = [];
    
    // Extract common patterns from sample data
    if (sampleValues.some(val => /^\d{4}-\d{2}-\d{2}$/.test(val))) {
      patterns.push('ISO Date Format (YYYY-MM-DD)');
    }
    
    if (sampleValues.some(val => /^\$\d+\.?\d*$/.test(val))) {
      patterns.push('Currency Format ($X.XX)');
    }
    
    if (sampleValues.some(val => /^\d+\.?\d*\s*(lbs?|kg|oz)$/i.test(val))) {
      patterns.push('Weight with Unit');
    }

    return patterns;
  }

  private static suggestTransformation(fieldName: string, sampleValues: string[]): string | undefined {
    // Suggest appropriate transformation functions
    switch (fieldName) {
      case 'weight':
      case 'quantity':
        return sampleValues.some(val => /kg|kilo|oz|ton/i.test(val)) ? 'parseWeight' : undefined;
      case 'storage_temp':
        return sampleValues.some(val => /[cf°]/i.test(val)) ? 'parseTemperature' : undefined;
      case 'batch_number':
        return 'standardizeBatchNumber';
      case 'date':
      case 'expiry_date':
        return 'parseDate';
      case 'name':
        return sampleValues.some(val => this.SPECIES_PATTERNS.salmon.test(val)) ? 'normalizeSpeciesName' : undefined;
      default:
        return undefined;
    }
  }

  private static suggestValidationRules(fieldName: string, detectedType: SeafoodColumnAnalysis['detectedType']): string[] {
    const rules: string[] = [];

    switch (fieldName) {
      case 'weight':
      case 'quantity':
        rules.push('positive_number', 'reasonable_seafood_weight');
        break;
      case 'price':
      case 'cost':
        rules.push('positive_number', 'reasonable_seafood_price');
        break;
      case 'storage_temp':
        rules.push('seafood_temperature_range');
        break;
      case 'expiry_date':
        rules.push('future_date', 'reasonable_shelf_life');
        break;
      case 'name':
        if (detectedType === 'species') {
          rules.push('valid_seafood_species');
        }
        break;
      case 'origin':
        rules.push('valid_fishing_area');
        break;
    }

    return rules;
  }

  /**
   * Apply transformations to mapped data
   */
  static applyTransformations(data: Record<string, unknown>[], mappings: SeafoodMappingRule[]): Record<string, unknown>[] {
    return data.map(row => {
      const transformedRow = { ...row };

      for (const mapping of mappings) {
        if (mapping.transformFunction && this.TRANSFORMATION_FUNCTIONS[mapping.transformFunction as keyof typeof this.TRANSFORMATION_FUNCTIONS]) {
          const sourceValue = row[mapping.sourceColumn];
          if (sourceValue !== null && sourceValue !== undefined) {
            const transformFunc = this.TRANSFORMATION_FUNCTIONS[mapping.transformFunction as keyof typeof this.TRANSFORMATION_FUNCTIONS];
            transformedRow[mapping.targetField] = transformFunc(String(sourceValue));
          }
        } else {
          transformedRow[mapping.targetField] = row[mapping.sourceColumn];
        }
      }

      return transformedRow;
    });
  }

  /**
   * Get confidence statistics for mapping results
   */
  static getMappingStats(analyses: SeafoodColumnAnalysis[]): {
    highConfidence: number;
    mediumConfidence: number;
    lowConfidence: number;
    unmapped: number;
    averageConfidence: number;
  } {
    const mappedColumns = analyses.filter(a => a.suggestedMappings.length > 0);
    const highConf = mappedColumns.filter(a => a.confidence >= 0.8).length;
    const medConf = mappedColumns.filter(a => a.confidence >= 0.5 && a.confidence < 0.8).length;
    const lowConf = mappedColumns.filter(a => a.confidence < 0.5).length;
    const unmapped = analyses.length - mappedColumns.length;
    
    const avgConfidence = mappedColumns.length > 0 
      ? mappedColumns.reduce((sum, a) => sum + a.confidence, 0) / mappedColumns.length 
      : 0;

    return {
      highConfidence: highConf,
      mediumConfidence: medConf,
      lowConfidence: lowConf,
      unmapped,
      averageConfidence: Math.round(avgConfidence * 100) / 100
    };
  }
}