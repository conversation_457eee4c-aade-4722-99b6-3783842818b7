/**
 * Template System for Common Seafood Import Patterns
 * Pre-configured templates for different seafood suppliers and formats
 * Includes template validation, customization, and sharing capabilities
 */

import type { SeafoodMappingRule } from './seafoodMapper';
import type { ValidationResult } from './seafoodValidator';

export interface ImportTemplate {
  id: string;
  name: string;
  description: string;
  category: TemplateCategory;
  sourceType: 'supplier' | 'standard' | 'custom' | 'regulatory';
  version: string;
  created: string;
  lastModified: string;
  author: string;
  isPublic: boolean;
  
  // Template configuration
  fileFormat: 'csv' | 'xlsx' | 'json' | 'xml' | 'mixed';
  hasHeader: boolean;
  delimiter?: string;
  encoding: string;
  
  // Field mappings
  fieldMappings: SeafoodMappingRule[];
  requiredFields: string[];
  optionalFields: string[];
  
  // Validation rules
  validationRules: TemplateValidationRule[];
  businessRules: TemplateBusinessRule[];
  
  // Data transformations
  transformations: TemplateTransformation[];
  
  // Template metadata
  metadata: TemplateMetadata;
  
  // Usage statistics
  usage: TemplateUsage;
}

export type TemplateCategory = 'salmon' | 'crab' | 'halibut' | 'mixed_seafood' | 'wholesale' | 
                              'retail' | 'restaurant' | 'processor' | 'regulatory' | 'custom';

export interface TemplateValidationRule {
  field: string;
  ruleType: 'required' | 'range' | 'pattern' | 'custom' | 'species' | 'price' | 'date';
  parameters: Record<string, unknown>;
  errorMessage: string;
  severity: 'error' | 'warning';
}

export interface TemplateBusinessRule {
  id: string;
  description: string;
  condition: string; // JavaScript expression
  action: 'reject' | 'warn' | 'transform' | 'flag';
  message: string;
}

export interface TemplateTransformation {
  field: string;
  transformType: 'normalize' | 'convert' | 'lookup' | 'calculate' | 'format';
  parameters: Record<string, unknown>;
  description: string;
}

export interface TemplateMetadata {
  supplierInfo?: {
    name: string;
    type: string;
    region: string;
    contact?: string;
  };
  dataFormat?: {
    sampleFile?: string;
    documentation?: string;
    lastUpdated?: string;
  };
  compliance?: {
    standards: string[]; // e.g., ['GDST', 'HACCP', 'MSC']
    certifications: string[];
  };
  performance?: {
    averageProcessingTime: number;
    successRate: number;
    commonIssues: string[];
  };
}

export interface TemplateUsage {
  timesUsed: number;
  lastUsed?: string;
  successfulImports: number;
  averageRecordsPerImport: number;
  commonErrors: Array<{
    error: string;
    frequency: number;
  }>;
}

export interface TemplateMatchResult {
  template: ImportTemplate;
  confidence: number;
  matchReasons: string[];
  suggestedModifications?: string[];
}

export interface TemplateValidationResult extends ValidationResult {
  templateCompliance: {
    requiredFieldsCovered: number;
    optionalFieldsMatched: number;
    validationRulesPassed: number;
    businessRulesPassed: number;
  };
  suggestions: string[];
}

export class TemplateSystem {
  private templates: Map<string, ImportTemplate> = new Map();
  private templateCache: Map<string, TemplateMatchResult[]> = new Map();

  constructor() {
    this.initializeBuiltInTemplates();
  }

  /**
   * Initialize built-in templates for common seafood suppliers and formats
   */
  private initializeBuiltInTemplates() {
    // Pacific Seafood Wholesale Template
    this.addTemplate({
      id: 'pacific_seafood_wholesale',
      name: 'Pacific Seafood Wholesale',
      description: 'Template for Pacific Seafood wholesale price lists and inventory',
      category: 'wholesale',
      sourceType: 'supplier',
      version: '1.0',
      created: new Date().toISOString(),
      lastModified: new Date().toISOString(),
      author: 'System',
      isPublic: true,
      fileFormat: 'csv',
      hasHeader: true,
      delimiter: ',',
      encoding: 'UTF-8',
      fieldMappings: [
        {
          targetField: 'name',
          sourceColumn: 'Product Name',
          confidence: 1.0,
          mappingType: 'exact'
        },
        {
          targetField: 'sku',
          sourceColumn: 'Item Code',
          confidence: 1.0,
          mappingType: 'exact'
        },
        {
          targetField: 'price',
          sourceColumn: 'Price per LB',
          confidence: 1.0,
          mappingType: 'exact',
          transformFunction: 'parsePrice'
        },
        {
          targetField: 'weight',
          sourceColumn: 'Net Weight',
          confidence: 1.0,
          mappingType: 'exact',
          transformFunction: 'parseWeight'
        },
        {
          targetField: 'origin',
          sourceColumn: 'Origin',
          confidence: 1.0,
          mappingType: 'exact'
        }
      ],
      requiredFields: ['name', 'sku', 'price', 'weight'],
      optionalFields: ['origin', 'category', 'notes'],
      validationRules: [
        {
          field: 'price',
          ruleType: 'range',
          parameters: { min: 5, max: 100 },
          errorMessage: 'Price must be between $5 and $100 per lb',
          severity: 'warning'
        },
        {
          field: 'name',
          ruleType: 'species',
          parameters: { validateSpecies: true },
          errorMessage: 'Invalid or unrecognized species name',
          severity: 'warning'
        }
      ],
      businessRules: [
        {
          id: 'price_weight_check',
          description: 'Check if price per weight seems reasonable',
          condition: 'price > 0 && weight > 0 && (price / weight) > 200',
          action: 'warn',
          message: 'Price per pound calculation seems unusually high'
        }
      ],
      transformations: [
        {
          field: 'name',
          transformType: 'normalize',
          parameters: { removeExtra: true, standardizeCase: true },
          description: 'Normalize species names to standard format'
        }
      ],
      metadata: {
        supplierInfo: {
          name: 'Pacific Seafood',
          type: 'Wholesale Distributor',
          region: 'Pacific Northwest'
        },
        compliance: {
          standards: ['HACCP', 'GDST'],
          certifications: ['MSC', 'ASC']
        }
      },
      usage: {
        timesUsed: 0,
        successfulImports: 0,
        averageRecordsPerImport: 0,
        commonErrors: []
      }
    });

    // Dungeness Crab Processor Template
    this.addTemplate({
      id: 'dungeness_crab_processor',
      name: 'Dungeness Crab Processor',
      description: 'Template for Dungeness crab processing facilities',
      category: 'crab',
      sourceType: 'standard',
      version: '1.0',
      created: new Date().toISOString(),
      lastModified: new Date().toISOString(),
      author: 'System',
      isPublic: true,
      fileFormat: 'xlsx',
      hasHeader: true,
      encoding: 'UTF-8',
      fieldMappings: [
        {
          targetField: 'name',
          sourceColumn: 'Crab Grade',
          confidence: 0.9,
          mappingType: 'semantic',
          transformFunction: 'normalizeCrabGrade'
        },
        {
          targetField: 'weight',
          sourceColumn: 'Live Weight (lbs)',
          confidence: 1.0,
          mappingType: 'exact'
        },
        {
          targetField: 'batch_number',
          sourceColumn: 'Boat Ticket',
          confidence: 1.0,
          mappingType: 'exact'
        },
        {
          targetField: 'date',
          sourceColumn: 'Landing Date',
          confidence: 1.0,
          mappingType: 'exact'
        },
        {
          targetField: 'origin',
          sourceColumn: 'Fishing Area',
          confidence: 1.0,
          mappingType: 'exact'
        }
      ],
      requiredFields: ['name', 'weight', 'batch_number', 'date', 'origin'],
      optionalFields: ['vessel_id', 'fisherman', 'price'],
      validationRules: [
        {
          field: 'weight',
          ruleType: 'range',
          parameters: { min: 1.5, max: 4.0 },
          errorMessage: 'Crab weight should be between 1.5 and 4.0 lbs',
          severity: 'warning'
        },
        {
          field: 'origin',
          ruleType: 'pattern',
          parameters: { pattern: '^(Area|Zone)\\s+\\d+' },
          errorMessage: 'Origin should specify fishing area (e.g., Area 1, Zone 2)',
          severity: 'error'
        }
      ],
      businessRules: [
        {
          id: 'crab_season_check',
          description: 'Verify crab season dates',
          condition: 'date && (new Date(date).getMonth() < 11 || new Date(date).getMonth() > 2)',
          action: 'warn',
          message: 'Dungeness crab season is typically December-March'
        }
      ],
      transformations: [
        {
          field: 'name',
          transformType: 'lookup',
          parameters: { 
            lookupTable: {
              '#1': 'Dungeness Crab - #1 Grade',
              '#2': 'Dungeness Crab - #2 Grade',
              'Select': 'Dungeness Crab - Select Grade'
            }
          },
          description: 'Convert crab grades to full product names'
        }
      ],
      metadata: {
        supplierInfo: {
          name: 'Generic Crab Processor',
          type: 'Seafood Processor',
          region: 'Pacific Coast'
        },
        compliance: {
          standards: ['HACCP', 'GDST'],
          certifications: []
        }
      },
      usage: {
        timesUsed: 0,
        successfulImports: 0,
        averageRecordsPerImport: 0,
        commonErrors: []
      }
    });

    // Generic Salmon Import Template
    this.addTemplate({
      id: 'generic_salmon_import',
      name: 'Generic Salmon Import',
      description: 'Flexible template for various salmon import formats',
      category: 'salmon',
      sourceType: 'standard',
      version: '1.0',
      created: new Date().toISOString(),
      lastModified: new Date().toISOString(),
      author: 'System',
      isPublic: true,
      fileFormat: 'mixed',
      hasHeader: true,
      encoding: 'UTF-8',
      fieldMappings: [
        {
          targetField: 'name',
          sourceColumn: 'species',
          confidence: 0.8,
          mappingType: 'semantic',
          transformFunction: 'normalizeSalmonSpecies'
        },
        {
          targetField: 'weight',
          sourceColumn: 'weight_lbs',
          confidence: 0.9,
          mappingType: 'fuzzy'
        },
        {
          targetField: 'price',
          sourceColumn: 'price_per_lb',
          confidence: 0.9,
          mappingType: 'fuzzy'
        },
        {
          targetField: 'origin',
          sourceColumn: 'catch_location',
          confidence: 0.8,
          mappingType: 'semantic'
        }
      ],
      requiredFields: ['name', 'weight'],
      optionalFields: ['price', 'origin', 'catch_date', 'vessel_id'],
      validationRules: [
        {
          field: 'name',
          ruleType: 'species',
          parameters: { 
            allowedSpecies: ['chinook', 'coho', 'sockeye', 'pink', 'chum']
          },
          errorMessage: 'Must be a valid salmon species',
          severity: 'error'
        },
        {
          field: 'weight',
          ruleType: 'range',
          parameters: { min: 3, max: 60 },
          errorMessage: 'Salmon weight should be between 3 and 60 lbs',
          severity: 'warning'
        }
      ],
      businessRules: [
        {
          id: 'salmon_season_price',
          description: 'Check seasonal price variations',
          condition: 'price && weight && name.includes("chinook") && price < 15',
          action: 'flag',
          message: 'Chinook salmon price seems low - verify pricing'
        }
      ],
      transformations: [
        {
          field: 'name',
          transformType: 'normalize',
          parameters: { 
            standardizeSpecies: true,
            removeWildFarmedPrefix: true
          },
          description: 'Standardize salmon species names'
        }
      ],
      metadata: {
        compliance: {
          standards: ['GDST'],
          certifications: ['MSC', 'ASC']
        }
      },
      usage: {
        timesUsed: 0,
        successfulImports: 0,
        averageRecordsPerImport: 0,
        commonErrors: []
      }
    });

    // GDST Compliance Template
    this.addTemplate({
      id: 'gdst_compliance_export',
      name: 'GDST Compliance Export',
      description: 'Template for GDST-compliant traceability data export',
      category: 'regulatory',
      sourceType: 'regulatory',
      version: '1.0',
      created: new Date().toISOString(),
      lastModified: new Date().toISOString(),
      author: 'System',
      isPublic: true,
      fileFormat: 'json',
      hasHeader: false,
      encoding: 'UTF-8',
      fieldMappings: [
        {
          targetField: 'eventId',
          sourceColumn: 'event_id',
          confidence: 1.0,
          mappingType: 'exact'
        },
        {
          targetField: 'species',
          sourceColumn: 'scientific_name',
          confidence: 1.0,
          mappingType: 'exact'
        },
        {
          targetField: 'catchArea',
          sourceColumn: 'fao_area',
          confidence: 1.0,
          mappingType: 'exact'
        },
        {
          targetField: 'vesselId',
          sourceColumn: 'vessel_registration',
          confidence: 1.0,
          mappingType: 'exact'
        }
      ],
      requiredFields: ['eventId', 'species', 'catchArea', 'eventTime'],
      optionalFields: ['vesselId', 'landingPort', 'harvestDate'],
      validationRules: [
        {
          field: 'eventId',
          ruleType: 'pattern',
          parameters: { pattern: '^urn:' },
          errorMessage: 'Event ID must be a valid URN',
          severity: 'error'
        },
        {
          field: 'catchArea',
          ruleType: 'pattern',
          parameters: { pattern: '^\\d{2}$' },
          errorMessage: 'Catch area must be a 2-digit FAO code',
          severity: 'error'
        }
      ],
      businessRules: [],
      transformations: [
        {
          field: 'eventTime',
          transformType: 'format',
          parameters: { outputFormat: 'ISO8601' },
          description: 'Ensure event time is in ISO8601 format'
        }
      ],
      metadata: {
        compliance: {
          standards: ['GDST_2_0'],
          certifications: []
        }
      },
      usage: {
        timesUsed: 0,
        successfulImports: 0,
        averageRecordsPerImport: 0,
        commonErrors: []
      }
    });
  }

  /**
   * Find templates that match the provided data structure
   */
  findMatchingTemplates(
    headers: string[], 
    sampleData: Record<string, unknown>[], 
    fileMetadata?: { format: string; supplier?: string }
  ): TemplateMatchResult[] {
    const cacheKey = JSON.stringify({ headers, fileMetadata });
    if (this.templateCache.has(cacheKey)) {
      return this.templateCache.get(cacheKey)!;
    }

    const results: TemplateMatchResult[] = [];

    for (const template of this.templates.values()) {
      const matchResult = this.calculateTemplateMatch(template, headers, sampleData, fileMetadata);
      if (matchResult.confidence > 0.3) {
        results.push(matchResult);
      }
    }

    // Sort by confidence
    results.sort((a, b) => b.confidence - a.confidence);

    // Cache results
    this.templateCache.set(cacheKey, results);

    return results.slice(0, 5); // Return top 5 matches
  }

  /**
   * Calculate how well a template matches the provided data
   */
  private calculateTemplateMatch(
    template: ImportTemplate,
    headers: string[],
    sampleData: Record<string, unknown>[],
    fileMetadata?: { format: string; supplier?: string }
  ): TemplateMatchResult {
    let confidence = 0;
    const matchReasons: string[] = [];
    const suggestedModifications: string[] = [];

    // File format match
    if (fileMetadata?.format === template.fileFormat || template.fileFormat === 'mixed') {
      confidence += 0.2;
      matchReasons.push(`File format matches (${fileMetadata?.format})`);
    }

    // Header matching
    const mappedHeaders = template.fieldMappings.map(m => m.sourceColumn.toLowerCase());
    const dataHeaders = headers.map(h => h.toLowerCase());
    
    const headerMatches = mappedHeaders.filter(h => dataHeaders.includes(h)).length;
    const headerMatchRatio = mappedHeaders.length > 0 ? headerMatches / mappedHeaders.length : 0;
    
    confidence += headerMatchRatio * 0.4;
    if (headerMatchRatio > 0.7) {
      matchReasons.push(`High header match ratio (${Math.round(headerMatchRatio * 100)}%)`);
    } else if (headerMatchRatio > 0.3) {
      matchReasons.push(`Moderate header match ratio (${Math.round(headerMatchRatio * 100)}%)`);
      suggestedModifications.push('Some column mappings may need adjustment');
    }

    // Supplier/source matching
    if (fileMetadata?.supplier && template.metadata.supplierInfo?.name) {
      const supplierSimilarity = this.calculateStringSimilarity(
        fileMetadata.supplier.toLowerCase(),
        template.metadata.supplierInfo.name.toLowerCase()
      );
      if (supplierSimilarity > 0.8) {
        confidence += 0.3;
        matchReasons.push(`Supplier name matches closely`);
      } else if (supplierSimilarity > 0.5) {
        confidence += 0.1;
        matchReasons.push(`Supplier name partially matches`);
      }
    }

    // Data pattern matching
    if (sampleData.length > 0) {
      const dataPatternScore = this.analyzeDataPatterns(template, sampleData);
      confidence += dataPatternScore * 0.1;
      if (dataPatternScore > 0.7) {
        matchReasons.push(`Data patterns match template expectations`);
      }
    }

    // Required fields coverage
    const requiredFieldsCovered = template.requiredFields.filter(field =>
      template.fieldMappings.some(mapping => 
        mapping.targetField === field && 
        dataHeaders.includes(mapping.sourceColumn.toLowerCase())
      )
    ).length;
    
    const requiredCoverageRatio = template.requiredFields.length > 0 
      ? requiredFieldsCovered / template.requiredFields.length 
      : 1;
    
    confidence += requiredCoverageRatio * 0.2;
    if (requiredCoverageRatio < 1) {
      suggestedModifications.push(`${template.requiredFields.length - requiredFieldsCovered} required fields need mapping`);
    }

    // Category-specific bonus
    if (this.detectDataCategory(sampleData) === template.category) {
      confidence += 0.1;
      matchReasons.push(`Data category matches template category (${template.category})`);
    }

    return {
      template,
      confidence: Math.min(confidence, 1.0),
      matchReasons,
      suggestedModifications: suggestedModifications.length > 0 ? suggestedModifications : undefined
    };
  }

  /**
   * Apply template to data and validate
   */
  applyTemplate(
    template: ImportTemplate,
    data: Record<string, unknown>[]
  ): TemplateValidationResult {
    const errors: any[] = [];
    const warnings: any[] = [];
    const corrections: any[] = [];

    // Apply field mappings
    const transformedData = data.map((row, index) => {
      const newRow: Record<string, unknown> = { ...row };
      
      for (const mapping of template.fieldMappings) {
        const sourceValue = row[mapping.sourceColumn];
        if (sourceValue !== undefined) {
          newRow[mapping.targetField] = sourceValue;
          
          // Apply transformations if specified
          if (mapping.transformFunction) {
            const transformation = template.transformations.find(t => 
              t.field === mapping.targetField && 
              t.transformType === this.getTransformationType(mapping.transformFunction!)
            );
            if (transformation) {
              try {
                newRow[mapping.targetField] = this.applyTransformation(sourceValue, transformation);
              } catch (error) {
                errors.push({
                  field: mapping.targetField,
                  row: index + 1,
                  code: 'TRANSFORMATION_ERROR',
                  message: `Failed to apply transformation: ${error instanceof Error ? error.message : 'Unknown error'}`,
                  severity: 'medium' as const
                });
              }
            }
          }
        }
      }
      
      return newRow;
    });

    // Validate against template rules
    let requiredFieldsCovered = 0;
    let optionalFieldsMatched = 0;
    let validationRulesPassed = 0;
    let businessRulesPassed = 0;

    for (let i = 0; i < transformedData.length; i++) {
      const row = transformedData[i];
      const rowNumber = i + 1;

      // Check required fields
      for (const requiredField of template.requiredFields) {
        if (row[requiredField] !== undefined && row[requiredField] !== null && row[requiredField] !== '') {
          requiredFieldsCovered++;
        } else {
          errors.push({
            field: requiredField,
            row: rowNumber,
            code: 'MISSING_REQUIRED_FIELD',
            message: `Required field '${requiredField}' is missing or empty`,
            severity: 'high' as const
          });
        }
      }

      // Check optional fields
      for (const optionalField of template.optionalFields) {
        if (row[optionalField] !== undefined && row[optionalField] !== null && row[optionalField] !== '') {
          optionalFieldsMatched++;
        }
      }

      // Apply validation rules
      for (const rule of template.validationRules) {
        const validationResult = this.applyValidationRule(row, rule, rowNumber);
        if (validationResult.passed) {
          validationRulesPassed++;
        } else {
          const errorObj = {
            field: rule.field,
            row: rowNumber,
            code: `VALIDATION_${rule.ruleType.toUpperCase()}`,
            message: validationResult.message || rule.errorMessage,
            severity: rule.severity as 'critical' | 'high' | 'medium' | 'low'
          };
          
          if (rule.severity === 'error') {
            errors.push(errorObj);
          } else {
            warnings.push({
              ...errorObj,
              impact: 'data_quality' as const
            });
          }
        }
      }

      // Apply business rules
      for (const businessRule of template.businessRules) {
        try {
          const conditionResult = this.evaluateCondition(businessRule.condition, row);
          if (conditionResult) {
            businessRulesPassed++;
            
            if (businessRule.action === 'warn' || businessRule.action === 'flag') {
              warnings.push({
                field: 'business_rule',
                row: rowNumber,
                code: businessRule.id.toUpperCase(),
                message: businessRule.message,
                impact: 'compliance' as const
              });
            } else if (businessRule.action === 'reject') {
              errors.push({
                field: 'business_rule',
                row: rowNumber,
                code: businessRule.id.toUpperCase(),
                message: businessRule.message,
                severity: 'high' as const
              });
            }
          }
        } catch (error) {
          warnings.push({
            field: 'business_rule',
            row: rowNumber,
            code: 'BUSINESS_RULE_ERROR',
            message: `Business rule evaluation failed: ${businessRule.id}`,
            impact: 'performance' as const
          });
        }
      }
    }

    // Generate suggestions
    const suggestions: string[] = [];
    if (requiredFieldsCovered < template.requiredFields.length * data.length) {
      suggestions.push('Some required fields are missing. Check your column mappings.');
    }
    if (validationRulesPassed < template.validationRules.length * data.length * 0.9) {
      suggestions.push('Many validation rules are failing. Review your data quality.');
    }
    if (errors.length > data.length * 0.1) {
      suggestions.push('High error rate detected. Consider using a different template.');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      corrections,
      templateCompliance: {
        requiredFieldsCovered,
        optionalFieldsMatched,
        validationRulesPassed,
        businessRulesPassed
      },
      suggestions
    };
  }

  /**
   * Create a custom template from user input
   */
  createCustomTemplate(
    name: string,
    description: string,
    category: TemplateCategory,
    fieldMappings: SeafoodMappingRule[],
    options: Partial<ImportTemplate> = {}
  ): ImportTemplate {
    const templateId = `custom_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const template: ImportTemplate = {
      id: templateId,
      name,
      description,
      category,
      sourceType: 'custom',
      version: '1.0',
      created: new Date().toISOString(),
      lastModified: new Date().toISOString(),
      author: 'User',
      isPublic: false,
      fileFormat: 'csv',
      hasHeader: true,
      encoding: 'UTF-8',
      fieldMappings,
      requiredFields: fieldMappings.filter(m => m.validationRules?.includes('required')).map(m => m.targetField),
      optionalFields: fieldMappings.filter(m => !m.validationRules?.includes('required')).map(m => m.targetField),
      validationRules: [],
      businessRules: [],
      transformations: [],
      metadata: {},
      usage: {
        timesUsed: 0,
        successfulImports: 0,
        averageRecordsPerImport: 0,
        commonErrors: []
      },
      ...options
    };

    this.addTemplate(template);
    return template;
  }

  /**
   * Get template by ID
   */
  getTemplate(id: string): ImportTemplate | undefined {
    return this.templates.get(id);
  }

  /**
   * Get all templates filtered by category
   */
  getTemplatesByCategory(category: TemplateCategory): ImportTemplate[] {
    return Array.from(this.templates.values()).filter(t => t.category === category);
  }

  /**
   * Get public templates
   */
  getPublicTemplates(): ImportTemplate[] {
    return Array.from(this.templates.values()).filter(t => t.isPublic);
  }

  /**
   * Add or update template
   */
  addTemplate(template: ImportTemplate): void {
    this.templates.set(template.id, template);
    this.templateCache.clear(); // Clear cache when templates change
  }

  /**
   * Update template usage statistics
   */
  updateTemplateUsage(templateId: string, recordCount: number, errors: string[] = []): void {
    const template = this.templates.get(templateId);
    if (!template) return;

    template.usage.timesUsed++;
    template.usage.lastUsed = new Date().toISOString();
    template.usage.averageRecordsPerImport = 
      (template.usage.averageRecordsPerImport * (template.usage.timesUsed - 1) + recordCount) / template.usage.timesUsed;
    
    if (errors.length === 0) {
      template.usage.successfulImports++;
    }

    // Update common errors
    for (const error of errors) {
      const existingError = template.usage.commonErrors.find(e => e.error === error);
      if (existingError) {
        existingError.frequency++;
      } else {
        template.usage.commonErrors.push({ error, frequency: 1 });
      }
    }

    // Keep only top 10 most common errors
    template.usage.commonErrors.sort((a, b) => b.frequency - a.frequency);
    template.usage.commonErrors = template.usage.commonErrors.slice(0, 10);

    template.lastModified = new Date().toISOString();
  }

  // Utility methods
  private calculateStringSimilarity(str1: string, str2: string): number {
    const matrix: number[][] = [];
    const len1 = str1.length;
    const len2 = str2.length;

    for (let i = 0; i <= len1; i++) matrix[i] = [i];
    for (let j = 0; j <= len2; j++) matrix[0][j] = j;

    for (let i = 1; i <= len1; i++) {
      for (let j = 1; j <= len2; j++) {
        const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j - 1] + cost
        );
      }
    }

    const maxLen = Math.max(len1, len2);
    return maxLen === 0 ? 1 : (maxLen - matrix[len1][len2]) / maxLen;
  }

  private analyzeDataPatterns(template: ImportTemplate, sampleData: Record<string, unknown>[]): number {
    // Simplified pattern analysis - in production this would be more sophisticated
    let score = 0;
    let checks = 0;

    // Check for expected data types
    for (const mapping of template.fieldMappings) {
      const sourceValues = sampleData.map(row => row[mapping.sourceColumn]).filter(v => v != null);
      if (sourceValues.length > 0) {
        checks++;
        
        // Basic type checking based on target field
        if (mapping.targetField === 'price' && sourceValues.every(v => !isNaN(Number(v)))) {
          score++;
        } else if (mapping.targetField === 'date' && sourceValues.every(v => !isNaN(Date.parse(String(v))))) {
          score++;
        } else if (mapping.targetField === 'name' && sourceValues.every(v => typeof v === 'string' && String(v).length > 2)) {
          score++;
        } else {
          score += 0.5; // Partial credit for having data
        }
      }
    }

    return checks > 0 ? score / checks : 0;
  }

  private detectDataCategory(sampleData: Record<string, unknown>[]): TemplateCategory {
    // Simple category detection based on common patterns
    const allText = JSON.stringify(sampleData).toLowerCase();
    
    if (allText.includes('salmon') || allText.includes('chinook') || allText.includes('coho')) return 'salmon';
    if (allText.includes('crab') || allText.includes('dungeness')) return 'crab';
    if (allText.includes('halibut')) return 'halibut';
    if (allText.includes('wholesale') || allText.includes('distributor')) return 'wholesale';
    if (allText.includes('restaurant') || allText.includes('menu')) return 'restaurant';
    
    return 'mixed_seafood';
  }

  private getTransformationType(transformFunction: string): TemplateTransformation['transformType'] {
    const mapping: Record<string, TemplateTransformation['transformType']> = {
      'parsePrice': 'convert',
      'parseWeight': 'convert',
      'normalizeCrabGrade': 'normalize',
      'normalizeSalmonSpecies': 'normalize',
      'standardizeCase': 'format'
    };
    return mapping[transformFunction] || 'normalize';
  }

  private applyTransformation(value: unknown, transformation: TemplateTransformation): unknown {
    // Simplified transformation application
    switch (transformation.transformType) {
      case 'normalize':
        return String(value).trim().toLowerCase();
      case 'convert':
        return Number(value) || 0;
      case 'format':
        return String(value).trim();
      default:
        return value;
    }
  }

  private applyValidationRule(
    row: Record<string, unknown>, 
    rule: TemplateValidationRule, 
    rowNumber: number
  ): { passed: boolean; message?: string } {
    const value = row[rule.field];
    
    switch (rule.ruleType) {
      case 'required':
        return { 
          passed: value !== undefined && value !== null && value !== '',
          message: value === undefined ? 'Field is missing' : 'Field is empty'
        };
      
      case 'range':
        const num = Number(value);
        const min = rule.parameters.min as number;
        const max = rule.parameters.max as number;
        return { 
          passed: !isNaN(num) && num >= min && num <= max,
          message: `Value ${num} is outside range ${min}-${max}`
        };
      
      case 'pattern':
        const pattern = new RegExp(rule.parameters.pattern as string);
        return { 
          passed: pattern.test(String(value)),
          message: `Value does not match required pattern`
        };
      
      default:
        return { passed: true };
    }
  }

  private evaluateCondition(condition: string, row: Record<string, unknown>): boolean {
    // Simplified condition evaluation - in production, use a safe expression evaluator
    try {
      // Create a safe context for evaluation
      const context = { ...row };
      const func = new Function(...Object.keys(context), `return ${condition}`);
      return Boolean(func(...Object.values(context)));
    } catch {
      return false;
    }
  }
}