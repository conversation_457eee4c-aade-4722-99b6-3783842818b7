/**
 * Streaming CSV Processor Manager
 * Manages Web Worker communication for large file processing
 * Optimized for seafood industry datasets with performance monitoring
 */

import type {
  ProcessingConfig,
  ProcessingProgress,
  ProcessingResult,
  WorkerMessage
} from '../../workers/csvProcessor.worker';

export interface StreamingProcessorOptions {
  chunkSize?: number;
  maxFileSize?: number; // in bytes
  timeout?: number; // in milliseconds
  onProgress?: (progress: ProcessingProgress) => void;
  onError?: (error: Error) => void;
  onComplete?: (result: ProcessingResult) => void;
  onCancel?: () => void;
}

export interface FileValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  fileInfo: {
    name: string;
    size: number;
    type: string;
    lastModified: number;
  };
}

export class StreamingProcessor {
  private worker: Worker | null = null;
  private isProcessing = false;
  private options: StreamingProcessorOptions;
  private startTime = 0;
  private currentFile: File | null = null;

  // Performance constants
  private static readonly DEFAULT_CHUNK_SIZE = 1000;
  private static readonly MAX_FILE_SIZE = 500 * 1024 * 1024; // 500MB
  private static readonly PROCESSING_TIMEOUT = 10 * 60 * 1000; // 10 minutes
  private static readonly LARGE_FILE_THRESHOLD = 10 * 1024 * 1024; // 10MB

  constructor(options: StreamingProcessorOptions = {}) {
    this.options = {
      chunkSize: StreamingProcessor.DEFAULT_CHUNK_SIZE,
      maxFileSize: StreamingProcessor.MAX_FILE_SIZE,
      timeout: StreamingProcessor.PROCESSING_TIMEOUT,
      ...options
    };
  }

  /**
   * Validate file before processing
   */
  validateFile(file: File): FileValidation {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check file size
    if (file.size === 0) {
      errors.push('File is empty');
    } else if (file.size > (this.options.maxFileSize || StreamingProcessor.MAX_FILE_SIZE)) {
      errors.push(`File size (${this.formatBytes(file.size)}) exceeds maximum allowed (${this.formatBytes(this.options.maxFileSize || StreamingProcessor.MAX_FILE_SIZE)})`);
    } else if (file.size > StreamingProcessor.LARGE_FILE_THRESHOLD) {
      warnings.push(`Large file detected (${this.formatBytes(file.size)}). Processing may take several minutes.`);
    }

    // Check file type
    const allowedTypes = [
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/tab-separated-values',
      'text/plain'
    ];

    if (!allowedTypes.includes(file.type) && !file.name.match(/\.(csv|xlsx|xls|tsv|txt)$/i)) {
      errors.push(`Unsupported file type: ${file.type || 'unknown'}. Supported formats: CSV, Excel, TSV`);
    }

    // Check file name
    if (!file.name || file.name.length === 0) {
      warnings.push('File has no name');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      fileInfo: {
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified
      }
    };
  }

  /**
   * Process file with streaming support
   */
  async processFile(file: File, config: Partial<ProcessingConfig> = {}): Promise<ProcessingResult> {
    if (this.isProcessing) {
      throw new Error('Another file is already being processed');
    }

    // Validate file
    const validation = this.validateFile(file);
    if (!validation.isValid) {
      throw new Error(`File validation failed: ${validation.errors.join(', ')}`);
    }

    // Show warnings
    if (validation.warnings.length > 0 && this.options.onError) {
      console.warn('File processing warnings:', validation.warnings);
    }

    this.currentFile = file;
    this.isProcessing = true;
    this.startTime = Date.now();

    try {
      // Initialize worker
      await this.initializeWorker();

      // Prepare processing configuration
      const processingConfig: ProcessingConfig = {
        chunkSize: config.chunkSize || this.options.chunkSize || StreamingProcessor.DEFAULT_CHUNK_SIZE,
        hasHeader: config.hasHeader ?? true,
        delimiter: config.delimiter || this.detectDelimiter(file.name),
        encoding: config.encoding || 'UTF-8',
        skipEmptyLines: config.skipEmptyLines ?? true
      };

      // Start processing
      const result = await this.processWithWorker(file, processingConfig);

      // Add performance metrics
      const processingTime = Date.now() - this.startTime;
      const performanceMetrics = this.calculatePerformanceMetrics(result, processingTime);

      return {
        ...result,
        ...performanceMetrics
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown processing error';
      const processingError = new Error(`File processing failed: ${errorMessage}`);
      
      if (this.options.onError) {
        this.options.onError(processingError);
      }
      
      throw processingError;
    } finally {
      this.cleanup();
    }
  }

  /**
   * Cancel current processing
   */
  cancelProcessing(): void {
    if (!this.isProcessing || !this.worker) {
      return;
    }

    this.worker.postMessage({ type: 'cancel' });
    
    if (this.options.onCancel) {
      this.options.onCancel();
    }

    this.cleanup();
  }

  /**
   * Get processing recommendations based on file characteristics
   */
  getProcessingRecommendations(file: File): {
    recommendedChunkSize: number;
    estimatedProcessingTime: number; // in seconds
    memoryUsage: 'low' | 'medium' | 'high';
    recommendations: string[];
  } {
    const recommendations: string[] = [];
    let recommendedChunkSize = StreamingProcessor.DEFAULT_CHUNK_SIZE;
    let memoryUsage: 'low' | 'medium' | 'high' = 'low';

    // Estimate rows based on file size (rough estimation)
    const estimatedRows = Math.ceil(file.size / 100);
    const estimatedProcessingTime = Math.ceil(estimatedRows / 1000); // 1000 rows per second

    if (file.size > 100 * 1024 * 1024) { // 100MB
      recommendedChunkSize = 500; // Smaller chunks for very large files
      memoryUsage = 'high';
      recommendations.push('Use smaller chunk size for better memory management');
      recommendations.push('Consider processing during off-peak hours');
    } else if (file.size > 50 * 1024 * 1024) { // 50MB
      recommendedChunkSize = 750;
      memoryUsage = 'medium';
      recommendations.push('Monitor memory usage during processing');
    } else if (file.size > 10 * 1024 * 1024) { // 10MB
      recommendedChunkSize = 1000;
      memoryUsage = 'medium';
      recommendations.push('Streaming processing will be used');
    } else {
      recommendedChunkSize = 2000; // Larger chunks for small files
      memoryUsage = 'low';
      recommendations.push('Fast processing expected');
    }

    return {
      recommendedChunkSize,
      estimatedProcessingTime,
      memoryUsage,
      recommendations
    };
  }

  private async initializeWorker(): Promise<void> {
    if (this.worker) {
      this.worker.terminate();
    }

    // Create worker from the TypeScript file
    // Note: In production, this would be the compiled JS file
    this.worker = new Worker(
      new URL('../../workers/csvProcessor.worker.ts', import.meta.url),
      { type: 'module' }
    );

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Worker initialization timeout'));
      }, 5000);

      this.worker!.onmessage = (event: MessageEvent<WorkerMessage>) => {
        clearTimeout(timeout);
        resolve();
      };

      this.worker!.onerror = (error) => {
        clearTimeout(timeout);
        reject(new Error(`Worker initialization failed: ${error.message}`));
      };

      // Send a test message to ensure worker is ready
      this.worker!.postMessage({ type: 'start', data: null });
    });
  }

  private async processWithWorker(file: File, config: ProcessingConfig): Promise<ProcessingResult> {
    if (!this.worker) {
      throw new Error('Worker not initialized');
    }

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`Processing timeout after ${this.options.timeout}ms`));
      }, this.options.timeout);

      this.worker!.onmessage = (event: MessageEvent<WorkerMessage>) => {
        const { type, data } = event.data;

        switch (type) {
          case 'progress':
            if (this.options.onProgress) {
              this.options.onProgress(data as ProcessingProgress);
            }
            break;

          case 'complete':
            clearTimeout(timeout);
            if (this.options.onComplete) {
              this.options.onComplete(data as ProcessingResult);
            }
            resolve(data as ProcessingResult);
            break;

          case 'error':
            clearTimeout(timeout);
            const error = new Error((data as any)?.message || 'Worker processing error');
            if (this.options.onError) {
              this.options.onError(error);
            }
            reject(error);
            break;

          case 'cancel':
            clearTimeout(timeout);
            if (this.options.onCancel) {
              this.options.onCancel();
            }
            reject(new Error('Processing was cancelled'));
            break;

          default:
            console.warn('Unknown worker message type:', type);
        }
      };

      this.worker!.onerror = (error) => {
        clearTimeout(timeout);
        const workerError = new Error(`Worker error: ${error.message}`);
        if (this.options.onError) {
          this.options.onError(workerError);
        }
        reject(workerError);
      };

      // Start processing
      this.worker!.postMessage({
        type: 'start',
        data: { file, config }
      });
    });
  }

  private detectDelimiter(filename: string): string {
    const extension = filename.toLowerCase().split('.').pop();
    switch (extension) {
      case 'tsv':
        return '\t';
      case 'csv':
      default:
        return ',';
    }
  }

  private calculatePerformanceMetrics(result: ProcessingResult, totalTime: number) {
    const rowsPerSecond = result.totalRows / (totalTime / 1000);
    const bytesPerSecond = this.currentFile ? this.currentFile.size / (totalTime / 1000) : 0;

    return {
      performanceMetrics: {
        totalProcessingTime: totalTime,
        rowsPerSecond: Math.round(rowsPerSecond),
        bytesPerSecond: Math.round(bytesPerSecond),
        memoryEfficiency: result.memoryUsage < 100 * 1024 * 1024 ? 'excellent' : 
                          result.memoryUsage < 250 * 1024 * 1024 ? 'good' : 'fair',
        errorRate: result.errors.length / result.totalRows,
        recommendedForSimilarFiles: this.getProcessingRecommendations(this.currentFile!).recommendedChunkSize
      }
    };
  }

  private formatBytes(bytes: number): string {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${Math.round(bytes / Math.pow(1024, i) * 100) / 100  } ${  sizes[i]}`;
  }

  private cleanup(): void {
    this.isProcessing = false;
    this.currentFile = null;
    
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }
  }

  /**
   * Static utility to check if streaming processing is needed
   */
  static shouldUseStreaming(file: File): boolean {
    return file.size > StreamingProcessor.LARGE_FILE_THRESHOLD;
  }

  /**
   * Static utility to get file processing estimate
   */
  static getProcessingEstimate(file: File): {
    estimatedRows: number;
    estimatedTime: number;
    recommendedApproach: 'standard' | 'streaming';
  } {
    const estimatedRows = Math.ceil(file.size / 100);
    const estimatedTime = Math.ceil(estimatedRows / 1000);
    const recommendedApproach = file.size > StreamingProcessor.LARGE_FILE_THRESHOLD ? 'streaming' : 'standard';

    return {
      estimatedRows,
      estimatedTime,
      recommendedApproach
    };
  }
}