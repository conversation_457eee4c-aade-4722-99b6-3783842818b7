/**
 * Multi-Format File Processor
 * Unified processing pipeline for CSV, XLSX, TSV, JSON, and XML formats
 * Optimized for seafood industry data with consistent output format
 */

import Papa from 'papapar<PERSON>';
import * as XLSX from 'xlsx';
import { StreamingProcessor, type StreamingProcessorOptions } from './streamingProcessor';

export type SupportedFormat = 'csv' | 'xlsx' | 'xls' | 'tsv' | 'json' | 'xml' | 'txt';

export interface FormatProcessingOptions {
  format?: SupportedFormat;
  encoding?: string;
  hasHeader?: boolean;
  delimiter?: string;
  sheetName?: string; // For Excel files
  jsonPath?: string; // JSON path for nested data
  xmlRootElement?: string; // XML root element
  skipRows?: number;
  maxRows?: number;
  dateFormat?: string;
  streaming?: boolean;
  streamingOptions?: StreamingProcessorOptions;
}

export interface ProcessedFileData {
  data: Record<string, unknown>[];
  headers: string[];
  metadata: FileMetadata;
  format: SupportedFormat;
  processingStats: ProcessingStats;
}

export interface FileMetadata {
  fileName: string;
  fileSize: number;
  originalFormat: SupportedFormat;
  encoding?: string;
  sheetNames?: string[]; // For Excel files
  totalSheets?: number;
  processedSheet?: string;
  rowCount: number;
  columnCount: number;
  hasHeader: boolean;
  firstRow?: Record<string, unknown>;
  lastModified?: number;
}

export interface ProcessingStats {
  startTime: number;
  endTime: number;
  processingTime: number;
  rowsPerSecond: number;
  bytesProcessed: number;
  errorsEncountered: number;
  warningsGenerated: number;
  memoryUsed: number;
  conversionApplied?: string;
}

export interface FormatDetectionResult {
  detectedFormat: SupportedFormat;
  confidence: number;
  reasons: string[];
  alternatives: Array<{ format: SupportedFormat; confidence: number }>;
}

export class MultiFormatProcessor {
  private static readonly FORMAT_SIGNATURES = {
    xlsx: [0x50, 0x4B, 0x03, 0x04], // ZIP signature (Excel is ZIP-based)
    xls: [0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1], // OLE2 signature
    csv: null, // Detected by content analysis
    tsv: null, // Detected by content analysis
    json: null, // Detected by content analysis
    xml: null, // Detected by content analysis
    txt: null // Default fallback
  };

  private static readonly COMMON_DELIMITERS = [',', '\t', ';', '|', ':', ' '];
  
  private static readonly SEAFOOD_JSON_PATHS = [
    '$.products',
    '$.inventory',
    '$.items',
    '$.data',
    '$.records',
    '$.results'
  ];

  private static readonly XML_ROOT_ELEMENTS = [
    'products',
    'inventory',
    'items',
    'catalog',
    'data',
    'records'
  ];

  /**
   * Automatically detect file format
   */
  static detectFormat(file: File): FormatDetectionResult {
    const fileName = file.name.toLowerCase();
    const fileExtension = fileName.split('.').pop() || '';
    
    // Primary detection by file extension
    const extensionFormat = this.getFormatFromExtension(fileExtension);
    if (extensionFormat) {
      return {
        detectedFormat: extensionFormat,
        confidence: 0.9,
        reasons: [`File extension .${fileExtension} indicates ${extensionFormat.toUpperCase()} format`],
        alternatives: []
      };
    }

    // Secondary detection by MIME type
    const mimeFormat = this.getFormatFromMimeType(file.type);
    if (mimeFormat) {
      return {
        detectedFormat: mimeFormat,
        confidence: 0.8,
        reasons: [`MIME type ${file.type} indicates ${mimeFormat.toUpperCase()} format`],
        alternatives: []
      };
    }

    // Fallback detection by file content (would require reading the file)
    return {
      detectedFormat: 'csv',
      confidence: 0.3,
      reasons: ['Unable to determine format from extension or MIME type, defaulting to CSV'],
      alternatives: [
        { format: 'tsv', confidence: 0.2 },
        { format: 'txt', confidence: 0.1 }
      ]
    };
  }

  /**
   * Process file with automatic format detection and conversion
   */
  static async processFile(
    file: File, 
    options: FormatProcessingOptions = {}
  ): Promise<ProcessedFileData> {
    const startTime = Date.now();
    
    try {
      // Detect format if not specified
      const format = options.format || this.detectFormat(file).detectedFormat;
      
      // Choose processing strategy based on format and file size
      const shouldUseStreaming = options.streaming || 
        (format === 'csv' && file.size > 10 * 1024 * 1024); // 10MB threshold

      let processedData: ProcessedFileData;

      if (shouldUseStreaming && format === 'csv') {
        processedData = await this.processWithStreaming(file, options);
      } else {
        processedData = await this.processStandard(file, format, options);
      }

      // Add processing statistics
      const endTime = Date.now();
      processedData.processingStats = {
        ...processedData.processingStats,
        startTime,
        endTime,
        processingTime: endTime - startTime,
        rowsPerSecond: processedData.data.length / ((endTime - startTime) / 1000),
        bytesProcessed: file.size
      };

      return processedData;

    } catch (error) {
      throw new Error(`Multi-format processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process using streaming for large files
   */
  private static async processWithStreaming(
    file: File,
    options: FormatProcessingOptions
  ): Promise<ProcessedFileData> {
    const processor = new StreamingProcessor(options.streamingOptions);
    
    const result = await processor.processFile(file, {
      chunkSize: 1000,
      hasHeader: options.hasHeader ?? true,
      delimiter: options.delimiter,
      encoding: options.encoding,
      skipEmptyLines: true
    });

    return {
      data: result.data,
      headers: result.headers,
      format: 'csv' as SupportedFormat,
      metadata: {
        fileName: file.name,
        fileSize: file.size,
        originalFormat: 'csv',
        encoding: options.encoding,
        rowCount: result.totalRows,
        columnCount: result.headers.length,
        hasHeader: options.hasHeader ?? true,
        lastModified: file.lastModified
      },
      processingStats: {
        startTime: 0, // Will be set by caller
        endTime: 0,
        processingTime: result.processingTime,
        rowsPerSecond: 0,
        bytesProcessed: file.size,
        errorsEncountered: result.errors.length,
        warningsGenerated: 0,
        memoryUsed: result.memoryUsage
      }
    };
  }

  /**
   * Standard processing for smaller files and non-CSV formats
   */
  private static async processStandard(
    file: File,
    format: SupportedFormat,
    options: FormatProcessingOptions
  ): Promise<ProcessedFileData> {
    switch (format) {
      case 'csv':
        return this.processCSV(file, options);
      case 'xlsx':
      case 'xls':
        return this.processExcel(file, options);
      case 'tsv':
        return this.processTSV(file, options);
      case 'json':
        return this.processJSON(file, options);
      case 'xml':
        return this.processXML(file, options);
      case 'txt':
        return this.processText(file, options);
      default:
        throw new Error(`Unsupported format: ${format}`);
    }
  }

  /**
   * Process CSV files
   */
  private static async processCSV(
    file: File,
    options: FormatProcessingOptions
  ): Promise<ProcessedFileData> {
    const text = await file.text();
    
    return new Promise((resolve, reject) => {
      Papa.parse(text, {
        header: options.hasHeader ?? true,
        delimiter: options.delimiter || this.detectDelimiter(text),
        skipEmptyLines: true,
        dynamicTyping: false, // Keep as strings for validation
        encoding: options.encoding || 'UTF-8',
        complete: (results) => {
          const headers = results.meta.fields || [];
          const data = results.data as Record<string, unknown>[];
          
          // Apply row limits if specified
          const processedData = this.applyRowLimits(data, options);
          
          resolve({
            data: processedData,
            headers,
            format: 'csv',
            metadata: {
              fileName: file.name,
              fileSize: file.size,
              originalFormat: 'csv',
              encoding: options.encoding,
              rowCount: processedData.length,
              columnCount: headers.length,
              hasHeader: options.hasHeader ?? true,
              firstRow: processedData[0],
              lastModified: file.lastModified
            },
            processingStats: {
              startTime: 0,
              endTime: 0,
              processingTime: 0,
              rowsPerSecond: 0,
              bytesProcessed: file.size,
              errorsEncountered: results.errors.length,
              warningsGenerated: 0,
              memoryUsed: this.estimateMemoryUsage(processedData)
            }
          });
        },
        error: (error) => {
          reject(new Error(`CSV parsing failed: ${error.message}`));
        }
      });
    });
  }

  /**
   * Process Excel files (XLSX/XLS)
   */
  private static async processExcel(
    file: File,
    options: FormatProcessingOptions
  ): Promise<ProcessedFileData> {
    const arrayBuffer = await file.arrayBuffer();
    const workbook = XLSX.read(arrayBuffer, { type: 'array' });
    
    // Select sheet to process
    const sheetName = options.sheetName || workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    
    if (!worksheet) {
      throw new Error(`Sheet "${sheetName}" not found in workbook`);
    }

    // Convert to JSON
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
      header: options.hasHeader ? 1 : 'A',
      defval: '',
      blankrows: false
    });

    const headers = options.hasHeader 
      ? Object.keys(jsonData[0] || {})
      : this.generateHeaders(Object.keys(jsonData[0] || {}).length);

    const data = this.applyRowLimits(jsonData as Record<string, unknown>[], options);

    return {
      data,
      headers,
      format: file.name.toLowerCase().endsWith('.xlsx') ? 'xlsx' : 'xls',
      metadata: {
        fileName: file.name,
        fileSize: file.size,
        originalFormat: file.name.toLowerCase().endsWith('.xlsx') ? 'xlsx' : 'xls',
        sheetNames: workbook.SheetNames,
        totalSheets: workbook.SheetNames.length,
        processedSheet: sheetName,
        rowCount: data.length,
        columnCount: headers.length,
        hasHeader: options.hasHeader ?? true,
        firstRow: data[0],
        lastModified: file.lastModified
      },
      processingStats: {
        startTime: 0,
        endTime: 0,
        processingTime: 0,
        rowsPerSecond: 0,
        bytesProcessed: file.size,
        errorsEncountered: 0,
        warningsGenerated: 0,
        memoryUsed: this.estimateMemoryUsage(data),
        conversionApplied: 'Excel to JSON conversion'
      }
    };
  }

  /**
   * Process TSV (Tab-Separated Values) files
   */
  private static async processTSV(
    file: File,
    options: FormatProcessingOptions
  ): Promise<ProcessedFileData> {
    const csvResult = await this.processCSV(file, {
      ...options,
      delimiter: '\t'
    });
    
    return {
      ...csvResult,
      format: 'tsv',
      metadata: {
        ...csvResult.metadata,
        originalFormat: 'tsv'
      }
    };
  }

  /**
   * Process JSON files
   */
  private static async processJSON(
    file: File,
    options: FormatProcessingOptions
  ): Promise<ProcessedFileData> {
    const text = await file.text();
    
    try {
      const jsonData = JSON.parse(text);
      let data: Record<string, unknown>[];

      // Handle different JSON structures
      if (Array.isArray(jsonData)) {
        data = jsonData;
      } else if (options.jsonPath) {
        data = this.extractFromJsonPath(jsonData, options.jsonPath);
      } else {
        // Try common seafood JSON paths
        data = this.findDataInJson(jsonData);
      }

      if (!Array.isArray(data)) {
        throw new Error('Unable to extract array data from JSON structure');
      }

      const headers = data.length > 0 ? Object.keys(data[0]) : [];
      const processedData = this.applyRowLimits(data, options);

      return {
        data: processedData,
        headers,
        format: 'json',
        metadata: {
          fileName: file.name,
          fileSize: file.size,
          originalFormat: 'json',
          rowCount: processedData.length,
          columnCount: headers.length,
          hasHeader: true, // JSON objects have keys as headers
          firstRow: processedData[0],
          lastModified: file.lastModified
        },
        processingStats: {
          startTime: 0,
          endTime: 0,
          processingTime: 0,
          rowsPerSecond: 0,
          bytesProcessed: file.size,
          errorsEncountered: 0,
          warningsGenerated: 0,
          memoryUsed: this.estimateMemoryUsage(processedData),
          conversionApplied: 'JSON to tabular conversion'
        }
      };

    } catch (error) {
      throw new Error(`JSON parsing failed: ${error instanceof Error ? error.message : 'Invalid JSON'}`);
    }
  }

  /**
   * Process XML files
   */
  private static async processXML(
    file: File,
    options: FormatProcessingOptions
  ): Promise<ProcessedFileData> {
    const text = await file.text();
    
    try {
      // Simple XML to JSON conversion
      const jsonData = this.xmlToJson(text, options.xmlRootElement);
      let data: Record<string, unknown>[];

      // Extract data array from XML structure
      if (Array.isArray(jsonData)) {
        data = jsonData;
      } else {
        data = this.findDataInJson(jsonData);
      }

      if (!Array.isArray(data)) {
        throw new Error('Unable to extract array data from XML structure');
      }

      const headers = data.length > 0 ? Object.keys(data[0]) : [];
      const processedData = this.applyRowLimits(data, options);

      return {
        data: processedData,
        headers,
        format: 'xml',
        metadata: {
          fileName: file.name,
          fileSize: file.size,
          originalFormat: 'xml',
          rowCount: processedData.length,
          columnCount: headers.length,
          hasHeader: true,
          firstRow: processedData[0],
          lastModified: file.lastModified
        },
        processingStats: {
          startTime: 0,
          endTime: 0,
          processingTime: 0,
          rowsPerSecond: 0,
          bytesProcessed: file.size,
          errorsEncountered: 0,
          warningsGenerated: 0,
          memoryUsed: this.estimateMemoryUsage(processedData),
          conversionApplied: 'XML to JSON conversion'
        }
      };

    } catch (error) {
      throw new Error(`XML parsing failed: ${error instanceof Error ? error.message : 'Invalid XML'}`);
    }
  }

  /**
   * Process plain text files (attempt CSV parsing)
   */
  private static async processText(
    file: File,
    options: FormatProcessingOptions
  ): Promise<ProcessedFileData> {
    const text = await file.text();
    
    // Detect delimiter from content
    const delimiter = options.delimiter || this.detectDelimiter(text);
    
    const csvResult = await this.processCSV(file, {
      ...options,
      delimiter
    });
    
    return {
      ...csvResult,
      format: 'txt',
      metadata: {
        ...csvResult.metadata,
        originalFormat: 'txt'
      },
      processingStats: {
        ...csvResult.processingStats,
        conversionApplied: `Text file processed as delimited data (delimiter: "${delimiter}")`
      }
    };
  }

  // Utility methods

  private static getFormatFromExtension(extension: string): SupportedFormat | null {
    const formatMap: Record<string, SupportedFormat> = {
      'csv': 'csv',
      'xlsx': 'xlsx',
      'xls': 'xls',
      'tsv': 'tsv',
      'tab': 'tsv',
      'json': 'json',
      'xml': 'xml',
      'txt': 'txt'
    };
    
    return formatMap[extension] || null;
  }

  private static getFormatFromMimeType(mimeType: string): SupportedFormat | null {
    const mimeMap: Record<string, SupportedFormat> = {
      'text/csv': 'csv',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
      'application/vnd.ms-excel': 'xls',
      'text/tab-separated-values': 'tsv',
      'application/json': 'json',
      'text/xml': 'xml',
      'application/xml': 'xml',
      'text/plain': 'txt'
    };
    
    return mimeMap[mimeType] || null;
  }

  private static detectDelimiter(text: string): string {
    const firstLines = text.split('\n').slice(0, 5).join('\n');
    const delimiterCounts = this.COMMON_DELIMITERS.map(delimiter => ({
      delimiter,
      count: (firstLines.match(new RegExp(`\\${delimiter}`, 'g')) || []).length
    }));

    delimiterCounts.sort((a, b) => b.count - a.count);
    return delimiterCounts[0]?.count > 0 ? delimiterCounts[0].delimiter : ',';
  }

  private static extractFromJsonPath(data: any, path: string): any {
    // Simple JSONPath implementation for basic paths
    const pathParts = path.replace(/^\$\./, '').split('.');
    let current = data;
    
    for (const part of pathParts) {
      if (current && typeof current === 'object' && part in current) {
        current = current[part];
      } else {
        throw new Error(`JSONPath "${path}" not found in data structure`);
      }
    }
    
    return current;
  }

  private static findDataInJson(jsonData: any): Record<string, unknown>[] {
    // Try common seafood JSON paths
    for (const path of this.SEAFOOD_JSON_PATHS) {
      try {
        const result = this.extractFromJsonPath(jsonData, path);
        if (Array.isArray(result)) return result;
      } catch {
        // Continue to next path
      }
    }

    // If it's an object, try to find the first array property
    if (typeof jsonData === 'object' && jsonData !== null) {
      for (const [key, value] of Object.entries(jsonData)) {
        if (Array.isArray(value) && value.length > 0 && typeof value[0] === 'object') {
          return value as Record<string, unknown>[];
        }
      }
    }

    throw new Error('No suitable data array found in JSON structure');
  }

  private static xmlToJson(xmlText: string, rootElement?: string): any {
    // This is a simplified XML parser - in production, use a proper XML library
    const parser = new DOMParser();
    const doc = parser.parseFromString(xmlText, 'text/xml');
    
    const errorNode = doc.querySelector('parsererror');
    if (errorNode) {
      throw new Error('XML parsing error');
    }

    // Simple conversion for seafood data structures
    const root = rootElement ? doc.querySelector(rootElement) : doc.documentElement;
    if (!root) {
      throw new Error(`Root element "${rootElement}" not found`);
    }

    return this.xmlNodeToJson(root);
  }

  private static xmlNodeToJson(node: Element): any {
    const result: any = {};
    const children = Array.from(node.children);
    
    if (children.length === 0) {
      return node.textContent?.trim() || '';
    }

    // Group similar elements into arrays
    const childGroups: Record<string, Element[]> = {};
    for (const child of children) {
      const tagName = child.tagName;
      if (!childGroups[tagName]) {
        childGroups[tagName] = [];
      }
      childGroups[tagName].push(child);
    }

    for (const [tagName, elements] of Object.entries(childGroups)) {
      if (elements.length === 1) {
        result[tagName] = this.xmlNodeToJson(elements[0]);
      } else {
        result[tagName] = elements.map(el => this.xmlNodeToJson(el));
      }
    }

    return result;
  }

  private static applyRowLimits(
    data: Record<string, unknown>[], 
    options: FormatProcessingOptions
  ): Record<string, unknown>[] {
    let result = data;
    
    if (options.skipRows && options.skipRows > 0) {
      result = result.slice(options.skipRows);
    }
    
    if (options.maxRows && options.maxRows > 0) {
      result = result.slice(0, options.maxRows);
    }
    
    return result;
  }

  private static generateHeaders(count: number): string[] {
    return Array.from({ length: count }, (_, i) => `Column_${i + 1}`);
  }

  private static estimateMemoryUsage(data: Record<string, unknown>[]): number {
    if (data.length === 0) return 0;
    
    const sampleSize = Math.min(10, data.length);
    const sample = data.slice(0, sampleSize);
    const sampleMemory = JSON.stringify(sample).length * 2; // UTF-16 encoding
    
    return Math.round((sampleMemory / sampleSize) * data.length);
  }

  /**
   * Get format-specific processing recommendations
   */
  static getProcessingRecommendations(file: File): {
    recommendedOptions: FormatProcessingOptions;
    warnings: string[];
    optimizations: string[];
  } {
    const format = this.detectFormat(file).detectedFormat;
    const warnings: string[] = [];
    const optimizations: string[] = [];
    
    const recommendedOptions: FormatProcessingOptions = {
      format,
      hasHeader: true,
      encoding: 'UTF-8',
      streaming: file.size > 10 * 1024 * 1024 // Stream for files > 10MB
    };

    // Format-specific recommendations
    switch (format) {
      case 'xlsx':
      case 'xls':
        if (file.size > 50 * 1024 * 1024) {
          warnings.push('Large Excel files may consume significant memory during processing');
          optimizations.push('Consider converting to CSV format for better performance');
        }
        break;
        
      case 'csv':
        if (file.size > 100 * 1024 * 1024) {
          optimizations.push('Streaming processing will be automatically enabled');
          recommendedOptions.streaming = true;
        }
        break;
        
      case 'json':
        warnings.push('JSON files are loaded entirely into memory');
        if (file.size > 20 * 1024 * 1024) {
          warnings.push('Large JSON files may impact browser performance');
        }
        break;
        
      case 'xml':
        warnings.push('XML processing involves conversion overhead');
        optimizations.push('CSV or JSON formats provide better performance');
        break;
    }

    return {
      recommendedOptions,
      warnings,
      optimizations
    };
  }

  /**
   * Get supported formats information
   */
  static getSupportedFormats(): Array<{
    format: SupportedFormat;
    name: string;
    extensions: string[];
    mimeTypes: string[];
    supportsStreaming: boolean;
    maxRecommendedSize: string;
  }> {
    return [
      {
        format: 'csv',
        name: 'Comma-Separated Values',
        extensions: ['.csv'],
        mimeTypes: ['text/csv'],
        supportsStreaming: true,
        maxRecommendedSize: '500MB'
      },
      {
        format: 'xlsx',
        name: 'Excel Workbook (2007+)',
        extensions: ['.xlsx'],
        mimeTypes: ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
        supportsStreaming: false,
        maxRecommendedSize: '50MB'
      },
      {
        format: 'xls',
        name: 'Excel Workbook (Legacy)',
        extensions: ['.xls'],
        mimeTypes: ['application/vnd.ms-excel'],
        supportsStreaming: false,
        maxRecommendedSize: '25MB'
      },
      {
        format: 'tsv',
        name: 'Tab-Separated Values',
        extensions: ['.tsv', '.tab'],
        mimeTypes: ['text/tab-separated-values'],
        supportsStreaming: true,
        maxRecommendedSize: '500MB'
      },
      {
        format: 'json',
        name: 'JSON Data',
        extensions: ['.json'],
        mimeTypes: ['application/json'],
        supportsStreaming: false,
        maxRecommendedSize: '20MB'
      },
      {
        format: 'xml',
        name: 'XML Data',
        extensions: ['.xml'],
        mimeTypes: ['text/xml', 'application/xml'],
        supportsStreaming: false,
        maxRecommendedSize: '10MB'
      },
      {
        format: 'txt',
        name: 'Plain Text (Delimited)',
        extensions: ['.txt'],
        mimeTypes: ['text/plain'],
        supportsStreaming: true,
        maxRecommendedSize: '100MB'
      }
    ];
  }
}