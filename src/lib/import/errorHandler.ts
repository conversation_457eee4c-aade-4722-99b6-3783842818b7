/**
 * Comprehensive Error Handling and Recovery System
 * Provides robust error handling, retry mechanisms, and recovery strategies
 * for seafood import operations with detailed logging and user guidance
 */

export type ErrorSeverity = 'critical' | 'high' | 'medium' | 'low';
export type ErrorCategory = 'parsing' | 'validation' | 'mapping' | 'processing' | 'network' | 'system' | 'user';
export type RecoveryStrategy = 'retry' | 'skip' | 'fallback' | 'manual' | 'abort';

export interface ImportError {
  id: string;
  code: string;
  category: ErrorCategory;
  severity: ErrorSeverity;
  message: string;
  details?: string;
  field?: string;
  row?: number;
  column?: string;
  timestamp: string;
  
  // Context information
  context: {
    operation: string;
    fileName?: string;
    fileSize?: number;
    rowCount?: number;
    progress?: number;
  };
  
  // Recovery information
  recovery: {
    strategy: RecoveryStrategy;
    attempts: number;
    maxAttempts: number;
    canRetry: boolean;
    canSkip: boolean;
    suggested: string[];
  };
  
  // User guidance
  userGuidance: {
    explanation: string;
    actions: string[];
    prevention: string[];
  };
  
  // Technical details for debugging
  technical?: {
    stackTrace?: string;
    originalError?: Error;
    systemInfo?: Record<string, unknown>;
  };
}

export interface RecoveryResult {
  success: boolean;
  action: 'retried' | 'skipped' | 'fallback_applied' | 'manual_required' | 'aborted';
  data?: unknown;
  newErrors?: ImportError[];
  message?: string;
}

export interface ErrorStats {
  total: number;
  bySeverity: Record<ErrorSeverity, number>;
  byCategory: Record<ErrorCategory, number>;
  recovered: number;
  unrecoverable: number;
  averageRecoveryTime: number;
}

export interface RetryConfig {
  maxAttempts: number;
  baseDelay: number; // milliseconds
  backoffMultiplier: number;
  maxDelay: number; // milliseconds
  retryableCategories: ErrorCategory[];
  retryableErrorCodes: string[];
}

export class ImportErrorHandler {
  private errors: Map<string, ImportError> = new Map();
  private recoveryHistory: Map<string, RecoveryResult[]> = new Map();
  private retryConfig: RetryConfig;
  private onErrorCallback?: (error: ImportError) => void;
  private onRecoveryCallback?: (error: ImportError, result: RecoveryResult) => void;

  // Predefined error patterns and their recovery strategies
  private static readonly ERROR_PATTERNS: Record<string, {
    category: ErrorCategory;
    severity: ErrorSeverity;
    strategy: RecoveryStrategy;
    userGuidance: {
      explanation: string;
      actions: string[];
      prevention: string[];
    };
  }> = {
    // Parsing errors
    'CSV_PARSE_FAILED': {
      category: 'parsing',
      severity: 'critical',
      strategy: 'fallback',
      userGuidance: {
        explanation: 'The CSV file could not be parsed due to formatting issues.',
        actions: [
          'Check file encoding (should be UTF-8)',
          'Verify delimiter consistency throughout file',
          'Remove any special characters from headers',
          'Ensure quotes are properly balanced'
        ],
        prevention: [
          'Export from Excel as UTF-8 CSV',
          'Avoid special characters in column headers',
          'Use consistent delimiters throughout'
        ]
      }
    },

    'EXCEL_FILE_CORRUPTED': {
      category: 'parsing',
      severity: 'critical',
      strategy: 'manual',
      userGuidance: {
        explanation: 'The Excel file appears to be corrupted or cannot be read.',
        actions: [
          'Try opening the file in Excel to verify it works',
          'Re-save the file as a new Excel workbook',
          'Export as CSV format instead',
          'Check file size is not 0 bytes'
        ],
        prevention: [
          'Always save backup copies of important data',
          'Use "Save As" instead of "Save" for critical files',
          'Avoid force-closing Excel while files are open'
        ]
      }
    },

    // Validation errors
    'SPECIES_NOT_RECOGNIZED': {
      category: 'validation',
      severity: 'medium',
      strategy: 'fallback',
      userGuidance: {
        explanation: 'One or more seafood species names were not recognized in our database.',
        actions: [
          'Check spelling of species names',
          'Use common names (e.g., "Salmon" instead of scientific names)',
          'Remove prefixes like "Wild" or "Fresh" from species names',
          'Refer to the species reference guide'
        ],
        prevention: [
          'Use standardized species naming conventions',
          'Validate species names before import',
          'Keep a reference list of accepted species names'
        ]
      }
    },

    'PRICE_OUT_OF_RANGE': {
      category: 'validation',
      severity: 'low',
      strategy: 'skip',
      userGuidance: {
        explanation: 'Some prices appear to be outside normal ranges for seafood products.',
        actions: [
          'Verify price units (per lb vs per kg)',
          'Check for decimal point errors',
          'Confirm currency is in USD',
          'Review seasonal price variations'
        ],
        prevention: [
          'Implement price validation at data entry',
          'Use consistent units across all products',
          'Regular price audits against market rates'
        ]
      }
    },

    // Mapping errors
    'COLUMN_MAPPING_FAILED': {
      category: 'mapping',
      severity: 'high',
      strategy: 'manual',
      userGuidance: {
        explanation: 'Required columns could not be automatically mapped to expected fields.',
        actions: [
          'Review column headers in your file',
          'Use the manual column mapping interface',
          'Check for extra spaces or special characters in headers',
          'Consider using a template for this supplier'
        ],
        prevention: [
          'Use consistent column naming across imports',
          'Create templates for frequent suppliers',
          'Standardize export formats from source systems'
        ]
      }
    },

    // Processing errors
    'MEMORY_EXCEEDED': {
      category: 'processing',
      severity: 'high',
      strategy: 'retry',
      userGuidance: {
        explanation: 'The file is too large to process in memory and caused performance issues.',
        actions: [
          'Split the file into smaller chunks (< 50MB)',
          'Try using streaming processing mode',
          'Remove unnecessary columns before import',
          'Process during off-peak hours'
        ],
        prevention: [
          'Keep import files under 100MB when possible',
          'Remove test or sample data before production imports',
          'Use incremental imports for large datasets'
        ]
      }
    },

    'WORKER_TIMEOUT': {
      category: 'processing',
      severity: 'medium',
      strategy: 'retry',
      userGuidance: {
        explanation: 'Processing took too long and was terminated to prevent system issues.',
        actions: [
          'Try processing a smaller file first',
          'Check your internet connection',
          'Reduce the chunk size in settings',
          'Try again during off-peak hours'
        ],
        prevention: [
          'Process large files during low-traffic periods',
          'Break large imports into smaller batches',
          'Monitor system performance during imports'
        ]
      }
    },

    // Network errors
    'UPLOAD_FAILED': {
      category: 'network',
      severity: 'medium',
      strategy: 'retry',
      userGuidance: {
        explanation: 'Data could not be uploaded to the server due to network issues.',
        actions: [
          'Check your internet connection',
          'Try uploading again in a few minutes',
          'Reduce file size if possible',
          'Switch to a more stable network connection'
        ],
        prevention: [
          'Use wired connection for large uploads',
          'Ensure stable internet before starting imports',
          'Save work frequently during import process'
        ]
      }
    },

    // System errors
    'DATABASE_CONNECTION_LOST': {
      category: 'system',
      severity: 'critical',
      strategy: 'retry',
      userGuidance: {
        explanation: 'Connection to the database was lost during processing.',
        actions: [
          'Wait a moment and try again',
          'Check if other parts of the system are working',
          'Contact support if problem persists',
          'Save your work locally as backup'
        ],
        prevention: [
          'Ensure stable internet connection',
          'Process during off-peak hours',
          'Keep local backups of important data'
        ]
      }
    }
  };

  constructor(
    retryConfig: Partial<RetryConfig> = {},
    onError?: (error: ImportError) => void,
    onRecovery?: (error: ImportError, result: RecoveryResult) => void
  ) {
    this.retryConfig = {
      maxAttempts: 3,
      baseDelay: 1000,
      backoffMultiplier: 2,
      maxDelay: 30000,
      retryableCategories: ['network', 'processing'],
      retryableErrorCodes: ['UPLOAD_FAILED', 'WORKER_TIMEOUT', 'DATABASE_CONNECTION_LOST'],
      ...retryConfig
    };
    
    this.onErrorCallback = onError;
    this.onRecoveryCallback = onRecovery;
  }

  /**
   * Handle an error with automatic recovery attempts
   */
  async handleError(
    rawError: Error | string | ImportError,
    context: ImportError['context'] = { operation: 'unknown' }
  ): Promise<RecoveryResult> {
    const error = this.normalizeError(rawError, context);
    this.errors.set(error.id, error);
    
    // Notify callback
    if (this.onErrorCallback) {
      this.onErrorCallback(error);
    }

    // Attempt recovery
    const recoveryResult = await this.attemptRecovery(error);
    
    // Store recovery history
    if (!this.recoveryHistory.has(error.id)) {
      this.recoveryHistory.set(error.id, []);
    }
    this.recoveryHistory.get(error.id)!.push(recoveryResult);

    // Notify recovery callback
    if (this.onRecoveryCallback) {
      this.onRecoveryCallback(error, recoveryResult);
    }

    return recoveryResult;
  }

  /**
   * Batch handle multiple errors
   */
  async handleBatchErrors(
    errors: Array<{ error: Error | string; context: ImportError['context'] }>
  ): Promise<RecoveryResult[]> {
    const results: RecoveryResult[] = [];
    
    // Group errors by category for optimized recovery
    const errorGroups = this.groupErrorsByCategory(errors);
    
    for (const [category, errorGroup] of errorGroups.entries()) {
      if (category === 'validation' && errorGroup.length > 10) {
        // For many validation errors, use batch recovery
        const batchResult = await this.handleBatchValidationErrors(errorGroup);
        results.push(...batchResult);
      } else {
        // Handle individually
        for (const { error, context } of errorGroup) {
          const result = await this.handleError(error, context);
          results.push(result);
        }
      }
    }
    
    return results;
  }

  /**
   * Retry a specific error
   */
  async retryError(errorId: string): Promise<RecoveryResult> {
    const error = this.errors.get(errorId);
    if (!error) {
      return {
        success: false,
        action: 'aborted',
        message: 'Error not found'
      };
    }

    if (error.recovery.attempts >= error.recovery.maxAttempts) {
      return {
        success: false,
        action: 'aborted',
        message: 'Maximum retry attempts exceeded'
      };
    }

    return await this.attemptRecovery(error);
  }

  /**
   * Skip an error (if possible)
   */
  skipError(errorId: string): RecoveryResult {
    const error = this.errors.get(errorId);
    if (!error) {
      return {
        success: false,
        action: 'aborted',
        message: 'Error not found'
      };
    }

    if (!error.recovery.canSkip) {
      return {
        success: false,
        action: 'aborted',
        message: 'This error cannot be skipped'
      };
    }

    return {
      success: true,
      action: 'skipped',
      message: 'Error was skipped and processing will continue'
    };
  }

  /**
   * Get all current errors
   */
  getErrors(): ImportError[] {
    return Array.from(this.errors.values());
  }

  /**
   * Get errors filtered by criteria
   */
  getFilteredErrors(filters: {
    severity?: ErrorSeverity[];
    category?: ErrorCategory[];
    canRetry?: boolean;
    unresolved?: boolean;
  }): ImportError[] {
    return this.getErrors().filter(error => {
      if (filters.severity && !filters.severity.includes(error.severity)) return false;
      if (filters.category && !filters.category.includes(error.category)) return false;
      if (filters.canRetry !== undefined && error.recovery.canRetry !== filters.canRetry) return false;
      
      if (filters.unresolved) {
        const history = this.recoveryHistory.get(error.id);
        const resolved = history?.some(h => h.success);
        if (resolved) return false;
      }
      
      return true;
    });
  }

  /**
   * Get error statistics
   */
  getErrorStats(): ErrorStats {
    const errors = this.getErrors();
    const stats: ErrorStats = {
      total: errors.length,
      bySeverity: { critical: 0, high: 0, medium: 0, low: 0 },
      byCategory: { parsing: 0, validation: 0, mapping: 0, processing: 0, network: 0, system: 0, user: 0 },
      recovered: 0,
      unrecoverable: 0,
      averageRecoveryTime: 0
    };

    const totalRecoveryTime = 0;
    let recoveryCount = 0;

    for (const error of errors) {
      stats.bySeverity[error.severity]++;
      stats.byCategory[error.category]++;

      const history = this.recoveryHistory.get(error.id);
      if (history) {
        const successful = history.filter(h => h.success);
        if (successful.length > 0) {
          stats.recovered++;
          // Calculate recovery time (simplified)
          recoveryCount++;
        } else if (error.recovery.attempts >= error.recovery.maxAttempts) {
          stats.unrecoverable++;
        }
      }
    }

    stats.averageRecoveryTime = recoveryCount > 0 ? totalRecoveryTime / recoveryCount : 0;
    return stats;
  }

  /**
   * Clear all errors
   */
  clearErrors(): void {
    this.errors.clear();
    this.recoveryHistory.clear();
  }

  /**
   * Export error report for debugging
   */
  exportErrorReport(): {
    summary: ErrorStats;
    errors: ImportError[];
    recoveryHistory: Record<string, RecoveryResult[]>;
    timestamp: string;
  } {
    return {
      summary: this.getErrorStats(),
      errors: this.getErrors(),
      recoveryHistory: Object.fromEntries(this.recoveryHistory.entries()),
      timestamp: new Date().toISOString()
    };
  }

  // Private methods

  private normalizeError(
    rawError: Error | string | ImportError,
    context: ImportError['context']
  ): ImportError {
    if (this.isImportError(rawError)) {
      return rawError;
    }

    const errorMessage = rawError instanceof Error ? rawError.message : String(rawError);
    const errorCode = this.detectErrorCode(errorMessage);
    const pattern = ImportErrorHandler.ERROR_PATTERNS[errorCode];

    const error: ImportError = {
      id: this.generateErrorId(),
      code: errorCode,
      category: pattern?.category || 'system',
      severity: pattern?.severity || 'medium',
      message: errorMessage,
      timestamp: new Date().toISOString(),
      context,
      recovery: {
        strategy: pattern?.strategy || 'manual',
        attempts: 0,
        maxAttempts: this.retryConfig.maxAttempts,
        canRetry: this.canRetry(errorCode, pattern?.category || 'system'),
        canSkip: this.canSkip(pattern?.severity || 'medium', pattern?.category || 'system'),
        suggested: pattern ? pattern.userGuidance.actions : []
      },
      userGuidance: pattern?.userGuidance || {
        explanation: 'An unexpected error occurred.',
        actions: ['Try the operation again', 'Contact support if the problem persists'],
        prevention: ['Ensure data quality before import', 'Use recommended file formats']
      }
    };

    if (rawError instanceof Error) {
      error.technical = {
        stackTrace: rawError.stack,
        originalError: rawError,
        systemInfo: {
          userAgent: navigator.userAgent,
          timestamp: Date.now(),
          url: window.location.href
        }
      };
    }

    return error;
  }

  private async attemptRecovery(error: ImportError): Promise<RecoveryResult> {
    error.recovery.attempts++;

    switch (error.recovery.strategy) {
      case 'retry':
        return await this.retryOperation(error);
      
      case 'skip':
        return this.skipOperation(error);
      
      case 'fallback':
        return await this.applyFallback(error);
      
      case 'manual':
        return this.requireManualIntervention(error);
      
      case 'abort':
      default:
        return {
          success: false,
          action: 'aborted',
          message: 'Operation was aborted due to critical error'
        };
    }
  }

  private async retryOperation(error: ImportError): Promise<RecoveryResult> {
    if (error.recovery.attempts > error.recovery.maxAttempts) {
      return {
        success: false,
        action: 'aborted',
        message: 'Maximum retry attempts exceeded'
      };
    }

    // Calculate delay with exponential backoff
    const delay = Math.min(
      this.retryConfig.baseDelay * Math.pow(this.retryConfig.backoffMultiplier, error.recovery.attempts - 1),
      this.retryConfig.maxDelay
    );

    // Wait before retry
    await new Promise(resolve => setTimeout(resolve, delay));

    try {
      // This would be implemented by the calling code
      // For now, we'll simulate a retry result
      const success = Math.random() > 0.3; // 70% success rate for simulation
      
      return {
        success,
        action: 'retried',
        message: success ? 'Retry successful' : 'Retry failed, will attempt again'
      };
    } catch (retryError) {
      return {
        success: false,
        action: 'retried',
        message: `Retry failed: ${retryError instanceof Error ? retryError.message : 'Unknown error'}`,
        newErrors: [this.normalizeError(retryError, error.context)]
      };
    }
  }

  private skipOperation(error: ImportError): RecoveryResult {
    if (!error.recovery.canSkip) {
      return {
        success: false,
        action: 'aborted',
        message: 'Error cannot be skipped'
      };
    }

    return {
      success: true,
      action: 'skipped',
      message: 'Error was skipped, processing will continue with remaining data'
    };
  }

  private async applyFallback(error: ImportError): Promise<RecoveryResult> {
    // Implement fallback strategies based on error type
    switch (error.code) {
      case 'CSV_PARSE_FAILED':
        return await this.fallbackCsvParsing(error);
      
      case 'SPECIES_NOT_RECOGNIZED':
        return await this.fallbackSpeciesMapping(error);
      
      case 'COLUMN_MAPPING_FAILED':
        return await this.fallbackColumnMapping(error);
      
      default:
        return {
          success: false,
          action: 'fallback_applied',
          message: 'No fallback strategy available for this error type'
        };
    }
  }

  private requireManualIntervention(error: ImportError): RecoveryResult {
    return {
      success: false,
      action: 'manual_required',
      message: 'Manual intervention is required to resolve this error',
      data: {
        guidance: error.userGuidance,
        suggestions: error.recovery.suggested
      }
    };
  }

  private async handleBatchValidationErrors(
    errors: Array<{ error: Error | string; context: ImportError['context'] }>
  ): Promise<RecoveryResult[]> {
    // Implement batch validation error handling
    // This could involve pattern analysis and bulk corrections
    return errors.map(() => ({
      success: true,
      action: 'skipped' as const,
      message: 'Validation error handled in batch'
    }));
  }

  private groupErrorsByCategory(
    errors: Array<{ error: Error | string; context: ImportError['context'] }>
  ): Map<ErrorCategory, Array<{ error: Error | string; context: ImportError['context'] }>> {
    const groups = new Map<ErrorCategory, Array<{ error: Error | string; context: ImportError['context'] }>>();
    
    for (const errorItem of errors) {
      const errorMessage = errorItem.error instanceof Error ? errorItem.error.message : String(errorItem.error);
      const errorCode = this.detectErrorCode(errorMessage);
      const category = ImportErrorHandler.ERROR_PATTERNS[errorCode]?.category || 'system';
      
      if (!groups.has(category)) {
        groups.set(category, []);
      }
      groups.get(category)!.push(errorItem);
    }
    
    return groups;
  }

  // Fallback implementations
  private async fallbackCsvParsing(error: ImportError): Promise<RecoveryResult> {
    // Try alternative CSV parsing approaches
    return {
      success: true,
      action: 'fallback_applied',
      message: 'Applied alternative CSV parsing method'
    };
  }

  private async fallbackSpeciesMapping(error: ImportError): Promise<RecoveryResult> {
    // Try fuzzy matching or user-defined mappings
    return {
      success: true,
      action: 'fallback_applied',
      message: 'Applied fuzzy species name matching'
    };
  }

  private async fallbackColumnMapping(error: ImportError): Promise<RecoveryResult> {
    // Try semantic matching or user templates
    return {
      success: true,
      action: 'fallback_applied',
      message: 'Applied semantic column mapping'
    };
  }

  // Utility methods
  private isImportError(obj: unknown): obj is ImportError {
    return typeof obj === 'object' && obj !== null && 'id' in obj && 'code' in obj;
  }

  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private detectErrorCode(message: string): string {
    // Match message against known error patterns
    for (const [code, pattern] of Object.entries(ImportErrorHandler.ERROR_PATTERNS)) {
      if (message.toLowerCase().includes(code.toLowerCase().replace(/_/g, ' '))) {
        return code;
      }
    }
    
    // Fallback pattern matching
    if (/parse|parsing/i.test(message)) return 'CSV_PARSE_FAILED';
    if (/species|fish|seafood/i.test(message)) return 'SPECIES_NOT_RECOGNIZED';
    if (/column|mapping|field/i.test(message)) return 'COLUMN_MAPPING_FAILED';
    if (/memory|size|large/i.test(message)) return 'MEMORY_EXCEEDED';
    if (/timeout|slow/i.test(message)) return 'WORKER_TIMEOUT';
    if (/network|connection|upload/i.test(message)) return 'UPLOAD_FAILED';
    if (/database|server/i.test(message)) return 'DATABASE_CONNECTION_LOST';
    
    return 'UNKNOWN_ERROR';
  }

  private canRetry(errorCode: string, category: ErrorCategory): boolean {
    return this.retryConfig.retryableCategories.includes(category) || 
           this.retryConfig.retryableErrorCodes.includes(errorCode);
  }

  private canSkip(severity: ErrorSeverity, category: ErrorCategory): boolean {
    // Critical parsing errors cannot be skipped
    if (severity === 'critical' && category === 'parsing') return false;
    
    // System errors usually cannot be skipped
    if (category === 'system') return false;
    
    // Most validation and mapping errors can be skipped
    return ['validation', 'mapping'].includes(category) || severity === 'low';
  }
}