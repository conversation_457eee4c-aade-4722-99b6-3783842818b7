/**
 * Comprehensive Seafood Industry Data Validation System
 * Includes taxonomy validation, business rules, and compliance checking
 * Optimized for real-time validation during import processing
 */

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  corrections?: ValidationCorrection[];
}

export interface ValidationError {
  field: string;
  row: number;
  code: string;
  message: string;
  severity: 'critical' | 'high' | 'medium' | 'low';
  suggestedValue?: unknown;
}

export interface ValidationWarning {
  field: string;
  row: number;
  code: string;
  message: string;
  impact: 'data_quality' | 'compliance' | 'performance' | 'user_experience';
}

export interface ValidationCorrection {
  field: string;
  row: number;
  originalValue: unknown;
  correctedValue: unknown;
  confidence: number;
  reasoning: string;
}

export interface SeafoodValidationConfig {
  enableSpeciesValidation: boolean;
  enablePriceValidation: boolean;
  enableDateValidation: boolean;
  enableTemperatureValidation: boolean;
  enableHACCPValidation: boolean;
  enableTraceabilityValidation: boolean;
  strictMode: boolean; // If true, warnings become errors
  region?: 'pacific' | 'atlantic' | 'global';
  validationLevel: 'basic' | 'standard' | 'comprehensive';
}

export class SeafoodValidator {
  private config: SeafoodValidationConfig;
  private speciesDatabase: Map<string, SeafoodSpecies>;
  private priceRanges: Map<string, PriceRange>;
  private temperatureRanges: Map<string, TemperatureRange>;
  private validationCache: Map<string, ValidationResult> = new Map();

  // Comprehensive seafood species database
  private static readonly SEAFOOD_SPECIES_DB: Record<string, SeafoodSpecies> = {
    // Salmon species
    'chinook_salmon': {
      commonNames: ['chinook salmon', 'king salmon', 'spring salmon'],
      scientificName: 'Oncorhynchus tshawytscha',
      category: 'salmon',
      averageWeight: { min: 15, max: 60, unit: 'lbs' },
      temperatureRange: { min: 28, max: 35, unit: 'fahrenheit' },
      shelfLife: { fresh: 7, frozen: 365, unit: 'days' },
      seasons: ['spring', 'summer', 'fall'],
      regions: ['pacific', 'alaska']
    },
    'coho_salmon': {
      commonNames: ['coho salmon', 'silver salmon'],
      scientificName: 'Oncorhynchus kisutch',
      category: 'salmon',
      averageWeight: { min: 6, max: 15, unit: 'lbs' },
      temperatureRange: { min: 28, max: 35, unit: 'fahrenheit' },
      shelfLife: { fresh: 7, frozen: 365, unit: 'days' },
      seasons: ['summer', 'fall'],
      regions: ['pacific', 'great_lakes']
    },
    'sockeye_salmon': {
      commonNames: ['sockeye salmon', 'red salmon'],
      scientificName: 'Oncorhynchus nerka',
      category: 'salmon',
      averageWeight: { min: 4, max: 12, unit: 'lbs' },
      temperatureRange: { min: 28, max: 35, unit: 'fahrenheit' },
      shelfLife: { fresh: 7, frozen: 365, unit: 'days' },
      seasons: ['summer'],
      regions: ['pacific', 'alaska']
    },
    
    // Crab species
    'dungeness_crab': {
      commonNames: ['dungeness crab', 'dungeness', 'dung crab'],
      scientificName: 'Metacarcinus magister',
      category: 'crab',
      averageWeight: { min: 1.5, max: 3, unit: 'lbs' },
      temperatureRange: { min: 28, max: 35, unit: 'fahrenheit' },
      shelfLife: { fresh: 3, frozen: 180, unit: 'days' },
      seasons: ['fall', 'winter', 'spring'],
      regions: ['pacific']
    },
    'king_crab': {
      commonNames: ['king crab', 'alaskan king crab', 'red king crab'],
      scientificName: 'Paralithodes camtschaticus',
      category: 'crab',
      averageWeight: { min: 6, max: 15, unit: 'lbs' },
      temperatureRange: { min: 28, max: 32, unit: 'fahrenheit' },
      shelfLife: { fresh: 3, frozen: 365, unit: 'days' },
      seasons: ['fall', 'winter'],
      regions: ['alaska', 'bering_sea']
    },

    // Halibut
    'pacific_halibut': {
      commonNames: ['pacific halibut', 'halibut', 'hippo'],
      scientificName: 'Hippoglossus stenolepis',
      category: 'flatfish',
      averageWeight: { min: 20, max: 200, unit: 'lbs' },
      temperatureRange: { min: 28, max: 35, unit: 'fahrenheit' },
      shelfLife: { fresh: 10, frozen: 365, unit: 'days' },
      seasons: ['spring', 'summer', 'fall'],
      regions: ['pacific', 'alaska']
    }
  };

  // Price validation ranges by category (per pound)
  private static readonly PRICE_RANGES: Record<string, PriceRange> = {
    salmon: { min: 8, max: 45, unit: 'per_lb', currency: 'USD' },
    crab: { min: 15, max: 80, unit: 'per_lb', currency: 'USD' },
    halibut: { min: 12, max: 35, unit: 'per_lb', currency: 'USD' },
    cod: { min: 6, max: 18, unit: 'per_lb', currency: 'USD' },
    shrimp: { min: 10, max: 40, unit: 'per_lb', currency: 'USD' },
    tuna: { min: 15, max: 60, unit: 'per_lb', currency: 'USD' }
  };

  // Temperature ranges for different seafood categories
  private static readonly TEMPERATURE_RANGES: Record<string, TemperatureRange> = {
    fresh_fish: { min: 28, max: 38, unit: 'fahrenheit', storage_type: 'refrigerated' },
    frozen_fish: { min: -10, max: 0, unit: 'fahrenheit', storage_type: 'frozen' },
    live_shellfish: { min: 35, max: 45, unit: 'fahrenheit', storage_type: 'live_tank' },
    fresh_shellfish: { min: 28, max: 35, unit: 'fahrenheit', storage_type: 'refrigerated' },
    frozen_shellfish: { min: -10, max: 0, unit: 'fahrenheit', storage_type: 'frozen' }
  };

  constructor(config: Partial<SeafoodValidationConfig> = {}) {
    this.config = {
      enableSpeciesValidation: true,
      enablePriceValidation: true,
      enableDateValidation: true,
      enableTemperatureValidation: true,
      enableHACCPValidation: true,
      enableTraceabilityValidation: true,
      strictMode: false,
      validationLevel: 'standard',
      ...config
    };

    this.initializeDatabases();
  }

  private initializeDatabases() {
    this.speciesDatabase = new Map(Object.entries(SeafoodValidator.SEAFOOD_SPECIES_DB));
    this.priceRanges = new Map(Object.entries(SeafoodValidator.PRICE_RANGES));
    this.temperatureRanges = new Map(Object.entries(SeafoodValidator.TEMPERATURE_RANGES));
  }

  /**
   * Validate a batch of seafood records
   */
  async validateBatch(records: Record<string, unknown>[]): Promise<ValidationResult[]> {
    const results: ValidationResult[] = [];
    
    for (let i = 0; i < records.length; i++) {
      const result = await this.validateRecord(records[i], i + 1);
      results.push(result);
    }

    return results;
  }

  /**
   * Validate a single seafood record
   */
  async validateRecord(record: Record<string, unknown>, rowNumber: number): Promise<ValidationResult> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    const corrections: ValidationCorrection[] = [];

    // Create cache key for performance
    const cacheKey = this.createCacheKey(record);
    if (this.validationCache.has(cacheKey)) {
      const cached = this.validationCache.get(cacheKey)!;
      return {
        ...cached,
        errors: cached.errors.map(e => ({ ...e, row: rowNumber })),
        warnings: cached.warnings.map(w => ({ ...w, row: rowNumber }))
      };
    }

    try {
      // Core field validations
      if (this.config.enableSpeciesValidation) {
        const speciesValidation = this.validateSpecies(record, rowNumber);
        errors.push(...speciesValidation.errors);
        warnings.push(...speciesValidation.warnings);
        corrections.push(...speciesValidation.corrections || []);
      }

      if (this.config.enablePriceValidation) {
        const priceValidation = this.validatePrice(record, rowNumber);
        errors.push(...priceValidation.errors);
        warnings.push(...priceValidation.warnings);
        corrections.push(...priceValidation.corrections || []);
      }

      if (this.config.enableDateValidation) {
        const dateValidation = this.validateDates(record, rowNumber);
        errors.push(...dateValidation.errors);
        warnings.push(...dateValidation.warnings);
      }

      if (this.config.enableTemperatureValidation) {
        const tempValidation = this.validateTemperature(record, rowNumber);
        errors.push(...tempValidation.errors);
        warnings.push(...tempValidation.warnings);
      }

      // Business rule validations
      const businessValidation = this.validateBusinessRules(record, rowNumber);
      errors.push(...businessValidation.errors);
      warnings.push(...businessValidation.warnings);

      // HACCP compliance validations
      if (this.config.enableHACCPValidation) {
        const haccpValidation = this.validateHACCPCompliance(record, rowNumber);
        errors.push(...haccpValidation.errors);
        warnings.push(...haccpValidation.warnings);
      }

      // Traceability validations
      if (this.config.enableTraceabilityValidation) {
        const traceValidation = this.validateTraceability(record, rowNumber);
        errors.push(...traceValidation.errors);
        warnings.push(...traceValidation.warnings);
      }

      // Cross-field validations
      const crossFieldValidation = this.validateCrossFields(record, rowNumber);
      errors.push(...crossFieldValidation.errors);
      warnings.push(...crossFieldValidation.warnings);

    } catch (error) {
      errors.push({
        field: 'record',
        row: rowNumber,
        code: 'VALIDATION_ERROR',
        message: `Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'critical'
      });
    }

    // Convert warnings to errors in strict mode
    if (this.config.strictMode) {
      warnings.forEach(warning => {
        errors.push({
          field: warning.field,
          row: warning.row,
          code: warning.code,
          message: `[STRICT MODE] ${warning.message}`,
          severity: 'medium'
        });
      });
    }

    const result: ValidationResult = {
      isValid: errors.length === 0,
      errors,
      warnings,
      corrections: corrections.length > 0 ? corrections : undefined
    };

    // Cache the result (without row-specific data)
    this.validationCache.set(cacheKey, {
      ...result,
      errors: result.errors.map(e => ({ ...e, row: 0 })),
      warnings: result.warnings.map(w => ({ ...w, row: 0 }))
    });

    return result;
  }

  /**
   * Validate seafood species information
   */
  private validateSpecies(record: Record<string, unknown>, rowNumber: number): Partial<ValidationResult> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    const corrections: ValidationCorrection[] = [];

    const name = this.getString(record, 'name') || this.getString(record, 'product_name') || this.getString(record, 'species');
    
    if (!name) {
      errors.push({
        field: 'name',
        row: rowNumber,
        code: 'MISSING_SPECIES_NAME',
        message: 'Species/product name is required',
        severity: 'critical'
      });
      return { errors, warnings, corrections };
    }

    // Check against known species database
    const matchedSpecies = this.findSpeciesMatch(name);
    
    if (!matchedSpecies) {
      if (this.config.validationLevel === 'comprehensive') {
        warnings.push({
          field: 'name',
          row: rowNumber,
          code: 'UNKNOWN_SPECIES',
          message: `Species "${name}" not found in database. Verify spelling and scientific accuracy.`,
          impact: 'data_quality'
        });
      }
    } else {
      // Validate weight against species average
      const weight = this.getNumber(record, 'weight') || this.getNumber(record, 'quantity');
      if (weight && matchedSpecies.averageWeight) {
        const { min, max } = matchedSpecies.averageWeight;
        if (weight < min * 0.5 || weight > max * 2) {
          warnings.push({
            field: 'weight',
            row: rowNumber,
            code: 'UNUSUAL_WEIGHT',
            message: `Weight ${weight}${matchedSpecies.averageWeight.unit} is unusual for ${matchedSpecies.commonNames[0]} (typical range: ${min}-${max}${matchedSpecies.averageWeight.unit})`,
            impact: 'data_quality'
          });
        }
      }

      // Suggest species name correction if close match
      if (!matchedSpecies.commonNames.includes(name.toLowerCase())) {
        const bestMatch = matchedSpecies.commonNames[0];
        corrections.push({
          field: 'name',
          row: rowNumber,
          originalValue: name,
          correctedValue: bestMatch,
          confidence: 0.8,
          reasoning: `Standardized to preferred common name: ${bestMatch}`
        });
      }
    }

    return { errors, warnings, corrections };
  }

  /**
   * Validate pricing information
   */
  private validatePrice(record: Record<string, unknown>, rowNumber: number): Partial<ValidationResult> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    const corrections: ValidationCorrection[] = [];

    const price = this.getNumber(record, 'price') || this.getNumber(record, 'unit_price');
    const cost = this.getNumber(record, 'cost') || this.getNumber(record, 'unit_cost');
    
    if (price !== undefined) {
      // Basic price validation
      if (price <= 0) {
        errors.push({
          field: 'price',
          row: rowNumber,
          code: 'INVALID_PRICE',
          message: 'Price must be greater than zero',
          severity: 'high'
        });
      } else if (price > 1000) {
        warnings.push({
          field: 'price',
          row: rowNumber,
          code: 'UNUSUALLY_HIGH_PRICE',
          message: `Price $${price} seems unusually high. Please verify.`,
          impact: 'data_quality'
        });
      }

      // Category-based price validation
      const category = this.getCategory(record);
      if (category && this.priceRanges.has(category)) {
        const range = this.priceRanges.get(category)!;
        if (price < range.min || price > range.max) {
          const severity = price < range.min * 0.5 || price > range.max * 2 ? 'high' : 'medium';
          warnings.push({
            field: 'price',
            row: rowNumber,
            code: 'PRICE_OUT_OF_RANGE',
            message: `Price $${price} is outside typical range for ${category}: $${range.min}-$${range.max}`,
            impact: 'data_quality'
          });
        }
      }
    }

    // Cost vs Price validation
    if (price && cost) {
      if (cost > price) {
        warnings.push({
          field: 'cost',
          row: rowNumber,
          code: 'COST_EXCEEDS_PRICE',
          message: `Cost ($${cost}) exceeds selling price ($${price}). Check for negative margin.`,
          impact: 'compliance'
        });
      }

      const margin = (price - cost) / price;
      if (margin < 0.1) {
        warnings.push({
          field: 'price',
          row: rowNumber,
          code: 'LOW_MARGIN',
          message: `Low profit margin detected: ${Math.round(margin * 100)}%. Industry average is 20-40%.`,
          impact: 'performance'
        });
      }
    }

    return { errors, warnings, corrections };
  }

  /**
   * Validate date fields
   */
  private validateDates(record: Record<string, unknown>, rowNumber: number): Partial<ValidationResult> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    const date = this.getDate(record, 'date') || this.getDate(record, 'received_date');
    const expiryDate = this.getDate(record, 'expiry_date') || this.getDate(record, 'best_by_date');
    const catchDate = this.getDate(record, 'catch_date') || this.getDate(record, 'harvest_date');

    const now = new Date();
    const oneYearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
    const oneYearFromNow = new Date(now.getFullYear() + 1, now.getMonth(), now.getDate());

    // Validate main date
    if (date) {
      if (date > oneYearFromNow) {
        errors.push({
          field: 'date',
          row: rowNumber,
          code: 'FUTURE_DATE',
          message: 'Date cannot be more than one year in the future',
          severity: 'medium'
        });
      }
      
      if (date < oneYearAgo) {
        warnings.push({
          field: 'date',
          row: rowNumber,
          code: 'OLD_DATE',
          message: 'Date is more than one year old. Verify data freshness.',
          impact: 'data_quality'
        });
      }
    }

    // Validate expiry date
    if (expiryDate) {
      if (expiryDate <= now) {
        errors.push({
          field: 'expiry_date',
          row: rowNumber,
          code: 'EXPIRED_PRODUCT',
          message: 'Product expiry date has passed',
          severity: 'critical'
        });
      }

      // Check reasonable shelf life
      if (date && expiryDate) {
        const shelfLifeDays = (expiryDate.getTime() - date.getTime()) / (1000 * 60 * 60 * 24);
        
        if (shelfLifeDays > 365) {
          warnings.push({
            field: 'expiry_date',
            row: rowNumber,
            code: 'LONG_SHELF_LIFE',
            message: `Shelf life of ${Math.round(shelfLifeDays)} days seems unusually long for seafood`,
            impact: 'data_quality'
          });
        } else if (shelfLifeDays < 1) {
          warnings.push({
            field: 'expiry_date',
            row: rowNumber,
            code: 'SHORT_SHELF_LIFE',
            message: `Shelf life of ${Math.round(shelfLifeDays)} days seems too short`,
            impact: 'data_quality'
          });
        }
      }
    }

    // Validate catch date
    if (catchDate && date) {
      if (catchDate > date) {
        errors.push({
          field: 'catch_date',
          row: rowNumber,
          code: 'CATCH_AFTER_RECEIVE',
          message: 'Catch date cannot be after received date',
          severity: 'high'
        });
      }

      const daysBetween = (date.getTime() - catchDate.getTime()) / (1000 * 60 * 60 * 24);
      if (daysBetween > 14) {
        warnings.push({
          field: 'catch_date',
          row: rowNumber,
          code: 'OLD_CATCH',
          message: `${Math.round(daysBetween)} days between catch and receipt is unusually long for fresh seafood`,
          impact: 'compliance'
        });
      }
    }

    return { errors, warnings };
  }

  /**
   * Validate temperature information
   */
  private validateTemperature(record: Record<string, unknown>, rowNumber: number): Partial<ValidationResult> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    const temp = this.getNumber(record, 'storage_temp') || this.getNumber(record, 'temperature');
    
    if (temp === undefined) return { errors, warnings };

    // Basic temperature range validation
    if (temp < -50 || temp > 100) {
      errors.push({
        field: 'storage_temp',
        row: rowNumber,
        code: 'INVALID_TEMPERATURE',
        message: `Temperature ${temp}°F is outside valid range (-50°F to 100°F)`,
        severity: 'high'
      });
      return { errors, warnings };
    }

    // Category-specific temperature validation
    const category = this.getCategory(record);
    const isFrozen = this.getString(record, 'storage_type')?.toLowerCase().includes('frozen') || temp < 10;
    
    let expectedRange: TemperatureRange | undefined;
    
    if (isFrozen) {
      expectedRange = this.temperatureRanges.get('frozen_fish') || this.temperatureRanges.get('frozen_shellfish');
    } else if (category?.includes('crab') || category?.includes('shrimp')) {
      expectedRange = this.temperatureRanges.get('fresh_shellfish');
    } else {
      expectedRange = this.temperatureRanges.get('fresh_fish');
    }

    if (expectedRange && (temp < expectedRange.min || temp > expectedRange.max)) {
      const severity = temp < expectedRange.min - 10 || temp > expectedRange.max + 10 ? 'high' : 'medium';
      warnings.push({
        field: 'storage_temp',
        row: rowNumber,
        code: 'TEMPERATURE_OUT_OF_RANGE',
        message: `Temperature ${temp}°F is outside recommended range for ${expectedRange.storage_type}: ${expectedRange.min}°F to ${expectedRange.max}°F`,
        impact: 'compliance'
      });
    }

    return { errors, warnings };
  }

  /**
   * Validate business rules specific to seafood operations
   */
  private validateBusinessRules(record: Record<string, unknown>, rowNumber: number): Partial<ValidationResult> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    const quantity = this.getNumber(record, 'quantity') || this.getNumber(record, 'stock');
    const weight = this.getNumber(record, 'weight');
    const unit = this.getString(record, 'unit');

    // Quantity validation
    if (quantity !== undefined) {
      if (quantity < 0) {
        errors.push({
          field: 'quantity',
          row: rowNumber,
          code: 'NEGATIVE_QUANTITY',
          message: 'Quantity cannot be negative',
          severity: 'high'
        });
      }
      
      if (quantity === 0) {
        warnings.push({
          field: 'quantity',
          row: rowNumber,
          code: 'ZERO_QUANTITY',
          message: 'Zero quantity items may indicate out-of-stock condition',
          impact: 'user_experience'
        });
      }
    }

    // Unit consistency validation
    if (unit && weight) {
      const unitLower = unit.toLowerCase();
      if ((unitLower.includes('lb') || unitLower.includes('pound')) && weight > 1000) {
        warnings.push({
          field: 'weight',
          row: rowNumber,
          code: 'WEIGHT_UNIT_MISMATCH',
          message: `Weight ${weight} seems high for unit "${unit}". Consider if unit should be tons or kg.`,
          impact: 'data_quality'
        });
      }
    }

    // SKU format validation
    const sku = this.getString(record, 'sku');
    if (sku && sku.length < 3) {
      warnings.push({
        field: 'sku',
        row: rowNumber,
        code: 'SHORT_SKU',
        message: 'SKU is very short. Consider using more descriptive product codes.',
        impact: 'user_experience'
      });
    }

    return { errors, warnings };
  }

  /**
   * Validate HACCP compliance requirements
   */
  private validateHACCPCompliance(record: Record<string, unknown>, rowNumber: number): Partial<ValidationResult> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    const temp = this.getNumber(record, 'storage_temp');
    const batchNumber = this.getString(record, 'batch_number');
    const supplier = this.getString(record, 'supplier');

    // Critical Control Point validations
    if (temp !== undefined && temp > 40) {
      errors.push({
        field: 'storage_temp',
        row: rowNumber,
        code: 'HACCP_TEMPERATURE_VIOLATION',
        message: `Storage temperature ${temp}°F exceeds HACCP limit (40°F for refrigerated seafood)`,
        severity: 'critical'
      });
    }

    // Traceability requirements
    if (!batchNumber) {
      if (this.config.validationLevel === 'comprehensive') {
        warnings.push({
          field: 'batch_number',
          row: rowNumber,
          code: 'MISSING_BATCH_NUMBER',
          message: 'Batch number recommended for complete traceability',
          impact: 'compliance'
        });
      }
    }

    if (!supplier) {
      warnings.push({
        field: 'supplier',
        row: rowNumber,
        code: 'MISSING_SUPPLIER',
        message: 'Supplier information required for HACCP compliance',
        impact: 'compliance'
      });
    }

    return { errors, warnings };
  }

  /**
   * Validate traceability information
   */
  private validateTraceability(record: Record<string, unknown>, rowNumber: number): Partial<ValidationResult> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    const batchNumber = this.getString(record, 'batch_number');
    const origin = this.getString(record, 'origin');
    const catchDate = this.getDate(record, 'catch_date');

    // Batch number format validation
    if (batchNumber) {
      if (batchNumber.length < 4) {
        warnings.push({
          field: 'batch_number',
          row: rowNumber,
          code: 'SHORT_BATCH_NUMBER',
          message: 'Batch number may be too short for effective traceability',
          impact: 'compliance'
        });
      }

      if (!/^[A-Z0-9-]+$/i.test(batchNumber)) {
        warnings.push({
          field: 'batch_number',
          row: rowNumber,
          code: 'INVALID_BATCH_FORMAT',
          message: 'Batch number contains invalid characters. Use alphanumeric and hyphens only.',
          impact: 'compliance'
        });
      }
    }

    // Origin validation
    if (origin && this.config.validationLevel === 'comprehensive') {
      const validOriginPatterns = [
        /fao\s*\d+/i,           // FAO fishing areas
        /area\s*\d+/i,          // Fishing areas
        /pacific|atlantic|gulf|bering/i, // Ocean regions
        /alaska|washington|oregon|california/i // Regional origins
      ];

      const hasValidPattern = validOriginPatterns.some(pattern => pattern.test(origin));
      if (!hasValidPattern) {
        warnings.push({
          field: 'origin',
          row: rowNumber,
          code: 'UNCLEAR_ORIGIN',
          message: 'Origin format could be more specific (e.g., FAO 67, Pacific Northwest)',
          impact: 'compliance'
        });
      }
    }

    return { errors, warnings };
  }

  /**
   * Validate relationships between different fields
   */
  private validateCrossFields(record: Record<string, unknown>, rowNumber: number): Partial<ValidationResult> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    const price = this.getNumber(record, 'price');
    const cost = this.getNumber(record, 'cost');
    const weight = this.getNumber(record, 'weight');
    const quantity = this.getNumber(record, 'quantity');

    // Price per weight calculations
    if (price && weight && quantity) {
      const pricePerPound = price / (weight / quantity);
      if (pricePerPound > 200) {
        warnings.push({
          field: 'price',
          row: rowNumber,
          code: 'HIGH_PRICE_PER_POUND',
          message: `Calculated price per pound ($${pricePerPound.toFixed(2)}) is very high`,
          impact: 'data_quality'
        });
      }
    }

    return { errors, warnings };
  }

  // Utility methods
  private getString(record: Record<string, unknown>, field: string): string | undefined {
    const value = record[field];
    return value ? String(value).trim() : undefined;
  }

  private getNumber(record: Record<string, unknown>, field: string): number | undefined {
    const value = record[field];
    if (value === null || value === undefined || value === '') return undefined;
    const num = typeof value === 'number' ? value : parseFloat(String(value).replace(/[^\d.-]/g, ''));
    return isNaN(num) ? undefined : num;
  }

  private getDate(record: Record<string, unknown>, field: string): Date | undefined {
    const value = record[field];
    if (!value) return undefined;
    try {
      const date = new Date(String(value));
      return isNaN(date.getTime()) ? undefined : date;
    } catch {
      return undefined;
    }
  }

  private getCategory(record: Record<string, unknown>): string | undefined {
    const category = this.getString(record, 'category');
    const name = this.getString(record, 'name');
    
    if (category) return category.toLowerCase();
    
    // Try to infer category from product name
    if (name) {
      for (const [categoryName, species] of this.speciesDatabase) {
        if (species.commonNames.some(commonName => name.toLowerCase().includes(commonName))) {
          return species.category;
        }
      }
    }
    
    return undefined;
  }

  private findSpeciesMatch(name: string): SeafoodSpecies | undefined {
    const nameLower = name.toLowerCase();
    
    for (const species of this.speciesDatabase.values()) {
      if (species.commonNames.some(commonName => 
        commonName === nameLower || nameLower.includes(commonName)
      )) {
        return species;
      }
    }
    
    return undefined;
  }

  private createCacheKey(record: Record<string, unknown>): string {
    // Create a cache key based on key fields (excluding row-specific data)
    const keyFields = ['name', 'category', 'price', 'weight', 'unit', 'storage_temp'];
    const keyValues = keyFields.map(field => String(record[field] || '')).join('|');
    return btoa(keyValues).substring(0, 16);
  }

  /**
   * Get validation statistics for a batch of results
   */
  static getValidationStats(results: ValidationResult[]): {
    totalRecords: number;
    validRecords: number;
    invalidRecords: number;
    totalErrors: number;
    totalWarnings: number;
    criticalErrors: number;
    correctionSuggestions: number;
  } {
    return {
      totalRecords: results.length,
      validRecords: results.filter(r => r.isValid).length,
      invalidRecords: results.filter(r => !r.isValid).length,
      totalErrors: results.reduce((sum, r) => sum + r.errors.length, 0),
      totalWarnings: results.reduce((sum, r) => sum + r.warnings.length, 0),
      criticalErrors: results.reduce((sum, r) => sum + r.errors.filter(e => e.severity === 'critical').length, 0),
      correctionSuggestions: results.reduce((sum, r) => sum + (r.corrections?.length || 0), 0)
    };
  }
}

// Type definitions
interface SeafoodSpecies {
  commonNames: string[];
  scientificName: string;
  category: string;
  averageWeight?: { min: number; max: number; unit: string };
  temperatureRange?: { min: number; max: number; unit: string };
  shelfLife?: { fresh: number; frozen: number; unit: string };
  seasons?: string[];
  regions?: string[];
}

interface PriceRange {
  min: number;
  max: number;
  unit: string;
  currency: string;
}

interface TemperatureRange {
  min: number;
  max: number;
  unit: string;
  storage_type: string;
}