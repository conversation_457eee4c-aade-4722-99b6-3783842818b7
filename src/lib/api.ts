import { supabase } from './supabase';
import type { <PERSON><PERSON><PERSON>, Customer, Product, ImportProductData, Event, Partner, Lot, TraceabilityEvent, ReceivingWithLotInput } from '../types';

const TIMEOUT_MS = 15000; // 15 seconds
const STORAGE_BUCKET = 'inventory-images';

async function withTimeout<T>(promiseOrFn: Promise<T> | (() => Promise<T>), ms: number): Promise<T> {
  const timeout = new Promise<never>((_, reject) => {
    setTimeout(() => reject(new Error('Request timed out')), ms);
  });
  const promise = typeof promiseOrFn === 'function' ? promiseOrFn() : promiseOrFn;
  return Promise.race([promise, timeout]);
}

// Vendor API
export async function createVendor(data: Partial<Vendor>) {
  try {
    const { data: vendor, error } = await supabase
      .from('vendors')
      .insert([{
        name: data.name,
        contact_name: data.contactName,
        email: data.email,
        phone: data.phone,
        address: data.address,
        status: data.status ?? 'active',
        payment_terms: data.paymentTerms,
        credit_limit: data.creditLimit,
        metadata: data.metadata,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }])
      .select()
      .single();

    if (error) {
      return {
        success: false,
        error: {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        }
      };
    }
    return { success: true, data: vendor };
  } catch (error) {
    console.error('Error in createVendor:', error);
    return {
      success: false,
      error: {
        message: 'An unexpected error occurred',
        details: error instanceof Error ? error.message : 'Unknown error'
      }
    };
  }
}

// Voice-specific vendor operations
export async function findVendorByName(name: string) {
  try {
    const { data, error } = await supabase
      .from('vendors')
      .select('id, name, address, status')
      .or(`name.ilike.%${name}%,address.ilike.%${name}%`)
      .eq('status', 'active')
      .limit(5);

    if (error) throw error;
    return { success: true, data: data || [] };
  } catch (error) {
    console.error('Error finding vendor:', error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Failed to search vendors'
      }
    };
  }
}

export async function createVendorFromVoice(name: string, info?: { location?: string; contactInfo?: string }) {
  try {
    const vendorData = {
      name: name.trim(),
      address: info?.location?.trim() ?? null,
      status: 'active',
      metadata: {
        source: 'voice_assistant',
        created_via: 'conversational_voice',
        original_input: { name, ...info }
      }
    };

    // Check if vendor already exists to avoid duplicates
    const existingCheck = await findVendorByName(name);
    if (existingCheck.success && existingCheck.data && existingCheck.data.length > 0) {
      return {
        success: false,
        error: {
          message: 'Vendor already exists',
          details: `Found existing vendor: ${existingCheck.data[0].name}`
        }
      };
    }

    return await createVendor(vendorData);
  } catch (error) {
    console.error('Error creating vendor from voice:', error);
    return {
      success: false,
      error: {
        message: 'Failed to create vendor',
        details: error instanceof Error ? error.message : 'Unknown error'
      }
    };
  }
}

export async function updateVendor(id: string, data: Partial<Vendor>) {
  try {
    const { data: vendor, error } = await supabase
      .from('vendors')
      .update({
        name: data.name,
        contact_name: data.contactName,
        email: data.email,
        phone: data.phone,
        address: data.address,
        status: data.status,
        payment_terms: data.paymentTerms,
        credit_limit: data.creditLimit,
        metadata: data.metadata,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      return {
        success: false,
        error: {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        }
      };
    }
    return { success: true, data: vendor };
  } catch (error) {
    console.error('Error in updateVendor:', error);
    return {
      success: false,
      error: {
        message: 'An unexpected error occurred',
        details: error instanceof Error ? error.message : 'Unknown error'
      }
    };
  }
}

// Customer API
export async function createCustomer(data: Partial<Customer>) {
  try {
    const { data: customer, error } = await supabase
      .from('customers')
      .insert([{
        name: data.name,
        contact_name: data.contactName,
        email: data.email,
        phone: data.phone,
        address: data.address,
        channel_type: data.channelType,
        customer_source: data.customerSource,
        status: data.status,
        payment_terms: data.paymentTerms,
        credit_limit: data.creditLimit,
        metadata: data.metadata,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }])
      .select()
      .single();

    if (error) {
      return {
        success: false,
        error: {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        }
      };
    }
    return { success: true, data: customer };
  } catch (error) {
    console.error('Error in createCustomer:', error);
    return {
      success: false,
      error: {
        message: 'An unexpected error occurred',
        details: error instanceof Error ? error.message : 'Unknown error'
      }
    };
  }
}

export async function updateCustomer(id: string, data: Partial<Customer>) {
  try {
    const { data: customer, error } = await supabase
      .from('customers')
      .update({
        name: data.name,
        contact_name: data.contactName,
        email: data.email,
        phone: data.phone,
        address: data.address,
        channel_type: data.channelType,
        customer_source: data.customerSource,
        status: data.status,
        payment_terms: data.paymentTerms,
        credit_limit: data.creditLimit,
        metadata: data.metadata,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      return {
        success: false,
        error: {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        }
      };
    }
    return { success: true, data: customer };
  } catch (error) {
    console.error('Error in updateCustomer:', error);
    return {
      success: false,
      error: {
        message: 'An unexpected error occurred',
        details: error instanceof Error ? error.message : 'Unknown error'
      }
    };
  }
}

// Product API
export async function createProduct(data: Partial<Product>) {
  try {
    const { data: product, error } = await supabase
      .from('products')
      .insert([{
        name: data.name,
        amount: data.amount,
        condition: data.condition,
        other_condition: data.otherCondition,
        category: data.category,
        sub_category: data.subCategory,
        price: data.price,
        supplier_id: data.supplierId,
        supplier: data.supplier,
        species_details: data.speciesDetails,
        notes: data.notes,
        images: data.image ? [data.image] : [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }])
      .select()
      .single();

    if (error) {
      return {
        success: false,
        error: {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        }
      };
    }

    return { success: true, data: product };
  } catch (error) {
    console.error('Error in createProduct:', error);
    return {
      success: false,
      error: {
        message: 'An unexpected error occurred',
        details: error instanceof Error ? error.message : 'Unknown error'
      }
    };
  }
}

export async function updateProduct(id: string, data: Partial<Product>) {
  try {
    const { data: product, error } = await supabase
      .from('products')
      .update({
        name: data.name,
        amount: data.amount,
        condition: data.condition,
        other_condition: data.otherCondition,
        category: data.category,
        sub_category: data.subCategory,
        price: data.price,
        supplier_id: data.supplierId,
        supplier: data.supplier,
        species_details: data.speciesDetails,
        notes: data.notes,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      return {
        success: false,
        error: {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        }
      };
    }

    return { success: true, data: product };
  } catch (error) {
    console.error('Error in updateProduct:', error);
    return {
      success: false,
      error: {
        message: 'An unexpected error occurred',
        details: error instanceof Error ? error.message : 'Unknown error'
      }
    };
  }
}

// Event API
export async function createEvent(data: Event, eventType: string) {
  try {
    const { data: event, error } = await supabase
      .from('events')
      .insert([{
        event_type: eventType,
        product_id: data.product_id,
        vendor_id: data.vendor_id,
        customer_id: data.customer_id,
        quantity: data.quantity,
        unit: data.unit,
        temperature: data.temperature,
        quality_status: data.quality_status,
        notes: data.notes,
        metadata: data.metadata,
        gdst_data: data.gdst_data,
        created_by: (await supabase.auth.getUser()).data.user?.id,
        created_at: new Date().toISOString()
      }])
      .select()
      .single();

    if (error) {
      return {
        success: false,
        error: {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        }
      };
    }

    return { success: true, data: event };
  } catch (error) {
    console.error('Error in createEvent:', error);
    return {
      success: false,
      error: {
        message: 'An unexpected error occurred',
        details: error instanceof Error ? error.message : 'Unknown error'
      }
    };
  }
}

// ===== Phase 1 Traceability API =====
async function upsertPartnerByName(name: string, type: Partner['type'] = 'supplier') {
  const { data: existing, error: selError } = await supabase
    .from('partners')
    .select('id, name, type')
    .eq('name', name)
    .eq('type', type)
    .limit(1)
    .maybeSingle();
  if (selError && selError.code !== 'PGRST116') throw selError; // ignore no rows
  if (existing?.id) return existing.id as string;

  const { data: inserted, error: insError } = await supabase
    .from('partners')
    .insert([{ name, type }])
    .select('id')
    .single();
  if (insError) throw insError;
  if (!inserted) throw new Error('Failed to create event - no data returned');
  return inserted.id as string;
}

async function getProductIdByName(name: string) {
  const { data, error } = await supabase
    .from('products')
    .select('id, name')
    .eq('name', name)
    .limit(1)
    .maybeSingle();
  if (error) throw error;
  return data?.id as string | undefined;
}

async function createLotRecord(lot: Partial<Lot>) {
  const { data, error } = await supabase
    .from('lots')
    .insert([lot])
    .select('id, tlc')
    .single();
  if (error) throw error;
  return data;
}

async function createTraceabilityEventRecord(event: Partial<TraceabilityEvent>) {
  const { data, error } = await supabase
    .from('traceability_events')
    .insert([event])
    .select('id')
    .single();
  if (error) throw error;
  return data;
}

async function linkEventLot(event_id: string, lot_id: string, role: 'input' | 'output', qty?: number, uom?: string) {
  const { data, error } = await supabase
    .from('event_lots')
    .insert([{ event_id, lot_id, role, qty: qty ?? null, uom: uom ?? null }])
    .select('id')
    .single();
  if (error) throw error;
  return data;
}

export async function createReceivingWithLot(input: ReceivingWithLotInput) {
  try {
    // Resolve product
    const productId = await getProductIdByName(input.productName);
    if (!productId) {
      return { success: false, error: { message: `Product not found: ${input.productName}` } };
    }

    // Ensure supplier partner exists
    const supplierId = await upsertPartnerByName(input.vendorName, 'supplier');

    // Create receiving event
    const event = await createTraceabilityEventRecord({
      event_type: 'receiving',
      event_time: input.receivingDate ? new Date(input.receivingDate).toISOString() : new Date().toISOString(),
      actor_partner_id: supplierId,
      notes: input.notes ?? null,
    });

    // Create lot (TLC auto-assigned via trigger unless provided manually)
    const lot = await createLotRecord({
      product_id: productId,
      initial_qty: input.quantity,
      uom: input.unit ?? null,
      tlc: input.tlc, // allow manual TLC if provided
      notes: input.condition ? `Condition: ${input.condition}${input.notes ? `; ${input.notes}` : ''}` : input.notes ?? null,
    });

    // Link event to new lot as output of receiving
    await linkEventLot(event.id as string, lot.id as string, 'output', input.quantity, input.unit);

    return { success: true, data: { event_id: event.id, lot_id: lot.id, tlc: lot.tlc } };
  } catch (error) {
    // Check if this is the expected "traceability tables don't exist" error
    const isTableMissing = error && typeof error === 'object' && 'code' in error && error.code === '42P01';
    if (isTableMissing) {
      console.warn('Traceability tables not available, will fallback to inventory_events only');
    } else {
      console.error('Error in createReceivingWithLot:', error);
    }
    return { success: false, error: { message: error instanceof Error ? error.message : 'Unknown error' } };
  }
}

// Add batch operation support
export async function batchCreateProducts(products: ImportProductData[]) {
  const BATCH_SIZE = 10;
  const results = [];
  
  for (let i = 0; i < products.length; i += BATCH_SIZE) {
    const batch = products.slice(i, i + BATCH_SIZE);
    const batchData = batch.map(product => ({
      name: product.name,
      vendor: product.vendor,
      condition: product.condition,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }));

    try {
      const result = await withTimeout(async () => {
        return await supabase
          .from('products')
          .insert(batchData)
          .select();
      }, TIMEOUT_MS);

      const { data, error } = result;

      if (error) throw error;
      results.push(...(data || []));
    } catch (error) {
      console.error(`Batch ${i / BATCH_SIZE + 1} failed:`, error);
      // Continue with next batch instead of failing completely
    }
  }

  return results;
}

// Batch code generation for conversational voice
export async function generateBatchCode(productName?: string, vendorName?: string, date?: string): Promise<string> {
  try {
    const useDate = date ? new Date(date) : new Date();
    const dateStr = useDate.toISOString().slice(2, 10).replace(/-/g, ''); // YYMMDD
    
    // Generate product code from name
    const productCode = productName 
      ? productName.replace(/[^a-zA-Z]/g, '').substring(0, 2).toUpperCase() || 'PR'
      : 'XX';
    
    // Generate vendor code from name
    const vendorCode = vendorName 
      ? vendorName.replace(/[^a-zA-Z]/g, '').substring(0, 2).toUpperCase() || 'VN'
      : 'XX';
    
    // Add time component for uniqueness
    const timeCode = useDate.getHours().toString().padStart(2, '0') + 
                     useDate.getMinutes().toString().padStart(2, '0');
    
    const batchCode = `${productCode}-${vendorCode}-${dateStr}-${timeCode}`;
    
    // Check for existing batch codes to ensure uniqueness
    const { data: existing } = await supabase
      .from('inventory_events')
      .select('metadata')
      .like('metadata->batch_code', `${batchCode}%`)
      .limit(1);
    
    if (existing && existing.length > 0) {
      // Add sequence number if batch code exists
      const sequence = Math.floor(Math.random() * 100).toString().padStart(2, '0');
      return `${batchCode}-${sequence}`;
    }
    
    return batchCode;
  } catch (error) {
    console.error('Error generating batch code:', error);
    // Fallback to simple timestamp-based code
    const timestamp = Date.now().toString().slice(-8);
    return `BATCH-${timestamp}`;
  }
}

// Product lookup with fuzzy matching for voice
export async function findProductByName(name: string) {
  try {
    const { data, error } = await supabase
      .from('products')
      .select('id, name, category, sub_category')
      .or(`name.ilike.%${name}%,category.ilike.%${name}%,sub_category.ilike.%${name}%`)
      .limit(10);

    if (error) throw error;
    
    // Sort by relevance (exact matches first, then partial matches)
    const results = (data || []).sort((a, b) => {
      const aExact = a.name.toLowerCase() === name.toLowerCase() ? 1 : 0;
      const bExact = b.name.toLowerCase() === name.toLowerCase() ? 1 : 0;
      if (aExact !== bExact) return bExact - aExact;
      
      const aIncludes = a.name.toLowerCase().includes(name.toLowerCase()) ? 1 : 0;
      const bIncludes = b.name.toLowerCase().includes(name.toLowerCase()) ? 1 : 0;
      return bIncludes - aIncludes;
    });
    
    return { success: true, data: results };
  } catch (error) {
    console.error('Error finding product:', error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Failed to search products'
      }
    };
  }
}

export async function uploadProductImage(file: File): Promise<string> {
  try {
    const fileExt = file.name.split('.').pop();
    const fileName = `${Date.now()}.${fileExt}`;
    const filePath = `products/${fileName}`;

    console.log('Uploading file:', {
      originalName: file.name,
      fileName,
      filePath,
      size: file.size,
      type: file.type
    });

    const { error: uploadError } = await supabase.storage
      .from(STORAGE_BUCKET)
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (uploadError) {
      console.error('Upload error:', uploadError);
      throw uploadError;
    }

    console.log('File uploaded successfully to path:', filePath);
    return filePath;  // Return just the path, let getProductImageUrl handle URL generation
  } catch (error) {
    console.error('Error uploading image:', error);
    throw error;
  }
}

export async function deleteProductImage(path: string): Promise<void> {
  try {
    console.log('Deleting image at path:', path);

    // If it's a URL, try to extract the path
    if (path.startsWith('http')) {
      try {
        const url = new URL(path);
        const pathMatch = url.pathname.match(/\/object\/(public|sign)\/([^?]+)/);
        if (pathMatch) {
          path = decodeURIComponent(pathMatch[2]);
          console.log('Extracted path from URL:', path);
        } else {
          throw new Error('Invalid image URL format');
        }
      } catch (error) {
        console.error('Error parsing URL:', error);
        throw new Error('Invalid image URL');
      }
    }

    // Clean the path and ensure it's in the products folder
    const cleanPath = path.replace(/^\/+/, '');
    const fullPath = cleanPath.startsWith('products/') ? cleanPath : `products/${cleanPath}`;

    console.log('Deleting file at path:', fullPath);
    const { error } = await supabase.storage
      .from(STORAGE_BUCKET)
      .remove([fullPath]);

    if (error) {
      console.error('Error deleting file:', error);
      throw error;
    }

    console.log('Successfully deleted file');
  } catch (error) {
    console.error('Error in deleteProductImage:', error);
    throw error;
  }
}

export async function getProductImageUrl(path: string): Promise<string> {
  try {
    console.log('Getting signed URL for path:', path);

    // If the path is already a full URL with a token, return it
    if (path.includes('token=')) {
      console.log('Path already has token, returning:', path);
      return path;
    }

    // If the path is a full URL without a token, try to get the path part
    if (path.startsWith('http')) {
      try {
        const url = new URL(path);
        // Handle various Supabase URL formats
        const pathMatch = url.pathname.match(/\/(?:storage|object)\/v1\/(?:object|sign)\/([^/]+)\/(.+?)(?:\?|$)/);
        if (pathMatch) {
          const [, bucket, filePath] = pathMatch;
          if (bucket === STORAGE_BUCKET) {
            path = decodeURIComponent(filePath);
          } else {
            console.log('URL is from a different bucket, returning original:', path);
            return path;
          }
          console.log('Extracted path from URL:', path);
        } else {
          console.log('Could not extract path from URL, returning original:', path);
          return path;
        }
      } catch {
        console.log('Error parsing URL, returning original:', path);
        return path;
      }
    }

    // Clean the path by removing any leading slashes and ensure it's in the products folder
    const cleanPath = path.replace(/^\/+/, '');
    const fullPath = cleanPath.startsWith('products/') ? cleanPath : `products/${cleanPath}`;
    
    console.log('Getting signed URL for cleaned path:', fullPath);

    // Get a signed URL that's valid for 1 hour (3600 seconds)
    const { data, error } = await supabase.storage
      .from(STORAGE_BUCKET)
      .createSignedUrl(fullPath, 3600);

    if (error) {
      console.error('Error getting signed URL:', error);
      throw error;
    }

    if (!data?.signedUrl) {
      console.error('No signed URL returned');
      throw new Error('Failed to get signed URL');
    }

    console.log('Successfully got signed URL:', data.signedUrl);
    return data.signedUrl;
  } catch (error) {
    console.error('Error in getProductImageUrl:', error);
    // If we fail to get a signed URL, try to get a public URL as fallback
    try {
      const { data } = await supabase.storage
        .from(STORAGE_BUCKET)
        .getPublicUrl(path);
      
      if (data?.publicUrl) {
        console.log('Falling back to public URL:', data.publicUrl);
        return data.publicUrl;
      }
    } catch (fallbackError) {
      console.error('Fallback to public URL also failed:', fallbackError);
    }
    throw error;
  }
}

// Add batch operation support
export async function batchCreateCustomers(customers: Partial<Customer>[]) {
  const BATCH_SIZE = 10;
  const results = [];
  
  for (let i = 0; i < customers.length; i += BATCH_SIZE) {
    const batch = customers.slice(i, i + BATCH_SIZE);
    const batchData = batch.map(customer => ({
      name: customer.name,
      contact_name: customer.contactName,
      email: customer.email,
      phone: customer.phone,
      address: customer.address,
      channel_type: customer.channelType,
      customer_source: customer.customerSource,
      status: customer.status ?? 'active',
      payment_terms: customer.paymentTerms,
      credit_limit: customer.creditLimit,
      metadata: customer.metadata,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }));

    try {
      const result = await withTimeout(async () => {
        return await supabase
          .from('customers')
          .insert(batchData)
          .select();
      }, TIMEOUT_MS);

      const { data, error } = result;

      if (error) throw error;
      results.push(...(data || []));
    } catch (error) {
      console.error(`Batch ${i / BATCH_SIZE + 1} failed:`, error);
      // Continue with next batch instead of failing completely
    }
  }

  return results;
}
