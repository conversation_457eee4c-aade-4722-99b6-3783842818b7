/**
 * Report Templates System for Temperature Monitoring
 * 
 * Defines and manages different report templates for various use cases,
 * from daily summaries to comprehensive HACCP compliance reports.
 */

import type {
  TemperatureReportData,
  TemperatureReportParams,
  TemperatureAlert,
  Sensor,
  StorageArea
} from '../types/tempstick';

import type { PDFSection, PDFTemplate } from './pdf-report-generator';

export interface ReportTemplateConfig {
  id: string;
  name: string;
  description: string;
  category: 'operational' | 'compliance' | 'analytical' | 'regulatory';
  estimatedPages: number;
  estimatedGenerationTime: number; // seconds
  requiredData: {
    sensors: boolean;
    readings: boolean;
    alerts: boolean;
    haccpData: boolean;
    storageAreas: boolean;
  };
  customizable: {
    dateRange: boolean;
    sensorSelection: boolean;
    chartTypes: boolean;
    sections: boolean;
  };
  outputFormats: ('pdf' | 'excel' | 'csv')[];
  automationSupported: boolean;
  complianceLevel: 'basic' | 'haccp' | 'regulatory' | 'audit';
}

export interface TemplateSection {
  id: string;
  name: string;
  type: PDFSection['type'];
  required: boolean;
  description: string;
  estimatedSize: 'small' | 'medium' | 'large';
  dependencies?: string[]; // Other section IDs this depends on
}

export interface CustomizableTemplate {
  baseTemplate: string;
  customSections: TemplateSection[];
  excludedSections: string[];
  chartConfigurations: {
    [sectionId: string]: {
      type: 'line' | 'bar' | 'pie' | 'scatter';
      options: Record<string, any>;
    };
  };
  branding?: {
    logo?: string;
    companyName?: string;
    colors?: {
      primary: string;
      secondary: string;
      accent: string;
    };
  };
}

/**
 * Report Templates Registry
 */
export class ReportTemplateRegistry {
  private static instance: ReportTemplateRegistry;
  private templates: Map<string, ReportTemplateConfig> = new Map();
  private sections: Map<string, TemplateSection> = new Map();

  constructor() {
    this.initializeDefaultTemplates();
    this.initializeDefaultSections();
  }

  static getInstance(): ReportTemplateRegistry {
    if (!ReportTemplateRegistry.instance) {
      ReportTemplateRegistry.instance = new ReportTemplateRegistry();
    }
    return ReportTemplateRegistry.instance;
  }

  /**
   * Get all available templates
   */
  getTemplates(category?: ReportTemplateConfig['category']): ReportTemplateConfig[] {
    const allTemplates = Array.from(this.templates.values());
    return category ? allTemplates.filter(t => t.category === category) : allTemplates;
  }

  /**
   * Get specific template configuration
   */
  getTemplate(templateId: string): ReportTemplateConfig | undefined {
    return this.templates.get(templateId);
  }

  /**
   * Get template sections
   */
  getTemplateSections(templateId: string): TemplateSection[] {
    const template = this.templates.get(templateId);
    if (!template) return [];

    // Return sections based on template requirements
    return Array.from(this.sections.values()).filter(section => {
      switch (templateId) {
        case 'daily-summary':
          return ['cover', 'summary', 'hourly-trend', 'sensor-status'].includes(section.id);
        case 'haccp-compliance':
          return ['cover', 'compliance-overview', 'violations', 'corrective-actions', 'compliance-chart'].includes(section.id);
        case 'comprehensive':
          return true; // Include all sections
        case 'sensor-performance':
          return ['cover', 'performance-summary', 'reliability-chart', 'statistics-table', 'maintenance-schedule'].includes(section.id);
        case 'alert-history':
          return ['cover', 'alert-summary', 'alert-timeline', 'alert-table', 'resolution-tracking'].includes(section.id);
        case 'regulatory-audit':
          return ['cover', 'audit-summary', 'compliance-statement', 'violations', 'corrective-actions', 'appendix'].includes(section.id);
        default:
          return false;
      }
    });
  }

  /**
   * Create customizable template
   */
  createCustomTemplate(
    baseTemplateId: string,
    customization: Partial<CustomizableTemplate>
  ): CustomizableTemplate {
    const baseTemplate = this.templates.get(baseTemplateId);
    if (!baseTemplate) {
      throw new Error(`Base template '${baseTemplateId}' not found`);
    }

    return {
      baseTemplate: baseTemplateId,
      customSections: customization.customSections || [],
      excludedSections: customization.excludedSections || [],
      chartConfigurations: customization.chartConfigurations || {},
      branding: customization.branding
    };
  }

  /**
   * Generate PDF template from configuration
   */
  generatePDFTemplate(
    templateId: string,
    reportData: TemperatureReportData,
    params: TemperatureReportParams,
    customization?: CustomizableTemplate
  ): PDFTemplate {
    const templateConfig = this.templates.get(templateId);
    if (!templateConfig) {
      throw new Error(`Template '${templateId}' not found`);
    }

    let sections = this.getTemplateSections(templateId);

    // Apply customizations
    if (customization) {
      // Remove excluded sections
      sections = sections.filter(section => !customization.excludedSections.includes(section.id));
      
      // Add custom sections
      sections.push(...customization.customSections);
    }

    // Convert template sections to PDF sections
    const pdfSections: PDFSection[] = sections.map(section => 
      this.convertToPDFSection(section, reportData, params, customization)
    );

    const startDate = new Date(reportData.summary.reportPeriod.start);
    const endDate = new Date(reportData.summary.reportPeriod.end);
    const companyName = customization?.branding?.companyName || 'Pacific Cloud Seafoods';

    return {
      name: templateConfig.name,
      description: templateConfig.description,
      sections: pdfSections,
      metadata: {
        title: this.generateTitle(templateConfig, startDate, endDate),
        subject: this.generateSubject(templateConfig),
        author: `${companyName} - Seafood Manager`,
        keywords: this.generateKeywords(templateConfig)
      }
    };
  }

  /**
   * Validate template requirements
   */
  validateTemplateRequirements(
    templateId: string,
    reportData: TemperatureReportData
  ): { valid: boolean; missingRequirements: string[] } {
    const template = this.templates.get(templateId);
    if (!template) {
      return { valid: false, missingRequirements: ['Template not found'] };
    }

    const missing: string[] = [];

    if (template.requiredData.sensors && (!reportData.sensorData || reportData.sensorData.length === 0)) {
      missing.push('Sensor data is required but not available');
    }

    if (template.requiredData.readings) {
      const hasReadings = reportData.sensorData?.some(sensor => sensor.readings.length > 0);
      if (!hasReadings) {
        missing.push('Temperature readings are required but not available');
      }
    }

    if (template.requiredData.alerts && (!reportData.alerts || reportData.alerts.length === 0)) {
      if (templateId === 'alert-history' || templateId === 'haccp-compliance') {
        missing.push('Alert data is required for this report type');
      }
    }

    if (template.requiredData.haccpData && (!reportData.haccpCompliance || reportData.haccpCompliance.length === 0)) {
      if (templateId === 'haccp-compliance' || templateId === 'regulatory-audit') {
        missing.push('HACCP compliance data is required for this report type');
      }
    }

    return {
      valid: missing.length === 0,
      missingRequirements: missing
    };
  }

  /**
   * Get estimated generation time and resource usage
   */
  getGenerationEstimate(templateId: string, reportData: TemperatureReportData): {
    estimatedTimeSeconds: number;
    estimatedPages: number;
    memoryUsageMB: number;
  } {
    const template = this.templates.get(templateId);
    if (!template) {
      throw new Error(`Template '${templateId}' not found`);
    }

    const dataSize = this.calculateDataSize(reportData);
    const baseTime = template.estimatedGenerationTime;
    const basePages = template.estimatedPages;

    // Adjust estimates based on data volume
    const dataMultiplier = Math.max(1, Math.log10(dataSize.totalReadings + 1) / 2);
    const chartMultiplier = this.countCharts(templateId) * 0.5;

    return {
      estimatedTimeSeconds: Math.ceil(baseTime * dataMultiplier + chartMultiplier),
      estimatedPages: Math.ceil(basePages + (dataSize.totalAlerts > 100 ? 2 : 0)),
      memoryUsageMB: Math.ceil(dataSize.totalReadings * 0.001 + this.countCharts(templateId) * 2)
    };
  }

  // Private initialization methods

  private initializeDefaultTemplates(): void {
    const templates: ReportTemplateConfig[] = [
      {
        id: 'daily-summary',
        name: 'Daily Temperature Summary',
        description: '24-hour sensor overview with key metrics and trends',
        category: 'operational',
        estimatedPages: 2,
        estimatedGenerationTime: 5,
        requiredData: {
          sensors: true,
          readings: true,
          alerts: false,
          haccpData: false,
          storageAreas: false
        },
        customizable: {
          dateRange: false,
          sensorSelection: true,
          chartTypes: false,
          sections: false
        },
        outputFormats: ['pdf', 'excel'],
        automationSupported: true,
        complianceLevel: 'basic'
      },
      {
        id: 'haccp-compliance',
        name: 'HACCP Compliance Report',
        description: 'Comprehensive HACCP violation tracking and corrective actions',
        category: 'compliance',
        estimatedPages: 8,
        estimatedGenerationTime: 15,
        requiredData: {
          sensors: true,
          readings: true,
          alerts: true,
          haccpData: true,
          storageAreas: true
        },
        customizable: {
          dateRange: true,
          sensorSelection: true,
          chartTypes: true,
          sections: true
        },
        outputFormats: ['pdf'],
        automationSupported: true,
        complianceLevel: 'haccp'
      },
      {
        id: 'comprehensive',
        name: 'Comprehensive Analysis',
        description: 'Complete temperature monitoring report with all available data',
        category: 'analytical',
        estimatedPages: 15,
        estimatedGenerationTime: 30,
        requiredData: {
          sensors: true,
          readings: true,
          alerts: true,
          haccpData: true,
          storageAreas: true
        },
        customizable: {
          dateRange: true,
          sensorSelection: true,
          chartTypes: true,
          sections: true
        },
        outputFormats: ['pdf', 'excel'],
        automationSupported: false,
        complianceLevel: 'audit'
      },
      {
        id: 'sensor-performance',
        name: 'Sensor Performance Analysis',
        description: 'Detailed sensor reliability, statistics, and maintenance insights',
        category: 'analytical',
        estimatedPages: 6,
        estimatedGenerationTime: 12,
        requiredData: {
          sensors: true,
          readings: true,
          alerts: false,
          haccpData: false,
          storageAreas: false
        },
        customizable: {
          dateRange: true,
          sensorSelection: true,
          chartTypes: true,
          sections: false
        },
        outputFormats: ['pdf', 'excel'],
        automationSupported: true,
        complianceLevel: 'basic'
      },
      {
        id: 'alert-history',
        name: 'Alert History Report',
        description: 'Complete alert documentation and resolution tracking',
        category: 'operational',
        estimatedPages: 10,
        estimatedGenerationTime: 20,
        requiredData: {
          sensors: true,
          readings: false,
          alerts: true,
          haccpData: false,
          storageAreas: false
        },
        customizable: {
          dateRange: true,
          sensorSelection: true,
          chartTypes: false,
          sections: false
        },
        outputFormats: ['pdf', 'excel', 'csv'],
        automationSupported: true,
        complianceLevel: 'basic'
      },
      {
        id: 'regulatory-audit',
        name: 'Regulatory Audit Package',
        description: 'Complete documentation package for regulatory inspections and audits',
        category: 'regulatory',
        estimatedPages: 25,
        estimatedGenerationTime: 45,
        requiredData: {
          sensors: true,
          readings: true,
          alerts: true,
          haccpData: true,
          storageAreas: true
        },
        customizable: {
          dateRange: true,
          sensorSelection: false,
          chartTypes: false,
          sections: false
        },
        outputFormats: ['pdf'],
        automationSupported: false,
        complianceLevel: 'regulatory'
      }
    ];

    templates.forEach(template => {
      this.templates.set(template.id, template);
    });
  }

  private initializeDefaultSections(): void {
    const sections: TemplateSection[] = [
      {
        id: 'cover',
        name: 'Cover Page',
        type: 'cover',
        required: true,
        description: 'Professional cover page with report metadata',
        estimatedSize: 'small'
      },
      {
        id: 'summary',
        name: 'Executive Summary',
        type: 'summary',
        required: true,
        description: 'High-level overview of key metrics and findings',
        estimatedSize: 'medium'
      },
      {
        id: 'hourly-trend',
        name: 'Hourly Temperature Trends',
        type: 'chart',
        required: false,
        description: 'Temperature trends broken down by hour of day',
        estimatedSize: 'medium'
      },
      {
        id: 'sensor-status',
        name: 'Current Sensor Status',
        type: 'table',
        required: false,
        description: 'Current status and latest readings for all sensors',
        estimatedSize: 'small'
      },
      {
        id: 'compliance-overview',
        name: 'HACCP Compliance Overview',
        type: 'compliance',
        required: false,
        description: 'HACCP compliance summary with storage area analysis',
        estimatedSize: 'large',
        dependencies: ['summary']
      },
      {
        id: 'violations',
        name: 'Temperature Violations',
        type: 'alerts',
        required: false,
        description: 'Detailed list of temperature violations and HACCP breaches',
        estimatedSize: 'large'
      },
      {
        id: 'corrective-actions',
        name: 'Corrective Actions Taken',
        type: 'text',
        required: false,
        description: 'Documentation of corrective actions for violations',
        estimatedSize: 'medium',
        dependencies: ['violations']
      },
      {
        id: 'compliance-chart',
        name: 'Compliance Rate by Storage Area',
        type: 'chart',
        required: false,
        description: 'Visual breakdown of compliance rates across storage areas',
        estimatedSize: 'medium',
        dependencies: ['compliance-overview']
      },
      {
        id: 'performance-summary',
        name: 'Sensor Performance Summary',
        type: 'table',
        required: false,
        description: 'Detailed performance statistics for each sensor',
        estimatedSize: 'large'
      },
      {
        id: 'reliability-chart',
        name: 'Sensor Reliability Chart',
        type: 'chart',
        required: false,
        description: 'Visual representation of sensor uptime and reliability',
        estimatedSize: 'medium'
      },
      {
        id: 'statistics-table',
        name: 'Detailed Statistics Table',
        type: 'table',
        required: false,
        description: 'Comprehensive statistical analysis of sensor data',
        estimatedSize: 'large'
      },
      {
        id: 'maintenance-schedule',
        name: 'Maintenance Schedule',
        type: 'table',
        required: false,
        description: 'Recommended maintenance schedule based on sensor performance',
        estimatedSize: 'small'
      },
      {
        id: 'alert-summary',
        name: 'Alert Summary',
        type: 'summary',
        required: false,
        description: 'Summary of alerts by type, severity, and resolution status',
        estimatedSize: 'medium'
      },
      {
        id: 'alert-timeline',
        name: 'Alert Timeline',
        type: 'chart',
        required: false,
        description: 'Timeline visualization of alerts over the reporting period',
        estimatedSize: 'medium'
      },
      {
        id: 'alert-table',
        name: 'Detailed Alert History',
        type: 'table',
        required: false,
        description: 'Complete table of all alerts with full details',
        estimatedSize: 'large'
      },
      {
        id: 'resolution-tracking',
        name: 'Resolution Tracking',
        type: 'table',
        required: false,
        description: 'Tracking of alert resolution times and actions taken',
        estimatedSize: 'medium',
        dependencies: ['alert-table']
      },
      {
        id: 'audit-summary',
        name: 'Audit Summary Statement',
        type: 'text',
        required: false,
        description: 'Executive summary for regulatory audit purposes',
        estimatedSize: 'medium'
      },
      {
        id: 'compliance-statement',
        name: 'Compliance Statement',
        type: 'text',
        required: false,
        description: 'Formal compliance statement with regulatory references',
        estimatedSize: 'small'
      },
      {
        id: 'appendix',
        name: 'Regulatory Appendix',
        type: 'text',
        required: false,
        description: 'Additional documentation required for regulatory compliance',
        estimatedSize: 'large'
      }
    ];

    sections.forEach(section => {
      this.sections.set(section.id, section);
    });
  }

  // Private helper methods

  private convertToPDFSection(
    section: TemplateSection,
    reportData: TemperatureReportData,
    params: TemperatureReportParams,
    customization?: CustomizableTemplate
  ): PDFSection {
    let content: any;
    let pageBreak = false;

    switch (section.type) {
      case 'cover':
        content = {
          title: this.generateCoverTitle(section.id, reportData),
          subtitle: `${reportData.summary.reportPeriod.start} - ${reportData.summary.reportPeriod.end}`,
          organization: customization?.branding?.companyName || 'Pacific Cloud Seafoods',
          generatedDate: new Date(),
          reportType: this.templates.get(customization?.baseTemplate || 'comprehensive')?.name || 'Temperature Report',
          confidentialityLevel: 'Internal Use'
        };
        pageBreak = true;
        break;

      case 'summary':
        content = reportData.summary;
        break;

      case 'chart':
        content = this.generateChartConfig(section.id, reportData, customization);
        break;

      case 'table':
        content = this.generateTableConfig(section.id, reportData);
        break;

      case 'compliance':
        content = reportData.haccpCompliance;
        pageBreak = true;
        break;

      case 'alerts':
        content = reportData.alerts;
        pageBreak = true;
        break;

      case 'text':
        content = this.generateTextContent(section.id, reportData);
        break;

      default:
        content = { message: 'Content not implemented for this section type' };
    }

    return {
      type: section.type,
      title: section.name,
      content,
      pageBreak
    };
  }

  private generateCoverTitle(sectionId: string, reportData: TemperatureReportData): string {
    const startDate = new Date(reportData.summary.reportPeriod.start);
    const endDate = new Date(reportData.summary.reportPeriod.end);
    
    if (sectionId.includes('daily')) {
      return 'Daily Temperature Summary';
    } else if (sectionId.includes('haccp')) {
      return 'HACCP Compliance Report';
    } else if (sectionId.includes('audit')) {
      return 'Regulatory Audit Package';
    } else {
      return 'Temperature Monitoring Report';
    }
  }

  private generateChartConfig(
    sectionId: string,
    reportData: TemperatureReportData,
    customization?: CustomizableTemplate
  ): any {
    const customChart = customization?.chartConfigurations[sectionId];
    
    switch (sectionId) {
      case 'hourly-trend':
        return {
          type: customChart?.type || 'line',
          data: this.prepareHourlyTrendData(reportData),
          options: {
            xLabel: 'Hour of Day',
            yLabel: 'Temperature (°F)',
            showGrid: true,
            temperatureUnit: 'F',
            ...customChart?.options
          }
        };

      case 'compliance-chart':
        return {
          type: customChart?.type || 'pie',
          data: this.prepareComplianceChartData(reportData),
          options: {
            showLegend: true,
            ...customChart?.options
          }
        };

      case 'reliability-chart':
        return {
          type: customChart?.type || 'bar',
          data: this.prepareSensorReliabilityData(reportData),
          options: {
            xLabel: 'Sensors',
            yLabel: 'Uptime %',
            showGrid: true,
            ...customChart?.options
          }
        };

      case 'alert-timeline':
        return {
          type: customChart?.type || 'line',
          data: this.prepareAlertTimelineData(reportData),
          options: {
            xLabel: 'Time',
            yLabel: 'Number of Alerts',
            showGrid: true,
            ...customChart?.options
          }
        };

      default:
        return {
          type: 'line',
          data: [],
          options: {}
        };
    }
  }

  private generateTableConfig(sectionId: string, reportData: TemperatureReportData): any {
    switch (sectionId) {
      case 'sensor-status':
        return {
          headers: ['Sensor', 'Location', 'Current Temp', 'Status', 'Last Reading'],
          rows: reportData.sensorData.map(sensor => {
            const lastReading = sensor.readings[sensor.readings.length - 1];
            return [
              sensor.sensor.name,
              sensor.sensor.location,
              lastReading ? `${lastReading.temperature.toFixed(1)}°F` : 'No Data',
              sensor.statistics.alertsCount === 0 ? '✓ Normal' : '⚠ Alert',
              lastReading ? new Date(lastReading.reading_timestamp).toLocaleString() : 'N/A'
            ];
          })
        };

      case 'performance-summary':
        return {
          headers: ['Sensor', 'Location', 'Avg Temp', 'Min/Max', 'Readings', 'Alerts', 'Reliability'],
          rows: reportData.sensorData.map(sensor => [
            sensor.sensor.name,
            sensor.sensor.location,
            `${sensor.statistics.avgTemp.toFixed(1)}°F`,
            `${sensor.statistics.minTemp.toFixed(1)}°F / ${sensor.statistics.maxTemp.toFixed(1)}°F`,
            sensor.statistics.readingsCount.toString(),
            sensor.statistics.alertsCount.toString(),
            '95%' // Placeholder - would calculate actual reliability
          ])
        };

      case 'statistics-table':
        return {
          headers: ['Sensor', 'Mean', 'Std Dev', 'Min', 'Max', 'Range', 'Variance'],
          rows: reportData.sensorData.map(sensor => {
            const temps = sensor.readings.map(r => r.temperature);
            const mean = temps.reduce((sum, t) => sum + t, 0) / temps.length;
            const variance = temps.reduce((sum, t) => sum + Math.pow(t - mean, 2), 0) / temps.length;
            const stdDev = Math.sqrt(variance);

            return [
              sensor.sensor.name,
              `${mean.toFixed(2)}°F`,
              `${stdDev.toFixed(2)}°F`,
              `${Math.min(...temps).toFixed(1)}°F`,
              `${Math.max(...temps).toFixed(1)}°F`,
              `${(Math.max(...temps) - Math.min(...temps)).toFixed(1)}°F`,
              `${variance.toFixed(2)}`
            ];
          })
        };

      default:
        return { headers: [], rows: [] };
    }
  }

  private generateTextContent(sectionId: string, reportData: TemperatureReportData): string {
    switch (sectionId) {
      case 'corrective-actions':
        const haccpViolations = reportData.alerts.filter(a => a.alert_type === 'haccp_violation');
        if (haccpViolations.length === 0) {
          return 'No HACCP violations occurred during the reporting period. All temperature control points maintained proper temperatures within specified limits.';
        }
        return `During the reporting period, ${haccpViolations.length} HACCP violations were recorded. All violations were addressed according to established corrective action procedures including immediate temperature correction, product assessment, and documentation of actions taken. Detailed corrective action records are maintained in the HACCP monitoring logs.`;

      case 'audit-summary':
        return `This regulatory audit package contains comprehensive temperature monitoring records for the period from ${reportData.summary.reportPeriod.start} to ${reportData.summary.reportPeriod.end}. The monitoring system recorded ${reportData.summary.totalReadings} temperature measurements across ${reportData.summary.sensorsIncluded} sensors with an overall compliance rate of ${reportData.summary.complianceRate.toFixed(1)}%. All temperature deviations were promptly addressed with appropriate corrective actions as documented in this report.`;

      case 'compliance-statement':
        return `Pacific Cloud Seafoods certifies that the temperature monitoring system and procedures documented in this report comply with applicable FDA Food Code regulations, HACCP principles, and industry best practices. All Critical Control Points (CCPs) are monitored continuously with appropriate corrective actions taken when deviations occur.`;

      default:
        return 'Content not available for this section.';
    }
  }

  private generateTitle(config: ReportTemplateConfig, startDate: Date, endDate: Date): string {
    const period = startDate.toLocaleDateString() === endDate.toLocaleDateString() ? 
      startDate.toLocaleDateString() : 
      `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`;

    return `${config.name} - ${period}`;
  }

  private generateSubject(config: ReportTemplateConfig): string {
    return `${config.description} - Temperature Monitoring System`;
  }

  private generateKeywords(config: ReportTemplateConfig): string[] {
    const baseKeywords = ['temperature', 'monitoring', 'seafood', 'Pacific Cloud Seafoods'];
    
    switch (config.complianceLevel) {
      case 'haccp':
        return [...baseKeywords, 'HACCP', 'compliance', 'critical control point', 'food safety'];
      case 'regulatory':
        return [...baseKeywords, 'regulatory', 'audit', 'FDA', 'compliance', 'inspection'];
      case 'audit':
        return [...baseKeywords, 'audit', 'analysis', 'performance', 'comprehensive'];
      default:
        return [...baseKeywords, 'operational', 'daily', 'summary'];
    }
  }

  private calculateDataSize(reportData: TemperatureReportData): {
    totalReadings: number;
    totalAlerts: number;
    totalSensors: number;
  } {
    const totalReadings = reportData.sensorData.reduce((sum, sensor) => sum + sensor.readings.length, 0);
    return {
      totalReadings,
      totalAlerts: reportData.alerts.length,
      totalSensors: reportData.sensorData.length
    };
  }

  private countCharts(templateId: string): number {
    const sections = this.getTemplateSections(templateId);
    return sections.filter(section => section.type === 'chart').length;
  }

  // Data preparation methods (simplified versions)
  private prepareHourlyTrendData(reportData: TemperatureReportData): any[] {
    const hourlyData = new Map<number, { total: number; count: number }>();
    
    reportData.sensorData.forEach(sensor => {
      sensor.readings.forEach((reading: any) => {
        const hour = new Date(reading.reading_timestamp).getHours();
        if (!hourlyData.has(hour)) {
          hourlyData.set(hour, { total: 0, count: 0 });
        }
        const data = hourlyData.get(hour)!;
        data.total += reading.temperature;
        data.count++;
      });
    });

    return Array.from(hourlyData.entries()).map(([hour, data]) => ({
      x: hour,
      y: data.total / data.count,
      label: `${hour}:00`
    }));
  }

  private prepareComplianceChartData(reportData: TemperatureReportData): any[] {
    return reportData.haccpCompliance.map(area => ({
      x: area.storageArea.name,
      y: area.complianceRate,
      label: `${area.storageArea.name}: ${area.complianceRate.toFixed(1)}%`,
      color: area.complianceRate >= 95 ? '#10b981' : '#ef4444'
    }));
  }

  private prepareSensorReliabilityData(reportData: TemperatureReportData): any[] {
    return reportData.sensorData.map(sensor => ({
      x: sensor.sensor.name,
      y: Math.min(100, (sensor.statistics.readingsCount / (24 * 4)) * 100), // Assuming 15-min intervals
      label: sensor.sensor.name,
      color: sensor.statistics.alertsCount === 0 ? '#10b981' : '#ef4444'
    }));
  }

  private prepareAlertTimelineData(reportData: TemperatureReportData): any[] {
    const hourlyAlerts = new Map<string, number>();
    
    reportData.alerts.forEach(alert => {
      const hourKey = new Date(alert.created_at).toISOString().slice(0, 13);
      hourlyAlerts.set(hourKey, (hourlyAlerts.get(hourKey) || 0) + 1);
    });

    return Array.from(hourlyAlerts.entries()).map(([hourKey, count]) => ({
      x: `${hourKey  }:00:00Z`,
      y: count,
      label: new Date(`${hourKey  }:00:00Z`).toLocaleString()
    }));
  }
}

// Export singleton instance
export const reportTemplateRegistry = ReportTemplateRegistry.getInstance();

export type { ReportTemplateConfig, TemplateSection, CustomizableTemplate };