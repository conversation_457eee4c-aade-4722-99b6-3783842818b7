// Database Query Optimization for Production Performance
import { supabase } from '../supabase';

// Query optimization patterns for seafood inventory management
export class QueryOptimizer {
  // Cached query results with TTL
  private static queryCache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  
  // Cache TTL constants (in milliseconds)
  private static readonly CACHE_TTL = {
    PRODUCTS: 5 * 60 * 1000,        // 5 minutes - products change frequently
    VENDORS: 15 * 60 * 1000,        // 15 minutes - vendors change less frequently
    CATEGORIES: 60 * 60 * 1000,     // 1 hour - categories rarely change
    INVENTORY: 2 * 60 * 1000,       // 2 minutes - inventory changes frequently
    HACCP_EVENTS: 10 * 60 * 1000,   // 10 minutes - HACCP events are time-sensitive
    USERS: 30 * 60 * 1000           // 30 minutes - user data changes infrequently
  };

  // Get cached data or fetch if expired
  private static getCachedOrFetch<T>(
    cacheKey: string,
    fetchFn: () => Promise<T>,
    ttl: number
  ): Promise<T> {
    const cached = this.queryCache.get(cacheKey);
    const now = Date.now();

    if (cached && (now - cached.timestamp) < cached.ttl) {
      return Promise.resolve(cached.data);
    }

    return fetchFn().then(data => {
      this.queryCache.set(cacheKey, {
        data,
        timestamp: now,
        ttl
      });
      return data;
    });
  }

  // Optimized inventory queries with selective columns
  static async getInventoryDashboard(userId: string) {
    const cacheKey = `inventory_dashboard_${userId}`;
    
    return this.getCachedOrFetch(
      cacheKey,
      async () => {
        const { data, error } = await supabase
          .from('inventory_events')
          .select(`
            id,
            event_type,
            quantity,
            unit,
            created_at,
            product:products (
              id,
              name,
              category,
              price_per_unit
            )
          `)
          .eq('user_id', userId)
          .order('created_at', { ascending: false })
          .limit(100); // Limit recent events for dashboard

        if (error) throw error;
        return data;
      },
      this.CACHE_TTL.INVENTORY
    );
  }

  // Optimized products query with category filtering
  static async getProductsByCategory(userId: string, category?: string) {
    const cacheKey = `products_${userId}_${category || 'all'}`;
    
    return this.getCachedOrFetch(
      cacheKey,
      async () => {
        let query = supabase
          .from('products')
          .select(`
            id,
            name,
            category,
            price_per_unit,
            unit,
            minimum_stock,
            current_stock,
            status,
            created_at
          `)
          .eq('user_id', userId);

        if (category) {
          query = query.eq('category', category);
        }

        const { data, error } = await query
          .order('name', { ascending: true })
          .limit(500); // Reasonable limit for UI performance

        if (error) throw error;
        return data;
      },
      this.CACHE_TTL.PRODUCTS
    );
  }

  // Optimized HACCP events query with date filtering
  static async getHACCPEventsByDateRange(
    userId: string, 
    startDate: string, 
    endDate: string
  ) {
    const cacheKey = `haccp_events_${userId}_${startDate}_${endDate}`;
    
    return this.getCachedOrFetch(
      cacheKey,
      async () => {
        const { data, error } = await supabase
          .from('calendar_events')
          .select(`
            id,
            title,
            start_date,
            end_date,
            event_type,
            temperature,
            notes,
            compliance_status,
            product:products (
              id,
              name,
              category
            )
          `)
          .eq('user_id', userId)
          .gte('start_date', startDate)
          .lte('end_date', endDate)
          .order('start_date', { ascending: true });

        if (error) throw error;
        return data;
      },
      this.CACHE_TTL.HACCP_EVENTS
    );
  }

  // Optimized vendor query with active status filter
  static async getActiveVendors(userId: string) {
    const cacheKey = `active_vendors_${userId}`;
    
    return this.getCachedOrFetch(
      cacheKey,
      async () => {
        const { data, error } = await supabase
          .from('vendors')
          .select(`
            id,
            name,
            contact_name,
            email,
            phone,
            status,
            payment_terms,
            credit_limit
          `)
          .eq('user_id', userId)
          .eq('status', 'active')
          .order('name', { ascending: true });

        if (error) throw error;
        return data;
      },
      this.CACHE_TTL.VENDORS
    );
  }

  // Optimized aggregation query for analytics
  static async getInventoryAnalytics(userId: string, days: number = 30) {
    const cacheKey = `inventory_analytics_${userId}_${days}`;
    
    return this.getCachedOrFetch(
      cacheKey,
      async () => {
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        
        // Use database functions for better performance
        const { data, error } = await supabase
          .rpc('get_inventory_analytics', {
            p_user_id: userId,
            p_start_date: startDate.toISOString(),
            p_end_date: new Date().toISOString()
          });

        if (error) throw error;
        return data;
      },
      this.CACHE_TTL.INVENTORY
    );
  }

  // Bulk operations with batch processing
  static async bulkInsertInventoryEvents(events: any[], batchSize: number = 100) {
    const batches = [];
    for (let i = 0; i < events.length; i += batchSize) {
      batches.push(events.slice(i, i + batchSize));
    }

    const results = await Promise.allSettled(
      batches.map(batch =>
        supabase
          .from('inventory_events')
          .insert(batch)
          .select()
      )
    );

    // Clear related cache entries
    this.clearCacheByPattern('inventory_');
    
    return results;
  }

  // Cache management
  static clearCache() {
    this.queryCache.clear();
  }

  static clearCacheByPattern(pattern: string) {
    for (const key of this.queryCache.keys()) {
      if (key.includes(pattern)) {
        this.queryCache.delete(key);
      }
    }
  }

  static getCacheStats() {
    return {
      size: this.queryCache.size,
      keys: Array.from(this.queryCache.keys()),
      memoryUsage: JSON.stringify(Array.from(this.queryCache.entries())).length
    };
  }
}

// Real-time subscription management with optimization
export class RealtimeOptimizer {
  private static activeChannels = new Map<string, any>();

  static subscribeToInventoryChanges(
    userId: string, 
    callback: (payload: any) => void
  ) {
    const channelKey = `inventory_${userId}`;
    
    // Avoid duplicate subscriptions
    if (this.activeChannels.has(channelKey)) {
      this.activeChannels.get(channelKey).unsubscribe();
    }

    const channel = supabase
      .channel(`inventory_changes_${userId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'inventory_events',
          filter: `user_id=eq.${userId}`
        },
        (payload) => {
          // Clear related cache when data changes
          QueryOptimizer.clearCacheByPattern('inventory_');
          callback(payload);
        }
      )
      .subscribe();

    this.activeChannels.set(channelKey, channel);
    return channel;
  }

  static unsubscribeAll() {
    for (const channel of this.activeChannels.values()) {
      channel.unsubscribe();
    }
    this.activeChannels.clear();
  }
}