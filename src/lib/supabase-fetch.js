
// Node-only script: do NOT import in browser bundles
if (typeof window !== 'undefined') {
  throw new Error('src/lib/supabase-fetch.js is Node-only. Do not import in the browser.');
}

// Direct fetch to Supabase to get table structure
require('dotenv').config();
const fetch = require('node-fetch');

const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_KEY = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

async function fetchTables() {
  try {
    // Query for tables
    const tablesUrl = `${SUPABASE_URL}/rest/v1/?apikey=${SUPABASE_KEY}`;
    
    const response = await fetch(tablesUrl, {
      headers: {
        'Authorization': `Bearer ${SUPABASE_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('Tables found:', Object.keys(data).join(', '));
    
    // For each table, get sample data
    for (const tableName of Object.keys(data)) {
      try {
        const sampleUrl = `${SUPABASE_URL}/rest/v1/${tableName}?select=*&limit=1&apikey=${SUPABASE_KEY}`;
        
        const sampleResponse = await fetch(sampleUrl, {
          headers: {
            'Authorization': `Bearer ${SUPABASE_KEY}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (!sampleResponse.ok) {
          console.error(`Error fetching sample for ${tableName}: ${sampleResponse.status}`);
          continue;
        }
        
        const sampleData = await sampleResponse.json();
        console.log(`\n${tableName} sample:`, sampleData);
        
        if (sampleData && sampleData.length > 0) {
          console.log(`${tableName} columns:`, Object.keys(sampleData[0]).join(', '));
        }
      } catch (tableError) {
        console.error(`Error fetching sample for ${tableName}:`, tableError);
      }
    }
  } catch (error) {
    console.error('Error fetching Supabase structure:', error);
  }
}

fetchTables();
