// Vendor Report Card API - Complete integration layer
// Handles all vendor performance tracking, ratings, and compliance

import { supabase } from './supabase';
import { 
  VendorInteraction, 
  VendorRating, 
  VendorMetrics, 
  VendorCompliance,
  VendorPerformanceAlert,
  VendorDashboardSummary
} from '../types/schema';

// ===== TYPE GUARDS AND UTILITIES =====

/**
 * Type guard to check if an object is a valid vendor interaction
 */
export function isValidVendorInteraction(obj: unknown): obj is VendorInteraction {
  return typeof obj === 'object' && obj !== null &&
         'vendor_id' in obj && 'interaction_type' in obj &&
         typeof (obj as VendorInteraction).vendor_id === 'string';
}

/**
 * Type guard for Supabase errors
 */
function isSupabaseError(error: unknown): error is { message: string; code?: string } {
  return typeof error === 'object' && error !== null && 'message' in error;
}

/**
 * Safe error message extraction
 */
function getErrorMessage(error: unknown): string {
  if (isSupabaseError(error)) {
    return error.message;
  }
  return error instanceof Error ? error.message : 'Unknown error occurred';
}

/**
 * Type-safe user ID retrieval
 */
async function getCurrentUserId(): Promise<string | null> {
  const { data: { user }, error } = await supabase.auth.getUser();
  if (error || !user) return null;
  return user.id;
}

// ===== VENDOR INTERACTIONS API =====

export const vendorInteractionsAPI = {
  // Create a new vendor interaction
  async create(interaction: Omit<VendorInteraction, 'id' | 'created_at' | 'updated_at'>): Promise<VendorInteraction> {
    const { data, error } = await supabase
      .from('vendor_interactions')
      .insert({
        ...interaction,
        created_by: await getCurrentUserId()
      })
      .select()
      .single();

    if (error) throw new Error(`Failed to create vendor interaction: ${getErrorMessage(error)}`);
    return data;
  },

  // Get interactions for a vendor
  async getByVendor(vendorId: string, options?: { 
    limit?: number; 
    offset?: number; 
    status?: VendorInteraction['delivery_status'];
    startDate?: string;
    endDate?: string;
  }): Promise<VendorInteraction[]> {
    let query = supabase
      .from('vendor_interactions')
      .select('*')
      .eq('vendor_id', vendorId)
      .order('created_at', { ascending: false });

    if (options?.status) {
      query = query.eq('status', options.status);
    }
    
    if (options?.startDate) {
      query = query.gte('created_at', options.startDate);
    }
    
    if (options?.endDate) {
      query = query.lte('created_at', options.endDate);
    }

    if (options?.limit) {
      query = query.limit(options.limit);
    }

    if (options?.offset) {
      query = query.range(options.offset, (options.offset + (options.limit || 10)) - 1);
    }

    const { data, error } = await query;
    if (error) throw new Error(`Failed to fetch vendor interactions: ${getErrorMessage(error)}`);
    return data || [];
  },

  // Update interaction status
  async updateStatus(
    interactionId: string, 
    status: VendorInteraction['delivery_status'], 
    additionalData?: Partial<VendorInteraction>
  ): Promise<void> {
    const { error } = await supabase
      .from('vendor_interactions')
      .update({
        status,
        ...additionalData,
        updated_at: new Date().toISOString()
      })
      .eq('id', interactionId);

    if (error) throw new Error(`Failed to update interaction status: ${getErrorMessage(error)}`);
  },

  // Get interaction by ID with related data
  async getById(interactionId: string): Promise<VendorInteraction | null> {
    const { data, error } = await supabase
      .from('vendor_interactions')
      .select(`
        *,
        vendors!inner(name, contact_person, email, phone)
      `)
      .eq('id', interactionId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw new Error(`Failed to fetch interaction: ${getErrorMessage(error)}`);
    }
    
    return data;
  }
};

// ===== VENDOR RATINGS API =====

export const vendorRatingsAPI = {
  // Create a vendor rating
  async create(rating: Omit<VendorRating, 'id' | 'rating_date' | 'created_at' | 'updated_at'>): Promise<VendorRating> {
    const { data, error } = await supabase
      .from('vendor_ratings')
      .insert({
        ...rating,
        created_by: await getCurrentUserId(),
        rating_date: new Date().toISOString()
      })
      .select()
      .single();

    if (error) throw new Error(`Failed to create vendor rating: ${getErrorMessage(error)}`);
    return data;
  },

  // Get ratings for a vendor
  async getByVendor(vendorId: string, options?: { limit?: number; startDate?: string; endDate?: string }): Promise<VendorRating[]> {
    let query = supabase
      .from('vendor_ratings')
      .select(`
        *,
        vendor_interactions!inner(interaction_type, expected_delivery_date, actual_delivery_date)
      `)
      .eq('vendor_id', vendorId)
      .order('rating_date', { ascending: false });

    if (options?.startDate) {
      query = query.gte('rating_date', options.startDate);
    }
    
    if (options?.endDate) {
      query = query.lte('rating_date', options.endDate);
    }

    if (options?.limit) {
      query = query.limit(options.limit);
    }

    const { data, error } = await query;
    if (error) throw new Error(`Failed to fetch vendor ratings: ${getErrorMessage(error)}`);
    return data || [];
  },

  // Get rating by interaction
  async getByInteraction(interactionId: string): Promise<VendorRating | null> {
    const { data, error } = await supabase
      .from('vendor_ratings')
      .select('*')
      .eq('vendor_interaction_id', interactionId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw new Error(`Failed to fetch rating: ${getErrorMessage(error)}`);
    }

    return data;
  },

  // Update rating
  async update(ratingId: string, updates: Partial<VendorRating>): Promise<void> {
    const { error } = await supabase
      .from('vendor_ratings')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', ratingId);

    if (error) throw new Error(`Failed to update rating: ${getErrorMessage(error)}`);
  }
};

// ===== VENDOR METRICS API =====

export const vendorMetricsAPI = {
  // Get current metrics for a vendor
  async getCurrent(vendorId: string, period: string = 'last_30_days'): Promise<VendorMetrics | null> {
    const { data, error } = await supabase
      .from('vendor_metrics')
      .select('*')
      .eq('vendor_id', vendorId)
      .eq('calculation_period', period)
      .order('calculated_at', { ascending: false })
      .limit(1)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw new Error(`Failed to fetch vendor metrics: ${error.message}`);
    }

    return data;
  },

  // Get metrics history for a vendor
  async getHistory(vendorId: string, period: string = 'last_30_days', limit: number = 12): Promise<VendorMetrics[]> {
    const { data, error } = await supabase
      .from('vendor_metrics')
      .select('*')
      .eq('vendor_id', vendorId)
      .eq('calculation_period', period)
      .order('calculated_at', { ascending: false })
      .limit(limit);

    if (error) throw new Error(`Failed to fetch metrics history: ${error.message}`);
    return data || [];
  },

  // Trigger metrics calculation for a vendor
  async calculate(vendorId?: string, period: string = 'last_30_days'): Promise<void> {
    const { error } = await supabase.rpc('calculate_vendor_metrics', {
      p_vendor_id: vendorId || null,
      p_calculation_period: period
    });

    if (error) throw new Error(`Failed to calculate metrics: ${error.message}`);
  },

  // Get top performing vendors
  async getTopPerformers(period: string = 'last_30_days', limit: number = 10): Promise<VendorMetrics[]> {
    const { data, error } = await supabase
      .from('vendor_metrics')
      .select(`
        *,
        vendors!inner(name, email, phone)
      `)
      .eq('calculation_period', period)
      .order('completion_rate', { ascending: false })
      .limit(limit);

    if (error) throw new Error(`Failed to fetch top performers: ${error.message}`);
    return data || [];
  }
};

// ===== VENDOR COMPLIANCE API =====

export const vendorComplianceAPI = {
  // Create compliance record
  async create(compliance: Omit<VendorCompliance, 'id' | 'assessed_date' | 'created_at' | 'updated_at'>): Promise<VendorCompliance> {
    const { data, error } = await supabase
      .from('vendor_compliance')
      .insert({
        ...compliance,
        created_by: await getCurrentUserId(),
        assessed_date: new Date().toISOString()
      })
      .select()
      .single();

    if (error) throw new Error(`Failed to create compliance record: ${error.message}`);
    return data;
  },

  // Get latest compliance status for vendor
  async getLatest(vendorId: string): Promise<VendorCompliance | null> {
    const { data, error } = await supabase
      .from('vendor_compliance')
      .select('*')
      .eq('vendor_id', vendorId)
      .order('assessed_date', { ascending: false })
      .limit(1)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw new Error(`Failed to fetch compliance status: ${error.message}`);
    }

    return data;
  },

  // Get compliance history for vendor
  async getHistory(vendorId: string, limit: number = 10): Promise<VendorCompliance[]> {
    const { data, error } = await supabase
      .from('vendor_compliance')
      .select('*')
      .eq('vendor_id', vendorId)
      .order('assessed_date', { ascending: false })
      .limit(limit);

    if (error) throw new Error(`Failed to fetch compliance history: ${error.message}`);
    return data || [];
  },

  // Get vendors with expiring HACCP certifications
  async getExpiringCertifications(daysAhead: number = 30): Promise<VendorCompliance[]> {
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + daysAhead);

    const { data, error } = await supabase
      .from('vendor_compliance')
      .select(`
        *,
        vendors!inner(name, email, contact_person)
      `)
      .lte('haccp_cert_expiry_date', expiryDate.toISOString().split('T')[0])
      .eq('haccp_certified', true)
      .order('haccp_cert_expiry_date');

    if (error) throw new Error(`Failed to fetch expiring certifications: ${error.message}`);
    return data || [];
  }
};

// ===== VENDOR PERFORMANCE ALERTS API =====

export const vendorAlertsAPI = {
  // Get active alerts for vendor
  async getActive(vendorId?: string): Promise<VendorPerformanceAlert[]> {
    let query = supabase
      .from('vendor_performance_alerts')
      .select(`
        *,
        vendors!inner(name, email, contact_person)
      `)
      .eq('status', 'active')
      .order('created_at', { ascending: false });

    if (vendorId) {
      query = query.eq('vendor_id', vendorId);
    }

    const { data, error } = await query;
    if (error) throw new Error(`Failed to fetch active alerts: ${error.message}`);
    return data || [];
  },

  // Acknowledge an alert
  async acknowledge(alertId: string): Promise<void> {
    const userId = (await supabase.auth.getUser()).data.user?.id;
    
    const { error } = await supabase
      .from('vendor_performance_alerts')
      .update({
        status: 'acknowledged',
        acknowledged_by: userId,
        acknowledged_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', alertId);

    if (error) throw new Error(`Failed to acknowledge alert: ${error.message}`);
  },

  // Resolve an alert
  async resolve(alertId: string, resolutionNotes?: string): Promise<void> {
    const userId = (await supabase.auth.getUser()).data.user?.id;
    
    const { error } = await supabase
      .from('vendor_performance_alerts')
      .update({
        status: 'resolved',
        resolved_by: userId,
        resolved_at: new Date().toISOString(),
        resolution_notes: resolutionNotes,
        updated_at: new Date().toISOString()
      })
      .eq('id', alertId);

    if (error) throw new Error(`Failed to resolve alert: ${error.message}`);
  },

  // Trigger alert checking for vendors
  async checkPerformanceAlerts(vendorId?: string): Promise<void> {
    const { error } = await supabase.rpc('check_vendor_performance_alerts', {
      p_vendor_id: vendorId || null
    });

    if (error) throw new Error(`Failed to check performance alerts: ${error.message}`);
  }
};

// ===== VENDOR DASHBOARD API =====

export const vendorDashboardAPI = {
  // Get comprehensive vendor dashboard summary
  async getSummary(): Promise<VendorDashboardSummary[]> {
    const { data, error } = await supabase.rpc('get_vendor_dashboard_summary');

    if (error) throw new Error(`Failed to fetch vendor dashboard: ${error.message}`);
    return data || [];
  },

  // Get detailed vendor performance data
  async getVendorDetails(vendorId: string): Promise<{
    vendor: {
      id: string;
      name: string;
      contact_person?: string;
      email?: string;
      phone?: string;
      address?: string;
      status?: 'active' | 'inactive';
      payment_terms?: string;
      credit_limit?: number;
    };
    currentMetrics: VendorMetrics | null;
    recentInteractions: VendorInteraction[];
    recentRatings: VendorRating[];
    complianceStatus: VendorCompliance | null;
    activeAlerts: VendorPerformanceAlert[];
  }> {
    // Fetch vendor basic info
    const { data: vendor, error: vendorError } = await supabase
      .from('vendors')
      .select('*')
      .eq('id', vendorId)
      .single();

    if (vendorError) throw new Error(`Failed to fetch vendor: ${vendorError.message}`);

    // Fetch all related data in parallel
    const [
      currentMetrics,
      recentInteractions,
      recentRatings,
      complianceStatus,
      activeAlerts
    ] = await Promise.all([
      vendorMetricsAPI.getCurrent(vendorId, 'last_30_days'),
      vendorInteractionsAPI.getByVendor(vendorId, { limit: 10 }),
      vendorRatingsAPI.getByVendor(vendorId, { limit: 10 }),
      vendorComplianceAPI.getLatest(vendorId),
      vendorAlertsAPI.getActive(vendorId)
    ]);

    return {
      vendor,
      currentMetrics,
      recentInteractions,
      recentRatings,
      complianceStatus,
      activeAlerts
    };
  },

  // Refresh dashboard materialized view
  async refreshSummary(): Promise<void> {
    const { error } = await supabase.rpc('refresh_vendor_dashboard_summary');
    if (error) throw new Error(`Failed to refresh dashboard: ${error.message}`);
  },

  // Get vendor comparison data
  async compareVendors(vendorIds: string[], period: string = 'last_30_days'): Promise<VendorMetrics[]> {
    const { data, error } = await supabase
      .from('vendor_metrics')
      .select(`
        *,
        vendors!inner(name)
      `)
      .in('vendor_id', vendorIds)
      .eq('calculation_period', period);

    if (error) throw new Error(`Failed to fetch vendor comparison: ${error.message}`);
    return data || [];
  }
};

// ===== INTEGRATION WITH INVENTORY EVENTS =====

export const vendorInventoryIntegration = {
  // Create vendor interaction from inventory event
  async createFromInventoryEvent(eventData: {
    vendorId: string;
    eventType: string;
    productId: string;
    quantity: number;
    unitPrice?: number;
    totalAmount?: number;
    condition?: string;
    temperature?: number;
    notes?: string;
    batchNumber?: string;
  }): Promise<VendorInteraction> {
    // Determine interaction type based on inventory event
    let interactionType: 'delivery' | 'order' | 'quality_check' | 'issue_resolution' | 'communication' = 'delivery';
    
    if (eventData.eventType === 'receiving') {
      interactionType = 'delivery';
    }

    const interaction = await vendorInteractionsAPI.create({
      vendor_id: eventData.vendorId,
      interaction_type: interactionType,
      expected_delivery_date: new Date().toISOString(),
      actual_delivery_date: new Date().toISOString(),
      order_total_amount: eventData.totalAmount || (eventData.unitPrice && eventData.quantity ? eventData.unitPrice * eventData.quantity : undefined),
      delivered_amount: eventData.totalAmount || (eventData.unitPrice && eventData.quantity ? eventData.unitPrice * eventData.quantity : undefined),
      status: 'completed',
      temperature_compliant: eventData.temperature ? (eventData.temperature >= -2 && eventData.temperature <= 4) : null,
      documentation_complete: true, // Assume complete if created via API
      product_condition: eventData.condition as VendorInteraction['product_condition'],
      notes: eventData.notes
    });

    // Trigger metrics calculation after new interaction
    setTimeout(() => {
      vendorMetricsAPI.calculate(eventData.vendorId, 'last_30_days');
      vendorAlertsAPI.checkPerformanceAlerts(eventData.vendorId);
    }, 1000);

    return interaction;
  }
};

// ===== MAIN VENDOR API EXPORT =====

export const vendorAPI = {
  interactions: vendorInteractionsAPI,
  ratings: vendorRatingsAPI,
  metrics: vendorMetricsAPI,
  compliance: vendorComplianceAPI,
  alerts: vendorAlertsAPI,
  dashboard: vendorDashboardAPI,
  integration: vendorInventoryIntegration
};

export default vendorAPI;