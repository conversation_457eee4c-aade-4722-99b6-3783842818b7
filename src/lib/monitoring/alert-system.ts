/**
 * Comprehensive Alert System for TempStick Integration
 * 
 * Handles temperature violations, system health monitoring,
 * escalation rules, and notification workflows for HACCP compliance.
 */

import { performanceMonitor } from './performance-monitor';
import { supabase } from '../supabase';

export interface Alert {
  id: string;
  type: 'temperature_violation' | 'system_health' | 'api_failure' | 'compliance_risk';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  sensorId?: string;
  timestamp: Date;
  acknowledged: boolean;
  resolved: boolean;
  assignedTo?: string;
  escalationLevel: number;
  metadata: Record<string, any>;
}

export interface EscalationRule {
  id: string;
  alertType: string;
  severity: string;
  timeToEscalate: number; // minutes
  escalationLevels: EscalationLevel[];
}

export interface EscalationLevel {
  level: number;
  notificationMethods: ('email' | 'sms' | 'webhook' | 'dashboard')[];
  recipients: string[];
  autoAssign?: string;
}

export interface NotificationChannel {
  type: 'email' | 'sms' | 'webhook' | 'dashboard';
  config: Record<string, any>;
  enabled: boolean;
}

class AlertSystem {
  private alerts: Map<string, Alert> = new Map();
  private escalationRules: Map<string, EscalationRule> = new Map();
  private notificationChannels: Map<string, NotificationChannel> = new Map();
  private escalationTimers: Map<string, NodeJS.Timeout> = new Map();
  private listeners: ((alert: Alert) => void)[] = [];

  constructor() {
    this.setupDefaultEscalationRules();
    this.setupNotificationChannels();
    this.startSystemHealthMonitoring();
    
    // Subscribe to performance monitor alerts
    performanceMonitor.onAlert(this.handlePerformanceAlert.bind(this));
  }

  /**
   * Create a new alert
   */
  async createAlert(alertData: Omit<Alert, 'id' | 'timestamp' | 'acknowledged' | 'resolved' | 'escalationLevel'>): Promise<Alert> {
    const alert: Alert = {
      ...alertData,
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      acknowledged: false,
      resolved: false,
      escalationLevel: 0
    };

    this.alerts.set(alert.id, alert);
    
    // Store in database
    await this.persistAlert(alert);
    
    // Start escalation process
    this.startEscalation(alert);
    
    // Notify listeners
    this.notifyListeners(alert);
    
    console.log(`🚨 Alert created: ${alert.title} (${alert.severity})`);
    
    return alert;
  }

  /**
   * Handle temperature violation alerts
   */
  async handleTemperatureViolation(
    sensorId: string,
    temperature: number,
    threshold: { min?: number; max?: number },
    location: string
  ): Promise<void> {
    const violation = temperature < (threshold.min || -Infinity) ? 'below' : 'above';
    const violatedThreshold = violation === 'below' ? threshold.min : threshold.max;
    
    const severity = this.calculateTemperatureSeverity(temperature, threshold);
    
    await this.createAlert({
      type: 'temperature_violation',
      severity,
      title: `Critical Temperature Violation - ${location}`,
      message: `Temperature ${violation} threshold: ${temperature}°F (threshold: ${violatedThreshold}°F)`,
      sensorId,
      metadata: {
        temperature,
        threshold,
        location,
        violation,
        haccpCriticalControlPoint: true,
        requiresImmediateAction: severity === 'critical'
      }
    });
  }

  /**
   * Handle system health alerts
   */
  async handleSystemHealth(
    component: string,
    status: 'degraded' | 'down' | 'error',
    details: string
  ): Promise<void> {
    const severity = status === 'down' ? 'critical' : status === 'error' ? 'high' : 'medium';
    
    await this.createAlert({
      type: 'system_health',
      severity,
      title: `System Health Issue - ${component}`,
      message: `${component} is ${status}: ${details}`,
      metadata: {
        component,
        status,
        details,
        impactsTemperatureMonitoring: component.includes('tempstick') || component.includes('sensor')
      }
    });
  }

  /**
   * Handle API failure alerts
   */
  async handleAPIFailure(
    endpoint: string,
    statusCode: number,
    errorMessage: string,
    consecutiveFailures: number
  ): Promise<void> {
    const severity = consecutiveFailures >= 3 ? 'critical' : consecutiveFailures >= 2 ? 'high' : 'medium';
    
    await this.createAlert({
      type: 'api_failure',
      severity,
      title: `TempStick API Failure`,
      message: `${endpoint} failed with ${statusCode}: ${errorMessage} (${consecutiveFailures} consecutive failures)`,
      metadata: {
        endpoint,
        statusCode,
        errorMessage,
        consecutiveFailures,
        impactsDataIngestion: true
      }
    });
  }

  /**
   * Handle compliance risk alerts
   */
  async handleComplianceRisk(
    riskType: string,
    description: string,
    affectedSensors: string[]
  ): Promise<void> {
    await this.createAlert({
      type: 'compliance_risk',
      severity: 'high',
      title: `HACCP Compliance Risk`,
      message: `${riskType}: ${description}`,
      metadata: {
        riskType,
        description,
        affectedSensors,
        requiresAuditTrail: true,
        haccpCompliance: true
      }
    });
  }

  /**
   * Acknowledge an alert
   */
  async acknowledgeAlert(alertId: string, acknowledgedBy: string): Promise<void> {
    const alert = this.alerts.get(alertId);
    if (!alert) return;

    alert.acknowledged = true;
    alert.assignedTo = acknowledgedBy;

    // Cancel escalation timer
    const timer = this.escalationTimers.get(alertId);
    if (timer) {
      clearTimeout(timer);
      this.escalationTimers.delete(alertId);
    }

    await this.persistAlert(alert);
    console.log(`✅ Alert acknowledged: ${alert.title} by ${acknowledgedBy}`);
  }

  /**
   * Resolve an alert
   */
  async resolveAlert(alertId: string, resolvedBy: string, resolution: string): Promise<void> {
    const alert = this.alerts.get(alertId);
    if (!alert) return;

    alert.resolved = true;
    alert.assignedTo = resolvedBy;
    alert.metadata.resolution = resolution;

    // Cancel escalation timer
    const timer = this.escalationTimers.get(alertId);
    if (timer) {
      clearTimeout(timer);
      this.escalationTimers.delete(alertId);
    }

    await this.persistAlert(alert);
    console.log(`✅ Alert resolved: ${alert.title} by ${resolvedBy}`);
  }

  /**
   * Get active alerts
   */
  getActiveAlerts(filters?: {
    type?: string;
    severity?: string;
    sensorId?: string;
  }): Alert[] {
    let alerts = Array.from(this.alerts.values()).filter(alert => !alert.resolved);

    if (filters?.type) {
      alerts = alerts.filter(alert => alert.type === filters.type);
    }
    if (filters?.severity) {
      alerts = alerts.filter(alert => alert.severity === filters.severity);
    }
    if (filters?.sensorId) {
      alerts = alerts.filter(alert => alert.sensorId === filters.sensorId);
    }

    return alerts.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  /**
   * Start escalation process for an alert
   */
  private startEscalation(alert: Alert): void {
    const ruleKey = `${alert.type}_${alert.severity}`;
    const rule = this.escalationRules.get(ruleKey) || this.escalationRules.get(alert.type);
    
    if (!rule) return;

    // Send initial notifications
    this.sendNotifications(alert, rule.escalationLevels[0]);

    // Set escalation timer
    if (rule.timeToEscalate > 0 && rule.escalationLevels.length > 1) {
      const timer = setTimeout(() => {
        this.escalateAlert(alert.id);
      }, rule.timeToEscalate * 60000); // Convert minutes to milliseconds

      this.escalationTimers.set(alert.id, timer);
    }
  }

  /**
   * Escalate an alert to the next level
   */
  private escalateAlert(alertId: string): void {
    const alert = this.alerts.get(alertId);
    if (!alert || alert.acknowledged || alert.resolved) return;

    const ruleKey = `${alert.type}_${alert.severity}`;
    const rule = this.escalationRules.get(ruleKey) || this.escalationRules.get(alert.type);
    
    if (!rule) return;

    alert.escalationLevel++;
    
    if (alert.escalationLevel < rule.escalationLevels.length) {
      const level = rule.escalationLevels[alert.escalationLevel];
      
      // Send escalated notifications
      this.sendNotifications(alert, level);
      
      // Auto-assign if configured
      if (level.autoAssign && !alert.assignedTo) {
        alert.assignedTo = level.autoAssign;
      }

      // Set next escalation timer
      if (alert.escalationLevel < rule.escalationLevels.length - 1) {
        const timer = setTimeout(() => {
          this.escalateAlert(alertId);
        }, rule.timeToEscalate * 60000);

        this.escalationTimers.set(alertId, timer);
      }

      console.log(`⬆️ Alert escalated to level ${alert.escalationLevel}: ${alert.title}`);
    }
  }

  /**
   * Send notifications for an alert
   */
  private async sendNotifications(alert: Alert, level: EscalationLevel): Promise<void> {
    for (const method of level.notificationMethods) {
      const channel = this.notificationChannels.get(method);
      if (!channel?.enabled) continue;

      try {
        await this.sendNotification(method, alert, level.recipients, channel.config);
      } catch (error) {
        console.error(`Failed to send ${method} notification:`, error);
      }
    }
  }

  /**
   * Send individual notification
   */
  private async sendNotification(
    method: string,
    alert: Alert,
    recipients: string[],
    config: Record<string, any>
  ): Promise<void> {
    switch (method) {
      case 'email':
        await this.sendEmailNotification(alert, recipients, config);
        break;
      case 'sms':
        await this.sendSMSNotification(alert, recipients, config);
        break;
      case 'webhook':
        await this.sendWebhookNotification(alert, config);
        break;
      case 'dashboard':
        // Dashboard notifications are handled by listeners
        break;
    }
  }

  /**
   * Send email notification
   */
  private async sendEmailNotification(alert: Alert, recipients: string[], config: any): Promise<void> {
    // Implement email sending logic
    console.log(`📧 Sending email notification to ${recipients.join(', ')} for alert: ${alert.title}`);
    
    // Integration with email service would go here
    const emailData = {
      to: recipients,
      subject: `${alert.severity.toUpperCase()} Alert: ${alert.title}`,
      body: `
        Alert Details:
        - Type: ${alert.type}
        - Severity: ${alert.severity}
        - Sensor: ${alert.sensorId || 'N/A'}
        - Time: ${alert.timestamp.toISOString()}
        - Message: ${alert.message}
        
        Please acknowledge this alert in the dashboard.
      `
    };
  }

  /**
   * Send SMS notification
   */
  private async sendSMSNotification(alert: Alert, recipients: string[], config: any): Promise<void> {
    console.log(`📱 Sending SMS notification to ${recipients.join(', ')} for alert: ${alert.title}`);
    // SMS service integration would go here
  }

  /**
   * Send webhook notification
   */
  private async sendWebhookNotification(alert: Alert, config: any): Promise<void> {
    console.log(`🔗 Sending webhook notification for alert: ${alert.title}`);
    
    try {
      await fetch(config.url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...config.headers
        },
        body: JSON.stringify({
          alert_id: alert.id,
          type: alert.type,
          severity: alert.severity,
          title: alert.title,
          message: alert.message,
          timestamp: alert.timestamp.toISOString(),
          metadata: alert.metadata
        })
      });
    } catch (error) {
      console.error('Webhook notification failed:', error);
    }
  }

  /**
   * Handle performance monitor alerts
   */
  private handlePerformanceAlert(perfAlert: any): void {
    const severity = perfAlert.level === 'critical' ? 'critical' : 'medium';
    
    this.createAlert({
      type: 'system_health',
      severity,
      title: `Performance Issue: ${perfAlert.metric}`,
      message: `${perfAlert.metric} value ${perfAlert.value} exceeds threshold ${perfAlert.threshold}`,
      metadata: {
        metric: perfAlert.metric,
        value: perfAlert.value,
        threshold: perfAlert.threshold,
        performanceAlert: true
      }
    });
  }

  /**
   * Calculate temperature alert severity
   */
  private calculateTemperatureSeverity(
    temperature: number, 
    threshold: { min?: number; max?: number }
  ): Alert['severity'] {
    const minDiff = threshold.min ? Math.abs(temperature - threshold.min) : Infinity;
    const maxDiff = threshold.max ? Math.abs(temperature - threshold.max) : Infinity;
    const diff = Math.min(minDiff, maxDiff);

    if (diff >= 10) return 'critical';  // 10+ degrees off
    if (diff >= 5) return 'high';       // 5-10 degrees off
    if (diff >= 2) return 'medium';     // 2-5 degrees off
    return 'low';                       // Less than 2 degrees off
  }

  /**
   * Setup default escalation rules
   */
  private setupDefaultEscalationRules(): void {
    // Temperature violation rules
    this.escalationRules.set('temperature_violation', {
      id: 'temp_violation_default',
      alertType: 'temperature_violation',
      severity: 'any',
      timeToEscalate: 5, // 5 minutes
      escalationLevels: [
        {
          level: 0,
          notificationMethods: ['dashboard', 'email'],
          recipients: ['<EMAIL>']
        },
        {
          level: 1,
          notificationMethods: ['dashboard', 'email', 'sms'],
          recipients: ['<EMAIL>', '<EMAIL>'],
          autoAssign: '<EMAIL>'
        },
        {
          level: 2,
          notificationMethods: ['dashboard', 'email', 'sms', 'webhook'],
          recipients: ['<EMAIL>', '<EMAIL>', '<EMAIL>']
        }
      ]
    });

    // Critical temperature violations (immediate escalation)
    this.escalationRules.set('temperature_violation_critical', {
      id: 'temp_violation_critical',
      alertType: 'temperature_violation',
      severity: 'critical',
      timeToEscalate: 1, // 1 minute
      escalationLevels: [
        {
          level: 0,
          notificationMethods: ['dashboard', 'email', 'sms'],
          recipients: ['<EMAIL>', '<EMAIL>']
        },
        {
          level: 1,
          notificationMethods: ['dashboard', 'email', 'sms', 'webhook'],
          recipients: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
          autoAssign: '<EMAIL>'
        }
      ]
    });

    // System health rules
    this.escalationRules.set('system_health', {
      id: 'system_health_default',
      alertType: 'system_health',
      severity: 'any',
      timeToEscalate: 10, // 10 minutes
      escalationLevels: [
        {
          level: 0,
          notificationMethods: ['dashboard', 'email'],
          recipients: ['<EMAIL>']
        },
        {
          level: 1,
          notificationMethods: ['dashboard', 'email', 'sms'],
          recipients: ['<EMAIL>', '<EMAIL>']
        }
      ]
    });
  }

  /**
   * Setup notification channels
   */
  private setupNotificationChannels(): void {
    this.notificationChannels.set('email', {
      type: 'email',
      enabled: true,
      config: {
        smtp_host: process.env.SMTP_HOST,
        smtp_port: process.env.SMTP_PORT,
        username: process.env.SMTP_USERNAME,
        password: process.env.SMTP_PASSWORD,
        from: '<EMAIL>'
      }
    });

    this.notificationChannels.set('sms', {
      type: 'sms',
      enabled: true,
      config: {
        provider: 'twilio',
        account_sid: process.env.TWILIO_ACCOUNT_SID,
        auth_token: process.env.TWILIO_AUTH_TOKEN,
        from_number: process.env.TWILIO_PHONE_NUMBER
      }
    });

    this.notificationChannels.set('webhook', {
      type: 'webhook',
      enabled: true,
      config: {
        url: process.env.ALERT_WEBHOOK_URL,
        headers: {
          'Authorization': `Bearer ${process.env.ALERT_WEBHOOK_TOKEN}`
        }
      }
    });

    this.notificationChannels.set('dashboard', {
      type: 'dashboard',
      enabled: true,
      config: {}
    });
  }

  /**
   * Start system health monitoring
   */
  private startSystemHealthMonitoring(): void {
    // Monitor database connectivity
    setInterval(async () => {
      try {
        const { data, error } = await supabase.from('sensor_readings').select('count').limit(1);
        if (error) {
          this.handleSystemHealth('database', 'error', error.message);
        }
      } catch (error) {
        this.handleSystemHealth('database', 'down', 'Database connection failed');
      }
    }, 60000); // Every minute

    // Monitor TempStick API connectivity
    setInterval(async () => {
      try {
        // This would be a health check endpoint
        const response = await fetch(`${process.env.VITE_TEMPSTICK_API_URL}/health`);
        if (!response.ok) {
          this.handleSystemHealth('tempstick_api', 'degraded', `API returned ${response.status}`);
        }
      } catch (error) {
        this.handleSystemHealth('tempstick_api', 'down', 'API connection failed');
      }
    }, 300000); // Every 5 minutes
  }

  /**
   * Persist alert to database
   */
  private async persistAlert(alert: Alert): Promise<void> {
    try {
      await supabase.from('alerts').upsert({
        id: alert.id,
        type: alert.type,
        severity: alert.severity,
        title: alert.title,
        message: alert.message,
        sensor_id: alert.sensorId,
        timestamp: alert.timestamp.toISOString(),
        acknowledged: alert.acknowledged,
        resolved: alert.resolved,
        assigned_to: alert.assignedTo,
        escalation_level: alert.escalationLevel,
        metadata: alert.metadata
      });
    } catch (error) {
      console.error('Failed to persist alert:', error);
    }
  }

  /**
   * Add alert listener
   */
  onAlert(listener: (alert: Alert) => void): void {
    this.listeners.push(listener);
  }

  /**
   * Notify listeners
   */
  private notifyListeners(alert: Alert): void {
    this.listeners.forEach(listener => {
      try {
        listener(alert);
      } catch (error) {
        console.error('Error in alert listener:', error);
      }
    });
  }
}

// Global alert system instance
export const alertSystem = new AlertSystem();

export default alertSystem;