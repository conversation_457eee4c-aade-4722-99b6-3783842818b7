/**
 * Performance Monitoring and Metrics Tracking for Import Operations
 * Comprehensive monitoring system for seafood import operations
 * Tracks performance, memory usage, success rates, and user experience metrics
 */

export interface PerformanceMetric {
  id: string;
  name: string;
  category: MetricCategory;
  value: number;
  unit: string;
  timestamp: number;
  tags: Record<string, string>;
  context: {
    operation: string;
    fileSize?: number;
    recordCount?: number;
    userId?: string;
    sessionId: string;
  };
}

export type MetricCategory = 'performance' | 'memory' | 'network' | 'user_experience' | 'business' | 'error' | 'system';

export interface ImportSession {
  id: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  
  // File information
  file: {
    name: string;
    size: number;
    format: string;
    recordCount: number;
  };
  
  // Performance metrics
  performance: {
    processingTime: number;
    validationTime: number;
    uploadTime: number;
    totalTime: number;
    throughputRecordsPerSecond: number;
    throughputMBPerSecond: number;
  };
  
  // Memory usage
  memory: {
    peakUsage: number;
    averageUsage: number;
    gcEvents: number;
    memoryLeaks: boolean;
  };
  
  // Quality metrics
  quality: {
    successfulRecords: number;
    failedRecords: number;
    warningCount: number;
    errorRate: number;
    dataQualityScore: number;
  };
  
  // User experience
  userExperience: {
    firstContentfulPaint: number;
    interactionDelay: number;
    uiResponsiveness: number;
    userSatisfactionScore: number;
  };
  
  // Business metrics
  business: {
    importValue: number; // Total value of imported data
    productCategories: string[];
    supplierCount: number;
    complianceScore: number;
  };
}

export interface AlertRule {
  id: string;
  name: string;
  metric: string;
  condition: 'greater_than' | 'less_than' | 'equals' | 'not_equals';
  threshold: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  enabled: boolean;
  actions: AlertAction[];
}

export interface AlertAction {
  type: 'email' | 'log' | 'toast' | 'webhook';
  config: Record<string, unknown>;
}

export interface PerformanceReport {
  period: {
    start: string;
    end: string;
  };
  summary: {
    totalImports: number;
    successfulImports: number;
    failedImports: number;
    totalRecordsProcessed: number;
    averageProcessingTime: number;
    averageThroughput: number;
  };
  trends: {
    performanceTrend: 'improving' | 'stable' | 'declining';
    errorRateTrend: 'improving' | 'stable' | 'declining';
    throughputTrend: 'improving' | 'stable' | 'declining';
  };
  recommendations: string[];
  topIssues: Array<{
    issue: string;
    frequency: number;
    impact: 'low' | 'medium' | 'high';
    suggestion: string;
  }>;
}

export class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private sessions: Map<string, ImportSession> = new Map();
  private alertRules: Map<string, AlertRule> = new Map();
  private observers: PerformanceObserver[] = [];
  private isMonitoring = false;
  private currentSession?: ImportSession;
  
  // Real-time monitoring
  private metricsBuffer: PerformanceMetric[] = [];
  private bufferFlushInterval = 5000; // 5 seconds
  private maxBufferSize = 100;
  
  // Performance budgets
  private performanceBudgets = {
    maxProcessingTime: 30000, // 30 seconds for 10k records
    maxMemoryUsage: 512 * 1024 * 1024, // 512MB
    minThroughput: 1000, // records per second
    maxErrorRate: 0.05, // 5%
    minUserSatisfaction: 7.0 // out of 10
  };

  constructor() {
    this.initializeDefaultAlerts();
    this.setupPerformanceObservers();
  }

  /**
   * Start monitoring an import session
   */
  startSession(fileInfo: {
    name: string;
    size: number;
    format: string;
    recordCount: number;
  }): string {
    const sessionId = this.generateSessionId();
    
    this.currentSession = {
      id: sessionId,
      startTime: Date.now(),
      status: 'running',
      file: fileInfo,
      performance: {
        processingTime: 0,
        validationTime: 0,
        uploadTime: 0,
        totalTime: 0,
        throughputRecordsPerSecond: 0,
        throughputMBPerSecond: 0
      },
      memory: {
        peakUsage: 0,
        averageUsage: 0,
        gcEvents: 0,
        memoryLeaks: false
      },
      quality: {
        successfulRecords: 0,
        failedRecords: 0,
        warningCount: 0,
        errorRate: 0,
        dataQualityScore: 0
      },
      userExperience: {
        firstContentfulPaint: 0,
        interactionDelay: 0,
        uiResponsiveness: 0,
        userSatisfactionScore: 0
      },
      business: {
        importValue: 0,
        productCategories: [],
        supplierCount: 0,
        complianceScore: 0
      }
    };

    this.sessions.set(sessionId, this.currentSession);
    this.startMonitoring();
    
    // Track session start
    this.trackMetric({
      name: 'import_session_started',
      category: 'business',
      value: 1,
      unit: 'count',
      tags: {
        fileFormat: fileInfo.format,
        fileSize: this.formatFileSize(fileInfo.size),
        recordCount: fileInfo.recordCount.toString()
      }
    });

    return sessionId;
  }

  /**
   * End monitoring session
   */
  endSession(sessionId: string, status: ImportSession['status'], results?: {
    successfulRecords: number;
    failedRecords: number;
    warnings: number;
    importValue?: number;
  }): void {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    const endTime = Date.now();
    const duration = endTime - session.startTime;

    // Update session
    session.endTime = endTime;
    session.duration = duration;
    session.status = status;
    session.performance.totalTime = duration;

    if (results) {
      session.quality.successfulRecords = results.successfulRecords;
      session.quality.failedRecords = results.failedRecords;
      session.quality.warningCount = results.warnings;
      session.quality.errorRate = results.failedRecords / (results.successfulRecords + results.failedRecords);
      session.business.importValue = results.importValue || 0;
      
      // Calculate throughput
      session.performance.throughputRecordsPerSecond = 
        (results.successfulRecords + results.failedRecords) / (duration / 1000);
      session.performance.throughputMBPerSecond = 
        session.file.size / 1024 / 1024 / (duration / 1000);
    }

    // Calculate scores
    session.quality.dataQualityScore = this.calculateDataQualityScore(session);
    session.userExperience.userSatisfactionScore = this.calculateUserSatisfactionScore(session);

    // Check against performance budgets
    this.checkPerformanceBudgets(session);

    // Track session completion
    this.trackMetric({
      name: 'import_session_completed',
      category: 'business',
      value: duration,
      unit: 'ms',
      tags: {
        status,
        recordCount: (session.quality.successfulRecords + session.quality.failedRecords).toString(),
        errorRate: session.quality.errorRate.toString(),
        throughput: session.performance.throughputRecordsPerSecond.toString()
      }
    });

    if (this.currentSession?.id === sessionId) {
      this.stopMonitoring();
      this.currentSession = undefined;
    }
  }

  /**
   * Track a performance metric
   */
  trackMetric(metric: Omit<PerformanceMetric, 'id' | 'timestamp' | 'context'>): void {
    const fullMetric: PerformanceMetric = {
      id: this.generateMetricId(),
      timestamp: Date.now(),
      context: {
        operation: this.currentSession ? 'import' : 'system',
        fileSize: this.currentSession?.file.size,
        recordCount: this.currentSession?.file.recordCount,
        sessionId: this.currentSession?.id || 'no_session'
      },
      ...metric
    };

    this.metricsBuffer.push(fullMetric);
    
    // Check alerts
    this.checkAlerts(fullMetric);
    
    // Flush buffer if needed
    if (this.metricsBuffer.length >= this.maxBufferSize) {
      this.flushMetricsBuffer();
    }
  }

  /**
   * Update session progress
   */
  updateProgress(sessionId: string, progress: {
    processedRecords: number;
    currentPhase: 'parsing' | 'validation' | 'mapping' | 'uploading';
    memoryUsage?: number;
    errors?: number;
    warnings?: number;
  }): void {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    // Update quality metrics
    if (progress.errors !== undefined) {
      session.quality.failedRecords = progress.errors;
    }
    if (progress.warnings !== undefined) {
      session.quality.warningCount = progress.warnings;
    }

    // Update memory usage
    if (progress.memoryUsage !== undefined) {
      session.memory.peakUsage = Math.max(session.memory.peakUsage, progress.memoryUsage);
      session.memory.averageUsage = (session.memory.averageUsage + progress.memoryUsage) / 2;
    }

    // Track progress metric
    this.trackMetric({
      name: 'import_progress',
      category: 'performance',
      value: progress.processedRecords / session.file.recordCount * 100,
      unit: 'percent',
      tags: {
        phase: progress.currentPhase,
        sessionId
      }
    });
  }

  /**
   * Track user interaction
   */
  trackUserInteraction(interaction: {
    type: 'click' | 'scroll' | 'input' | 'navigation';
    element: string;
    responseTime: number;
  }): void {
    this.trackMetric({
      name: 'user_interaction',
      category: 'user_experience',
      value: interaction.responseTime,
      unit: 'ms',
      tags: {
        interactionType: interaction.type,
        element: interaction.element
      }
    });

    // Update current session UX metrics
    if (this.currentSession) {
      this.currentSession.userExperience.interactionDelay = 
        (this.currentSession.userExperience.interactionDelay + interaction.responseTime) / 2;
    }
  }

  /**
   * Get real-time metrics
   */
  getRealTimeMetrics(): {
    currentMemoryUsage: number;
    activeConnections: number;
    processingSpeed: number;
    errorRate: number;
    systemLoad: number;
  } {
    const currentSession = this.currentSession;
    
    return {
      currentMemoryUsage: currentSession?.memory.peakUsage || 0,
      activeConnections: 1, // Simplified
      processingSpeed: currentSession?.performance.throughputRecordsPerSecond || 0,
      errorRate: currentSession?.quality.errorRate || 0,
      systemLoad: this.getSystemLoad()
    };
  }

  /**
   * Generate performance report
   */
  generateReport(startDate: Date, endDate: Date): PerformanceReport {
    const sessionsInPeriod = Array.from(this.sessions.values()).filter(
      session => session.startTime >= startDate.getTime() && session.startTime <= endDate.getTime()
    );

    const totalImports = sessionsInPeriod.length;
    const successfulImports = sessionsInPeriod.filter(s => s.status === 'completed').length;
    const failedImports = sessionsInPeriod.filter(s => s.status === 'failed').length;
    
    const totalRecords = sessionsInPeriod.reduce(
      (sum, session) => sum + session.quality.successfulRecords + session.quality.failedRecords, 0
    );
    
    const averageProcessingTime = totalImports > 0 
      ? sessionsInPeriod.reduce((sum, session) => sum + (session.performance.totalTime || 0), 0) / totalImports
      : 0;
    
    const averageThroughput = totalImports > 0
      ? sessionsInPeriod.reduce((sum, session) => sum + session.performance.throughputRecordsPerSecond, 0) / totalImports
      : 0;

    return {
      period: {
        start: startDate.toISOString(),
        end: endDate.toISOString()
      },
      summary: {
        totalImports,
        successfulImports,
        failedImports,
        totalRecordsProcessed: totalRecords,
        averageProcessingTime,
        averageThroughput
      },
      trends: {
        performanceTrend: this.calculateTrend(sessionsInPeriod, 'performance'),
        errorRateTrend: this.calculateTrend(sessionsInPeriod, 'errorRate'),
        throughputTrend: this.calculateTrend(sessionsInPeriod, 'throughput')
      },
      recommendations: this.generateRecommendations(sessionsInPeriod),
      topIssues: this.identifyTopIssues(sessionsInPeriod)
    };
  }

  /**
   * Get session details
   */
  getSession(sessionId: string): ImportSession | undefined {
    return this.sessions.get(sessionId);
  }

  /**
   * Get all sessions
   */
  getAllSessions(): ImportSession[] {
    return Array.from(this.sessions.values());
  }

  // Private methods

  private initializeDefaultAlerts(): void {
    const defaultAlerts: AlertRule[] = [
      {
        id: 'high_processing_time',
        name: 'High Processing Time',
        metric: 'processing_time',
        condition: 'greater_than',
        threshold: this.performanceBudgets.maxProcessingTime,
        severity: 'medium',
        enabled: true,
        actions: [
          { type: 'toast', config: { message: 'Import is taking longer than expected' } },
          { type: 'log', config: { level: 'warning' } }
        ]
      },
      {
        id: 'memory_usage_high',
        name: 'High Memory Usage',
        metric: 'memory_usage',
        condition: 'greater_than',
        threshold: this.performanceBudgets.maxMemoryUsage,
        severity: 'high',
        enabled: true,
        actions: [
          { type: 'toast', config: { message: 'High memory usage detected' } },
          { type: 'log', config: { level: 'error' } }
        ]
      },
      {
        id: 'low_throughput',
        name: 'Low Throughput',
        metric: 'throughput',
        condition: 'less_than',
        threshold: this.performanceBudgets.minThroughput,
        severity: 'medium',
        enabled: true,
        actions: [
          { type: 'log', config: { level: 'warning' } }
        ]
      },
      {
        id: 'high_error_rate',
        name: 'High Error Rate',
        metric: 'error_rate',
        condition: 'greater_than',
        threshold: this.performanceBudgets.maxErrorRate,
        severity: 'high',
        enabled: true,
        actions: [
          { type: 'toast', config: { message: 'High error rate detected in import' } },
          { type: 'log', config: { level: 'error' } }
        ]
      }
    ];

    for (const alert of defaultAlerts) {
      this.alertRules.set(alert.id, alert);
    }
  }

  private setupPerformanceObservers(): void {
    // Memory usage observer
    if ('memory' in performance) {
      setInterval(() => {
        const memInfo = (performance as any).memory;
        if (memInfo) {
          this.trackMetric({
            name: 'memory_usage',
            category: 'memory',
            value: memInfo.usedJSHeapSize,
            unit: 'bytes',
            tags: { type: 'heap' }
          });
        }
      }, 5000);
    }

    // Navigation timing
    if ('getEntriesByType' in performance) {
      const paintEntries = performance.getEntriesByType('paint');
      for (const entry of paintEntries) {
        if (entry.name === 'first-contentful-paint') {
          this.trackMetric({
            name: 'first_contentful_paint',
            category: 'user_experience',
            value: entry.startTime,
            unit: 'ms',
            tags: { type: 'paint' }
          });
        }
      }
    }
  }

  private startMonitoring(): void {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    
    // Start buffer flush interval
    setInterval(() => {
      if (this.metricsBuffer.length > 0) {
        this.flushMetricsBuffer();
      }
    }, this.bufferFlushInterval);
  }

  private stopMonitoring(): void {
    this.isMonitoring = false;
    this.flushMetricsBuffer();
  }

  private flushMetricsBuffer(): void {
    this.metrics.push(...this.metricsBuffer);
    this.metricsBuffer = [];
    
    // Keep only recent metrics in memory (last 1000)
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }
  }

  private checkAlerts(metric: PerformanceMetric): void {
    for (const alert of this.alertRules.values()) {
      if (!alert.enabled) continue;
      
      if (this.metricMatchesAlert(metric, alert)) {
        this.triggerAlert(alert, metric);
      }
    }
  }

  private metricMatchesAlert(metric: PerformanceMetric, alert: AlertRule): boolean {
    if (metric.name !== alert.metric) return false;
    
    switch (alert.condition) {
      case 'greater_than':
        return metric.value > alert.threshold;
      case 'less_than':
        return metric.value < alert.threshold;
      case 'equals':
        return metric.value === alert.threshold;
      case 'not_equals':
        return metric.value !== alert.threshold;
      default:
        return false;
    }
  }

  private triggerAlert(alert: AlertRule, metric: PerformanceMetric): void {
    for (const action of alert.actions) {
      this.executeAlertAction(action, alert, metric);
    }
  }

  private executeAlertAction(action: AlertAction, alert: AlertRule, metric: PerformanceMetric): void {
    switch (action.type) {
      case 'toast':
        // Show toast notification
        console.warn(`Alert: ${alert.name} - ${action.config.message}`);
        break;
      case 'log':
        console.log(`[${action.config.level}] ${alert.name}: ${metric.name} = ${metric.value}${metric.unit}`);
        break;
      case 'webhook':
        // Send webhook (would be implemented)
        break;
      case 'email':
        // Send email (would be implemented)
        break;
    }
  }

  private checkPerformanceBudgets(session: ImportSession): void {
    const budgetViolations: string[] = [];
    
    if (session.performance.totalTime > this.performanceBudgets.maxProcessingTime) {
      budgetViolations.push('Processing time exceeded budget');
    }
    
    if (session.memory.peakUsage > this.performanceBudgets.maxMemoryUsage) {
      budgetViolations.push('Memory usage exceeded budget');
    }
    
    if (session.performance.throughputRecordsPerSecond < this.performanceBudgets.minThroughput) {
      budgetViolations.push('Throughput below minimum requirement');
    }
    
    if (session.quality.errorRate > this.performanceBudgets.maxErrorRate) {
      budgetViolations.push('Error rate exceeded acceptable threshold');
    }
    
    if (budgetViolations.length > 0) {
      this.trackMetric({
        name: 'performance_budget_violation',
        category: 'performance',
        value: budgetViolations.length,
        unit: 'count',
        tags: {
          sessionId: session.id,
          violations: budgetViolations.join(',')
        }
      });
    }
  }

  private calculateDataQualityScore(session: ImportSession): number {
    const total = session.quality.successfulRecords + session.quality.failedRecords;
    if (total === 0) return 0;
    
    const successRate = session.quality.successfulRecords / total;
    const warningPenalty = Math.min(session.quality.warningCount / total * 0.1, 0.3);
    
    return Math.max(0, (successRate - warningPenalty) * 10);
  }

  private calculateUserSatisfactionScore(session: ImportSession): number {
    // Simplified calculation based on performance metrics
    let score = 10;
    
    // Penalize for slow processing
    if (session.performance.totalTime > this.performanceBudgets.maxProcessingTime) {
      score -= 2;
    }
    
    // Penalize for high error rates
    if (session.quality.errorRate > this.performanceBudgets.maxErrorRate) {
      score -= 3;
    }
    
    // Penalize for poor responsiveness
    if (session.userExperience.interactionDelay > 200) {
      score -= 1;
    }
    
    return Math.max(0, score);
  }

  private calculateTrend(
    sessions: ImportSession[], 
    metric: 'performance' | 'errorRate' | 'throughput'
  ): 'improving' | 'stable' | 'declining' {
    if (sessions.length < 5) return 'stable';
    
    // Simple trend calculation based on recent vs older sessions
    const recent = sessions.slice(-5);
    const older = sessions.slice(0, 5);
    
    let recentAvg = 0;
    let olderAvg = 0;
    
    switch (metric) {
      case 'performance':
        recentAvg = recent.reduce((sum, s) => sum + s.performance.totalTime, 0) / recent.length;
        olderAvg = older.reduce((sum, s) => sum + s.performance.totalTime, 0) / older.length;
        return recentAvg < olderAvg ? 'improving' : recentAvg > olderAvg ? 'declining' : 'stable';
      
      case 'errorRate':
        recentAvg = recent.reduce((sum, s) => sum + s.quality.errorRate, 0) / recent.length;
        olderAvg = older.reduce((sum, s) => sum + s.quality.errorRate, 0) / older.length;
        return recentAvg < olderAvg ? 'improving' : recentAvg > olderAvg ? 'declining' : 'stable';
      
      case 'throughput':
        recentAvg = recent.reduce((sum, s) => sum + s.performance.throughputRecordsPerSecond, 0) / recent.length;
        olderAvg = older.reduce((sum, s) => sum + s.performance.throughputRecordsPerSecond, 0) / older.length;
        return recentAvg > olderAvg ? 'improving' : recentAvg < olderAvg ? 'declining' : 'stable';
      
      default:
        return 'stable';
    }
  }

  private generateRecommendations(sessions: ImportSession[]): string[] {
    const recommendations: string[] = [];
    
    const avgProcessingTime = sessions.reduce((sum, s) => sum + s.performance.totalTime, 0) / sessions.length;
    const avgErrorRate = sessions.reduce((sum, s) => sum + s.quality.errorRate, 0) / sessions.length;
    const avgMemoryUsage = sessions.reduce((sum, s) => sum + s.memory.peakUsage, 0) / sessions.length;
    
    if (avgProcessingTime > this.performanceBudgets.maxProcessingTime) {
      recommendations.push('Consider using streaming processing for large files');
      recommendations.push('Process files during off-peak hours for better performance');
    }
    
    if (avgErrorRate > this.performanceBudgets.maxErrorRate) {
      recommendations.push('Improve data quality validation before import');
      recommendations.push('Create standardized templates for common suppliers');
    }
    
    if (avgMemoryUsage > this.performanceBudgets.maxMemoryUsage * 0.8) {
      recommendations.push('Reduce import batch sizes to manage memory usage');
      recommendations.push('Consider using Web Workers for heavy processing');
    }
    
    return recommendations;
  }

  private identifyTopIssues(sessions: ImportSession[]): PerformanceReport['topIssues'] {
    // Simplified issue identification
    const issues = [
      {
        issue: 'High processing times',
        frequency: sessions.filter(s => s.performance.totalTime > this.performanceBudgets.maxProcessingTime).length,
        impact: 'medium' as const,
        suggestion: 'Use streaming processing for large files'
      },
      {
        issue: 'Memory usage spikes',
        frequency: sessions.filter(s => s.memory.peakUsage > this.performanceBudgets.maxMemoryUsage).length,
        impact: 'high' as const,
        suggestion: 'Implement chunked processing'
      },
      {
        issue: 'Data validation errors',
        frequency: sessions.filter(s => s.quality.errorRate > this.performanceBudgets.maxErrorRate).length,
        impact: 'medium' as const,
        suggestion: 'Improve pre-import data quality checks'
      }
    ];
    
    return issues.filter(issue => issue.frequency > 0).sort((a, b) => b.frequency - a.frequency);
  }

  private getSystemLoad(): number {
    // Simplified system load calculation
    // In production, this would use actual system metrics
    return Math.random() * 100;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateMetricId(): string {
    return `metric_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private formatFileSize(bytes: number): string {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + sizes[i];
  }
}