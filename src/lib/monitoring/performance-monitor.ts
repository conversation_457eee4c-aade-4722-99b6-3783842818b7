/**
 * Real-time Performance Monitoring for TempStick Integration
 * 
 * Tracks temperature data ingestion performance, database queries,
 * API response times, and memory usage for production optimization.
 */

interface PerformanceMetric {
  metric: string;
  value: number;
  timestamp: Date;
  tags?: Record<string, string>;
  threshold?: {
    warning: number;
    critical: number;
  };
}

interface PerformanceAlert {
  id: string;
  metric: string;
  level: 'warning' | 'critical';
  value: number;
  threshold: number;
  timestamp: Date;
  resolved: boolean;
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric[]> = new Map();
  private alerts: PerformanceAlert[] = [];
  private listeners: ((alert: PerformanceAlert) => void)[] = [];
  private metricsBuffer: PerformanceMetric[] = [];
  private flushInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.startMetricsCollection();
    this.setupPerformanceObserver();
  }

  /**
   * Track temperature data ingestion performance
   */
  trackDataIngestion(sensorId: string, recordCount: number, processingTime: number): void {
    const throughput = recordCount / (processingTime / 1000); // records per second
    
    this.recordMetric({
      metric: 'tempstick.data_ingestion.throughput',
      value: throughput,
      timestamp: new Date(),
      tags: {
        sensor_id: sensorId,
        record_count: recordCount.toString()
      },
      threshold: {
        warning: 50, // Below 50 records/sec
        critical: 20  // Below 20 records/sec
      }
    });

    this.recordMetric({
      metric: 'tempstick.data_ingestion.processing_time',
      value: processingTime,
      timestamp: new Date(),
      tags: { sensor_id: sensorId },
      threshold: {
        warning: 5000,  // 5 seconds
        critical: 10000 // 10 seconds
      }
    });
  }

  /**
   * Track database query performance
   */
  trackDatabaseQuery(
    queryType: string, 
    executionTime: number, 
    recordsAffected: number,
    tableName: string
  ): void {
    this.recordMetric({
      metric: 'database.query.execution_time',
      value: executionTime,
      timestamp: new Date(),
      tags: {
        query_type: queryType,
        table_name: tableName,
        records_affected: recordsAffected.toString()
      },
      threshold: {
        warning: 1000,  // 1 second
        critical: 3000  // 3 seconds
      }
    });

    // Track query rate
    this.recordMetric({
      metric: 'database.query.rate',
      value: 1,
      timestamp: new Date(),
      tags: {
        query_type: queryType,
        table_name: tableName
      }
    });
  }

  /**
   * Track TempStick API response times
   */
  trackAPICall(
    endpoint: string, 
    responseTime: number, 
    statusCode: number,
    dataSize?: number
  ): void {
    this.recordMetric({
      metric: 'tempstick.api.response_time',
      value: responseTime,
      timestamp: new Date(),
      tags: {
        endpoint,
        status_code: statusCode.toString(),
        data_size: dataSize?.toString() || '0'
      },
      threshold: {
        warning: 2000,  // 2 seconds
        critical: 5000  // 5 seconds
      }
    });

    // Track error rates
    if (statusCode >= 400) {
      this.recordMetric({
        metric: 'tempstick.api.error_rate',
        value: 1,
        timestamp: new Date(),
        tags: {
          endpoint,
          status_code: statusCode.toString()
        },
        threshold: {
          warning: 0.05,  // 5% error rate
          critical: 0.1   // 10% error rate
        }
      });
    }
  }

  /**
   * Track memory usage for sensor data processing
   */
  trackMemoryUsage(context: string): void {
    const memInfo = process.memoryUsage();
    
    this.recordMetric({
      metric: 'system.memory.heap_used',
      value: memInfo.heapUsed / 1024 / 1024, // MB
      timestamp: new Date(),
      tags: { context },
      threshold: {
        warning: 512,  // 512 MB
        critical: 1024 // 1 GB
      }
    });

    this.recordMetric({
      metric: 'system.memory.heap_total',
      value: memInfo.heapTotal / 1024 / 1024, // MB
      timestamp: new Date(),
      tags: { context }
    });

    this.recordMetric({
      metric: 'system.memory.external',
      value: memInfo.external / 1024 / 1024, // MB
      timestamp: new Date(),
      tags: { context }
    });
  }

  /**
   * Record a performance metric
   */
  private recordMetric(metric: PerformanceMetric): void {
    // Add to buffer for batch processing
    this.metricsBuffer.push(metric);

    // Store in memory for immediate access
    const key = metric.metric;
    if (!this.metrics.has(key)) {
      this.metrics.set(key, []);
    }
    
    const metricHistory = this.metrics.get(key)!;
    metricHistory.push(metric);

    // Keep only last 1000 entries per metric
    if (metricHistory.length > 1000) {
      metricHistory.shift();
    }

    // Check thresholds and generate alerts
    this.checkThresholds(metric);
  }

  /**
   * Check metric thresholds and generate alerts
   */
  private checkThresholds(metric: PerformanceMetric): void {
    if (!metric.threshold) return;

    const { warning, critical } = metric.threshold;
    let level: 'warning' | 'critical' | null = null;

    if (metric.value >= critical) {
      level = 'critical';
    } else if (metric.value >= warning) {
      level = 'warning';
    }

    if (level) {
      const alert: PerformanceAlert = {
        id: `${metric.metric}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        metric: metric.metric,
        level,
        value: metric.value,
        threshold: level === 'critical' ? critical : warning,
        timestamp: new Date(),
        resolved: false
      };

      this.alerts.push(alert);
      this.notifyListeners(alert);

      // Keep only last 100 alerts
      if (this.alerts.length > 100) {
        this.alerts.shift();
      }
    }
  }

  /**
   * Get current metrics summary
   */
  getMetricsSummary(): Record<string, any> {
    const summary: Record<string, any> = {};

    for (const [metricName, history] of this.metrics.entries()) {
      if (history.length === 0) continue;

      const recent = history.slice(-10); // Last 10 measurements
      const values = recent.map(m => m.value);
      
      summary[metricName] = {
        current: values[values.length - 1],
        average: values.reduce((a, b) => a + b, 0) / values.length,
        min: Math.min(...values),
        max: Math.max(...values),
        count: history.length,
        lastUpdated: recent[recent.length - 1].timestamp
      };
    }

    return summary;
  }

  /**
   * Get active alerts
   */
  getActiveAlerts(): PerformanceAlert[] {
    return this.alerts.filter(alert => !alert.resolved);
  }

  /**
   * Resolve an alert
   */
  resolveAlert(alertId: string): void {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.resolved = true;
    }
  }

  /**
   * Add alert listener
   */
  onAlert(listener: (alert: PerformanceAlert) => void): void {
    this.listeners.push(listener);
  }

  /**
   * Remove alert listener
   */
  removeListener(listener: (alert: PerformanceAlert) => void): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * Notify alert listeners
   */
  private notifyListeners(alert: PerformanceAlert): void {
    this.listeners.forEach(listener => {
      try {
        listener(alert);
      } catch (error) {
        console.error('Error in alert listener:', error);
      }
    });
  }

  /**
   * Start collecting system metrics
   */
  private startMetricsCollection(): void {
    // Collect metrics every 30 seconds
    setInterval(() => {
      this.trackMemoryUsage('system_monitor');
    }, 30000);

    // Flush metrics buffer every 10 seconds
    this.flushInterval = setInterval(() => {
      this.flushMetrics();
    }, 10000);
  }

  /**
   * Setup Performance Observer for Web APIs
   */
  private setupPerformanceObserver(): void {
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'navigation') {
              const navEntry = entry as PerformanceNavigationTiming;
              this.recordMetric({
                metric: 'frontend.page_load_time',
                value: navEntry.loadEventEnd - navEntry.fetchStart,
                timestamp: new Date(),
                threshold: {
                  warning: 3000,  // 3 seconds
                  critical: 5000  // 5 seconds
                }
              });
            }
          }
        });

        observer.observe({ entryTypes: ['navigation', 'resource'] });
      } catch (error) {
        console.warn('Performance Observer not supported:', error);
      }
    }
  }

  /**
   * Flush metrics buffer (for batch processing/external systems)
   */
  private flushMetrics(): void {
    if (this.metricsBuffer.length === 0) return;

    // Here you would send metrics to external monitoring systems
    // like DataDog, New Relic, Prometheus, etc.
    console.log(`Flushing ${this.metricsBuffer.length} metrics to external systems`);
    
    // Clear buffer
    this.metricsBuffer = [];
  }

  /**
   * Export metrics for external monitoring systems
   */
  exportMetrics(): PerformanceMetric[] {
    return [...this.metricsBuffer];
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.flushInterval) {
      clearInterval(this.flushInterval);
      this.flushInterval = null;
    }
    
    this.listeners = [];
    this.metrics.clear();
    this.alerts = [];
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

// Helper functions for common monitoring patterns
export const monitorAsync = async <T>(
  operation: () => Promise<T>,
  metricName: string,
  tags?: Record<string, string>
): Promise<T> => {
  const startTime = Date.now();
  
  try {
    const result = await operation();
    const duration = Date.now() - startTime;
    
    performanceMonitor.recordMetric({
      metric: `${metricName}.duration`,
      value: duration,
      timestamp: new Date(),
      tags
    });
    
    performanceMonitor.recordMetric({
      metric: `${metricName}.success`,
      value: 1,
      timestamp: new Date(),
      tags
    });
    
    return result;
  } catch (error) {
    const duration = Date.now() - startTime;
    
    performanceMonitor.recordMetric({
      metric: `${metricName}.duration`,
      value: duration,
      timestamp: new Date(),
      tags: { ...tags, error: 'true' }
    });
    
    performanceMonitor.recordMetric({
      metric: `${metricName}.error`,
      value: 1,
      timestamp: new Date(),
      tags
    });
    
    throw error;
  }
};

export default performanceMonitor;