import { z } from 'zod';

export const receivingSchema = z.object({
  productName: z.string()
    .min(1, 'Product name is required')
    .max(100, 'Product name cannot exceed 100 characters'),
  receivingDate: z.string()
    .min(1, 'Receiving date is required'),
  quantity: z.number()
    .positive('Quantity must be a positive number')
    .int('Quantity must be a whole number'),
  vendorName: z.string()
    .min(1, 'Vendor name is required'),
  condition: z.enum(['Excellent', 'Good', 'Fair', 'Poor', 'Damaged']),
  daysInTransit: z.number()
    .min(0, 'Days in transit cannot be negative')
    .optional(),
  costPerUnit: z.number()
    .positive('Cost must be a positive number')
    .optional(),
  notes: z.string()
    .max(500, 'Notes cannot exceed 500 characters')
    .optional()
});

export const importValidationSchema = z.object({
  name: z.string().min(1, 'Product name is required'),
  vendor: z.string().min(1, 'Vendor name is required'),
  condition: z.enum(['fresh', 'frozen', 'other'], {
    errorMap: () => ({ message: 'Product condition must be fresh, frozen, or other' })
  }),
  otherCondition: z.string().optional()
});

export const productSchema = z.object({
  name: z.string()
    .min(1, 'Product name is required')
    .max(100, 'Product name cannot exceed 100 characters'),
  date: z.string().nonempty('Date is required'),
  amount: z.number()
    .min(0, 'Amount cannot be negative')
    .transform(val => (isNaN(val) ? 0 : val)),
  condition: z.enum(['fresh', 'frozen', 'other'], {
    required_error: 'Please select a condition',
    invalid_type_error: 'Please select a valid condition',
  }),
  otherCondition: z.string()
    .min(1, 'Please specify the condition')
    .optional()
    .nullable(),
  category: z.string()
    .min(1, 'Category is required')
    .optional()
    .nullable(),
  subCategory: z.string()
    .optional()
    .nullable(),
  price: z.number()
    .min(0, 'Price cannot be negative')
    .transform(val => (isNaN(val) ? null : val))
    .nullable()
    .optional(),
  supplierId: z.string()
    .uuid()
    .optional()
    .nullable(),
  supplier: z.string()
    .optional()
    .nullable(),
  images: z.array(z.string().url())
    .optional()
    .nullable(),
  speciesDetails: z.object({
    scientificName: z.string().optional().nullable(),
    habitat: z.string().optional().nullable(),
    sustainabilityRating: z.string().optional().nullable()
  })
  .optional()
  .nullable(),
  notes: z.string()
    .max(500, 'Notes cannot exceed 500 characters')
    .optional()
    .nullable()
}).refine(
  (data) => !(data.condition === 'other' && !data.otherCondition),
  {
    message: "Other condition must be specified when condition is 'other'",
    path: ['otherCondition']
  }
);

export const vendorSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  contactName: z.string().optional(),
  email: z.string().email('Invalid email address').optional(),
  phone: z.string().optional(),
  address: z.string().optional(),
  status: z.enum(['active', 'inactive']).default('active'),
  paymentTerms: z.string().optional(),
  creditLimit: z.number().positive().optional(),
  metadata: z.record(z.unknown()).optional()
});

export const customerSchema = z.object({
  name: z.string()
    .min(1, 'Business name is required')
    .max(100, 'Business name cannot exceed 100 characters')
    .optional(),
  contactName: z.string()
    .min(1, 'Customer name is required'),
  email: z.string().email('Invalid email address').optional(),
  phone: z.string().optional(),
  address: z.string().optional(),
  channelType: z.enum(['wholesale', 'retail', 'distributor']),
  customerSource: z.string().optional().describe('Referral Source'),
  status: z.enum(['active', 'inactive']).default('active'),
  paymentTerms: z.string().optional(),
  creditLimit: z.number().positive().optional(),
  metadata: z.record(z.unknown()).optional()
}).refine(
  (data) => {
    // Business name is required for wholesale and distributor
    if (data.channelType !== 'retail') {
      return !!data.name;
    }
    return true;
  },
  {
    message: "Business name is required for wholesale and distributor customers",
    path: ['name']
  }
);

export type ImportValidationError = {
  field: string;
  message: string;
  code?: string;
  suggestion?: string;
};

export function validateImportData(data: ImportProductData): ImportValidationError[] {
  try {
    importValidationSchema.parse(data);
    return [];
  } catch (error) {
    if (error instanceof z.ZodError) {
      return error.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message,
        suggestion: getSuggestion(err.path[0] as string)
      }));
    }
    return [{
      field: 'unknown',
      message: error instanceof Error ? error.message : 'Unknown validation error'
    }];
  }
}

function getSuggestion(field: string): string {
  switch (field) {
    case 'name':
      return 'Provide a valid product name (e.g., "Atlantic Salmon")';
    case 'vendor':
      return 'Enter the full vendor company name';
    case 'condition':
      return 'Use one of: fresh, frozen, or other';
    case 'otherCondition':
      return 'Specify the condition when selecting other';
    default:
      return 'Check the field value and try again';
  }
}
