/**
 * Real-Time Sync Service
 * 
 * Dedicated service for continuous synchronization of TempStick sensor data
 * to remote Supabase database with enhanced monitoring and control.
 */

import { supabase } from './supabase';
import { tempStickService } from './tempstick-service';
import type { TempStickSensor, TempStickReading } from '../types/tempstick';

export interface SyncOperation {
  id: string;
  operation: 'sensor_sync' | 'readings_sync' | 'full_sync';
  status: 'pending' | 'running' | 'completed' | 'failed';
  startTime: Date;
  endTime?: Date;
  duration?: number;
  results: {
    sensorsProcessed: number;
    readingsProcessed: number;
    readingsSaved: number;
    errors: string[];
  };
}

export interface RealtimeSyncMetrics {
  totalOperations: number;
  successfulOperations: number;
  failedOperations: number;
  averageDuration: number;
  lastOperationTime: Date | null;
  uptime: number; // in milliseconds
  healthScore: number; // 0-100
}

export interface DatabaseWriteResult {
  success: boolean;
  recordsWritten: number;
  error?: string;
  latency: number;
}

export class RealtimeSyncService {
  private operations: Map<string, SyncOperation> = new Map();
  private metrics: RealtimeSyncMetrics;
  private isRunning: boolean = false;
  private syncInterval: NodeJS.Timeout | null = null;
  private startTime: Date;
  private maxOperationHistory = 50; // Keep last 50 operations

  constructor() {
    this.startTime = new Date();
    this.metrics = {
      totalOperations: 0,
      successfulOperations: 0,
      failedOperations: 0,
      averageDuration: 0,
      lastOperationTime: null,
      uptime: 0,
      healthScore: 100
    };
  }

  /**
   * Start continuous real-time sync with configurable interval
   */
  public startRealtimeSync(intervalMinutes: number = 5): void {
    if (this.isRunning) {
      console.warn('Real-time sync already running');
      return;
    }

    this.isRunning = true;
    console.log(`🚀 Starting real-time sync with ${intervalMinutes}-minute intervals`);

    // Initial sync
    this.performFullSync();

    // Set up interval
    this.syncInterval = setInterval(() => {
      this.performFullSync();
    }, intervalMinutes * 60 * 1000);
  }

  /**
   * Stop continuous sync
   */
  public stopRealtimeSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
    this.isRunning = false;
    console.log('⏹️ Real-time sync stopped');
  }

  /**
   * Perform full sync operation (sensors + readings)
   */
  public async performFullSync(): Promise<SyncOperation> {
    const operation = this.createOperation('full_sync');
    
    try {
      console.log(`📊 Starting full sync operation ${operation.id}`);
      operation.status = 'running';

      // Step 1: Sync sensors from TempStick API
      const sensorResult = await this.syncSensorsFromApi();
      operation.results.sensorsProcessed = sensorResult.sensorsProcessed;
      operation.results.errors.push(...sensorResult.errors);

      // Step 2: Sync temperature readings
      const readingsResult = await this.syncTemperatureReadings();
      operation.results.readingsProcessed = readingsResult.readingsProcessed;
      operation.results.readingsSaved = readingsResult.readingsSaved;
      operation.results.errors.push(...readingsResult.errors);

      // Mark as completed
      operation.status = operation.results.errors.length > 0 ? 'failed' : 'completed';
      this.completeOperation(operation);

      console.log(`✅ Full sync completed: ${operation.results.sensorsProcessed} sensors, ${operation.results.readingsSaved} readings saved`);
      
      return operation;

    } catch (error) {
      operation.status = 'failed';
      operation.results.errors.push(`Full sync failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      this.completeOperation(operation);
      
      console.error(`❌ Full sync failed:`, error);
      throw error;
    }
  }

  /**
   * Sync current sensor data from TempStick API to database
   */
  public async syncCurrentReadingsToDatabase(): Promise<DatabaseWriteResult> {
    const startTime = Date.now();
    
    try {
      console.log('📡 Fetching current readings from TempStick API...');

      // Get all sensors from TempStick API
      const tempStickSensors = await tempStickService.getAllSensors();
      
      if (tempStickSensors.length === 0) {
        return {
          success: true,
          recordsWritten: 0,
          latency: Date.now() - startTime
        };
      }

      const allReadings: Array<{
        user_id: string;
        sensor_id: string;
        temp_celsius: number;
        temp_fahrenheit: number;
        humidity?: number;
        recorded_at: string;
        raw_data: any;
      }> = [];

      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Fetch latest readings for each sensor
      for (const tempStickSensor of tempStickSensors) {
        try {
          const readings = await tempStickService.getLatestReadings(tempStickSensor.sensor_id, 1);
          
          if (readings.length > 0) {
            const reading = readings[0];
            
            // Find corresponding database sensor
            const { data: dbSensor } = await supabase
              .from('sensors')
              .select('id')
              .eq('sensor_id', tempStickSensor.sensor_id)
              .single();

            if (dbSensor) {
              // Convert temperature to Celsius (assuming TempStick returns Fahrenheit)
              const tempF = reading.temperature;
              const tempC = (tempF - 32) * 5 / 9;

              allReadings.push({
                user_id: user.id,
                sensor_id: dbSensor.id,
                temp_celsius: tempC,
                temp_fahrenheit: tempF,
                humidity: reading.humidity,
                recorded_at: reading.timestamp,
                raw_data: {
                  tempstick_sensor_id: tempStickSensor.sensor_id,
                  battery_level: reading.battery_level,
                  signal_strength: reading.signal_strength,
                  original_reading: reading
                }
              });
            }
          }
        } catch (error) {
          console.warn(`Failed to get readings for sensor ${tempStickSensor.sensor_id}:`, error);
        }
      }

      // Write readings to database
      if (allReadings.length > 0) {
        console.log(`💾 Writing ${allReadings.length} readings to Supabase database...`);
        
        const { data, error } = await supabase
          .from('temperature_readings')
          .insert(allReadings)
          .select('id');

        if (error) {
          console.error('Database write error:', error);
          return {
            success: false,
            recordsWritten: 0,
            error: error.message,
            latency: Date.now() - startTime
          };
        }

        console.log(`✅ Successfully wrote ${data?.length || allReadings.length} readings to database`);
        
        return {
          success: true,
          recordsWritten: data?.length || allReadings.length,
          latency: Date.now() - startTime
        };
      }

      return {
        success: true,
        recordsWritten: 0,
        latency: Date.now() - startTime
      };

    } catch (error) {
      console.error('Sync to database failed:', error);
      return {
        success: false,
        recordsWritten: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
        latency: Date.now() - startTime
      };
    }
  }

  /**
   * Get live readings from TempStick API (not saved to database)
   */
  public async getLiveReadingsFromApi(): Promise<Array<TempStickReading & { sensor_name: string }>> {
    try {
      console.log('🔍 Fetching live readings from TempStick API...');
      
      const tempStickSensors = await tempStickService.getAllSensors();
      const allReadings: Array<TempStickReading & { sensor_name: string }> = [];

      for (const sensor of tempStickSensors) {
        try {
          const readings = await tempStickService.getLatestReadings(sensor.sensor_id, 1);
          
          if (readings.length > 0) {
            allReadings.push({
              ...readings[0],
              sensor_name: sensor.sensor_name
            });
          }
        } catch (error) {
          console.warn(`Failed to get live reading for sensor ${sensor.sensor_id}:`, error);
        }
      }

      console.log(`📊 Retrieved ${allReadings.length} live readings from TempStick API`);
      return allReadings;

    } catch (error) {
      console.error('Failed to get live readings from API:', error);
      return [];
    }
  }

  /**
   * Sync sensors from TempStick API
   */
  private async syncSensorsFromApi(): Promise<{ sensorsProcessed: number; errors: string[] }> {
    try {
      const result = await tempStickService.syncSensors();
      return {
        sensorsProcessed: result.syncedSensors,
        errors: result.errors
      };
    } catch (error) {
      return {
        sensorsProcessed: 0,
        errors: [`Sensor sync failed: ${error instanceof Error ? error.message : 'Unknown error'}`]
      };
    }
  }

  /**
   * Sync temperature readings
   */
  private async syncTemperatureReadings(): Promise<{ readingsProcessed: number; readingsSaved: number; errors: string[] }> {
    try {
      const result = await tempStickService.syncAllTemperatureReadings();
      return {
        readingsProcessed: result.newReadings,
        readingsSaved: result.newReadings,
        errors: result.errors
      };
    } catch (error) {
      return {
        readingsProcessed: 0,
        readingsSaved: 0,
        errors: [`Readings sync failed: ${error instanceof Error ? error.message : 'Unknown error'}`]
      };
    }
  }

  /**
   * Create new sync operation
   */
  private createOperation(operation: SyncOperation['operation']): SyncOperation {
    const id = `${operation}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const newOperation: SyncOperation = {
      id,
      operation,
      status: 'pending',
      startTime: new Date(),
      results: {
        sensorsProcessed: 0,
        readingsProcessed: 0,
        readingsSaved: 0,
        errors: []
      }
    };

    this.operations.set(id, newOperation);
    
    // Cleanup old operations
    if (this.operations.size > this.maxOperationHistory) {
      const oldestKey = Array.from(this.operations.keys())[0];
      this.operations.delete(oldestKey);
    }

    this.metrics.totalOperations++;
    return newOperation;
  }

  /**
   * Complete sync operation and update metrics
   */
  private completeOperation(operation: SyncOperation): void {
    operation.endTime = new Date();
    operation.duration = operation.endTime.getTime() - operation.startTime.getTime();

    // Update metrics
    this.metrics.lastOperationTime = operation.endTime;
    this.metrics.uptime = Date.now() - this.startTime.getTime();

    if (operation.status === 'completed') {
      this.metrics.successfulOperations++;
    } else {
      this.metrics.failedOperations++;
    }

    // Calculate average duration
    const completedOperations = Array.from(this.operations.values())
      .filter(op => op.duration !== undefined);
    
    if (completedOperations.length > 0) {
      const totalDuration = completedOperations.reduce((sum, op) => sum + (op.duration || 0), 0);
      this.metrics.averageDuration = totalDuration / completedOperations.length;
    }

    // Calculate health score (0-100)
    const successRate = this.metrics.totalOperations > 0 
      ? (this.metrics.successfulOperations / this.metrics.totalOperations) * 100
      : 100;
    
    this.metrics.healthScore = Math.round(successRate);
  }

  /**
   * Get current metrics
   */
  public getMetrics(): RealtimeSyncMetrics {
    return { ...this.metrics };
  }

  /**
   * Get recent operations
   */
  public getRecentOperations(limit: number = 10): SyncOperation[] {
    return Array.from(this.operations.values())
      .sort((a, b) => b.startTime.getTime() - a.startTime.getTime())
      .slice(0, limit);
  }

  /**
   * Get current sync status
   */
  public getStatus(): {
    isRunning: boolean;
    currentOperations: number;
    nextScheduledSync: Date | null;
  } {
    const runningOps = Array.from(this.operations.values())
      .filter(op => op.status === 'running' || op.status === 'pending').length;

    // Calculate next sync time if interval is set
    let nextSync: Date | null = null;
    if (this.isRunning && this.metrics.lastOperationTime) {
      // Assuming 5-minute intervals by default
      nextSync = new Date(this.metrics.lastOperationTime.getTime() + (5 * 60 * 1000));
    }

    return {
      isRunning: this.isRunning,
      currentOperations: runningOps,
      nextScheduledSync: nextSync
    };
  }

  /**
   * Test database connectivity
   */
  public async testDatabaseConnection(): Promise<{ success: boolean; latency: number; error?: string }> {
    const startTime = Date.now();
    
    try {
      const { data, error } = await supabase
        .from('sensors')
        .select('id')
        .limit(1);

      if (error) {
        return {
          success: false,
          latency: Date.now() - startTime,
          error: error.message
        };
      }

      return {
        success: true,
        latency: Date.now() - startTime
      };
    } catch (error) {
      return {
        success: false,
        latency: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Shutdown service gracefully
   */
  public shutdown(): void {
    this.stopRealtimeSync();
    console.log('🔌 RealtimeSyncService shutdown complete');
  }
}

// Export singleton instance
export const realtimeSyncService = new RealtimeSyncService();