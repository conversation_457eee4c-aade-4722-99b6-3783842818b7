/**
 * Email Template System for TempStick Reports
 * 
 * Professional, responsive email templates for:
 * - Temperature compliance reports
 * - Alert notifications
 * - HACCP violation reports
 * - Daily/Weekly/Monthly summaries
 * - Custom temperature reports
 */

import type {
  TemperatureAlert,
  TemperatureReading,
  Sensor,
  StorageArea,
  TemperatureReportData
} from '../types/tempstick';

interface EmailTemplateData {
  companyName: string;
  companyLogo?: string;
  recipientName?: string;
  unsubscribeUrl: string;
  dashboardUrl: string;
  reportDate: string;
  sensors?: Sensor[];
  readings?: TemperatureReading[];
  alerts?: TemperatureAlert[];
  summary?: {
    totalSensors: number;
    activeAlerts: number;
    criticalAlerts: number;
    averageTemp: number;
    complianceRate: number;
    reportPeriod: string;
  };
  charts?: {
    temperatureTrend?: string;
    alertDistribution?: string;
    complianceMetrics?: string;
  };
  customData?: Record<string, any>;
}

/**
 * Email Template Builder Class
 */
export class EmailTemplateBuilder {
  private baseStyles = `
    <style>
      * { margin: 0; padding: 0; box-sizing: border-box; }
      body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; line-height: 1.6; color: #333333; }
      .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
      .header { background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); color: white; padding: 30px 20px; text-align: center; }
      .header h1 { font-size: 28px; font-weight: bold; margin-bottom: 8px; }
      .header p { font-size: 16px; opacity: 0.9; }
      .content { padding: 30px 20px; }
      .section { margin-bottom: 30px; }
      .section h2 { font-size: 22px; color: #1e40af; margin-bottom: 16px; padding-bottom: 8px; border-bottom: 2px solid #e5e7eb; }
      .section h3 { font-size: 18px; color: #374151; margin-bottom: 12px; }
      .alert-critical { background-color: #fef2f2; border-left: 4px solid #dc2626; padding: 16px; border-radius: 4px; margin-bottom: 16px; }
      .alert-high { background-color: #fff7ed; border-left: 4px solid #ea580c; padding: 16px; border-radius: 4px; margin-bottom: 16px; }
      .alert-medium { background-color: #fefce8; border-left: 4px solid #ca8a04; padding: 16px; border-radius: 4px; margin-bottom: 16px; }
      .alert-low { background-color: #f0f9ff; border-left: 4px solid #0284c7; padding: 16px; border-radius: 4px; margin-bottom: 16px; }
      .metric-card { background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; margin-bottom: 16px; text-align: center; }
      .metric-value { font-size: 32px; font-weight: bold; color: #1e40af; margin-bottom: 4px; }
      .metric-label { font-size: 14px; color: #64748b; text-transform: uppercase; letter-spacing: 0.5px; }
      .sensor-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 16px; margin-bottom: 24px; }
      .sensor-card { background-color: #ffffff; border: 1px solid #e2e8f0; border-radius: 8px; padding: 16px; }
      .sensor-status-online { border-left: 4px solid #059669; }
      .sensor-status-warning { border-left: 4px solid #d97706; }
      .sensor-status-critical { border-left: 4px solid #dc2626; }
      .sensor-status-offline { border-left: 4px solid #6b7280; }
      .temp-reading { display: flex; justify-content: space-between; align-items: center; padding: 8px 0; border-bottom: 1px solid #f1f5f9; }
      .temp-reading:last-child { border-bottom: none; }
      .table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
      .table th, .table td { padding: 12px; text-align: left; border-bottom: 1px solid #e2e8f0; }
      .table th { background-color: #f8fafc; font-weight: 600; color: #374151; }
      .table tr:hover { background-color: #f8fafc; }
      .btn { display: inline-block; padding: 12px 24px; background-color: #1e40af; color: white; text-decoration: none; border-radius: 6px; font-weight: 500; margin: 8px 8px 8px 0; }
      .btn-secondary { background-color: #6b7280; }
      .btn-danger { background-color: #dc2626; }
      .chart-container { background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 16px; margin: 16px 0; text-align: center; }
      .footer { background-color: #f8fafc; padding: 30px 20px; text-align: center; border-top: 1px solid #e2e8f0; }
      .footer p { color: #6b7280; font-size: 14px; margin-bottom: 8px; }
      .footer a { color: #1e40af; text-decoration: none; }
      .footer a:hover { text-decoration: underline; }
      .compliance-rate { font-size: 24px; font-weight: bold; }
      .compliance-excellent { color: #059669; }
      .compliance-good { color: #0284c7; }
      .compliance-warning { color: #d97706; }
      .compliance-critical { color: #dc2626; }
      @media only screen and (max-width: 600px) {
        .sensor-grid { grid-template-columns: 1fr; }
        .header { padding: 20px; }
        .header h1 { font-size: 24px; }
        .content { padding: 20px; }
        .metric-value { font-size: 28px; }
      }
    </style>
  `;

  /**
   * Build base email template structure
   */
  private buildBaseTemplate(
    title: string,
    subtitle: string,
    content: string,
    data: EmailTemplateData
  ): string {
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${title}</title>
        ${this.baseStyles}
      </head>
      <body>
        <div class="container">
          <div class="header">
            ${data.companyLogo ? `<img src="${data.companyLogo}" alt="${data.companyName}" style="max-height: 40px; margin-bottom: 16px;">` : ''}
            <h1>${title}</h1>
            <p>${subtitle}</p>
          </div>
          
          <div class="content">
            ${content}
          </div>
          
          <div class="footer">
            <p><strong>${data.companyName}</strong> - Temperature Monitoring System</p>
            <p>Generated on ${data.reportDate}</p>
            <p>
              <a href="${data.dashboardUrl}">View Dashboard</a> | 
              <a href="${data.unsubscribeUrl}">Unsubscribe</a>
            </p>
            <p style="margin-top: 16px; font-size: 12px; color: #9ca3af;">
              This is an automated message from your temperature monitoring system. 
              Please do not reply to this email.
            </p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate alert notification email
   */
  generateAlertNotification(
    alert: TemperatureAlert & { sensors: Sensor & { storage_areas: StorageArea } },
    data: EmailTemplateData
  ): { html: string; text: string } {
    const sensor = alert.sensors;
    const storageArea = sensor.storage_areas;
    const alertType = alert.alert_type.replace(/_/g, ' ').toUpperCase();
    const severityClass = `alert-${alert.severity}`;
    
    const content = `
      <div class="section">
        <div class="${severityClass}">
          <h2>🚨 ${alertType} ALERT</h2>
          <div style="margin-top: 16px;">
            <strong>Sensor:</strong> ${sensor.name}<br>
            <strong>Location:</strong> ${sensor.location}<br>
            ${storageArea ? `<strong>Storage Area:</strong> ${storageArea.name}<br>` : ''}
            ${alert.temperature ? `<strong>Temperature:</strong> ${alert.temperature.toFixed(1)}°F<br>` : ''}
            <strong>Severity:</strong> ${alert.severity.toUpperCase()}<br>
            <strong>Time:</strong> ${new Date(alert.created_at).toLocaleString()}
          </div>
          
          ${storageArea?.haccp_control_point ? `
            <div style="margin-top: 16px; padding: 12px; background-color: rgba(220, 38, 38, 0.1); border-radius: 4px;">
              ⚠️ <strong>HACCP CRITICAL CONTROL POINT VIOLATION</strong><br>
              This alert affects a critical control point and requires immediate attention.
            </div>
          ` : ''}
        </div>
      </div>
      
      <div class="section">
        <h3>Recommended Actions</h3>
        ${this.getRecommendedActionsHTML(alert)}
      </div>
      
      <div class="section">
        <a href="${data.dashboardUrl}/alerts/${alert.id}" class="btn">View Alert Details</a>
        <a href="${data.dashboardUrl}/sensors/${sensor.id}" class="btn btn-secondary">View Sensor</a>
      </div>
    `;
    
    const html = this.buildBaseTemplate(
      `Temperature Alert: ${alertType}`,
      `Sensor: ${sensor.name} - ${sensor.location}`,
      content,
      data
    );
    
    const text = this.generatePlainTextAlert(alert, data);
    
    return { html, text };
  }

  /**
   * Generate daily temperature report email
   */
  generateDailyReport(
    reportData: TemperatureReportData,
    data: EmailTemplateData
  ): { html: string; text: string } {
    const summary = reportData.summary;
    const complianceClass = this.getComplianceClass(summary.complianceRate);
    
    const content = `
      <div class="section">
        <h2>Daily Summary - ${summary.reportPeriod.start}</h2>
        
        <div class="sensor-grid">
          <div class="metric-card">
            <div class="metric-value">${summary.sensorsIncluded}</div>
            <div class="metric-label">Active Sensors</div>
          </div>
          <div class="metric-card">
            <div class="metric-value">${summary.totalReadings.toLocaleString()}</div>
            <div class="metric-label">Temperature Readings</div>
          </div>
          <div class="metric-card">
            <div class="metric-value" style="color: ${summary.totalAlerts > 0 ? '#dc2626' : '#059669'}">${summary.totalAlerts}</div>
            <div class="metric-label">Alerts Generated</div>
          </div>
          <div class="metric-card">
            <div class="metric-value compliance-rate ${complianceClass}">${summary.complianceRate.toFixed(1)}%</div>
            <div class="metric-label">Compliance Rate</div>
          </div>
        </div>
      </div>
      
      ${this.generateSensorStatusSection(reportData.sensorData)}
      
      ${reportData.alerts.length > 0 ? this.generateAlertsSection(reportData.alerts) : ''}
      
      ${reportData.haccpCompliance.length > 0 ? this.generateHACCPSection(reportData.haccpCompliance) : ''}
      
      ${data.charts?.temperatureTrend ? `
        <div class="section">
          <h2>Temperature Trends</h2>
          <div class="chart-container">
            <img src="data:image/png;base64,${data.charts.temperatureTrend}" alt="Temperature Trend Chart" style="max-width: 100%; height: auto;">
          </div>
        </div>
      ` : ''}
      
      <div class="section">
        <a href="${data.dashboardUrl}/reports" class="btn">View Full Report</a>
        <a href="${data.dashboardUrl}" class="btn btn-secondary">Open Dashboard</a>
      </div>
    `;
    
    const html = this.buildBaseTemplate(
      'Daily Temperature Report',
      `${summary.sensorsIncluded} sensors monitored - ${summary.complianceRate.toFixed(1)}% compliance`,
      content,
      data
    );
    
    const text = this.generatePlainTextReport(reportData, data);
    
    return { html, text };
  }

  /**
   * Generate weekly summary report email
   */
  generateWeeklyReport(
    reportData: TemperatureReportData,
    data: EmailTemplateData
  ): { html: string; text: string } {
    const summary = reportData.summary;
    const complianceClass = this.getComplianceClass(summary.complianceRate);
    
    const content = `
      <div class="section">
        <h2>Weekly Summary Report</h2>
        <p style="color: #6b7280; margin-bottom: 24px;">
          Report Period: ${summary.reportPeriod.start} to ${summary.reportPeriod.end}
        </p>
        
        <div class="sensor-grid">
          <div class="metric-card">
            <div class="metric-value">${summary.sensorsIncluded}</div>
            <div class="metric-label">Sensors Monitored</div>
          </div>
          <div class="metric-card">
            <div class="metric-value">${(summary.totalReadings / 1000).toFixed(1)}K</div>
            <div class="metric-label">Total Readings</div>
          </div>
          <div class="metric-card">
            <div class="metric-value" style="color: ${summary.totalAlerts > 0 ? '#dc2626' : '#059669'}">${summary.totalAlerts}</div>
            <div class="metric-label">Weekly Alerts</div>
          </div>
          <div class="metric-card">
            <div class="metric-value compliance-rate ${complianceClass}">${summary.complianceRate.toFixed(1)}%</div>
            <div class="metric-label">Avg Compliance</div>
          </div>
        </div>
      </div>
      
      ${this.generateWeeklyTrendsSection(reportData)}
      
      ${this.generateTopAlertsSection(reportData.alerts.slice(0, 10))}
      
      ${data.charts?.complianceMetrics ? `
        <div class="section">
          <h2>Compliance Metrics</h2>
          <div class="chart-container">
            <img src="data:image/png;base64,${data.charts.complianceMetrics}" alt="Compliance Metrics Chart" style="max-width: 100%; height: auto;">
          </div>
        </div>
      ` : ''}
      
      <div class="section">
        <a href="${data.dashboardUrl}/reports/weekly" class="btn">View Detailed Report</a>
        <a href="${data.dashboardUrl}/analytics" class="btn btn-secondary">View Analytics</a>
      </div>
    `;
    
    const html = this.buildBaseTemplate(
      'Weekly Temperature Summary',
      `Monitoring report for ${summary.sensorsIncluded} sensors`,
      content,
      data
    );
    
    const text = this.generatePlainTextWeeklyReport(reportData, data);
    
    return { html, text };
  }

  /**
   * Generate HACCP compliance report email
   */
  generateHACCPReport(
    reportData: TemperatureReportData,
    data: EmailTemplateData
  ): { html: string; text: string } {
    const haccpViolations = reportData.alerts.filter(alert => alert.alert_type === 'haccp_violation');
    const criticalViolations = haccpViolations.filter(alert => alert.severity === 'critical');
    
    const content = `
      <div class="section">
        <h2>HACCP Compliance Report</h2>
        <p style="color: #6b7280; margin-bottom: 24px;">
          Report Period: ${reportData.summary.reportPeriod.start} to ${reportData.summary.reportPeriod.end}
        </p>
        
        <div class="sensor-grid">
          <div class="metric-card">
            <div class="metric-value compliance-rate ${this.getComplianceClass(reportData.summary.complianceRate)}">${reportData.summary.complianceRate.toFixed(1)}%</div>
            <div class="metric-label">Overall Compliance</div>
          </div>
          <div class="metric-card">
            <div class="metric-value" style="color: ${haccpViolations.length > 0 ? '#dc2626' : '#059669'}">${haccpViolations.length}</div>
            <div class="metric-label">HACCP Violations</div>
          </div>
          <div class="metric-card">
            <div class="metric-value" style="color: ${criticalViolations.length > 0 ? '#dc2626' : '#059669'}">${criticalViolations.length}</div>
            <div class="metric-label">Critical Violations</div>
          </div>
          <div class="metric-card">
            <div class="metric-value">${reportData.haccpCompliance.length}</div>
            <div class="metric-label">Control Points</div>
          </div>
        </div>
      </div>
      
      ${this.generateHACCPComplianceDetails(reportData.haccpCompliance)}
      
      ${haccpViolations.length > 0 ? `
        <div class="section">
          <h2>🚨 HACCP Violations</h2>
          ${haccpViolations.map(alert => `
            <div class="alert-${alert.severity}" style="margin-bottom: 16px;">
              <h4>${alert.alert_type.replace(/_/g, ' ').toUpperCase()}</h4>
              <p><strong>Time:</strong> ${new Date(alert.created_at).toLocaleString()}</p>
              ${alert.temperature ? `<p><strong>Temperature:</strong> ${alert.temperature.toFixed(1)}°F</p>` : ''}
              <p><strong>Severity:</strong> ${alert.severity.toUpperCase()}</p>
            </div>
          `).join('')}
        </div>
      ` : ''}
      
      <div class="section">
        <a href="${data.dashboardUrl}/haccp" class="btn">View HACCP Dashboard</a>
        <a href="${data.dashboardUrl}/reports/haccp" class="btn btn-secondary">Download Full Report</a>
      </div>
    `;
    
    const html = this.buildBaseTemplate(
      'HACCP Compliance Report',
      `${reportData.summary.complianceRate.toFixed(1)}% compliance rate`,
      content,
      data
    );
    
    const text = this.generatePlainTextHACCPReport(reportData, data);
    
    return { html, text };
  }

  /**
   * Generate sensor status section
   */
  private generateSensorStatusSection(sensorData: TemperatureReportData['sensorData']): string {
    if (!sensorData.length) return '';
    
    return `
      <div class="section">
        <h2>Sensor Status</h2>
        <div class="sensor-grid">
          ${sensorData.slice(0, 6).map(data => {
            const latestReading = data.readings[data.readings.length - 1];
            const status = this.getSensorStatus(data);
            return `
              <div class="sensor-card sensor-status-${status}">
                <h4>${data.sensor.name}</h4>
                <p style="color: #6b7280; margin-bottom: 8px;">${data.sensor.location}</p>
                ${latestReading ? `
                  <div class="temp-reading">
                    <span>Latest Temperature:</span>
                    <span style="font-weight: bold;">${latestReading.temperature.toFixed(1)}°F</span>
                  </div>
                  <div class="temp-reading">
                    <span>Readings Today:</span>
                    <span style="font-weight: bold;">${data.readings.length}</span>
                  </div>
                  <div class="temp-reading">
                    <span>Alerts:</span>
                    <span style="font-weight: bold; color: ${data.alerts.length > 0 ? '#dc2626' : '#059669'}">${data.alerts.length}</span>
                  </div>
                ` : '<p style="color: #6b7280;">No readings available</p>'}
              </div>
            `;
          }).join('')}
        </div>
        ${sensorData.length > 6 ? `<p style="text-align: center; margin-top: 16px; color: #6b7280;">And ${sensorData.length - 6} more sensors...</p>` : ''}
      </div>
    `;
  }

  /**
   * Generate alerts section
   */
  private generateAlertsSection(alerts: TemperatureAlert[]): string {
    if (!alerts.length) return '';
    
    const recentAlerts = alerts.slice(0, 5);
    
    return `
      <div class="section">
        <h2>Recent Alerts</h2>
        <table class="table">
          <thead>
            <tr>
              <th>Time</th>
              <th>Type</th>
              <th>Severity</th>
              <th>Temperature</th>
            </tr>
          </thead>
          <tbody>
            ${recentAlerts.map(alert => `
              <tr>
                <td>${new Date(alert.created_at).toLocaleString()}</td>
                <td>${alert.alert_type.replace(/_/g, ' ')}</td>
                <td><span style="color: ${this.getSeverityColor(alert.severity)}">${alert.severity.toUpperCase()}</span></td>
                <td>${alert.temperature ? `${alert.temperature.toFixed(1)  }°F` : 'N/A'}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
        ${alerts.length > 5 ? `<p style="color: #6b7280;">And ${alerts.length - 5} more alerts...</p>` : ''}
      </div>
    `;
  }

  /**
   * Generate HACCP section
   */
  private generateHACCPSection(haccpCompliance: TemperatureReportData['haccpCompliance']): string {
    if (!haccpCompliance.length) return '';
    
    return `
      <div class="section">
        <h2>HACCP Compliance Status</h2>
        ${haccpCompliance.map(compliance => `
          <div class="metric-card" style="text-align: left; margin-bottom: 16px;">
            <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 8px;">
              <h4>${compliance.storageArea.name}</h4>
              <span class="compliance-rate ${this.getComplianceClass(compliance.complianceRate)}">${compliance.complianceRate.toFixed(1)}%</span>
            </div>
            <div class="temp-reading">
              <span>Violations:</span>
              <span style="color: ${compliance.violationsCount > 0 ? '#dc2626' : '#059669'}">${compliance.violationsCount}</span>
            </div>
            <div class="temp-reading">
              <span>Critical Violations:</span>
              <span style="color: ${compliance.criticalViolations.length > 0 ? '#dc2626' : '#059669'}">${compliance.criticalViolations.length}</span>
            </div>
          </div>
        `).join('')}
      </div>
    `;
  }

  /**
   * Helper methods
   */
  private getRecommendedActionsHTML(alert: TemperatureAlert): string {
    const actions = this.getRecommendedActions(alert.alert_type);
    return `<ul style="margin-left: 20px;">${actions.map(action => `<li style="margin-bottom: 4px;">${action}</li>`).join('')}</ul>`;
  }

  private getRecommendedActions(alertType: string): string[] {
    switch (alertType) {
      case 'high_temp':
        return [
          'Check refrigeration system immediately',
          'Verify door seals are intact',
          'Move products to backup storage if necessary',
          'Document corrective actions taken'
        ];
      case 'low_temp':
        return [
          'Check if freezer is functioning properly',
          'Verify temperature sensor calibration',
          'Ensure products are not frozen when they shouldn\'t be',
          'Document any product quality impacts'
        ];
      case 'haccp_violation':
        return [
          'IMMEDIATE HACCP CORRECTIVE ACTION REQUIRED',
          'Stop all operations in affected area',
          'Quarantine affected products',
          'Contact HACCP coordinator',
          'Document all actions in HACCP log'
        ];
      default:
        return ['Please investigate and take appropriate corrective action'];
    }
  }

  private getComplianceClass(rate: number): string {
    if (rate >= 95) return 'compliance-excellent';
    if (rate >= 85) return 'compliance-good';
    if (rate >= 70) return 'compliance-warning';
    return 'compliance-critical';
  }

  private getSeverityColor(severity: string): string {
    switch (severity) {
      case 'critical': return '#dc2626';
      case 'high': return '#ea580c';
      case 'medium': return '#ca8a04';
      case 'low': return '#0284c7';
      default: return '#6b7280';
    }
  }

  private getSensorStatus(data: TemperatureReportData['sensorData'][0]): string {
    if (!data.readings.length) return 'offline';
    if (data.alerts.some(alert => alert.severity === 'critical')) return 'critical';
    if (data.alerts.length > 0) return 'warning';
    return 'online';
  }

  private generateWeeklyTrendsSection(reportData: TemperatureReportData): string {
    // This would typically include trend analysis
    return `
      <div class="section">
        <h2>Weekly Trends</h2>
        <div class="metric-card">
          <p style="margin-bottom: 16px;">Analysis of temperature patterns and compliance trends over the past week.</p>
          <p><strong>Average daily readings:</strong> ${Math.round(reportData.summary.totalReadings / 7).toLocaleString()}</p>
          <p><strong>Peak alert period:</strong> Most alerts occurred during evening hours</p>
          <p><strong>Best performing sensors:</strong> ${reportData.sensorData.filter(s => s.alerts.length === 0).length} sensors with zero violations</p>
        </div>
      </div>
    `;
  }

  private generateTopAlertsSection(alerts: TemperatureAlert[]): string {
    if (!alerts.length) return '';
    
    return `
      <div class="section">
        <h2>Top Alerts This Week</h2>
        <table class="table">
          <thead>
            <tr>
              <th>Date</th>
              <th>Type</th>
              <th>Severity</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            ${alerts.map(alert => `
              <tr>
                <td>${new Date(alert.created_at).toLocaleDateString()}</td>
                <td>${alert.alert_type.replace(/_/g, ' ')}</td>
                <td><span style="color: ${this.getSeverityColor(alert.severity)}">${alert.severity}</span></td>
                <td>${alert.resolved_at ? '✅ Resolved' : '🔄 Active'}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    `;
  }

  private generateHACCPComplianceDetails(haccpCompliance: TemperatureReportData['haccpCompliance']): string {
    return `
      <div class="section">
        <h2>Control Point Details</h2>
        ${haccpCompliance.map(compliance => `
          <div class="metric-card" style="text-align: left; margin-bottom: 16px; ${compliance.violationsCount > 0 ? 'border-left: 4px solid #dc2626;' : 'border-left: 4px solid #059669;'}">
            <h4>${compliance.storageArea.name}</h4>
            <div style="margin-top: 12px;">
              <div class="temp-reading">
                <span>Compliance Rate:</span>
                <span class="compliance-rate ${this.getComplianceClass(compliance.complianceRate)}">${compliance.complianceRate.toFixed(1)}%</span>
              </div>
              <div class="temp-reading">
                <span>Total Violations:</span>
                <span style="color: ${compliance.violationsCount > 0 ? '#dc2626' : '#059669'}">${compliance.violationsCount}</span>
              </div>
              <div class="temp-reading">
                <span>Critical Violations:</span>
                <span style="color: ${compliance.criticalViolations.length > 0 ? '#dc2626' : '#059669'}">${compliance.criticalViolations.length}</span>
              </div>
            </div>
          </div>
        `).join('')}
      </div>
    `;
  }

  /**
   * Generate plain text versions for better accessibility and backup
   */
  private generatePlainTextAlert(
    alert: TemperatureAlert & { sensors: Sensor & { storage_areas: StorageArea } },
    data: EmailTemplateData
  ): string {
    const sensor = alert.sensors;
    const storageArea = sensor.storage_areas;
    const alertType = alert.alert_type.replace(/_/g, ' ').toUpperCase();
    
    let text = `${data.companyName} - Temperature Alert\n`;
    text += `=========================================\n\n`;
    text += `ALERT: ${alertType}\n`;
    text += `Sensor: ${sensor.name}\n`;
    text += `Location: ${sensor.location}\n`;
    
    if (storageArea) {
      text += `Storage Area: ${storageArea.name}\n`;
      if (storageArea.haccp_control_point) {
        text += `** HACCP CRITICAL CONTROL POINT **\n`;
      }
    }
    
    if (alert.temperature) {
      text += `Temperature: ${alert.temperature.toFixed(1)}°F\n`;
    }
    
    text += `Severity: ${alert.severity.toUpperCase()}\n`;
    text += `Time: ${new Date(alert.created_at).toLocaleString()}\n\n`;
    
    text += `Recommended Actions:\n`;
    const actions = this.getRecommendedActions(alert.alert_type);
    actions.forEach(action => {
      text += `• ${action}\n`;
    });
    
    text += `\nView Alert: ${data.dashboardUrl}/alerts/${alert.id}\n`;
    text += `Dashboard: ${data.dashboardUrl}\n`;
    text += `Unsubscribe: ${data.unsubscribeUrl}\n`;
    
    return text;
  }

  private generatePlainTextReport(reportData: TemperatureReportData, data: EmailTemplateData): string {
    const summary = reportData.summary;
    
    let text = `${data.companyName} - Daily Temperature Report\n`;
    text += `=============================================\n\n`;
    text += `Report Date: ${summary.reportPeriod.start}\n\n`;
    
    text += `SUMMARY:\n`;
    text += `• Active Sensors: ${summary.sensorsIncluded}\n`;
    text += `• Temperature Readings: ${summary.totalReadings.toLocaleString()}\n`;
    text += `• Alerts Generated: ${summary.totalAlerts}\n`;
    text += `• Compliance Rate: ${summary.complianceRate.toFixed(1)}%\n\n`;
    
    if (reportData.alerts.length > 0) {
      text += `RECENT ALERTS:\n`;
      reportData.alerts.slice(0, 5).forEach(alert => {
        text += `• ${new Date(alert.created_at).toLocaleString()} - ${alert.alert_type.replace(/_/g, ' ')} (${alert.severity})\n`;
      });
      text += '\n';
    }
    
    text += `View Full Report: ${data.dashboardUrl}/reports\n`;
    text += `Dashboard: ${data.dashboardUrl}\n`;
    text += `Unsubscribe: ${data.unsubscribeUrl}\n`;
    
    return text;
  }

  private generatePlainTextWeeklyReport(reportData: TemperatureReportData, data: EmailTemplateData): string {
    const summary = reportData.summary;
    
    let text = `${data.companyName} - Weekly Temperature Summary\n`;
    text += `===============================================\n\n`;
    text += `Report Period: ${summary.reportPeriod.start} to ${summary.reportPeriod.end}\n\n`;
    
    text += `WEEKLY SUMMARY:\n`;
    text += `• Sensors Monitored: ${summary.sensorsIncluded}\n`;
    text += `• Total Readings: ${summary.totalReadings.toLocaleString()}\n`;
    text += `• Weekly Alerts: ${summary.totalAlerts}\n`;
    text += `• Average Compliance: ${summary.complianceRate.toFixed(1)}%\n\n`;
    
    text += `View Detailed Report: ${data.dashboardUrl}/reports/weekly\n`;
    text += `Analytics Dashboard: ${data.dashboardUrl}/analytics\n`;
    text += `Unsubscribe: ${data.unsubscribeUrl}\n`;
    
    return text;
  }

  private generatePlainTextHACCPReport(reportData: TemperatureReportData, data: EmailTemplateData): string {
    const haccpViolations = reportData.alerts.filter(alert => alert.alert_type === 'haccp_violation');
    
    let text = `${data.companyName} - HACCP Compliance Report\n`;
    text += `===========================================\n\n`;
    text += `Report Period: ${reportData.summary.reportPeriod.start} to ${reportData.summary.reportPeriod.end}\n\n`;
    
    text += `COMPLIANCE SUMMARY:\n`;
    text += `• Overall Compliance: ${reportData.summary.complianceRate.toFixed(1)}%\n`;
    text += `• HACCP Violations: ${haccpViolations.length}\n`;
    text += `• Critical Violations: ${haccpViolations.filter(v => v.severity === 'critical').length}\n`;
    text += `• Control Points: ${reportData.haccpCompliance.length}\n\n`;
    
    if (haccpViolations.length > 0) {
      text += `VIOLATIONS:\n`;
      haccpViolations.forEach(violation => {
        text += `• ${new Date(violation.created_at).toLocaleString()} - ${violation.severity.toUpperCase()}\n`;
      });
      text += '\n';
    }
    
    text += `HACCP Dashboard: ${data.dashboardUrl}/haccp\n`;
    text += `Download Full Report: ${data.dashboardUrl}/reports/haccp\n`;
    text += `Unsubscribe: ${data.unsubscribeUrl}\n`;
    
    return text;
  }
}

// Export singleton instance
export const emailTemplateBuilder = new EmailTemplateBuilder();