import { SupabaseClient } from '@supabase/supabase-js';
import {
  AudioFile,
  AudioUploadOptions,
  AudioDownloadOptions,
  AudioCompressionOptions,
  AudioStorageConfig,
  StorageMetrics,
  StorageHealth,
  AudioProcessingResult,
  AudioCleanupOptions,
  AudioCleanupResult,
  AudioStorageEvent,
  AudioStorageEventType,
  AudioMetadata
} from './types';
import { errorManager, StorageError } from '../error-handling';

/**
 * Centralized audio storage management system
 */
export class AudioStorageManager {
  private supabase: SupabaseClient;
  private config: AudioStorageConfig;
  private metrics: StorageMetrics;
  private eventListeners = new Map<AudioStorageEventType, Set<(event: AudioStorageEvent) => void>>();
  private cleanupInterval?: NodeJS.Timeout;
  private healthCheckInterval?: NodeJS.Timeout;

  constructor(supabase: SupabaseClient, config: Partial<AudioStorageConfig> = {}) {
    this.supabase = supabase;
    this.config = {
      bucketName: config.bucketName || 'voice-recordings',
      maxFileSize: config.maxFileSize || 10 * 1024 * 1024, // 10MB
      allowedMimeTypes: config.allowedMimeTypes || ['audio/webm', 'audio/wav', 'audio/mp3', 'audio/ogg'],
      defaultCompression: config.defaultCompression ?? true,
      defaultQuality: config.defaultQuality || 'medium',
      enableCleanup: config.enableCleanup ?? true,
      cleanupInterval: config.cleanupInterval || 24 * 60 * 60 * 1000, // 24 hours
      retentionDays: config.retentionDays || 90,
      enableMetrics: config.enableMetrics ?? true
    };

    this.metrics = {
      totalFiles: 0,
      totalSize: 0,
      averageFileSize: 0,
      compressionSavings: 0,
      uploadCount: 0,
      downloadCount: 0,
      errorCount: 0,
      cleanupCount: 0
    };

    this.initializeBucket();
    this.startCleanupSchedule();
    this.startHealthChecking();
  }

  /**
   * Upload audio file
   */
  async uploadAudio(
    audioBlob: Blob,
    filename: string,
    options: AudioUploadOptions = {}
  ): Promise<AudioFile> {
    try {
      // Validate file
      this.validateAudioFile(audioBlob, options);

      // Generate file path
      const filePath = this.generateFilePath(filename, options.metadata);

      // Compress if enabled
      let processedBlob = audioBlob;
      let processingResult: AudioProcessingResult | undefined;

      if (options.compress ?? this.config.defaultCompression) {
        const compressionOptions: AudioCompressionOptions = {
          quality: options.quality || this.config.defaultQuality
        };
        
        const compressionResult = await this.compressAudio(audioBlob, compressionOptions);
        if (compressionResult.success) {
          processedBlob = new Blob([compressionResult.compressedSize], { type: audioBlob.type });
          processingResult = compressionResult;
        }
      }

      // Upload to storage
      const { data, error } = await this.supabase.storage
        .from(this.config.bucketName)
        .upload(filePath, processedBlob, {
          cacheControl: '3600',
          upsert: false,
          metadata: {
            ...options.metadata,
            originalSize: audioBlob.size.toString(),
            processedSize: processedBlob.size.toString(),
            uploadedAt: new Date().toISOString()
          }
        });

      if (error) {
        throw new StorageError(`Upload failed: ${error.message}`, 'upload');
      }

      // Create audio file record
      const audioFile: AudioFile = {
        id: this.generateFileId(),
        path: data.path,
        originalName: filename,
        mimeType: audioBlob.type,
        size: processedBlob.size,
        metadata: {
          ...options.metadata,
          originalSize: audioBlob.size,
          compressionRatio: processingResult?.compressionRatio
        },
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Update metrics
      this.updateMetrics('upload', audioFile);

      // Emit event
      this.emitEvent('file_uploaded', audioFile.id, audioFile);

      return audioFile;
    } catch (error) {
      this.metrics.errorCount++;
      this.emitEvent('storage_error', undefined, undefined, error as Error);
      
      await errorManager.handleError(error, {
        component: 'AudioStorageManager',
        action: 'uploadAudio',
        filename
      });
      
      throw error;
    }
  }

  /**
   * Download audio file
   */
  async downloadAudio(
    filePath: string,
    options: AudioDownloadOptions = {}
  ): Promise<Blob | string> {
    try {
      if (options.signedUrl) {
        const { data, error } = await this.supabase.storage
          .from(this.config.bucketName)
          .createSignedUrl(filePath, options.expiresIn || 3600);

        if (error) {
          throw new StorageError(`Failed to create signed URL: ${error.message}`, 'download');
        }

        this.metrics.downloadCount++;
        this.emitEvent('file_downloaded', filePath);
        
        return data.signedUrl;
      } else {
        const { data, error } = await this.supabase.storage
          .from(this.config.bucketName)
          .download(filePath);

        if (error) {
          throw new StorageError(`Download failed: ${error.message}`, 'download');
        }

        this.metrics.downloadCount++;
        this.emitEvent('file_downloaded', filePath);
        
        return data;
      }
    } catch (error) {
      this.metrics.errorCount++;
      this.emitEvent('storage_error', filePath, undefined, error as Error);
      
      await errorManager.handleError(error, {
        component: 'AudioStorageManager',
        action: 'downloadAudio',
        filePath
      });
      
      throw error;
    }
  }

  /**
   * Delete audio file
   */
  async deleteAudio(filePath: string): Promise<void> {
    try {
      const { error } = await this.supabase.storage
        .from(this.config.bucketName)
        .remove([filePath]);

      if (error) {
        throw new StorageError(`Delete failed: ${error.message}`, 'delete');
      }

      this.emitEvent('file_deleted', filePath);
    } catch (error) {
      this.metrics.errorCount++;
      this.emitEvent('storage_error', filePath, undefined, error as Error);
      
      await errorManager.handleError(error, {
        component: 'AudioStorageManager',
        action: 'deleteAudio',
        filePath
      });
      
      throw error;
    }
  }

  /**
   * List audio files
   */
  async listAudioFiles(
    prefix?: string,
    limit?: number,
    offset?: number
  ): Promise<AudioFile[]> {
    try {
      const { data, error } = await this.supabase.storage
        .from(this.config.bucketName)
        .list(prefix, {
          limit,
          offset,
          sortBy: { column: 'created_at', order: 'desc' }
        });

      if (error) {
        throw new StorageError(`List failed: ${error.message}`, 'list');
      }

      return (data || []).map(file => ({
        id: file.id || this.generateFileId(),
        path: file.name,
        originalName: file.name,
        mimeType: file.metadata?.mimetype || 'audio/webm',
        size: file.metadata?.size || 0,
        metadata: file.metadata,
        createdAt: new Date(file.created_at),
        updatedAt: new Date(file.updated_at)
      }));
    } catch (error) {
      this.metrics.errorCount++;
      
      await errorManager.handleError(error, {
        component: 'AudioStorageManager',
        action: 'listAudioFiles',
        prefix
      });
      
      throw error;
    }
  }

  /**
   * Compress audio file
   */
  async compressAudio(
    audioBlob: Blob,
    options: AudioCompressionOptions
  ): Promise<AudioProcessingResult> {
    try {
      // For now, implement basic compression simulation
      // In a real implementation, you would use Web Audio API or a compression library
      
      const originalSize = audioBlob.size;
      let compressionRatio = 1;

      switch (options.quality) {
        case 'low':
          compressionRatio = 0.3;
          break;
        case 'medium':
          compressionRatio = 0.5;
          break;
        case 'high':
          compressionRatio = 0.7;
          break;
      }

      const compressedSize = Math.floor(originalSize * compressionRatio);

      this.emitEvent('file_compressed', undefined, {
        originalSize,
        compressedSize,
        compressionRatio: 1 - compressionRatio
      });

      return {
        success: true,
        originalSize,
        compressedSize,
        compressionRatio: 1 - compressionRatio,
        format: options.format || 'webm'
      };
    } catch (error) {
      return {
        success: false,
        originalSize: audioBlob.size,
        compressedSize: audioBlob.size,
        compressionRatio: 0,
        format: 'webm',
        error: (error as Error).message
      };
    }
  }

  /**
   * Cleanup old files
   */
  async cleanupOldFiles(options: AudioCleanupOptions = {}): Promise<AudioCleanupResult> {
    const startTime = Date.now();
    let filesDeleted = 0;
    let spaceFreed = 0;
    const errors: string[] = [];

    try {
      const cutoffDate = options.olderThan || new Date(Date.now() - this.config.retentionDays * 24 * 60 * 60 * 1000);
      
      const { data: files, error } = await this.supabase.storage
        .from(this.config.bucketName)
        .list('', {
          limit: options.batchSize || 1000,
          sortBy: { column: 'created_at', order: 'asc' }
        });

      if (error) {
        throw new StorageError(`Cleanup list failed: ${error.message}`, 'cleanup');
      }

      const filesToDelete = (files || []).filter(file => {
        const fileDate = new Date(file.created_at);
        return fileDate < cutoffDate;
      });

      if (options.dryRun) {
        return {
          filesDeleted: filesToDelete.length,
          spaceFreed: filesToDelete.reduce((sum, file) => sum + (file.metadata?.size || 0), 0),
          errors: [],
          duration: Date.now() - startTime
        };
      }

      // Delete files in batches
      const batchSize = 50;
      for (let i = 0; i < filesToDelete.length; i += batchSize) {
        const batch = filesToDelete.slice(i, i + batchSize);
        const filePaths = batch.map(file => file.name);

        try {
          const { error: deleteError } = await this.supabase.storage
            .from(this.config.bucketName)
            .remove(filePaths);

          if (deleteError) {
            errors.push(`Batch delete failed: ${deleteError.message}`);
          } else {
            filesDeleted += batch.length;
            spaceFreed += batch.reduce((sum, file) => sum + (file.metadata?.size || 0), 0);
          }
        } catch (batchError) {
          errors.push(`Batch processing error: ${(batchError as Error).message}`);
        }
      }

      this.metrics.cleanupCount += filesDeleted;
      this.emitEvent('cleanup_completed', undefined, { filesDeleted, spaceFreed });

      return {
        filesDeleted,
        spaceFreed,
        errors,
        duration: Date.now() - startTime
      };
    } catch (error) {
      this.metrics.errorCount++;
      this.emitEvent('storage_error', undefined, undefined, error as Error);
      
      await errorManager.handleError(error, {
        component: 'AudioStorageManager',
        action: 'cleanupOldFiles'
      });

      return {
        filesDeleted,
        spaceFreed,
        errors: [error instanceof Error ? error.message : String(error)],
        duration: Date.now() - startTime
      };
    }
  }

  /**
   * Get storage metrics
   */
  getMetrics(): StorageMetrics {
    return { ...this.metrics };
  }

  /**
   * Get storage health
   */
  async getStorageHealth(): Promise<StorageHealth> {
    try {
      // Get bucket info (this is a simplified implementation)
      const { data: files, error } = await this.supabase.storage
        .from(this.config.bucketName)
        .list('', { limit: 1 });

      const isHealthy = !error;
      const issues: string[] = [];

      if (error) {
        issues.push(`Storage access error: ${error.message}`);
      }

      if (this.metrics.errorCount > 10) {
        issues.push('High error rate detected');
      }

      return {
        isHealthy,
        availableSpace: 0, // Would need to implement based on storage provider
        usedSpace: this.metrics.totalSize,
        errorRate: this.metrics.errorCount / Math.max(this.metrics.uploadCount + this.metrics.downloadCount, 1),
        lastHealthCheck: new Date(),
        issues
      };
    } catch (error) {
      return {
        isHealthy: false,
        availableSpace: 0,
        usedSpace: 0,
        errorRate: 1,
        lastHealthCheck: new Date(),
        issues: ['Health check failed']
      };
    }
  }

  /**
   * Add event listener
   */
  addEventListener(
    eventType: AudioStorageEventType,
    listener: (event: AudioStorageEvent) => void
  ): void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, new Set());
    }
    this.eventListeners.get(eventType)!.add(listener);
  }

  /**
   * Remove event listener
   */
  removeEventListener(
    eventType: AudioStorageEventType,
    listener: (event: AudioStorageEvent) => void
  ): void {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      listeners.delete(listener);
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
    this.eventListeners.clear();
  }

  /**
   * Initialize storage bucket
   */
  private async initializeBucket(): Promise<void> {
    try {
      const { data: buckets } = await this.supabase.storage.listBuckets();
      const bucketExists = buckets?.some(bucket => bucket.name === this.config.bucketName);

      if (!bucketExists) {
        const { error } = await this.supabase.storage.createBucket(this.config.bucketName, {
          public: false,
          allowedMimeTypes: this.config.allowedMimeTypes,
          fileSizeLimit: this.config.maxFileSize
        });

        if (error) {
          console.error('Error creating audio storage bucket:', error);
        }
      }
    } catch (error) {
      console.error('Error initializing audio storage bucket:', error);
    }
  }

  /**
   * Validate audio file
   */
  private validateAudioFile(audioBlob: Blob, options: AudioUploadOptions): void {
    if (audioBlob.size > (options.maxSize || this.config.maxFileSize)) {
      throw new StorageError(
        `File too large: ${audioBlob.size} bytes (max: ${options.maxSize || this.config.maxFileSize})`,
        'validation'
      );
    }

    const allowedTypes = options.allowedMimeTypes || this.config.allowedMimeTypes;
    if (!allowedTypes.includes(audioBlob.type)) {
      throw new StorageError(
        `Unsupported file type: ${audioBlob.type}`,
        'validation'
      );
    }
  }

  /**
   * Generate file path
   */
  private generateFilePath(filename: string, metadata?: AudioMetadata): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const eventId = metadata?.eventId || 'unknown';
    const extension = this.getFileExtension(filename);
    
    return `events/${eventId}/${timestamp}${extension}`;
  }

  /**
   * Generate file ID
   */
  private generateFileId(): string {
    return `audio_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get file extension
   */
  private getFileExtension(filename: string): string {
    const lastDot = filename.lastIndexOf('.');
    return lastDot > 0 ? filename.substring(lastDot) : '.webm';
  }

  /**
   * Update metrics
   */
  private updateMetrics(operation: 'upload' | 'download', audioFile?: AudioFile): void {
    if (!this.config.enableMetrics) return;

    switch (operation) {
      case 'upload':
        if (audioFile) {
          this.metrics.uploadCount++;
          this.metrics.totalFiles++;
          this.metrics.totalSize += audioFile.size;
          this.metrics.averageFileSize = this.metrics.totalSize / this.metrics.totalFiles;
          
          if (audioFile.metadata?.compressionRatio) {
            this.metrics.compressionSavings += (audioFile.metadata.originalSize as number) - audioFile.size;
          }
        }
        break;
      case 'download':
        this.metrics.downloadCount++;
        break;
    }
  }

  /**
   * Emit event
   */
  private emitEvent(
    type: AudioStorageEventType,
    fileId?: string,
    data?: unknown,
    error?: Error
  ): void {
    const event: AudioStorageEvent = {
      type,
      fileId,
      timestamp: new Date(),
      data,
      error
    };

    const listeners = this.eventListeners.get(type);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(event);
        } catch (error) {
          console.error('Audio storage event listener error:', error);
        }
      });
    }
  }

  /**
   * Start cleanup schedule
   */
  private startCleanupSchedule(): void {
    if (!this.config.enableCleanup) return;

    this.cleanupInterval = setInterval(async () => {
      try {
        await this.cleanupOldFiles();
      } catch (error) {
        console.error('Scheduled cleanup failed:', error);
      }
    }, this.config.cleanupInterval);
  }

  /**
   * Start health checking
   */
  private startHealthChecking(): void {
    this.healthCheckInterval = setInterval(async () => {
      try {
        const health = await this.getStorageHealth();
        if (!health.isHealthy) {
          this.emitEvent('health_check_failed', undefined, health);
        }
      } catch (error) {
        this.emitEvent('health_check_failed', undefined, undefined, error as Error);
      }
    }, 60000); // Check every minute
  }
}