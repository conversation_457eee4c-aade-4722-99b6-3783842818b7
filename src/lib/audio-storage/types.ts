/**
 * Audio storage management types and interfaces
 */

export interface AudioFile {
  id: string;
  path: string;
  originalName?: string;
  mimeType: string;
  size: number;
  duration?: number;
  metadata?: AudioMetadata;
  createdAt: Date;
  updatedAt: Date;
}

export interface AudioMetadata {
  eventId?: string;
  userId?: string;
  transcription?: string;
  confidenceScore?: number;
  processingStatus?: 'pending' | 'processing' | 'completed' | 'failed';
  compressionRatio?: number;
  originalSize?: number;
  [key: string]: unknown;
}

export interface AudioUploadOptions {
  compress?: boolean;
  quality?: 'low' | 'medium' | 'high';
  maxSize?: number;
  allowedMimeTypes?: string[];
  generateThumbnail?: boolean;
  metadata?: AudioMetadata;
}

export interface AudioDownloadOptions {
  signedUrl?: boolean;
  expiresIn?: number;
  download?: boolean;
  filename?: string;
}

export interface AudioCompressionOptions {
  quality: 'low' | 'medium' | 'high';
  format?: 'webm' | 'mp3' | 'ogg';
  bitrate?: number;
  sampleRate?: number;
}

export interface AudioStorageConfig {
  bucketName: string;
  maxFileSize: number;
  allowedMimeTypes: string[];
  defaultCompression: boolean;
  defaultQuality: 'low' | 'medium' | 'high';
  enableCleanup: boolean;
  cleanupInterval: number;
  retentionDays: number;
  enableMetrics: boolean;
}

export interface StorageMetrics {
  totalFiles: number;
  totalSize: number;
  averageFileSize: number;
  compressionSavings: number;
  uploadCount: number;
  downloadCount: number;
  errorCount: number;
  cleanupCount: number;
}

export interface StorageHealth {
  isHealthy: boolean;
  availableSpace: number;
  usedSpace: number;
  errorRate: number;
  lastHealthCheck: Date;
  issues: string[];
}

export interface AudioProcessingResult {
  success: boolean;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  duration?: number;
  format: string;
  error?: string;
}

export interface AudioCleanupOptions {
  olderThan?: Date;
  eventIds?: string[];
  userIds?: string[];
  dryRun?: boolean;
  batchSize?: number;
}

export interface AudioCleanupResult {
  filesDeleted: number;
  spaceFreed: number;
  errors: string[];
  duration: number;
}

export type AudioStorageEventType = 
  | 'file_uploaded'
  | 'file_downloaded'
  | 'file_deleted'
  | 'file_compressed'
  | 'cleanup_completed'
  | 'storage_error'
  | 'health_check_failed';

export interface AudioStorageEvent {
  type: AudioStorageEventType;
  fileId?: string;
  timestamp: Date;
  data?: unknown;
  error?: Error;
}