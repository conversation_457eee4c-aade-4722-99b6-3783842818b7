/**
 * Report Service Integration
 * 
 * Integrates PDF report generation with existing email and export systems.
 * Provides unified interface for generating, scheduling, and delivering reports.
 */

import { pdfReportGenerator } from './pdf-report-generator';
import { reportTemplateRegistry } from './report-templates';
import { chartGenerator } from './chart-generator';
import { getEmailService } from './email-service';
import { tempStickService } from './tempstick-service';

import type {
  TemperatureReportData,
  TemperatureReportParams,
  EmailReportConfig,
  TemperatureReading,
  TemperatureAlert,
  Sensor
} from '../types/tempstick';

import type { 
  PDFGenerationProgress,
  PDFGenerationCallback
} from './pdf-report-generator';

import type {
  ReportTemplateConfig,
  CustomizableTemplate
} from './report-templates';

export interface ReportGenerationRequest {
  templateId: string;
  params: TemperatureReportParams;
  customization?: CustomizableTemplate;
  delivery?: {
    email?: {
      recipients: string[];
      subject?: string;
      includeCharts: boolean;
    };
    download?: {
      filename?: string;
    };
    schedule?: {
      frequency: 'daily' | 'weekly' | 'monthly';
      time: string; // HH:mm format
      enabled: boolean;
    };
  };
  options?: {
    priority: 'low' | 'normal' | 'high';
    compress: boolean;
    watermark?: string;
  };
}

export interface ReportGenerationResult {
  success: boolean;
  reportId: string;
  filename: string;
  fileSizeKB: number;
  generationTimeMs: number;
  pdfBuffer?: Uint8Array;
  downloadUrl?: string;
  emailDeliveryIds?: string[];
  error?: string;
  warnings?: string[];
}

export interface ReportJobStatus {
  id: string;
  status: 'queued' | 'generating' | 'completed' | 'failed' | 'cancelled';
  templateId: string;
  requestedAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  progress?: PDFGenerationProgress;
  result?: ReportGenerationResult;
  error?: string;
  estimatedTimeRemaining?: number;
}

export interface ScheduledReport {
  id: string;
  name: string;
  templateId: string;
  params: TemperatureReportParams;
  schedule: {
    frequency: 'daily' | 'weekly' | 'monthly';
    time: string;
    timezone: string;
    enabled: boolean;
  };
  delivery: {
    email: {
      recipients: string[];
      subject: string;
    };
  };
  lastRunAt?: Date;
  nextRunAt: Date;
  createdAt: Date;
  createdBy: string;
}

/**
 * Integrated Report Service
 */
export class ReportService {
  private static instance: ReportService;
  private reportJobs: Map<string, ReportJobStatus> = new Map();
  private scheduledReports: Map<string, ScheduledReport> = new Map();
  private isProcessingQueue = false;
  private reportQueue: ReportGenerationRequest[] = [];

  constructor() {
    // Initialize scheduled report checking
    this.startScheduleChecker();
  }

  static getInstance(): ReportService {
    if (!ReportService.instance) {
      ReportService.instance = new ReportService();
    }
    return ReportService.instance;
  }

  /**
   * Generate report with full integration
   */
  async generateReport(
    request: ReportGenerationRequest,
    progressCallback?: PDFGenerationCallback
  ): Promise<ReportGenerationResult> {
    const startTime = Date.now();
    const reportId = this.generateReportId();

    try {
      // Validate template and requirements
      const validation = this.validateReportRequest(request);
      if (!validation.valid) {
        throw new Error(`Invalid report request: ${validation.errors.join(', ')}`);
      }

      // Create job status tracking
      const jobStatus: ReportJobStatus = {
        id: reportId,
        status: 'generating',
        templateId: request.templateId,
        requestedAt: new Date(),
        startedAt: new Date()
      };

      this.reportJobs.set(reportId, jobStatus);

      // Collect data for report
      const reportData = await this.collectReportData(request.params);

      // Set up progress tracking
      const trackingCallback: PDFGenerationCallback = (progress) => {
        jobStatus.progress = progress;
        jobStatus.estimatedTimeRemaining = this.calculateTimeRemaining(progress, startTime);
        
        if (progressCallback) {
          progressCallback(progress);
        }
      };

      // Generate PDF
      const pdfBuffer = await pdfReportGenerator.generateReport(
        reportData,
        request.params,
        request.templateId,
        trackingCallback
      );

      const generationTime = Date.now() - startTime;
      const filename = this.generateFilename(request.templateId, request.params);

      // Create result
      const result: ReportGenerationResult = {
        success: true,
        reportId,
        filename,
        fileSizeKB: Math.round(pdfBuffer.byteLength / 1024),
        generationTimeMs: generationTime,
        pdfBuffer,
        warnings: []
      };

      // Handle delivery options
      if (request.delivery) {
        await this.handleReportDelivery(result, request.delivery, reportData);
      }

      // Update job status
      jobStatus.status = 'completed';
      jobStatus.completedAt = new Date();
      jobStatus.result = result;

      return result;

    } catch (error) {
      const jobStatus = this.reportJobs.get(reportId);
      if (jobStatus) {
        jobStatus.status = 'failed';
        jobStatus.error = error instanceof Error ? error.message : 'Unknown error';
        jobStatus.completedAt = new Date();
      }

      return {
        success: false,
        reportId,
        filename: '',
        fileSizeKB: 0,
        generationTimeMs: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Report generation failed'
      };
    }
  }

  /**
   * Queue report for background generation
   */
  async queueReport(request: ReportGenerationRequest): Promise<string> {
    const reportId = this.generateReportId();
    
    const jobStatus: ReportJobStatus = {
      id: reportId,
      status: 'queued',
      templateId: request.templateId,
      requestedAt: new Date()
    };

    this.reportJobs.set(reportId, jobStatus);
    this.reportQueue.push(request);

    // Start processing queue if not already running
    if (!this.isProcessingQueue) {
      this.processReportQueue();
    }

    return reportId;
  }

  /**
   * Get report job status
   */
  getReportStatus(reportId: string): ReportJobStatus | undefined {
    return this.reportJobs.get(reportId);
  }

  /**
   * Cancel queued or generating report
   */
  async cancelReport(reportId: string): Promise<boolean> {
    const jobStatus = this.reportJobs.get(reportId);
    if (!jobStatus) return false;

    if (jobStatus.status === 'queued') {
      // Remove from queue
      const queueIndex = this.reportQueue.findIndex(req => 
        this.reportJobs.get(reportId)?.templateId === req.templateId
      );
      if (queueIndex !== -1) {
        this.reportQueue.splice(queueIndex, 1);
      }
      
      jobStatus.status = 'cancelled';
      jobStatus.completedAt = new Date();
      return true;
    }

    // For generating reports, mark as cancelled but let them complete
    if (jobStatus.status === 'generating') {
      jobStatus.status = 'cancelled';
      return true;
    }

    return false;
  }

  /**
   * Schedule recurring report
   */
  async scheduleReport(config: Omit<ScheduledReport, 'id' | 'createdAt' | 'nextRunAt'>): Promise<string> {
    const id = this.generateScheduleId();
    const scheduledReport: ScheduledReport = {
      ...config,
      id,
      createdAt: new Date(),
      nextRunAt: this.calculateNextRunTime(config.schedule)
    };

    this.scheduledReports.set(id, scheduledReport);
    
    console.log(`📅 Scheduled report: ${config.name} (${id})`);
    return id;
  }

  /**
   * Update scheduled report
   */
  async updateScheduledReport(
    id: string,
    updates: Partial<Omit<ScheduledReport, 'id' | 'createdAt'>>
  ): Promise<boolean> {
    const existing = this.scheduledReports.get(id);
    if (!existing) return false;

    const updated = { ...existing, ...updates };
    
    // Recalculate next run time if schedule changed
    if (updates.schedule) {
      updated.nextRunAt = this.calculateNextRunTime(updates.schedule);
    }

    this.scheduledReports.set(id, updated);
    return true;
  }

  /**
   * Delete scheduled report
   */
  async deleteScheduledReport(id: string): Promise<boolean> {
    return this.scheduledReports.delete(id);
  }

  /**
   * Get all scheduled reports
   */
  getScheduledReports(): ScheduledReport[] {
    return Array.from(this.scheduledReports.values());
  }

  /**
   * Get available templates with metadata
   */
  getAvailableTemplates(): ReportTemplateConfig[] {
    return reportTemplateRegistry.getTemplates();
  }

  /**
   * Validate report requirements
   */
  async validateReportRequirements(
    templateId: string,
    params: TemperatureReportParams
  ): Promise<{
    valid: boolean;
    errors: string[];
    warnings: string[];
    estimatedTime: number;
    estimatedSize: number;
  }> {
    const template = reportTemplateRegistry.getTemplate(templateId);
    if (!template) {
      return {
        valid: false,
        errors: ['Template not found'],
        warnings: [],
        estimatedTime: 0,
        estimatedSize: 0
      };
    }

    // Collect sample data for validation
    const reportData = await this.collectReportData(params);
    
    // Validate template requirements
    const validation = reportTemplateRegistry.validateTemplateRequirements(templateId, reportData);
    const estimate = reportTemplateRegistry.getGenerationEstimate(templateId, reportData);

    const warnings: string[] = [];
    
    // Check data volume warnings
    if (reportData.sensorData.length > 20) {
      warnings.push('Large number of sensors may increase generation time');
    }
    
    const totalReadings = reportData.sensorData.reduce((sum, sensor) => sum + sensor.readings.length, 0);
    if (totalReadings > 10000) {
      warnings.push('Large dataset may require significant processing time');
    }

    return {
      valid: validation.valid,
      errors: validation.missingRequirements,
      warnings,
      estimatedTime: estimate.estimatedTimeSeconds,
      estimatedSize: estimate.estimatedPages
    };
  }

  /**
   * Generate sample data for template preview
   */
  async generateTemplatePreview(templateId: string): Promise<string> {
    // Generate sample data
    const sampleData = this.createSampleReportData();
    const sampleParams: TemperatureReportParams = {
      startDate: new Date(Date.now() - 24 * 60 * 60 * 1000),
      endDate: new Date(),
      includeAlerts: true,
      includeCharts: true,
      includeHACCPData: true,
      format: 'pdf'
    };

    // Generate preview with reduced quality for speed
    return pdfReportGenerator.generateReport(sampleData, sampleParams, templateId);
  }

  // Private methods

  private async processReportQueue(): Promise<void> {
    this.isProcessingQueue = true;

    try {
      while (this.reportQueue.length > 0) {
        const request = this.reportQueue.shift();
        if (!request) continue;

        await this.generateReport(request);
        
        // Small delay between reports to prevent overwhelming the system
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    } finally {
      this.isProcessingQueue = false;
    }
  }

  private async collectReportData(params: TemperatureReportParams): Promise<TemperatureReportData> {
    try {
      // Get temperature readings
      const readings = await tempStickService.getReadingsForDateRange(
        params.startDate,
        params.endDate,
        params.sensorIds
      );

      // Get alerts
      const alerts = params.includeAlerts ? 
        await this.getAlertsForPeriod(params.startDate, params.endDate, params.sensorIds) : 
        [];

      // Group readings by sensor
      const sensorMap = new Map<string, { 
        sensor: Sensor;
        readings: TemperatureReading[];
        alerts: TemperatureAlert[];
        statistics: any;
      }>();

      readings.forEach(reading => {
        if (!sensorMap.has(reading.sensor_id)) {
          sensorMap.set(reading.sensor_id, {
            sensor: reading.sensors!,
            readings: [],
            alerts: [],
            statistics: {
              avgTemp: 0,
              minTemp: Number.MAX_VALUE,
              maxTemp: Number.MIN_VALUE,
              readingsCount: 0,
              alertsCount: 0
            }
          });
        }
        
        const sensorData = sensorMap.get(reading.sensor_id)!;
        sensorData.readings.push(reading);
        
        // Update statistics
        sensorData.statistics.readingsCount++;
        sensorData.statistics.minTemp = Math.min(sensorData.statistics.minTemp, reading.temperature);
        sensorData.statistics.maxTemp = Math.max(sensorData.statistics.maxTemp, reading.temperature);
      });

      // Add alerts to sensors and calculate averages
      alerts.forEach(alert => {
        const sensorData = sensorMap.get(alert.sensor_id);
        if (sensorData) {
          sensorData.alerts.push(alert);
          sensorData.statistics.alertsCount++;
        }
      });

      // Finalize statistics
      sensorMap.forEach(sensorData => {
        if (sensorData.readings.length > 0) {
          sensorData.statistics.avgTemp = 
            sensorData.readings.reduce((sum, r) => sum + r.temperature, 0) / sensorData.readings.length;
        }
      });

      // Get HACCP compliance data if requested
      const haccpCompliance = params.includeHACCPData ? 
        await this.getHACCPComplianceData(params.startDate, params.endDate) : 
        [];

      // Calculate summary
      const totalReadings = readings.length;
      const totalAlerts = alerts.length;
      const totalSensors = sensorMap.size;
      const complianceRate = totalReadings > 0 ? 
        ((totalReadings - totalAlerts) / totalReadings) * 100 : 
        100;

      return {
        summary: {
          reportPeriod: {
            start: params.startDate.toISOString(),
            end: params.endDate.toISOString()
          },
          sensorsIncluded: totalSensors,
          totalReadings,
          totalAlerts,
          complianceRate
        },
        sensorData: Array.from(sensorMap.values()),
        alerts,
        haccpCompliance
      };
    } catch (error) {
      console.error('Failed to collect report data:', error);
      throw new Error('Failed to collect data for report generation');
    }
  }

  private async getAlertsForPeriod(
    startDate: Date,
    endDate: Date,
    sensorIds?: string[]
  ): Promise<TemperatureAlert[]> {
    // Implementation would query alerts from database
    // For now, return empty array as placeholder
    return [];
  }

  private async getHACCPComplianceData(startDate: Date, endDate: Date): Promise<any[]> {
    // Implementation would calculate HACCP compliance metrics
    // For now, return empty array as placeholder
    return [];
  }

  private async handleReportDelivery(
    result: ReportGenerationResult,
    delivery: ReportGenerationRequest['delivery'],
    reportData: TemperatureReportData
  ): Promise<void> {
    const promises: Promise<void>[] = [];

    // Email delivery
    if (delivery?.email) {
      promises.push(this.sendReportByEmail(result, delivery.email, reportData));
    }

    // Download URL generation
    if (delivery?.download) {
      result.downloadUrl = await this.createDownloadUrl(result.pdfBuffer!, result.filename);
    }

    await Promise.all(promises);
  }

  private async sendReportByEmail(
    result: ReportGenerationResult,
    emailConfig: NonNullable<ReportGenerationRequest['delivery']>['email'],
    reportData: TemperatureReportData
  ): Promise<void> {
    const emailService = getEmailService();
    
    const reportConfig: EmailReportConfig = {
      frequency: 'daily', // Default, would be configurable
      recipients: emailConfig!.recipients,
      sensorIds: [],
      alertsOnly: false,
      includePDF: true,
      includeExcel: false,
      timeOfDay: '08:00'
    };

    await emailService.sendScheduledReport(reportData, reportConfig);
    
    console.log(`📧 Report emailed to ${emailConfig!.recipients.length} recipients`);
  }

  private async createDownloadUrl(pdfBuffer: Uint8Array, filename: string): Promise<string> {
    // In production, would upload to cloud storage and return URL
    // For now, create blob URL
    const blob = new Blob([pdfBuffer], { type: 'application/pdf' });
    return URL.createObjectURL(blob);
  }

  private validateReportRequest(request: ReportGenerationRequest): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Validate template
    const template = reportTemplateRegistry.getTemplate(request.templateId);
    if (!template) {
      errors.push('Invalid template ID');
    }

    // Validate date range
    if (request.params.startDate >= request.params.endDate) {
      errors.push('Start date must be before end date');
    }

    // Validate date range is not too large
    const daysDiff = Math.abs(request.params.endDate.getTime() - request.params.startDate.getTime()) / (1000 * 60 * 60 * 24);
    if (daysDiff > 365) {
      errors.push('Date range cannot exceed 1 year');
    }

    // Validate email addresses if email delivery requested
    if (request.delivery?.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      const invalidEmails = request.delivery.email.recipients.filter(email => !emailRegex.test(email));
      if (invalidEmails.length > 0) {
        errors.push(`Invalid email addresses: ${invalidEmails.join(', ')}`);
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  private startScheduleChecker(): void {
    // Check for scheduled reports every minute
    setInterval(() => {
      this.checkScheduledReports();
    }, 60000);
  }

  private async checkScheduledReports(): Promise<void> {
    const now = new Date();
    
    for (const [id, scheduledReport] of this.scheduledReports.entries()) {
      if (!scheduledReport.schedule.enabled) continue;
      
      if (now >= scheduledReport.nextRunAt) {
        try {
          console.log(`🕐 Executing scheduled report: ${scheduledReport.name}`);
          
          const request: ReportGenerationRequest = {
            templateId: scheduledReport.templateId,
            params: scheduledReport.params,
            delivery: {
              email: {
                recipients: scheduledReport.delivery.email.recipients,
                subject: scheduledReport.delivery.email.subject,
                includeCharts: true
              }
            },
            options: {
              priority: 'normal',
              compress: true
            }
          };

          await this.queueReport(request);

          // Update last run and next run times
          scheduledReport.lastRunAt = now;
          scheduledReport.nextRunAt = this.calculateNextRunTime(scheduledReport.schedule);
          
          console.log(`✅ Scheduled report queued: ${scheduledReport.name}`);
        } catch (error) {
          console.error(`❌ Failed to execute scheduled report ${scheduledReport.name}:`, error);
        }
      }
    }
  }

  private calculateNextRunTime(schedule: ScheduledReport['schedule']): Date {
    const now = new Date();
    const [hour, minute] = schedule.time.split(':').map(Number);
    
    const nextRun = new Date(now);
    nextRun.setHours(hour, minute, 0, 0);

    switch (schedule.frequency) {
      case 'daily':
        if (nextRun <= now) {
          nextRun.setDate(nextRun.getDate() + 1);
        }
        break;
      
      case 'weekly':
        // Run on the same day of week
        if (nextRun <= now) {
          nextRun.setDate(nextRun.getDate() + 7);
        }
        break;
      
      case 'monthly':
        // Run on the same day of month
        if (nextRun <= now) {
          nextRun.setMonth(nextRun.getMonth() + 1);
        }
        break;
    }

    return nextRun;
  }

  private calculateTimeRemaining(progress: PDFGenerationProgress, startTime: number): number {
    if (progress.progress === 0) return 0;
    
    const elapsed = Date.now() - startTime;
    const estimatedTotal = (elapsed / progress.progress) * 100;
    return Math.max(0, estimatedTotal - elapsed);
  }

  private generateReportId(): string {
    return `rpt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateScheduleId(): string {
    return `sch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateFilename(templateId: string, params: TemperatureReportParams): string {
    const template = reportTemplateRegistry.getTemplate(templateId);
    const templateName = template?.name.replace(/\s+/g, '_') || templateId;
    const startDate = params.startDate.toISOString().split('T')[0];
    const endDate = params.endDate.toISOString().split('T')[0];
    
    if (startDate === endDate) {
      return `${templateName}_${startDate}.pdf`;
    } else {
      return `${templateName}_${startDate}_to_${endDate}.pdf`;
    }
  }

  private createSampleReportData(): TemperatureReportData {
    // Generate sample data for previews
    const now = new Date();
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    const sampleReadings: TemperatureReading[] = [];
    const sampleAlerts: TemperatureAlert[] = [];

    // Generate sample readings
    for (let i = 0; i < 144; i++) { // 24 hours * 6 readings per hour
      const timestamp = new Date(yesterday.getTime() + i * 10 * 60 * 1000); // Every 10 minutes
      sampleReadings.push({
        id: `reading_${i}`,
        sensor_id: 'sample_sensor_1',
        temperature: 38 + Math.random() * 4, // 38-42°F
        humidity: 65 + Math.random() * 10, // 65-75%
        reading_timestamp: timestamp.toISOString(),
        alert_triggered: Math.random() < 0.05, // 5% chance of alert
        created_at: timestamp.toISOString()
      });
    }

    return {
      summary: {
        reportPeriod: {
          start: yesterday.toISOString(),
          end: now.toISOString()
        },
        sensorsIncluded: 1,
        totalReadings: sampleReadings.length,
        totalAlerts: sampleAlerts.length,
        complianceRate: 95.0
      },
      sensorData: [{
        sensor: {
          id: 'sample_sensor_1',
          tempstick_sensor_id: 'ts_001',
          name: 'Walk-in Cooler #1',
          location: 'Main Storage',
          sensor_type: 'temperature_humidity',
          temp_min_threshold: 35,
          temp_max_threshold: 42,
          storage_area_id: 'storage_1',
          active: true,
          created_at: yesterday.toISOString()
        },
        readings: sampleReadings,
        alerts: sampleAlerts,
        statistics: {
          avgTemp: 40.0,
          minTemp: 38.2,
          maxTemp: 41.8,
          readingsCount: sampleReadings.length,
          alertsCount: sampleAlerts.length
        }
      }],
      alerts: sampleAlerts,
      haccpCompliance: [{
        storageArea: {
          id: 'storage_1',
          name: 'Main Storage',
          area_type: 'refrigerator',
          required_temp_min: 35,
          required_temp_max: 42,
          haccp_control_point: true,
          created_at: yesterday.toISOString()
        },
        violationsCount: 0,
        complianceRate: 100.0,
        criticalViolations: []
      }]
    };
  }
}

// Export singleton instance
export const reportService = ReportService.getInstance();

export type {
  ReportGenerationRequest,
  ReportGenerationResult,
  ReportJobStatus,
  ScheduledReport
};