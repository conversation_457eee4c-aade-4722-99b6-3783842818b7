import { supabase } from './supabase';

export const EVENT_TYPES = ['received', 'disposal', 'physical_count', 'sale'] as const;
export type EventType = typeof EVENT_TYPES[number];

export const setupDatabase = async () => {
  try {
    console.log('Initializing database setup...');
    
    // First, verify basic database connection
    const { error: connectionError } = await supabase
      .from('categories')
      .select('count');

    if (connectionError) {
      console.error('Database connection error:', connectionError);
      if (connectionError.message.includes('does not exist')) {
        throw new Error('Database tables are not set up. Please run the migrations first.');
      }
      throw new Error(`Database connection failed: ${connectionError.message}`);
    }

    // Verify categories exist using a limited select
    const { error: fetchError } = await supabase
      .from('categories')
      .select('name', { count: 'exact', head: true });

    if (fetchError) {
      console.error('Category fetch error:', fetchError);
      throw new Error(`Failed to fetch categories: ${fetchError.message}`);
    }

    // Verify all required categories exist
    const { data: categories, error: verifyError } = await supabase
      .from('categories')
      .select('id, name')
      .in('name', ['Receiving', 'Disposal', 'Physical Count', 'Re-processing']);

    if (verifyError) {
      console.error('Category verification error:', verifyError);
      throw new Error(`Failed to verify categories: ${verifyError.message}`);
    }

    if (!categories || categories.length < 4) {
      const missing = ['Receiving', 'Disposal', 'Physical Count', 'Re-processing']
        .filter(name => !categories?.some(cat => cat.name === name))
        .join(', ');
      console.error('Missing categories:', missing);
      throw new Error(`Missing required categories: ${missing}. Please run the database migrations or seed script server-side.`);
    }

    // Verify products table exists (surface clearer error than a generic 404 later)
    const { error: productsError } = await supabase
      .from('Products')
      .select('id', { head: true, count: 'exact' });

    if (productsError) {
      console.error('Products table check error:', productsError);
      throw new Error(
        "'Products' table is missing or not exposed to PostgREST. Run your Supabase migrations (e.g., migrations/001_create_tables.sql) on the remote project, or create the table via SQL. Ensure the table name matches casing."
      );
    }

    // Verify product_categories table exists (optional, used by some UIs)
    const { error: productCategoriesError } = await supabase
      .from('product_categories')
      .select('id', { head: true, count: 'exact' });

    if (productCategoriesError) {
      console.warn('Product categories table check warning:', productCategoriesError);
      // Do not block app startup; some flows may not require product_categories immediately
    }

    console.log('Database setup completed successfully');
    return {
      success: true,
      categories,
      eventTypes: EVENT_TYPES
    };
  } catch (error) {
    if (error instanceof Error) {
      console.error('Database setup error:', error.message);
      throw error;
    }
    console.error('Unknown database setup error:', error);
    throw new Error('Failed to set up database. Please check your configuration.');
  }
};

export const getCategories = async () => {
  const { data, error } = await supabase
    .from('categories')
    .select('id, name')
    .order('name', { ascending: true });

  if (error) {
    console.error('Error fetching categories:', error);
  }
  return { data, error };
};

export const getProductCategories = async () => {
  const { data, error } = await supabase
    .from('product_categories')
    .select('id, name, description')
    .order('name', { ascending: true });

  if (error) {
    // If table isn't present yet on remote, avoid hard-failing UI; log and return empty list
    console.warn('Error fetching product categories:', error);
    return { data: [], error };
  }

  // Transform data to match the expected format
  const transformedData = data?.map(cat => ({
    value: cat.id,
    label: cat.name,
    description: cat.description
  }));

  return { data: transformedData, error: null };
};
