/**
 * GDST (Global Dialogue on Seafood Traceability) Export System
 * Generates compliant export formats for regulatory reporting and traceability
 * Supports multiple export formats and compliance standards
 */

import Papa from 'papaparse';
import * as XLSX from 'xlsx';

export type ExportFormat = 'gdst_json' | 'gdst_csv' | 'haccp_report' | 'traceability_report' | 
                          'regulatory_export' | 'audit_trail' | 'inventory_report' | 'sales_report';

export type ComplianceStandard = 'GDST_1_0' | 'GDST_2_0' | 'HACCP' | 'FDA_FSMA' | 'EU_REGULATION' | 'NOAA_SEAFOOD';

export interface ExportOptions {
  format: ExportFormat;
  standard?: ComplianceStandard;
  dateRange?: {
    startDate: string;
    endDate: string;
  };
  includeMetadata?: boolean;
  includeValidation?: boolean;
  compressionLevel?: 'none' | 'standard' | 'high';
  encryptionKey?: string;
  customFields?: Record<string, unknown>;
  template?: string;
}

export interface GDSTExportData {
  kdes: GDSTKeyDataElement[];
  events: GDSTTraceabilityEvent[];
  master_data: GDSTMasterData;
  metadata: ExportMetadata;
}

export interface GDSTKeyDataElement {
  eventId: string;
  eventType: 'transformation' | 'aggregation' | 'transaction' | 'object';
  eventTime: string;
  bizLocation: string;
  bizStep: string;
  disposition: string;
  readPoint?: string;
  bizTransactionList?: Array<{
    type: string;
    bizTransaction: string;
  }>;
  quantityList?: Array<{
    epcClass: string;
    quantity: number;
    uom: string;
  }>;
  extensions?: Record<string, unknown>;
}

export interface GDSTTraceabilityEvent {
  eventId: string;
  parentId?: string;
  eventTime: string;
  species: {
    scientificName: string;
    commonName: string;
    asfisCode?: string;
  };
  fishingGear?: string;
  vesselId?: string;
  catchArea?: {
    faoMajorFishingArea?: string;
    economicZone?: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  landingLocation?: string;
  certificationList?: Array<{
    certificationType: string;
    certificationStandard: string;
    certificationAgency: string;
    certificationValue: string;
    certificationIdentification: string;
  }>;
  productionMethodForFishAndSeafoodCode?: string;
  harvestStartDate?: string;
  harvestEndDate?: string;
  vesselFlagState?: string;
  fishingGearTypeCode?: string;
  unloadingPort?: string;
  landingDate?: string;
}

export interface GDSTMasterData {
  locations: Array<{
    id: string;
    name: string;
    address?: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  }>;
  parties: Array<{
    id: string;
    name: string;
    type: 'processor' | 'distributor' | 'retailer' | 'restaurant' | 'vessel_owner';
    contact?: {
      email?: string;
      phone?: string;
      address?: string;
    };
  }>;
  products: Array<{
    id: string;
    gtin?: string;
    productName: string;
    species: {
      scientificName: string;
      commonName: string;
      asfisCode?: string;
    };
    packaging?: string;
    weightUnit?: string;
  }>;
}

export interface ExportMetadata {
  exportId: string;
  exportTime: string;
  exportedBy: string;
  complianceStandard: ComplianceStandard;
  recordCount: number;
  dateRange: {
    startDate: string;
    endDate: string;
  };
  version: string;
  checksum?: string;
}

export interface HACCPReportData {
  facilityInfo: {
    name: string;
    address: string;
    permitNumber?: string;
    lastInspection?: string;
  };
  criticalControlPoints: Array<{
    ccp: string;
    criticalLimit: string;
    monitoringProcedure: string;
    frequency: string;
    records: Array<{
      date: string;
      time: string;
      value: number;
      unit: string;
      withinLimits: boolean;
      correctionAction?: string;
    }>;
  }>;
  temperatureRecords: Array<{
    location: string;
    date: string;
    time: string;
    temperature: number;
    unit: string;
    withinLimits: boolean;
  }>;
  supplierVerification: Array<{
    supplier: string;
    product: string;
    certificationType: string;
    verificationDate: string;
    status: 'approved' | 'pending' | 'rejected';
  }>;
}

export class GDSTExporter {
  private static readonly GDST_NAMESPACES = {
    'gdst': 'https://traceability-dialogue.org/gdst',
    'epcis': 'urn:epcglobal:epcis:xsd:1',
    'cbv': 'urn:epcglobal:epcis-cb:xsd:1'
  };

  private static readonly ASFIS_SPECIES_CODES = {
    'chinook_salmon': 'SAL',
    'coho_salmon': 'COH', 
    'sockeye_salmon': 'SOC',
    'dungeness_crab': 'DNC',
    'king_crab': 'KIC',
    'pacific_halibut': 'HAL'
  };

  private static readonly FAO_FISHING_AREAS = {
    'pacific_northwest': '67',
    'alaska': '61',
    'california': '77',
    'bering_sea': '61'
  };

  /**
   * Export data in GDST-compliant format
   */
  static async exportGDST(
    inventoryData: Record<string, unknown>[],
    eventData: Record<string, unknown>[],
    options: ExportOptions
  ): Promise<string | Blob> {
    try {
      const exportData = this.transformToGDST(inventoryData, eventData, options);
      
      switch (options.format) {
        case 'gdst_json':
          return this.exportAsGDSTJson(exportData, options);
        case 'gdst_csv':
          return this.exportAsGDSTCsv(exportData, options);
        case 'traceability_report':
          return this.exportTraceabilityReport(exportData, options);
        default:
          throw new Error(`Unsupported GDST export format: ${options.format}`);
      }
    } catch (error) {
      throw new Error(`GDST export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Export HACCP compliance report
   */
  static async exportHACCP(
    temperatureRecords: Record<string, unknown>[],
    supplierData: Record<string, unknown>[],
    options: ExportOptions
  ): Promise<string | Blob> {
    try {
      const haccpData = this.transformToHACCP(temperatureRecords, supplierData, options);
      
      switch (options.format) {
        case 'haccp_report':
          return this.exportHACCPReport(haccpData, options);
        default:
          throw new Error(`Unsupported HACCP export format: ${options.format}`);
      }
    } catch (error) {
      throw new Error(`HACCP export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Export regulatory compliance report
   */
  static async exportRegulatory(
    data: Record<string, unknown>[],
    options: ExportOptions
  ): Promise<string | Blob> {
    try {
      switch (options.standard) {
        case 'FDA_FSMA':
          return this.exportFDAFSMA(data, options);
        case 'EU_REGULATION':
          return this.exportEURegulation(data, options);
        case 'NOAA_SEAFOOD':
          return this.exportNOAASeafood(data, options);
        default:
          return this.exportGenericRegulatory(data, options);
      }
    } catch (error) {
      throw new Error(`Regulatory export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Transform inventory data to GDST format
   */
  private static transformToGDST(
    inventoryData: Record<string, unknown>[],
    eventData: Record<string, unknown>[],
    options: ExportOptions
  ): GDSTExportData {
    const exportId = this.generateExportId();
    const exportTime = new Date().toISOString();

    // Transform inventory data to GDST KDEs
    const kdes: GDSTKeyDataElement[] = inventoryData.map((item, index) => ({
      eventId: `urn:gdst:event:${exportId}:${index + 1}`,
      eventType: 'object',
      eventTime: this.formatGDSTDate(item.date as string || exportTime),
      bizLocation: this.formatLocation(item.location as string || 'Unknown'),
      bizStep: this.mapToBizStep(item.event_type as string || 'inventory'),
      disposition: this.mapToDisposition(item.status as string || 'active'),
      quantityList: [{
        epcClass: this.generateEPCClass(item),
        quantity: Number(item.quantity) || 0,
        uom: this.mapToUOM(item.unit as string || 'KGM')
      }],
      extensions: {
        'gdst:productionMethodForFishAndSeafoodCode': this.mapProductionMethod(item.origin as string),
        'gdst:speciesForFishAndSeafoodCode': this.mapSpeciesCode(item.name as string),
        'gdst:catchArea': this.mapCatchArea(item.origin as string),
        'gdst:vesselId': item.vessel_id as string,
        'gdst:harvestDate': this.formatGDSTDate(item.catch_date as string)
      }
    }));

    // Transform event data to GDST traceability events
    const events: GDSTTraceabilityEvent[] = eventData.map(event => ({
      eventId: `urn:gdst:trace:${exportId}:${event.id}`,
      eventTime: this.formatGDSTDate(event.created_at as string || exportTime),
      species: {
        scientificName: this.getScientificName(event.name as string),
        commonName: event.name as string || '',
        asfisCode: this.getASFISCode(event.name as string)
      },
      fishingGear: event.fishing_gear as string,
      vesselId: event.vessel_id as string,
      catchArea: {
        faoMajorFishingArea: this.getFAOArea(event.origin as string),
        economicZone: event.economic_zone as string,
        coordinates: this.parseCoordinates(event.coordinates as string)
      },
      landingLocation: event.landing_location as string,
      harvestStartDate: this.formatGDSTDate(event.harvest_start_date as string),
      harvestEndDate: this.formatGDSTDate(event.harvest_end_date as string),
      vesselFlagState: event.flag_state as string || 'US',
      productionMethodForFishAndSeafoodCode: this.mapProductionMethod(event.production_method as string)
    }));

    // Generate master data
    const masterData: GDSTMasterData = {
      locations: this.extractLocations(inventoryData),
      parties: this.extractParties(inventoryData),
      products: this.extractProducts(inventoryData)
    };

    // Generate metadata
    const metadata: ExportMetadata = {
      exportId,
      exportTime,
      exportedBy: 'Pacific Cloud Seafoods Manager',
      complianceStandard: options.standard || 'GDST_2_0',
      recordCount: inventoryData.length + eventData.length,
      dateRange: options.dateRange || {
        startDate: this.getEarliest(inventoryData, 'date'),
        endDate: this.getLatest(inventoryData, 'date')
      },
      version: '2.0.0'
    };

    return {
      kdes,
      events,
      master_data: masterData,
      metadata
    };
  }

  /**
   * Export as GDST JSON format
   */
  private static async exportAsGDSTJson(
    data: GDSTExportData,
    options: ExportOptions
  ): Promise<string> {
    const jsonData = {
      '@context': {
        ...this.GDST_NAMESPACES,
        'gdst': 'https://traceability-dialogue.org/gdst/1.0/'
      },
      type: 'EPCISDocument',
      schemaVersion: '2.0',
      creationDate: data.metadata.exportTime,
      epcisBody: {
        eventList: data.kdes.map(kde => ({
          ...kde,
          type: 'ObjectEvent',
          action: 'OBSERVE'
        }))
      },
      traceabilityEvents: data.events,
      masterData: data.master_data,
      metadata: data.metadata
    };

    if (options.includeValidation) {
      jsonData.metadata.checksum = this.calculateChecksum(JSON.stringify(jsonData));
    }

    return JSON.stringify(jsonData, null, options.includeMetadata ? 2 : 0);
  }

  /**
   * Export as GDST CSV format
   */
  private static async exportAsGDSTCsv(
    data: GDSTExportData,
    options: ExportOptions
  ): Promise<string> {
    // Flatten GDST data for CSV export
    const flattenedData = data.kdes.map(kde => ({
      eventId: kde.eventId,
      eventType: kde.eventType,
      eventTime: kde.eventTime,
      bizLocation: kde.bizLocation,
      bizStep: kde.bizStep,
      disposition: kde.disposition,
      epcClass: kde.quantityList?.[0]?.epcClass || '',
      quantity: kde.quantityList?.[0]?.quantity || 0,
      uom: kde.quantityList?.[0]?.uom || '',
      speciesCode: kde.extensions?.['gdst:speciesForFishAndSeafoodCode'] as string || '',
      productionMethod: kde.extensions?.['gdst:productionMethodForFishAndSeafoodCode'] as string || '',
      catchArea: kde.extensions?.['gdst:catchArea'] as string || '',
      vesselId: kde.extensions?.['gdst:vesselId'] as string || '',
      harvestDate: kde.extensions?.['gdst:harvestDate'] as string || ''
    }));

    return Papa.unparse(flattenedData, {
      header: true,
      quotes: true
    });
  }

  /**
   * Export traceability report
   */
  private static async exportTraceabilityReport(
    data: GDSTExportData,
    options: ExportOptions
  ): Promise<Blob> {
    // Create Excel workbook with multiple sheets
    const workbook = XLSX.utils.book_new();

    // Summary sheet
    const summaryData = [
      ['Traceability Report'],
      ['Export Date', data.metadata.exportTime],
      ['Compliance Standard', data.metadata.complianceStandard],
      ['Total Records', data.metadata.recordCount],
      ['Date Range', `${data.metadata.dateRange.startDate} to ${data.metadata.dateRange.endDate}`],
      [],
      ['Summary Statistics'],
      ['Total Products', data.master_data.products.length],
      ['Total Locations', data.master_data.locations.length],
      ['Total Parties', data.master_data.parties.length],
      ['Total Events', data.kdes.length]
    ];
    
    const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
    XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');

    // Events sheet
    const eventsData = data.kdes.map(kde => ({
      'Event ID': kde.eventId,
      'Event Type': kde.eventType,
      'Event Time': kde.eventTime,
      'Location': kde.bizLocation,
      'Business Step': kde.bizStep,
      'Disposition': kde.disposition,
      'Product Class': kde.quantityList?.[0]?.epcClass || '',
      'Quantity': kde.quantityList?.[0]?.quantity || 0,
      'Unit': kde.quantityList?.[0]?.uom || '',
      'Species Code': kde.extensions?.['gdst:speciesForFishAndSeafoodCode'] as string || '',
      'Catch Area': kde.extensions?.['gdst:catchArea'] as string || '',
      'Vessel ID': kde.extensions?.['gdst:vesselId'] as string || ''
    }));
    
    const eventsSheet = XLSX.utils.json_to_sheet(eventsData);
    XLSX.utils.book_append_sheet(workbook, eventsSheet, 'Events');

    // Products sheet
    const productsData = data.master_data.products.map(product => ({
      'Product ID': product.id,
      'Product Name': product.productName,
      'Scientific Name': product.species.scientificName,
      'Common Name': product.species.commonName,
      'ASFIS Code': product.species.asfisCode || '',
      'GTIN': product.gtin || '',
      'Packaging': product.packaging || '',
      'Weight Unit': product.weightUnit || ''
    }));
    
    const productsSheet = XLSX.utils.json_to_sheet(productsData);
    XLSX.utils.book_append_sheet(workbook, productsSheet, 'Products');

    // Convert to binary
    const wbBinary = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    return new Blob([wbBinary], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
  }

  /**
   * Transform temperature records to HACCP format
   */
  private static transformToHACCP(
    temperatureRecords: Record<string, unknown>[],
    supplierData: Record<string, unknown>[],
    options: ExportOptions
  ): HACCPReportData {
    return {
      facilityInfo: {
        name: 'Pacific Cloud Seafoods',
        address: options.customFields?.facilityAddress as string || '',
        permitNumber: options.customFields?.permitNumber as string,
        lastInspection: options.customFields?.lastInspection as string
      },
      criticalControlPoints: [
        {
          ccp: 'Receiving Temperature',
          criticalLimit: '≤ 40°F (4.4°C)',
          monitoringProcedure: 'Digital thermometer reading',
          frequency: 'Every shipment',
          records: temperatureRecords.map(record => ({
            date: this.formatDate(record.date as string),
            time: this.formatTime(record.time as string || record.created_at as string),
            value: Number(record.temperature) || 0,
            unit: record.unit as string || '°F',
            withinLimits: Number(record.temperature) <= 40,
            correctionAction: Number(record.temperature) > 40 ? record.correction_action as string || 'Product rejected' : undefined
          }))
        }
      ],
      temperatureRecords: temperatureRecords.map(record => ({
        location: record.location as string || 'Storage',
        date: this.formatDate(record.date as string),
        time: this.formatTime(record.time as string || record.created_at as string),
        temperature: Number(record.temperature) || 0,
        unit: record.unit as string || '°F',
        withinLimits: Number(record.temperature) <= 40
      })),
      supplierVerification: supplierData.map(supplier => ({
        supplier: supplier.name as string || '',
        product: supplier.product as string || '',
        certificationType: supplier.certification_type as string || 'HACCP',
        verificationDate: this.formatDate(supplier.verification_date as string),
        status: supplier.status as 'approved' | 'pending' | 'rejected' || 'pending'
      }))
    };
  }

  /**
   * Export HACCP report
   */
  private static async exportHACCPReport(
    data: HACCPReportData,
    options: ExportOptions
  ): Promise<Blob> {
    const workbook = XLSX.utils.book_new();

    // Facility Information sheet
    const facilityData = [
      ['HACCP Compliance Report'],
      ['Facility Name', data.facilityInfo.name],
      ['Address', data.facilityInfo.address],
      ['Permit Number', data.facilityInfo.permitNumber || ''],
      ['Last Inspection', data.facilityInfo.lastInspection || ''],
      ['Report Generated', new Date().toISOString()]
    ];
    
    const facilitySheet = XLSX.utils.aoa_to_sheet(facilityData);
    XLSX.utils.book_append_sheet(workbook, facilitySheet, 'Facility Info');

    // Temperature Records sheet
    const tempRecords = data.temperatureRecords.map(record => ({
      'Location': record.location,
      'Date': record.date,
      'Time': record.time,
      'Temperature': record.temperature,
      'Unit': record.unit,
      'Within Limits': record.withinLimits ? 'Yes' : 'No'
    }));
    
    const tempSheet = XLSX.utils.json_to_sheet(tempRecords);
    XLSX.utils.book_append_sheet(workbook, tempSheet, 'Temperature Records');

    // Supplier Verification sheet
    const supplierRecords = data.supplierVerification.map(record => ({
      'Supplier': record.supplier,
      'Product': record.product,
      'Certification Type': record.certificationType,
      'Verification Date': record.verificationDate,
      'Status': record.status
    }));
    
    const supplierSheet = XLSX.utils.json_to_sheet(supplierRecords);
    XLSX.utils.book_append_sheet(workbook, supplierSheet, 'Supplier Verification');

    const wbBinary = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    return new Blob([wbBinary], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
  }

  // Utility methods for GDST transformation
  private static generateExportId(): string {
    return `gdst-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private static formatGDSTDate(date: string | undefined): string {
    if (!date) return new Date().toISOString();
    try {
      return new Date(date).toISOString();
    } catch {
      return new Date().toISOString();
    }
  }

  private static formatDate(date: string): string {
    try {
      return new Date(date).toLocaleDateString();
    } catch {
      return '';
    }
  }

  private static formatTime(time: string): string {
    try {
      return new Date(time).toLocaleTimeString();
    } catch {
      return '';
    }
  }

  private static formatLocation(location: string): string {
    return `urn:epc:id:sgln:${location.replace(/\s+/g, '')}`;
  }

  private static mapToBizStep(eventType: string): string {
    const mapping: Record<string, string> = {
      'receiving': 'urn:epcglobal:cbv:bizstep:receiving',
      'sales': 'urn:epcglobal:cbv:bizstep:retail_selling',
      'disposal': 'urn:epcglobal:cbv:bizstep:destroying',
      'production': 'urn:epcglobal:cbv:bizstep:transforming',
      'adjustment': 'urn:epcglobal:cbv:bizstep:stock_taking'
    };
    return mapping[eventType] || 'urn:epcglobal:cbv:bizstep:other';
  }

  private static mapToDisposition(status: string): string {
    const mapping: Record<string, string> = {
      'active': 'urn:epcglobal:cbv:disp:in_progress',
      'sold': 'urn:epcglobal:cbv:disp:retail_sold',
      'disposed': 'urn:epcglobal:cbv:disp:destroyed',
      'expired': 'urn:epcglobal:cbv:disp:expired'
    };
    return mapping[status] || 'urn:epcglobal:cbv:disp:unknown';
  }

  private static mapToUOM(unit: string): string {
    const mapping: Record<string, string> = {
      'lbs': 'LBR',
      'pounds': 'LBR', 
      'kg': 'KGM',
      'kilograms': 'KGM',
      'oz': 'ONZ',
      'tons': 'TNE',
      'pieces': 'C62',
      'each': 'C62'
    };
    return mapping[unit.toLowerCase()] || 'KGM';
  }

  private static generateEPCClass(item: Record<string, unknown>): string {
    const sku = item.sku as string || 'unknown';
    return `urn:epc:class:lgtin:${sku.replace(/\s+/g, '')}`;
  }

  private static mapProductionMethod(origin: string | undefined): string {
    if (!origin) return '';
    const originLower = origin.toLowerCase();
    if (originLower.includes('wild') || originLower.includes('caught')) return 'MARINE_FISHERY';
    if (originLower.includes('farm') || originLower.includes('aquaculture')) return 'AQUACULTURE';
    return 'MARINE_FISHERY'; // Default
  }

  private static mapSpeciesCode(name: string | undefined): string {
    if (!name) return '';
    const nameLower = name.toLowerCase();
    for (const [species, code] of Object.entries(this.ASFIS_SPECIES_CODES)) {
      if (nameLower.includes(species.replace('_', ' '))) return code;
    }
    return '';
  }

  private static mapCatchArea(origin: string | undefined): string {
    if (!origin) return '';
    const originLower = origin.toLowerCase();
    for (const [area, faoCode] of Object.entries(this.FAO_FISHING_AREAS)) {
      if (originLower.includes(area.replace('_', ' '))) return faoCode;
    }
    return '';
  }

  private static getScientificName(commonName: string): string {
    // This would typically be a database lookup
    const mapping: Record<string, string> = {
      'chinook salmon': 'Oncorhynchus tshawytscha',
      'coho salmon': 'Oncorhynchus kisutch',
      'sockeye salmon': 'Oncorhynchus nerka',
      'dungeness crab': 'Metacarcinus magister',
      'king crab': 'Paralithodes camtschaticus',
      'pacific halibut': 'Hippoglossus stenolepis'
    };
    
    return mapping[commonName?.toLowerCase()] || commonName;
  }

  private static getASFISCode(name: string): string | undefined {
    return this.mapSpeciesCode(name) || undefined;
  }

  private static getFAOArea(origin: string | undefined): string | undefined {
    return this.mapCatchArea(origin) || undefined;
  }

  private static parseCoordinates(coords: string | undefined): { latitude: number; longitude: number } | undefined {
    if (!coords) return undefined;
    try {
      const parts = coords.split(',');
      if (parts.length === 2) {
        return {
          latitude: parseFloat(parts[0].trim()),
          longitude: parseFloat(parts[1].trim())
        };
      }
    } catch {
      // Ignore parsing errors
    }
    return undefined;
  }

  private static extractLocations(data: Record<string, unknown>[]): GDSTMasterData['locations'] {
    const locations = new Set<string>();
    data.forEach(item => {
      if (item.location) locations.add(item.location as string);
      if (item.origin) locations.add(item.origin as string);
    });
    
    return Array.from(locations).map((location, index) => ({
      id: `loc_${index + 1}`,
      name: location
    }));
  }

  private static extractParties(data: Record<string, unknown>[]): GDSTMasterData['parties'] {
    const parties = new Set<string>();
    data.forEach(item => {
      if (item.supplier) parties.add(item.supplier as string);
      if (item.vendor) parties.add(item.vendor as string);
    });
    
    return Array.from(parties).map((party, index) => ({
      id: `party_${index + 1}`,
      name: party,
      type: 'processor' as const
    }));
  }

  private static extractProducts(data: Record<string, unknown>[]): GDSTMasterData['products'] {
    return data.map((item, index) => ({
      id: item.sku as string || `prod_${index + 1}`,
      gtin: item.gtin as string,
      productName: item.name as string || '',
      species: {
        scientificName: this.getScientificName(item.name as string),
        commonName: item.name as string || '',
        asfisCode: this.getASFISCode(item.name as string)
      },
      packaging: item.packaging as string,
      weightUnit: this.mapToUOM(item.unit as string || 'lbs')
    }));
  }

  private static getEarliest(data: Record<string, unknown>[], field: string): string {
    const dates = data.map(item => item[field] as string).filter(Boolean);
    if (dates.length === 0) return new Date().toISOString().split('T')[0];
    return dates.sort()[0];
  }

  private static getLatest(data: Record<string, unknown>[], field: string): string {
    const dates = data.map(item => item[field] as string).filter(Boolean);
    if (dates.length === 0) return new Date().toISOString().split('T')[0];
    return dates.sort().reverse()[0];
  }

  private static calculateChecksum(data: string): string {
    // Simple checksum calculation (in production, use a proper hash function)
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16);
  }

  // Additional export methods (placeholder implementations)
  private static async exportFDAFSMA(data: Record<string, unknown>[], options: ExportOptions): Promise<string> {
    // FDA FSMA-specific export format
    return Papa.unparse(data, { header: true });
  }

  private static async exportEURegulation(data: Record<string, unknown>[], options: ExportOptions): Promise<string> {
    // EU regulation-specific export format
    return Papa.unparse(data, { header: true });
  }

  private static async exportNOAASeafood(data: Record<string, unknown>[], options: ExportOptions): Promise<string> {
    // NOAA seafood-specific export format
    return Papa.unparse(data, { header: true });
  }

  private static async exportGenericRegulatory(data: Record<string, unknown>[], options: ExportOptions): Promise<string> {
    // Generic regulatory export format
    return Papa.unparse(data, { header: true });
  }
}