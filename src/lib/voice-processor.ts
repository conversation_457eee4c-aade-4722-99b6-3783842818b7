// Enhanced Voice Processing with OpenAI Whisper + GPT-4
// Specialized for seafood inventory management with comprehensive terminology

interface VoiceCommand {
  action_type: 'create_event' | 'query_inventory' | 'get_info';
  event_type?: 'receiving' | 'disposal' | 'physical_count' | 'sale';
  product_name?: string;
  quantity?: number;
  unit?: 'lbs' | 'kg' | 'cases' | 'units';
  vendor_name?: string;
  customer_name?: string;
  condition?: 'Excellent' | 'Good' | 'Fair' | 'Poor' | 'Damaged';
  event_date?: string; // ISO date string
  occurred_at?: string; // When the event actually occurred
  temperature?: number;
  temperature_unit?: 'fahrenheit' | 'celsius';
  processing_method?: string; // Fresh, Frozen, IQF, etc.
  quality_grade?: string; // Premium, Grade A, etc.
  market_form?: string; // Whole, Fillets, H&G, etc.
  notes?: string;
  confidence_score: number;
  confidence_breakdown?: {
    product_match: number;
    quantity_extraction: number;
    vendor_match: number;
    overall: number;
  };
  raw_transcript: string;
}

class VoiceProcessor {
  private apiKey: string;
  private isProcessing = false;
  
  constructor() {
    this.apiKey = import.meta.env.VITE_OPENAI_API_KEY;
    if (!this.apiKey) {
      throw new Error('OpenAI API key not found. Add VITE_OPENAI_API_KEY to your .env file');
    }
  }

  async processAudioBlob(audioBlob: Blob): Promise<VoiceCommand> {
    if (this.isProcessing) {
      throw new Error('Already processing audio. Please wait.');
    }

    this.isProcessing = true;
    
    try {
      // Step 1: Transcribe audio using Whisper
      const transcript = await this.transcribeWithWhisper(audioBlob);
      console.log('Whisper transcript:', transcript);

      // Step 2: Parse command using GPT-4
      const command = await this.parseCommandWithGPT(transcript);
      console.log('Parsed command:', command);

      return command;
    } finally {
      this.isProcessing = false;
    }
  }

  private async transcribeWithWhisper(audioBlob: Blob): Promise<string> {
    const formData = new FormData();
    formData.append('file', audioBlob, 'audio.webm');
    formData.append('model', 'whisper-1');
    formData.append('language', 'en');
    formData.append('prompt', `Seafood inventory management: receiving fresh coho salmon fillets, dungeness crab, pacific cod H&G, IQF scallops from Pacific Seafoods, 49th State Seafoods, Trident Seafoods, Ocean Fresh. Processing methods: fresh, frozen, previously frozen, IQF, H&G, fillets, whole, live.`);

    const response = await fetch('https://api.openai.com/v1/audio/transcriptions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
      },
      body: formData,
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Whisper transcription failed: ${error}`);
    }

    const result = await response.json();
    return result.text.trim();
  }

  private async parseCommandWithGPT(transcript: string): Promise<VoiceCommand> {
    const systemPrompt = `You are a specialized AI assistant for seafood inventory management. Parse voice commands into structured data with industry expertise.

COMPREHENSIVE SEAFOOD DATABASE:

FINFISH SPECIES:
- Salmon: Atlantic Salmon, Pacific King Salmon, Coho Salmon (Silver Salmon), Sockeye Salmon, Chinook Salmon (King Salmon), Pink Salmon, Chum Salmon, Steelhead
- Cod Family: Atlantic Cod, Pacific Cod, Lingcod, Black Cod (Sablefish), Alaska Pollock, Haddock, Whiting
- Flatfish: Pacific Halibut, Atlantic Halibut, Dover Sole, Petrale Sole, English Sole, Flounder, Turbot, Plaice
- Tuna: Bluefin Tuna, Yellowfin Tuna (Ahi), Albacore Tuna, Skipjack Tuna, Bigeye Tuna
- Bass: Sea Bass, Striped Bass, Black Bass, Chilean Sea Bass
- Other: Red Snapper, Grouper, Mahi Mahi, Swordfish, Rockfish, Monkfish, John Dory

SHELLFISH:
- Oysters: Pacific Oysters, Eastern Oysters, Kumamoto Oysters, Blue Point Oysters, Belon Oysters, Olympia Oysters
- Clams: Manila Clams, Littleneck Clams, Razor Clams, Geoduck Clams, Steamer Clams, Quahog Clams
- Mussels: Blue Mussels, Mediterranean Mussels, Green Mussels, Prince Edward Island Mussels
- Scallops: Sea Scallops, Bay Scallops, Diver Scallops, Weathervane Scallops

CRUSTACEANS:
- Lobster: Maine Lobster, Spiny Lobster, Rock Lobster, Langostino, European Lobster
- Crab: Dungeness Crab, King Crab, Snow Crab, Blue Crab, Jonah Crab, Stone Crab, Mud Crab
- Shrimp/Prawns: Tiger Prawns, Spot Prawns, White Shrimp, Pink Shrimp, Rock Shrimp, Gulf Shrimp

SPECIALTY PRODUCTS:
- Roe: Caviar, Salmon Roe, Tobiko, Ikura
- Cephalopods: Octopus, Squid, Cuttlefish
- Mollusks: Abalone, Sea Urchin (Uni), Conch
- Other: Sea Cucumber, Jellyfish

PROCESSING METHODS & FORMS:
- Fresh, Frozen, Previously Frozen (PF), Live, Smoked, Cured, Salted
- IQF (Individually Quick Frozen), Block Frozen, Glazed
- H&G (Head and Gutted), Whole, Fillets, Portions, Steaks
- Skin-on, Skinless, Boneless, Pin-bone Out
- Value-added, Breaded, Marinated

QUALITY GRADES:
- Premium, Grade A, Grade B, Choice, Select, Commercial
- Sashimi Grade, Restaurant Quality, Retail Quality

COMMON VENDORS (fuzzy matching):
- 49th State Seafoods (variations: forty ninth state, 49th state, forty-ninth state, forty nine state)
- Pacific Seafoods (variations: pacific seafood, pac seafoods, pacific sea foods)
- Ocean Fresh Seafoods (variations: ocean fresh, ocean fresh seafood)
- Trident Seafoods (variations: trident, trident seafood)
- Bristol Bay (variations: bristol bay seafood, bristol bay fisheries)
- Icicle Seafoods, UniSea, Peter Pan Seafood, Silver Bay Seafoods

VOICE RECOGNITION CORRECTIONS:
- "dangerous grab" → "Dungeness Crab"
- "dangerous crab" → "Dungeness Crab"  
- "king grab" → "King Crab"
- "snow grab" → "Snow Crab"
- "dover soul" → "Dover Sole"
- "petrel sole" → "Petrale Sole"
- "see scallops" → "Sea Scallops"
- "bay scallops" → "Bay Scallops"
- "alaskan pollock" → "Alaska Pollock"
- "silver salmon" → "Coho Salmon"
- "king salmon" → "Chinook Salmon"
- "age and g" → "H&G"
- "head and gutted" → "H&G"
- "i q f" → "IQF"
- "individually quick frozen" → "IQF"

UNITS & MEASUREMENTS:
- Weight: lbs, pounds, kg, kilograms, tons
- Count: units, pieces, each, dozen
- Volume: cases, boxes, bags, containers
- Temperature: fahrenheit, celsius, degrees

EVENT TYPES:
- receiving: getting new inventory from vendors
- disposal: throwing away expired/damaged items  
- physical_count: counting existing inventory
- sale: selling to customers

DATE/TIME PARSING:
- "yesterday" = previous day
- "today" = current day  
- "this morning" = today 9am
- "this afternoon" = today 2pm
- "last week" = 7 days ago
- Specific: "march 15", "3/15/24", "monday"

RESPONSE FORMAT (JSON only):
{
  "action_type": "create_event" | "query_inventory" | "get_info",
  "event_type": "receiving" | "disposal" | "physical_count" | "sale",
  "product_name": "standardized product name from database",
  "quantity": number,
  "unit": "lbs" | "kg" | "cases" | "units",
  "vendor_name": "matched vendor name",
  "customer_name": "customer name if sale",
  "condition": "Excellent" | "Good" | "Fair" | "Poor" | "Damaged",
  "event_date": "YYYY-MM-DD",
  "occurred_at": "YYYY-MM-DDTHH:mm:ss.sssZ",
  "temperature": number,
  "temperature_unit": "fahrenheit" | "celsius",
  "processing_method": "processing method if mentioned",
  "quality_grade": "quality grade if mentioned", 
  "market_form": "market form if mentioned",
  "notes": "additional context and details",
  "confidence_score": 0.0-1.0,
  "confidence_breakdown": {
    "product_match": 0.0-1.0,
    "quantity_extraction": 0.0-1.0,
    "vendor_match": 0.0-1.0,
    "overall": 0.0-1.0
  },
  "raw_transcript": "original text"
}

CONFIDENCE SCORING:
- High (0.9+): Exact species match, clear quantity, known vendor
- Medium (0.7-0.89): Good species match, quantity present, some ambiguity
- Low (0.5-0.69): Generic terms, unclear quantity, unknown vendor
- Very Low (<0.5): Unable to identify key information

Today's date is ${new Date().toISOString().split('T')[0]}.
Current time is ${new Date().toISOString()}.`;

    const userPrompt = `Parse this seafood inventory command: "${transcript}"`;

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini', // Fast and cost-effective for this use case
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.1,
        max_tokens: 500,
      }),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`GPT parsing failed: ${error}`);
    }

    const result = await response.json();
    const content = result.choices[0]?.message?.content;
    
    if (!content) {
      throw new Error('No response from GPT');
    }

    try {
      const parsed = JSON.parse(content);
      parsed.raw_transcript = transcript;
      return parsed as VoiceCommand;
    } catch (parseError) {
      console.error('Failed to parse GPT response:', content);
      // Fallback command
      return {
        action_type: 'create_event',
        event_type: 'receiving',
        confidence_score: 0.1,
        raw_transcript: transcript,
      };
    }
  }

  // Helper method to record audio from microphone
  async recordAudio(durationMs: number = 5000): Promise<Blob> {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    const mediaRecorder = new MediaRecorder(stream);
    const audioChunks: Blob[] = [];

    return new Promise((resolve, reject) => {
      mediaRecorder.ondataavailable = (event) => {
        audioChunks.push(event.data);
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
        stream.getTracks().forEach(track => track.stop());
        resolve(audioBlob);
      };

      mediaRecorder.onerror = (event) => {
        stream.getTracks().forEach(track => track.stop());
        reject(event.error);
      };

      mediaRecorder.start();
      setTimeout(() => {
        if (mediaRecorder.state === 'recording') {
          mediaRecorder.stop();
        }
      }, durationMs);
    });
  }

  // Check if processing is in progress
  getProcessingStatus(): boolean {
    return this.isProcessing;
  }
}

export default VoiceProcessor;
export type { VoiceCommand };