import { createClient } from '@supabase/supabase-js';

// Defensive: trim to avoid stray whitespace/newlines from .env pastes
const supabaseUrl = (import.meta.env.VITE_SUPABASE_URL || '').trim();
const supabaseAnonKey = (import.meta.env.VITE_SUPABASE_ANON_KEY || '').trim();

// Basic validation without exposing secrets
if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase env vars', {
    hasUrl: !!supabaseUrl,
    hasAnonKey: !!supabaseAnonKey,
  });
  throw new Error('Missing Supabase environment variables. Please check your .env file.');
}

// Optional masked diagnostics (safe): log host and key length only
try {
  const host = new URL(supabaseUrl).host;
  const keyLen = supabaseAnonKey.length;
  console.info('[Supabase] Using host:', host, 'anonKeyLength:', keyLen);
} catch {
  // ignore URL parse issues; createClient will throw later
}

// Client-side Supabase instance MUST use the anon key
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  db: { schema: 'public' },
  auth: {
    persistSession: true,
    autoRefreshToken: true,
  },
});

// Note: Service role key must NEVER be used in the browser. For privileged
// operations, use server-side scripts (see Node scripts in project root).
