/**
 * Form Validation Framework
 * 
 * Comprehensive validation system with:
 * - Schema-based validation
 * - Built-in validation rules
 * - Custom validation rules
 * - Cross-field validation
 * - Async validation with caching
 * - Transform and sanitization
 */

// Import from new module location to make symbols available
import { FormValidator as NewFormValidator } from '../../modules/validation/FormValidator';
import * as rules from '../../modules/validation/rules';

// Re-export everything from new module location
export * from '../../modules/validation/types';
export { FormValidator } from '../../modules/validation/FormValidator';
export * as rules from '../../modules/validation/rules';

// Utility functions
export const createValidator = (schema: import('../../modules/validation/types').FormValidationSchema) => {
  return new NewFormValidator(schema);
};

export const validateField = async (
  fieldName: string,
  value: unknown,
  config: import('../../modules/validation/types').FieldValidationConfig,
  context?: import('../../modules/validation/types').ValidationContext
) => {
  const validator = new NewFormValidator({ [fieldName]: config });
  return validator.validateField(fieldName, value, context);
};

// Common validation schemas
export const commonSchemas = {
  voiceEvent: {
    event_type: {
      rules: [rules.required],
      required: true
    },
    product_name: {
      rules: [rules.required, rules.productName],
      required: true,
      transform: (value: unknown) => String(value).trim()
    },
    quantity: {
      rules: [rules.required, rules.quantity, rules.positive],
      required: true,
      transform: (value: unknown) => Number(value)
    },
    unit: {
      rules: [rules.required],
      required: true
    },
    voice_confidence_score: {
      rules: [rules.confidenceScore],
      required: false,
      transform: (value: unknown) => Number(value)
    },
    temperature: {
      rules: [rules.temperature],
      required: false,
      transform: (value: unknown) => value === '' ? undefined : Number(value)
    },
    occurred_at: {
      rules: [rules.required, rules.dateFormat()],
      required: true
    }
  },
  
  user: {
    email: {
      rules: [rules.required, rules.email],
      required: true,
      transform: (value: unknown) => String(value).toLowerCase().trim()
    },
    password: {
      rules: [rules.required, rules.minLength(8)],
      required: true
    },
    name: {
      rules: [rules.required, rules.minLength(2), rules.maxLength(50)],
      required: true,
      transform: (value: unknown) => String(value).trim()
    }
  }
};