/**
 * Environment Configuration for TempStick Production Deployment
 * 
 * Manages environment variables, feature flags, and configuration
 * settings for different deployment environments.
 */

export interface EnvironmentConfig {
  supabase: {
    url: string;
    anonKey: string;
    serviceRoleKey?: string;
  };
  tempstick: {
    apiUrl: string;
    apiKey: string;
    webhookUrl?: string;
    syncInterval: number; // milliseconds
  };
  monitoring: {
    enabled: boolean;
    datadog?: {
      apiKey: string;
      appKey: string;
    };
    sentry?: {
      dsn: string;
    };
    logLevel: 'debug' | 'info' | 'warn' | 'error';
  };
  notifications: {
    email: {
      enabled: boolean;
      smtpHost?: string;
      smtpPort?: number;
      username?: string;
      password?: string;
      fromAddress: string;
    };
    sms: {
      enabled: boolean;
      provider: 'twilio' | 'aws-sns';
      config: Record<string, string>;
    };
    slack: {
      enabled: boolean;
      webhookUrl?: string;
      botToken?: string;
    };
  };
  features: {
    temperatureMonitoring: boolean;
    realTimeAlerts: boolean;
    advancedReporting: boolean;
    autoScaling: boolean;
    performanceMonitoring: boolean;
  };
  performance: {
    maxSensorsPerSync: number;
    maxDataPointsPerRequest: number;
    cacheTimeoutMs: number;
    connectionPoolSize: number;
  };
  security: {
    enableRateLimiting: boolean;
    maxRequestsPerMinute: number;
    enableCors: boolean;
    trustedDomains: string[];
  };
}

/**
 * Get environment configuration based on current environment
 */
function getEnvironmentConfig(): EnvironmentConfig {
  const isDev = import.meta.env.DEV;
  const isStaging = import.meta.env.VITE_ENVIRONMENT === 'staging';
  const isProd = import.meta.env.VITE_ENVIRONMENT === 'production';

  // Base configuration
  const baseConfig: EnvironmentConfig = {
    supabase: {
      url: import.meta.env.VITE_SUPABASE_URL || '',
      anonKey: import.meta.env.VITE_SUPABASE_ANON_KEY || '',
      serviceRoleKey: import.meta.env.VITE_SUPABASE_SERVICE_ROLE_KEY
    },
    tempstick: {
      apiUrl: import.meta.env.VITE_TEMPSTICK_API_URL || 'https://tempstickapi.com/api/v1',
      apiKey: import.meta.env.VITE_TEMPSTICK_API_KEY || '',
      webhookUrl: import.meta.env.VITE_TEMPSTICK_WEBHOOK_URL,
      syncInterval: parseInt(import.meta.env.VITE_TEMPSTICK_SYNC_INTERVAL || '300000') // 5 minutes default
    },
    monitoring: {
      enabled: !isDev,
      datadog: {
        apiKey: import.meta.env.VITE_DATADOG_API_KEY || '',
        appKey: import.meta.env.VITE_DATADOG_APP_KEY || ''
      },
      sentry: {
        dsn: import.meta.env.VITE_SENTRY_DSN || ''
      },
      logLevel: isDev ? 'debug' : isProd ? 'warn' : 'info'
    },
    notifications: {
      email: {
        enabled: !isDev,
        smtpHost: import.meta.env.VITE_SMTP_HOST,
        smtpPort: parseInt(import.meta.env.VITE_SMTP_PORT || '587'),
        username: import.meta.env.VITE_SMTP_USERNAME,
        password: import.meta.env.VITE_SMTP_PASSWORD,
        fromAddress: import.meta.env.VITE_SMTP_FROM || '<EMAIL>'
      },
      sms: {
        enabled: !isDev,
        provider: (import.meta.env.VITE_SMS_PROVIDER as 'twilio' | 'aws-sns') || 'twilio',
        config: {
          accountSid: import.meta.env.VITE_TWILIO_ACCOUNT_SID || '',
          authToken: import.meta.env.VITE_TWILIO_AUTH_TOKEN || '',
          fromNumber: import.meta.env.VITE_TWILIO_FROM_NUMBER || ''
        }
      },
      slack: {
        enabled: !isDev,
        webhookUrl: import.meta.env.VITE_SLACK_WEBHOOK_URL,
        botToken: import.meta.env.VITE_SLACK_BOT_TOKEN
      }
    },
    features: {
      temperatureMonitoring: true,
      realTimeAlerts: !isDev,
      advancedReporting: true,
      autoScaling: isProd,
      performanceMonitoring: !isDev
    },
    performance: {
      maxSensorsPerSync: isDev ? 5 : isProd ? 100 : 20,
      maxDataPointsPerRequest: isDev ? 100 : isProd ? 1000 : 500,
      cacheTimeoutMs: isDev ? 30000 : 300000, // 30s dev, 5min prod
      connectionPoolSize: isDev ? 5 : isProd ? 20 : 10
    },
    security: {
      enableRateLimiting: !isDev,
      maxRequestsPerMinute: isDev ? 1000 : isProd ? 100 : 200,
      enableCors: true,
      trustedDomains: isDev 
        ? ['http://localhost:5177', 'http://localhost:3000']
        : isProd 
          ? ['https://seafoodmanager.com', 'https://www.seafoodmanager.com']
          : ['https://staging.seafoodmanager.com']
    }
  };

  return baseConfig;
}

/**
 * Validate required environment variables
 */
function validateEnvironment(): void {
  const config = getEnvironmentConfig();
  const errors: string[] = [];

  // Required variables
  if (!config.supabase.url) {
    errors.push('VITE_SUPABASE_URL is required');
  }
  if (!config.supabase.anonKey) {
    errors.push('VITE_SUPABASE_ANON_KEY is required');
  }
  if (!config.tempstick.apiKey) {
    errors.push('VITE_TEMPSTICK_API_KEY is required');
  }

  // Production-specific validations
  if (import.meta.env.VITE_ENVIRONMENT === 'production') {
    if (!config.monitoring.sentry?.dsn) {
      errors.push('VITE_SENTRY_DSN is required in production');
    }
    if (!config.notifications.email.smtpHost) {
      errors.push('VITE_SMTP_HOST is required in production');
    }
  }

  if (errors.length > 0) {
    console.error('Environment validation failed:');
    errors.forEach(error => console.error(`  - ${error}`));
    throw new Error(`Missing required environment variables: ${errors.join(', ')}`);
  }
}

/**
 * Get feature flag value
 */
export function getFeatureFlag(feature: keyof EnvironmentConfig['features']): boolean {
  return environmentConfig.features[feature];
}

/**
 * Get performance setting
 */
export function getPerformanceSetting<K extends keyof EnvironmentConfig['performance']>(
  setting: K
): EnvironmentConfig['performance'][K] {
  return environmentConfig.performance[setting];
}

/**
 * Check if environment is development
 */
export function isDevelopment(): boolean {
  return import.meta.env.DEV;
}

/**
 * Check if environment is staging
 */
export function isStaging(): boolean {
  return import.meta.env.VITE_ENVIRONMENT === 'staging';
}

/**
 * Check if environment is production
 */
export function isProduction(): boolean {
  return import.meta.env.VITE_ENVIRONMENT === 'production';
}

/**
 * Get current environment name
 */
export function getEnvironmentName(): string {
  if (isDevelopment()) return 'development';
  if (isStaging()) return 'staging';
  if (isProduction()) return 'production';
  return 'unknown';
}

/**
 * Environment-specific logging
 */
export function log(level: 'debug' | 'info' | 'warn' | 'error', message: string, ...args: any[]): void {
  const config = getEnvironmentConfig();
  const logLevels = ['debug', 'info', 'warn', 'error'];
  const currentLevelIndex = logLevels.indexOf(config.monitoring.logLevel);
  const messageLevelIndex = logLevels.indexOf(level);

  if (messageLevelIndex >= currentLevelIndex) {
    const timestamp = new Date().toISOString();
    const prefix = `[${timestamp}] [${level.toUpperCase()}] [${getEnvironmentName()}]`;
    
    switch (level) {
      case 'debug':
        console.debug(prefix, message, ...args);
        break;
      case 'info':
        console.info(prefix, message, ...args);
        break;
      case 'warn':
        console.warn(prefix, message, ...args);
        break;
      case 'error':
        console.error(prefix, message, ...args);
        break;
    }
  }
}

/**
 * Initialize environment and validate configuration
 */
export function initializeEnvironment(): EnvironmentConfig {
  try {
    validateEnvironment();
    const config = getEnvironmentConfig();
    
    log('info', `Environment initialized: ${getEnvironmentName()}`);
    log('debug', 'Configuration loaded:', {
      features: config.features,
      performance: config.performance,
      monitoring: { enabled: config.monitoring.enabled }
    });
    
    return config;
  } catch (error) {
    console.error('Failed to initialize environment:', error);
    throw error;
  }
}

// Export the configuration instance
export const environmentConfig = getEnvironmentConfig();

// Auto-initialize in non-test environments
if (typeof window !== 'undefined' && !import.meta.env.VITEST) {
  try {
    initializeEnvironment();
  } catch (error) {
    console.error('Environment initialization failed:', error);
  }
}

export default environmentConfig;