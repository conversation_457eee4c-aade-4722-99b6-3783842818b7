/**
 * TempStick Service Configuration Management
 * 
 * Centralized configuration for TempStick API integration with
 * environment-specific settings and validation.
 */

export interface TempStickConfig {
  // API Configuration
  apiKey: string;
  baseUrl: string;
  apiVersion: string;
  
  // Rate Limiting
  rateLimit: {
    requestsPerMinute: number;
    burstLimit: number;
  };
  
  // Retry Configuration
  retry: {
    maxRetries: number;
    baseDelay: number;
    maxDelay: number;
    backoffMultiplier: number;
  };
  
  // Timeout Settings
  timeout: {
    requestTimeout: number;
    healthCheckTimeout: number;
  };
  
  // Health Check Settings
  healthCheck: {
    enabled: boolean;
    intervalMinutes: number;
  };
  
  // Data Management
  data: {
    maxReadingsPerRequest: number;
    maxDateRangeDays: number;
    defaultReadingsLimit: number;
  };
}

/**
 * Default TempStick configuration
 */
export const DEFAULT_TEMPSTICK_CONFIG: TempStickConfig = {
  apiKey: '',
  baseUrl: process.env.NODE_ENV === 'development' 
    ? 'http://localhost:3001/api'  // Use proxy server in development
    : 'https://api.tempstick.com', // Correct TempStick API URL
  apiVersion: 'v1',
  
  rateLimit: {
    requestsPerMinute: 60,
    burstLimit: 10,
  },
  
  retry: {
    maxRetries: 3,
    baseDelay: 1000, // 1 second
    maxDelay: 30000, // 30 seconds
    backoffMultiplier: 2,
  },
  
  timeout: {
    requestTimeout: 30000, // 30 seconds
    healthCheckTimeout: 10000, // 10 seconds
  },
  
  healthCheck: {
    enabled: true,
    intervalMinutes: 5,
  },
  
  data: {
    maxReadingsPerRequest: 1000,
    maxDateRangeDays: 30,
    defaultReadingsLimit: 100,
  },
};

/**
 * Production-optimized configuration
 */
export const PRODUCTION_TEMPSTICK_CONFIG: Partial<TempStickConfig> = {
  rateLimit: {
    requestsPerMinute: 90, // More aggressive for production
    burstLimit: 15,
  },
  
  retry: {
    maxRetries: 5,
    baseDelay: 2000,
    maxDelay: 60000,
    backoffMultiplier: 2,
  },
  
  healthCheck: {
    enabled: true,
    intervalMinutes: 3, // More frequent checks in production
  },
};

/**
 * Development-optimized configuration
 */
export const DEVELOPMENT_TEMPSTICK_CONFIG: Partial<TempStickConfig> = {
  rateLimit: {
    requestsPerMinute: 30, // Lower rate for development
    burstLimit: 5,
  },
  
  retry: {
    maxRetries: 2,
    baseDelay: 500,
    maxDelay: 10000,
    backoffMultiplier: 1.5,
  },
  
  healthCheck: {
    enabled: false, // Disable health checks in development
    intervalMinutes: 10,
  },
};

/**
 * Load TempStick configuration from environment variables
 */
export function loadTempStickConfig(): TempStickConfig {
  const config = { ...DEFAULT_TEMPSTICK_CONFIG };
  
  // Load API configuration
  config.apiKey = import.meta.env.VITE_TEMPSTICK_API_KEY || '';
  
  // Override with environment-specific settings
  if (import.meta.env.PROD) {
    Object.assign(config, PRODUCTION_TEMPSTICK_CONFIG);
  } else if (import.meta.env.DEV) {
    Object.assign(config, DEVELOPMENT_TEMPSTICK_CONFIG);
  }
  
  // Override specific settings from environment variables
  if (import.meta.env.VITE_TEMPSTICK_BASE_URL) {
    config.baseUrl = import.meta.env.VITE_TEMPSTICK_BASE_URL;
  }
  

  
  // Parse numeric environment variables
  if (import.meta.env.VITE_TEMPSTICK_RATE_LIMIT) {
    config.rateLimit.requestsPerMinute = parseInt(import.meta.env.VITE_TEMPSTICK_RATE_LIMIT, 10);
  }
  
  if (import.meta.env.VITE_TEMPSTICK_MAX_RETRIES) {
    config.retry.maxRetries = parseInt(import.meta.env.VITE_TEMPSTICK_MAX_RETRIES, 10);
  }
  
  return config;
}

/**
 * Validate TempStick configuration
 */
export function validateTempStickConfig(config: TempStickConfig): string[] {
  const errors: string[] = [];
  
  // Validate API key
  if (!config.apiKey.trim()) {
    errors.push('TempStick API key is required (VITE_TEMPSTICK_API_KEY)');
  }
  
  // Validate base URL format
  try {
    new URL(`${config.baseUrl}/${config.apiVersion}`);
  } catch {
    errors.push(`Invalid TempStick base URL: ${config.baseUrl}`);
  }
  
  // Validate rate limit settings
  if (config.rateLimit.requestsPerMinute <= 0) {
    errors.push('Rate limit requests per minute must be positive');
  }
  
  if (config.rateLimit.burstLimit <= 0) {
    errors.push('Rate limit burst limit must be positive');
  }
  
  // Validate retry settings
  if (config.retry.maxRetries < 0) {
    errors.push('Max retries cannot be negative');
  }
  
  if (config.retry.baseDelay <= 0) {
    errors.push('Base delay must be positive');
  }
  
  if (config.retry.maxDelay < config.retry.baseDelay) {
    errors.push('Max delay must be greater than or equal to base delay');
  }
  
  // Validate timeout settings
  if (config.timeout.requestTimeout <= 0) {
    errors.push('Request timeout must be positive');
  }
  
  if (config.timeout.healthCheckTimeout <= 0) {
    errors.push('Health check timeout must be positive');
  }
  
  // Validate data settings
  if (config.data.maxReadingsPerRequest <= 0) {
    errors.push('Max readings per request must be positive');
  }
  
  if (config.data.maxDateRangeDays <= 0) {
    errors.push('Max date range days must be positive');
  }
  
  return errors;
}

/**
 * Get TempStick configuration with validation
 */
export function getTempStickConfig(): { config: TempStickConfig; errors: string[] } {
  const config = loadTempStickConfig();
  const errors = validateTempStickConfig(config);
  
  return { config, errors };
}

/**
 * Create a formatted configuration summary for logging
 */
export function formatConfigSummary(config: TempStickConfig): string {
  const summary = {
    environment: import.meta.env.MODE,
    baseUrl: config.baseUrl,
    apiVersion: config.apiVersion,
    hasApiKey: !!config.apiKey,
    rateLimit: `${config.rateLimit.requestsPerMinute}/min (burst: ${config.rateLimit.burstLimit})`,
    retry: `${config.retry.maxRetries} retries, ${config.retry.baseDelay}-${config.retry.maxDelay}ms`,
    timeout: `${config.timeout.requestTimeout}ms`,
    healthCheck: config.healthCheck.enabled ? `every ${config.healthCheck.intervalMinutes}min` : 'disabled',
  };
  
  return JSON.stringify(summary, null, 2);
}

/**
 * Export singleton configuration instance
 */
export const tempStickConfig = loadTempStickConfig();

// Log configuration summary in development
if (import.meta.env.DEV) {
  const errors = validateTempStickConfig(tempStickConfig);
  
  console.group('🌡️ TempStick Configuration');
  console.log(formatConfigSummary(tempStickConfig));
  
  if (errors.length > 0) {
    console.warn('Configuration warnings:', errors);
  } else {
    console.log('✅ Configuration is valid');
  }
  
  console.groupEnd();
}