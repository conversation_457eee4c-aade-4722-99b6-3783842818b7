// Conversational Voice Processing Engine
// Transforms single commands into intelligent multi-turn conversations

import VoiceProcessor, { VoiceCommand } from './voice-processor';
import { supabase } from './supabase';
import { findVendorByName, createVendorFromVoice, generateBatchCode, findProductByName } from './api';

export interface ConversationState {
  id: string;
  stage: 'initial' | 'collecting' | 'confirming' | 'vendor_creation' | 'batch_confirmation' | 'completed' | 'error';
  currentData: Partial<VoiceCommand>;
  missingFields: string[];
  conversationHistory: ConversationTurn[];
  awaitingResponse: 'user_input' | 'confirmation' | 'vendor_creation' | 'batch_acceptance' | null;
  retryCount: number;
  lastError?: string;
  vendorCreationContext?: {
    newVendorName: string;
    detectedInfo?: {
      location?: string;
      contactInfo?: string;
    };
  };
  batchContext?: {
    generatedCode: string;
    autoGenerated: boolean;
  };
}

export interface ConversationTurn {
  id: string;
  timestamp: string;
  type: 'user_voice' | 'user_text' | 'agent_question' | 'agent_confirmation' | 'system_message';
  content: string;
  extractedData?: Partial<VoiceCommand>;
  confidence?: number;
}

export interface ConversationResponse {
  success: boolean;
  state: ConversationState;
  agentMessage?: string;
  shouldSpeak?: boolean;
  requiresUserInput?: boolean;
  suggestedResponses?: string[];
  error?: string;
}

class ConversationalVoiceProcessor {
  private voiceProcessor: VoiceProcessor;
  private conversations: Map<string, ConversationState> = new Map();
  
  constructor() {
    this.voiceProcessor = new VoiceProcessor();
  }

  async startConversation(initialInput: string): Promise<ConversationResponse> {
    const conversationId = `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      // Process initial voice input
      const audioBlob = await this.textToAudioBlob(initialInput); // Mock for now
      const initialCommand = await this.voiceProcessor.processAudioBlob(audioBlob);
      
      const conversation: ConversationState = {
        id: conversationId,
        stage: 'initial',
        currentData: initialCommand,
        missingFields: [],
        conversationHistory: [{
          id: `turn_${Date.now()}`,
          timestamp: new Date().toISOString(),
          type: 'user_voice',
          content: initialInput,
          extractedData: initialCommand,
          confidence: initialCommand.confidence_score
        }],
        awaitingResponse: null,
        retryCount: 0
      };

      this.conversations.set(conversationId, conversation);
      
      return await this.processConversationTurn(conversationId);
    } catch (error) {
      return {
        success: false,
        state: {} as ConversationState,
        error: error instanceof Error ? error.message : 'Failed to start conversation'
      };
    }
  }

  async continueConversation(conversationId: string, userInput: string, inputType: 'voice' | 'text' = 'voice'): Promise<ConversationResponse> {
    const conversation = this.conversations.get(conversationId);
    
    if (!conversation) {
      return {
        success: false,
        state: {} as ConversationState,
        error: 'Conversation not found'
      };
    }

    try {
      // Process user input based on current conversation stage
      let newData: Partial<VoiceCommand> = {};
      let confidence = 1.0;
      
      if (inputType === 'voice') {
        const audioBlob = await this.textToAudioBlob(userInput); // Mock for now
        const voiceCommand = await this.voiceProcessor.processAudioBlob(audioBlob);
        newData = voiceCommand;
        confidence = voiceCommand.confidence_score;
      } else {
        // Parse text input based on conversation context
        newData = await this.parseContextualTextInput(userInput, conversation);
      }

      // Add turn to conversation history
      conversation.conversationHistory.push({
        id: `turn_${Date.now()}`,
        timestamp: new Date().toISOString(),
        type: inputType === 'voice' ? 'user_voice' : 'user_text',
        content: userInput,
        extractedData: newData,
        confidence
      });

      // Update conversation data
      this.mergeConversationData(conversation, newData);
      
      return await this.processConversationTurn(conversationId);
    } catch (error) {
      conversation.stage = 'error';
      conversation.lastError = error instanceof Error ? error.message : 'Unknown error';
      conversation.retryCount++;
      
      return {
        success: false,
        state: conversation,
        error: conversation.lastError,
        agentMessage: "I encountered an error. Would you like to start over or try again?",
        suggestedResponses: ["Start over", "Try again", "Cancel"]
      };
    }
  }

  private async processConversationTurn(conversationId: string): Promise<ConversationResponse> {
    const conversation = this.conversations.get(conversationId)!;
    
    // Analyze current state and determine next action
    switch (conversation.stage) {
      case 'initial':
        return await this.handleInitialStage(conversation);
      case 'collecting':
        return await this.handleCollectingStage(conversation);
      case 'vendor_creation':
        return await this.handleVendorCreationStage(conversation);
      case 'batch_confirmation':
        return await this.handleBatchConfirmationStage(conversation);
      case 'confirming':
        return await this.handleConfirmationStage(conversation);
      case 'completed':
        return await this.handleCompletedStage(conversation);
      case 'error':
        return await this.handleErrorStage(conversation);
      default:
        return {
          success: false,
          state: conversation,
          error: 'Unknown conversation stage'
        };
    }
  }

  private async handleInitialStage(conversation: ConversationState): Promise<ConversationResponse> {
    // Analyze what data we have and what's missing
    const missingFields = this.detectMissingFields(conversation.currentData);
    conversation.missingFields = missingFields;

    if (missingFields.length === 0) {
      // We have everything, check if vendor exists
      if (conversation.currentData.vendor_name) {
        const vendorExists = await this.checkVendorExists(conversation.currentData.vendor_name);
        if (!vendorExists) {
          conversation.stage = 'vendor_creation';
          conversation.vendorCreationContext = {
            newVendorName: conversation.currentData.vendor_name
          };
          return this.handleVendorCreationStage(conversation);
        }
      }
      
      // Move to batch confirmation for receiving events
      if (conversation.currentData.event_type === 'receiving') {
        conversation.stage = 'batch_confirmation';
        return this.handleBatchConfirmationStage(conversation);
      } else {
        conversation.stage = 'confirming';
        return this.handleConfirmationStage(conversation);
      }
    }

    // We have missing fields, start collecting
    conversation.stage = 'collecting';
    return this.handleCollectingStage(conversation);
  }

  private async handleCollectingStage(conversation: ConversationState): Promise<ConversationResponse> {
    const nextField = conversation.missingFields[0];
    const question = this.generateFieldQuestion(nextField, conversation.currentData);
    
    conversation.awaitingResponse = 'user_input';
    
    // Add agent question to history
    conversation.conversationHistory.push({
      id: `turn_${Date.now()}`,
      timestamp: new Date().toISOString(),
      type: 'agent_question',
      content: question
    });

    return {
      success: true,
      state: conversation,
      agentMessage: question,
      shouldSpeak: true,
      requiresUserInput: true,
      suggestedResponses: this.getSuggestedResponses(nextField)
    };
  }

  private async handleVendorCreationStage(conversation: ConversationState): Promise<ConversationResponse> {
    const vendorContext = conversation.vendorCreationContext!;
    
    if (!vendorContext.detectedInfo) {
      // Ask for vendor information
      const question = `I don't see "${vendorContext.newVendorName}" in our system. Would you like me to add them as a new vendor? If yes, please provide their location or contact information.`;
      
      conversation.awaitingResponse = 'vendor_creation';
      
      conversation.conversationHistory.push({
        id: `turn_${Date.now()}`,
        timestamp: new Date().toISOString(),
        type: 'agent_question',
        content: question
      });

      return {
        success: true,
        state: conversation,
        agentMessage: question,
        shouldSpeak: true,
        requiresUserInput: true,
        suggestedResponses: ["Yes, add them", "No, skip vendor", "They're in Seattle", "They're local"]
      };
    } else {
      // Create the vendor
      try {
        await this.createNewVendor(vendorContext.newVendorName, vendorContext.detectedInfo);
        
        conversation.stage = 'batch_confirmation';
        
        const successMessage = `Great! I've added ${vendorContext.newVendorName} to our vendor database.`;
        conversation.conversationHistory.push({
          id: `turn_${Date.now()}`,
          timestamp: new Date().toISOString(),
          type: 'system_message',
          content: successMessage
        });

        return this.handleBatchConfirmationStage(conversation);
      } catch (error) {
        conversation.stage = 'error';
        conversation.lastError = `Failed to create vendor: ${error instanceof Error ? error.message : 'Unknown error'}`;
        
        return {
          success: false,
          state: conversation,
          error: conversation.lastError,
          agentMessage: "I couldn't create the vendor. Would you like to skip this step or try again?",
          suggestedResponses: ["Skip vendor", "Try again", "Start over"]
        };
      }
    }
  }

  private async handleBatchConfirmationStage(conversation: ConversationState): Promise<ConversationResponse> {
    if (!conversation.batchContext) {
      // Generate batch code
      const batchCode = await this.generateBatchCodeForData(conversation.currentData);
      conversation.batchContext = {
        generatedCode: batchCode,
        autoGenerated: true
      };
    }

    const question = `I've generated batch code "${conversation.batchContext.generatedCode}". Would you like to use this batch number for this receiving?`;
    
    conversation.awaitingResponse = 'batch_acceptance';
    
    conversation.conversationHistory.push({
      id: `turn_${Date.now()}`,
      timestamp: new Date().toISOString(),
      type: 'agent_question',
      content: question
    });

    return {
      success: true,
      state: conversation,
      agentMessage: question,
      shouldSpeak: true,
      requiresUserInput: true,
      suggestedResponses: ["Yes, use it", "No, generate another", "Let me specify one", "Skip batch code"]
    };
  }

  private async handleConfirmationStage(conversation: ConversationState): Promise<ConversationResponse> {
    const summary = this.generateTransactionSummary(conversation.currentData, conversation.batchContext);
    const question = `Let me confirm: ${summary}. Should I add this to inventory?`;
    
    conversation.awaitingResponse = 'confirmation';
    
    conversation.conversationHistory.push({
      id: `turn_${Date.now()}`,
      timestamp: new Date().toISOString(),
      type: 'agent_confirmation',
      content: question
    });

    return {
      success: true,
      state: conversation,
      agentMessage: question,
      shouldSpeak: true,
      requiresUserInput: true,
      suggestedResponses: ["Yes, add it", "No, change something", "Start over", "Cancel"]
    };
  }

  private async handleCompletedStage(conversation: ConversationState): Promise<ConversationResponse> {
    try {
      // Submit to database
      const result = await this.submitInventoryTransaction(conversation.currentData, conversation.batchContext);
      
      const successMessage = `Perfect! I've successfully added ${conversation.currentData.quantity} ${conversation.currentData.unit} of ${conversation.currentData.product_name} to inventory${conversation.batchContext ? ` with batch code ${conversation.batchContext.generatedCode}` : ''}.`;
      
      conversation.conversationHistory.push({
        id: `turn_${Date.now()}`,
        timestamp: new Date().toISOString(),
        type: 'system_message',
        content: successMessage
      });

      // Clean up conversation
      this.conversations.delete(conversation.id);

      return {
        success: true,
        state: conversation,
        agentMessage: successMessage,
        shouldSpeak: true,
        requiresUserInput: false
      };
    } catch (error) {
      conversation.stage = 'error';
      conversation.lastError = error instanceof Error ? error.message : 'Failed to save to database';
      
      return {
        success: false,
        state: conversation,
        error: conversation.lastError,
        agentMessage: "I couldn't save the transaction. Would you like me to try again?",
        suggestedResponses: ["Try again", "Start over", "Cancel"]
      };
    }
  }

  private async handleErrorStage(conversation: ConversationState): Promise<ConversationResponse> {
    const errorType = conversation.lastError || 'Unknown error occurred';
    let agentMessage = "I encountered an error. ";
    let suggestedResponses = ["Start over", "Try again", "Cancel"];
    
    if (errorType === 'Help requested') {
      agentMessage = `I can help you add inventory items through conversation. You can say things like:
        
- "Received 50 pounds of salmon from Pacific Seafoods"
- "Got 25 pounds dungeness crab"
- "Disposal of 10 pounds expired cod"

You can also say "start over", "go back", "skip", or ask for "help" at any time.`;
      suggestedResponses = ["Start over", "Try an example", "Got it"];
      conversation.stage = 'initial'; // Reset after help
    } else if (errorType === 'User cancelled') {
      agentMessage = "Transaction cancelled. Would you like to start a new one?";
      suggestedResponses = ["Start new transaction", "Exit"];
    } else if (errorType.includes('Product not found')) {
      agentMessage = "I couldn't find that product in our database. Would you like to try with a different product name or start over?";
      suggestedResponses = ["Try different product", "Start over", "Cancel"];
    } else if (errorType.includes('vendor')) {
      agentMessage = "There was an issue with the vendor. Would you like to try again or skip vendor information?";
      suggestedResponses = ["Try again", "Skip vendor", "Start over"];
    } else if (conversation.retryCount >= 3) {
      agentMessage = "I've tried several times but keep running into issues. Let me start fresh with you.";
      suggestedResponses = ["Start fresh", "Get human help", "Exit"];
      conversation.stage = 'initial'; // Force reset after too many retries
      conversation.retryCount = 0;
    } else {
      agentMessage += "Would you like to try again or start over?";
    }
    
    return {
      success: false,
      state: conversation,
      error: errorType,
      agentMessage,
      suggestedResponses,
      shouldSpeak: true
    };
  }

  // Helper methods
  private detectMissingFields(data: Partial<VoiceCommand>): string[] {
    const missing: string[] = [];
    
    if (!data.product_name) missing.push('product');
    if (!data.quantity || data.quantity <= 0) missing.push('quantity');
    if (!data.unit) missing.push('unit');
    
    if (data.event_type === 'receiving') {
      if (!data.vendor_name) missing.push('vendor');
    }
    
    if (data.event_type === 'sale') {
      if (!data.customer_name) missing.push('customer');
    }
    
    return missing;
  }

  private generateFieldQuestion(field: string, currentData: Partial<VoiceCommand>): string {
    switch (field) {
      case 'product':
        return "I didn't catch the product name. What seafood product are we processing?";
      case 'quantity':
        return "How much are we processing? Please specify the quantity and unit.";
      case 'unit':
        return "What unit should I use? Pounds, kilograms, cases, or units?";
      case 'vendor':
        return "Which vendor is this from? Please tell me the supplier name.";
      case 'customer':
        return "Who is the customer for this sale?";
      default:
        return `I need more information about ${field}. Can you provide that?`;
    }
  }

  private getSuggestedResponses(field: string): string[] {
    switch (field) {
      case 'product':
        return ["Salmon", "Dungeness Crab", "Halibut", "Cod"];
      case 'quantity':
        return ["50 pounds", "25 kilograms", "10 cases"];
      case 'unit':
        return ["Pounds", "Kilograms", "Cases", "Units"];
      case 'vendor':
        return ["Pacific Seafoods", "49th State", "Ocean Fresh", "Trident"];
      case 'customer':
        return ["Restaurant ABC", "Market XYZ", "Hotel Chain"];
      default:
        return [];
    }
  }

  private mergeConversationData(conversation: ConversationState, newData: Partial<VoiceCommand>): void {
    // Merge new data into current data, being smart about conflicts
    Object.keys(newData).forEach(key => {
      const value = newData[key as keyof VoiceCommand];
      if (value !== undefined && value !== null && value !== '') {
        (conversation.currentData as any)[key] = value;
      }
    });

    // Update missing fields
    conversation.missingFields = this.detectMissingFields(conversation.currentData);
  }

  private async parseContextualTextInput(input: string, conversation: ConversationState): Promise<Partial<VoiceCommand>> {
    const lowerInput = input.toLowerCase().trim();
    
    // Handle recovery commands
    if (['start over', 'restart', 'begin again', 'reset'].includes(lowerInput)) {
      conversation.stage = 'initial';
      conversation.currentData = {};
      conversation.missingFields = [];
      conversation.awaitingResponse = null;
      conversation.retryCount = 0;
      conversation.lastError = undefined;
      return {};
    }
    
    if (['go back', 'previous', 'undo'].includes(lowerInput)) {
      return this.handleGoBack(conversation);
    }
    
    if (['help', 'what can i say', 'commands'].includes(lowerInput)) {
      conversation.stage = 'error';
      conversation.lastError = 'Help requested';
      return {};
    }
    
    // Handle yes/no responses
    if (['yes', 'yeah', 'yep', 'confirm', 'correct', 'ok', 'okay', 'sounds good', 'that works'].includes(lowerInput)) {
      if (conversation.awaitingResponse === 'confirmation') {
        conversation.stage = 'completed';
      } else if (conversation.awaitingResponse === 'batch_acceptance') {
        conversation.stage = 'confirming';
      } else if (conversation.awaitingResponse === 'vendor_creation') {
        // User confirmed vendor creation, need more info
        return {};
      }
      return {};
    }
    
    if (['no', 'nope', 'cancel', 'stop', 'abort', 'quit', 'never mind'].includes(lowerInput)) {
      if (conversation.awaitingResponse === 'batch_acceptance') {
        // Regenerate batch code
        conversation.batchContext = undefined;
        return {};
      } else if (conversation.awaitingResponse === 'vendor_creation') {
        // Skip vendor creation
        conversation.stage = 'batch_confirmation';
        return {};
      } else {
        conversation.stage = 'error';
        conversation.lastError = 'User cancelled';
        return {};
      }
    }

    // Handle corrections and modifications
    if (lowerInput.startsWith('actually') || lowerInput.startsWith('change') || lowerInput.startsWith('make that') || lowerInput.startsWith('correction')) {
      return await this.parseCorrection(input, conversation);
    }
    
    // Handle skip commands
    if (lowerInput.includes('skip') || lowerInput.includes('next') || lowerInput.includes('continue without')) {
      return this.handleSkip(conversation);
    }

    // Parse based on what we're expecting
    if (conversation.awaitingResponse === 'vendor_creation') {
      return this.parseVendorInfo(input, conversation);
    }

    // Default to voice processing for complex inputs
    const audioBlob = await this.textToAudioBlob(input);
    return await this.voiceProcessor.processAudioBlob(audioBlob);
  }

  private handleGoBack(conversation: ConversationState): Partial<VoiceCommand> {
    // Go back to previous stage based on current stage
    switch (conversation.stage) {
      case 'confirming':
        if (conversation.batchContext) {
          conversation.stage = 'batch_confirmation';
        } else if (conversation.vendorCreationContext) {
          conversation.stage = 'vendor_creation';
        } else if (conversation.missingFields.length > 0) {
          conversation.stage = 'collecting';
        } else {
          conversation.stage = 'initial';
        }
        break;
      case 'batch_confirmation':
        if (conversation.vendorCreationContext) {
          conversation.stage = 'vendor_creation';
        } else if (conversation.missingFields.length > 0) {
          conversation.stage = 'collecting';
        } else {
          conversation.stage = 'initial';
        }
        break;
      case 'vendor_creation':
        if (conversation.missingFields.length > 0) {
          conversation.stage = 'collecting';
        } else {
          conversation.stage = 'initial';
        }
        break;
      case 'collecting':
        conversation.stage = 'initial';
        break;
      default:
        conversation.stage = 'initial';
    }
    conversation.awaitingResponse = null;
    return {};
  }

  private handleSkip(conversation: ConversationState): Partial<VoiceCommand> {
    switch (conversation.awaitingResponse) {
      case 'vendor_creation':
        conversation.stage = 'batch_confirmation';
        break;
      case 'batch_acceptance':
        conversation.stage = 'confirming';
        break;
      case 'user_input':
        // Skip current missing field
        if (conversation.missingFields.length > 0) {
          conversation.missingFields.shift();
          if (conversation.missingFields.length === 0) {
            conversation.stage = 'confirming';
          }
        }
        break;
      default:
        conversation.stage = 'confirming';
    }
    conversation.awaitingResponse = null;
    return {};
  }

  private parseVendorInfo(input: string, conversation: ConversationState): Partial<VoiceCommand> {
    const lowerInput = input.toLowerCase();
    
    // Extract location information
    const locationPatterns = [
      /(?:in|from|located in|based in)\s+([^,]+)/i,
      /([a-z\s]+)(?:\s+based|\s+location)/i,
      /(seattle|portland|alaska|california|vancouver|tacoma)/i
    ];
    
    let location: string | undefined;
    for (const pattern of locationPatterns) {
      const match = input.match(pattern);
      if (match) {
        location = match[1].trim();
        break;
      }
    }

    if (location && conversation.vendorCreationContext) {
      conversation.vendorCreationContext.detectedInfo = { location };
    }

    return {};
  }

  private async parseCorrection(input: string, conversation: ConversationState): Promise<Partial<VoiceCommand>> {
    // Use voice processor to parse the correction
    const audioBlob = await this.textToAudioBlob(input);
    return await this.voiceProcessor.processAudioBlob(audioBlob);
  }

  private async checkVendorExists(vendorName: string): Promise<boolean> {
    try {
      const result = await findVendorByName(vendorName);
      return result.success && result.data && result.data.length > 0;
    } catch {
      return false;
    }
  }

  private async createNewVendor(name: string, info: { location?: string; contactInfo?: string }): Promise<void> {
    const result = await createVendorFromVoice(name, info);
    if (!result.success) {
      throw new Error(result.error?.message || 'Failed to create vendor');
    }
  }

  private async generateBatchCodeForData(data: Partial<VoiceCommand>): Promise<string> {
    return await generateBatchCode(data.product_name, data.vendor_name);
  }

  private generateTransactionSummary(data: Partial<VoiceCommand>, batchContext?: { generatedCode: string }): string {
    let summary = `${data.event_type} ${data.quantity} ${data.unit} of ${data.product_name}`;
    
    if (data.vendor_name) {
      summary += ` from ${data.vendor_name}`;
    }
    
    if (data.customer_name) {
      summary += ` to ${data.customer_name}`;
    }
    
    if (batchContext) {
      summary += `, batch ${batchContext.generatedCode}`;
    }
    
    return summary;
  }

  private async submitInventoryTransaction(data: Partial<VoiceCommand>, batchContext?: { generatedCode: string }): Promise<any> {
    try {
      // First, resolve product ID from name
      let productId: string | null = null;
      if (data.product_name) {
        const productResult = await findProductByName(data.product_name);
        if (productResult.success && productResult.data && productResult.data.length > 0) {
          productId = productResult.data[0].id;
        } else {
          throw new Error(`Product not found: ${data.product_name}`);
        }
      }

      // Resolve vendor ID if needed
      let vendorId: string | null = null;
      if (data.vendor_name) {
        const vendorResult = await findVendorByName(data.vendor_name);
        if (vendorResult.success && vendorResult.data && vendorResult.data.length > 0) {
          vendorId = vendorResult.data[0].id;
        }
      }

      // Create the inventory event
      const eventData = {
        event_type: data.event_type,
        product_id: productId,
        quantity: data.quantity,
        notes: data.notes,
        metadata: {
          source: 'conversational-voice',
          batch_code: batchContext?.generatedCode,
          vendor_name: data.vendor_name,
          vendor_id: vendorId,
          customer_name: data.customer_name,
          unit: data.unit,
          confidence_score: data.confidence_score,
          conversation_completed: true,
          processing_method: data.processing_method,
          quality_grade: data.quality_grade,
          market_form: data.market_form,
          temperature: data.temperature,
          temperature_unit: data.temperature_unit,
          occurred_at: data.occurred_at || new Date().toISOString()
        },
        created_at: new Date().toISOString()
      };

      const { error } = await supabase
        .from('inventory_events')
        .insert(eventData);
      
      if (error) throw error;
      
      return { 
        success: true, 
        data: { 
          event_id: 'generated_id',
          batch_code: batchContext?.generatedCode,
          product_id: productId,
          vendor_id: vendorId
        }
      };
    } catch (error) {
      console.error('Error submitting inventory transaction:', error);
      throw error;
    }
  }

  // Mock method - in real implementation, this would convert text to audio blob for processing
  private async textToAudioBlob(text: string): Promise<Blob> {
    // For now, create a mock blob
    // In production, this might use text-to-speech or just pass text directly to a text processor
    return new Blob([text], { type: 'text/plain' });
  }

  // Public API methods
  public getConversationState(conversationId: string): ConversationState | undefined {
    return this.conversations.get(conversationId);
  }

  public resetConversation(conversationId: string): void {
    this.conversations.delete(conversationId);
  }

  public getAllActiveConversations(): ConversationState[] {
    return Array.from(this.conversations.values());
  }
}

export default ConversationalVoiceProcessor;
export type { ConversationState, ConversationTurn, ConversationResponse };