# TempStick API Service Documentation

## Overview

The TempStick service provides a robust integration with TempStick temperature monitoring sensors for the Seafood Manager application. It includes comprehensive error handling, rate limiting, retry logic, and mock data support for development.

## Architecture

```
TempStickService
├── TempStickApiClient (low-level API client)
├── Database Integration (Supabase)
├── Mock Data System (development/testing)
└── Configuration Management
```

## Quick Start

### Basic Usage

```typescript
import { tempStickService } from '@/lib/tempstick-service';

// Get all sensors
const sensors = await tempStickService.getSensors();

// Get latest readings for a sensor
const readings = await tempStickService.getLatestReadings('TS240001', 50);

// Sync all sensor data from TempStick API
const syncResult = await tempStickService.syncSensors();
```

### Data Source Selection

The service supports three data modes:

```typescript
// Use real API data only (throws errors if API fails)
tempStickService.useRealData();

// Use mock data only (for development/testing)
tempStickService.useMockData();

// Use automatic mode (real data with fallback to mock)
tempStickService.useAutoMode();
```

## Configuration

### Environment Variables

```env
# Required
VITE_TEMPSTICK_API_KEY=your_tempstick_api_key_here

# Optional
VITE_TEMPSTICK_BASE_URL=https://tempstickapi.com/api
VITE_USE_MOCK_TEMPSTICK_DATA=true
VITE_TEMPSTICK_RATE_LIMIT=60
VITE_TEMPSTICK_MAX_RETRIES=3
```

### Configuration Object

```typescript
import { loadTempStickConfig } from '@/lib/config/tempstick-config';

const config = loadTempStickConfig();
console.log(config.rateLimit.requestsPerMinute); // 60
```

## API Methods

### Core API Methods

#### `getSensors()`
Retrieve all sensors from TempStick API with data source selection.

```typescript
const sensors: TempStickSensor[] = await tempStickService.getSensors();
```

#### `getLatestReadings(sensorId, limit?)`
Get latest temperature readings for a specific sensor.

```typescript
const readings = await tempStickService.getLatestReadings('TS240001', 100);
```

#### `getSensorHealth(sensorId)`
Check sensor connectivity and health status.

```typescript
const health = await tempStickService.getSensorHealth('TS240001');
console.log(health.online, health.batteryLevel, health.signalStrength);
```

#### `getReadingsForPeriod(sensorId, startDate, endDate, limit?)`
Get historical readings within a date range.

```typescript
const startDate = new Date('2024-01-01');
const endDate = new Date('2024-01-02');
const readings = await apiClient.getReadingsForPeriod('TS240001', startDate, endDate);
```

### Sync Operations

#### `syncSensors()`
Synchronize sensor metadata from TempStick API to local database.

```typescript
const result: SyncResponse = await tempStickService.syncSensors();
console.log(`Synced ${result.syncedSensors} sensors`);
```

#### `syncAllTemperatureReadings()`
Synchronize temperature readings for all active sensors.

```typescript
const result = await tempStickService.syncAllTemperatureReadings();
console.log(`${result.newReadings} new readings, ${result.newAlerts} alerts`);
```

#### `scheduledSync()`
Complete sync operation (sensors + readings) for scheduled tasks.

```typescript
const result = await tempStickService.scheduledSync();
```

### Monitoring & Health

#### `performHealthCheck()`
Comprehensive health check of the entire system.

```typescript
const health: SystemHealth = await tempStickService.performHealthCheck();
console.log(health.tempstickApi.status); // 'healthy' | 'degraded' | 'down'
```

#### `getServiceMetrics()`
Get service performance metrics and sync status.

```typescript
const metrics = tempStickService.getServiceMetrics();
console.log(`API calls: ${metrics.api.totalRequests}`);
console.log(`Success rate: ${metrics.api.successfulRequests / metrics.api.totalRequests * 100}%`);
```

## Error Handling

### Error Types

```typescript
import { TempStickApiError, TempStickServiceError } from '@/lib/tempstick-service';

try {
  const sensors = await tempStickService.getSensors();
} catch (error) {
  if (error instanceof TempStickApiError) {
    // API-specific error (network, auth, rate limiting)
    console.log(`API Error: ${error.message} (${error.statusCode})`);
    console.log(`Retryable: ${error.retryable}`);
  } else if (error instanceof TempStickServiceError) {
    // Service logic error (database, validation)
    console.log(`Service Error: ${error.message} in ${error.operation}`);
  }
}
```

### Retry Logic

The service automatically retries failed requests with exponential backoff:

- **Max Retries**: 3 (configurable)
- **Base Delay**: 1 second
- **Max Delay**: 30 seconds
- **Backoff Multiplier**: 2

Rate-limited requests (HTTP 429) get special handling with `Retry-After` header support.

## Rate Limiting

The service enforces TempStick API rate limits:

- **60 requests per minute** (sliding window)
- **10 burst limit** for immediate requests
- Automatic queuing and throttling

```typescript
// Configure custom rate limits
const customService = new TempStickService({
  apiKey: 'your-key',
  rateLimit: {
    requestsPerMinute: 90,
    burstLimit: 15
  }
});
```

## Mock Data System

For development and testing without a real TempStick account:

### Automatic Mock Data

```typescript
// Environment variable controls default behavior
VITE_USE_MOCK_TEMPSTICK_DATA=true

// Or programmatically
tempStickService.useMockData();
```

### Mock Data Generation

```typescript
import { 
  generateMockTemperatureTrends,
  generateMockSystemHealth,
  mockSensors 
} from '@/lib/mock-tempstick-data';

// Generate 24 hours of temperature data
const trends = generateMockTemperatureTrends(24);

// Get mock system health
const health = generateMockSystemHealth();
```

## Database Integration

The service automatically syncs data with Supabase tables:

### Tables Used

- **`sensors`** - Sensor metadata and configuration
- **`temperature_readings`** - Temperature and humidity data
- **`temperature_alerts`** - Alert conditions and notifications
- **`storage_areas`** - Storage area definitions for HACCP compliance

### HACCP Compliance

Temperature readings are automatically checked against:

1. **Sensor Thresholds** - Individual sensor limits
2. **Storage Area Requirements** - HACCP critical control points
3. **Product Requirements** - Product-specific storage conditions

Violations generate alerts with appropriate severity levels.

## Performance Optimization

### Batch Processing

```typescript
// Get readings for multiple sensors efficiently
const sensorIds = ['TS240001', 'TS240002', 'TS240003'];
const results = await apiClient.getBatchLatestReadings(sensorIds);
```

### Request Optimization

- **Connection Pooling**: Automatic HTTP connection reuse
- **Concurrent Requests**: Parallel processing with rate limiting
- **Data Validation**: Client-side filtering of invalid readings
- **Incremental Sync**: Only sync new readings since last sync

## Testing

### Unit Tests

```bash
npm test tempstick
```

### Integration Tests

```typescript
// Test with mock data
tempStickService.useMockData();
const sensors = await tempStickService.getSensors();
expect(sensors.length).toBeGreaterThan(0);

// Test with real API (requires valid API key)
tempStickService.useRealData();
const health = await tempStickService.performHealthCheck();
expect(health.tempstickApi.status).toBe('healthy');
```

### Load Testing

```typescript
// Simulate high API load
const promises = Array.from({ length: 100 }, () => 
  tempStickService.getSensors()
);
const results = await Promise.allSettled(promises);
```

## Monitoring & Alerts

### Service Metrics

```typescript
const metrics = tempStickService.getServiceMetrics();

// Monitor these metrics:
console.log(`API Success Rate: ${metrics.api.successfulRequests / metrics.api.totalRequests * 100}%`);
console.log(`Average Response Time: ${metrics.api.averageResponseTime}ms`);
console.log(`Rate Limited Requests: ${metrics.api.rateLimitedRequests}`);
console.log(`Last Sync: ${metrics.sync.lastFullSync}`);
```

### Health Monitoring

```typescript
// Automatic health checks (every 5 minutes)
tempStickService.startHealthChecks();

// Manual health check
const health = await tempStickService.performHealthCheck();

// Alert conditions
if (health.sensors.offline > 0) {
  console.warn(`${health.sensors.offline} sensors are offline`);
}

if (health.alerts.critical > 0) {
  console.error(`${health.alerts.critical} critical temperature alerts`);
}
```

## Production Deployment

### Environment Setup

```bash
# Production environment variables
VITE_TEMPSTICK_API_KEY=your_production_api_key
VITE_USE_MOCK_TEMPSTICK_DATA=false
VITE_TEMPSTICK_RATE_LIMIT=90
```

### Scheduled Sync

Set up a scheduled task to sync data regularly:

```typescript
// Run every 15 minutes
setInterval(async () => {
  try {
    const result = await tempStickService.scheduledSync();
    console.log(`Sync completed: ${result.newReadings} new readings`);
  } catch (error) {
    console.error('Scheduled sync failed:', error);
  }
}, 15 * 60 * 1000);
```

### Error Monitoring

```typescript
// Log errors for monitoring
tempStickService.on('error', (error) => {
  // Send to error tracking service (Sentry, etc.)
  console.error('TempStick service error:', error);
});
```

## Troubleshooting

### Common Issues

#### "API Key Required" Error
- Check that `VITE_TEMPSTICK_API_KEY` is set in your environment
- Verify the API key is valid and active

#### Rate Limiting
- Monitor `rateLimitedRequests` metric
- Consider reducing sync frequency
- Check if multiple instances are sharing the same API key

#### Sensor Offline
- Check sensor battery levels and signal strength
- Verify sensor placement and network connectivity
- Review sensor calibration dates

#### Database Sync Failures
- Check Supabase connection and RLS policies
- Verify database schema matches expected structure
- Monitor database connection pool usage

### Debug Mode

```typescript
// Enable verbose logging
localStorage.setItem('tempstick-debug', 'true');

// Check service configuration
console.log(tempStickService.getServiceMetrics());

// Test API connectivity
const connectionTest = await apiClient.testConnection();
console.log('API Connection:', connectionTest);
```

### Support

For issues with the TempStick service:

1. Check the service metrics and health status
2. Review error logs for specific error types
3. Test with mock data to isolate API issues
4. Verify database connectivity and schema
5. Contact TempStick support for API-specific issues

## API Reference

### TempStickService Class

| Method | Parameters | Returns | Description |
|--------|------------|---------|-------------|
| `getSensors()` | - | `Promise<TempStickSensor[]>` | Get all sensors |
| `getLatestReadings(sensorId, limit?)` | `string, number?` | `Promise<TempStickReading[]>` | Get latest readings |
| `getSensorHealth(sensorId)` | `string` | `Promise<{online, batteryLevel, signalStrength}>` | Get sensor health |
| `syncSensors()` | - | `Promise<SyncResponse>` | Sync sensor metadata |
| `syncAllTemperatureReadings()` | - | `Promise<SyncResponse>` | Sync all readings |
| `performHealthCheck()` | - | `Promise<SystemHealth>` | Full system health check |
| `setDataMode(mode)` | `'real'\|'mock'\|'auto'` | `void` | Set data source mode |

### Configuration Types

See `types/tempstick.ts` for complete TypeScript definitions of all interfaces and types used by the service.