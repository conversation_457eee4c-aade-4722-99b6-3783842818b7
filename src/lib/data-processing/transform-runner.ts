// lib/data-processing/transform-runner.ts
import { transformSquareData } from './square-transform';
import { supabase } from '../supabase';

interface TransformResult {
  success: boolean;
  message: string;
  productsProcessed?: number;
}

export async function runSquareTransform(csvData: string): Promise<TransformResult> {
  try {
    // Transform the data
    const transformedProducts = await transformSquareData(csvData);
    
    // Get existing categories for matching (will be used for category mapping)
    const { data: categories } = await supabase
      .from('categories')
      .select('id, name');
    
    // TODO: Map categories to the transformed products using the categories data
    console.log('Available categories:', categories);
    
    // Insert products into database
    const { error } = await supabase
      .from('Products')
      .insert(transformedProducts)
      .select();

    if (error) throw error;

    return {
      success: true,
      message: 'Data imported successfully',
      productsProcessed: transformedProducts.length
    };
  } catch (error) {
    console.error('Error running Square transform:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to import data'
    };
  }
}
