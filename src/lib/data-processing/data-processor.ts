// lib/data-processing/square-transform.ts
import Papa from 'papaparse';
import { format } from 'date-fns';

export interface ProgressCallback {
  (progress: number): void;
}

interface SquareTransaction {
  'Transaction ID': string;
  'Date': string;
  'Discounts': string;
  'Gross Sales': string;
  'Total Collected': string;
  'Staff Name': string;
  'Customer ID'?: string;
  'Customer Name'?: string;
  'Discount Name'?: string;
}

interface SquareItem {
  'Transaction ID': string;
  'Date': string;
  'Category': string;
  'Qty': string;
  'SKU': string;
  'Item': string;
  'Gross Sales': string;
  'Discounts': string;
  'Unit'?: string;
  'Customer ID'?: string;
  'Customer Name'?: string;
}

interface TransformedData {
  'Date': string;
  'Category': string;
  'Qty': string;
  'SKU': string;
  'Product Name': string;
  'Gross Sales': string;
  'Discount Amount': string;
  'Net Sales': string;
  'Cogs': string;
  'Per Unit Cogs': string;
  'Customer ID': string;
  'Customer Name': string;
  'Discount Name': string;
  'Unit': string;
  'Count': string;
  'Notes': string;
  'Transaction ID': string;
  'Customer Paid': string;
  'Fulfilled By': string;
  'Sale Type': string;
  'Move To Balance Sheet': string;
  'Profit': string;
  'Margin': string;
  'Column 1': string;
  'Column 2': string;
}

export class SquareTransformer {
  private transactionMap: Map<string, SquareTransaction>;
  private itemsByTransaction: Map<string, SquareItem[]>;
  private cogsData: Map<string, number>;
  private onProgress: ProgressCallback;
  
  constructor(cogsData: {[key: string]: number}, onProgress?: ProgressCallback) {
    this.transactionMap = new Map();
    this.itemsByTransaction = new Map();
    this.cogsData = new Map(Object.entries(cogsData));
    this.onProgress = onProgress || (() => {});
  }
  
  private formatCurrency(amount: number): string {
    return amount === 0 ? '0' : `$${Math.abs(amount).toFixed(2)}`;
  }
  
  private formatDate(date: Date): string {
    return format(new Date(date), 'M/d/yyyy');
  }
  
  private parseCurrency(value: string): number {
    return parseFloat(value.replace(/[$,()\\-]/g, '')) || 0;
  }
  
  private calculateMargin(grossSales: number, cogs: number): string {
    if (grossSales === 0) return '0%';
    const margin = ((grossSales - cogs) / grossSales) * 100;
    return `${Math.round(margin)}%`;
  }
  
  async transformSquareData(itemsPath: string, transactionsPath: string) {
    // Read and parse files
    this.onProgress(10);
    const itemsData = await window.fs.readFile(itemsPath, { encoding: 'utf8' });
    const transactionsData = await window.fs.readFile(transactionsPath, { encoding: 'utf8' });
    
    this.onProgress(20);
    
    // Parse CSV data
    const items = Papa.parse(itemsData, {
      header: true,
      skipEmptyLines: true,
      dynamicTyping: true
    }).data;
    
    const transactions = Papa.parse(transactionsData, {
      header: true,
      skipEmptyLines: true,
      dynamicTyping: true
    }).data;
    
    this.onProgress(30);
    
    // Build lookup maps
    transactions.forEach(trans => {
      this.transactionMap.set(trans['Transaction ID'], trans);
    });
    
    items.forEach(item => {
      if (!this.itemsByTransaction.has(item['Transaction ID'])) {
        this.itemsByTransaction.set(item['Transaction ID'], []);
      }
      this.itemsByTransaction.get(item['Transaction ID'])?.push(item);
    });
    
    this.onProgress(50);
    
    // Transform data
    const transformedData = [];
    let processedCount = 0;
    const totalTransactions = this.itemsByTransaction.size;
    
    for (const [transId, items] of this.itemsByTransaction) {
      const transaction = this.transactionMap.get(transId);
      processedCount++;
      
      for (const item of items) {
        const qty = parseFloat(item.Qty) || 0;
        const grossSales = this.parseCurrency(item['Gross Sales']);
        const itemDiscount = this.parseCurrency(item.Discounts);
        
        // Calculate COGS
        const perUnitCogs = this.cogsData.get(item.SKU?.toString()) || 0;
        const totalCogs = perUnitCogs * qty;
        
        // Calculate net sales
        const transactionDiscount = this.parseCurrency(transaction.Discounts);
        const totalTransGross = this.parseCurrency(transaction['Gross Sales']);
        const discountShare = totalTransGross > 0 
          ? (grossSales / totalTransGross) * transactionDiscount 
          : 0;
        
        const netSales = grossSales - itemDiscount - discountShare;
        const profit = netSales - totalCogs;
        
        transformedData.push({
          'Date': this.formatDate(new Date(item.Date)),
          'Category': item.Category || '',
          'Qty': qty.toString(),
          'SKU': item.SKU?.toString() || '',
          'Product Name': item.Item,
          'Gross Sales': this.formatCurrency(grossSales),
          'Discount Amount': this.formatCurrency(itemDiscount + discountShare),
          'Net Sales': this.formatCurrency(netSales),
          'Cogs': this.formatCurrency(totalCogs),
          'Per Unit Cogs': this.formatCurrency(perUnitCogs),
          'Customer ID': item['Customer ID'] || transaction['Customer ID'] || '',
          'Customer Name': item['Customer Name'] || transaction['Customer Name'] || '',
          'Discount Name': transaction['Discount Name'] || '',
          'Unit': item.Unit || 'lb',
          'Count': '1',
          'Notes': '',
          'Transaction ID': transId,
          'Customer Paid': transaction['Total Collected'] ? 'Yes' : 'No',
          'Fulfilled By': transaction['Staff Name'] || 'PCS',
          'Sale Type': 'Retail',
          'Move To Balance Sheet': 'No',
          'Profit': this.formatCurrency(profit),
          'Margin': this.calculateMargin(netSales, totalCogs),
          'Column 1': '',
          'Column 2': ''
        });
      }
      
      this.onProgress(50 + (processedCount / totalTransactions) * 40);
    }
    
    return transformedData;
  }
  
  async exportToCSV(transformedData: TransformedData[], outputPath: string) {
    const csv = Papa.unparse(transformedData, {
      quotes: true,
      header: true
    });
    
    await window.fs.writeFile(outputPath, csv, 'utf8');
  }
}
