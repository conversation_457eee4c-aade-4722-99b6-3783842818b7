import { ImportConfig } from '@/components/import/types';

export const importSources: Record<string, ImportConfig> = {
  default: {
    requiredFields: ['date', 'name', 'sku', 'vendor', 'unit'],
    optionalFields: [
      'category',
      'sub_category',
      'stock',
      'min_stock',
      'price',
      'cost',
      'gross_amount',
      'net_amount',
      'supplier_id',
      'expiry_date',
      'storage_temp',
      'handling_instructions',
      'event_type',
      'customer',
      'channel',
      'total_amount',
      'unit_price',
      'unit_cost',
      'quality_status',
      'batch_number'
    ],
    defaultValues: {
      event_type: 'receiving'
    }
  }
}; 