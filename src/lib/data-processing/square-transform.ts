interface TransformedProduct {
  name: string;
  price: number;
  category: string | null;
  sku: string;
  condition: 'fresh' | 'frozen' | 'other';  // Added required field
  amount: number;  // Added required field
}

function parseCSV(csv: string): string[][] {
  const lines = csv.split(/\r\n|\n/);
  return lines.map(line => {
    let inQuotes = false;
    let field = '';
    const fields: string[] = [];
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      if (char === '"') {
        inQuotes = !inQuotes;
        continue;
      }
      if (char === ',' && !inQuotes) {
        fields.push(field.trim());
        field = '';
        continue;
      }
      field += char;
    }
    fields.push(field.trim());
    return fields;
  }).filter(line => line.some(field => field.length > 0));
}

export async function transformSquareData(csvData: string): Promise<TransformedProduct[]> {
  try {
    const rows = parseCSV(csvData);
    if (rows.length < 2) {
      throw new Error('CSV file is empty or missing data');
    }

    const headers = rows[0].map(h => h.toLowerCase());
    const nameIndex = headers.findIndex(h => h.includes('item name') || h.includes('name'));
    const priceIndex = headers.findIndex(h => h.includes('price'));
    const skuIndex = headers.findIndex(h => h.includes('sku'));
    const categoryIndex = headers.findIndex(h => h.includes('category'));
    const conditionIndex = headers.findIndex(h => h.includes('condition'));
    const amountIndex = headers.findIndex(h => h.includes('amount') || h.includes('quantity'));

    if (nameIndex === -1 || priceIndex === -1) {
      throw new Error('Required columns (Item Name, Price) not found in CSV');
    }

    return rows.slice(1)
      .filter(row => row.length >= Math.max(nameIndex, priceIndex, skuIndex) + 1)
      .map(product => {
        // Parse condition from CSV or default to 'other'
        let condition: 'fresh' | 'frozen' | 'other' = 'other';
        if (conditionIndex !== -1) {
          const rawCondition = product[conditionIndex].toLowerCase();
          if (rawCondition === 'fresh' || rawCondition === 'frozen') {
            condition = rawCondition;
          }
        }

        // Parse amount from CSV or default to 0
        let amount = 0;
        if (amountIndex !== -1) {
          amount = parseFloat(product[amountIndex].replace(/[^0-9.-]+/g, '')) || 0;
        }

        return {
          name: product[nameIndex],
          price: parseFloat(product[priceIndex].replace(/[^0-9.-]+/g, '')) || 0,
          category: categoryIndex !== -1 ? product[categoryIndex] : null,
          sku: skuIndex !== -1 ? product[skuIndex] : '',
          condition,
          amount
        };
      });
  } catch (error) {
    console.error('Error transforming Square data:', error);
    throw error;
  }
}
