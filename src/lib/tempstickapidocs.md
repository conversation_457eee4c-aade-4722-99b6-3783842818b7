```
# Temp Stick API Documentation
**1.0.0**

## Getting Started

Warning: to use the Temp Stick API you will need experience with programming and using REST APIs.

### Working with the Temp Stick API

#### X-API-KEY

All requests made by your application must include the `X-API-Key` HTTP header.

`X-API-KEY: YOUR_ACCCOUNT_API_KEY`

#### GZIP

Data from the API is compressed using gzip and encoded as JSON, your application must support decompressing this format.

#### Temperature

All readings and settings regarding temperature are presented in **Celsius**.

The formula converting Celsius to Fahrenheit is: `(CELSIUS * 1.8) + 32`

Example function in PHP:

```
function celsius_to_fahrenheit ($celsius, $round = 1) {
    return round(($celsius * 1.8) + 32, $round);
}
```

#### Dates and time

All timestamps returned by the API are in the UTC time zone (denoted with "Z" or time offset +00:00).

## Getting Your API Key

1. Log into your Temp Stick account
2. Go to the [Account Page](https://temperaturestick.com/sensors/account)
3. Select the "Developers" tab.
4. Click "Show Key" to reveal your API Key

### Example GET request:

Below are three different ways to make a request to the API: PHP, Javascript and a basic CURL request.

**PHP Example**
```
// initialize curl
$curl = curl_init();
curl_setopt_array($curl, array(
    CURLOPT_URL => 'https://tempstickapi.com/api/v1/sensors/all',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => '',
    CURLOPT_TIMEOUT => 0,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_CUSTOMREQUEST => 'GET',
    CURLOPT_HTTPHEADER => array(
        'X-API-KEY: YOUR_API_KEY'
    ),
));
$response = curl_exec($curl);
curl_close($curl);
echo $response;
```

**Javascript Example**
```
// set the header and include the X-API-KEY
var requestHeaders = new Headers();
requestHeaders.append("X-API-KEY", "YOUR_API_KEY");
var requestOptions = {
  method: 'GET',
  headers: requestHeaders,
  redirect: 'follow'
};
fetch("https://tempstickapi.com/api/v1/sensors/all", requestOptions)
  .then(response => response.text())
  .then(result => console.log(result))
  .catch(error => console.log('error', error));
```

**CURL Example**
```
curl --location --request GET 'https://tempstickapi.com/api/v1/sensors/all' -A "" --header 'X-API-KEY: YOUR_API_KEY'
```
A note for CURL: Commandline CURL's default User-Agent header includes `curl` which is automatically blocked by the server firewall resulting in a "406 Not Acceptable" error. Use the `-A` parameter to change the default user agent. `-A ""` is allowed.

### Example POST (update) request:

When changing settings via POST endpoints, the data should be an HTTP POST using `multipart/form-data`.

**PHP Example**
```
$curl = curl_init();
curl_setopt_array($curl, array(
  CURLOPT_URL => 'https://tempstickapi.com/api/v1/sensor/YOUR_SENSOR_ID',
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_CUSTOMREQUEST => 'POST',
  CURLOPT_POSTFIELDS => [
    'sensor_name' => 'Test',
    'send_interval' => '3600',
    'alert_interval' => '1800',
    'use_alert_interval' => '0',
    'alert_temp_below' => '',
    'alert_temp_above' => '',
    'alert_humidity_below' => '',
    'alert_humidity_above' => '',
    'connection_sensitivity' => '3',
    'use_offset' => '0',
    'temp_offset' => '0',
    'humidity_offset' => '0',
  ],
  CURLOPT_HTTPHEADER => array(
    'X-API-KEY: YOUR_API_KEY',
  ),
));
$response = curl_exec($curl);
curl_close($curl);
echo $response;
```
```
Respond "next" for the next segment.

[1](comet://newtab/)

```
---

# API Endpoints

## Alerts

### Get Alert

`GET /api/v1/alert/{alert_id}`

Returns a single alert by its ID.

#### Example Response

```
{
    "success": true,
    "alert": {
        "alert_id": "1234",
        "sensor_id": "5678",
        "alert_type": "temperature",
        "condition": "above",
        "threshold": 30,
        "enabled": true,
        ...
    }
}
```

### Get Alerts

`GET /api/v1/alerts`

Returns all alerts for your account.

#### Example Response

```
{
    "success": true,
    "alerts": [
        {
            "alert_id": "1234",
            "sensor_id": "5678",
            "alert_type": "temperature",
            "condition": "above",
            "threshold": 30,
            "enabled": true
        },
        ...
    ]
}
```

### Get Sensor Notifications

`GET /api/v1/notifications/sensor/{sensor_id}`

Returns a list of all notifications for a specific sensor.

### Get User Notifications

`GET /api/v1/notifications/user`

Returns all notifications across all sensors for your account.

---

## Sensors

### Get Sensors

`GET /api/v1/sensors/all`

Returns all sensors for the logged-in account.

#### Example Response

```
{
    "success": true,
    "sensors": [
        {
            "sensor_id": "12345678901234EXAMPLE",
            "sensor_name": "Warehouse",
            "latest_reading": {
                "timestamp": "2020-10-05T19:08:12Z",
                "temperature": 22.5,
                "humidity": 55,
                "signal": -55
            }
        },
        ...
    ]
}
```

### Get Sensor

`GET /api/v1/sensor/{sensor_id}`

Returns a single sensor by its ID.

### Get Readings

`GET /api/v1/readings/{sensor_id}/{count}`

Returns the most recent `{count}` readings for a specific sensor.

---
```
---

### Update Sensor Settings

`POST /api/v1/sensor/{sensor_id}`

Updates settings for a specific sensor. Data should be posted as multipart/form-data.

#### Example Fields

- `sensor_name`: Name for the sensor.
- `send_interval`: How often the sensor reports (in seconds).
- `alert_interval`: Minimum alert interval (in seconds).
- `use_alert_interval`: Set to `1` to enable alert interval, otherwise `0`.
- `alert_temp_below` / `alert_temp_above`: Set threshold temperatures.
- `alert_humidity_below` / `alert_humidity_above`: Set threshold humidities.
- `connection_sensitivity`: Connection status sensitivity.
- `use_offset`: Set to `1` to use temperature/humidity offset.
- `temp_offset` / `humidity_offset`: Offset values.

#### Example Request (POST fields)

```
{
    "sensor_name": "Freezer",
    "send_interval": "1800",
    "alert_temp_below": "-18",
    "alert_temp_above": "-10",
    "connection_sensitivity": "2"
}
```

#### Example Response

```
{
    "success": true,
    "sensor": {
        "sensor_id": "YOUR_SENSOR_ID",
        "sensor_name": "Freezer",
        "settings_updated": true
    }
}
```

---

## User

### Get Current User

`GET /api/v1/user`

Returns information about the currently authenticated user.

#### Example Response

```
{
    "success": true,
    "user": {
        "user_id": "YOUR_USER_ID",
        "full_name": "John Smith",
        "email": "<EMAIL>"
    }
}
```

### Get Email Reports

`GET /api/v1/user/reports`

Returns all scheduled email reports set up for the account.

---

```
---

### Get Timezones

`GET /api/v1/user/timezones`

Returns the full list of valid timezones.

#### Example Response

```
{
    "success": true,
    "timezones": [
        "Pacific/Honolulu",
        "America/Anchorage",
        "America/Los_Angeles",
        // ... and so on
    ]
}
```

### Update User Display Preferences

`POST /api/v1/user/display`

Updates user display preferences.

#### Example Fields

- `temperature_unit`: "C" or "F"
- `time_format`: "12h" or "24h"
- `humidity_unit`: "%" (currently only supported value)

#### Example Request (POST fields)

```
{
    "temperature_unit": "F",
    "time_format": "12h"
}
```

#### Example Response

```
{
    "success": true,
    "preferences_updated": true
}
```

---

## Examples

### Reading Sensor Data

#### Example: Get last 10 readings for a sensor (Javascript)

```
let requestHeaders = new Headers();
requestHeaders.append("X-API-KEY", "YOUR_API_KEY");
let requestOptions = {
  method: 'GET',
  headers: requestHeaders,
  redirect: 'follow'
};
fetch("https://tempstickapi.com/api/v1/readings/YOUR_SENSOR_ID/10", requestOptions)
  .then(response => response.json())
  .then(result => console.log(result))
  .catch(error => console.log('error', error));
```

---
```
---

### Example: Update sensor settings (PHP)

```
$curl = curl_init();
curl_setopt_array($curl, array(
  CURLOPT_URL => 'https://tempstickapi.com/api/v1/sensor/YOUR_SENSOR_ID',
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_CUSTOMREQUEST => 'POST',
  CURLOPT_POSTFIELDS => [
    'sensor_name' => 'Lab',
    'alert_temp_above' => '40'
  ],
  CURLOPT_HTTPHEADER => array(
    'X-API-KEY: YOUR_API_KEY',
  ),
));
$response = curl_exec($curl);
curl_close($curl);
echo $response;
```

---

## Useful Code

### Example: Convert Celsius to Fahrenheit in Python

```
def celsius_to_fahrenheit(celsius, round_to=1):
    return round(celsius * 1.8 + 32, round_to)
```

### Example: Parse readings into a list in Javascript

```
const readings = [{
    "timestamp": "2020-10-05T19:08:12Z",
    "temperature": 22.5,
    "humidity": 55,
    "signal": -55
  },
  {
    "timestamp": "2020-10-05T18:02:44Z",
    "temperature": 22.6,
    "humidity": 54,
    "signal": -53
  }];
const temps = readings.map(r => r.temperature);
console.log(temps);
```

---

**End of Temp Stick API Documentation**

