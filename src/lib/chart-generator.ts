/**
 * Advanced Chart Generation Utilities for Temperature Reports
 * 
 * Professional chart generation system optimized for temperature monitoring data.
 * Creates high-quality charts for PDF reports with HACCP compliance visualization.
 */

import type {
  TemperatureReading,
  TemperatureAlert,
  Sensor,
  TemperatureReportData
} from '../types/tempstick';

export interface ChartDataPoint {
  x: number | string | Date;
  y: number;
  label?: string;
  color?: string;
  metadata?: Record<string, unknown>;
}

export interface ChartSeries {
  name: string;
  data: ChartDataPoint[];
  color: string;
  type?: 'line' | 'area' | 'scatter';
  yAxisId?: string;
}

export interface ChartAxis {
  id: string;
  label: string;
  unit?: string;
  min?: number;
  max?: number;
  tickInterval?: number;
  format?: 'number' | 'temperature' | 'datetime' | 'percentage';
  position?: 'left' | 'right' | 'top' | 'bottom';
}

export interface AlertThreshold {
  value: number;
  label: string;
  color: string;
  type: 'min' | 'max' | 'target';
  dashStyle?: 'solid' | 'dashed' | 'dotted';
}

export interface ChartConfiguration {
  title: string;
  subtitle?: string;
  width: number;
  height: number;
  type: 'line' | 'bar' | 'pie' | 'scatter' | 'heatmap' | 'combo';
  series: ChartSeries[];
  xAxis: ChartAxis;
  yAxes: ChartAxis[];
  thresholds?: AlertThreshold[];
  annotations?: ChartAnnotation[];
  styling: ChartStyling;
  interactivity?: {
    tooltip: boolean;
    zoom: boolean;
    pan: boolean;
    legend: boolean;
  };
}

export interface ChartAnnotation {
  type: 'text' | 'arrow' | 'box' | 'line';
  x: number | string;
  y?: number;
  text: string;
  color: string;
  fontSize?: number;
  backgroundColor?: string;
}

export interface ChartStyling {
  backgroundColor: string;
  gridColor: string;
  textColor: string;
  primaryColor: string;
  secondaryColor: string;
  alertColor: string;
  warningColor: string;
  successColor: string;
  font: {
    family: string;
    size: number;
    weight: 'normal' | 'bold';
  };
  margins: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
}

export interface HeatmapData {
  hour: number;
  day: string;
  temperature: number;
  sensor: string;
  alertLevel: 'normal' | 'warning' | 'critical';
}

/**
 * Advanced Chart Generator for Temperature Data
 */
export class ChartGenerator {
  private static instance: ChartGenerator;
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;
  private defaultStyling: ChartStyling;

  constructor() {
    // Create off-screen canvas for high-quality rendering
    this.canvas = document.createElement('canvas');
    this.ctx = this.canvas.getContext('2d')!;
    
    // Enable high DPI rendering
    const devicePixelRatio = window.devicePixelRatio || 1;
    this.canvas.style.width = '800px';
    this.canvas.style.height = '400px';
    this.canvas.width = 800 * devicePixelRatio;
    this.canvas.height = 400 * devicePixelRatio;
    this.ctx.scale(devicePixelRatio, devicePixelRatio);

    this.defaultStyling = this.createDefaultStyling();
  }

  static getInstance(): ChartGenerator {
    if (!ChartGenerator.instance) {
      ChartGenerator.instance = new ChartGenerator();
    }
    return ChartGenerator.instance;
  }

  /**
   * Generate temperature trend chart
   */
  async generateTemperatureTrendChart(
    sensorData: TemperatureReportData['sensorData'],
    options: {
      timeRange: 'hour' | 'day' | 'week' | 'month';
      showThresholds: boolean;
      showAlerts: boolean;
      sensors?: string[];
      width?: number;
      height?: number;
    }
  ): Promise<string> {
    const config = this.createTemperatureTrendConfig(sensorData, options);
    return this.renderChart(config);
  }

  /**
   * Generate compliance heatmap
   */
  async generateComplianceHeatmap(
    sensorData: TemperatureReportData['sensorData'],
    options: {
      period: 'week' | 'month';
      width?: number;
      height?: number;
    }
  ): Promise<string> {
    const heatmapData = this.prepareHeatmapData(sensorData, options.period);
    const config = this.createHeatmapConfig(heatmapData, options);
    return this.renderChart(config);
  }

  /**
   * Generate alert distribution chart
   */
  async generateAlertDistributionChart(
    alerts: TemperatureAlert[],
    options: {
      groupBy: 'sensor' | 'type' | 'severity' | 'time';
      chartType: 'pie' | 'bar' | 'line';
      width?: number;
      height?: number;
    }
  ): Promise<string> {
    const config = this.createAlertDistributionConfig(alerts, options);
    return this.renderChart(config);
  }

  /**
   * Generate sensor performance comparison
   */
  async generateSensorComparisonChart(
    sensorData: TemperatureReportData['sensorData'],
    metrics: ('average' | 'reliability' | 'alerts' | 'variance')[],
    options: {
      width?: number;
      height?: number;
    }
  ): Promise<string> {
    const config = this.createSensorComparisonConfig(sensorData, metrics, options);
    return this.renderChart(config);
  }

  /**
   * Generate HACCP compliance timeline
   */
  async generateHACCPTimelineChart(
    haccpData: TemperatureReportData['haccpCompliance'],
    alerts: TemperatureAlert[],
    options: {
      showViolations: boolean;
      showCorrections: boolean;
      width?: number;
      height?: number;
    }
  ): Promise<string> {
    const config = this.createHACCPTimelineConfig(haccpData, alerts, options);
    return this.renderChart(config);
  }

  /**
   * Generate multi-axis combo chart
   */
  async generateComboChart(
    temperatureData: ChartSeries[],
    humidityData: ChartSeries[],
    alertData: ChartSeries[],
    options: {
      width?: number;
      height?: number;
    }
  ): Promise<string> {
    const config = this.createComboChartConfig(temperatureData, humidityData, alertData, options);
    return this.renderChart(config);
  }

  /**
   * Create statistical distribution chart
   */
  async generateDistributionChart(
    readings: TemperatureReading[],
    options: {
      type: 'histogram' | 'box-plot' | 'violin';
      bins?: number;
      width?: number;
      height?: number;
    }
  ): Promise<string> {
    const config = this.createDistributionConfig(readings, options);
    return this.renderChart(config);
  }

  // Configuration creators

  private createTemperatureTrendConfig(
    sensorData: TemperatureReportData['sensorData'],
    options: {
      timeRange: 'hour' | 'day' | 'week' | 'month';
      showThresholds: boolean;
      showAlerts: boolean;
      sensors?: string[];
      width?: number;
      height?: number;
    }
  ): ChartConfiguration {
    const series: ChartSeries[] = [];
    const colors = this.generateColorPalette(sensorData.length);

    sensorData.forEach((sensor, index) => {
      if (options.sensors && !options.sensors.includes(sensor.sensor.id)) {
        return;
      }

      const data: ChartDataPoint[] = sensor.readings.map(reading => ({
        x: new Date(reading.reading_timestamp),
        y: reading.temperature,
        metadata: {
          sensorId: sensor.sensor.id,
          sensorName: sensor.sensor.name,
          humidity: reading.humidity,
          alert: reading.alert_triggered
        }
      }));

      series.push({
        name: sensor.sensor.name,
        data: this.aggregateDataByTimeRange(data, options.timeRange),
        color: colors[index],
        type: 'line'
      });
    });

    const thresholds: AlertThreshold[] = [];
    if (options.showThresholds) {
      // Add common temperature thresholds
      thresholds.push(
        { value: 32, label: 'Freezing Point', color: '#3b82f6', type: 'min', dashStyle: 'dashed' },
        { value: 40, label: 'Refrigeration Max', color: '#f59e0b', type: 'max', dashStyle: 'solid' }
      );
    }

    return {
      title: 'Temperature Trends',
      subtitle: `${this.formatTimeRange(options.timeRange)} Analysis`,
      width: options.width || 800,
      height: options.height || 400,
      type: 'line',
      series,
      xAxis: {
        id: 'time',
        label: 'Time',
        format: 'datetime'
      },
      yAxes: [{
        id: 'temperature',
        label: 'Temperature',
        unit: '°F',
        format: 'temperature',
        position: 'left'
      }],
      thresholds,
      styling: this.defaultStyling,
      interactivity: {
        tooltip: true,
        zoom: true,
        pan: true,
        legend: true
      }
    };
  }

  private createHeatmapConfig(data: HeatmapData[], options: {
      period: 'week' | 'month';
      width?: number;
      height?: number;
    }): ChartConfiguration {
    const series: ChartSeries[] = [{
      name: 'Temperature Heatmap',
      data: data.map(point => ({
        x: point.hour,
        y: point.day,
        label: `${point.temperature.toFixed(1)}°F`,
        color: this.getHeatmapColor(point.temperature, point.alertLevel),
        metadata: point
      })),
      color: '#3b82f6'
    }];

    return {
      title: 'Temperature Compliance Heatmap',
      subtitle: `${options.period} View - Hourly Temperature Distribution`,
      width: options.width || 900,
      height: options.height || 500,
      type: 'heatmap',
      series,
      xAxis: {
        id: 'hour',
        label: 'Hour of Day',
        min: 0,
        max: 23,
        tickInterval: 2
      },
      yAxes: [{
        id: 'day',
        label: 'Day',
        format: 'datetime',
        position: 'left'
      }],
      styling: this.defaultStyling
    };
  }

  private createAlertDistributionConfig(alerts: TemperatureAlert[], options: {
      groupBy: 'sensor' | 'type' | 'severity' | 'time';
      chartType: 'pie' | 'bar' | 'line';
      width?: number;
      height?: number;
    }): ChartConfiguration {
    let data: ChartDataPoint[] = [];

    switch (options.groupBy) {
      case 'sensor':
        data = this.groupAlertsBySensor(alerts);
        break;
      case 'type':
        data = this.groupAlertsByType(alerts);
        break;
      case 'severity':
        data = this.groupAlertsBySeverity(alerts);
        break;
      case 'time':
        data = this.groupAlertsByTime(alerts);
        break;
    }

    const series: ChartSeries[] = [{
      name: 'Alert Distribution',
      data,
      color: this.defaultStyling.alertColor
    }];

    return {
      title: 'Alert Distribution Analysis',
      subtitle: `Grouped by ${options.groupBy}`,
      width: options.width || 600,
      height: options.height || 400,
      type: options.chartType,
      series,
      xAxis: {
        id: 'category',
        label: this.capitalizeFirst(options.groupBy)
      },
      yAxes: [{
        id: 'count',
        label: 'Number of Alerts',
        format: 'number',
        position: 'left'
      }],
      styling: this.defaultStyling,
      interactivity: {
        tooltip: true,
        zoom: false,
        pan: false,
        legend: options.chartType === 'pie'
      }
    };
  }

  private createSensorComparisonConfig(
    sensorData: TemperatureReportData['sensorData'],
    metrics: ('average' | 'reliability' | 'alerts' | 'variance')[],
    options: {
      width?: number;
      height?: number;
    }
  ): ChartConfiguration {
    const series: ChartSeries[] = [];
    const colors = this.generateColorPalette(metrics.length);

    metrics.forEach((metric, index) => {
      const data: ChartDataPoint[] = sensorData.map(sensor => ({
        x: sensor.sensor.name,
        y: this.calculateSensorMetric(sensor, metric),
        label: sensor.sensor.name,
        metadata: { metric, sensorId: sensor.sensor.id }
      }));

      series.push({
        name: this.formatMetricName(metric),
        data,
        color: colors[index],
        yAxisId: metric === 'reliability' ? 'percentage' : 'primary'
      });
    });

    const yAxes: ChartAxis[] = [{
      id: 'primary',
      label: 'Value',
      format: 'number',
      position: 'left'
    }];

    if (metrics.includes('reliability')) {
      yAxes.push({
        id: 'percentage',
        label: 'Reliability (%)',
        format: 'percentage',
        position: 'right',
        min: 0,
        max: 100
      });
    }

    return {
      title: 'Sensor Performance Comparison',
      subtitle: 'Multi-metric analysis across all sensors',
      width: options.width || 800,
      height: options.height || 500,
      type: 'bar',
      series,
      xAxis: {
        id: 'sensor',
        label: 'Sensors'
      },
      yAxes,
      styling: this.defaultStyling,
      interactivity: {
        tooltip: true,
        zoom: false,
        pan: false,
        legend: true
      }
    };
  }

  private createHACCPTimelineConfig(
    haccpData: TemperatureReportData['haccpCompliance'],
    alerts: TemperatureAlert[],
    options: {
      showViolations: boolean;
      showCorrections: boolean;
      width?: number;
      height?: number;
    }
  ): ChartConfiguration {
    const series: ChartSeries[] = [];

    // Compliance rate over time
    const complianceData: ChartDataPoint[] = haccpData.map((area, index) => ({
      x: area.storageArea.name,
      y: area.complianceRate,
      color: area.complianceRate >= 95 ? this.defaultStyling.successColor : 
             area.complianceRate >= 90 ? this.defaultStyling.warningColor : 
             this.defaultStyling.alertColor,
      metadata: area
    }));

    series.push({
      name: 'HACCP Compliance Rate',
      data: complianceData,
      color: this.defaultStyling.primaryColor
    });

    // Violations over time if requested
    if (options.showViolations) {
      const violationData: ChartDataPoint[] = haccpData.map(area => ({
        x: area.storageArea.name,
        y: area.violationsCount,
        color: this.defaultStyling.alertColor,
        metadata: area
      }));

      series.push({
        name: 'Violations Count',
        data: violationData,
        color: this.defaultStyling.alertColor,
        yAxisId: 'violations'
      });
    }

    const yAxes: ChartAxis[] = [{
      id: 'compliance',
      label: 'Compliance Rate (%)',
      format: 'percentage',
      position: 'left',
      min: 0,
      max: 100
    }];

    if (options.showViolations) {
      yAxes.push({
        id: 'violations',
        label: 'Number of Violations',
        format: 'number',
        position: 'right',
        min: 0
      });
    }

    return {
      title: 'HACCP Compliance Timeline',
      subtitle: 'Storage area compliance analysis',
      width: options.width || 800,
      height: options.height || 400,
      type: 'combo',
      series,
      xAxis: {
        id: 'area',
        label: 'Storage Areas'
      },
      yAxes,
      styling: this.defaultStyling,
      interactivity: {
        tooltip: true,
        zoom: false,
        pan: false,
        legend: true
      }
    };
  }

  private createComboChartConfig(
    temperatureData: ChartSeries[],
    humidityData: ChartSeries[],
    alertData: ChartSeries[],
    options: {
      width?: number;
      height?: number;
    }
  ): ChartConfiguration {
    const series: ChartSeries[] = [
      ...temperatureData.map(s => ({ ...s, yAxisId: 'temperature' })),
      ...humidityData.map(s => ({ ...s, yAxisId: 'humidity' })),
      ...alertData.map(s => ({ ...s, yAxisId: 'alerts', type: 'scatter' as const }))
    ];

    return {
      title: 'Multi-Parameter Environmental Monitoring',
      subtitle: 'Temperature, humidity, and alert correlation',
      width: options.width || 1000,
      height: options.height || 600,
      type: 'combo',
      series,
      xAxis: {
        id: 'time',
        label: 'Time',
        format: 'datetime'
      },
      yAxes: [
        {
          id: 'temperature',
          label: 'Temperature (°F)',
          format: 'temperature',
          position: 'left'
        },
        {
          id: 'humidity',
          label: 'Humidity (%)',
          format: 'percentage',
          position: 'right',
          min: 0,
          max: 100
        },
        {
          id: 'alerts',
          label: 'Alert Level',
          format: 'number',
          position: 'right',
          min: 0,
          max: 3
        }
      ],
      styling: this.defaultStyling,
      interactivity: {
        tooltip: true,
        zoom: true,
        pan: true,
        legend: true
      }
    };
  }

  private createDistributionConfig(readings: TemperatureReading[], options: {
      type: 'histogram' | 'box-plot' | 'violin';
      bins?: number;
      width?: number;
      height?: number;
    }): ChartConfiguration {
    const temperatures = readings.map(r => r.temperature);
    let data: ChartDataPoint[] = [];

    switch (options.type) {
      case 'histogram':
        data = this.createHistogramData(temperatures, options.bins || 20);
        break;
      case 'box-plot':
        data = this.createBoxPlotData(temperatures);
        break;
      case 'violin':
        data = this.createViolinPlotData(temperatures);
        break;
    }

    const series: ChartSeries[] = [{
      name: 'Temperature Distribution',
      data,
      color: this.defaultStyling.primaryColor
    }];

    return {
      title: `Temperature ${this.capitalizeFirst(options.type.replace('-', ' '))}`,
      subtitle: `Statistical distribution of ${readings.length} temperature readings`,
      width: options.width || 700,
      height: options.height || 400,
      type: options.type === 'histogram' ? 'bar' : 'scatter',
      series,
      xAxis: {
        id: 'temperature',
        label: 'Temperature (°F)',
        format: 'temperature'
      },
      yAxes: [{
        id: 'frequency',
        label: options.type === 'histogram' ? 'Frequency' : 'Density',
        format: 'number',
        position: 'left'
      }],
      styling: this.defaultStyling
    };
  }

  // Chart rendering

  private async renderChart(config: ChartConfiguration): Promise<string> {
    // Set canvas dimensions
    this.canvas.width = config.width;
    this.canvas.height = config.height;

    // Clear canvas
    this.ctx.clearRect(0, 0, config.width, config.height);

    // Draw background
    this.ctx.fillStyle = config.styling.backgroundColor;
    this.ctx.fillRect(0, 0, config.width, config.height);

    // Calculate chart area
    const chartArea = {
      x: config.styling.margins.left,
      y: config.styling.margins.top,
      width: config.width - config.styling.margins.left - config.styling.margins.right,
      height: config.height - config.styling.margins.top - config.styling.margins.bottom
    };

    // Draw title and subtitle
    this.drawTitle(config.title, config.subtitle, config.styling);

    // Draw axes
    this.drawAxes(config.xAxis, config.yAxes, chartArea, config.styling);

    // Draw grid
    this.drawGrid(chartArea, config.styling);

    // Draw thresholds
    if (config.thresholds) {
      this.drawThresholds(config.thresholds, chartArea, config.yAxes[0], config.styling);
    }

    // Draw data series
    for (const series of config.series) {
      await this.drawSeries(series, config.type, chartArea, config.xAxis, config.yAxes, config.styling);
    }

    // Draw annotations
    if (config.annotations) {
      this.drawAnnotations(config.annotations, chartArea, config.styling);
    }

    // Draw legend
    if (config.interactivity?.legend && config.series.length > 1) {
      this.drawLegend(config.series, config.styling);
    }

    return this.canvas.toDataURL('image/png', 0.95);
  }

  // Drawing methods

  private drawTitle(title: string, subtitle?: string, styling: ChartStyling): void {
    this.ctx.textAlign = 'center';
    this.ctx.fillStyle = styling.textColor;

    // Main title
    this.ctx.font = `bold ${styling.font.size + 4}px ${styling.font.family}`;
    this.ctx.fillText(title, this.canvas.width / 2, 30);

    // Subtitle
    if (subtitle) {
      this.ctx.font = `normal ${styling.font.size}px ${styling.font.family}`;
      this.ctx.fillStyle = this.adjustColorOpacity(styling.textColor, 0.7);
      this.ctx.fillText(subtitle, this.canvas.width / 2, 50);
    }
  }

  private drawAxes(xAxis: ChartAxis, yAxes: ChartAxis[], chartArea: any, styling: ChartStyling): void {
    this.ctx.strokeStyle = styling.textColor;
    this.ctx.lineWidth = 2;

    // Draw X-axis
    this.ctx.beginPath();
    this.ctx.moveTo(chartArea.x, chartArea.y + chartArea.height);
    this.ctx.lineTo(chartArea.x + chartArea.width, chartArea.y + chartArea.height);
    this.ctx.stroke();

    // Draw Y-axes
    yAxes.forEach(yAxis => {
      const x = yAxis.position === 'right' ? chartArea.x + chartArea.width : chartArea.x;
      this.ctx.beginPath();
      this.ctx.moveTo(x, chartArea.y);
      this.ctx.lineTo(x, chartArea.y + chartArea.height);
      this.ctx.stroke();
    });

    // Draw axis labels
    this.drawAxisLabels(xAxis, yAxes, chartArea, styling);
  }

  private drawAxisLabels(xAxis: ChartAxis, yAxes: ChartAxis[], chartArea: any, styling: ChartStyling): void {
    this.ctx.font = `${styling.font.weight} ${styling.font.size}px ${styling.font.family}`;
    this.ctx.fillStyle = styling.textColor;

    // X-axis label
    this.ctx.textAlign = 'center';
    this.ctx.fillText(
      `${xAxis.label}${xAxis.unit ? ` (${xAxis.unit})` : ''}`,
      chartArea.x + chartArea.width / 2,
      chartArea.y + chartArea.height + 40
    );

    // Y-axis labels
    yAxes.forEach(yAxis => {
      const x = yAxis.position === 'right' ? 
        chartArea.x + chartArea.width + 50 : 
        chartArea.x - 50;
      
      this.ctx.save();
      this.ctx.translate(x, chartArea.y + chartArea.height / 2);
      this.ctx.rotate(-Math.PI / 2);
      this.ctx.textAlign = 'center';
      this.ctx.fillText(
        `${yAxis.label}${yAxis.unit ? ` (${yAxis.unit})` : ''}`,
        0, 0
      );
      this.ctx.restore();
    });
  }

  private drawGrid(chartArea: any, styling: ChartStyling): void {
    this.ctx.strokeStyle = styling.gridColor;
    this.ctx.lineWidth = 1;
    this.ctx.setLineDash([2, 2]);

    // Vertical grid lines
    for (let i = 0; i <= 10; i++) {
      const x = chartArea.x + (chartArea.width / 10) * i;
      this.ctx.beginPath();
      this.ctx.moveTo(x, chartArea.y);
      this.ctx.lineTo(x, chartArea.y + chartArea.height);
      this.ctx.stroke();
    }

    // Horizontal grid lines
    for (let i = 0; i <= 10; i++) {
      const y = chartArea.y + (chartArea.height / 10) * i;
      this.ctx.beginPath();
      this.ctx.moveTo(chartArea.x, y);
      this.ctx.lineTo(chartArea.x + chartArea.width, y);
      this.ctx.stroke();
    }

    this.ctx.setLineDash([]);
  }

  private drawThresholds(
    thresholds: AlertThreshold[],
    chartArea: any,
    yAxis: ChartAxis,
    styling: ChartStyling
  ): void {
    const yMin = yAxis.min || 0;
    const yMax = yAxis.max || 100;

    thresholds.forEach(threshold => {
      const y = chartArea.y + chartArea.height - ((threshold.value - yMin) / (yMax - yMin)) * chartArea.height;
      
      this.ctx.strokeStyle = threshold.color;
      this.ctx.lineWidth = 2;
      
      if (threshold.dashStyle === 'dashed') {
        this.ctx.setLineDash([5, 5]);
      } else if (threshold.dashStyle === 'dotted') {
        this.ctx.setLineDash([2, 2]);
      } else {
        this.ctx.setLineDash([]);
      }

      this.ctx.beginPath();
      this.ctx.moveTo(chartArea.x, y);
      this.ctx.lineTo(chartArea.x + chartArea.width, y);
      this.ctx.stroke();

      // Threshold label
      this.ctx.fillStyle = threshold.color;
      this.ctx.font = `normal ${styling.font.size - 2}px ${styling.font.family}`;
      this.ctx.textAlign = 'right';
      this.ctx.fillText(threshold.label, chartArea.x + chartArea.width - 5, y - 5);
    });

    this.ctx.setLineDash([]);
  }

  private async drawSeries(
    series: ChartSeries,
    chartType: string,
    chartArea: any,
    xAxis: ChartAxis,
    yAxes: ChartAxis[],
    styling: ChartStyling
  ): Promise<void> {
    const yAxis = yAxes.find(axis => axis.id === series.yAxisId) || yAxes[0];
    
    switch (chartType) {
      case 'line':
        this.drawLineSeries(series, chartArea, xAxis, yAxis);
        break;
      case 'bar':
        this.drawBarSeries(series, chartArea, xAxis, yAxis);
        break;
      case 'scatter':
        this.drawScatterSeries(series, chartArea, xAxis, yAxis);
        break;
      case 'pie':
        this.drawPieSeries(series, chartArea);
        break;
      case 'heatmap':
        this.drawHeatmapSeries(series, chartArea, xAxis, yAxis);
        break;
      case 'combo':
        if (series.type === 'line' || !series.type) {
          this.drawLineSeries(series, chartArea, xAxis, yAxis);
        } else if (series.type === 'scatter') {
          this.drawScatterSeries(series, chartArea, xAxis, yAxis);
        }
        break;
    }
  }

  private drawLineSeries(series: ChartSeries, chartArea: any, xAxis: ChartAxis, yAxis: ChartAxis): void {
    if (series.data.length === 0) return;

    // Calculate data ranges
    const xValues = series.data.map(d => this.normalizeXValue(d.x));
    const yValues = series.data.map(d => d.y);
    const xMin = Math.min(...xValues);
    const xMax = Math.max(...xValues);
    const yMin = yAxis.min ?? Math.min(...yValues);
    const yMax = yAxis.max ?? Math.max(...yValues);

    // Draw line
    this.ctx.strokeStyle = series.color;
    this.ctx.lineWidth = 3;
    this.ctx.beginPath();

    series.data.forEach((point, index) => {
      const x = chartArea.x + ((this.normalizeXValue(point.x) - xMin) / (xMax - xMin)) * chartArea.width;
      const y = chartArea.y + chartArea.height - ((point.y - yMin) / (yMax - yMin)) * chartArea.height;

      if (index === 0) {
        this.ctx.moveTo(x, y);
      } else {
        this.ctx.lineTo(x, y);
      }
    });

    this.ctx.stroke();

    // Draw data points
    this.ctx.fillStyle = series.color;
    series.data.forEach(point => {
      const x = chartArea.x + ((this.normalizeXValue(point.x) - xMin) / (xMax - xMin)) * chartArea.width;
      const y = chartArea.y + chartArea.height - ((point.y - yMin) / (yMax - yMin)) * chartArea.height;

      this.ctx.beginPath();
      this.ctx.arc(x, y, 3, 0, 2 * Math.PI);
      this.ctx.fill();
    });
  }

  private drawBarSeries(series: ChartSeries, chartArea: any, xAxis: ChartAxis, yAxis: ChartAxis): void {
    const barWidth = chartArea.width / series.data.length * 0.8;
    const barSpacing = chartArea.width / series.data.length * 0.2;
    
    const yValues = series.data.map(d => d.y);
    const yMin = yAxis.min ?? Math.min(0, Math.min(...yValues));
    const yMax = yAxis.max ?? Math.max(...yValues);

    series.data.forEach((point, index) => {
      const x = chartArea.x + barSpacing / 2 + index * (barWidth + barSpacing);
      const barHeight = ((point.y - yMin) / (yMax - yMin)) * chartArea.height;
      const y = chartArea.y + chartArea.height - barHeight;

      this.ctx.fillStyle = point.color || series.color;
      this.ctx.fillRect(x, y, barWidth, barHeight);

      // Value label on top
      this.ctx.fillStyle = this.defaultStyling.textColor;
      this.ctx.font = `normal ${this.defaultStyling.font.size - 2}px ${this.defaultStyling.font.family}`;
      this.ctx.textAlign = 'center';
      this.ctx.fillText(point.y.toFixed(1), x + barWidth / 2, y - 5);
    });
  }

  private drawScatterSeries(series: ChartSeries, chartArea: any, xAxis: ChartAxis, yAxis: ChartAxis): void {
    const xValues = series.data.map(d => this.normalizeXValue(d.x));
    const yValues = series.data.map(d => d.y);
    const xMin = Math.min(...xValues);
    const xMax = Math.max(...xValues);
    const yMin = yAxis.min ?? Math.min(...yValues);
    const yMax = yAxis.max ?? Math.max(...yValues);

    this.ctx.fillStyle = series.color;
    series.data.forEach(point => {
      const x = chartArea.x + ((this.normalizeXValue(point.x) - xMin) / (xMax - xMin)) * chartArea.width;
      const y = chartArea.y + chartArea.height - ((point.y - yMin) / (yMax - yMin)) * chartArea.height;

      this.ctx.beginPath();
      this.ctx.arc(x, y, 4, 0, 2 * Math.PI);
      this.ctx.fill();
    });
  }

  private drawPieSeries(series: ChartSeries, chartArea: any): void {
    const centerX = chartArea.x + chartArea.width / 2;
    const centerY = chartArea.y + chartArea.height / 2;
    const radius = Math.min(chartArea.width, chartArea.height) / 2 - 20;

    const total = series.data.reduce((sum, point) => sum + point.y, 0);
    let currentAngle = -Math.PI / 2;

    series.data.forEach((point, index) => {
      const sliceAngle = (point.y / total) * 2 * Math.PI;
      const color = point.color || this.generateColorPalette(series.data.length)[index];

      // Draw slice
      this.ctx.fillStyle = color;
      this.ctx.beginPath();
      this.ctx.moveTo(centerX, centerY);
      this.ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
      this.ctx.closePath();
      this.ctx.fill();

      // Draw percentage label
      const labelAngle = currentAngle + sliceAngle / 2;
      const labelX = centerX + Math.cos(labelAngle) * (radius * 0.7);
      const labelY = centerY + Math.sin(labelAngle) * (radius * 0.7);
      
      this.ctx.fillStyle = '#ffffff';
      this.ctx.font = `bold ${this.defaultStyling.font.size}px ${this.defaultStyling.font.family}`;
      this.ctx.textAlign = 'center';
      this.ctx.fillText(`${((point.y / total) * 100).toFixed(1)}%`, labelX, labelY);

      currentAngle += sliceAngle;
    });
  }

  private drawHeatmapSeries(series: ChartSeries, chartArea: any, xAxis: ChartAxis, yAxis: ChartAxis): void {
    const cellWidth = chartArea.width / 24; // 24 hours
    const cellHeight = chartArea.height / 7; // 7 days

    series.data.forEach(point => {
      const x = chartArea.x + (point.x as number) * cellWidth;
      const y = chartArea.y + (point.y as number) * cellHeight;

      this.ctx.fillStyle = point.color || series.color;
      this.ctx.fillRect(x, y, cellWidth, cellHeight);

      // Cell border
      this.ctx.strokeStyle = this.defaultStyling.backgroundColor;
      this.ctx.lineWidth = 1;
      this.ctx.strokeRect(x, y, cellWidth, cellHeight);

      // Value label if space allows
      if (cellWidth > 30 && cellHeight > 20) {
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = `normal ${Math.min(cellWidth / 4, cellHeight / 4)}px ${this.defaultStyling.font.family}`;
        this.ctx.textAlign = 'center';
        this.ctx.fillText(
          point.label || point.y.toFixed(1),
          x + cellWidth / 2,
          y + cellHeight / 2
        );
      }
    });
  }

  private drawAnnotations(annotations: ChartAnnotation[], chartArea: any, styling: ChartStyling): void {
    annotations.forEach(annotation => {
      this.ctx.fillStyle = annotation.color;
      this.ctx.font = `normal ${annotation.fontSize || styling.font.size}px ${styling.font.family}`;
      this.ctx.textAlign = 'left';

      const x = typeof annotation.x === 'number' ? 
        chartArea.x + (annotation.x / 100) * chartArea.width : 
        chartArea.x + 10;
      const y = annotation.y ? 
        chartArea.y + ((100 - annotation.y) / 100) * chartArea.height : 
        chartArea.y + 20;

      if (annotation.backgroundColor) {
        const textMetrics = this.ctx.measureText(annotation.text);
        this.ctx.fillStyle = annotation.backgroundColor;
        this.ctx.fillRect(x - 5, y - 15, textMetrics.width + 10, 20);
      }

      this.ctx.fillStyle = annotation.color;
      this.ctx.fillText(annotation.text, x, y);
    });
  }

  private drawLegend(series: ChartSeries[], styling: ChartStyling): void {
    const legendX = this.canvas.width - 200;
    const legendY = 100;
    const itemHeight = 20;

    series.forEach((s, index) => {
      const y = legendY + index * itemHeight;

      // Color box
      this.ctx.fillStyle = s.color;
      this.ctx.fillRect(legendX, y - 10, 15, 15);

      // Text
      this.ctx.fillStyle = styling.textColor;
      this.ctx.font = `normal ${styling.font.size}px ${styling.font.family}`;
      this.ctx.textAlign = 'left';
      this.ctx.fillText(s.name, legendX + 25, y);
    });
  }

  // Utility methods

  private createDefaultStyling(): ChartStyling {
    return {
      backgroundColor: '#ffffff',
      gridColor: '#e5e7eb',
      textColor: '#374151',
      primaryColor: '#3b82f6',
      secondaryColor: '#10b981',
      alertColor: '#ef4444',
      warningColor: '#f59e0b',
      successColor: '#10b981',
      font: {
        family: 'Arial, sans-serif',
        size: 12,
        weight: 'normal'
      },
      margins: {
        top: 80,
        right: 60,
        bottom: 80,
        left: 80
      }
    };
  }

  private generateColorPalette(count: number): string[] {
    const baseColors = [
      '#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6',
      '#f97316', '#06b6d4', '#84cc16', '#ec4899', '#64748b'
    ];

    const colors: string[] = [];
    for (let i = 0; i < count; i++) {
      colors.push(baseColors[i % baseColors.length]);
    }

    return colors;
  }

  private normalizeXValue(x: number | string | Date): number {
    if (typeof x === 'number') return x;
    if (x instanceof Date) return x.getTime();
    return new Date(x).getTime();
  }

  private adjustColorOpacity(color: string, opacity: number): string {
    // Simple opacity adjustment - in production, you'd use a proper color library
    const alpha = Math.round(opacity * 255).toString(16).padStart(2, '0');
    return color + alpha;
  }

  private getHeatmapColor(temperature: number, alertLevel: 'normal' | 'warning' | 'critical'): string {
    switch (alertLevel) {
      case 'critical': return '#ef4444';
      case 'warning': return '#f59e0b';
      default:
        // Temperature-based color gradient
        if (temperature < 32) return '#3b82f6'; // Blue for frozen
        if (temperature < 40) return '#10b981'; // Green for refrigerated
        if (temperature < 70) return '#f59e0b'; // Yellow for room temp
        return '#ef4444'; // Red for hot
    }
  }

  // Data preparation methods (simplified implementations)
  private aggregateDataByTimeRange(data: ChartDataPoint[], timeRange: string): ChartDataPoint[] {
    // Simplified aggregation - would implement proper time bucketing in production
    return data.filter((_, index) => index % Math.ceil(data.length / 100) === 0);
  }

  private prepareHeatmapData(sensorData: any[], period: string): HeatmapData[] {
    const heatmapData: HeatmapData[] = [];
    // Implementation would create hour/day grid data
    return heatmapData;
  }

  private groupAlertsBySensor(alerts: TemperatureAlert[]): ChartDataPoint[] {
    const grouped = new Map<string, number>();
    alerts.forEach(alert => {
      const sensor = alert.sensors?.name || 'Unknown';
      grouped.set(sensor, (grouped.get(sensor) || 0) + 1);
    });

    return Array.from(grouped.entries()).map(([sensor, count]) => ({
      x: sensor,
      y: count,
      label: sensor
    }));
  }

  private groupAlertsByType(alerts: TemperatureAlert[]): ChartDataPoint[] {
    const grouped = new Map<string, number>();
    alerts.forEach(alert => {
      grouped.set(alert.alert_type, (grouped.get(alert.alert_type) || 0) + 1);
    });

    return Array.from(grouped.entries()).map(([type, count]) => ({
      x: type.replace(/_/g, ' '),
      y: count,
      label: type.replace(/_/g, ' ')
    }));
  }

  private groupAlertsBySeverity(alerts: TemperatureAlert[]): ChartDataPoint[] {
    const grouped = new Map<string, number>();
    alerts.forEach(alert => {
      grouped.set(alert.severity, (grouped.get(alert.severity) || 0) + 1);
    });

    return Array.from(grouped.entries()).map(([severity, count]) => ({
      x: severity,
      y: count,
      label: severity,
      color: severity === 'critical' ? '#ef4444' : severity === 'high' ? '#f59e0b' : '#10b981'
    }));
  }

  private groupAlertsByTime(alerts: TemperatureAlert[]): ChartDataPoint[] {
    const grouped = new Map<string, number>();
    alerts.forEach(alert => {
      const hour = new Date(alert.created_at).getHours();
      const hourKey = `${hour}:00`;
      grouped.set(hourKey, (grouped.get(hourKey) || 0) + 1);
    });

    return Array.from(grouped.entries()).map(([hour, count]) => ({
      x: hour,
      y: count,
      label: hour
    }));
  }

  private calculateSensorMetric(sensor: any, metric: string): number {
    switch (metric) {
      case 'average': return sensor.statistics.avgTemp;
      case 'alerts': return sensor.statistics.alertsCount;
      case 'variance':
        const temps = sensor.readings.map((r: any) => r.temperature);
        const mean = temps.reduce((sum: number, t: number) => sum + t, 0) / temps.length;
        return temps.reduce((sum: number, t: number) => sum + Math.pow(t - mean, 2), 0) / temps.length;
      case 'reliability':
        return Math.min(100, (sensor.statistics.readingsCount / (24 * 4)) * 100); // Assuming 15-min intervals
      default: return 0;
    }
  }

  private formatMetricName(metric: string): string {
    return metric.charAt(0).toUpperCase() + metric.slice(1);
  }

  private formatTimeRange(timeRange: string): string {
    const formats: Record<string, string> = {
      hour: 'Hourly',
      day: 'Daily',
      week: 'Weekly',
      month: 'Monthly'
    };
    return formats[timeRange] || 'Custom';
  }

  private capitalizeFirst(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  private createHistogramData(values: number[], bins: number): ChartDataPoint[] {
    const min = Math.min(...values);
    const max = Math.max(...values);
    const binWidth = (max - min) / bins;
    const histogram = new Array(bins).fill(0);

    values.forEach(value => {
      const binIndex = Math.min(bins - 1, Math.floor((value - min) / binWidth));
      histogram[binIndex]++;
    });

    return histogram.map((count, index) => ({
      x: min + index * binWidth + binWidth / 2,
      y: count,
      label: `${(min + index * binWidth).toFixed(1)}-${(min + (index + 1) * binWidth).toFixed(1)}`
    }));
  }

  private createBoxPlotData(values: number[]): ChartDataPoint[] {
    const sorted = values.slice().sort((a, b) => a - b);
    const q1 = sorted[Math.floor(sorted.length * 0.25)];
    const median = sorted[Math.floor(sorted.length * 0.5)];
    const q3 = sorted[Math.floor(sorted.length * 0.75)];
    const min = sorted[0];
    const max = sorted[sorted.length - 1];

    return [
      { x: 'Min', y: min, label: 'Minimum' },
      { x: 'Q1', y: q1, label: 'Q1' },
      { x: 'Median', y: median, label: 'Median' },
      { x: 'Q3', y: q3, label: 'Q3' },
      { x: 'Max', y: max, label: 'Maximum' }
    ];
  }

  private createViolinPlotData(values: number[]): ChartDataPoint[] {
    // Simplified violin plot - would use proper kernel density estimation
    return this.createHistogramData(values, 50);
  }
}

// Export singleton instance
export const chartGenerator = ChartGenerator.getInstance();