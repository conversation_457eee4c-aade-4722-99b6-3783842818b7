/**
 * Email Reporting Service for TempStick Integration
 * 
 * Handles automated temperature report generation and email scheduling:
 * - Daily, weekly, monthly temperature compliance reports
 * - Alert summary reports with corrective actions
 * - HACCP violation reports
 * - Custom report scheduling and distribution
 * - Chart generation for email attachments
 * - Subscription management and preferences
 */

import { supabase } from './supabase';
import { EmailService } from './email-service';
import { emailTemplateBuilder } from './email-templates';
import type {
  TemperatureAlert,
  TemperatureReportData,
  Sensor,
  StorageArea
} from '../types/tempstick';

// Report Configuration Types
interface ReportSubscription {
  id: string;
  user_id: string;
  report_type: 'daily' | 'weekly' | 'monthly' | 'alert_summary' | 'haccp_compliance' | 'custom';
  frequency: 'daily' | 'weekly' | 'monthly' | 'immediate';
  recipients: string[];
  sensor_ids?: string[];
  storage_area_ids?: string[];
  include_charts: boolean;
  include_raw_data: boolean;
  active: boolean;
  delivery_time: string; // HH:mm format
  delivery_days?: number[]; // 0=Sunday, 1=Monday, etc.
  last_sent?: string;
  created_at: string;
  preferences: {
    alertsOnly?: boolean;
    includeRecommendations?: boolean;
    complianceThreshold?: number;
    temperatureUnit?: 'F' | 'C';
    timeZone?: string;
  };
}

interface ChartData {
  temperatureTrend?: Buffer;
  alertDistribution?: Buffer;
  complianceMetrics?: Buffer;
  haccpStatus?: Buffer;
}

interface ReportJobData {
  subscriptionId: string;
  reportType: ReportSubscription['report_type'];
  scheduledFor: Date;
  retryCount: number;
}

/**
 * Email Reporting Service Class
 */
export class EmailReportingService {
  private scheduledJobs = new Map<string, NodeJS.Timeout>();
  private reportQueue = new Map<string, ReportJobData>();
  
  private config = {
    maxRecipientsPerReport: 50,
    maxReportsPerDay: 100,
    chartWidth: 800,
    chartHeight: 400,
    reportRetentionDays: 90,
    defaultTimeZone: 'America/Los_Angeles'
  };


  constructor() {
    this.startScheduler();
    this.loadActiveSubscriptions();
  }

  /**
   * Create a new report subscription
   */
  async createSubscription(
    userId: string,
    config: Omit<ReportSubscription, 'id' | 'user_id' | 'created_at' | 'last_sent'>
  ): Promise<string> {
    try {
      const subscription: Omit<ReportSubscription, 'id'> = {
        user_id: userId,
        created_at: new Date().toISOString(),
        ...config
      };

      const { data, error } = await supabase
        .from('email_report_subscriptions')
        .insert([subscription])
        .select('id')
        .single();

      if (error) throw error;

      const subscriptionId = data.id;
      
      // Schedule the subscription  
      const fullSubscription = { ...subscription, id: subscriptionId } as ReportSubscription;
      await this.scheduleSubscription(subscriptionId, fullSubscription);
      
      console.log(`📧 Report subscription created: ${subscriptionId}`);
      return subscriptionId;
      
    } catch (error) {
      console.error('Failed to create report subscription:', error);
      throw error;
    }
  }

  /**
   * Update report subscription
   */
  async updateSubscription(
    subscriptionId: string,
    updates: Partial<Omit<ReportSubscription, 'id' | 'user_id' | 'created_at'>>
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from('email_report_subscriptions')
        .update(updates)
        .eq('id', subscriptionId);

      if (error) throw error;

      // Reschedule if timing or frequency changed
      if (updates.delivery_time || updates.delivery_days || updates.frequency || updates.active !== undefined) {
        const { data: subscription } = await supabase
          .from('email_report_subscriptions')
          .select('*')
          .eq('id', subscriptionId)
          .single();

        if (subscription) {
          this.cancelScheduledReport(subscriptionId);
          if (subscription.active) {
            await this.scheduleSubscription(subscriptionId, subscription);
          }
        }
      }

      console.log(`📧 Report subscription updated: ${subscriptionId}`);
      
    } catch (error) {
      console.error('Failed to update report subscription:', error);
      throw error;
    }
  }

  /**
   * Delete report subscription
   */
  async deleteSubscription(subscriptionId: string): Promise<void> {
    try {
      // Cancel scheduled job
      this.cancelScheduledReport(subscriptionId);

      const { error } = await supabase
        .from('email_report_subscriptions')
        .delete()
        .eq('id', subscriptionId);

      if (error) throw error;

      console.log(`📧 Report subscription deleted: ${subscriptionId}`);
      
    } catch (error) {
      console.error('Failed to delete report subscription:', error);
      throw error;
    }
  }

  /**
   * Generate and send immediate report
   */
  async generateImmediateReport(
    reportType: ReportSubscription['report_type'],
    recipients: string[],
    options: {
      sensorIds?: string[];
      storageAreaIds?: string[];
      startDate?: Date;
      endDate?: Date;
      includeCharts?: boolean;
      includeRawData?: boolean;
      preferences?: ReportSubscription['preferences'];
    } = {}
  ): Promise<string> {
    try {
      console.log(`📊 Generating immediate ${reportType} report for ${recipients.length} recipients`);

      const reportData = await this.generateReportData(reportType, options);
      const charts = options.includeCharts ? await this.generateCharts(reportData) : undefined;
      
      // Calculate additional summary data required by EmailTemplateData
      const avgTemp = reportData.sensorData.reduce((sum, sensor) => {
        return sum + sensor.statistics.avgTemp;
      }, 0) / (reportData.sensorData.length || 1);
      
      const criticalAlerts = reportData.alerts.filter(alert => alert.severity === 'critical').length;
      const activeAlerts = reportData.alerts.filter(alert => !alert.resolved_at).length;
      
      const emailData = {
        companyName: 'Pacific Cloud Seafoods',
        recipientName: undefined,
        unsubscribeUrl: `${process.env.VITE_APP_URL ?? 'http://localhost:5177'}/unsubscribe`,
        dashboardUrl: `${process.env.VITE_APP_URL ?? 'http://localhost:5177'}/dashboard`,
        reportDate: new Date().toLocaleDateString(),
        summary: {
          totalSensors: reportData.summary.sensorsIncluded,
          activeAlerts,
          criticalAlerts,
          averageTemp: avgTemp,
          complianceRate: reportData.summary.complianceRate,
          reportPeriod: `${reportData.summary.reportPeriod.start} to ${reportData.summary.reportPeriod.end}`
        },
        charts: charts ? {
          temperatureTrend: charts.temperatureTrend?.toString('base64'),
          alertDistribution: charts.alertDistribution?.toString('base64'),
          complianceMetrics: charts.complianceMetrics?.toString('base64')
        } : undefined
      };

      let _emailContent;
      let _subject: string;

      switch (reportType) {
        case 'daily':
          _emailContent = emailTemplateBuilder.generateDailyReport(reportData, emailData);
          _subject = `Daily Temperature Report - ${new Date().toLocaleDateString()}`;
          break;
          
        case 'weekly':
          _emailContent = emailTemplateBuilder.generateWeeklyReport(reportData, emailData);
          _subject = `Weekly Temperature Summary - ${new Date().toLocaleDateString()}`;
          break;
          
        case 'haccp_compliance':
          _emailContent = emailTemplateBuilder.generateHACCPReport(reportData, emailData);
          _subject = `HACCP Compliance Report - ${new Date().toLocaleDateString()}`;
          break;
          
        case 'alert_summary': {
          const recentAlerts = reportData.alerts.filter(alert => 
            new Date(alert.created_at).getTime() > Date.now() - 24 * 60 * 60 * 1000
          );
          _subject = `Temperature Alert Summary - ${recentAlerts.length} alerts`;
          _emailContent = emailTemplateBuilder.generateDailyReport(reportData, emailData);
          break;
        }
          
        default:
          // Default email content generation - could be used for unknown report types
          // emailContent = emailTemplateBuilder.generateDailyReport(reportData, emailData);
          // subject = `Temperature Report - ${new Date().toLocaleDateString()}`;
          break;
      }

      // Prepare attachments
      const attachments = [];
      if (options.includeRawData) {
        const csvData = this.generateCSVExport(reportData);
        attachments.push({
          filename: `temperature_data_${new Date().toISOString().split('T')[0]}.csv`,
          content: Buffer.from(csvData),
          contentType: 'text/csv'
        });
      }

      if (charts) {
        if (charts.temperatureTrend) {
          attachments.push({
            filename: 'temperature_trends.png',
            content: charts.temperatureTrend,
            contentType: 'image/png'
          });
        }
        if (charts.complianceMetrics) {
          attachments.push({
            filename: 'compliance_metrics.png',
            content: charts.complianceMetrics,
            contentType: 'image/png'
          });
        }
      }

      // Create email service instance if needed
      const emailServiceInstance = EmailService.getInstance({
        provider: 'sendgrid',
        apiKey: process.env.SENDGRID_API_KEY ?? '',
        fromEmail: '<EMAIL>',
        fromName: 'Pacific Cloud Seafoods - Temperature Reports'
      });
      
      // Send emails to each recipient using scheduled report method
      // Note: Using any here temporarily as EmailReportConfig interface may need adjustment
      await emailServiceInstance.sendScheduledReport(reportData, {
        recipients,
        reportType: reportType as 'daily' | 'weekly' | 'monthly',
        includeCharts: options.includeCharts ?? false,
        includeRawData: options.includeRawData ?? false,
        scheduledTime: new Date().toISOString()
      } as unknown as Parameters<typeof emailServiceInstance.sendScheduledReport>[1]);
      
      const emailId = `report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Log the report generation
      await this.logReportGeneration(reportType, recipients, emailId);

      console.log(`✅ Immediate ${reportType} report generated: ${emailId}`);
      return emailId;
      
    } catch (error) {
      console.error('Failed to generate immediate report:', error);
      throw error;
    }
  }

  /**
   * Send alert notification email immediately
   */
  async sendAlertNotification(
    alert: TemperatureAlert & { sensors: Sensor & { storage_areas: StorageArea } },
    recipients: string[]
  ): Promise<string> {
    try {
      console.log(`🚨 Sending alert notification for ${alert.id} to ${recipients.length} recipients`);

      // Prepare email data for template generation
      // const emailData = {
      //   companyName: 'Pacific Cloud Seafoods',
      //   unsubscribeUrl: `${process.env.VITE_APP_URL ?? 'http://localhost:5177'}/unsubscribe`,
      //   dashboardUrl: `${process.env.VITE_APP_URL ?? 'http://localhost:5177'}/dashboard`,
      //   reportDate: new Date().toLocaleDateString()
      // };

      // Generate email content for alert notification
      // const emailContent = emailTemplateBuilder.generateAlertNotification(alert, emailData);
      // const subject = `🚨 Temperature Alert: ${alert.alert_type.replace(/_/g, ' ')} - ${alert.sensors.name}`;

      const emailServiceInstance = EmailService.getInstance({
        provider: 'sendgrid',
        apiKey: process.env.SENDGRID_API_KEY ?? '',
        fromEmail: '<EMAIL>',
        fromName: 'Pacific Cloud Seafoods - Temperature Alerts'
      });
      
      // Use the sendAlertEmail method for alerts
      await emailServiceInstance.sendAlertEmail(alert, alert.sensors, recipients);
      
      const emailId = `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Update alert to mark notification sent
      await supabase
        .from('temperature_alerts')
        .update({ notification_sent: true })
        .eq('id', alert.id);

      console.log(`✅ Alert notification sent: ${emailId}`);
      return emailId;
      
    } catch (error) {
      console.error('Failed to send alert notification:', error);
      throw error;
    }
  }

  /**
   * Generate report data based on type and options
   */
  private async generateReportData(
    reportType: ReportSubscription['report_type'],
    options: {
      sensorIds?: string[];
      storageAreaIds?: string[];
      startDate?: Date;
      endDate?: Date;
    } = {}
  ): Promise<TemperatureReportData> {
    const endDate = options.endDate ?? new Date();
    let startDate = options.startDate;

    // Set default date range based on report type
    if (!startDate) {
      switch (reportType) {
        case 'daily':
          startDate = new Date(endDate.getTime() - 24 * 60 * 60 * 1000);
          break;
        case 'weekly':
          startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'monthly':
          startDate = new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(endDate.getTime() - 24 * 60 * 60 * 1000);
      }
    }

    // Fetch sensors
    // Get sensors first
    let sensorsQuery = supabase
      .from('sensors')
      .select('*')
      .eq('active', true);

    if (options.sensorIds?.length) {
      sensorsQuery = sensorsQuery.in('id', options.sensorIds);
    }

    if (options.storageAreaIds?.length) {
      sensorsQuery = sensorsQuery.in('storage_area_id', options.storageAreaIds);
    }

    const { data: sensorsData, error: sensorsError } = await sensorsQuery;
    if (sensorsError) throw sensorsError;

    // Get storage areas separately
    const { data: storageAreasData, error: areasError } = await supabase
      .from('storage_areas')
      .select('*');

    if (areasError) {
      console.warn('Failed to load storage areas:', areasError);
    }

    // Map storage areas to sensors
    const sensors = (sensorsData || []).map(sensor => {
      const storage_area = storageAreasData?.find(area => area.id === sensor.storage_area_id);
      return {
        ...sensor,
        storage_areas: storage_area || null
      };
    });

    // Fetch temperature readings
    const { data: readings, error: readingsError } = await supabase
      .from('temperature_readings')
      .select(`
        *,
        sensors (
          name,
          location,
          tempstick_sensor_id
        )
      `)
      .gte('recorded_at', startDate.toISOString())
      .lte('recorded_at', endDate.toISOString())
      .in('sensor_id', sensors?.map(s => s.id) ?? [])
      .order('recorded_at', { ascending: true });

    if (readingsError) throw readingsError;

    // Fetch alerts
    const { data: alerts, error: alertsError } = await supabase
      .from('temperature_alerts')
      .select(`
        *,
        sensors (
          name,
          location,
          storage_areas (
            name,
            area_type
          )
        )
      `)
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString())
      .in('sensor_id', sensors?.map(s => s.id) ?? [])
      .order('created_at', { ascending: false });

    if (alertsError) throw alertsError;

    // Calculate summary statistics
    const totalReadings = readings?.length ?? 0;
    const totalAlerts = alerts?.length ?? 0;
    // Calculate average temperature for potential future use
    // const avgTemp = readings?.length ? 
    //   readings.reduce((sum, r) => sum + r.temperature, 0) / readings.length : 0;

    // Calculate compliance rate (readings without alerts)
    const readingsWithAlerts = new Set();
    alerts?.forEach(alert => {
      const alertTime = new Date(alert.created_at).getTime();
      readings?.forEach(reading => {
        if (reading.sensor_id === alert.sensor_id) {
          const readingTime = new Date(reading.recorded_at).getTime();
          if (Math.abs(alertTime - readingTime) < 5 * 60 * 1000) { // 5 minute window
            readingsWithAlerts.add(reading.id);
          }
        }
      });
    });

    const complianceRate = totalReadings > 0 ? 
      ((totalReadings - readingsWithAlerts.size) / totalReadings) * 100 : 100;

    // Group data by sensor
    const sensorData = sensors?.map(sensor => {
      const sensorReadings = readings?.filter(r => r.sensor_id === sensor.id) ?? [];
      const sensorAlerts = alerts?.filter(a => a.sensor_id === sensor.id) ?? [];

      return {
        sensor,
        readings: sensorReadings,
        alerts: sensorAlerts,
        statistics: {
          avgTemp: sensorReadings.length > 0 ? 
            sensorReadings.reduce((sum, r) => sum + r.temperature, 0) / sensorReadings.length : 0,
          minTemp: sensorReadings.length > 0 ? 
            Math.min(...sensorReadings.map(r => r.temperature)) : 0,
          maxTemp: sensorReadings.length > 0 ? 
            Math.max(...sensorReadings.map(r => r.temperature)) : 0,
          readingsCount: sensorReadings.length,
          alertsCount: sensorAlerts.length
        }
      };
    }) ?? [];

    // HACCP compliance analysis
    const haccpCompliance = sensors
      ?.filter(sensor => sensor.storage_areas?.haccp_control_point)
      .map(sensor => {
        const storageArea = sensor.storage_areas;
        const sensorAlerts = alerts?.filter(a => 
          a.sensor_id === sensor.id && a.alert_type === 'haccp_violation'
        ) ?? [];
        const criticalViolations = sensorAlerts.filter(a => a.severity === 'critical');
        const sensorReadings = readings?.filter(r => r.sensor_id === sensor.id) ?? [];
        
        const violationsCount = sensorAlerts.length;
        const sensorComplianceRate = sensorReadings.length > 0 ?
          ((sensorReadings.length - violationsCount) / sensorReadings.length) * 100 : 100;

        return {
          storageArea,
          violationsCount,
          complianceRate: sensorComplianceRate,
          criticalViolations
        };
      }) ?? [];

    return {
      summary: {
        reportPeriod: {
          start: startDate.toISOString(),
          end: endDate.toISOString()
        },
        sensorsIncluded: sensors?.length ?? 0,
        totalReadings,
        totalAlerts,
        complianceRate
      },
      sensorData,
      alerts: alerts ?? [],
      haccpCompliance
    };
  }

  /**
   * Generate charts for reports (placeholder - would use actual charting library)
   */
  private async generateCharts(_reportData: TemperatureReportData): Promise<ChartData> {
    // TODO: Implement actual chart generation using a library like Chart.js or D3
    // For now, return placeholder buffers
    console.log('📊 Generating charts for report...');
    
    return {
      temperatureTrend: Buffer.from('placeholder-temperature-chart'),
      alertDistribution: Buffer.from('placeholder-alert-chart'),
      complianceMetrics: Buffer.from('placeholder-compliance-chart'),
      haccpStatus: Buffer.from('placeholder-haccp-chart')
    };
  }

  /**
   * Generate CSV export of temperature data
   */
  private generateCSVExport(reportData: TemperatureReportData): string {
    const headers = [
      'Timestamp',
      'Sensor ID',
      'Sensor Name',
      'Location',
      'Temperature (°F)',
      'Humidity (%)',
      'Storage Area',
      'Alert Status'
    ];

    let csv = `${headers.join(',')}\n`;

    reportData.sensorData.forEach(sensorData => {
      sensorData.readings.forEach(reading => {
        const hasAlert = sensorData.alerts.some(alert => 
          Math.abs(new Date(alert.created_at).getTime() - 
                   new Date(reading.recorded_at).getTime()) < 5 * 60 * 1000
        );

        const row = [
          reading.recorded_at,
          sensorData.sensor.id,
          `"${sensorData.sensor.name}"`,
          `"${sensorData.sensor.location}"`,
          reading.temperature.toFixed(2),
          reading.humidity?.toFixed(2) ?? '',
          sensorData.sensor.storage_areas?.name ? `"${sensorData.sensor.storage_areas.name}"` : '',
          hasAlert ? 'Alert' : 'Normal'
        ];

        csv += `${row.join(',')}\n`;
      });
    });

    return csv;
  }

  /**
   * Schedule a subscription for automatic delivery
   */
  private async scheduleSubscription(
    subscriptionId: string,
    subscription: ReportSubscription
  ): Promise<void> {
    if (!subscription.active) return;

    // Cancel existing schedule
    this.cancelScheduledReport(subscriptionId);

    const now = new Date();
    const nextRunTime = this.calculateNextRunTime(subscription, now);

    const timeout = setTimeout(async () => {
      await this.executeScheduledReport(subscriptionId);
    }, nextRunTime.getTime() - now.getTime());

    this.scheduledJobs.set(subscriptionId, timeout);

    console.log(`⏰ Scheduled ${subscription.report_type} report: ${subscriptionId} for ${nextRunTime.toISOString()}`);
  }

  /**
   * Calculate next run time for a subscription
   */
  private calculateNextRunTime(subscription: ReportSubscription, fromDate: Date): Date {
    const [hours, minutes] = subscription.delivery_time.split(':').map(n => parseInt(n));
    const nextRun = new Date(fromDate);

    switch (subscription.frequency) {
      case 'daily':
        nextRun.setDate(nextRun.getDate() + 1);
        break;
        
      case 'weekly': {
        const targetDay = subscription.delivery_days?.[0] ?? 1; // Default to Monday
        const daysUntilTarget = (targetDay - nextRun.getDay() + 7) % 7;
        nextRun.setDate(nextRun.getDate() + (daysUntilTarget || 7));
        break;
      }
        
      case 'monthly':
        nextRun.setMonth(nextRun.getMonth() + 1);
        nextRun.setDate(1); // First day of month
        break;
    }

    nextRun.setHours(hours, minutes, 0, 0);
    
    // If the scheduled time has already passed today, move to tomorrow
    if (nextRun <= fromDate) {
      nextRun.setDate(nextRun.getDate() + 1);
    }

    return nextRun;
  }

  /**
   * Execute a scheduled report
   */
  private async executeScheduledReport(subscriptionId: string): Promise<void> {
    try {
      const { data: subscription, error } = await supabase
        .from('email_report_subscriptions')
        .select('*')
        .eq('id', subscriptionId)
        .eq('active', true)
        .single();

      if (error || !subscription) {
        console.warn(`Subscription ${subscriptionId} not found or inactive`);
        return;
      }

      console.log(`📊 Executing scheduled ${subscription.report_type} report: ${subscriptionId}`);

      await this.generateImmediateReport(
        subscription.report_type,
        subscription.recipients,
        {
          sensorIds: subscription.sensor_ids,
          storageAreaIds: subscription.storage_area_ids,
          includeCharts: subscription.include_charts,
          includeRawData: subscription.include_raw_data,
          preferences: subscription.preferences
        }
      );

      // Update last sent timestamp
      await supabase
        .from('email_report_subscriptions')
        .update({ last_sent: new Date().toISOString() })
        .eq('id', subscriptionId);

      // Schedule next occurrence
      await this.scheduleSubscription(subscriptionId, subscription);

    } catch (error) {
      console.error(`Failed to execute scheduled report ${subscriptionId}:`, error);
      
      // Retry logic could be added here
      // For now, just reschedule for next occurrence
      const { data: subscription } = await supabase
        .from('email_report_subscriptions')
        .select('*')
        .eq('id', subscriptionId)
        .single();

      if (subscription) {
        await this.scheduleSubscription(subscriptionId, subscription);
      }
    }
  }

  /**
   * Cancel scheduled report
   */
  private cancelScheduledReport(subscriptionId: string): void {
    const timeout = this.scheduledJobs.get(subscriptionId);
    if (timeout) {
      clearTimeout(timeout);
      this.scheduledJobs.delete(subscriptionId);
    }
  }

  /**
   * Load active subscriptions on startup
   */
  private async loadActiveSubscriptions(): Promise<void> {
    try {
      const { data: subscriptions, error } = await supabase
        .from('email_report_subscriptions')
        .select('*')
        .eq('active', true);

      if (error) throw error;

      for (const subscription of subscriptions || []) {
        await this.scheduleSubscription(subscription.id, subscription);
      }

      console.log(`📧 Loaded ${subscriptions?.length ?? 0} active report subscriptions`);
      
    } catch (error) {
      console.error('Failed to load active subscriptions:', error);
    }
  }

  /**
   * Start the scheduler service
   */
  private startScheduler(): void {
    // Cleanup completed jobs every hour
    setInterval(() => {
      this.cleanupCompletedJobs();
    }, 60 * 60 * 1000);

    console.log('📅 Email reporting scheduler started');
  }

  /**
   * Cleanup completed jobs from memory
   */
  private cleanupCompletedJobs(): void {
    // This would typically check for completed jobs and clean them up
    // For now, just log the current state
    console.log(`📊 Scheduler status: ${this.scheduledJobs.size} active jobs, ${this.reportQueue.size} queued reports`);
  }

  /**
   * Log report generation for analytics
   */
  private async logReportGeneration(
    reportType: string,
    recipients: string[],
    emailId: string
  ): Promise<void> {
    try {
      await supabase
        .from('email_report_logs')
        .insert({
          report_type: reportType,
          recipient_count: recipients.length,
          email_id: emailId,
          generated_at: new Date().toISOString(),
          status: 'sent'
        });
    } catch (error) {
      console.error('Failed to log report generation:', error);
    }
  }

  /**
   * Get reporting statistics
   */
  async getReportingStats(): Promise<{
    activeSubscriptions: number;
    scheduledJobs: number;
    reportsToday: number;
    averageDeliveryTime: number;
  }> {
    const { data: activeSubscriptions } = await supabase
      .from('email_report_subscriptions')
      .select('id')
      .eq('active', true);

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const { data: todayReports } = await supabase
      .from('email_report_logs')
      .select('id')
      .gte('generated_at', today.toISOString());

    return {
      activeSubscriptions: activeSubscriptions?.length ?? 0,
      scheduledJobs: this.scheduledJobs.size,
      reportsToday: todayReports?.length ?? 0,
      averageDeliveryTime: 0 // Would calculate from email service stats
    };
  }
}

// Export singleton instance
export const emailReportingService = new EmailReportingService();