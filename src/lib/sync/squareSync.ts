import { createClient } from '@supabase/supabase-js';
import type { SupabaseClient } from '@supabase/supabase-js';

interface SquareLineItem {
  quantity?: string;
  base_price_money?: {
    amount?: number;
  };
  variation_total_price_money?: {
    amount?: number;
  };
}

interface SquareOrder {
  id: string;
  location_id: string;
  created_at: string;
  updated_at: string;
  state: string;
  line_items: SquareLineItem[];
  total_money?: {
    amount?: number;
  };
  fulfillments?: Array<{
    state: string;
    type: string;
  }>;
}

/**
 * Service for syncing data between Square and the Seafood Manager
 */
export class SquareSyncService {
  private supabase: SupabaseClient;
  private squareAccessToken: string | null = null;
  private squareLocationId: string | null = null;
  private squareApiUrl = 'https://connect.squareup.com/v2';
  
  constructor(supabaseUrl: string, supabaseKey: string) {
    this.supabase = createClient(supabaseUrl, supabaseKey);
  }
  
  /**
   * Initialize Square credentials from platform_integrations table
   */
  public async initialize(): Promise<boolean> {
    try {
      const { data, error } = await this.supabase
        .from('platform_integrations')
        .select('*')
        .eq('platform_name', 'square')
        .single();
      
      if (error || !data) {
        console.error('Error loading Square credentials:', error);
        return false;
      }
      
      this.squareAccessToken = data.access_token;
      this.squareLocationId = data.config?.location_id || null;
      
      return !!(this.squareAccessToken && this.squareLocationId);
    } catch (error) {
      console.error('Error initializing Square sync:', error);
      return false;
    }
  }
  
  /**
   * Sync orders from Square to fulfillments table
   */
  public async syncOrders(startTime?: string): Promise<{ 
    success: boolean;
    processed: number;
    created: number;
    updated: number;
    failed: number;
  }> {
    try {
      if (!this.squareAccessToken || !this.squareLocationId) {
        await this.initialize();
        
        if (!this.squareAccessToken || !this.squareLocationId) {
          throw new Error('Square credentials not available');
        }
      }
      
      // Log sync start
      const syncLogId = await this.logSyncStart('orders');
      
      // Determine start time for sync
      let syncStartTime = startTime;
      
      if (!syncStartTime) {
        const { data: platformData } = await this.supabase
          .from('platform_integrations')
          .select('last_sync_time')
          .eq('platform_name', 'square')
          .single();
        
        syncStartTime = platformData?.last_sync_time || new Date(0).toISOString();
      }
      
      // Format date for Square API
      const formattedStartTime = new Date(syncStartTime).toISOString();
      
      // Fetch orders from Square API
      const orders = await this.fetchOrdersFromSquare(formattedStartTime);
      
      if (!orders || !Array.isArray(orders)) {
        throw new Error('Failed to fetch orders from Square');
      }
      
      // Process orders
      let processed = 0;
      let created = 0;
      let updated = 0;
      let failed = 0;
      
      for (const order of orders) {
        try {
          const result = await this.processSquareOrder(order);
          processed++;
          
          if (result.created) {
            created++;
          } else if (result.updated) {
            updated++;
          }
        } catch (orderError) {
          console.error(`Error processing Square order ${order.id}:`, orderError);
          failed++;
        }
      }
      
      // Update last sync time
      const now = new Date().toISOString();
      await this.supabase
        .from('platform_integrations')
        .update({ last_sync_time: now })
        .eq('platform_name', 'square');
      
      // Log sync completion
      await this.logSyncComplete(syncLogId, {
        processed,
        created,
        updated,
        failed
      });
      
      return {
        success: true,
        processed,
        created,
        updated,
        failed
      };
    } catch (error) {
      console.error('Error syncing Square orders:', error);
      return {
        success: false,
        processed: 0,
        created: 0,
        updated: 0,
        failed: 0
      };
    }
  }
  
  /**
   * Fetch orders from Square API
   */
  private async fetchOrdersFromSquare(startTime: string): Promise<SquareOrder[]> {
    try {
      const response = await fetch(`${this.squareApiUrl}/orders/search`, {
        method: 'POST',
        headers: {
          'Square-Version': '2023-09-25',
          'Authorization': `Bearer ${this.squareAccessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          location_ids: [this.squareLocationId],
          query: {
            filter: {
              date_time_filter: {
                updated_at: {
                  start_at: startTime
                }
              },
              state_filter: {
                states: ['COMPLETED']
              }
            },
            sort: {
              sort_field: 'UPDATED_AT',
              sort_order: 'ASC'
            }
          },
          limit: 100
        })
      });
      
      if (!response.ok) {
        throw new Error(`Square API error: ${response.status} ${response.statusText}`);
      }
      
      const { orders } = await response.json();
      return orders || [];
    } catch (error) {
      console.error('Error fetching orders from Square:', error);
      throw error;
    }
  }
  
  /**
   * Process a Square order and add it to the fulfillments table
   */
  private async processSquareOrder(order: SquareOrder): Promise<{ created: boolean; updated: boolean }> {
    try {
      // Check if order already exists
      const { data: existingFulfillment } = await this.supabase
        .from('fulfillments')
        .select('id')
        .eq('square_transaction_id', order.id)
        .maybeSingle();
      
      // Map Square order to our Fulfillment schema
      const fulfillmentData = {
        order_number: order.id,
        square_transaction_id: order.id,
        fulfillment_date: order.created_at,
        fulfillment_status: this.mapSquareStatusToFulfillmentStatus(order.state),
        quantity: this.calculateTotalQuantity(order),
        unit_price: this.calculateUnitPrice(order),
        total_price: this.calculateTotalPrice(order),
        // We'll need to map to product_id later
        product_id: await this.findProductId(order),
        metadata: { square_data: order }
      };
      
      if (existingFulfillment) {
        // Update existing fulfillment
        await this.supabase
          .from('fulfillments')
          .update(fulfillmentData)
          .eq('id', existingFulfillment.id);
        
        return { created: false, updated: true };
      } else {
        // Create new fulfillment
        await this.supabase
          .from('fulfillments')
          .insert(fulfillmentData);
        
        return { created: true, updated: false };
      }
    } catch (error) {
      console.error('Error processing Square order:', error);
      throw error;
    }
  }
  
  /**
   * Find or create a product ID for the Square order
   */
  private async findProductId(order: SquareOrder): Promise<string> {
    // Try to find an existing product mapping based on Square catalog item ID
    if (order.line_items && order.line_items.length > 0) {
      // Get the first line item's catalog item ID
      const catalogItemId = order.line_items[0].catalog_object_id;
      
      if (catalogItemId) {
        const { data: product } = await this.supabase
          .from('Products')
          .select('id')
          .eq('metadata->square_catalog_id', catalogItemId)
          .maybeSingle();
        
        if (product) {
          return product.id;
        }
      }
      
      // If no mapping found, try by name
      const itemName = order.line_items[0].name;
      
      if (itemName) {
        const { data: product } = await this.supabase
          .from('Products')
          .select('id')
          .eq('name', itemName)
          .maybeSingle();
        
        if (product) {
          return product.id;
        }
      }
    }
    
    // If no matching product found, create a placeholder product or use a default
    const { data: defaultProduct } = await this.supabase
      .from('Products')
      .select('id')
      .eq('name', 'Unknown Square Product')
      .maybeSingle();
    
    if (defaultProduct) {
      return defaultProduct.id;
    }
    
    // Create a placeholder product
    const productName = order.line_items && order.line_items.length > 0 
      ? order.line_items[0].name 
      : 'Unknown Square Product';
    
    const { data: newProduct, error } = await this.supabase
      .from('Products')
      .insert({
        name: productName,
        description: 'Automatically created from Square order',
        metadata: {
          square_catalog_id: order.line_items?.[0]?.catalog_object_id,
          source: 'square_sync'
        }
      })
      .select('id')
      .single();
    
    if (error || !newProduct) {
      throw new Error(`Failed to create placeholder product: ${error?.message}`);
    }
    
    return newProduct.id;
  }
  
  /**
   * Log the start of a sync operation
   */
  private async logSyncStart(syncType: string): Promise<string> {
    try {
      const { data: platformData } = await this.supabase
        .from('platform_integrations')
        .select('id')
        .eq('platform_name', 'square')
        .single();
      
      if (!platformData) {
        throw new Error('Square platform integration record not found');
      }
      
      const { data: syncLog, error } = await this.supabase
        .from('sync_logs')
        .insert({
          platform_id: platformData.id,
          sync_start_time: new Date().toISOString(),
          sync_type: syncType,
          sync_direction: 'import',
          status: 'running'
        })
        .select('id')
        .single();
      
      if (error || !syncLog) {
        throw new Error(`Failed to create sync log: ${error?.message}`);
      }
      
      return syncLog.id;
    } catch (error) {
      console.error('Error logging sync start:', error);
      return '';
    }
  }
  
  /**
   * Log the completion of a sync operation
   */
  private async logSyncComplete(
    syncLogId: string,
    stats: {
      processed: number;
      created: number;
      updated: number;
      failed: number;
    }
  ): Promise<void> {
    if (!syncLogId) return;
    
    try {
      await this.supabase
        .from('sync_logs')
        .update({
          sync_end_time: new Date().toISOString(),
          items_processed: stats.processed,
          items_created: stats.created,
          items_updated: stats.updated,
          items_failed: stats.failed,
          status: stats.failed > 0 ? 'partially_completed' : 'completed'
        })
        .eq('id', syncLogId);
    } catch (error) {
      console.error('Error logging sync completion:', error);
    }
  }
  
  /**
   * Map Square order status to fulfillment status
   */
  private mapSquareStatusToFulfillmentStatus(squareStatus: string): 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled' {
    const statusMap: Record<string, 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled'> = {
      'OPEN': 'pending',
      'COMPLETED': 'delivered',
      'CANCELED': 'cancelled',
      'DRAFT': 'pending'
    };
    
    return statusMap[squareStatus] || 'pending';
  }
  
  /**
   * Calculate total quantity from a Square order
   */
  private calculateTotalQuantity(order: SquareOrder): number {
    if (!order.line_items || !Array.isArray(order.line_items)) {
      return 0;
    }
    
    return order.line_items.reduce((total: number, item: SquareLineItem) => {
      return total + (parseInt(item.quantity, 10) || 0);
    }, 0);
  }
  
  /**
   * Calculate unit price from a Square order
   */
  private calculateUnitPrice(order: SquareOrder): number {
    if (!order.line_items || !Array.isArray(order.line_items) || order.line_items.length === 0) {
      return 0;
    }
    
    // For simplicity, average the unit prices of all line items
    const totalBasePrice = order.line_items.reduce((sum: number, item: SquareLineItem) => {
      const basePrice = item.base_price_money?.amount || 0;
      return sum + basePrice;
    }, 0);
    
    return totalBasePrice / order.line_items.length / 100; // Convert from cents to dollars
  }
  
  /**
   * Calculate total price from a Square order
   */
  private calculateTotalPrice(order: SquareOrder): number {
    if (!order.total_money?.amount) {
      return 0;
    }
    
    return order.total_money.amount / 100; // Convert from cents to dollars
  }
}

// Export a singleton instance
export const squareSyncService = new SquareSyncService(
  import.meta.env.VITE_SUPABASE_URL || '',
  import.meta.env.VITE_SUPABASE_ANON_KEY || ''
);
