import { createClient } from '@supabase/supabase-js';
import { Fulfillment } from '../../types/schema';

interface SquareLineItem {
  quantity?: string;
  base_price_money?: {
    amount?: number;
  };
  variation_total_price_money?: {
    amount?: number;
  };
}

interface SquareOrder {
  id: string;
  location_id: string;
  created_at: string;
  updated_at: string;
  state: string;
  line_items: SquareLineItem[];
  total_money?: {
    amount?: number;
  };
  fulfillments?: Array<{
    state: string;
    type: string;
  }>;
}

// Constants
const SQUARE_API_BASE_URL = 'https://connect.squareup.com/v2';

/**
 * Synchronizes data with external systems like Square and DBP
 */
export class ExternalSyncService {
  private supabase;
  private squareAccessToken?: string;
  private dbpAccessToken?: string;
  
  constructor(supabaseUrl: string, supabaseKey: string) {
    this.supabase = createClient(supabaseUrl, supabaseKey);
  }
  
  /**
   * Set Square API credentials
   */
  public setSquareCredentials(accessToken: string): void {
    this.squareAccessToken = accessToken;
  }
  
  /**
   * Set DBP API credentials
   */
  public setDBPCredentials(accessToken: string): void {
    this.dbpAccessToken = accessToken;
  }
  
  /**
   * Sync orders from Square
   */
  public async syncSquareOrders(): Promise<void> {
    try {
      // Log sync start
      await this.logSyncStart('square', 'orders', 'import');
      
      if (!this.squareAccessToken) {
        throw new Error('Square access token not set');
      }
      
      // Fetch the latest sync timestamp
      const { data: platformData } = await this.supabase
        .from('platform_integrations')
        .select('last_sync_time')
        .eq('platform_name', 'square')
        .single();
      
      const lastSyncTime = platformData?.last_sync_time || new Date(0).toISOString();
      
      // Fetch orders from Square API
      const response = await fetch(`${SQUARE_API_BASE_URL}/orders/search`, {
        method: 'POST',
        headers: {
          'Square-Version': '2023-09-25',
          'Authorization': `Bearer ${this.squareAccessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          location_ids: ['your-location-id'], // Replace with actual location ID
          query: {
            filter: {
              date_time_filter: {
                updated_at: {
                  start_at: lastSyncTime
                }
              },
              state_filter: {
                states: ['COMPLETED']
              }
            },
            sort: {
              sort_field: 'UPDATED_AT',
              sort_order: 'ASC'
            }
          }
        })
      });
      
      if (!response.ok) {
        throw new Error(`Square API error: ${response.status} ${response.statusText}`);
      }
      
      const { orders } = await response.json();
      
      // Process each order
      let processedCount = 0;
      let createdCount = 0;
      let updatedCount = 0;
      let failedCount = 0;
      
      for (const order of orders) {
        try {
          // Check if order already exists
          const { data: existingFulfillment } = await this.supabase
            .from('fulfillments')
            .select('id')
            .eq('square_transaction_id', order.id)
            .maybeSingle();
          
          // Map Square order to our Fulfillment schema
          const fulfillment: Partial<Fulfillment> = {
            order_number: order.id,
            square_transaction_id: order.id,
            fulfillment_date: order.created_at,
            fulfillment_status: this.mapSquareStatusToFulfillmentStatus(order.state),
            quantity: this.calculateTotalQuantity(order),
            unit_price: this.calculateUnitPrice(order),
            total_price: this.calculateTotalPrice(order),
            metadata: { square_data: order }
          };
          
          if (existingFulfillment) {
            // Update existing fulfillment
            await this.supabase
              .from('fulfillments')
              .update(fulfillment)
              .eq('id', existingFulfillment.id);
            
            updatedCount++;
          } else {
            // Create new fulfillment
            await this.supabase
              .from('fulfillments')
              .insert(fulfillment);
            
            createdCount++;
          }
          
          processedCount++;
        } catch (orderError) {
          console.error(`Error processing Square order ${order.id}:`, orderError);
          failedCount++;
        }
      }
      
      // Update last sync time
      await this.supabase
        .from('platform_integrations')
        .update({ 
          last_sync_time: new Date().toISOString() 
        })
        .eq('platform_name', 'square');
      
      // Log sync completion
      await this.logSyncComplete(
        'square',
        'orders',
        'import',
        processedCount,
        createdCount,
        updatedCount,
        failedCount
      );
      
      console.log(`Square orders sync complete. Processed: ${processedCount}, Created: ${createdCount}, Updated: ${updatedCount}, Failed: ${failedCount}`);
    } catch (error) {
      console.error('Error syncing Square orders:', error);
      
      // Log sync failure
      await this.logSyncFailure('square', 'orders', 'import', error);
    }
  }
  
  /**
   * Sync orders from DBP
   */
  public async syncDBPOrders(): Promise<void> {
    try {
      // Log sync start
      await this.logSyncStart('dbp', 'orders', 'import');
      
      if (!this.dbpAccessToken) {
        throw new Error('DBP access token not set');
      }
      
      // Fetch the latest sync timestamp
      const { data: platformData } = await this.supabase
        .from('platform_integrations')
        .select('last_sync_time')
        .eq('platform_name', 'dbp')
        .single();
      
      const lastSyncTime = platformData?.last_sync_time || new Date(0).toISOString();
      
      // Implementation will depend on DBP API structure
      // This is a placeholder for the actual implementation
      console.log('Fetching orders from DBP since:', lastSyncTime);
      
      // Update last sync time
      await this.supabase
        .from('platform_integrations')
        .update({ 
          last_sync_time: new Date().toISOString() 
        })
        .eq('platform_name', 'dbp');
      
      // Log sync completion
      await this.logSyncComplete(
        'dbp',
        'orders',
        'import',
        0, // processed
        0, // created
        0, // updated
        0  // failed
      );
    } catch (error) {
      console.error('Error syncing DBP orders:', error);
      
      // Log sync failure
      await this.logSyncFailure('dbp', 'orders', 'import', error);
    }
  }
  
  /**
   * Update inventory in external systems based on local data
   */
  public async syncInventoryToExternalSystems(): Promise<void> {
    try {
      // Implementation will depend on requirements
      console.log('Syncing inventory to external systems...');
      
      // Fetch inventory data
      const { data: inventory, error } = await this.supabase
        .from('inventory_snapshots')
        .select('*')
        .order('snapshot_date', { ascending: false })
        .limit(100);
      
      if (error) throw error;
      
      // Logic to update external systems would go here
      
      console.log(`Synced ${inventory?.length || 0} inventory items to external systems`);
    } catch (error) {
      console.error('Error syncing inventory to external systems:', error);
    }
  }
  
  /**
   * Update COGS data based on fulfillments
   */
  public async updateCOGSFromFulfillments(): Promise<void> {
    try {
      console.log('Updating COGS from fulfillments...');
      
      // Fetch recent fulfillments that might need COGS calculation
      const { data: fulfillments, error } = await this.supabase
        .from('fulfillments')
        .select(`
          id,
          batch_id,
          product_id,
          quantity,
          unit_price,
          total_price
        `)
        .is('batch_id', 'not.null')
        .limit(100);
      
      if (error) throw error;
      
      if (!fulfillments || fulfillments.length === 0) {
        console.log('No fulfillments found for COGS calculation');
        return;
      }
      
      for (const fulfillment of fulfillments) {
        if (!fulfillment.batch_id) continue;
        
        // Check if COGS record exists for this batch
        const { data: existingCOGS } = await this.supabase
          .from('cogs')
          .select('id')
          .eq('batch_id', fulfillment.batch_id)
          .maybeSingle();
        
        if (existingCOGS) {
          console.log(`COGS record already exists for batch ${fulfillment.batch_id}`);
          continue;
        }
        
        // Fetch batch data
        const { data: batch } = await this.supabase
          .from('batches')
          .select('*')
          .eq('id', fulfillment.batch_id)
          .single();
        
        if (!batch) {
          console.log(`Batch ${fulfillment.batch_id} not found`);
          continue;
        }
        
        // Create COGS record
        const cogsRecord = {
          batch_id: fulfillment.batch_id,
          raw_product_cost: batch.cost || 0,
          shipping_cost: 0, // These would need to be determined from other data
          handling_cost: 0,
          processing_cost: 0,
          packaging_cost: 0,
          labor_cost: 0,
          other_costs: 0,
          cost_per_unit: batch.cost ? batch.cost / batch.quantity : 0,
          notes: `Automatically generated from fulfillment ${fulfillment.id}`
        };
        
        const { error: insertError } = await this.supabase
          .from('cogs')
          .insert(cogsRecord);
        
        if (insertError) {
          console.error(`Error creating COGS record for batch ${fulfillment.batch_id}:`, insertError);
          continue;
        }
        
        console.log(`COGS record created for batch ${fulfillment.batch_id}`);
      }
      
      console.log('COGS update complete');
    } catch (error) {
      console.error('Error updating COGS from fulfillments:', error);
    }
  }
  
  // Helper methods
  private async logSyncStart(platform: string, syncType: string, direction: string): Promise<void> {
    try {
      const { data: platformData } = await this.supabase
        .from('platform_integrations')
        .select('id')
        .eq('platform_name', platform)
        .single();
      
      if (!platformData) {
        console.warn(`Platform ${platform} not found, creating record...`);
        
        const { data: newPlatform, error: insertError } = await this.supabase
          .from('platform_integrations')
          .insert({
            platform_name: platform,
            status: 'active',
            config: {}
          })
          .select('id')
          .single();
        
        if (insertError) throw insertError;
        
        await this.supabase
          .from('sync_logs')
          .insert({
            platform_id: newPlatform.id,
            sync_start_time: new Date().toISOString(),
            sync_type: syncType,
            sync_direction: direction,
            status: 'running'
          });
      } else {
        await this.supabase
          .from('sync_logs')
          .insert({
            platform_id: platformData.id,
            sync_start_time: new Date().toISOString(),
            sync_type: syncType,
            sync_direction: direction,
            status: 'running'
          });
      }
    } catch (error) {
      console.error('Error logging sync start:', error);
    }
  }
  
  private async logSyncComplete(
    platform: string,
    syncType: string,
    direction: string,
    processed: number,
    created: number,
    updated: number,
    failed: number
  ): Promise<void> {
    try {
      const { data: platformData } = await this.supabase
        .from('platform_integrations')
        .select('id')
        .eq('platform_name', platform)
        .single();
      
      if (!platformData) {
        console.error(`Platform ${platform} not found for sync completion log`);
        return;
      }
      
      const { data: syncLog } = await this.supabase
        .from('sync_logs')
        .select('id')
        .eq('platform_id', platformData.id)
        .eq('sync_type', syncType)
        .eq('status', 'running')
        .order('sync_start_time', { ascending: false })
        .limit(1)
        .single();
      
      if (!syncLog) {
        console.error(`No running sync log found for ${platform} ${syncType}`);
        return;
      }
      
      await this.supabase
        .from('sync_logs')
        .update({
          sync_end_time: new Date().toISOString(),
          items_processed: processed,
          items_created: created,
          items_updated: updated,
          items_failed: failed,
          status: failed > 0 ? 'partially_completed' : 'completed'
        })
        .eq('id', syncLog.id);
    } catch (error) {
      console.error('Error logging sync completion:', error);
    }
  }
  
  private async logSyncFailure(
    platform: string,
    syncType: string,
    direction: string,
    error: Error | string
  ): Promise<void> {
    try {
      const { data: platformData } = await this.supabase
        .from('platform_integrations')
        .select('id')
        .eq('platform_name', platform)
        .single();
      
      if (!platformData) {
        console.error(`Platform ${platform} not found for sync failure log`);
        return;
      }
      
      const { data: syncLog } = await this.supabase
        .from('sync_logs')
        .select('id')
        .eq('platform_id', platformData.id)
        .eq('sync_type', syncType)
        .eq('status', 'running')
        .order('sync_start_time', { ascending: false })
        .limit(1)
        .single();
      
      if (!syncLog) {
        console.error(`No running sync log found for ${platform} ${syncType}`);
        return;
      }
      
      await this.supabase
        .from('sync_logs')
        .update({
          sync_end_time: new Date().toISOString(),
          status: 'failed',
          error_details: {
            message: error.message || 'Unknown error',
            stack: error.stack || '',
            timestamp: new Date().toISOString()
          }
        })
        .eq('id', syncLog.id);
    } catch (logError) {
      console.error('Error logging sync failure:', logError);
    }
  }
  
  // Utility methods for Square data parsing
  private mapSquareStatusToFulfillmentStatus(squareStatus: string): 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled' {
    const statusMap: Record<string, 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled'> = {
      'OPEN': 'pending',
      'COMPLETED': 'delivered',
      'CANCELED': 'cancelled',
      'DRAFT': 'pending'
    };
    
    return statusMap[squareStatus] || 'pending';
  }
  
  private calculateTotalQuantity(order: SquareOrder): number {
    if (!order.line_items || !Array.isArray(order.line_items)) {
      return 0;
    }
    
    return order.line_items.reduce((total: number, item: SquareLineItem) => {
      return total + (parseInt(item.quantity, 10) || 0);
    }, 0);
  }
  
  private calculateUnitPrice(order: SquareOrder): number {
    if (!order.line_items || !Array.isArray(order.line_items) || order.line_items.length === 0) {
      return 0;
    }
    
    // For simplicity, average the unit prices of all line items
    const totalBasePrice = order.line_items.reduce((sum: number, item: SquareLineItem) => {
      const basePrice = item.base_price_money?.amount || 0;
      return sum + basePrice;
    }, 0);
    
    return totalBasePrice / order.line_items.length / 100; // Convert from cents to dollars
  }
  
  private calculateTotalPrice(order: SquareOrder): number {
    if (!order.total_money?.amount) {
      return 0;
    }
    
    return order.total_money.amount / 100; // Convert from cents to dollars
  }
}

// Export a singleton instance
export const externalSyncService = new ExternalSyncService(
  import.meta.env.VITE_SUPABASE_URL || '',
  import.meta.env.VITE_SUPABASE_ANON_KEY || ''
);
