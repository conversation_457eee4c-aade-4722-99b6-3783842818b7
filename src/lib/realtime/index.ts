/**
 * Real-time Subscription Management Framework
 * 
 * Centralized management of Supabase real-time subscriptions with:
 * - Automatic reconnection and error handling
 * - Connection health monitoring
 * - Subscription lifecycle management
 * - Performance metrics and monitoring
 * - Event-driven architecture
 */

// Core types and classes
export * from '../../modules/realtime/types';
export { RealtimeManager } from '../../modules/realtime/RealtimeManager';

// Utility functions and hooks
import { RealtimeManager } from '../../modules/realtime/RealtimeManager';
import { supabase } from '../supabase';

// Singleton instance
export const realtimeManager = new RealtimeManager(supabase);

// Utility functions
export const createSubscription = (config: import('../../modules/realtime/types').SubscriptionConfig) => {
  return realtimeManager.subscribe(config);
};

export const removeSubscription = (subscriptionId: string) => {
  return realtimeManager.unsubscribe(subscriptionId);
};

export const getSubscriptionHealth = () => {
  return realtimeManager.getConnectionHealth();
};

export const getSubscriptionMetrics = () => {
  return realtimeManager.getMetrics();
};

// Common subscription configurations
export const commonSubscriptions = {
  voiceEvents: (callback: import('./types').SubscriptionCallback) => ({
    channel: 'voice-events-changes',
    event: '*',
    schema: 'public',
    table: 'inventory_events',
    filter: 'created_by_voice=eq.true',
    callback,
    reconnectOnError: true,
    maxReconnectAttempts: 5
  }),

  inventoryEvents: (callback: import('./types').SubscriptionCallback) => ({
    channel: 'inventory-changes',
    event: '*',
    schema: 'public',
    table: 'inventory_events',
    callback,
    reconnectOnError: true,
    maxReconnectAttempts: 3
  }),

  auditTrail: (callback: import('./types').SubscriptionCallback) => ({
    channel: 'audit-trail-changes',
    event: 'INSERT',
    schema: 'public',
    table: 'voice_event_audit',
    callback,
    reconnectOnError: true,
    maxReconnectAttempts: 3
  })
};