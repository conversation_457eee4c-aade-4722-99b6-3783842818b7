/**
 * Voice Processing Cost Optimizer
 * 
 * Manages OpenAI API usage to minimize costs while maintaining quality
 * Includes intelligent caching, batch processing, and usage tracking
 */

interface CostOptimizationConfig {
  maxCostPerHour: number; // Max cost in USD
  maxCostPerDay: number;
  maxCostPerMonth: number;
  whisperCostPerMinute: number; // Current OpenAI pricing
  gptCostPer1kTokens: number;
  enableSmartCaching: boolean;
  enableBatchProcessing: boolean;
  cacheExpiryHours: number;
}

interface UsageMetrics {
  whisperMinutesUsed: number;
  gptTokensUsed: number;
  totalCost: number;
  requestCount: number;
  cacheHitRate: number;
  averageProcessingTime: number;
  lastReset: number;
  period: 'hour' | 'day' | 'month';
}

interface CostAlert {
  type: 'warning' | 'limit' | 'critical';
  message: string;
  currentCost: number;
  limit: number;
  percentageUsed: number;
  suggestedAction: string;
}

export class VoiceCostOptimizer {
  private config: CostOptimizationConfig;
  private hourlyMetrics: UsageMetrics;
  private dailyMetrics: UsageMetrics;
  private monthlyMetrics: UsageMetrics;
  private responseCache: Map<string, { data: any; timestamp: number; cost: number }>;
  private batchQueue: Array<{ audioBlob: Blob; resolve: Function; reject: Function }>;
  private batchProcessingTimer?: NodeJS.Timeout;

  constructor(config: Partial<CostOptimizationConfig> = {}) {
    this.config = {
      maxCostPerHour: 5.00, // $5/hour
      maxCostPerDay: 50.00, // $50/day  
      maxCostPerMonth: 500.00, // $500/month
      whisperCostPerMinute: 0.006, // $0.006/minute as of 2024
      gptCostPer1kTokens: 0.03, // GPT-4 cost per 1k tokens
      enableSmartCaching: true,
      enableBatchProcessing: true,
      cacheExpiryHours: 24,
      ...config
    };

    this.hourlyMetrics = this.initMetrics('hour');
    this.dailyMetrics = this.initMetrics('day');
    this.monthlyMetrics = this.initMetrics('month');
    this.responseCache = new Map();
    this.batchQueue = [];

    // Load persisted metrics from localStorage
    this.loadPersistedMetrics();

    // Set up periodic metrics reset
    this.setupMetricsReset();
  }

  /**
   * Check if a request should be allowed based on cost limits
   */
  canMakeRequest(estimatedCost: number): { allowed: boolean; alert?: CostAlert } {
    this.updateMetrics();

    // Check hourly limit
    if (this.hourlyMetrics.totalCost + estimatedCost > this.config.maxCostPerHour) {
      return {
        allowed: false,
        alert: {
          type: 'limit',
          message: 'Hourly cost limit would be exceeded',
          currentCost: this.hourlyMetrics.totalCost,
          limit: this.config.maxCostPerHour,
          percentageUsed: (this.hourlyMetrics.totalCost / this.config.maxCostPerHour) * 100,
          suggestedAction: 'Wait until next hour or increase hourly limit'
        }
      };
    }

    // Check daily limit
    if (this.dailyMetrics.totalCost + estimatedCost > this.config.maxCostPerDay) {
      return {
        allowed: false,
        alert: {
          type: 'limit',
          message: 'Daily cost limit would be exceeded',
          currentCost: this.dailyMetrics.totalCost,
          limit: this.config.maxCostPerDay,
          percentageUsed: (this.dailyMetrics.totalCost / this.config.maxCostPerDay) * 100,
          suggestedAction: 'Wait until tomorrow or increase daily limit'
        }
      };
    }

    // Check monthly limit
    if (this.monthlyMetrics.totalCost + estimatedCost > this.config.maxCostPerMonth) {
      return {
        allowed: false,
        alert: {
          type: 'critical',
          message: 'Monthly cost limit would be exceeded',
          currentCost: this.monthlyMetrics.totalCost,
          limit: this.config.maxCostPerMonth,
          percentageUsed: (this.monthlyMetrics.totalCost / this.config.maxCostPerMonth) * 100,
          suggestedAction: 'Wait until next month or increase monthly limit'
        }
      };
    }

    // Check for warnings (80% of limits)
    const hourlyWarning = (this.hourlyMetrics.totalCost / this.config.maxCostPerHour) > 0.8;
    const dailyWarning = (this.dailyMetrics.totalCost / this.config.maxCostPerDay) > 0.8;
    
    if (hourlyWarning || dailyWarning) {
      return {
        allowed: true,
        alert: {
          type: 'warning',
          message: hourlyWarning ? 'Approaching hourly cost limit' : 'Approaching daily cost limit',
          currentCost: hourlyWarning ? this.hourlyMetrics.totalCost : this.dailyMetrics.totalCost,
          limit: hourlyWarning ? this.config.maxCostPerHour : this.config.maxCostPerDay,
          percentageUsed: hourlyWarning 
            ? (this.hourlyMetrics.totalCost / this.config.maxCostPerHour) * 100
            : (this.dailyMetrics.totalCost / this.config.maxCostPerDay) * 100,
          suggestedAction: 'Consider reducing usage or monitoring closely'
        }
      };
    }

    return { allowed: true };
  }

  /**
   * Check cache for existing result
   */
  getCachedResult(cacheKey: string): any | null {
    if (!this.config.enableSmartCaching) return null;

    const cached = this.responseCache.get(cacheKey);
    if (!cached) return null;

    // Check if cache has expired
    const ageHours = (Date.now() - cached.timestamp) / (1000 * 60 * 60);
    if (ageHours > this.config.cacheExpiryHours) {
      this.responseCache.delete(cacheKey);
      return null;
    }

    // Update cache hit metrics
    this.updateCacheHitRate();
    
    return cached.data;
  }

  /**
   * Cache a result with cost tracking
   */
  cacheResult(cacheKey: string, data: any, cost: number): void {
    if (!this.config.enableSmartCaching) return;

    this.responseCache.set(cacheKey, {
      data,
      timestamp: Date.now(),
      cost
    });

    // Limit cache size to prevent memory issues
    if (this.responseCache.size > 1000) {
      const oldestKey = Array.from(this.responseCache.keys())[0];
      this.responseCache.delete(oldestKey);
    }
  }

  /**
   * Record usage for cost tracking
   */
  recordUsage(
    whisperMinutes: number = 0, 
    gptTokens: number = 0, 
    processingTimeMs: number = 0,
    wasCached: boolean = false
  ): void {
    const whisperCost = whisperMinutes * this.config.whisperCostPerMinute;
    const gptCost = (gptTokens / 1000) * this.config.gptCostPer1kTokens;
    const totalCost = wasCached ? 0 : whisperCost + gptCost; // No cost for cached results

    // Update all metrics periods
    [this.hourlyMetrics, this.dailyMetrics, this.monthlyMetrics].forEach(metrics => {
      metrics.whisperMinutesUsed += wasCached ? 0 : whisperMinutes;
      metrics.gptTokensUsed += wasCached ? 0 : gptTokens;
      metrics.totalCost += totalCost;
      metrics.requestCount += 1;
      
      // Update average processing time
      const totalTime = (metrics.averageProcessingTime * (metrics.requestCount - 1)) + processingTimeMs;
      metrics.averageProcessingTime = totalTime / metrics.requestCount;
    });

    // Persist metrics
    this.persistMetrics();
  }

  /**
   * Get current usage statistics
   */
  getUsageStats(): {
    hourly: UsageMetrics;
    daily: UsageMetrics;
    monthly: UsageMetrics;
    recommendations: string[];
  } {
    this.updateMetrics();

    const recommendations: string[] = [];

    // Generate recommendations based on usage patterns
    if (this.hourlyMetrics.cacheHitRate < 0.3) {
      recommendations.push('Enable or increase caching to reduce API costs');
    }

    if (this.hourlyMetrics.averageProcessingTime > 5000) {
      recommendations.push('Consider optimizing prompts to reduce processing time');
    }

    const hourlyUsagePercent = (this.hourlyMetrics.totalCost / this.config.maxCostPerHour) * 100;
    if (hourlyUsagePercent > 50) {
      recommendations.push('Monitor hourly usage - currently at high levels');
    }

    const dailyUsagePercent = (this.dailyMetrics.totalCost / this.config.maxCostPerDay) * 100;
    if (dailyUsagePercent > 70) {
      recommendations.push('Consider increasing daily limit or reducing usage');
    }

    if (this.batchQueue.length > 10) {
      recommendations.push('Enable batch processing to optimize API efficiency');
    }

    return {
      hourly: { ...this.hourlyMetrics },
      daily: { ...this.dailyMetrics },
      monthly: { ...this.monthlyMetrics },
      recommendations
    };
  }

  /**
   * Add request to batch processing queue
   */
  addToBatch(audioBlob: Blob): Promise<any> {
    return new Promise((resolve, reject) => {
      this.batchQueue.push({ audioBlob, resolve, reject });
      
      // Process batch if it reaches optimal size or after timeout
      if (this.batchQueue.length >= 5) {
        this.processBatch();
      } else if (!this.batchProcessingTimer) {
        this.batchProcessingTimer = setTimeout(() => {
          this.processBatch();
        }, 2000); // 2 second timeout
      }
    });
  }

  /**
   * Process queued requests in batch
   */
  private async processBatch(): Promise<void> {
    if (this.batchQueue.length === 0) return;

    const batch = this.batchQueue.splice(0, 10); // Process up to 10 at once
    
    if (this.batchProcessingTimer) {
      clearTimeout(this.batchProcessingTimer);
      this.batchProcessingTimer = undefined;
    }

    // TODO: Implement actual batch processing with OpenAI API
    // For now, process individually but with shared context
    for (const item of batch) {
      try {
        // Individual processing would happen here
        // This is a placeholder for the actual batch optimization
        item.resolve(null);
      } catch (error) {
        item.reject(error);
      }
    }
  }

  /**
   * Estimate cost for a request
   */
  estimateRequestCost(audioLengthSeconds: number, expectedTokens: number = 150): number {
    const whisperCost = (audioLengthSeconds / 60) * this.config.whisperCostPerMinute;
    const gptCost = (expectedTokens / 1000) * this.config.gptCostPer1kTokens;
    return whisperCost + gptCost;
  }

  /**
   * Clear cache to free memory and reset costs
   */
  clearCache(): void {
    this.responseCache.clear();
  }

  /**
   * Get cost-optimized configuration recommendations
   */
  getOptimizationRecommendations(): {
    currentEfficiency: number;
    recommendations: Array<{
      action: string;
      impact: 'low' | 'medium' | 'high';
      estimatedSavings: number;
      description: string;
    }>;
  } {
    const stats = this.getUsageStats();
    const efficiency = stats.hourly.cacheHitRate * 0.4 + 
                     (stats.hourly.averageProcessingTime < 3000 ? 0.3 : 0) +
                     (this.config.enableBatchProcessing ? 0.3 : 0);

    const recommendations = [
      {
        action: 'Enable smart caching',
        impact: 'high' as const,
        estimatedSavings: stats.daily.totalCost * 0.3,
        description: 'Cache responses to avoid repeated API calls'
      },
      {
        action: 'Use batch processing',
        impact: 'medium' as const,
        estimatedSavings: stats.daily.totalCost * 0.15,
        description: 'Process multiple requests together for efficiency'
      },
      {
        action: 'Optimize prompt length',
        impact: 'low' as const,
        estimatedSavings: stats.daily.totalCost * 0.1,
        description: 'Reduce token usage with more concise prompts'
      }
    ];

    return {
      currentEfficiency: efficiency,
      recommendations: recommendations.filter(rec => {
        if (rec.action.includes('caching') && !this.config.enableSmartCaching) return true;
        if (rec.action.includes('batch') && !this.config.enableBatchProcessing) return true;
        if (rec.action.includes('prompt') && stats.hourly.averageProcessingTime > 4000) return true;
        return false;
      })
    };
  }

  // Private helper methods

  private initMetrics(period: 'hour' | 'day' | 'month'): UsageMetrics {
    return {
      whisperMinutesUsed: 0,
      gptTokensUsed: 0,
      totalCost: 0,
      requestCount: 0,
      cacheHitRate: 0,
      averageProcessingTime: 0,
      lastReset: Date.now(),
      period
    };
  }

  private updateMetrics(): void {
    const now = Date.now();
    
    // Check if we need to reset hourly metrics
    if (now - this.hourlyMetrics.lastReset > 60 * 60 * 1000) {
      this.hourlyMetrics = this.initMetrics('hour');
    }
    
    // Check if we need to reset daily metrics
    if (now - this.dailyMetrics.lastReset > 24 * 60 * 60 * 1000) {
      this.dailyMetrics = this.initMetrics('day');
    }
    
    // Check if we need to reset monthly metrics
    if (now - this.monthlyMetrics.lastReset > 30 * 24 * 60 * 60 * 1000) {
      this.monthlyMetrics = this.initMetrics('month');
    }
  }

  private updateCacheHitRate(): void {
    [this.hourlyMetrics, this.dailyMetrics, this.monthlyMetrics].forEach(metrics => {
      // Simple cache hit rate calculation
      metrics.cacheHitRate = Math.min(1, metrics.cacheHitRate + 0.1);
    });
  }

  private setupMetricsReset(): void {
    // Reset hourly metrics every hour
    setInterval(() => {
      this.hourlyMetrics = this.initMetrics('hour');
    }, 60 * 60 * 1000);

    // Reset daily metrics every day  
    setInterval(() => {
      this.dailyMetrics = this.initMetrics('day');
    }, 24 * 60 * 60 * 1000);
  }

  private persistMetrics(): void {
    try {
      const metricsData = {
        hourly: this.hourlyMetrics,
        daily: this.dailyMetrics,
        monthly: this.monthlyMetrics,
        timestamp: Date.now()
      };
      localStorage.setItem('voice-cost-metrics', JSON.stringify(metricsData));
    } catch (error) {
      console.warn('Failed to persist cost metrics:', error);
    }
  }

  private loadPersistedMetrics(): void {
    try {
      const stored = localStorage.getItem('voice-cost-metrics');
      if (stored) {
        const data = JSON.parse(stored);
        
        // Only load if recent (within last day)
        if (Date.now() - data.timestamp < 24 * 60 * 60 * 1000) {
          this.hourlyMetrics = { ...this.hourlyMetrics, ...data.hourly };
          this.dailyMetrics = { ...this.dailyMetrics, ...data.daily };
          this.monthlyMetrics = { ...this.monthlyMetrics, ...data.monthly };
        }
      }
    } catch (error) {
      console.warn('Failed to load persisted metrics:', error);
    }
  }
}

// Export singleton
let costOptimizerInstance: VoiceCostOptimizer | null = null;

export function getVoiceCostOptimizer(): VoiceCostOptimizer {
  if (!costOptimizerInstance) {
    costOptimizerInstance = new VoiceCostOptimizer();
  }
  return costOptimizerInstance;
}

export type { CostOptimizationConfig, UsageMetrics, CostAlert };