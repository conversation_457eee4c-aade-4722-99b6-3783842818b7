/**
 * Voice Processing Testing Suite
 * 
 * Comprehensive testing system for voice recognition accuracy,
 * performance benchmarking, and seafood terminology validation
 */

interface TestCase {
  id: string;
  name: string;
  transcript: string;
  expectedOutput: {
    action_type: string;
    event_type?: string;
    product_name?: string;
    quantity?: number;
    unit?: string;
    vendor_name?: string;
    customer_name?: string;
    condition?: string;
    price?: number;
  };
  difficulty: 'easy' | 'medium' | 'hard';
  category: 'receiving' | 'disposal' | 'sale' | 'query' | 'navigation';
  tags: string[];
}

interface TestResult {
  testCaseId: string;
  passed: boolean;
  score: number; // 0-1
  processingTimeMs: number;
  actualOutput: any;
  expectedOutput: any;
  errors: string[];
  confidence: number;
  timestamp: number;
}

interface TestSuiteResults {
  totalTests: number;
  passedTests: number;
  failedTests: number;
  averageScore: number;
  averageProcessingTime: number;
  averageConfidence: number;
  categoryResults: Record<string, { passed: number; total: number; score: number }>;
  difficultyResults: Record<string, { passed: number; total: number; score: number }>;
  results: TestResult[];
  recommendations: string[];
}

export class VoiceTestingSuite {
  private testCases: TestCase[] = [];
  private voiceProcessor: any; // VoiceProcessor instance
  
  constructor(voiceProcessor: any) {
    this.voiceProcessor = voiceProcessor;
    this.initializeTestCases();
  }

  /**
   * Initialize comprehensive test cases for seafood inventory commands
   */
  private initializeTestCases(): void {
    this.testCases = [
      // RECEIVING TESTS
      {
        id: 'recv_001',
        name: 'Basic receiving command',
        transcript: 'receive 10 pounds of coho salmon from 49th state seafoods',
        expectedOutput: {
          action_type: 'create_event',
          event_type: 'receiving',
          product_name: 'Coho Salmon',
          quantity: 10,
          unit: 'lbs',
          vendor_name: '49th State Seafoods'
        },
        difficulty: 'easy',
        category: 'receiving',
        tags: ['salmon', 'vendor', 'quantity']
      },
      {
        id: 'recv_002', 
        name: 'Receiving with condition and date',
        transcript: 'received 25 pounds dungeness crab from pacific seafoods yesterday condition excellent',
        expectedOutput: {
          action_type: 'create_event',
          event_type: 'receiving',
          product_name: 'Dungeness Crab',
          quantity: 25,
          unit: 'lbs',
          vendor_name: 'Pacific Seafoods',
          condition: 'Excellent'
        },
        difficulty: 'medium',
        category: 'receiving',
        tags: ['crab', 'condition', 'date']
      },
      {
        id: 'recv_003',
        name: 'Complex receiving with temperature',
        transcript: 'add receiving event 50 pounds of pacific cod from ocean fresh delivered this morning temperature 32 degrees fahrenheit condition good',
        expectedOutput: {
          action_type: 'create_event',
          event_type: 'receiving',
          product_name: 'Pacific Cod',
          quantity: 50,
          unit: 'lbs',
          vendor_name: 'Ocean Fresh',
          condition: 'Good'
        },
        difficulty: 'hard',
        category: 'receiving',
        tags: ['cod', 'temperature', 'complex']
      },
      
      // DISPOSAL TESTS
      {
        id: 'disp_001',
        name: 'Basic disposal command',
        transcript: 'dispose 5 pounds of salmon expired',
        expectedOutput: {
          action_type: 'create_event',
          event_type: 'disposal',
          product_name: 'Salmon',
          quantity: 5,
          unit: 'lbs'
        },
        difficulty: 'easy',
        category: 'disposal',
        tags: ['salmon', 'expired']
      },
      {
        id: 'disp_002',
        name: 'Disposal with specific reason',
        transcript: 'disposal 12 pounds king crab damaged during transport',
        expectedOutput: {
          action_type: 'create_event',
          event_type: 'disposal',
          product_name: 'King Crab',
          quantity: 12,
          unit: 'lbs'
        },
        difficulty: 'medium',
        category: 'disposal',
        tags: ['crab', 'damage', 'transport']
      },
      
      // SALES TESTS
      {
        id: 'sale_001',
        name: 'Basic sale command',
        transcript: 'sell 20 pounds halibut to ocean restaurant',
        expectedOutput: {
          action_type: 'create_event',
          event_type: 'sale',
          product_name: 'Halibut',
          quantity: 20,
          unit: 'lbs',
          customer_name: 'Ocean Restaurant'
        },
        difficulty: 'easy',
        category: 'sale',
        tags: ['halibut', 'restaurant']
      },
      {
        id: 'sale_002',
        name: 'Sale with price',
        transcript: 'sold 15 pounds of alaskan halibut to marina bistro at twelve dollars per pound',
        expectedOutput: {
          action_type: 'create_event',
          event_type: 'sale',
          product_name: 'Alaskan Halibut',
          quantity: 15,
          unit: 'lbs',
          customer_name: 'Marina Bistro',
          price: 12
        },
        difficulty: 'medium',
        category: 'sale',
        tags: ['halibut', 'price', 'number-words']
      },
      
      // QUERY TESTS
      {
        id: 'query_001',
        name: 'Basic inventory query',
        transcript: 'how much salmon do we have',
        expectedOutput: {
          action_type: 'query_inventory',
          product_name: 'Salmon'
        },
        difficulty: 'easy',
        category: 'query',
        tags: ['salmon', 'inventory']
      },
      {
        id: 'query_002',
        name: 'Specific species query',
        transcript: 'show me inventory for coho salmon',
        expectedOutput: {
          action_type: 'query_inventory',
          product_name: 'Coho Salmon'
        },
        difficulty: 'medium',
        category: 'query',
        tags: ['salmon', 'species-specific']
      },
      
      // NAVIGATION TESTS
      {
        id: 'nav_001',
        name: 'Basic navigation',
        transcript: 'show me events',
        expectedOutput: {
          action_type: 'navigate'
        },
        difficulty: 'easy',
        category: 'navigation',
        tags: ['events', 'navigation']
      },
      
      // CHALLENGING CASES
      {
        id: 'challenge_001',
        name: 'Multiple products in one command',
        transcript: 'received 30 pounds coho salmon and 20 pounds dungeness crab from 49th state seafoods',
        expectedOutput: {
          action_type: 'create_event',
          event_type: 'receiving',
          product_name: 'Coho Salmon', // Should pick first or most prominent
          quantity: 30,
          unit: 'lbs',
          vendor_name: '49th State Seafoods'
        },
        difficulty: 'hard',
        category: 'receiving',
        tags: ['multiple-products', 'complex']
      },
      {
        id: 'challenge_002',
        name: 'Vendor name variations',
        transcript: 'receive 40 pounds cod from forty ninth state seafoods',
        expectedOutput: {
          action_type: 'create_event',
          event_type: 'receiving',
          product_name: 'Cod',
          quantity: 40,
          unit: 'lbs',
          vendor_name: '49th State Seafoods' // Should normalize number format
        },
        difficulty: 'hard',
        category: 'receiving',
        tags: ['vendor-variations', 'number-normalization']
      },
      {
        id: 'challenge_003',
        name: 'Colloquial language',
        transcript: 'we got in 15 lbs of crab from the usual supplier yesterday',
        expectedOutput: {
          action_type: 'create_event',
          event_type: 'receiving',
          product_name: 'Crab',
          quantity: 15,
          unit: 'lbs'
        },
        difficulty: 'hard',
        category: 'receiving',
        tags: ['colloquial', 'implicit-vendor']
      }
    ];
  }

  /**
   * Run all test cases
   */
  async runAllTests(): Promise<TestSuiteResults> {
    console.log('🧪 Starting Voice Processing Test Suite...');
    
    const results: TestResult[] = [];
    
    for (const testCase of this.testCases) {
      console.log(`Testing: ${testCase.name}...`);
      const result = await this.runSingleTest(testCase);
      results.push(result);
    }

    return this.analyzeResults(results);
  }

  /**
   * Run tests by category
   */
  async runTestsByCategory(category: string): Promise<TestSuiteResults> {
    const filteredTests = this.testCases.filter(tc => tc.category === category);
    const results: TestResult[] = [];
    
    for (const testCase of filteredTests) {
      const result = await this.runSingleTest(testCase);
      results.push(result);
    }

    return this.analyzeResults(results);
  }

  /**
   * Run tests by difficulty
   */
  async runTestsByDifficulty(difficulty: string): Promise<TestSuiteResults> {
    const filteredTests = this.testCases.filter(tc => tc.difficulty === difficulty);
    const results: TestResult[] = [];
    
    for (const testCase of filteredTests) {
      const result = await this.runSingleTest(testCase);
      results.push(result);
    }

    return this.analyzeResults(results);
  }

  /**
   * Run a single test case
   */
  private async runSingleTest(testCase: TestCase): Promise<TestResult> {
    const startTime = Date.now();
    
    try {
      // Create a mock audio blob for testing (since we're testing the parsing, not audio processing)
      const mockResult = await this.voiceProcessor.parseSeafoodCommand(testCase.transcript);
      const processingTime = Date.now() - startTime;

      const score = this.calculateScore(testCase.expectedOutput, mockResult);
      const passed = score >= 0.8; // Pass threshold of 80%

      return {
        testCaseId: testCase.id,
        passed,
        score,
        processingTimeMs: processingTime,
        actualOutput: mockResult,
        expectedOutput: testCase.expectedOutput,
        errors: this.findErrors(testCase.expectedOutput, mockResult),
        confidence: mockResult.confidence_score || 0,
        timestamp: Date.now()
      };
    } catch (error) {
      return {
        testCaseId: testCase.id,
        passed: false,
        score: 0,
        processingTimeMs: Date.now() - startTime,
        actualOutput: null,
        expectedOutput: testCase.expectedOutput,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
        confidence: 0,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Calculate accuracy score between expected and actual output
   */
  private calculateScore(expected: any, actual: any): number {
    if (!actual) return 0;

    let score = 0;
    let totalFields = 0;

    // Check each expected field
    for (const [key, expectedValue] of Object.entries(expected)) {
      totalFields++;
      const actualValue = actual[key];

      if (actualValue === undefined || actualValue === null) {
        continue; // No points for missing fields
      }

      // Exact match
      if (actualValue === expectedValue) {
        score += 1;
        continue;
      }

      // Fuzzy matching for strings
      if (typeof expectedValue === 'string' && typeof actualValue === 'string') {
        const similarity = this.calculateStringSimilarity(
          expectedValue.toLowerCase(),
          actualValue.toLowerCase()
        );
        score += similarity;
        continue;
      }

      // Numeric tolerance
      if (typeof expectedValue === 'number' && typeof actualValue === 'number') {
        const tolerance = Math.abs(expectedValue - actualValue) / expectedValue;
        if (tolerance <= 0.1) { // 10% tolerance
          score += 1;
        } else if (tolerance <= 0.2) { // 20% tolerance gets partial credit
          score += 0.5;
        }
        continue;
      }
    }

    return totalFields > 0 ? score / totalFields : 0;
  }

  /**
   * Find specific errors between expected and actual output
   */
  private findErrors(expected: any, actual: any): string[] {
    const errors: string[] = [];

    if (!actual) {
      errors.push('No output generated');
      return errors;
    }

    for (const [key, expectedValue] of Object.entries(expected)) {
      const actualValue = actual[key];

      if (actualValue === undefined || actualValue === null) {
        errors.push(`Missing field: ${key}`);
        continue;
      }

      if (typeof expectedValue === 'string' && typeof actualValue === 'string') {
        const similarity = this.calculateStringSimilarity(
          expectedValue.toLowerCase(),
          actualValue.toLowerCase()
        );
        if (similarity < 0.8) {
          errors.push(`${key}: expected "${expectedValue}", got "${actualValue}"`);
        }
        continue;
      }

      if (expectedValue !== actualValue) {
        errors.push(`${key}: expected "${expectedValue}", got "${actualValue}"`);
      }
    }

    return errors;
  }

  /**
   * Calculate string similarity using Levenshtein distance
   */
  private calculateStringSimilarity(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1, // deletion
          matrix[j - 1][i] + 1, // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        );
      }
    }

    const distance = matrix[str2.length][str1.length];
    const maxLength = Math.max(str1.length, str2.length);
    return maxLength === 0 ? 1 : (maxLength - distance) / maxLength;
  }

  /**
   * Analyze test results and generate summary
   */
  private analyzeResults(results: TestResult[]): TestSuiteResults {
    const totalTests = results.length;
    const passedTests = results.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    
    const averageScore = results.reduce((sum, r) => sum + r.score, 0) / totalTests;
    const averageProcessingTime = results.reduce((sum, r) => sum + r.processingTimeMs, 0) / totalTests;
    const averageConfidence = results.reduce((sum, r) => sum + r.confidence, 0) / totalTests;

    // Category analysis
    const categoryResults: Record<string, { passed: number; total: number; score: number }> = {};
    const difficultyResults: Record<string, { passed: number; total: number; score: number }> = {};

    for (const result of results) {
      const testCase = this.testCases.find(tc => tc.id === result.testCaseId)!;
      
      // Category stats
      if (!categoryResults[testCase.category]) {
        categoryResults[testCase.category] = { passed: 0, total: 0, score: 0 };
      }
      categoryResults[testCase.category].total++;
      categoryResults[testCase.category].score += result.score;
      if (result.passed) categoryResults[testCase.category].passed++;

      // Difficulty stats  
      if (!difficultyResults[testCase.difficulty]) {
        difficultyResults[testCase.difficulty] = { passed: 0, total: 0, score: 0 };
      }
      difficultyResults[testCase.difficulty].total++;
      difficultyResults[testCase.difficulty].score += result.score;
      if (result.passed) difficultyResults[testCase.difficulty].passed++;
    }

    // Normalize scores
    Object.values(categoryResults).forEach(cat => {
      cat.score = cat.score / cat.total;
    });
    Object.values(difficultyResults).forEach(diff => {
      diff.score = diff.score / diff.total;
    });

    // Generate recommendations
    const recommendations = this.generateRecommendations(results, categoryResults, difficultyResults);

    return {
      totalTests,
      passedTests,
      failedTests,
      averageScore,
      averageProcessingTime,
      averageConfidence,
      categoryResults,
      difficultyResults,
      results,
      recommendations
    };
  }

  /**
   * Generate recommendations based on test results
   */
  private generateRecommendations(
    results: TestResult[],
    categoryResults: Record<string, any>,
    difficultyResults: Record<string, any>
  ): string[] {
    const recommendations: string[] = [];

    // Overall performance recommendations
    const averageScore = results.reduce((sum, r) => sum + r.score, 0) / results.length;
    if (averageScore < 0.7) {
      recommendations.push('Overall accuracy below 70% - consider improving GPT-4 prompts and seafood terminology');
    }

    // Processing time recommendations
    const averageTime = results.reduce((sum, r) => sum + r.processingTimeMs, 0) / results.length;
    if (averageTime > 3000) {
      recommendations.push('Average processing time over 3 seconds - optimize API calls and caching');
    }

    // Category-specific recommendations
    Object.entries(categoryResults).forEach(([category, stats]) => {
      if (stats.score < 0.8) {
        recommendations.push(`${category} commands need improvement - only ${Math.round(stats.score * 100)}% accuracy`);
      }
    });

    // Difficulty-specific recommendations  
    if (difficultyResults.hard?.score < 0.6) {
      recommendations.push('Hard difficulty tests struggling - enhance complex command parsing');
    }

    // Specific error pattern analysis
    const commonErrors = new Map<string, number>();
    results.forEach(result => {
      result.errors.forEach(error => {
        commonErrors.set(error, (commonErrors.get(error) || 0) + 1);
      });
    });

    // Top 3 most common errors
    const sortedErrors = Array.from(commonErrors.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3);

    sortedErrors.forEach(([error, count]) => {
      if (count >= 3) {
        recommendations.push(`Common issue: ${error} (occurred ${count} times)`);
      }
    });

    return recommendations;
  }

  /**
   * Export test results to JSON
   */
  exportResults(results: TestSuiteResults): string {
    return JSON.stringify({
      timestamp: new Date().toISOString(),
      summary: {
        totalTests: results.totalTests,
        passedTests: results.passedTests,
        failedTests: results.failedTests,
        averageScore: Math.round(results.averageScore * 100) / 100,
        averageProcessingTime: Math.round(results.averageProcessingTime),
        averageConfidence: Math.round(results.averageConfidence * 100) / 100
      },
      categoryResults: results.categoryResults,
      difficultyResults: results.difficultyResults,
      recommendations: results.recommendations,
      detailedResults: results.results.map(r => ({
        testId: r.testCaseId,
        passed: r.passed,
        score: Math.round(r.score * 100) / 100,
        processingTimeMs: r.processingTimeMs,
        confidence: Math.round(r.confidence * 100) / 100,
        errors: r.errors
      }))
    }, null, 2);
  }
}

export type { TestCase, TestResult, TestSuiteResults };