/**
 * Enhanced TempStick API Service
 * 
 * Handles integration with TempStick temperature monitoring sensors with:
 * - Robust error handling and retry logic
 * - API rate limiting and request throttling
 * - Comprehensive logging and monitoring
 * - Data quality validation
 * - Health check endpoints
 * 
 * @see https://tempstickapi.com/docs/
 */

import { supabase } from './supabase';
import type { 
  TempStickSensor, 
  TempStickReading, 
  TempStickApiResponse,
  Sensor,
  TemperatureReading,
  TemperatureAlert,
  SyncResponse,
  SystemHealth
} from '../types/tempstick';
import type {
  HistoricalDataRequest,
  HistoricalDataResponse,
  HistoricalDataPoint,
  SensorStatistics,
  DateRange,
  AggregationInterval,
  AggregatedDataPoint
} from '../types/historical-dashboard';

// Enhanced error types for better error handling
export class TempStickApiError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public retryable: boolean = false,
    public rateLimited: boolean = false
  ) {
    super(message);
    this.name = 'TempStickApiError';
  }
}

export class TempStickServiceError extends Error {
  constructor(
    message: string,
    public operation: string,
    public retryable: boolean = false
  ) {
    super(message);
    this.name = 'TempStickServiceError';
  }
}

// Rate limiting and retry configuration
interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
}

interface RateLimitConfig {
  requestsPerMinute: number;
  burstLimit: number;
}

// Service health monitoring
interface ServiceMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  rateLimitedRequests: number;
  averageResponseTime: number;
  lastSyncTime: Date | null;
  lastErrorTime: Date | null;
  lastError: string | null;
}

/**
 * Enhanced TempStick API Client with rate limiting and retry logic
 */
class TempStickApiClient {
  private apiKey: string;
  private baseUrl: string;
  private retryConfig: RetryConfig;
  private rateLimitConfig: RateLimitConfig;
  private requestQueue: Array<{ timestamp: number; resolve: Function; reject: Function }> = [];
  private metrics: ServiceMetrics;

  constructor(apiKey: string, config?: { retry?: Partial<RetryConfig>; rateLimit?: Partial<RateLimitConfig>; baseUrl?: string }) {
    if (!apiKey) {
      throw new TempStickApiError('TempStick API key is required');
    }

    this.apiKey = apiKey;
    // Fix: Use proper API v1 endpoints according to TempStick documentation
    this.baseUrl = config?.baseUrl || '/api/tempstick';
    this.retryConfig = {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 30000,
      backoffMultiplier: 2,
      ...config?.retry
    };
    this.rateLimitConfig = {
      requestsPerMinute: 60,
      burstLimit: 10,
      ...config?.rateLimit
    };
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      rateLimitedRequests: 0,
      averageResponseTime: 0,
      lastSyncTime: null,
      lastErrorTime: null,
      lastError: null
    };
  }

  /**
   * Enhanced request method with retry logic and rate limiting
   */
  private async request<T>(endpoint: string, options?: RequestInit): Promise<TempStickApiResponse<T>> {
    const startTime = Date.now();
    let lastError: Error;

    for (let attempt = 0; attempt <= this.retryConfig.maxRetries; attempt++) {
      try {
        // Apply rate limiting
        await this.enforceRateLimit();
        
        // Fix: Ensure proper endpoint construction
        const url = `${this.baseUrl}${endpoint.startsWith('/') ? endpoint : `/${  endpoint}`}`;
        this.metrics.totalRequests++;
        
        console.log(`🌐 TempStick API Request: ${url}`);
        
        const response = await fetch(url, {
          ...options,
          headers: {
            'X-API-KEY': this.apiKey,
            'Content-Type': 'application/json',
            'User-Agent': 'Seafood-Manager/1.0',
            'Accept': 'application/json',
            ...options?.headers,
          },
          timeout: 30000, // 30 second timeout
        });

        const responseTime = Date.now() - startTime;
        this.updateMetrics(responseTime, true);

        console.log(`📡 TempStick API Response: ${response.status} ${response.statusText}`);

        if (!response.ok) {
          const errorText = await response.text();
          const isRateLimited = response.status === 429;
          const isRetryable = response.status >= 500 || isRateLimited;
          
          if (isRateLimited) {
            this.metrics.rateLimitedRequests++;
            const retryAfter = response.headers.get('Retry-After');
            const delay = retryAfter ? parseInt(retryAfter) * 1000 : this.calculateDelay(attempt);
            
            if (attempt < this.retryConfig.maxRetries) {
              console.warn(`⏳ Rate limited, retrying after ${delay}ms (attempt ${attempt + 1}/${this.retryConfig.maxRetries + 1})`);
              await this.sleep(delay);
              continue;
            }
          }
          
          throw new TempStickApiError(
            `TempStick API error: ${response.status} ${response.statusText} - ${errorText}`,
            response.status,
            isRetryable,
            isRateLimited
          );
        }

        const data = await response.json();
        console.log(`✅ TempStick API Success:`, data);
        
        this.metrics.successfulRequests++;
        this.metrics.lastSyncTime = new Date();
        
        return data;
        
      } catch (error) {
        lastError = error as Error;
        this.updateMetrics(Date.now() - startTime, false, error as Error);
        
        if (error instanceof TempStickApiError && !error.retryable) {
          throw error;
        }
        
        if (attempt < this.retryConfig.maxRetries) {
          const delay = this.calculateDelay(attempt);
          console.warn(`⚠️ Request failed, retrying after ${delay}ms (attempt ${attempt + 1}/${this.retryConfig.maxRetries + 1}):`, error.message);
          await this.sleep(delay);
        }
      }
    }
    
    throw new TempStickApiError(
      `Request failed after ${this.retryConfig.maxRetries + 1} attempts: ${lastError.message}`,
      undefined,
      false
    );
  }

  /**
   * Rate limiting enforcement
   */
  private async enforceRateLimit(): Promise<void> {
    const now = Date.now();
    const oneMinuteAgo = now - 60000;
    
    // Clean old requests from queue
    this.requestQueue = this.requestQueue.filter(req => req.timestamp > oneMinuteAgo);
    
    // Check if we're within rate limits
    if (this.requestQueue.length >= this.rateLimitConfig.requestsPerMinute) {
      const oldestRequest = this.requestQueue[0];
      const waitTime = 60000 - (now - oldestRequest.timestamp);
      
      if (waitTime > 0) {
        console.log(`⏱️ Rate limit reached, waiting ${waitTime}ms`);
        await this.sleep(waitTime);
      }
    }
    
    // Add current request to queue
    this.requestQueue.push({
      timestamp: now,
      resolve: () => {},
      reject: () => {}
    });
  }

  /**
   * Calculate exponential backoff delay
   */
  private calculateDelay(attempt: number): number {
    const delay = this.retryConfig.baseDelay * Math.pow(this.retryConfig.backoffMultiplier, attempt);
    return Math.min(delay, this.retryConfig.maxDelay);
  }

  /**
   * Sleep utility
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Update service metrics
   */
  private updateMetrics(responseTime: number, success: boolean, error?: Error): void {
    if (success) {
      this.metrics.averageResponseTime = 
        (this.metrics.averageResponseTime * this.metrics.successfulRequests + responseTime) / 
        (this.metrics.successfulRequests + 1);
    } else {
      this.metrics.failedRequests++;
      this.metrics.lastErrorTime = new Date();
      this.metrics.lastError = error?.message || 'Unknown error';
    }
  }

  /**
   * Get service health metrics
   */
  public getMetrics(): ServiceMetrics {
    return { ...this.metrics };
  }

  /**
   * Reset metrics (useful for monitoring)
   */
  public resetMetrics(): void {
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      rateLimitedRequests: 0,
      averageResponseTime: 0,
      lastSyncTime: null,
      lastErrorTime: null,
      lastError: null
    };
  }

  /**
   * Get all sensors from TempStick API v1 - Fixed endpoint
   */
  async getSensors(): Promise<TempStickSensor[]> {
    try {
      console.log('🔍 Fetching sensors from /sensors/all endpoint...');
      const response = await this.request<any>('/sensors/all');
      
      // Handle TempStick API response format according to official docs
      // The API returns: { success: true, sensors: [...] } or { sensors: [...] }
      const sensors = response?.sensors || response?.data?.sensors || response?.data?.items || [];
      
      console.log(`📊 Retrieved ${sensors?.length || 0} sensors from API`);
      
      if (!Array.isArray(sensors)) {
        console.warn('⚠️ Sensors data is not an array:', sensors);
        return [];
      }
      
      // Validate sensor data quality
      const validSensors = sensors.filter((sensor: TempStickSensor) => this.validateSensorData(sensor));
      console.log(`✅ ${validSensors.length} sensors passed validation`);
      
      return validSensors;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('❌ Failed to fetch sensors:', errorMessage);
      throw new TempStickApiError(
        `Failed to fetch sensors: ${errorMessage}`,
        undefined,
        true
      );
    }
  }

  /**
   * Get latest readings for a specific sensor - Fixed endpoint format
   */
  async getLatestReadings(sensorId: string, limit: number = 100): Promise<TempStickReading[]> {
    try {
      console.log(`🔍 Fetching latest ${limit} readings for sensor ${sensorId}...`);
      
      // Fix: Use correct TempStick API endpoint format
      const endpoint = `/readings/${encodeURIComponent(sensorId)}/${limit}`;
      console.log(`📡 API endpoint: ${endpoint}`);
      
      const response = await this.request<any>(endpoint);
      
      // Handle TempStick API response format
      // The API may return: { success: true, data: { items: [...] } } or { readings: [...] }
      const readings = response?.data?.items || response?.readings || response?.data?.readings || [];
      
      console.log(`📊 Retrieved ${readings?.length || 0} readings for sensor ${sensorId}`);
      
      if (!Array.isArray(readings)) {
        console.warn('⚠️ Readings data is not an array:', readings);
        return [];
      }
      
      // Convert TempStick API format to our internal format
      const convertedReadings = readings.map((reading: any) => ({
        temperature: parseFloat(reading.temp || reading.temperature || 0),
        humidity: reading.hum !== undefined ? parseFloat(reading.hum || reading.humidity || 0) : undefined,
        timestamp: reading.date || reading.timestamp || reading.recorded_at || new Date().toISOString(),
        sensor_id: sensorId,
        battery_level: reading.battery || reading.battery_pct,
        signal_strength: reading.rssi ? parseInt(reading.rssi) : undefined
      }));
      
      // Validate and filter readings
      const validReadings = convertedReadings.filter(reading => this.validateReadingData(reading));
      console.log(`✅ ${validReadings.length} readings passed validation`);
      
      return validReadings;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      // Check if this is a 404 error indicating historical data is not available
      if (errorMessage.includes('404') || errorMessage.includes('Not Found')) {
        console.warn(`📭 Historical readings not available for sensor ${sensorId} (this is normal for some sensors)`);
        return []; // Return empty array instead of throwing error
      }
      
      console.error(`❌ Failed to fetch readings for sensor ${sensorId}:`, errorMessage);
      throw new TempStickApiError(
        `Failed to fetch readings for sensor ${sensorId}: ${errorMessage}`,
        undefined,
        true
      );
    }
  }

  /**
   * Get historical readings for a sensor within a date range - New implementation
   */
  async getReadingsForPeriod(
    sensorId: string,
    startDate: Date,
    endDate: Date
  ): Promise<TempStickReading[]> {
    try {
      // Validate date range
      if (startDate >= endDate) {
        throw new TempStickApiError('Start date must be before end date', undefined, false);
      }
      
      const maxRangeMs = 30 * 24 * 60 * 60 * 1000; // 30 days
      if (endDate.getTime() - startDate.getTime() > maxRangeMs) {
        throw new TempStickApiError('Date range cannot exceed 30 days', undefined, false);
      }
      
      const start = startDate.toISOString();
      const end = endDate.toISOString();
      
      console.log(`🔍 Fetching historical readings for sensor ${sensorId} from ${start} to ${end}...`);
      
      // Use TempStick API endpoint for historical data
      const endpoint = `/sensors/${encodeURIComponent(sensorId)}/readings?start=${encodeURIComponent(start)}&end=${encodeURIComponent(end)}`;
      
      const response = await this.request<any>(endpoint);
      const readings = response?.data?.readings || response?.readings || [];
      
      console.log(`📊 Retrieved ${readings?.length || 0} historical readings`);
      
      if (!Array.isArray(readings)) {
        console.warn('⚠️ Historical readings data is not an array:', readings);
        return [];
      }
      
      // Convert and validate readings
      const convertedReadings = readings.map((reading: any) => ({
        temperature: parseFloat(reading.temp || reading.temperature || 0),
        humidity: reading.hum !== undefined ? parseFloat(reading.hum || reading.humidity || 0) : undefined,
        timestamp: reading.date || reading.timestamp || reading.recorded_at || new Date().toISOString(),
        sensor_id: sensorId,
        battery_level: reading.battery || reading.battery_pct,
        signal_strength: reading.rssi ? parseInt(reading.rssi) : undefined
      }));
      
      const validReadings = convertedReadings.filter(reading => this.validateReadingData(reading));
      console.log(`✅ ${validReadings.length} historical readings passed validation`);
      
      return validReadings;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`❌ Failed to fetch historical readings for sensor ${sensorId}:`, errorMessage);
      throw new TempStickApiError(
        `Failed to fetch historical readings for sensor ${sensorId}: ${errorMessage}`,
        undefined,
        true
      );
    }
  }

  /**
   * Get all alerts from TempStick API - New implementation
   */
  async getAlerts(): Promise<any[]> {
    try {
      console.log('🔍 Fetching alerts from /alerts endpoint...');
      
      const response = await this.request<any>('/alerts');
      
      // Handle TempStick API response format
      const alerts = response?.alerts || response?.data?.alerts || response?.data?.items || [];
      
      console.log(`📊 Retrieved ${alerts?.length || 0} alerts from API`);
      
      if (!Array.isArray(alerts)) {
        console.warn('⚠️ Alerts data is not an array:', alerts);
        return [];
      }
      
      return alerts;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('❌ Failed to fetch alerts:', errorMessage);
      throw new TempStickApiError(
        `Failed to fetch alerts: ${errorMessage}`,
        undefined,
        true
      );
    }
  }

  /**
   * Get user notifications from TempStick API - New implementation
   */
  async getNotifications(): Promise<any[]> {
    try {
      console.log('🔍 Fetching notifications from /notifications/user endpoint...');
      
      const response = await this.request<any>('/notifications/user');
      
      // Handle TempStick API response format
      const notifications = response?.notifications || response?.data?.notifications || response?.data?.items || [];
      
      console.log(`📊 Retrieved ${notifications?.length || 0} notifications from API`);
      
      if (!Array.isArray(notifications)) {
        console.warn('⚠️ Notifications data is not an array:', notifications);
        return [];
      }
      
      return notifications;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('❌ Failed to fetch notifications:', errorMessage);
      throw new TempStickApiError(
        `Failed to fetch notifications: ${errorMessage}`,
        undefined,
        true
      );
    }
  }

  /**
   * Get sensor health status - Enhanced implementation
   */
  async getSensorHealth(sensorId: string): Promise<{ online: boolean; batteryLevel: number; signalStrength: number }> {
    try {
      console.log(`🔍 Fetching health status for sensor ${sensorId}...`);
      
      const response = await this.request<any>(`/sensors/${encodeURIComponent(sensorId)}/health`);
      const health = response?.data?.health || response?.health || {};
      
      const result = {
        online: health.online !== undefined ? Boolean(health.online) : false,
        batteryLevel: parseInt(health.batteryLevel || health.battery_pct || 0),
        signalStrength: parseInt(health.signalStrength || health.rssi || 0)
      };
      
      console.log(`✅ Health status for sensor ${sensorId}:`, result);
      return result;
    } catch (error) {
      console.warn(`⚠️ Failed to get health for sensor ${sensorId}:`, error.message);
      return { online: false, batteryLevel: 0, signalStrength: 0 };
    }
  }

  /**
   * Batch get multiple sensors' latest readings - Enhanced implementation
   */
  async getBatchLatestReadings(sensorIds: string[]): Promise<Record<string, TempStickReading[]>> {
    const results: Record<string, TempStickReading[]> = {};
    
    console.log(`🔄 Fetching batch readings for ${sensorIds.length} sensors...`);
    
    // Process in batches to avoid overwhelming the API
    const batchSize = 5;
    for (let i = 0; i < sensorIds.length; i += batchSize) {
      const batch = sensorIds.slice(i, i + batchSize);
      console.log(`📦 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(sensorIds.length / batchSize)}...`);
      
      const promises = batch.map(async (sensorId) => {
        try {
          const readings = await this.getLatestReadings(sensorId, 10);
          return { sensorId, readings };
        } catch (error) {
          console.warn(`⚠️ Failed to get readings for sensor ${sensorId}:`, error.message);
          return { sensorId, readings: [] };
        }
      });
      
      const batchResults = await Promise.all(promises);
      batchResults.forEach(({ sensorId, readings }) => {
        results[sensorId] = readings;
      });
      
      // Small delay between batches to be respectful to the API
      if (i + batchSize < sensorIds.length) {
        await this.sleep(500); // Increased delay for better rate limiting
      }
    }
    
    console.log(`✅ Batch fetch completed for ${Object.keys(results).length} sensors`);
    return results;
  }

  /**
   * Validate sensor data quality - Enhanced validation
   */
  private validateSensorData(sensor: any): boolean {
    if (!sensor) {
      console.warn('⚠️ Invalid sensor: null or undefined');
      return false;
    }

    // Check for required sensor ID fields
    const sensorId = sensor.sensor_id || sensor.id;
    if (!sensorId || typeof sensorId !== 'string') {
      console.warn('⚠️ Invalid sensor: missing or invalid sensor_id', sensor);
      return false;
    }
    
    // Check for sensor name
    const sensorName = sensor.sensor_name || sensor.name;
    if (!sensorName || typeof sensorName !== 'string') {
      console.warn('⚠️ Invalid sensor: missing sensor_name', sensor);
      return false;
    }
    
    // Validate temperature if present
    if (sensor.last_temp !== undefined && 
        (isNaN(parseFloat(sensor.last_temp)) || parseFloat(sensor.last_temp) < -100 || parseFloat(sensor.last_temp) > 200)) {
      console.warn('⚠️ Invalid sensor: temperature out of reasonable range', sensor);
      return false;
    }
    
    return true;
  }

  /**
   * Validate temperature reading data quality - Enhanced validation
   */
  private validateReadingData(reading: any): boolean {
    if (!reading) {
      console.warn('⚠️ Invalid reading: null or undefined');
      return false;
    }

    // Check required fields
    if (!reading.sensor_id || !reading.timestamp) {
      console.warn('⚠️ Invalid reading: missing sensor_id or timestamp', reading);
      return false;
    }
    
    // Validate temperature range (reasonable for food storage)
    if (typeof reading.temperature !== 'number' || 
        isNaN(reading.temperature) ||
        reading.temperature < -50 || reading.temperature > 150) {
      console.warn('⚠️ Invalid reading: temperature out of reasonable range', reading);
      return false;
    }
    
    // Validate humidity if present
    if (reading.humidity !== undefined && 
        (typeof reading.humidity !== 'number' || 
         isNaN(reading.humidity) || 
         reading.humidity < 0 || reading.humidity > 100)) {
      console.warn('⚠️ Invalid reading: humidity out of valid range', reading);
      return false;
    }
    
    // Validate timestamp
    const readingTime = new Date(reading.timestamp);
    const now = new Date();
    const oneYearAgo = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
    
    if (isNaN(readingTime.getTime()) || readingTime > now || readingTime < oneYearAgo) {
      console.warn('⚠️ Invalid reading: timestamp out of reasonable range', reading);
      return false;
    }
    
    return true;
  }

  /**
   * Test API connectivity and authentication - Enhanced testing
   */
  async testConnection(): Promise<{ success: boolean; latency: number; error?: string }> {
    const startTime = Date.now();
    
    try {
      console.log('🔍 Testing TempStick API connection...');
      
      const response = await this.request('/sensors/all');
      const latency = Date.now() - startTime;
      
      console.log(`✅ Connection test successful - latency: ${latency}ms`);
      return { success: true, latency };
    } catch (error) {
      const latency = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown connection error';
      
      console.error(`❌ Connection test failed - latency: ${latency}ms, error:`, errorMessage);
      return { 
        success: false, 
        latency, 
        error: errorMessage 
      };
    }
  }
}

/**
 * Enhanced TempStick Service for Seafood Manager Integration
 */
export class TempStickService {
  private apiClient: TempStickApiClient | null;
  private syncInProgress: boolean = false;
  private lastFullSync: Date | null = null;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private lastApiCall: number = 0;
  private minCallInterval: number = 2000; // Minimum 2 seconds between API calls

  constructor(config?: { 
    apiKey?: string; 
    retry?: Partial<RetryConfig>; 
    rateLimit?: Partial<RateLimitConfig>;
    enableHealthChecks?: boolean;
  }) {
    const apiKey = config?.apiKey ?? import.meta.env.VITE_TEMPSTICK_API_KEY;
    
    // Only create API client if we have an API key
    if (apiKey) {
      try {
        this.apiClient = new TempStickApiClient(apiKey, {
          retry: config?.retry,
          rateLimit: config?.rateLimit,
          baseUrl: 'http://localhost:3001/api/v1'
        });
      } catch (error) {
        console.error('Failed to initialize TempStick API client:', error.message);
        throw new TempStickServiceError('Failed to initialize TempStick API client', 'initialization');
      }
    } else {
      console.error('🔧 No TempStick API key found. Please configure VITE_TEMPSTICK_API_KEY environment variable.');
      throw new TempStickServiceError('TempStick API key is required', 'initialization');
    }

    // Start health checks if enabled
    if (config?.enableHealthChecks !== false) {
      this.startHealthChecks();
    }
  }

  /**
   * Start periodic health checks
   */
  private startHealthChecks(): void {
    // Check every 5 minutes
    this.healthCheckInterval = setInterval(async () => {
      try {
        await this.performHealthCheck();
      } catch (error) {
        console.error('Health check failed:', error);
      }
    }, 5 * 60 * 1000);
  }

  /**
   * Stop health checks
   */
  public stopHealthChecks(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
  }

  /**
   * Perform comprehensive health check
   */
  public async performHealthCheck(): Promise<SystemHealth> {
    const startTime = Date.now();
    
    try {
      // Test API connectivity
      const apiTest = await this.apiClient.testConnection();
      
      // Get database health
      const dbHealth = await this.checkDatabaseHealth();
      
      // Get sensor status summary
      const sensorSummary = await this.getSensorSummary();
      
      // Get alert summary
      const alertSummary = await this.getAlertSummary();
      
      return {
        tempstickApi: {
          status: apiTest.success ? 'healthy' : 'down',
          latency: apiTest.latency,
          lastCheck: new Date().toISOString()
        },
        database: dbHealth,
        sensors: sensorSummary,
        alerts: alertSummary
      };
    } catch (error) {
      console.error('Health check failed:', error);
      return {
        tempstickApi: {
          status: 'down',
          latency: Date.now() - startTime,
          lastCheck: new Date().toISOString()
        },
        database: {
          status: 'down',
          activeConnections: 0,
          lastMigration: 'unknown'
        },
        sensors: {
          total: 0,
          online: 0,
          offline: 0,
          lowBattery: 0
        },
        alerts: {
          active: 0,
          unresolved: 0,
          critical: 0
        }
      };
    }
  }

  /**
   * Check database connectivity and health
   */
  private async checkDatabaseHealth(): Promise<SystemHealth['database']> {
    try {
      const { data, error } = await supabase
        .from('sensors')
        .select('id')
        .limit(1);
      
      if (error) {
        return {
          status: 'down',
          activeConnections: 0,
          lastMigration: 'unknown'
        };
      }
      
      return {
        status: 'healthy',
        activeConnections: 1, // Supabase handles connection pooling
        lastMigration: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'down',
        activeConnections: 0,
        lastMigration: 'unknown'
      };
    }
  }

  /**
   * Get sensor status summary
   */
  private async getSensorSummary(): Promise<SystemHealth['sensors']> {
    try {
      const { data: sensors, error } = await supabase
        .from('sensors')
        .select('is_online, battery_level')
        .eq('is_active', true);
      
      if (error || !sensors) {
        return { total: 0, online: 0, offline: 0, lowBattery: 0 };
      }
      
      const total = sensors.length;
      const online = sensors.filter(s => s.is_online).length;
      const offline = total - online;
      const lowBattery = sensors.filter(s => s.battery_level && s.battery_level < 25).length;
      
      return { total, online, offline, lowBattery };
    } catch (error) {
      return { total: 0, online: 0, offline: 0, lowBattery: 0 };
    }
  }

  /**
   * Get alert status summary
   */
  private async getAlertSummary(): Promise<SystemHealth['alerts']> {
    try {
      const { data: alerts, error } = await supabase
        .from('temperature_alerts')
        .select('alert_status, severity')
        .eq('alert_status', 'active');
      
      if (error || !alerts) {
        return { active: 0, unresolved: 0, critical: 0 };
      }
      
      const active = alerts.length;
      const unresolved = alerts.filter(a => a.alert_status === 'active').length;
      const critical = alerts.filter(a => a.severity === 'critical' || a.severity === 'emergency').length;
      
      return { active, unresolved, critical };
    } catch (error) {
      return { active: 0, unresolved: 0, critical: 0 };
    }
  }

  /**
   * Enhanced sensor sync with comprehensive error handling
   */
  async syncSensors(): Promise<SyncResponse> {
    const startTime = Date.now();
    const result: SyncResponse = {
      success: false,
      syncedSensors: 0,
      newReadings: 0,
      newAlerts: 0,
      errors: [],
      lastSyncTime: new Date().toISOString()
    };

    try {
      console.log('🌡️ Starting enhanced sensor sync from TempStick API...');
      
      const tempStickSensors = await this.apiClient.getSensors();
      
      if (tempStickSensors.length === 0) {
        console.log('No sensors found in TempStick API');
        result.success = true;
        return result;
      }

      console.log(`Found ${tempStickSensors.length} sensors in TempStick API`);

      for (const tempStickSensor of tempStickSensors) {
        try {
          await this.syncSingleSensor(tempStickSensor);
          result.syncedSensors++;
        } catch (error) {
          const errorMsg = `Failed to sync sensor ${tempStickSensor.id}: ${error.message}`;
          console.error(errorMsg);
          result.errors.push(errorMsg);
        }
      }

      // Update sensor health status
      await this.updateSensorHealthStatus();

      const duration = Date.now() - startTime;
      console.log(`🎉 Sensor sync completed in ${duration}ms - ${result.syncedSensors}/${tempStickSensors.length} sensors synced`);
      
      result.success = result.errors.length === 0;
      return result;
      
    } catch (error) {
      const errorMsg = `Sensor sync failed: ${error.message}`;
      console.error('❌', errorMsg);
      result.errors.push(errorMsg);
      throw new TempStickServiceError(errorMsg, 'syncSensors', true);
    }
  }

  /**
   * Sync a single sensor with detailed error handling
   */
  private async syncSingleSensor(tempStickSensor: TempStickSensor): Promise<void> {
    try {
      // Check if sensor already exists in our database
      const { data: existingSensor, error: fetchError } = await supabase
        .from('sensors')
        .select('*')
        .eq('sensor_id', tempStickSensor.id)
        .maybeSingle();

      if (fetchError) {
        throw new TempStickServiceError(
          `Database query failed for sensor ${tempStickSensor.id}: ${fetchError.message}`,
          'syncSingleSensor'
        );
      }

      if (!existingSensor) {
        // Create new sensor record
        const newSensor = {
          sensor_id: tempStickSensor.id,
          name: tempStickSensor.name || `TempStick ${tempStickSensor.id}`,
          location: tempStickSensor.location || 'Unknown Location',
          is_online: tempStickSensor.status === 'online',
          battery_level: tempStickSensor.battery_level || null,
          is_active: true,
        };

        const { error: insertError } = await supabase
          .from('sensors')
          .insert([newSensor]);

        if (insertError) {
          throw new TempStickServiceError(
            `Failed to create sensor ${tempStickSensor.id}: ${insertError.message}`,
            'syncSingleSensor'
          );
        }

        console.log(`✅ Created sensor: ${newSensor.name}`);
      } else {
        // Update existing sensor with latest data
        const updates = {
          name: tempStickSensor.name || existingSensor.name,
          location: tempStickSensor.location || existingSensor.location,
          is_online: tempStickSensor.status === 'online',
          battery_level: tempStickSensor.battery_level || existingSensor.battery_level,
          updated_at: new Date().toISOString()
        };

        const { error: updateError } = await supabase
          .from('sensors')
          .update(updates)
          .eq('sensor_id', tempStickSensor.id);

        if (updateError) {
          throw new TempStickServiceError(
            `Failed to update sensor ${tempStickSensor.id}: ${updateError.message}`,
            'syncSingleSensor'
          );
        }

        console.log(`🔄 Updated sensor: ${updates.name}`);
      }
    } catch (error) {
      if (error instanceof TempStickServiceError) {
        throw error;
      }
      throw new TempStickServiceError(
        `Unexpected error syncing sensor ${tempStickSensor.id}: ${error.message}`,
        'syncSingleSensor'
      );
    }
  }

  /**
   * Update sensor health status based on latest data
   */
  private async updateSensorHealthStatus(): Promise<void> {
    try {
      const { data: sensors, error } = await supabase
        .from('sensors')
        .select('id, sensor_id, is_active')
        .eq('is_active', true);

      if (error || !sensors) {
        console.warn('Failed to fetch sensors for health update:', error?.message);
        return;
      }

      for (const sensor of sensors) {
        try {
          const health = await this.apiClient.getSensorHealth(sensor.sensor_id);
          
          await supabase
            .from('sensors')
            .update({
              is_online: health.online,
              battery_level: health.batteryLevel,
              updated_at: new Date().toISOString()
            })
            .eq('id', sensor.id);
            
        } catch (error) {
          console.warn(`Failed to update health for sensor ${sensor.sensor_id}:`, error.message);
        }
      }
    } catch (error) {
      console.warn('Failed to update sensor health status:', error.message);
    }
  }

  /**
   * Enhanced temperature readings sync for all active sensors
   */
  async syncAllTemperatureReadings(): Promise<SyncResponse> {
    if (this.syncInProgress) {
      throw new TempStickServiceError('Sync already in progress', 'syncAllTemperatureReadings');
    }

    this.syncInProgress = true;
    const startTime = Date.now();
    const result: SyncResponse = {
      success: false,
      syncedSensors: 0,
      newReadings: 0,
      newAlerts: 0,
      errors: [],
      lastSyncTime: new Date().toISOString()
    };

    try {
      console.log('🌡️ Starting enhanced temperature readings sync...');

      // Get all active sensors from database
      const { data: sensors, error } = await supabase
        .from('sensors')
        .select('id, sensor_id, name, is_active')
        .eq('is_active', true);

      if (error) {
        throw new TempStickServiceError(
          `Failed to fetch sensors: ${error.message}`,
          'syncAllTemperatureReadings'
        );
      }

      if (!sensors || sensors.length === 0) {
        console.log('No active sensors found');
        result.success = true;
        return result;
      }

      console.log(`Syncing temperature readings for ${sensors.length} sensors...`);

      // Process sensors in batches to avoid overwhelming the API
      const batchSize = 3;
      for (let i = 0; i < sensors.length; i += batchSize) {
        const batch = sensors.slice(i, i + batchSize);
        
        const batchPromises = batch.map(async (sensor) => {
          try {
            const sensorResult = await this.syncTemperatureReadingsForSensor(
              sensor.id, 
              sensor.sensor_id
            );
            result.syncedSensors++;
            result.newReadings += sensorResult.newReadings;
            result.newAlerts += sensorResult.newAlerts;
            return sensorResult;
          } catch (error) {
            const errorMsg = `Failed to sync readings for sensor ${sensor.name} (${sensor.sensor_id}): ${error.message}`;
            console.error(errorMsg);
            result.errors.push(errorMsg);
            return { newReadings: 0, newAlerts: 0 };
          }
        });

        await Promise.all(batchPromises);
        
        // Small delay between batches
        if (i + batchSize < sensors.length) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      this.lastFullSync = new Date();
      const duration = Date.now() - startTime;
      
      console.log(`🎉 Temperature readings sync completed in ${duration}ms`);
      console.log(`   📊 ${result.syncedSensors}/${sensors.length} sensors synced`);
      console.log(`   📈 ${result.newReadings} new readings`);
      console.log(`   🚨 ${result.newAlerts} new alerts`);
      
      result.success = result.errors.length === 0;
      return result;
      
    } catch (error) {
      const errorMsg = `Temperature readings sync failed: ${error.message}`;
      console.error('❌', errorMsg);
      result.errors.push(errorMsg);
      throw new TempStickServiceError(errorMsg, 'syncAllTemperatureReadings', true);
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * Sync temperature readings for a specific sensor
   */
  private async syncTemperatureReadingsForSensor(
    sensorId: string,
    tempstickSensorId: string
  ): Promise<{ newReadings: number; newAlerts: number }> {
    try {
      // Get latest reading timestamp to avoid duplicates
      const { data: latestReading } = await supabase
        .from('temperature_readings')
        .select('recorded_at')
        .eq('sensor_id', sensorId)
        .order('recorded_at', { ascending: false })
        .limit(1)
        .single();

      // Fetch latest readings from TempStick API
      const tempstickReadings = await this.apiClient.getLatestReadings(tempstickSensorId);
      
      if (tempstickReadings.length === 0) {
        return;
      }

      // Filter out readings we already have
      const latestTimestamp = latestReading?.recorded_at
        ? new Date(latestReading.recorded_at)
        : new Date(0);

      const newReadings = tempstickReadings.filter(reading => 
        new Date(reading.timestamp) > latestTimestamp
      );

      if (newReadings.length === 0) {
        console.log(`No new readings for sensor ${sensorId}`);
        return { newReadings: 0, newAlerts: 0 };
      }

      // Get current user ID
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new TempStickServiceError('User not authenticated', 'syncTemperatureReadingsForSensor');
      }

      // Convert TempStick readings to our database format
      const readings = newReadings.map(reading => ({
        user_id: user.id,
        sensor_id: sensorId,
        temp_celsius: reading.temperature,
        temp_fahrenheit: (reading.temperature * 9/5) + 32,
        humidity: reading.humidity,
        recorded_at: reading.timestamp,
      }));

      // Insert new readings
      const { error } = await supabase
        .from('temperature_readings')
        .insert(readings);

      if (error) {
        console.error(`Failed to insert readings for sensor ${sensorId}:`, error);
      } else {
        console.log(`✅ Inserted ${readings.length} readings for sensor ${sensorId}`);
        
        // Check for alert conditions
        const newAlerts = await this.checkAlertConditions(sensorId, readings);
        
        return { newReadings: readings.length, newAlerts };
      }
    } catch (error) {
      console.error(`Failed to sync readings for sensor ${sensorId}:`, error);
      throw new TempStickServiceError(
        `Failed to sync readings for sensor ${sensorId}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'syncTemperatureReadingsForSensor',
        true
      );
    }
  }

  /**
   * Check temperature readings against thresholds and create alerts
   */
  private async checkAlertConditions(
    sensorId: string,
    readings: Array<{
      user_id: string;
      sensor_id: string;
      temp_celsius: number;
      temp_fahrenheit: number;
      humidity: number;
      recorded_at: string;
    }>
  ): Promise<number> {
    try {
      // Get sensor thresholds and storage area requirements
      const { data: sensor, error } = await supabase
        .from('sensors')
        .select(`
          *,
          storage_areas (
            required_temp_min,
            required_temp_max,
            haccp_control_point
          )
        `)
        .eq('id', sensorId)
        .single();

      if (error || !sensor) {
        return 0;
      }

      const alerts: any[] = [];

      for (const reading of readings) {
        // Check sensor-specific thresholds
        if (sensor.temp_min_threshold && reading.temp_celsius < sensor.temp_min_threshold) {
          alerts.push({
            user_id: reading.user_id,
            sensor_id: sensorId,
            alert_type: 'low_temp',
            severity: 'critical',
            title: 'Low Temperature Alert',
            message: `Temperature ${reading.temp_celsius}°C is below minimum threshold of ${sensor.temp_min_threshold}°C`,
            threshold_value: sensor.temp_min_threshold,
            actual_value: reading.temp_celsius,
            deviation: sensor.temp_min_threshold - reading.temp_celsius,
            first_detected_at: reading.recorded_at,
            haccp_violation: true,
            product_safety_risk: 'high',
          });
        }

        if (sensor.temp_max_threshold && reading.temp_celsius > sensor.temp_max_threshold) {
          alerts.push({
            user_id: reading.user_id,
            sensor_id: sensorId,
            alert_type: 'high_temp',
            severity: 'critical',
            title: 'High Temperature Alert',
            message: `Temperature ${reading.temp_celsius}°C is above maximum threshold of ${sensor.temp_max_threshold}°C`,
            threshold_value: sensor.temp_max_threshold,
            actual_value: reading.temp_celsius,
            deviation: reading.temp_celsius - sensor.temp_max_threshold,
            first_detected_at: reading.recorded_at,
            haccp_violation: true,
            product_safety_risk: 'high',
          });
        }

        // Check storage area requirements (HACCP compliance)
        const storageArea = sensor.storage_areas;
        if (storageArea) {
          if (storageArea.required_temp_min && reading.temp_celsius < storageArea.required_temp_min) {
            alerts.push({
              user_id: reading.user_id,
              sensor_id: sensorId,
              storage_area_id: storageArea.id,
              alert_type: 'low_temp',
              severity: storageArea.haccp_control_point ? 'critical' : 'warning',
              title: 'HACCP Temperature Violation - Low',
              message: `Temperature ${reading.temp_celsius}°C is below required minimum of ${storageArea.required_temp_min}°C for ${storageArea.name}`,
              threshold_value: storageArea.required_temp_min,
              actual_value: reading.temp_celsius,
              deviation: storageArea.required_temp_min - reading.temp_celsius,
              first_detected_at: reading.recorded_at,
              haccp_violation: true,
              regulatory_notification_required: storageArea.haccp_control_point,
              product_safety_risk: 'high',
            });
          }

          if (storageArea.required_temp_max && reading.temp_celsius > storageArea.required_temp_max) {
            alerts.push({
              user_id: reading.user_id,
              sensor_id: sensorId,
              storage_area_id: storageArea.id,
              alert_type: 'high_temp',
              severity: storageArea.haccp_control_point ? 'critical' : 'warning',
              title: 'HACCP Temperature Violation - High',
              message: `Temperature ${reading.temp_celsius}°C is above required maximum of ${storageArea.required_temp_max}°C for ${storageArea.name}`,
              threshold_value: storageArea.required_temp_max,
              actual_value: reading.temp_celsius,
              deviation: reading.temp_celsius - storageArea.required_temp_max,
              first_detected_at: reading.recorded_at,
              haccp_violation: true,
              regulatory_notification_required: storageArea.haccp_control_point,
              product_safety_risk: 'high',
            });
          }
        }
      }

      // Insert alerts if any were generated
      if (alerts.length > 0) {
        const { error: alertError } = await supabase
          .from('temperature_alerts')
          .insert(alerts);

        if (alertError) {
          console.error('Failed to create temperature alerts:', alertError);
        } else {
          console.log(`🚨 Created ${alerts.length} temperature alerts for sensor ${sensorId}`);
          return alerts.length;
        }
      }
      
      return 0;
    } catch (error) {
      console.error('Failed to check alert conditions:', error);
      return 0;
    }
  }

  /**
   * Get current temperature readings for dashboard
   */
  async getCurrentReadings(): Promise<TemperatureReading[]> {
    const { data, error } = await supabase
      .from('temperature_readings')
      .select(`
        *,
        sensors (
          name,
          storage_areas (
            name,
            area_type
          )
        )
      `)
      .order('recorded_at', { ascending: false })
      .limit(50); // Get latest 50 readings across all sensors

    if (error) {
      console.error('Failed to fetch current readings:', error);
      return [];
    }

    return data || [];
  }

  /**
   * Get temperature readings for a specific time range
   */
  async getReadingsForDateRange(
    startDate: Date,
    endDate: Date,
    sensorIds?: string[]
  ): Promise<TemperatureReading[]> {
    let query = supabase
      .from('temperature_readings')
      .select(`
        *,
        sensors (
          name,
          location_description,
          tempstick_sensor_id
        )
      `)
      .gte('recorded_at', startDate.toISOString())
      .lte('recorded_at', endDate.toISOString())
      .order('recorded_at', { ascending: true });

    if (sensorIds && sensorIds.length > 0) {
      query = query.in('sensor_id', sensorIds);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Failed to fetch readings for date range:', error);
      return [];
    }

    return data || [];
  }

  /**
   * Get comprehensive historical data for the historical dashboard
   * Optimized for large date ranges with statistics calculation
   */
  async getHistoricalData(request: HistoricalDataRequest): Promise<HistoricalDataResponse> {
    try {
      console.log(`📊 Fetching historical data for ${request.sensorIds.length} sensors from ${request.startDate.toISOString()} to ${request.endDate.toISOString()}`);
      
      // Get historical readings using existing method
      const readings = await this.getReadingsForDateRange(
        request.startDate,
        request.endDate,
        request.sensorIds
      );

      // Transform to HistoricalDataPoint format
      const dataPoints: HistoricalDataPoint[] = readings.map((reading: any) => ({
        timestamp: new Date(reading.recorded_at || reading.reading_timestamp),
        sensorId: reading.sensor_id,
        sensorName: reading.sensors?.name || `Sensor ${reading.sensor_id}`,
        temperature: reading.temperature,
        humidity: reading.humidity,
        batteryLevel: reading.battery_level,
        signalStrength: reading.signal_strength,
        dewpoint: this.calculateDewpoint(reading.temperature, reading.humidity),
        heatIndex: this.calculateHeatIndex(reading.temperature, reading.humidity)
      }));

      // Calculate statistics if requested
      const statistics: SensorStatistics[] = request.includeStatistics 
        ? this.calculateSensorStatistics(dataPoints, request.dateRange || { start: request.startDate, end: request.endDate })
        : [];

      console.log(`✅ Retrieved ${dataPoints.length} historical data points with ${statistics.length} sensor statistics`);

      return {
        data: dataPoints,
        statistics,
        totalPoints: dataPoints.length,
        hasMore: false, // TODO: Implement pagination for large datasets
        dateRange: { start: request.startDate, end: request.endDate }
      };

    } catch (error) {
      console.error('Failed to fetch historical data:', error);
      throw new TempStickServiceError(
        `Failed to fetch historical data: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'getHistoricalData',
        true
      );
    }
  }

  /**
   * Calculate comprehensive statistics for sensors over a time period
   */
  private calculateSensorStatistics(dataPoints: HistoricalDataPoint[], period: DateRange): SensorStatistics[] {
    const sensorGroups = new Map<string, HistoricalDataPoint[]>();
    
    // Group data points by sensor
    dataPoints.forEach(point => {
      if (!sensorGroups.has(point.sensorId)) {
        sensorGroups.set(point.sensorId, []);
      }
      sensorGroups.get(point.sensorId)!.push(point);
    });

    // Calculate statistics for each sensor
    return Array.from(sensorGroups.entries()).map(([sensorId, points]) => {
      const temperatures = points.map(p => p.temperature).filter(t => t !== null && t !== undefined);
      const humidities = points.map(p => p.humidity).filter(h => h !== null && h !== undefined);
      
      const tempStats = temperatures.length > 0 ? {
        min: Math.min(...temperatures),
        max: Math.max(...temperatures),
        average: temperatures.reduce((sum, t) => sum + t, 0) / temperatures.length,
        minTimestamp: points.find(p => p.temperature === Math.min(...temperatures))?.timestamp || period.start,
        maxTimestamp: points.find(p => p.temperature === Math.max(...temperatures))?.timestamp || period.end,
        dataPoints: temperatures.length
      } : {
        min: 0, max: 0, average: 0,
        minTimestamp: period.start,
        maxTimestamp: period.end,
        dataPoints: 0
      };

      const humidityStats = humidities.length > 0 ? {
        min: Math.min(...humidities),
        max: Math.max(...humidities),
        average: humidities.reduce((sum, h) => sum + h, 0) / humidities.length,
        dataPoints: humidities.length
      } : undefined;

      return {
        sensorId,
        sensorName: points[0]?.sensorName || `Sensor ${sensorId}`,
        period,
        temperature: tempStats,
        humidity: humidityStats
      };
    });
  }

  /**
   * Calculate dewpoint from temperature and humidity
   */
  private calculateDewpoint(temperature: number, humidity?: number): number | undefined {
    if (!humidity || humidity === 0) return undefined;
    
    // Magnus formula approximation
    const a = 17.27;
    const b = 237.7;
    const alpha = ((a * temperature) / (b + temperature)) + Math.log(humidity / 100.0);
    const dewpoint = (b * alpha) / (a - alpha);
    
    return Math.round(dewpoint * 10) / 10; // Round to 1 decimal place
  }

  /**
   * Calculate heat index from temperature and humidity (for temperatures > 80°F)
   */
  private calculateHeatIndex(temperature: number, humidity?: number): number | undefined {
    if (!humidity || temperature < 80) return undefined;
    
    // Rothfusz regression (simplified version)
    const T = temperature;
    const R = humidity;
    
    const c1 = -42.379;
    const c2 = 2.04901523;
    const c3 = 10.14333127;
    const c4 = -0.22475541;
    const c5 = -6.83783e-3;
    const c6 = -5.481717e-2;
    const c7 = 1.22874e-3;
    const c8 = 8.5282e-4;
    const c9 = -1.99e-6;
    
    const heatIndex = c1 + (c2 * T) + (c3 * R) + (c4 * T * R) + 
                      (c5 * T * T) + (c6 * R * R) + (c7 * T * T * R) + 
                      (c8 * T * R * R) + (c9 * T * T * R * R);
    
    return Math.round(heatIndex * 10) / 10; // Round to 1 decimal place
  }

  /**
   * Get aggregated data for large date ranges to improve chart performance
   */
  async getAggregatedHistoricalData(
    request: HistoricalDataRequest, 
    interval: AggregationInterval = 'hour'
  ): Promise<AggregatedDataPoint[]> {
    try {
      // Get raw data first
      const response = await this.getHistoricalData(request);
      const dataPoints = response.data;
      
      // Group by time intervals and sensor
      const aggregatedMap = new Map<string, AggregatedDataPoint>();
      
      dataPoints.forEach(point => {
        const roundedTime = this.roundToInterval(point.timestamp, interval);
        const key = `${point.sensorId}-${roundedTime.getTime()}`;
        
        if (!aggregatedMap.has(key)) {
          aggregatedMap.set(key, {
            timestamp: roundedTime,
            sensorId: point.sensorId,
            aggregation: interval,
            temperature: {
              min: point.temperature,
              max: point.temperature,
              average: point.temperature,
              count: 1
            },
            humidity: point.humidity ? {
              min: point.humidity,
              max: point.humidity,
              average: point.humidity,
              count: 1
            } : undefined
          });
        } else {
          const existing = aggregatedMap.get(key)!;
          existing.temperature.min = Math.min(existing.temperature.min, point.temperature);
          existing.temperature.max = Math.max(existing.temperature.max, point.temperature);
          existing.temperature.average = ((existing.temperature.average * existing.temperature.count) + point.temperature) / (existing.temperature.count + 1);
          existing.temperature.count += 1;
          
          if (point.humidity && existing.humidity) {
            existing.humidity.min = Math.min(existing.humidity.min, point.humidity);
            existing.humidity.max = Math.max(existing.humidity.max, point.humidity);
            existing.humidity.average = ((existing.humidity.average * existing.humidity.count) + point.humidity) / (existing.humidity.count + 1);
            existing.humidity.count += 1;
          }
        }
      });
      
      return Array.from(aggregatedMap.values()).sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
      
    } catch (error) {
      console.error('Failed to get aggregated historical data:', error);
      return [];
    }
  }

  /**
   * Round timestamp to specified interval for aggregation
   */
  private roundToInterval(date: Date, interval: AggregationInterval): Date {
    const rounded = new Date(date);
    
    switch (interval) {
      case 'minute':
        rounded.setSeconds(0, 0);
        break;
      case 'hour':
        rounded.setMinutes(0, 0, 0);
        break;
      case 'day':
        rounded.setHours(0, 0, 0, 0);
        break;
      case 'week':
        const dayOfWeek = rounded.getDay();
        rounded.setDate(rounded.getDate() - dayOfWeek);
        rounded.setHours(0, 0, 0, 0);
        break;
    }
    
    return rounded;
  }

  /**
   * Get active temperature alerts
   */
  async getActiveAlerts(): Promise<TemperatureAlert[]> {
    const { data, error } = await supabase
      .from('temperature_alerts')
      .select(`
        *,
        sensors (
          name,
          storage_areas (
            name,
            area_type
          )
        )
      `)
      .is('resolved_at', null)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Failed to fetch active alerts:', error);
      return [];
    }

    return data || [];
  }

  /**
   * Resolve a temperature alert
   */
  async resolveAlert(alertId: string): Promise<void> {
    const { error } = await supabase
      .from('temperature_alerts')
      .update({
        resolved_at: new Date().toISOString(),
        alert_status: 'resolved'
      })
      .eq('id', alertId);

    if (error) {
      console.error('Failed to resolve alert:', error);
      throw error;
    }
  }

  /**
   * Enhanced scheduled sync with comprehensive monitoring
   */
  async scheduledSync(): Promise<SyncResponse> {
    const startTime = Date.now();
    const result: SyncResponse = {
      success: false,
      syncedSensors: 0,
      newReadings: 0,
      newAlerts: 0,
      errors: [],
      lastSyncTime: new Date().toISOString()
    };

    try {
      console.log('🔄 Starting enhanced scheduled TempStick sync...');
      
      // First sync sensors (in case new ones were added)
      const sensorResult = await this.syncSensors();
      result.syncedSensors = sensorResult.syncedSensors;
      result.errors.push(...sensorResult.errors);
      
      // Then sync temperature readings
      const readingsResult = await this.syncAllTemperatureReadings();
      result.newReadings = readingsResult.newReadings;
      result.newAlerts = readingsResult.newAlerts;
      result.errors.push(...readingsResult.errors);
      
      const duration = Date.now() - startTime;
      console.log(`✅ Scheduled sync completed in ${duration}ms`);
      console.log(`   📊 ${result.syncedSensors} sensors synced`);
      console.log(`   📈 ${result.newReadings} new readings`);
      console.log(`   🚨 ${result.newAlerts} new alerts`);
      
      result.success = result.errors.length === 0;
      return result;
      
    } catch (error) {
      const errorMsg = `Scheduled sync failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
      console.error('❌', errorMsg);
      result.errors.push(errorMsg);
      throw new TempStickServiceError(errorMsg, 'scheduledSync', true);
    }
  }

  /**
   * Get service metrics and health information
   */
  public getServiceMetrics(): {
    api: ServiceMetrics;
    sync: {
      inProgress: boolean;
      lastFullSync: Date | null;
    };
  } {
    return {
      api: this.apiClient?.getMetrics() || {},
      sync: {
        inProgress: this.syncInProgress,
        lastFullSync: this.lastFullSync
      }
    };
  }

  /**
   * Reset service metrics
   */
  public resetMetrics(): void {
    if (this.apiClient) {
      this.apiClient.resetMetrics();
    }
  }


  /**
   * Get all sensors from TempStick API with rate limiting
   */
  public async getAllSensors(): Promise<any[]> {
    if (!this.apiClient) {
      throw new TempStickServiceError('TempStick API client not initialized', 'getAllSensors');
    }

    // Simple rate limiting - wait if last call was too recent
    const now = Date.now();
    const timeSinceLastCall = now - this.lastApiCall;
    if (timeSinceLastCall < this.minCallInterval) {
      const waitTime = this.minCallInterval - timeSinceLastCall;
      console.log(`⏱️  Rate limiting: waiting ${waitTime}ms before API call`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
    this.lastApiCall = Date.now();

    try {
      // Get sensors from TempStick API
      const tempStickSensors = await this.apiClient.getSensors();
      if (tempStickSensors.length > 0) {
        console.log('✅ Successfully retrieved', tempStickSensors.length, 'sensors from TempStick API');
        return tempStickSensors.map(sensor => ({
          sensor_id: sensor.sensor_id,
          sensor_name: sensor.sensor_name,
          location: sensor.sensor_name, // Use sensor name as location fallback
          offline: sensor.offline,
          last_temp: sensor.last_temp,
          last_humidity: sensor.last_humidity,
          last_checkin: sensor.last_checkin,
          battery_pct: sensor.battery_pct,
          rssi: sensor.rssi
        }));
      } else {
        console.log('⚠️ No sensors found in TempStick API response');
        return [];
      }
    } catch (error) {
      throw new TempStickServiceError(`Failed to get sensors: ${error.message}`, 'getAllSensors', true);
    }
  }



  /**
   * Get latest readings for a sensor from TempStick API
   */
  public async getLatestReadings(sensorId: string, limit: number = 100): Promise<TempStickReading[]> {
    if (!this.apiClient) {
      throw new TempStickServiceError('TempStick API client not initialized', 'getLatestReadings');
    }

    try {
      // Get readings from TempStick API
      const readings = await this.apiClient.getLatestReadings(sensorId, limit);
      if (readings && readings.length > 0) {
        console.log(`✅ Retrieved ${readings.length} readings for sensor ${sensorId}`);
        return readings;
      } else {
        console.log(`⚠️ No readings found for sensor ${sensorId}`);
        return [];
      }
    } catch (error) {
      throw new TempStickServiceError(`Failed to get readings for sensor ${sensorId}: ${error.message}`, 'getLatestReadings', true);
    }
  }






  /**
   * Graceful shutdown
   */
  public shutdown(): void {
    this.stopHealthChecks();
    console.log('TempStick service shutdown completed');
  }
}

// Export singleton instance with enhanced configuration
export const tempStickService = new TempStickService({
  enableHealthChecks: true,
  retry: {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 30000,
    backoffMultiplier: 2
  },
  rateLimit: {
    requestsPerMinute: 60,
    burstLimit: 10
  }
});

// Export classes and types for testing and advanced usage
export { 
  TempStickApiClient,
  type RetryConfig,
  type RateLimitConfig,
  type ServiceMetrics
};