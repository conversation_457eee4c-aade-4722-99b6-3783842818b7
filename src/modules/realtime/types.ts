/**
 * Real-time subscription management types
 */

export interface SubscriptionConfig {
  channel: string;
  event: string;
  schema?: string;
  table?: string;
  filter?: string;
  callback: SubscriptionCallback;
  errorHandler?: (error: Error) => void;
  reconnectOnError?: boolean;
  maxReconnectAttempts?: number;
  reconnectDelay?: number;
}

export interface SubscriptionCallback {
  (payload: SubscriptionPayload): void | Promise<void>;
}

export interface SubscriptionPayload {
  eventType: 'INSERT' | 'UPDATE' | 'DELETE' | '*';
  new?: Record<string, unknown>;
  old?: Record<string, unknown>;
  schema: string;
  table: string;
  commit_timestamp: string;
  errors?: string[];
}

export interface SubscriptionStatus {
  id: string;
  channel: string;
  status: 'connecting' | 'connected' | 'disconnected' | 'error' | 'reconnecting';
  lastConnected?: Date;
  lastError?: Error;
  reconnectAttempts: number;
  config: SubscriptionConfig;
}

export interface RealtimeManagerConfig {
  maxSubscriptions?: number;
  defaultReconnectAttempts?: number;
  defaultReconnectDelay?: number;
  enableLogging?: boolean;
  enableMetrics?: boolean;
}

export interface SubscriptionMetrics {
  totalSubscriptions: number;
  activeSubscriptions: number;
  failedSubscriptions: number;
  totalMessages: number;
  messagesPerSecond: number;
  averageLatency: number;
  reconnectAttempts: number;
}

export interface ConnectionHealth {
  isHealthy: boolean;
  activeConnections: number;
  failedConnections: number;
  lastHealthCheck: Date;
  issues: string[];
}

export type SubscriptionEventType = 
  | 'subscription_created'
  | 'subscription_connected'
  | 'subscription_disconnected'
  | 'subscription_error'
  | 'subscription_reconnecting'
  | 'subscription_destroyed'
  | 'message_received'
  | 'connection_health_changed';

export interface SubscriptionEvent {
  type: SubscriptionEventType;
  subscriptionId: string;
  timestamp: Date;
  data?: unknown;
  error?: Error;
}
