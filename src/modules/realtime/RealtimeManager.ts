import { RealtimeChannel, SupabaseClient } from '@supabase/supabase-js';
import {
  SubscriptionConfig,
  SubscriptionStatus,
  SubscriptionPayload,
  RealtimeManagerConfig,
  SubscriptionMetrics,
  ConnectionHealth,
  SubscriptionEvent,
  SubscriptionEventType
} from './types';
import { errorManager } from '../../lib/error-handling';

/**
 * Centralized real-time subscription management
 */
export class RealtimeManager {
  private supabase: SupabaseClient;
  private subscriptions = new Map<string, SubscriptionStatus>();
  private channels = new Map<string, RealtimeChannel>();
  private config: Required<RealtimeManagerConfig>;
  private metrics: SubscriptionMetrics;
  private eventListeners = new Map<SubscriptionEventType, Set<(event: SubscriptionEvent) => void>>();
  private healthCheckInterval?: NodeJS.Timeout;
  private metricsInterval?: NodeJS.Timeout;

  constructor(supabase: SupabaseClient, config: RealtimeManagerConfig = {}) {
    this.supabase = supabase;
    this.config = {
      maxSubscriptions: config.maxSubscriptions ?? 50,
      defaultReconnectAttempts: config.defaultReconnectAttempts ?? 5,
      defaultReconnectDelay: config.defaultReconnectDelay ?? 1000,
      enableLogging: config.enableLogging ?? true,
      enableMetrics: config.enableMetrics ?? true
    };

    this.metrics = {
      totalSubscriptions: 0,
      activeSubscriptions: 0,
      failedSubscriptions: 0,
      totalMessages: 0,
      messagesPerSecond: 0,
      averageLatency: 0,
      reconnectAttempts: 0
    };

    this.startHealthChecking();
    this.startMetricsCollection();
  }

  /**
   * Create a new subscription
   */
  async subscribe(config: SubscriptionConfig): Promise<string> {
    if (this.subscriptions.size >= this.config.maxSubscriptions) {
      throw new Error(`Maximum subscriptions limit reached (${this.config.maxSubscriptions})`);
    }

    const subscriptionId = this.generateSubscriptionId(config);
    
    if (this.subscriptions.has(subscriptionId)) {
      throw new Error(`Subscription with ID ${subscriptionId} already exists`);
    }

    const status: SubscriptionStatus = {
      id: subscriptionId,
      channel: config.channel,
      status: 'connecting',
      reconnectAttempts: 0,
      config: {
        ...config,
        maxReconnectAttempts: config.maxReconnectAttempts ?? this.config.defaultReconnectAttempts,
        reconnectDelay: config.reconnectDelay ?? this.config.defaultReconnectDelay,
        reconnectOnError: config.reconnectOnError ?? true
      }
    };

    this.subscriptions.set(subscriptionId, status);
    this.metrics.totalSubscriptions++;

    try {
      await this.createSubscription(subscriptionId, status);
      this.emitEvent('subscription_created', subscriptionId);
      
      if (this.config.enableLogging) {
        console.log(`Subscription created: ${subscriptionId}`);
      }

      return subscriptionId;
    } catch (error) {
      this.subscriptions.delete(subscriptionId);
      this.metrics.totalSubscriptions--;
      throw error;
    }
  }

  /**
   * Unsubscribe from a subscription
   */
  async unsubscribe(subscriptionId: string): Promise<void> {
    const subscription = this.subscriptions.get(subscriptionId);
    if (!subscription) {
      throw new Error(`Subscription ${subscriptionId} not found`);
    }

    const channel = this.channels.get(subscriptionId);
    if (channel) {
      await this.supabase.removeChannel(channel);
      this.channels.delete(subscriptionId);
    }

    this.subscriptions.delete(subscriptionId);
    this.updateMetrics();

    this.emitEvent('subscription_destroyed', subscriptionId);
    
    if (this.config.enableLogging) {
      console.log(`Subscription destroyed: ${subscriptionId}`);
    }
  }

  /**
   * Get subscription status
   */
  getSubscriptionStatus(subscriptionId: string): SubscriptionStatus | undefined {
    return this.subscriptions.get(subscriptionId);
  }

  /**
   * Get all subscriptions
   */
  getAllSubscriptions(): SubscriptionStatus[] {
    return Array.from(this.subscriptions.values());
  }

  /**
   * Get subscription metrics
   */
  getMetrics(): SubscriptionMetrics {
    return { ...this.metrics };
  }

  /**
   * Get connection health
   */
  getConnectionHealth(): ConnectionHealth {
    const subscriptions = Array.from(this.subscriptions.values());
    const activeConnections = subscriptions.filter(s => s.status === 'connected').length;
    const failedConnections = subscriptions.filter(s => s.status === 'error').length;
    const issues: string[] = [];

    if (failedConnections > 0) {
      issues.push(`${failedConnections} failed connections`);
    }

    if (activeConnections === 0 && subscriptions.length > 0) {
      issues.push('No active connections');
    }

    return {
      isHealthy: issues.length === 0,
      activeConnections,
      failedConnections,
      lastHealthCheck: new Date(),
      issues
    };
  }

  /**
   * Reconnect all failed subscriptions
   */
  async reconnectAll(): Promise<void> {
    const failedSubscriptions = Array.from(this.subscriptions.values())
      .filter(s => s.status === 'error' || s.status === 'disconnected');

    const reconnectPromises = failedSubscriptions.map(subscription =>
      this.reconnectSubscription(subscription.id)
    );

    await Promise.allSettled(reconnectPromises);
  }

  /**
   * Add event listener
   */
  addEventListener(
    eventType: SubscriptionEventType,
    listener: (event: SubscriptionEvent) => void
  ): void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, new Set());
    }
    this.eventListeners.get(eventType)!.add(listener);
  }

  /**
   * Remove event listener
   */
  removeEventListener(
    eventType: SubscriptionEventType,
    listener: (event: SubscriptionEvent) => void
  ): void {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      listeners.delete(listener);
    }
  }

  /**
   * Cleanup all subscriptions and intervals
   */
  async cleanup(): Promise<void> {
    // Clear intervals
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
    if (this.metricsInterval) {
      clearInterval(this.metricsInterval);
    }

    // Unsubscribe from all subscriptions
    const unsubscribePromises = Array.from(this.subscriptions.keys())
      .map(id => this.unsubscribe(id));

    await Promise.allSettled(unsubscribePromises);

    // Clear event listeners
    this.eventListeners.clear();

    if (this.config.enableLogging) {
      console.log('RealtimeManager cleanup completed');
    }
  }

  /**
   * Create actual subscription
   */
  private async createSubscription(
    subscriptionId: string,
    status: SubscriptionStatus
  ): Promise<void> {
    const { config } = status;
    
    const channel = this.supabase.channel(config.channel);
    
    // Configure the subscription based on config
    const subscription = channel.on(
      'postgres_changes',
      {
        event: config.event as any,
        schema: config.schema || 'public',
        table: config.table,
        filter: config.filter
      },
      async (payload: any) => {
        try {
          this.metrics.totalMessages++;
          
          const subscriptionPayload: SubscriptionPayload = {
            eventType: payload.eventType,
            new: payload.new,
            old: payload.old,
            schema: payload.schema,
            table: payload.table,
            commit_timestamp: payload.commit_timestamp,
            errors: payload.errors
          };

          await config.callback(subscriptionPayload);
          this.emitEvent('message_received', subscriptionId, subscriptionPayload);
        } catch (error) {
          await this.handleSubscriptionError(subscriptionId, error as Error);
        }
      }
    );

    // Handle subscription events
    subscription.subscribe((status, err) => {
      this.handleSubscriptionStatusChange(subscriptionId, status, err);
    });

    this.channels.set(subscriptionId, channel);
    this.updateSubscriptionStatus(subscriptionId, 'connected');
  }

  /**
   * Handle subscription status changes
   */
  private handleSubscriptionStatusChange(
    subscriptionId: string,
    status: string,
    error?: Error
  ): void {
    const subscription = this.subscriptions.get(subscriptionId);
    if (!subscription) return;

    switch (status) {
      case 'SUBSCRIBED':
        this.updateSubscriptionStatus(subscriptionId, 'connected');
        subscription.lastConnected = new Date();
        subscription.reconnectAttempts = 0;
        this.emitEvent('subscription_connected', subscriptionId);
        break;

      case 'CHANNEL_ERROR':
      case 'TIMED_OUT':
      case 'CLOSED':
        this.updateSubscriptionStatus(subscriptionId, 'error', error);
        this.emitEvent('subscription_error', subscriptionId, undefined, error);
        
        if (subscription.config.reconnectOnError) {
          this.scheduleReconnect(subscriptionId);
        }
        break;
    }

    this.updateMetrics();
  }

  /**
   * Handle subscription errors
   */
  private async handleSubscriptionError(subscriptionId: string, error: Error): Promise<void> {
    const subscription = this.subscriptions.get(subscriptionId);
    if (!subscription) return;

    this.updateSubscriptionStatus(subscriptionId, 'error', error);

    // Use error manager for consistent error handling
    await errorManager.handleError(error, {
      component: 'RealtimeManager',
      action: 'subscription_callback',
      metadata: { subscriptionId }
    });

    if (subscription.config.errorHandler) {
      try {
        subscription.config.errorHandler(error);
      } catch (handlerError) {
        console.error('Subscription error handler failed:', handlerError);
      }
    }

    this.emitEvent('subscription_error', subscriptionId, undefined, error);
  }

  /**
   * Schedule reconnection attempt
   */
  private scheduleReconnect(subscriptionId: string): void {
    const subscription = this.subscriptions.get(subscriptionId);
    if (!subscription) return;

    if (subscription.reconnectAttempts >= subscription.config.maxReconnectAttempts!) {
      if (this.config.enableLogging) {
        console.log(`Max reconnect attempts reached for subscription: ${subscriptionId}`);
      }
      return;
    }

    const delay = subscription.config.reconnectDelay! * Math.pow(2, subscription.reconnectAttempts);
    
    setTimeout(async () => {
      await this.reconnectSubscription(subscriptionId);
    }, delay);

    this.updateSubscriptionStatus(subscriptionId, 'reconnecting');
    this.emitEvent('subscription_reconnecting', subscriptionId);
  }

  /**
   * Reconnect a subscription
   */
  private async reconnectSubscription(subscriptionId: string): Promise<void> {
    const subscription = this.subscriptions.get(subscriptionId);
    if (!subscription) return;

    subscription.reconnectAttempts++;
    this.metrics.reconnectAttempts++;

    try {
      // Remove existing channel
      const existingChannel = this.channels.get(subscriptionId);
      if (existingChannel) {
        await this.supabase.removeChannel(existingChannel);
        this.channels.delete(subscriptionId);
      }

      // Create new subscription
      await this.createSubscription(subscriptionId, subscription);
      
      if (this.config.enableLogging) {
        console.log(`Subscription reconnected: ${subscriptionId}`);
      }
    } catch (error) {
      await this.handleSubscriptionError(subscriptionId, error as Error);
    }
  }

  /**
   * Update subscription status
   */
  private updateSubscriptionStatus(
    subscriptionId: string,
    status: SubscriptionStatus['status'],
    error?: Error
  ): void {
    const subscription = this.subscriptions.get(subscriptionId);
    if (!subscription) return;

    subscription.status = status;
    if (error) {
      subscription.lastError = error;
    }
  }

  /**
   * Generate unique subscription ID
   */
  private generateSubscriptionId(config: SubscriptionConfig): string {
    const parts = [
      config.channel,
      config.event,
      config.table || 'all',
      config.filter || 'none'
    ];
    return `${parts.join(':')  }:${  Date.now()}`;
  }

  /**
   * Emit subscription event
   */
  private emitEvent(
    type: SubscriptionEventType,
    subscriptionId: string,
    data?: unknown,
    error?: Error
  ): void {
    const event: SubscriptionEvent = {
      type,
      subscriptionId,
      timestamp: new Date(),
      data,
      error
    };

    const listeners = this.eventListeners.get(type);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(event);
        } catch (error) {
          console.error('Event listener error:', error);
        }
      });
    }
  }

  /**
   * Update metrics
   */
  private updateMetrics(): void {
    const subscriptions = Array.from(this.subscriptions.values());
    this.metrics.activeSubscriptions = subscriptions.filter(s => s.status === 'connected').length;
    this.metrics.failedSubscriptions = subscriptions.filter(s => s.status === 'error').length;
  }

  /**
   * Start health checking
   */
  private startHealthChecking(): void {
    if (!this.config.enableMetrics) return;

    this.healthCheckInterval = setInterval(() => {
      const health = this.getConnectionHealth();
      if (!health.isHealthy) {
        this.emitEvent('connection_health_changed', 'system', health);
      }
    }, 30000); // Check every 30 seconds
  }

  /**
   * Start metrics collection
   */
  private startMetricsCollection(): void {
    if (!this.config.enableMetrics) return;

    let lastMessageCount = 0;
    
    this.metricsInterval = setInterval(() => {
      const currentMessages = this.metrics.totalMessages;
      this.metrics.messagesPerSecond = (currentMessages - lastMessageCount) / 10; // 10 second interval
      lastMessageCount = currentMessages;
      
      this.updateMetrics();
    }, 10000); // Update every 10 seconds
  }
}
