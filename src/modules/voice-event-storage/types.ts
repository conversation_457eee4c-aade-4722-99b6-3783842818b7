// VOICE EVENT STORAGE TYPES
// This file contains all types and interfaces for voice event storage

export interface VoiceEvent {
  id?: string;
  event_type: 'receiving' | 'disposal' | 'physical_count' | 'sale';
  product_name: string;
  quantity: number;
  unit: string;
  vendor_name?: string;
  customer_name?: string;
  condition?: string;
  temperature?: number;
  temperature_unit?: 'fahrenheit' | 'celsius';
  notes?: string;
  event_date?: string;
  occurred_at?: string;
  
  // Voice-specific fields
  voice_confidence_score: number;
  voice_confidence_breakdown: {
    product_match: number;
    quantity_extraction: number;
    vendor_match: number;
    overall: number;
  };
  raw_transcript: string;
  audio_recording_url?: string;
  created_by_voice: boolean;
  
  // Audit fields
  created_at?: string;
  updated_at?: string;
  created_by?: string;
  last_modified_by?: string;
  
  // Additional metadata
  session_id?: string;
}

export interface VoiceEventFilters {
  dateRange?: {
    start: string;
    end: string;
  };
  eventType?: string[];
  confidenceThreshold?: number;
  searchQuery?: string;
  createdBy?: string;
}

export interface VoiceEventResult {
  eventData: VoiceEvent;
  confidence: number;
  requiresConfirmation: boolean;
  audioConfirmation?: string;
}

export interface VoiceEventData {
  event_type: 'receiving' | 'disposal' | 'physical_count' | 'sale';
  product_name: string;
  quantity: number;
  unit: string;
  vendor_name?: string;
  customer_name?: string;
  condition?: string;
  temperature?: number;
  temperature_unit?: string;
  notes?: string;
  occurred_at?: string;
  
  // Voice-specific fields
  voice_confidence_score: number;
  voice_confidence_breakdown: {
    product_match: number;
    quantity_extraction: number;
    vendor_match: number;
    overall: number;
  };
  raw_transcript: string;
  audio_recording_url?: string;
}

export interface EventAudit {
  id?: string;
  event_id: string;
  field_name: string;
  old_value?: any;
  new_value?: any;
  changed_by?: string;
  changed_at?: string;
  change_reason?: string;
  change_source?: 'manual' | 'voice' | 'system';
}

export interface VoiceEventStatistics {
  totalEvents: number;
  highConfidenceEvents: number;
  mediumConfidenceEvents: number;
  lowConfidenceEvents: number;
  averageConfidence: number;
  eventsNeedingReview: number;
}

export interface VoiceEventStorageConfig {
  confidenceThreshold?: number;
  enableAuditTrail?: boolean;
  enableAudioStorage?: boolean;
  maxAudioFileSize?: number;
}
