import { supabase } from '../../lib/supabase';
import { VoiceEvent, VoiceEventData, VoiceEventFilters } from '../../types/schema';
import { EventAudit, VoiceEventStatistics } from './types';

/**
 * Service class for managing voice events in the database
 * Handles CRUD operations, filtering, and audit trail management
 */
export class VoiceEventService {

  /**
   * Create a new voice event in the database
   * @param eventData - The voice event data to save
   * @returns Promise<VoiceEvent> - The created event with ID
   */
  async createVoiceEvent(eventData: VoiceEventData): Promise<VoiceEvent> {
    try {
      // Validate required voice fields
      if (!eventData.voice_confidence_score || !eventData.raw_transcript) {
        throw new Error('Voice confidence score and raw transcript are required for voice events');
      }

      // Prepare the event data for insertion
      const insertData = {
        event_type: eventData.event_type,
        product_id: null, // Will be resolved by product lookup
        quantity: eventData.quantity,
        unit: eventData.unit,
        notes: eventData.notes,
        occurred_at: eventData.occurred_at || new Date().toISOString(),
        
        // Voice-specific fields
        voice_confidence_score: eventData.voice_confidence_score,
        voice_confidence_breakdown: eventData.voice_confidence_breakdown,
        raw_transcript: eventData.raw_transcript,
        audio_recording_url: eventData.audio_recording_url,
        created_by_voice: true,
        
        // Metadata with voice-specific information
        metadata: {
          product_name: eventData.product_name,
          vendor_name: eventData.vendor_name,
          customer_name: eventData.customer_name,
          condition: eventData.condition,
          temperature: eventData.temperature,
          temperature_unit: eventData.temperature_unit,
          processing_method: eventData.processing_method,
          quality_grade: eventData.quality_grade,
          market_form: eventData.market_form,
          session_id: eventData.session_id,
          created_by_voice: true,
          voice_processing_timestamp: new Date().toISOString()
        }
      };

      const { data, error } = await supabase
        .from('inventory_events')
        .insert(insertData)
        .select()
        .single();

      if (error) {
        console.error('Error creating voice event:', error);
        throw new Error(`Failed to create voice event: ${error.message}`);
      }

      return this.mapDatabaseEventToVoiceEvent(data);
    } catch (error) {
      console.error('VoiceEventService.createVoiceEvent error:', error);
      throw error;
    }
  }

  /**
   * Retrieve voice events with optional filtering
   * @param filters - Optional filters for the query
   * @returns Promise<VoiceEvent[]> - Array of voice events
   */
  async getVoiceEvents(filters?: VoiceEventFilters): Promise<VoiceEvent[]> {
    try {
      let query = supabase
        .from('inventory_events')
        .select(`
          id,
          event_type,
          name,
          quantity,
          unit,
          total_amount,
          unit_price,
          notes,
          voice_confidence_score,
          voice_confidence_breakdown,
          raw_transcript,
          audio_recording_url,
          created_by_voice,
          vendor_name,
          customer_name,
          condition_on_receipt,
          temperature_at_receipt,
          occurred_at,
          created_at,
          updated_at,
          metadata
        `)
        .eq('created_by_voice', true)
        .order('created_at', { ascending: false });

      // Apply filters
      if (filters) {
        // Date range filter
        if (filters.dateRange) {
          query = query
            .gte('created_at', filters.dateRange.start)
            .lte('created_at', filters.dateRange.end);
        }

        // Event type filter
        if (filters.eventType && filters.eventType.length > 0) {
          query = query.in('event_type', filters.eventType);
        }

        // Confidence threshold filter
        if (filters.confidenceThreshold !== undefined) {
          query = query.gte('voice_confidence_score', filters.confidenceThreshold);
        }

        // Search query filter (searches in name and notes)
        if (filters.searchQuery) {
          query = query.or(`name.ilike.%${filters.searchQuery}%,notes.ilike.%${filters.searchQuery}%`);
        }

        // Created by user filter
        if (filters.createdBy) {
          query = query.eq('metadata->created_by_user_id', filters.createdBy);
        }
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching voice events:', error);
        throw new Error(`Failed to fetch voice events: ${error.message}`);
      }

      return (data || []).map(event => this.mapDatabaseEventToVoiceEvent(event));
    } catch (error) {
      console.error('VoiceEventService.getVoiceEvents error:', error);
      throw error;
    }
  }

  /**
   * Update a voice event and create audit trail
   * @param eventId - ID of the event to update
   * @param updates - Partial event data to update
   * @param userId - ID of the user making the change
   * @param changeReason - Optional reason for the change
   * @returns Promise<VoiceEvent> - The updated event
   */
  async updateVoiceEvent(
    eventId: string, 
    updates: Partial<VoiceEvent>,
    userId?: string,
    changeReason?: string
  ): Promise<VoiceEvent> {
    try {
      // First, get the current event for audit trail
      const { data: currentEvent, error: fetchError } = await supabase
        .from('inventory_events')
        .select('*')
        .eq('id', eventId)
        .single();

      if (fetchError) {
        throw new Error(`Failed to fetch current event: ${fetchError.message}`);
      }

      // Prepare update data
      const updateData: any = {};
      
      if (updates.product_name !== undefined) updateData.name = updates.product_name;
      if (updates.quantity !== undefined) updateData.quantity = updates.quantity;
      if (updates.unit !== undefined) updateData.unit = updates.unit;
      if (updates.notes !== undefined) updateData.notes = updates.notes;
      if (updates.voice_confidence_score !== undefined) updateData.voice_confidence_score = updates.voice_confidence_score;
      if (updates.voice_confidence_breakdown !== undefined) updateData.voice_confidence_breakdown = updates.voice_confidence_breakdown;
      if (updates.raw_transcript !== undefined) updateData.raw_transcript = updates.raw_transcript;
      if (updates.condition !== undefined) updateData.condition_on_receipt = updates.condition;
      if (updates.temperature !== undefined) updateData.temperature_at_receipt = updates.temperature;
      if (updates.vendor_name !== undefined) updateData.vendor_name = updates.vendor_name;
      if (updates.customer_name !== undefined) updateData.customer_name = updates.customer_name;
      if (updates.occurred_at !== undefined) updateData.occurred_at = updates.occurred_at;

      // Update the event
      const { data: updatedEvent, error: updateError } = await supabase
        .from('inventory_events')
        .update(updateData)
        .eq('id', eventId)
        .select()
        .single();

      if (updateError) {
        throw new Error(`Failed to update voice event: ${updateError.message}`);
      }

      // Create audit trail entries for changed fields
      await this.createAuditTrailEntries(
        eventId,
        currentEvent,
        updatedEvent,
        userId,
        changeReason
      );

      return this.mapDatabaseEventToVoiceEvent(updatedEvent);
    } catch (error) {
      console.error('VoiceEventService.updateVoiceEvent error:', error);
      throw error;
    }
  }

  /**
   * Delete a voice event
   * @param eventId - ID of the event to delete
   * @returns Promise<void>
   */
  async deleteVoiceEvent(eventId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('inventory_events')
        .delete()
        .eq('id', eventId);

      if (error) {
        throw new Error(`Failed to delete voice event: ${error.message}`);
      }
    } catch (error) {
      console.error('VoiceEventService.deleteVoiceEvent error:', error);
      throw error;
    }
  }

  /**
   * Get events that require quality review (low confidence)
   * @param confidenceThreshold - Threshold below which events need review (default: 0.7)
   * @returns Promise<VoiceEvent[]> - Array of events needing review
   */
  async getEventsForQualityReview(confidenceThreshold: number = 0.7): Promise<VoiceEvent[]> {
    try {
      const { data, error } = await supabase
        .from('inventory_events')
        .select(`
          id,
          event_type,
          name,
          quantity,
          unit,
          voice_confidence_score,
          voice_confidence_breakdown,
          raw_transcript,
          audio_recording_url,
          created_at,
          occurred_at,
          metadata
        `)
        .eq('created_by_voice', true)
        .lt('voice_confidence_score', confidenceThreshold)
        .order('created_at', { ascending: true }); // Oldest first for review queue

      if (error) {
        throw new Error(`Failed to fetch events for quality review: ${error.message}`);
      }

      return (data || []).map(event => this.mapDatabaseEventToVoiceEvent(event));
    } catch (error) {
      console.error('VoiceEventService.getEventsForQualityReview error:', error);
      throw error;
    }
  }

  /**
   * Get audit trail for a specific event
   * @param eventId - ID of the event
   * @returns Promise<EventAudit[]> - Array of audit trail entries
   */
  async getEventAuditTrail(eventId: string): Promise<EventAudit[]> {
    try {
      const { data, error } = await supabase
        .from('event_audit_trail')
        .select('*')
        .eq('event_id', eventId)
        .order('changed_at', { ascending: false });

      if (error) {
        throw new Error(`Failed to fetch audit trail: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      console.error('VoiceEventService.getEventAuditTrail error:', error);
      throw error;
    }
  }

  /**
   * Approve a voice event (mark as reviewed and approved)
   * @param eventId - ID of the event to approve
   * @param reviewerId - ID of the user approving the event
   * @returns Promise<void>
   */
  async approveVoiceEvent(eventId: string, reviewerId?: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('inventory_events')
        .update({
          metadata: {
            review_status: 'approved',
            reviewed_by: reviewerId,
            reviewed_at: new Date().toISOString()
          }
        })
        .eq('id', eventId);

      if (error) {
        throw new Error(`Failed to approve voice event: ${error.message}`);
      }

      // Create audit trail entry
      await this.createAuditTrailEntries(
        eventId,
        { metadata: { review_status: 'pending' } },
        { metadata: { review_status: 'approved' } },
        reviewerId,
        'Event approved during quality review'
      );
    } catch (error) {
      console.error('VoiceEventService.approveVoiceEvent error:', error);
      throw error;
    }
  }

  /**
   * Reject a voice event (mark as reviewed and rejected)
   * @param eventId - ID of the event to reject
   * @param reason - Reason for rejection
   * @param reviewerId - ID of the user rejecting the event
   * @returns Promise<void>
   */
  async rejectVoiceEvent(eventId: string, reason: string, reviewerId?: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('inventory_events')
        .update({
          metadata: {
            review_status: 'rejected',
            rejection_reason: reason,
            reviewed_by: reviewerId,
            reviewed_at: new Date().toISOString()
          }
        })
        .eq('id', eventId);

      if (error) {
        throw new Error(`Failed to reject voice event: ${error.message}`);
      }

      // Create audit trail entry
      await this.createAuditTrailEntries(
        eventId,
        { metadata: { review_status: 'pending' } },
        { metadata: { review_status: 'rejected', rejection_reason: reason } },
        reviewerId,
        `Event rejected during quality review: ${reason}`
      );
    } catch (error) {
      console.error('VoiceEventService.rejectVoiceEvent error:', error);
      throw error;
    }
  }

  /**
   * Batch approve multiple voice events
   * @param eventIds - Array of event IDs to approve
   * @param reviewerId - ID of the user approving the events
   * @returns Promise<void>
   */
  async batchApproveVoiceEvents(eventIds: string[], reviewerId?: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('inventory_events')
        .update({
          metadata: {
            review_status: 'approved',
            reviewed_by: reviewerId,
            reviewed_at: new Date().toISOString()
          }
        })
        .in('id', eventIds);

      if (error) {
        throw new Error(`Failed to batch approve voice events: ${error.message}`);
      }

      // Create audit trail entries for each event
      for (const eventId of eventIds) {
        await this.createAuditTrailEntries(
          eventId,
          { metadata: { review_status: 'pending' } },
          { metadata: { review_status: 'approved' } },
          reviewerId,
          'Event approved during batch quality review'
        );
      }
    } catch (error) {
      console.error('VoiceEventService.batchApproveVoiceEvents error:', error);
      throw error;
    }
  }

  /**
   * Batch reject multiple voice events
   * @param eventIds - Array of event IDs to reject
   * @param reason - Reason for rejection
   * @param reviewerId - ID of the user rejecting the events
   * @returns Promise<void>
   */
  async batchRejectVoiceEvents(eventIds: string[], reason: string, reviewerId?: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('inventory_events')
        .update({
          metadata: {
            review_status: 'rejected',
            rejection_reason: reason,
            reviewed_by: reviewerId,
            reviewed_at: new Date().toISOString()
          }
        })
        .in('id', eventIds);

      if (error) {
        throw new Error(`Failed to batch reject voice events: ${error.message}`);
      }

      // Create audit trail entries for each event
      for (const eventId of eventIds) {
        await this.createAuditTrailEntries(
          eventId,
          { metadata: { review_status: 'pending' } },
          { metadata: { review_status: 'rejected', rejection_reason: reason } },
          reviewerId,
          `Event rejected during batch quality review: ${reason}`
        );
      }
    } catch (error) {
      console.error('VoiceEventService.batchRejectVoiceEvents error:', error);
      throw error;
    }
  }

  /**
   * Get voice event statistics
   * @returns Promise with various statistics about voice events
   */
  async getVoiceEventStatistics(): Promise<VoiceEventStatistics> {
    try {
      const { data, error } = await supabase
        .from('inventory_events')
        .select('voice_confidence_score')
        .eq('created_by_voice', true);

      if (error) {
        throw new Error(`Failed to fetch voice event statistics: ${error.message}`);
      }

      const events = data || [];
      const totalEvents = events.length;
      
      if (totalEvents === 0) {
        return {
          totalEvents: 0,
          highConfidenceEvents: 0,
          mediumConfidenceEvents: 0,
          lowConfidenceEvents: 0,
          averageConfidence: 0,
          eventsNeedingReview: 0
        };
      }

      const highConfidenceEvents = events.filter(e => e.voice_confidence_score >= 0.9).length;
      const mediumConfidenceEvents = events.filter(e => e.voice_confidence_score >= 0.7 && e.voice_confidence_score < 0.9).length;
      const lowConfidenceEvents = events.filter(e => e.voice_confidence_score < 0.7).length;
      const averageConfidence = events.reduce((sum, e) => sum + (e.voice_confidence_score || 0), 0) / totalEvents;

      return {
        totalEvents,
        highConfidenceEvents,
        mediumConfidenceEvents,
        lowConfidenceEvents,
        averageConfidence: Math.round(averageConfidence * 100) / 100,
        eventsNeedingReview: lowConfidenceEvents
      };
    } catch (error) {
      console.error('VoiceEventService.getVoiceEventStatistics error:', error);
      throw error;
    }
  }

  /**
   * Private method to create audit trail entries for changed fields
   */
  private async createAuditTrailEntries(
    eventId: string,
    oldEvent: any,
    newEvent: any,
    userId?: string,
    changeReason?: string
  ): Promise<void> {
    const auditEntries: any[] = [];
    const fieldsToTrack = [
      'name', 'quantity', 'unit', 'notes', 'voice_confidence_score',
      'voice_confidence_breakdown', 'raw_transcript', 'condition_on_receipt',
      'temperature_at_receipt', 'vendor_name', 'customer_name', 'occurred_at'
    ];

    for (const field of fieldsToTrack) {
      if (oldEvent[field] !== newEvent[field]) {
        auditEntries.push({
          event_id: eventId,
          field_name: field,
          old_value: oldEvent[field],
          new_value: newEvent[field],
          changed_by: userId,
          change_reason: changeReason,
          change_source: 'manual'
        });
      }
    }

    if (auditEntries.length > 0) {
      const { error } = await supabase
        .from('event_audit_trail')
        .insert(auditEntries);

      if (error) {
        console.error('Error creating audit trail entries:', error);
        // Don't throw here - audit trail failure shouldn't prevent the update
      }
    }
  }

  /**
   * Private method to map database event to VoiceEvent interface
   */
  private mapDatabaseEventToVoiceEvent(dbEvent: any): VoiceEvent {
    const metadata = dbEvent.metadata || {};
    
    return {
      id: dbEvent.id,
      event_type: dbEvent.event_type,
      product_name: metadata.product_name || dbEvent.name || 'Unknown Product',
      quantity: dbEvent.quantity,
      unit: dbEvent.unit || 'lbs',
      vendor_name: metadata.vendor_name,
      customer_name: metadata.customer_name,
      condition: metadata.condition,
      temperature: metadata.temperature,
      temperature_unit: metadata.temperature_unit || 'fahrenheit',
      processing_method: metadata.processing_method,
      quality_grade: metadata.quality_grade,
      market_form: metadata.market_form,
      notes: dbEvent.notes,
      occurred_at: dbEvent.occurred_at,
      
      // Voice-specific fields
      voice_confidence_score: dbEvent.voice_confidence_score || 0,
      voice_confidence_breakdown: dbEvent.voice_confidence_breakdown || {
        product_match: 0,
        quantity_extraction: 0,
        vendor_match: 0,
        overall: 0
      },
      raw_transcript: dbEvent.raw_transcript || '',
      audio_recording_url: dbEvent.audio_recording_url,
      created_by_voice: dbEvent.created_by_voice || false,
      
      // Metadata
      created_at: dbEvent.created_at,
      updated_at: dbEvent.updated_at,
      created_by: metadata.created_by_user_id,
      last_modified_by: metadata.last_modified_by,
      session_id: metadata.session_id
    };
  }
}

// Export a singleton instance
export const voiceEventService = new VoiceEventService();
