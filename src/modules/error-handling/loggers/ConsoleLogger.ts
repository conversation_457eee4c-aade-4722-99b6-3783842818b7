import { ErrorLogger, ErrorDetails, ErrorSeverity } from '../types';

/**
 * Console-based error logger
 */
export class ConsoleLogger implements ErrorLogger {
  private isDevelopment = process.env.NODE_ENV === 'development';

  async log(errorDetails: ErrorDetails): Promise<void> {
    const logLevel = this.getLogLevel(errorDetails.severity);
    const logMessage = this.formatLogMessage(errorDetails);
    
    // Use appropriate console method based on severity
    switch (logLevel) {
      case 'error':
        console.error(logMessage, errorDetails);
        break;
      case 'warn':
        console.warn(logMessage, errorDetails);
        break;
      case 'info':
        console.info(logMessage, errorDetails);
        break;
      default:
        console.log(logMessage, errorDetails);
    }

    // In development, also log the stack trace
    if (this.isDevelopment && errorDetails.stack) {
      console.error('Stack trace:', errorDetails.stack);
    }
  }

  private getLogLevel(severity: ErrorSeverity): 'error' | 'warn' | 'info' | 'log' {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        return 'error';
      case ErrorSeverity.MEDIUM:
        return 'warn';
      case ErrorSeverity.LOW:
        return 'info';
      default:
        return 'log';
    }
  }

  private formatLogMessage(errorDetails: ErrorDetails): string {
    const timestamp = new Date().toISOString();
    const context = errorDetails.context ? 
      ` [${Object.entries(errorDetails.context)
        .filter(([_, value]) => value !== undefined)
        .map(([key, value]) => `${key}=${value}`)
        .join(', ')}]` : '';
    
    return `[${timestamp}] ${errorDetails.severity.toUpperCase()} ${errorDetails.category}/${errorDetails.code}: ${errorDetails.message}${context}`;
  }
}
