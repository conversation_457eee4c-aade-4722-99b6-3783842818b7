import { 
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>rror<PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>rror<PERSON>ot<PERSON>,
  ErrorManagerConfig,
  <PERSON>rror<PERSON>ontext,
  <PERSON>rror<PERSON>ategory,
  ErrorSeverity
} from './types';
import { ConsoleLogger } from './loggers/ConsoleLogger.js';
import { DefaultErrorHandler } from './handlers/DefaultErrorHandler.js';

/**
 * Centralized error management system
 */
export class ErrorManager {
  private handlers: ErrorHandler[] = [];
  private loggers: ErrorLogger[] = [];
  private recoveryStrategies: ErrorRecovery[] = [];
  private notifiers: ErrorNotifier[] = [];
  private config: Required<ErrorManagerConfig>;

  constructor(config: ErrorManagerConfig = {}) {
    this.config = {
      loggers: config.loggers || [new ConsoleLogger()],
      handlers: config.handlers || [new DefaultError<PERSON><PERSON><PERSON>()],
      recoveryStrategies: config.recoveryStrategies || [],
      notifiers: config.notifiers || [],
      enableAutoRecovery: config.enableAutoRecovery ?? true,
      enableUserNotification: config.enableUserNotification ?? true,
      maxRetries: config.maxRetries ?? 3,
      retryDelay: config.retryDelay ?? 1000
    };

    this.handlers = this.config.handlers;
    this.loggers = this.config.loggers;
    this.recoveryStrategies = this.config.recoveryStrategies;
    this.notifiers = this.config.notifiers;
  }

  /**
   * Handle an error with full processing pipeline
   */
  async handleError(error: unknown, context?: ErrorContext): Promise<ErrorDetails> {
    try {
      // Find appropriate handler
      const handler = this.findHandler(error);
      const errorDetails = handler.handle(error, context);

      // Log the error
      await this.logError(errorDetails);

      // Attempt recovery if enabled
      if (this.config.enableAutoRecovery) {
        await this.attemptRecovery(errorDetails);
      }

      // Notify if enabled and severity is high enough
      if (this.config.enableUserNotification && this.shouldNotify(errorDetails)) {
        await this.notifyError(errorDetails);
      }

      return errorDetails;
    } catch (handlingError) {
      // Fallback error handling
      const fallbackError: ErrorDetails = {
        code: 'ERROR_HANDLING_FAILED',
        message: 'Error handling system failed',
        category: ErrorCategory.SYSTEM,
        severity: ErrorSeverity.CRITICAL,
        context,
        originalError: handlingError instanceof Error ? handlingError : new Error(String(handlingError))
      };

      await this.logError(fallbackError);
      return fallbackError;
    }
  }

  /**
   * Handle error with retry logic
   */
  async handleErrorWithRetry<T>(
    operation: () => Promise<T>,
    context?: ErrorContext,
    maxRetries?: number
  ): Promise<T> {
    const retries = maxRetries ?? this.config.maxRetries;
    let lastError: unknown;

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        
        if (attempt === retries) {
          // Final attempt failed, handle the error
          await this.handleError(error, { 
            ...context, 
            metadata: { ...context?.metadata, attempt, maxRetries: retries } 
          });
          throw error;
        }

        // Log retry attempt
        const errorDetails = this.findHandler(error).handle(error, context);
        if (errorDetails.severity !== ErrorSeverity.LOW) {
          await this.logError({
            ...errorDetails,
            message: `Retry attempt ${attempt + 1}/${retries}: ${errorDetails.message}`,
            context: { 
              ...errorDetails.context, 
              metadata: { ...errorDetails.context?.metadata, attempt, maxRetries: retries }
            }
          });
        }

        // Wait before retry with exponential backoff
        await this.delay(this.config.retryDelay * Math.pow(2, attempt));
      }
    }

    throw lastError;
  }

  /**
   * Create a wrapper function with error handling
   */
  withErrorHandling<T extends any[], R>(
    fn: (...args: T) => Promise<R>,
    context?: ErrorContext
  ): (...args: T) => Promise<R> {
    return async (...args: T): Promise<R> => {
      try {
        return await fn(...args);
      } catch (error) {
        await this.handleError(error, context);
        throw error;
      }
    };
  }

  /**
   * Add error handler
   */
  addHandler(handler: ErrorHandler): void {
    this.handlers.unshift(handler); // Add to beginning for priority
  }

  /**
   * Add error logger
   */
  addLogger(logger: ErrorLogger): void {
    this.loggers.push(logger);
  }

  /**
   * Add recovery strategy
   */
  addRecoveryStrategy(strategy: ErrorRecovery): void {
    this.recoveryStrategies.push(strategy);
  }

  /**
   * Add notifier
   */
  addNotifier(notifier: ErrorNotifier): void {
    this.notifiers.push(notifier);
  }

  /**
   * Find appropriate error handler
   */
  private findHandler(error: unknown): ErrorHandler {
    const handler = this.handlers.find(h => h.canHandle(error));
    return handler || this.handlers[this.handlers.length - 1]; // Fallback to last handler
  }

  /**
   * Log error using all configured loggers
   */
  private async logError(errorDetails: ErrorDetails): Promise<void> {
    const logPromises = this.loggers.map(logger => 
      logger.log(errorDetails).catch(logError => 
        console.error('Logger failed:', logError)
      )
    );
    
    await Promise.allSettled(logPromises);
  }

  /**
   * Attempt error recovery
   */
  private async attemptRecovery(errorDetails: ErrorDetails): Promise<boolean> {
    for (const strategy of this.recoveryStrategies) {
      if (strategy.canRecover(errorDetails)) {
        try {
          const recovered = await strategy.recover(errorDetails);
          if (recovered) {
            await this.logError({
              ...errorDetails,
              message: `Recovery successful: ${errorDetails.message}`,
              severity: ErrorSeverity.LOW
            });
            return true;
          }
        } catch (recoveryError) {
          await this.logError({
            code: 'RECOVERY_FAILED',
            message: `Recovery failed for ${errorDetails.code}`,
            category: ErrorCategory.SYSTEM,
            severity: ErrorSeverity.MEDIUM,
            originalError: recoveryError instanceof Error ? recoveryError : new Error(String(recoveryError))
          });
        }
      }
    }
    return false;
  }

  /**
   * Notify about error
   */
  private async notifyError(errorDetails: ErrorDetails): Promise<void> {
    const notifyPromises = this.notifiers.map(notifier =>
      notifier.notify(errorDetails).catch(notifyError =>
        console.error('Notifier failed:', notifyError)
      )
    );

    await Promise.allSettled(notifyPromises);
  }

  /**
   * Determine if error should trigger notification
   */
  private shouldNotify(errorDetails: ErrorDetails): boolean {
    return errorDetails.severity === ErrorSeverity.HIGH || 
           errorDetails.severity === ErrorSeverity.CRITICAL;
  }

  /**
   * Delay utility for retry logic
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get error statistics
   */
  getErrorStats(): {
    totalHandlers: number;
    totalLoggers: number;
    totalRecoveryStrategies: number;
    totalNotifiers: number;
  } {
    return {
      totalHandlers: this.handlers.length,
      totalLoggers: this.loggers.length,
      totalRecoveryStrategies: this.recoveryStrategies.length,
      totalNotifiers: this.notifiers.length
    };
  }
}

// Singleton instance
export const errorManager = new ErrorManager();
