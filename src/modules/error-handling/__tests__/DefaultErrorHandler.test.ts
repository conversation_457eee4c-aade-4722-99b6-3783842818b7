import { describe, it, expect } from 'vitest';
import { DefaultErrorHandler } from '../handlers/DefaultErrorHandler';
import { AppError, ErrorCategory, ErrorSeverity } from '../types';

describe('DefaultErrorHandler', () => {
  let handler: DefaultErrorHandler;

  beforeEach(() => {
    handler = new DefaultErrorHandler();
  });

  describe('canHandle', () => {
    it('should handle any error type', () => {
      expect(handler.canHandle(new Error())).toBe(true);
      expect(handler.canHandle('string error')).toBe(true);
      expect(handler.canHandle(123)).toBe(true);
      expect(handler.canHandle(null)).toBe(true);
    });
  });

  describe('handle', () => {
    it('should handle AppError instances', () => {
      const appError = new AppError('Test error', 'TEST_CODE', ErrorCategory.VALIDATION);
      const result = handler.handle(appError);

      expect(result.code).toBe('TEST_CODE');
      expect(result.message).toBe('Test error');
      expect(result.category).toBe(ErrorCategory.VALIDATION);
      expect(result.severity).toBe(ErrorSeverity.MEDIUM);
    });

    it('should handle standard Error instances', () => {
      const error = new Error('Standard error');
      const result = handler.handle(error);

      expect(result.code).toBeDefined();
      expect(result.message).toBe('Standard error');
      expect(result.category).toBe(ErrorCategory.SYSTEM);
      expect(result.severity).toBe(ErrorSeverity.HIGH); // Contains 'error' keyword
    });

    it('should handle string errors', () => {
      const result = handler.handle('String error message');

      expect(result.code).toBe('STRING_ERROR');
      expect(result.message).toBe('String error message');
      expect(result.category).toBe(ErrorCategory.SYSTEM);
    });

    it('should handle unknown error types', () => {
      const result = handler.handle(null);

      expect(result.code).toBe('UNKNOWN_ERROR');
      expect(result.message).toBe('An unknown error occurred');
      expect(result.category).toBe(ErrorCategory.SYSTEM);
    });

    it('should include context information', () => {
      const context = { operation: 'test', userId: '123' };
      const result = handler.handle(new Error('Test'), context);

      expect(result.context).toEqual(context);
    });
  });

  describe('error categorization', () => {
    it('should categorize network errors', () => {
      const networkError = new Error('Network request failed');
      const result = handler.handle(networkError);

      expect(result.category).toBe(ErrorCategory.NETWORK);
      expect(result.userMessage).toContain('Network connection');
    });

    it('should categorize database errors', () => {
      const dbError = new Error('Database query failed');
      const result = handler.handle(dbError);

      expect(result.category).toBe(ErrorCategory.DATABASE);
      expect(result.userMessage).toContain('Database operation');
    });

    it('should categorize authentication errors', () => {
      const authError = new Error('Authentication failed');
      const result = handler.handle(authError);

      expect(result.category).toBe(ErrorCategory.AUTHENTICATION);
      expect(result.userMessage).toContain('Authentication failed');
    });

    it('should categorize validation errors', () => {
      const validationError = new Error('Validation failed');
      const result = handler.handle(validationError);

      expect(result.category).toBe(ErrorCategory.VALIDATION);
      expect(result.userMessage).toContain('Please check your input');
    });

    it('should categorize voice processing errors', () => {
      const voiceError = new Error('Voice transcription failed');
      const result = handler.handle(voiceError);

      expect(result.category).toBe(ErrorCategory.VOICE_PROCESSING);
      expect(result.userMessage).toContain('Voice processing');
    });

    it('should categorize storage errors', () => {
      const storageError = new Error('Storage upload failed');
      const result = handler.handle(storageError);

      expect(result.category).toBe(ErrorCategory.STORAGE);
      expect(result.userMessage).toContain('File operation');
    });
  });

  describe('severity determination', () => {
    it('should determine critical severity', () => {
      const criticalError = new Error('Critical system failure');
      const result = handler.handle(criticalError);

      expect(result.severity).toBe(ErrorSeverity.CRITICAL);
    });

    it('should determine high severity for database errors', () => {
      const dbError = new Error('Database connection failed');
      const result = handler.handle(dbError);

      expect(result.severity).toBe(ErrorSeverity.HIGH);
    });

    it('should determine low severity for validation errors', () => {
      const validationError = new Error('Invalid input format');
      const result = handler.handle(validationError);

      expect(result.severity).toBe(ErrorSeverity.LOW);
    });
  });

  describe('recovery actions', () => {
    it('should provide network recovery actions', () => {
      const networkError = new Error('Network error');
      const result = handler.handle(networkError);

      expect(result.recoveryActions).toContain('Check your internet connection');
      expect(result.recoveryActions).toContain('Try again in a few moments');
    });

    it('should provide validation recovery actions', () => {
      const validationError = new Error('Validation error');
      const result = handler.handle(validationError);

      expect(result.recoveryActions).toContain('Check the highlighted fields');
      expect(result.recoveryActions).toContain('Ensure all required fields are filled');
    });
  });
});
