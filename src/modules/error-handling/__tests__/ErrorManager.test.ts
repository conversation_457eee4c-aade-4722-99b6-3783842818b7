import { describe, it, expect, vi, beforeEach } from 'vitest';
import { ErrorManager } from '../ErrorManager';
import { ErrorCategory, ErrorSeverity } from '../types';

// Mock dependencies
vi.mock('../loggers/ConsoleLogger', () => ({
  ConsoleLogger: vi.fn().mockImplementation(() => ({
    log: vi.fn().mockResolvedValue(undefined)
  }))
}));

vi.mock('../handlers/DefaultErrorHandler', () => ({
  DefaultErrorHandler: vi.fn().mockImplementation(() => ({
    canHandle: vi.fn().mockReturnValue(true),
    handle: vi.fn().mockImplementation((error) => ({
      code: 'TEST_ERROR',
      message: String(error),
      category: ErrorCategory.SYSTEM,
      severity: ErrorSeverity.MEDIUM
    }))
  }))
}));

describe('ErrorManager', () => {
  let errorManager: ErrorManager;

  beforeEach(() => {
    errorManager = new ErrorManager();
  });

  describe('handleError', () => {
    it('should handle basic errors', async () => {
      const error = new Error('Test error');
      const result = await errorManager.handleError(error);

      expect(result).toBeDefined();
      expect(result.message).toBe('Error: Test error');
      expect(result.category).toBe(ErrorCategory.SYSTEM);
    });

    it('should handle string errors', async () => {
      const result = await errorManager.handleError('String error');

      expect(result).toBeDefined();
      expect(result.code).toBe('TEST_ERROR');
      expect(result.message).toBe('String error');
    });

    it('should include context information', async () => {
      const context = { operation: 'test', userId: '123' };
      const result = await errorManager.handleError('Error with context', context);

      // The actual implementation should preserve context
      expect(result.context).toBeDefined();
    });
  });

  describe('handleErrorWithRetry', () => {
    it('should succeed on first attempt', async () => {
      const operation = vi.fn().mockResolvedValue('success');
      const result = await errorManager.handleErrorWithRetry(operation);

      expect(result).toBe('success');
      expect(operation).toHaveBeenCalledTimes(1);
    });

    it('should retry on failure and succeed', async () => {
      const operation = vi.fn()
        .mockRejectedValueOnce(new Error('First failure'))
        .mockResolvedValueOnce('success');

      const result = await errorManager.handleErrorWithRetry(operation, undefined, 1);

      expect(result).toBe('success');
      expect(operation).toHaveBeenCalledTimes(2);
    });

    it('should throw after max retries', async () => {
      const operation = vi.fn().mockRejectedValue(new Error('Persistent failure'));

      await expect(
        errorManager.handleErrorWithRetry(operation, undefined, 0)
      ).rejects.toThrow('Persistent failure');
    });
  });

  describe('withErrorHandling', () => {
    it('should wrap async functions with error handling', async () => {
      const fn = vi.fn().mockResolvedValue('result');
      const wrappedFn = errorManager.withErrorHandling(fn);

      const result = await wrappedFn('arg1', 'arg2');

      expect(result).toBe('result');
      expect(fn).toHaveBeenCalledWith('arg1', 'arg2');
    });

    it('should handle errors in wrapped functions', async () => {
      const error = new Error('Wrapped function error');
      const fn = vi.fn().mockRejectedValue(error);
      const wrappedFn = errorManager.withErrorHandling(fn);

      await expect(wrappedFn()).rejects.toThrow('Wrapped function error');
    });
  });

  describe('configuration', () => {
    it('should use custom configuration', () => {
      const customConfig = {
        maxRetries: 5,
        retryDelay: 2000,
        enableAutoRecovery: false
      };

      const customErrorManager = new ErrorManager(customConfig);
      const stats = customErrorManager.getErrorStats();

      expect(stats).toBeDefined();
      expect(stats.totalHandlers).toBeGreaterThan(0);
      expect(stats.totalLoggers).toBeGreaterThan(0);
    });
  });

  describe('extensibility', () => {
    it('should allow adding handlers', () => {
      const handler = {
        canHandle: vi.fn().mockReturnValue(true),
        handle: vi.fn().mockReturnValue({
          code: 'CUSTOM',
          message: 'Custom handled',
          category: ErrorCategory.SYSTEM,
          severity: ErrorSeverity.LOW
        })
      };

      errorManager.addHandler(handler);
      const stats = errorManager.getErrorStats();

      expect(stats.totalHandlers).toBeGreaterThan(1);
    });

    it('should allow adding loggers', () => {
      const logger = { log: vi.fn().mockResolvedValue(undefined) };
      errorManager.addLogger(logger);

      const stats = errorManager.getErrorStats();
      expect(stats.totalLoggers).toBeGreaterThan(1);
    });
  });
});
