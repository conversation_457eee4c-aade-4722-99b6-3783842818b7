import { describe, it, expect, vi, beforeEach } from 'vitest';
import { ConsoleLogger } from '../loggers/ConsoleLogger';
import { ErrorSeverity, ErrorCategory } from '../types';

describe('ConsoleLogger', () => {
  let logger: ConsoleLogger;
  let consoleSpy: {
    error: ReturnType<typeof vi.spyOn>;
    warn: ReturnType<typeof vi.spyOn>;
    info: ReturnType<typeof vi.spyOn>;
    log: ReturnType<typeof vi.spyOn>;
  };

  beforeEach(() => {
    logger = new ConsoleLogger();
    consoleSpy = {
      error: vi.spyOn(console, 'error').mockImplementation(() => {}),
      warn: vi.spyOn(console, 'warn').mockImplementation(() => {}),
      info: vi.spyOn(console, 'info').mockImplementation(() => {}),
      log: vi.spyOn(console, 'log').mockImplementation(() => {})
    };
    
    // Reset NODE_ENV for consistent testing
    vi.stubEnv('NODE_ENV', 'test');
  });

  afterEach(() => {
    vi.restoreAllMocks();
    vi.unstubAllEnvs();
  });

  describe('log', () => {
    it('should log critical errors with console.error', async () => {
      const errorDetails = {
        code: 'CRITICAL_ERROR',
        message: 'Critical system failure',
        category: ErrorCategory.SYSTEM,
        severity: ErrorSeverity.CRITICAL
      };

      await logger.log(errorDetails);

      expect(consoleSpy.error).toHaveBeenCalledWith(
        expect.stringContaining('CRITICAL system/CRITICAL_ERROR: Critical system failure'),
        errorDetails
      );
    });

    it('should log high errors with console.error', async () => {
      const errorDetails = {
        code: 'HIGH_ERROR',
        message: 'High severity error',
        category: ErrorCategory.DATABASE,
        severity: ErrorSeverity.HIGH
      };

      await logger.log(errorDetails);

      expect(consoleSpy.error).toHaveBeenCalled();
    });

    it('should log medium errors with console.warn', async () => {
      const errorDetails = {
        code: 'MEDIUM_ERROR',
        message: 'Medium severity error',
        category: ErrorCategory.NETWORK,
        severity: ErrorSeverity.MEDIUM
      };

      await logger.log(errorDetails);

      expect(consoleSpy.warn).toHaveBeenCalled();
    });

    it('should log low errors with console.info', async () => {
      const errorDetails = {
        code: 'LOW_ERROR',
        message: 'Low severity error',
        category: ErrorCategory.VALIDATION,
        severity: ErrorSeverity.LOW
      };

      await logger.log(errorDetails);

      expect(consoleSpy.info).toHaveBeenCalled();
    });

    it('should include context in log message', async () => {
      const errorDetails = {
        code: 'CONTEXT_ERROR',
        message: 'Error with context',
        category: ErrorCategory.SYSTEM,
        severity: ErrorSeverity.MEDIUM,
        context: {
          userId: '123',
          operation: 'test'
        }
      };

      await logger.log(errorDetails);

      expect(consoleSpy.warn).toHaveBeenCalledWith(
        expect.stringContaining('userId=123, operation=test'),
        errorDetails
      );
    });

    it('should handle undefined context gracefully', async () => {
      const errorDetails = {
        code: 'NO_CONTEXT_ERROR',
        message: 'Error without context',
        category: ErrorCategory.SYSTEM,
        severity: ErrorSeverity.MEDIUM
      };

      await logger.log(errorDetails);

      expect(consoleSpy.warn).toHaveBeenCalledWith(
        expect.not.stringContaining('undefined'),
        errorDetails
      );
    });

    it('should log stack trace in development mode', async () => {
      vi.stubEnv('NODE_ENV', 'development');
      
      const errorDetails = {
        code: 'DEV_ERROR',
        message: 'Development error',
        category: ErrorCategory.SYSTEM,
        severity: ErrorSeverity.MEDIUM,
        stack: 'Error: Development error\n    at test.js:1:1'
      };

      await logger.log(errorDetails);

      // Check if any console.error call includes stack trace logging
      const errorCalls = consoleSpy.error.mock.calls;
      const hasStackTrace = errorCalls.some(call => 
        call[0] === 'Stack trace:' && call[1] === errorDetails.stack
      );
      expect(hasStackTrace).toBe(true);
    });

    it('should not log stack trace in non-development mode', async () => {
      vi.stubEnv('NODE_ENV', 'production');
      
      const errorDetails = {
        code: 'PROD_ERROR',
        message: 'Production error',
        category: ErrorCategory.SYSTEM,
        severity: ErrorSeverity.MEDIUM,
        stack: 'Error: Production error\n    at test.js:1:1'
      };

      await logger.log(errorDetails);

      expect(consoleSpy.error).not.toHaveBeenCalledWith('Stack trace:', expect.any(String));
    });
  });
});
