/**
 * Error handling framework types and interfaces
 */

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum ErrorCategory {
  VALIDATION = 'validation',
  NETWORK = 'network',
  DATABASE = 'database',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  VOICE_PROCESSING = 'voice_processing',
  STORAGE = 'storage',
  BUSINESS_LOGIC = 'business_logic',
  SYSTEM = 'system'
}

export interface ErrorContext {
  userId?: string;
  sessionId?: string;
  requestId?: string;
  component?: string;
  action?: string;
  metadata?: Record<string, unknown>;
  timestamp?: string;
}

export interface ErrorDetails {
  code: string;
  message: string;
  category: ErrorCategory;
  severity: ErrorSeverity;
  context?: ErrorContext;
  originalError?: Error;
  stack?: string;
  userMessage?: string;
  recoveryActions?: string[];
}

export interface ErrorHandler {
  canHandle(error: unknown): boolean;
  handle(error: unknown, context?: ErrorContext): ErrorDetails;
}

export interface ErrorLogger {
  log(errorDetails: ErrorDetails): Promise<void>;
}

export interface ErrorRecovery {
  canRecover(errorDetails: ErrorDetails): boolean;
  recover(errorDetails: ErrorDetails): Promise<boolean>;
}

export interface ErrorNotifier {
  notify(errorDetails: ErrorDetails): Promise<void>;
}

export interface ErrorManagerConfig {
  loggers?: ErrorLogger[];
  handlers?: ErrorHandler[];
  recoveryStrategies?: ErrorRecovery[];
  notifiers?: ErrorNotifier[];
  enableAutoRecovery?: boolean;
  enableUserNotification?: boolean;
  maxRetries?: number;
  retryDelay?: number;
}

export class AppError extends Error {
  public readonly code: string;
  public readonly category: ErrorCategory;
  public readonly severity: ErrorSeverity;
  public readonly context?: ErrorContext;
  public readonly userMessage?: string;
  public readonly recoveryActions?: string[];

  constructor(
    message: string,
    code: string,
    category: ErrorCategory,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    context?: ErrorContext,
    userMessage?: string,
    recoveryActions?: string[]
  ) {
    super(message);
    this.name = 'AppError';
    this.code = code;
    this.category = category;
    this.severity = severity;
    this.context = context;
    this.userMessage = userMessage;
    this.recoveryActions = recoveryActions;
  }
}

export class ValidationError extends AppError {
  constructor(
    message: string,
    field?: string,
    context?: ErrorContext
  ) {
    super(
      message,
      'VALIDATION_ERROR',
      ErrorCategory.VALIDATION,
      ErrorSeverity.LOW,
      { ...context, metadata: { ...context?.metadata, field } },
      message,
      ['Please correct the highlighted fields and try again']
    );
  }
}

export class NetworkError extends AppError {
  constructor(
    message: string,
    statusCode?: number,
    context?: ErrorContext
  ) {
    super(
      message,
      'NETWORK_ERROR',
      ErrorCategory.NETWORK,
      ErrorSeverity.MEDIUM,
      { ...context, metadata: { ...context?.metadata, statusCode } },
      'Network connection issue. Please check your internet connection.',
      ['Check your internet connection', 'Try again in a few moments']
    );
  }
}

export class DatabaseError extends AppError {
  constructor(
    message: string,
    operation?: string,
    context?: ErrorContext
  ) {
    super(
      message,
      'DATABASE_ERROR',
      ErrorCategory.DATABASE,
      ErrorSeverity.HIGH,
      { ...context, metadata: { ...context?.metadata, operation } },
      'Database operation failed. Please try again.',
      ['Try again', 'Contact support if the problem persists']
    );
  }
}

export class VoiceProcessingError extends AppError {
  constructor(
    message: string,
    stage?: string,
    context?: ErrorContext
  ) {
    super(
      message,
      'VOICE_PROCESSING_ERROR',
      ErrorCategory.VOICE_PROCESSING,
      ErrorSeverity.MEDIUM,
      { ...context, metadata: { ...context?.metadata, stage } },
      'Voice processing failed. Please try recording again.',
      ['Speak clearly and try again', 'Check your microphone settings']
    );
  }
}

export class StorageError extends AppError {
  constructor(
    message: string,
    operation?: string,
    context?: ErrorContext
  ) {
    super(
      message,
      'STORAGE_ERROR',
      ErrorCategory.STORAGE,
      ErrorSeverity.MEDIUM,
      { ...context, metadata: { ...context?.metadata, operation } },
      'File storage operation failed.',
      ['Try again', 'Check available storage space']
    );
  }
}
