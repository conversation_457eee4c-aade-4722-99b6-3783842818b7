import { 
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  ErrorDetails, 
  ErrorContext, 
  ErrorCategory, 
  ErrorSeverity,
  AppError 
} from '../types';

/**
 * Default error handler that can handle any error type
 */
export class DefaultErrorHandler implements ErrorHandler {
  canHandle(_error: unknown): boolean {
    return true; // Can handle any error as fallback
  }

  handle(error: unknown, context?: ErrorContext): ErrorDetails {
    // Handle AppError instances
    if (error instanceof AppError) {
      return {
        code: error.code,
        message: error.message,
        category: error.category,
        severity: error.severity,
        context: { ...error.context, ...context },
        originalError: error,
        stack: error.stack,
        userMessage: error.userMessage,
        recoveryActions: error.recoveryActions
      };
    }

    // Handle standard Error instances
    if (error instanceof Error) {
      return this.handleStandardError(error, context);
    }

    // Handle string errors
    if (typeof error === 'string') {
      return {
        code: 'STRING_ERROR',
        message: error,
        category: ErrorCategory.SYSTEM,
        severity: ErrorSeverity.MEDIUM,
        context,
        userMessage: 'An unexpected error occurred'
      };
    }

    // Handle unknown error types
    return {
      code: 'UNKNOWN_ERROR',
      message: 'An unknown error occurred',
      category: ErrorCategory.SYSTEM,
      severity: ErrorSeverity.MEDIUM,
      context,
      originalError: error instanceof Error ? error : new Error(String(error)),
      userMessage: 'An unexpected error occurred'
    };
  }

  private handleStandardError(error: Error, context?: ErrorContext): ErrorDetails {
    // Categorize based on error message patterns
    const category = this.categorizeError(error);
    const severity = this.determineSeverity(error, category);
    const userMessage = this.generateUserMessage(error, category);
    const recoveryActions = this.generateRecoveryActions(category);

    return {
      code: this.generateErrorCode(error, category),
      message: error.message,
      category,
      severity,
      context,
      originalError: error,
      stack: error.stack,
      userMessage,
      recoveryActions
    };
  }

  private categorizeError(error: Error): ErrorCategory {
    const message = error.message.toLowerCase();
    const name = error.name.toLowerCase();

    // Network errors
    if (message.includes('network') || 
        message.includes('fetch') || 
        message.includes('connection') ||
        name.includes('networkerror')) {
      return ErrorCategory.NETWORK;
    }

    // Database errors
    if (message.includes('database') || 
        message.includes('sql') || 
        message.includes('query') ||
        name.includes('databaseerror')) {
      return ErrorCategory.DATABASE;
    }

    // Authentication errors
    if (message.includes('auth') || 
        message.includes('login') || 
        message.includes('unauthorized') ||
        name.includes('autherror')) {
      return ErrorCategory.AUTHENTICATION;
    }

    // Validation errors
    if (message.includes('validation') || 
        message.includes('invalid') || 
        message.includes('required') ||
        name.includes('validationerror')) {
      return ErrorCategory.VALIDATION;
    }

    // Voice processing errors
    if (message.includes('voice') || 
        message.includes('audio') || 
        message.includes('speech') ||
        message.includes('transcription')) {
      return ErrorCategory.VOICE_PROCESSING;
    }

    // Storage errors
    if (message.includes('storage') || 
        message.includes('upload') || 
        message.includes('download') ||
        message.includes('file')) {
      return ErrorCategory.STORAGE;
    }

    return ErrorCategory.SYSTEM;
  }

  private determineSeverity(error: Error, category: ErrorCategory): ErrorSeverity {
    const message = error.message.toLowerCase();

    // Critical errors
    if (message.includes('critical') || 
        message.includes('fatal') || 
        message.includes('crash')) {
      return ErrorSeverity.CRITICAL;
    }

    // High severity errors
    if (category === ErrorCategory.DATABASE || 
        category === ErrorCategory.AUTHENTICATION ||
        message.includes('failed') ||
        message.includes('error')) {
      return ErrorSeverity.HIGH;
    }

    // Low severity errors
    if (category === ErrorCategory.VALIDATION ||
        message.includes('warning') ||
        message.includes('invalid input')) {
      return ErrorSeverity.LOW;
    }

    return ErrorSeverity.MEDIUM;
  }

  private generateUserMessage(error: Error, category: ErrorCategory): string {
    switch (category) {
      case ErrorCategory.NETWORK:
        return 'Network connection issue. Please check your internet connection and try again.';
      
      case ErrorCategory.DATABASE:
        return 'Database operation failed. Please try again in a moment.';
      
      case ErrorCategory.AUTHENTICATION:
        return 'Authentication failed. Please log in again.';
      
      case ErrorCategory.VALIDATION:
        return 'Please check your input and try again.';
      
      case ErrorCategory.VOICE_PROCESSING:
        return 'Voice processing failed. Please try recording again.';
      
      case ErrorCategory.STORAGE:
        return 'File operation failed. Please try again.';
      
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }

  private generateRecoveryActions(category: ErrorCategory): string[] {
    switch (category) {
      case ErrorCategory.NETWORK:
        return [
          'Check your internet connection',
          'Try again in a few moments',
          'Contact support if the problem persists'
        ];
      
      case ErrorCategory.DATABASE:
        return [
          'Try again',
          'Refresh the page',
          'Contact support if the problem persists'
        ];
      
      case ErrorCategory.AUTHENTICATION:
        return [
          'Log out and log back in',
          'Clear your browser cache',
          'Contact support if you continue to have issues'
        ];
      
      case ErrorCategory.VALIDATION:
        return [
          'Check the highlighted fields',
          'Ensure all required fields are filled',
          'Verify the format of your input'
        ];
      
      case ErrorCategory.VOICE_PROCESSING:
        return [
          'Speak clearly and try again',
          'Check your microphone settings',
          'Ensure you have a stable internet connection'
        ];
      
      case ErrorCategory.STORAGE:
        return [
          'Try again',
          'Check available storage space',
          'Verify file permissions'
        ];
      
      default:
        return [
          'Try again',
          'Refresh the page',
          'Contact support if the problem persists'
        ];
    }
  }

  private generateErrorCode(_error: Error, category: ErrorCategory): string {
    const categoryPrefix = category.toUpperCase();
    return `${categoryPrefix}_UNKNOWN`;
  }
}
