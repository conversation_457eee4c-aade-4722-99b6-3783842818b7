/**
 * Form validation framework types and interfaces
 */

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings?: ValidationWarning[];
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
  value?: unknown;
}

export interface ValidationWarning {
  field: string;
  message: string;
  code: string;
  value?: unknown;
}

export interface ValidationRule<T = unknown> {
  name: string;
  validate: (value: T, context?: ValidationContext) => ValidationResult | Promise<ValidationResult>;
  message?: string;
  code?: string;
}

export interface ValidationContext {
  field: string;
  allValues: Record<string, unknown>;
  metadata?: Record<string, unknown>;
}

export interface FieldValidationConfig<T = unknown> {
  rules: ValidationRule<T>[];
  required?: boolean;
  transform?: (value: unknown) => T;
  dependencies?: string[];
}

export interface FormValidationSchema {
  [fieldName: string]: FieldValidationConfig;
}

export interface AsyncValidationRule<T = unknown> extends ValidationRule<T> {
  validate: (value: T, context?: ValidationContext) => Promise<ValidationResult>;
  debounceMs?: number;
}

export interface ValidationOptions {
  abortEarly?: boolean;
  includeWarnings?: boolean;
  validateDependencies?: boolean;
  skipAsyncValidation?: boolean;
}

export type ValidatorFunction<T = unknown> = (
  value: T,
  context?: ValidationContext
) => ValidationResult | Promise<ValidationResult>;

export interface ConditionalValidationRule<T = unknown> extends ValidationRule<T> {
  condition: (context: ValidationContext) => boolean;
}

export interface CrossFieldValidationRule {
  name: string;
  fields: string[];
  validate: (values: Record<string, unknown>, context?: ValidationContext) => ValidationResult;
  message?: string;
  code?: string;
}
