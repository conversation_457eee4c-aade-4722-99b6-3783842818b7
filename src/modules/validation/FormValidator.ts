import {
  ValidationResult,
  ValidationError,
  ValidationWarning,
  ValidationRule,
  ValidationContext,
  FieldValidationConfig,
  FormValidationSchema,
  ValidationOptions,
  CrossFieldValidationRule
} from './types';

/**
 * Comprehensive form validation system
 */
export class FormValidator {
  private schema: FormValidationSchema;
  private crossFieldRules: CrossFieldValidationRule[] = [];
  private asyncValidationCache = new Map<string, { result: ValidationResult; timestamp: number }>();
  private readonly CACHE_TTL = 5000; // 5 seconds

  constructor(schema: FormValidationSchema) {
    this.schema = schema;
  }

  /**
   * Validate entire form
   */
  async validateForm(
    values: Record<string, unknown>,
    options: ValidationOptions = {}
  ): Promise<ValidationResult> {
    const {
      abortEarly = false,
      includeWarnings = true,
      validateDependencies = true,
      skipAsyncValidation = false
    } = options;

    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // Validate individual fields
    for (const [fieldName, config] of Object.entries(this.schema)) {
      const fieldValue = values[fieldName];
      const context: ValidationContext = {
        field: fieldName,
        allValues: values
      };

      try {
        const fieldResult = await this.validateField(
          fieldName,
          fieldValue,
          context,
          { skipAsyncValidation }
        );

        errors.push(...fieldResult.errors);
        if (includeWarnings && fieldResult.warnings) {
          warnings.push(...fieldResult.warnings);
        }

        if (abortEarly && fieldResult.errors.length > 0) {
          break;
        }
      } catch (error) {
        errors.push({
          field: fieldName,
          message: 'Validation error occurred',
          code: 'VALIDATION_EXCEPTION',
          value: fieldValue
        });
      }
    }

    // Validate cross-field rules
    if (!abortEarly || errors.length === 0) {
      for (const rule of this.crossFieldRules) {
        try {
          const ruleResult = rule.validate(values);
          errors.push(...ruleResult.errors);
          if (includeWarnings && ruleResult.warnings) {
            warnings.push(...ruleResult.warnings);
          }
        } catch (error) {
          errors.push({
            field: rule.fields.join(','),
            message: rule.message || 'Cross-field validation failed',
            code: rule.code || 'CROSS_FIELD_ERROR'
          });
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings: includeWarnings ? warnings : undefined
    };
  }

  /**
   * Validate single field
   */
  async validateField(
    fieldName: string,
    value: unknown,
    context?: ValidationContext,
    options: { skipAsyncValidation?: boolean } = {}
  ): Promise<ValidationResult> {
    const config = this.schema[fieldName];
    if (!config) {
      return { isValid: true, errors: [] };
    }

    const fieldContext: ValidationContext = context || {
      field: fieldName,
      allValues: { [fieldName]: value }
    };

    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // Transform value if transformer is provided
    let transformedValue = value;
    if (config.transform) {
      try {
        transformedValue = config.transform(value);
      } catch (error) {
        errors.push({
          field: fieldName,
          message: 'Invalid value format',
          code: 'TRANSFORM_ERROR',
          value
        });
        return { isValid: false, errors };
      }
    }

    // Check required validation
    if (config.required && this.isEmpty(transformedValue)) {
      errors.push({
        field: fieldName,
        message: `${fieldName} is required`,
        code: 'REQUIRED',
        value: transformedValue
      });
      return { isValid: false, errors };
    }

    // Skip other validations if value is empty and not required
    if (this.isEmpty(transformedValue) && !config.required) {
      return { isValid: true, errors: [] };
    }

    // Run validation rules
    for (const rule of config.rules) {
      try {
        const ruleResult = await this.executeRule(
          rule,
          transformedValue,
          fieldContext,
          options.skipAsyncValidation
        );

        errors.push(...ruleResult.errors);
        if (ruleResult.warnings) {
          warnings.push(...ruleResult.warnings);
        }

        // Stop on first error if rule is critical
        if (ruleResult.errors.length > 0 && rule.name.includes('critical')) {
          break;
        }
      } catch (error) {
        errors.push({
          field: fieldName,
          message: rule.message || 'Validation rule failed',
          code: rule.code || 'RULE_ERROR',
          value: transformedValue
        });
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings: warnings.length > 0 ? warnings : undefined
    };
  }

  /**
   * Add cross-field validation rule
   */
  addCrossFieldRule(rule: CrossFieldValidationRule): void {
    this.crossFieldRules.push(rule);
  }

  /**
   * Update field configuration
   */
  updateFieldConfig(fieldName: string, config: FieldValidationConfig): void {
    this.schema[fieldName] = config;
  }

  /**
   * Remove field from schema
   */
  removeField(fieldName: string): void {
    delete this.schema[fieldName];
  }

  /**
   * Get field configuration
   */
  getFieldConfig(fieldName: string): FieldValidationConfig | undefined {
    return this.schema[fieldName];
  }

  /**
   * Clear validation cache
   */
  clearCache(): void {
    this.asyncValidationCache.clear();
  }

  /**
   * Execute validation rule with caching for async rules
   */
  private async executeRule(
    rule: ValidationRule,
    value: unknown,
    context: ValidationContext,
    skipAsync = false
  ): Promise<ValidationResult> {
    // Check if it's an async rule
    const isAsync = rule.validate.constructor.name === 'AsyncFunction';
    
    if (isAsync && skipAsync) {
      return { isValid: true, errors: [] };
    }

    // Check cache for async rules
    if (isAsync) {
      const cacheKey = `${context.field}:${rule.name}:${JSON.stringify(value)}`;
      const cached = this.asyncValidationCache.get(cacheKey);
      
      if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
        return cached.result;
      }

      const result = await rule.validate(value, context);
      this.asyncValidationCache.set(cacheKey, {
        result,
        timestamp: Date.now()
      });
      
      return result;
    }

    // Execute synchronous rule
    return rule.validate(value, context);
  }

  /**
   * Check if value is empty
   */
  private isEmpty(value: unknown): boolean {
    if (value === null || value === undefined) return true;
    if (typeof value === 'string') return value.trim() === '';
    if (Array.isArray(value)) return value.length === 0;
    if (typeof value === 'object') return Object.keys(value).length === 0;
    return false;
  }

  /**
   * Get validation statistics
   */
  getStats(): {
    totalFields: number;
    totalRules: number;
    crossFieldRules: number;
    cacheSize: number;
  } {
    const totalRules = Object.values(this.schema)
      .reduce((sum, config) => sum + config.rules.length, 0);

    return {
      totalFields: Object.keys(this.schema).length,
      totalRules,
      crossFieldRules: this.crossFieldRules.length,
      cacheSize: this.asyncValidationCache.size
    };
  }
}
