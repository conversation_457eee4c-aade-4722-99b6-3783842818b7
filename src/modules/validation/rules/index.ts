/**
 * Built-in validation rules
 */

import { ValidationRule, ValidationResult, ValidationContext } from '../types';

// String validation rules
export const required: ValidationRule<unknown> = {
  name: 'required',
  code: 'REQUIRED',
  validate: (value) => ({
    isValid: value !== null && value !== undefined && value !== '',
    errors: value === null || value === undefined || value === '' ? [{
      field: '',
      message: 'This field is required',
      code: 'REQUIRED',
      value
    }] : []
  })
};

export const minLength = (min: number): ValidationRule<string> => ({
  name: 'minLength',
  code: 'MIN_LENGTH',
  validate: (value) => {
    const length = value?.length || 0;
    return {
      isValid: length >= min,
      errors: length < min ? [{
        field: '',
        message: `Must be at least ${min} characters long`,
        code: 'MIN_LENGTH',
        value
      }] : []
    };
  }
});

export const maxLength = (max: number): ValidationRule<string> => ({
  name: 'maxLength',
  code: 'MAX_LENGTH',
  validate: (value) => {
    const length = value?.length || 0;
    return {
      isValid: length <= max,
      errors: length > max ? [{
        field: '',
        message: `Must be no more than ${max} characters long`,
        code: 'MAX_LENGTH',
        value
      }] : []
    };
  }
});

export const pattern = (regex: RegExp, message?: string): ValidationRule<string> => ({
  name: 'pattern',
  code: 'PATTERN',
  validate: (value) => {
    const isValid = !value || regex.test(value);
    return {
      isValid,
      errors: !isValid ? [{
        field: '',
        message: message || 'Invalid format',
        code: 'PATTERN',
        value
      }] : []
    };
  }
});

export const email: ValidationRule<string> = {
  name: 'email',
  code: 'EMAIL',
  validate: (value) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const isValid = !value || emailRegex.test(value);
    return {
      isValid,
      errors: !isValid ? [{
        field: '',
        message: 'Please enter a valid email address',
        code: 'EMAIL',
        value
      }] : []
    };
  }
};

// Number validation rules
export const min = (minimum: number): ValidationRule<number> => ({
  name: 'min',
  code: 'MIN',
  validate: (value) => {
    const numValue = Number(value);
    const isValid = isNaN(numValue) || numValue >= minimum;
    return {
      isValid,
      errors: !isValid ? [{
        field: '',
        message: `Must be at least ${minimum}`,
        code: 'MIN',
        value
      }] : []
    };
  }
});

export const max = (maximum: number): ValidationRule<number> => ({
  name: 'max',
  code: 'MAX',
  validate: (value) => {
    const numValue = Number(value);
    const isValid = isNaN(numValue) || numValue <= maximum;
    return {
      isValid,
      errors: !isValid ? [{
        field: '',
        message: `Must be no more than ${maximum}`,
        code: 'MAX',
        value
      }] : []
    };
  }
});

export const integer: ValidationRule<number> = {
  name: 'integer',
  code: 'INTEGER',
  validate: (value) => {
    const numValue = Number(value);
    const isValid = isNaN(numValue) || Number.isInteger(numValue);
    return {
      isValid,
      errors: !isValid ? [{
        field: '',
        message: 'Must be a whole number',
        code: 'INTEGER',
        value
      }] : []
    };
  }
};

export const positive: ValidationRule<number> = {
  name: 'positive',
  code: 'POSITIVE',
  validate: (value) => {
    const numValue = Number(value);
    const isValid = isNaN(numValue) || numValue > 0;
    return {
      isValid,
      errors: !isValid ? [{
        field: '',
        message: 'Must be a positive number',
        code: 'POSITIVE',
        value
      }] : []
    };
  }
};

// Date validation rules
export const dateFormat = (format: string = 'YYYY-MM-DD'): ValidationRule<string> => ({
  name: 'dateFormat',
  code: 'DATE_FORMAT',
  validate: (value) => {
    if (!value) return { isValid: true, errors: [] };
    
    const date = new Date(value);
    const isValid = !isNaN(date.getTime());
    return {
      isValid,
      errors: !isValid ? [{
        field: '',
        message: `Please enter a valid date in ${format} format`,
        code: 'DATE_FORMAT',
        value
      }] : []
    };
  }
});

export const futureDate: ValidationRule<string> = {
  name: 'futureDate',
  code: 'FUTURE_DATE',
  validate: (value) => {
    if (!value) return { isValid: true, errors: [] };
    
    const date = new Date(value);
    const now = new Date();
    const isValid = date > now;
    return {
      isValid,
      errors: !isValid ? [{
        field: '',
        message: 'Date must be in the future',
        code: 'FUTURE_DATE',
        value
      }] : []
    };
  }
};

export const pastDate: ValidationRule<string> = {
  name: 'pastDate',
  code: 'PAST_DATE',
  validate: (value) => {
    if (!value) return { isValid: true, errors: [] };
    
    const date = new Date(value);
    const now = new Date();
    const isValid = date < now;
    return {
      isValid,
      errors: !isValid ? [{
        field: '',
        message: 'Date must be in the past',
        code: 'PAST_DATE',
        value
      }] : []
    };
  }
};

// Array validation rules
export const minItems = (min: number): ValidationRule<unknown[]> => ({
  name: 'minItems',
  code: 'MIN_ITEMS',
  validate: (value) => {
    const length = Array.isArray(value) ? value.length : 0;
    return {
      isValid: length >= min,
      errors: length < min ? [{
        field: '',
        message: `Must have at least ${min} items`,
        code: 'MIN_ITEMS',
        value
      }] : []
    };
  }
});

export const maxItems = (max: number): ValidationRule<unknown[]> => ({
  name: 'maxItems',
  code: 'MAX_ITEMS',
  validate: (value) => {
    const length = Array.isArray(value) ? value.length : 0;
    return {
      isValid: length <= max,
      errors: length > max ? [{
        field: '',
        message: `Must have no more than ${max} items`,
        code: 'MAX_ITEMS',
        value
      }] : []
    };
  }
});

// Custom validation rules for voice events
export const confidenceScore: ValidationRule<number> = {
  name: 'confidenceScore',
  code: 'CONFIDENCE_SCORE',
  validate: (value) => {
    const numValue = Number(value);
    const isValid = !isNaN(numValue) && numValue >= 0 && numValue <= 1;
    return {
      isValid,
      errors: !isValid ? [{
        field: '',
        message: 'Confidence score must be between 0 and 1',
        code: 'CONFIDENCE_SCORE',
        value
      }] : []
    };
  }
};

export const productName: ValidationRule<string> = {
  name: 'productName',
  code: 'PRODUCT_NAME',
  validate: (value) => {
    if (!value) return { isValid: true, errors: [] };
    
    const trimmed = value.trim();
    const isValid = trimmed.length >= 2 && trimmed.length <= 100;
    return {
      isValid,
      errors: !isValid ? [{
        field: '',
        message: 'Product name must be between 2 and 100 characters',
        code: 'PRODUCT_NAME',
        value
      }] : []
    };
  }
};

export const quantity: ValidationRule<number> = {
  name: 'quantity',
  code: 'QUANTITY',
  validate: (value) => {
    const numValue = Number(value);
    const isValid = !isNaN(numValue) && numValue > 0 && numValue <= 999999;
    return {
      isValid,
      errors: !isValid ? [{
        field: '',
        message: 'Quantity must be a positive number up to 999,999',
        code: 'QUANTITY',
        value
      }] : []
    };
  }
};

export const temperature: ValidationRule<number> = {
  name: 'temperature',
  code: 'TEMPERATURE',
  validate: (value) => {
    if (value === null || value === undefined) return { isValid: true, errors: [] };
    
    const numValue = Number(value);
    const isValid = !isNaN(numValue) && numValue >= -50 && numValue <= 200;
    return {
      isValid,
      errors: !isValid ? [{
        field: '',
        message: 'Temperature must be between -50 and 200 degrees',
        code: 'TEMPERATURE',
        value
      }] : []
    };
  }
};
