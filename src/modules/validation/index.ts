/**
 * Form Validation Framework (modularized)
 * - Schema-based validation
 * - Built-in and custom rules
 * - Cross-field validation
 * - Async validation with caching
 * - Transform and sanitization
 */

export * from './types';
export { FormValidator } from './FormValidator';
export * as rules from './rules';

import { FormValidator } from './FormValidator';
import type { FormValidationSchema, FieldValidationConfig, ValidationContext } from './types';
import * as rules from './rules';

export const createValidator = (schema: FormValidationSchema) => {
  return new FormValidator(schema);
};

export const validateField = async (
  fieldName: string,
  value: unknown,
  config: FieldValidationConfig,
  context?: ValidationContext
) => {
  const validator = new FormValidator({ [fieldName]: config });
  return validator.validateField(fieldName, value, context);
};

export const commonSchemas = {
  voiceEvent: {
    event_type: {
      rules: [rules.required],
      required: true
    },
    product_name: {
      rules: [rules.required, rules.productName],
      required: true,
      transform: (value: unknown) => String(value).trim()
    },
    quantity: {
      rules: [rules.required, rules.quantity, rules.positive],
      required: true,
      transform: (value: unknown) => Number(value)
    },
    unit: {
      rules: [rules.required],
      required: true
    },
    voice_confidence_score: {
      rules: [rules.confidenceScore],
      required: false,
      transform: (value: unknown) => Number(value)
    },
    temperature: {
      rules: [rules.temperature],
      required: false,
      transform: (value: unknown) => value === '' ? undefined : Number(value)
    },
    occurred_at: {
      rules: [rules.required, rules.dateFormat()],
      required: true
    }
  },
  
  user: {
    email: {
      rules: [rules.required, rules.email],
      required: true,
      transform: (value: unknown) => String(value).toLowerCase().trim()
    },
    password: {
      rules: [rules.required, rules.minLength(8)],
      required: true
    },
    name: {
      rules: [rules.required, rules.minLength(2), rules.maxLength(50)],
      required: true,
      transform: (value: unknown) => String(value).trim()
    }
  }
};
