# Source Code Directory

This directory contains the main application source code for the Pacific Cloud Seafoods Manager - a comprehensive seafood inventory management and compliance tracking system.

## Overview

The Pacific Cloud Seafoods Manager is a React-based web application built with TypeScript, Vite, and Supabase. It provides real-time inventory tracking, HACCP compliance monitoring, voice-enabled data entry, and comprehensive traceability for seafood operations.

## Directory Structure

### `/components` - UI Components
React components organized by feature and functionality. Contains the main user interface elements including forms, tables, modals, and specialized components for voice interaction, compliance tracking, and inventory management.

### `/contexts` - React Contexts
Application-wide state management using React Context API for navigation and other shared state.

### `/data` - Static Data
Mock data and static datasets used for development and testing.

### `/hooks` - Custom React Hooks
Reusable React hooks for voice commands, real-time processing, and other application-specific functionality.

### `/lib` - Core Libraries
Core business logic, utilities, and integrations including:
- Database setup and configuration
- API integrations
- Voice processing
- Data validation
- Performance monitoring
- Security utilities

### `/services` - Business Services
Service layer containing business logic for voice event management, audio storage, and data processing.

### `/test` - Testing Infrastructure
Testing utilities, mocks, and configuration for unit and integration tests.

### `/types` - TypeScript Definitions
Type definitions and interfaces for the application, including database schema types and API contracts.

### `/utils` - Utility Functions
Helper functions and utilities for lazy loading, data processing, and other common operations.

### `/workers` - Web Workers
Background processing workers for CPU-intensive tasks like CSV processing.

## Key Files

- `App.tsx` - Main application component and routing
- `main.tsx` - Application entry point
- `index.css` - Global styles and Tailwind CSS imports
- `types.ts` - Legacy type definitions (being migrated to `/types` directory)

## Technology Stack

- **Frontend**: React 18 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS with Radix UI components
- **Database**: Supabase (PostgreSQL)
- **Voice Processing**: OpenAI Whisper API
- **State Management**: React Context + React Hook Form
- **Testing**: Vitest + React Testing Library + Playwright

## Integration Points

### Database Layer
- Connects to Supabase PostgreSQL database
- Uses typed queries with generated types from `/types/schema.ts`
- Implements Row Level Security (RLS) for data access control

### Voice Processing
- Integrates with OpenAI Whisper for speech-to-text
- Real-time voice command processing
- Audio storage and playback capabilities

### Compliance Systems
- HACCP monitoring and reporting
- Traceability tracking per GDST standards
- Vendor performance monitoring

### External APIs
- Square payment processing
- Vendor API integrations
- Export/import data processing

## Development Workflow

1. **Component Development**: Create components in appropriate feature directories
2. **Type Safety**: Define types in `/types` directory and import as needed
3. **Business Logic**: Implement in `/services` or `/lib` depending on complexity
4. **Testing**: Add tests in corresponding `__tests__` directories
5. **Integration**: Connect components through contexts and hooks

## Performance Considerations

- Lazy loading implemented for large components
- Web workers for CPU-intensive operations
- Optimized bundle splitting with Vite
- Performance monitoring and budgets in place

## Security Features

- Row Level Security (RLS) policies
- Input validation with Zod schemas
- Secure API endpoints
- Authentication via Supabase Auth