// Advanced Lazy Loading Configuration for Bundle Size Optimization
import { lazy, ComponentType, LazyExoticComponent } from 'react';

/**
 * Performance-optimized lazy loading utility with error boundaries and loading states
 */

// Error retry configuration
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000;

/**
 * Enhanced lazy loading with retry mechanism and preloading
 */
export function createLazyComponent<T extends ComponentType>(
  importFn: () => Promise<{ default: T }>,
  options: {
    retries?: number;
    retryDelay?: number;
    preload?: boolean;
    priority?: 'high' | 'medium' | 'low';
  } = {}
): LazyExoticComponent<T> & { preload: () => Promise<void> } {
  const { retries = MAX_RETRIES, retryDelay = RETRY_DELAY, preload = false } = options;

  let componentPromise: Promise<{ default: T }> | null = null;

  const loadComponent = async (attempt = 0): Promise<{ default: T }> => {
    try {
      const component = await importFn();
      return component;
    } catch (error) {
      if (attempt < retries) {
        console.warn(`Lazy load failed, retrying (${attempt + 1}/${retries})...`, error);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        return loadComponent(attempt + 1);
      }
      throw error;
    }
  };

  const getComponentPromise = () => {
    if (!componentPromise) {
      componentPromise = loadComponent();
    }
    return componentPromise;
  };

  const LazyComponent = lazy(() => getComponentPromise()) as LazyExoticComponent<T> & {
    preload: () => Promise<void>;
  };

  // Add preload method
  LazyComponent.preload = async () => {
    await getComponentPromise();
  };

  // Auto-preload if requested
  if (preload) {
    // Preload after a small delay to not block initial render
    setTimeout(() => LazyComponent.preload(), 100);
  }

  return LazyComponent;
}

// ===== HIGH PRIORITY COMPONENTS (Core Application) =====
// Dashboard Components - Load immediately
export const Dashboard = createLazyComponent(
  () => import('../components/Dashboard'),
  { priority: 'high', preload: true }
);

// Core Inventory Management - Critical for main workflows
export const Inventory = createLazyComponent(
  () => import('../components/Inventory'),
  { priority: 'high', preload: true }
);

export const Products = createLazyComponent(
  () => import('../components/Products'),
  { priority: 'high', preload: true }
);

// ===== MEDIUM PRIORITY COMPONENTS (Common Features) =====
// HACCP & Compliance - Frequently used
export const HACCPCalendar = createLazyComponent(
  () => import('../components/HACCPCalendar'),
  { priority: 'medium' }
);

export const HACCPEventsView = createLazyComponent(
  () => import('../components/HACCPEventsView'),
  { priority: 'medium' }
);

// Core Voice Features (optimize bundle)
export const VoiceInventory = createLazyComponent(
  () => import('../components/voice/VoiceInventory'),
  { priority: 'medium' }
);

export const VoiceInput = createLazyComponent(
  () => import('../components/voice/VoiceInput'),
  { priority: 'medium' }
);

// Vendor Management
export const VendorsView = createLazyComponent(
  () => import('../components/vendors/VendorsView'),
  { priority: 'medium' }
);

export const VendorDashboard = createLazyComponent(
  () => import('../components/vendors/VendorDashboard'),
  { priority: 'medium' }
);

// Customer Management
export const CustomersView = createLazyComponent(
  () => import('../components/customers/CustomersView'),
  { priority: 'medium' }
);

// Product Details and Categories
export const ProductDetails = createLazyComponent(
  () => import('../components/ProductDetails'),
  { priority: 'medium' }
);

export const CategoryView = createLazyComponent(
  () => import('../components/CategoryView'),
  { priority: 'medium' }
);

// Batch Tracking
export const BatchTracking = createLazyComponent(
  () => import('../components/BatchTracking'),
  { priority: 'medium' }
);

// ===== LOW PRIORITY COMPONENTS (Heavy/Specialized Features) =====
// Data Import/Export - Heavy processing, load on demand
export const ImportWizard = createLazyComponent(
  () => import('../components/import/ImportWizard'),
  { priority: 'low' }
);

export const ImportInventory = createLazyComponent(
  () => import('../components/import/ImportInventory'),
  { priority: 'low' }
);

export const ImportProgressDashboard = createLazyComponent(
  () => import('../components/import/ImportProgressDashboard'),
  { priority: 'low' }
);

// Analytics & Reports - Heavy charts library
export const Analytics = createLazyComponent(
  () => import('../components/Analytics'),
  { priority: 'low' }
);

// Settings & Configuration
export const Settings = createLazyComponent(
  () => import('../components/Settings'),
  { priority: 'low' }
);

// ===== ADVANCED VOICE COMPONENTS (Heavy OpenAI bundle) =====
export const VoiceAssistant = createLazyComponent(
  () => import('../components/voice/VoiceAssistant'),
  { priority: 'low' }
);

export const EnhancedVoiceAssistant = createLazyComponent(
  () => import('../components/voice/EnhancedVoiceAssistant'),
  { priority: 'low' }
);

export const VoiceProcessingDemo = createLazyComponent(
  () => import('../components/voice/VoiceProcessingDemo'),
  { priority: 'low' }
);

export const VoiceTestPage = createLazyComponent(
  () => import('../components/voice/VoiceTestPage'),
  { priority: 'low' }
);

export const ConversationalVoiceInterface = createLazyComponent(
  () => import('../components/voice/ConversationalVoiceInterface'),
  { priority: 'low' }
);

export const EnterpriseVoiceManager = createLazyComponent(
  () => import('../components/voice/EnterpriseVoiceManager'),
  { priority: 'low' }
);

export const VoicePerformanceDashboard = createLazyComponent(
  () => import('../components/voice/VoicePerformanceDashboard'),
  { priority: 'low' }
);

// ===== COMPLIANCE & MONITORING (Heavy features) =====
export const ComplianceDashboard = createLazyComponent(
  () => import('../components/compliance/ComplianceDashboard'),
  { priority: 'low' }
);

export const EnhancedComplianceDashboard = createLazyComponent(
  () => import('../components/compliance/EnhancedComplianceDashboard'),
  { priority: 'low' }
);

export const RealTimeMonitoringDashboard = createLazyComponent(
  () => import('../components/compliance/RealTimeMonitoringDashboard'),
  { priority: 'low' }
);

export const TraceabilityChain = createLazyComponent(
  () => import('../components/compliance/TraceabilityChain'),
  { priority: 'low' }
);

// ===== PARTNER MANAGEMENT (Low usage) =====
export const PartnersView = createLazyComponent(
  () => import('../components/partners/PartnersView'),
  { priority: 'low' }
);

export const PartnerView = createLazyComponent(
  () => import('../components/partners/PartnerView'),
  { priority: 'low' }
);

// ===== SPECIALIZED COMPONENTS =====
export const BatchNumberGenerator = createLazyComponent(
  () => import('../components/batch/BatchNumberGenerator'),
  { priority: 'low' }
);

export const ImportCustomers = createLazyComponent(
  () => import('../components/customers/ImportCustomers'),
  { priority: 'low' }
);

export const VendorReportCard = createLazyComponent(
  () => import('../components/vendors/VendorReportCard'),
  { priority: 'low' }
);

// ===== PAGE COMPONENTS =====
export const VoiceEventManagement = createLazyComponent(
  () => import('../pages/VoiceEventManagement'),
  { priority: 'low' }
);

export const RealtimeAssistantDemo = createLazyComponent(
  () => import('../pages/RealtimeAssistantDemo'),
  { priority: 'low' }
);

/**
 * Intelligent preloading based on route patterns and user behavior
 */
export const preloadStrategies = {
  // Immediate preload for critical components
  critical: () => {
    console.log('🚀 Preloading critical components...');
    return Promise.allSettled([
      Dashboard.preload(),
      Inventory.preload(),
      Products.preload(),
    ]);
  },

  // Route-based preloading
  routes: {
    '/dashboard': () => Promise.allSettled([
      Inventory.preload(),
      Analytics.preload(),
    ]),
    
    '/inventory': () => Promise.allSettled([
      VoiceInventory.preload(),
      ImportInventory.preload(),
      ProductDetails.preload(),
    ]),
    
    '/products': () => Promise.allSettled([
      ProductDetails.preload(),
      CategoryView.preload(),
    ]),
    
    '/import': () => Promise.allSettled([
      ImportWizard.preload(),
      ImportProgressDashboard.preload(),
    ]),
    
    '/voice': () => Promise.allSettled([
      VoiceInput.preload(),
      VoiceAssistant.preload(),
    ]),
    
    '/vendors': () => Promise.allSettled([
      VendorDashboard.preload(),
      VendorReportCard.preload(),
    ]),
    
    '/compliance': () => Promise.allSettled([
      HACCPCalendar.preload(),
      ComplianceDashboard.preload(),
    ]),
    
    '/haccp': () => Promise.allSettled([
      HACCPEventsView.preload(),
      RealTimeMonitoringDashboard.preload(),
    ]),
  },

  // User behavior patterns
  userFlow: (currentRoute: string, _previousRoutes: string[] = []) => {
    const patterns: Record<string, () => Promise<unknown>[]> = {
      '/dashboard': () => [
        // Users often go from dashboard to inventory
        Inventory.preload(),
        Products.preload(),
      ],
      
      '/inventory': () => [
        // From inventory, users often add items or use voice
        VoiceInventory.preload(),
        ImportInventory.preload(),
      ],
      
      '/voice': () => [
        // Voice users often need advanced features
        VoiceAssistant.preload(),
        ConversationalVoiceInterface.preload(),
      ],
    };

    const preloadFn = patterns[currentRoute];
    return preloadFn ? Promise.allSettled(preloadFn()) : Promise.resolve();
  },

  // Idle time preloading
  idle: () => {
    console.log('💤 Idle preloading secondary components...');
    return Promise.allSettled([
      Settings.preload(),
      Analytics.preload(),
      CustomersView.preload(),
    ]);
  },
};

/**
 * Smart preloading manager
 */
class PreloadManager {
  private preloadedComponents = new Set<string>();
  private idleTimeout: NodeJS.Timeout | null = null;

  constructor() {
    this.setupIdlePreloading();
  }

  private setupIdlePreloading() {
    // Preload during idle time
    if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
      window.requestIdleCallback(() => {
        this.scheduleIdlePreload();
      });
    } else {
      // Fallback for browsers without requestIdleCallback
      setTimeout(() => this.scheduleIdlePreload(), 5000);
    }
  }

  private scheduleIdlePreload() {
    this.idleTimeout = setTimeout(() => {
      preloadStrategies.idle();
    }, 3000);
  }

  resetIdleTimer() {
    if (this.idleTimeout) {
      clearTimeout(this.idleTimeout);
      this.scheduleIdlePreload();
    }
  }

  async preloadForRoute(route: string, previousRoutes: string[] = []) {
    const routeKey = `route:${route}`;
    if (this.preloadedComponents.has(routeKey)) return;

    console.log(`🎯 Preloading for route: ${route}`);
    
    // Route-specific preloading
    const routePreload = preloadStrategies.routes[route as keyof typeof preloadStrategies.routes];
    if (routePreload) {
      await routePreload();
    }

    // User flow preloading
    await preloadStrategies.userFlow(route, previousRoutes);

    this.preloadedComponents.add(routeKey);
  }

  async preloadCritical() {
    if (this.preloadedComponents.has('critical')) return;
    
    await preloadStrategies.critical();
    this.preloadedComponents.add('critical');
  }
}

export const preloadManager = new PreloadManager();

// Initialize critical preloading
if (typeof window !== 'undefined') {
  // Preload critical components after initial render
  setTimeout(() => preloadManager.preloadCritical(), 1000);
}

/**
 * Legacy compatibility functions
 */
export const preloadCriticalComponents = preloadStrategies.critical;
export const preloadByUserFlow = (currentView: string) => 
  preloadManager.preloadForRoute(`/${currentView}`);