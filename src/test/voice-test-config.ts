/**
 * Voice Testing Configuration
 * Centralized configuration for all voice-related tests
 */

export const VOICE_TEST_CONFIG = {
  // Test environment settings
  environment: {
    testTimeout: 30000,           // 30 seconds for voice tests
    retryAttempts: 3,             // Retry failed tests 3 times
    parallelTests: 2,             // Run 2 voice tests in parallel
    setupTimeout: 10000,          // 10 seconds for test setup
    teardownTimeout: 5000         // 5 seconds for cleanup
  },

  // Performance thresholds
  performance: {
    maxProcessingTime: 2000,      // 2 seconds max processing
    maxTranscriptionTime: 1000,   // 1 second max transcription
    maxDatabaseWriteTime: 200,    // 200ms max database write
    maxCacheRetrievalTime: 10,    // 10ms max cache retrieval
    maxConcurrentUsers: 10        // Test up to 10 concurrent users
  },

  // Accuracy thresholds
  accuracy: {
    minProductRecognition: 0.85,   // 85% product recognition
    minQuantityExtraction: 0.80,   // 80% quantity extraction
    minVendorRecognition: 0.75,    // 75% vendor recognition
    minEventClassification: 0.90,  // 90% event type classification
    minOverallConfidence: 0.75,    // 75% average confidence
    maxRegressionTolerance: 0.05   // 5% max accuracy degradation
  },

  // Test data configuration
  testData: {
    regressionSamples: 50,         // Number of regression test samples
    accentVariations: 8,           // Number of accent types to test
    environmentalConditions: 5,    // Number of noise conditions
    edgeCases: 15,                 // Number of edge case scenarios
    performanceBenchmarks: 20      // Number of performance test iterations
  },

  // Browser compatibility settings
  browsers: {
    targets: ['chromium', 'firefox', 'webkit'],
    viewports: [
      { width: 1920, height: 1080, name: 'desktop' },
      { width: 768, height: 1024, name: 'tablet' },
      { width: 375, height: 667, name: 'mobile' }
    ],
    permissions: ['microphone'],
    features: [
      'speechRecognition',
      'mediaRecorder', 
      'getUserMedia',
      'webAudio',
      'speechSynthesis'
    ]
  },

  // Mock API configuration
  mocks: {
    openAI: {
      defaultLatency: 500,         // 500ms default API latency
      errorRate: 0.02,             // 2% error rate simulation
      confidenceVariation: 0.1,   // ±10% confidence variation
      transcriptionAccuracy: 0.95  // 95% transcription accuracy
    },
    supabase: {
      queryLatency: 50,            // 50ms query latency
      writeLatency: 100,           // 100ms write latency
      errorRate: 0.01,             // 1% database error rate
      connectionPoolSize: 10       // Mock connection pool
    },
    browserAPIs: {
      microphoneDelay: 100,        // 100ms microphone access delay
      audioRecordingLatency: 200,  // 200ms recording startup
      speechSynthesisDelay: 300    // 300ms speech synthesis delay
    }
  },

  // Test categories and their weights
  categories: {
    unit: {
      weight: 0.25,
      includes: ['transcription-accuracy', 'voice-processing']
    },
    integration: {
      weight: 0.30,
      includes: ['database-workflow', 'real-time-updates']
    },
    performance: {
      weight: 0.20,
      includes: ['latency', 'concurrency', 'memory-usage']
    },
    userAcceptance: {
      weight: 0.15,
      includes: ['accents', 'edge-cases', 'accessibility']
    },
    regression: {
      weight: 0.10,
      includes: ['accuracy-maintenance', 'consistency']
    }
  },

  // Reporting configuration
  reporting: {
    formats: ['json', 'html', 'junit'],
    includeCoverage: true,
    includePerformanceMetrics: true,
    includeScreenshots: true,
    generateTrends: true,
    alertThresholds: {
      failureRate: 0.05,           // Alert if >5% tests fail
      performanceDegradation: 0.20, // Alert if >20% slower
      accuracyDrop: 0.03           // Alert if >3% accuracy drop
    }
  },

  // CI/CD integration settings
  cicd: {
    runOnPullRequest: true,
    runOnMerge: true,
    runNightly: true,
    abortOnFailure: false,        // Continue other tests if voice tests fail
    notificationChannels: ['slack', 'email'],
    artifactRetention: '30d'      // Keep test artifacts for 30 days
  }
};

// Test suite configurations
export const TEST_SUITES = {
  quick: {
    name: 'Quick Voice Test Suite',
    description: 'Fast smoke tests for voice functionality',
    timeout: 5000,
    tests: [
      'voice-transcription-accuracy.test.ts',
      'voice-basic-integration.test.ts'
    ],
    runConditions: ['pre-commit', 'pre-push']
  },

  comprehensive: {
    name: 'Comprehensive Voice Test Suite', 
    description: 'Full test coverage including performance and edge cases',
    timeout: 30000,
    tests: [
      'voice-transcription-accuracy.test.ts',
      'voice-database-workflow.test.ts',
      'voice-performance.test.ts',
      'voice-user-acceptance.test.ts',
      'voice-regression.test.ts'
    ],
    runConditions: ['nightly', 'release']
  },

  performance: {
    name: 'Voice Performance Test Suite',
    description: 'Performance and load testing for voice features',
    timeout: 60000,
    tests: [
      'voice-performance.test.ts'
    ],
    runConditions: ['performance-testing', 'pre-release']
  },

  crossBrowser: {
    name: 'Cross-Browser Compatibility Suite',
    description: 'Browser compatibility testing for voice features',
    timeout: 45000,
    tests: [
      'voice-browser-compatibility.spec.ts'
    ],
    runConditions: ['browser-testing', 'release']
  },

  regression: {
    name: 'Voice Regression Test Suite',
    description: 'Automated regression testing to detect accuracy degradation',
    timeout: 20000,
    tests: [
      'voice-regression.test.ts'
    ],
    runConditions: ['weekly', 'model-update']
  }
};

// Environment-specific overrides
export const ENVIRONMENT_OVERRIDES = {
  development: {
    testTimeout: 60000,           // Longer timeout for debugging
    retryAttempts: 1,             // No retries in dev
    parallelTests: 1,             // Sequential execution for debugging
    verboseLogging: true
  },

  ci: {
    testTimeout: 20000,           // Shorter timeout in CI
    retryAttempts: 3,             // Retry flaky tests
    parallelTests: 4,             // More parallel execution
    verboseLogging: false,
    headless: true
  },

  production: {
    testTimeout: 10000,           // Very short timeout
    retryAttempts: 0,             // No retries in prod
    parallelTests: 1,             // Conservative execution
    verboseLogging: false,
    monitoring: true
  }
};

// Test data generators
export function generateTestConfiguration(environment: string = 'development') {
  const baseConfig = { ...VOICE_TEST_CONFIG };
  const overrides = ENVIRONMENT_OVERRIDES[environment as keyof typeof ENVIRONMENT_OVERRIDES] || {};
  
  return {
    ...baseConfig,
    environment: {
      ...baseConfig.environment,
      ...overrides
    }
  };
}

// Test suite selector
export function selectTestSuite(trigger: string): typeof TEST_SUITES[keyof typeof TEST_SUITES] {
  for (const suite of Object.values(TEST_SUITES)) {
    if (suite.runConditions.includes(trigger)) {
      return suite;
    }
  }
  
  return TEST_SUITES.quick; // Default to quick suite
}

// Performance baseline tracking
export const PERFORMANCE_BASELINES = {
  transcriptionLatency: {
    target: 800,
    warning: 1200,
    critical: 2000
  },
  databaseWrite: {
    target: 100,
    warning: 200,
    critical: 500
  },
  overallProcessing: {
    target: 1500,
    warning: 2500,
    critical: 4000
  },
  memoryUsage: {
    target: 50, // MB
    warning: 100,
    critical: 200
  }
};

// Quality gates
export const QUALITY_GATES = {
  coverage: {
    statements: 80,
    branches: 75,
    functions: 85,
    lines: 80
  },
  performance: {
    maxRegressionPercent: 20,
    maxLatencyMs: 2000,
    minThroughput: 10 // requests per second
  },
  accuracy: {
    minProductRecognition: 85,
    minQuantityExtraction: 80,
    minOverallConfidence: 75
  },
  reliability: {
    maxFailureRate: 5, // percent
    minUptime: 99.5    // percent
  }
};