import '@testing-library/jest-dom';
import { vi } from 'vitest';
import { setupServer } from 'msw/node';
import { beforeAll, afterEach, afterAll, beforeEach } from 'vitest';
import { handlers } from './mocks/handlers';

// Setup MSW server for API mocking with default handlers
export const server = setupServer(...handlers);

beforeAll(() => {
  // Start the interception on the client side before any tests run
  server.listen({ 
    onUnhandledRequest: 'warn' // Changed from 'error' to 'warn' for better debugging
  });
  
  // Initialize performance monitoring
  if (typeof performance !== 'undefined') {
    performance.mark('test-setup-start');
  }
});

beforeEach(() => {
  // Reset mocks before each test to ensure clean state
  vi.clearAllMocks();
  
  // Clear any runtime handlers added during tests
  server.resetHandlers();
  
  // Reset local storage
  localStorage.clear();
  sessionStorage.clear();
  
  // Reset any global state
  if ((globalThis as any).mockDatabase) {
    (globalThis as any).mockDatabase.clear();
  }
});

afterEach(() => {
  // Clean up any timers or intervals
  vi.clearAllTimers();
  
  // Reset handlers to original state
  server.resetHandlers();
});

afterAll(() => {
  // Clean up MSW
  server.close();
  
  // Performance cleanup
  if (typeof performance !== 'undefined') {
    performance.mark('test-setup-end');
    performance.measure('test-setup-duration', 'test-setup-start', 'test-setup-end');
  }
});

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock fetch for API calls
global.fetch = vi.fn().mockImplementation(() =>
  Promise.resolve({
    ok: false,
    status: 500,
    statusText: 'Internal Server Error',
    json: () => Promise.resolve({ error: 'Mocked server error' }),
    text: () => Promise.resolve('Server Error'),
    headers: new Headers(),
  })
);

// Mock Web Speech API
global.SpeechRecognition = vi.fn().mockImplementation(() => ({
  start: vi.fn(),
  stop: vi.fn(),
  abort: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  continuous: false,
  interimResults: false,
  lang: 'en-US',
}));

global.webkitSpeechRecognition = global.SpeechRecognition;

// Mock File API - simplified for testing
class MockFile {
  name: string;
  size: number;
  type: string;
  lastModified: number;
  webkitRelativePath = '';
  
  constructor(chunks: BlobPart[], filename: string, options: { type?: string; lastModified?: number } = {}) {
    this.name = filename;
    this.size = chunks.reduce((acc, chunk) => {
      if (typeof chunk === 'string') return acc + chunk.length;
      if (chunk instanceof ArrayBuffer) return acc + chunk.byteLength;
      if (chunk instanceof Uint8Array) return acc + chunk.length;
      return acc;
    }, 0);
    this.type = options.type || '';
    this.lastModified = options.lastModified || Date.now();
  }
  
  // File methods
  stream(): ReadableStream<Uint8Array> { return new ReadableStream(); }
  text(): Promise<string> { return Promise.resolve(''); }
  arrayBuffer(): Promise<ArrayBuffer> { return Promise.resolve(new ArrayBuffer(0)); }
  slice(_start?: number, _end?: number, contentType?: string): Blob { 
    return new Blob([], { type: contentType }); 
  }
}

// Properly assign to global with type assertion
Object.defineProperty(globalThis, 'File', {
  writable: true,
  configurable: true,
  value: MockFile
});

// Mock FileReader - simplified for testing
class MockFileReader {
  static readonly EMPTY = 0;
  static readonly LOADING = 1;
  static readonly DONE = 2;

  readAsText = vi.fn();
  readAsArrayBuffer = vi.fn();
  readAsDataURL = vi.fn();
  readAsBinaryString = vi.fn();
  addEventListener = vi.fn();
  removeEventListener = vi.fn();
  dispatchEvent = vi.fn();
  abort = vi.fn();

  result: string | ArrayBuffer | null = null;
  error: DOMException | null = null;
  readyState = 0;
  
  onabort: any = null;
  onerror: any = null;
  onload: any = null;
  onloadend: any = null;
  onloadstart: any = null;
  onprogress: any = null;
}

// Properly assign to global with type assertion
Object.defineProperty(globalThis, 'FileReader', {
  writable: true,
  configurable: true,
  value: MockFileReader
});

// Mock URL.createObjectURL
global.URL.createObjectURL = vi.fn(() => 'mocked-url');
global.URL.revokeObjectURL = vi.fn();

// Environment variables for tests
process.env.VITE_SUPABASE_URL = 'https://test.supabase.co';
process.env.VITE_SUPABASE_ANON_KEY = 'test-anon-key';
process.env.VITE_OPENAI_API_KEY = 'test-openai-key';