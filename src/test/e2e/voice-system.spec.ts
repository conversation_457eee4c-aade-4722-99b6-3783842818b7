import { test, expect, Page } from '@playwright/test';

// Test configuration
const BASE_URL = process.env.PLAYWRIGHT_TEST_BASE_URL || 'http://localhost:3000';
const TEST_USER_EMAIL = process.env.TEST_USER_EMAIL || '<EMAIL>';
const TEST_USER_PASSWORD = process.env.TEST_USER_PASSWORD || 'testpassword123';

// Helper functions
async function loginUser(page: Page) {
  await page.goto(BASE_URL);
  
  // Wait for auth form
  await page.waitForSelector('[data-testid="auth-form"]', { timeout: 10000 });
  
  // Fill login form
  await page.fill('input[type="email"]', TEST_USER_EMAIL);
  await page.fill('input[type="password"]', TEST_USER_PASSWORD);
  await page.click('button[type="submit"]');
  
  // Wait for dashboard to load
  await page.waitForSelector('[data-testid="dashboard"]', { timeout: 15000 });
}

async function navigateToVoiceEvents(page: Page) {
  // Click on voice events in navigation
  await page.click('[data-testid="nav-voice-events"]');
  await page.waitForSelector('[data-testid="voice-events-list"]', { timeout: 5000 });
}

async function mockVoiceInput(page: Page, transcript: string) {
  // Mock the voice input by directly calling the processing function
  await page.evaluate((text) => {
    // Simulate voice processing result
    window.mockVoiceProcessingResult = {
      transcription: text,
      confidence_score: 0.92,
      processing_time: 1500,
      event_id: 'mock-event-' + Date.now()
    };
  }, transcript);
}

test.describe('Voice System End-to-End Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Set up test environment
    await page.setViewportSize({ width: 1280, height: 720 });
    
    // Mock media devices for voice recording
    await page.addInitScript(() => {
      // Mock MediaRecorder
      global.MediaRecorder = class MockMediaRecorder {
        constructor() {
          this.state = 'inactive';
          this.ondataavailable = null;
          this.onstop = null;
        }
        
        start() {
          this.state = 'recording';
          setTimeout(() => {
            if (this.ondataavailable) {
              this.ondataavailable({
                data: new Blob(['mock audio data'], { type: 'audio/webm' })
              });
            }
          }, 100);
        }
        
        stop() {
          this.state = 'inactive';
          if (this.onstop) {
            this.onstop();
          }
        }
        
        addEventListener(event, handler) {
          if (event === 'dataavailable') {
            this.ondataavailable = handler;
          } else if (event === 'stop') {
            this.onstop = handler;
          }
        }
      };
      
      // Mock getUserMedia
      navigator.mediaDevices = {
        getUserMedia: () => Promise.resolve({
          getTracks: () => [{ stop: () => {} }]
        })
      };
      
      // Mock Audio
      global.Audio = class MockAudio {
        constructor(src) {
          this.src = src;
        }
        
        play() {
          return Promise.resolve();
        }
        
        pause() {}
        load() {}
      };
    });
  });

  test('Complete Voice Event Creation Workflow', async ({ page }) => {
    await loginUser(page);
    
    // Navigate to voice events
    await navigateToVoiceEvents(page);
    
    // Start voice recording
    await page.click('[data-testid="start-voice-recording"]');
    
    // Wait for recording to start
    await page.waitForSelector('[data-testid="recording-indicator"]', { timeout: 3000 });
    expect(await page.isVisible('[data-testid="recording-indicator"]')).toBe(true);
    
    // Mock voice input
    await mockVoiceInput(page, 'Received fifty pounds of Atlantic salmon from Ocean Fresh Seafood');
    
    // Stop recording
    await page.click('[data-testid="stop-voice-recording"]');
    
    // Wait for processing
    await page.waitForSelector('[data-testid="processing-indicator"]', { timeout: 3000 });
    
    // Wait for event to appear in list
    await page.waitForSelector('[data-testid="voice-event-item"]', { timeout: 10000 });
    
    // Verify event details
    const eventItem = page.locator('[data-testid="voice-event-item"]').first();
    await expect(eventItem.locator('[data-testid="product-name"]')).toContainText('Atlantic salmon');
    await expect(eventItem.locator('[data-testid="quantity"]')).toContainText('50');
    await expect(eventItem.locator('[data-testid="confidence-score"]')).toContainText('92%');
    
    // Verify audio playback button is present
    await expect(eventItem.locator('[data-testid="play-audio-button"]')).toBeVisible();
  });

  test('Voice Event Editing Workflow', async ({ page }) => {
    await loginUser(page);
    await navigateToVoiceEvents(page);
    
    // Assume there's already a voice event in the list
    await page.waitForSelector('[data-testid="voice-event-item"]', { timeout: 5000 });
    
    // Click edit button on first event
    await page.click('[data-testid="voice-event-item"] [data-testid="edit-event-button"]');
    
    // Wait for editor to open
    await page.waitForSelector('[data-testid="voice-event-editor"]', { timeout: 5000 });
    
    // Verify editor shows event details
    await expect(page.locator('[data-testid="product-name-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="quantity-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="confidence-display"]')).toBeVisible();
    
    // Edit product name
    await page.fill('[data-testid="product-name-input"]', 'Atlantic Salmon - Premium Grade');
    
    // Edit quantity
    await page.fill('[data-testid="quantity-input"]', '55');
    
    // Save changes
    await page.click('[data-testid="save-event-button"]');
    
    // Wait for save confirmation
    await page.waitForSelector('[data-testid="save-success-message"]', { timeout: 5000 });
    
    // Verify changes are reflected in the list
    await page.waitForSelector('[data-testid="voice-events-list"]', { timeout: 3000 });
    const updatedEvent = page.locator('[data-testid="voice-event-item"]').first();
    await expect(updatedEvent.locator('[data-testid="product-name"]')).toContainText('Premium Grade');
    await expect(updatedEvent.locator('[data-testid="quantity"]')).toContainText('55');
  });

  test('Quality Review Workflow', async ({ page }) => {
    await loginUser(page);
    
    // Navigate to quality review panel
    await page.click('[data-testid="nav-quality-review"]');
    await page.waitForSelector('[data-testid="quality-review-panel"]', { timeout: 5000 });
    
    // Check if there are events needing review
    const hasEventsForReview = await page.isVisible('[data-testid="review-event-item"]');
    
    if (hasEventsForReview) {
      // Test individual event approval
      const reviewEvent = page.locator('[data-testid="review-event-item"]').first();
      
      // Verify event details are shown
      await expect(reviewEvent.locator('[data-testid="confidence-score"]')).toBeVisible();
      await expect(reviewEvent.locator('[data-testid="original-transcript"]')).toBeVisible();
      
      // Play audio recording
      await reviewEvent.locator('[data-testid="play-audio-button"]').click();
      
      // Approve the event
      await reviewEvent.locator('[data-testid="approve-event-button"]').click();
      
      // Verify event is removed from review list
      await page.waitForTimeout(1000); // Wait for removal animation
      const remainingEvents = await page.locator('[data-testid="review-event-item"]').count();
      expect(remainingEvents).toBeLessThan(1);
    } else {
      // Verify empty state
      await expect(page.locator('[data-testid="no-events-message"]')).toContainText('All caught up!');
    }
  });

  test('Voice Event Filtering and Search', async ({ page }) => {
    await loginUser(page);
    await navigateToVoiceEvents(page);
    
    // Wait for events to load
    await page.waitForSelector('[data-testid="voice-event-item"]', { timeout: 5000 });
    const initialEventCount = await page.locator('[data-testid="voice-event-item"]').count();
    
    // Test search functionality
    await page.fill('[data-testid="search-input"]', 'salmon');
    await page.press('[data-testid="search-input"]', 'Enter');
    
    // Wait for filtered results
    await page.waitForTimeout(1000);
    const searchResults = await page.locator('[data-testid="voice-event-item"]').count();
    expect(searchResults).toBeLessThanOrEqual(initialEventCount);
    
    // Clear search
    await page.fill('[data-testid="search-input"]', '');
    await page.press('[data-testid="search-input"]', 'Enter');
    
    // Test event type filter
    await page.selectOption('[data-testid="event-type-filter"]', 'receiving');
    await page.waitForTimeout(1000);
    
    // Verify filtered events are all receiving type
    const eventTypes = await page.locator('[data-testid="event-type"]').allTextContents();
    eventTypes.forEach(type => {
      expect(type.toLowerCase()).toContain('receiving');
    });
    
    // Test confidence filter
    await page.selectOption('[data-testid="confidence-filter"]', 'High (90%+)');
    await page.waitForTimeout(1000);
    
    // Verify all visible events have high confidence
    const confidenceScores = await page.locator('[data-testid="confidence-score"]').allTextContents();
    confidenceScores.forEach(score => {
      const numericScore = parseInt(score.replace('%', ''));
      expect(numericScore).toBeGreaterThanOrEqual(90);
    });
  });

  test('Audio Playback Functionality', async ({ page }) => {
    await loginUser(page);
    await navigateToVoiceEvents(page);
    
    // Wait for events with audio
    await page.waitForSelector('[data-testid="voice-event-item"] [data-testid="play-audio-button"]', { timeout: 5000 });
    
    // Click play audio button
    const playButton = page.locator('[data-testid="play-audio-button"]').first();
    await playButton.click();
    
    // Verify audio playback started (in a real test, you might check for audio context or mock audio events)
    // For now, we'll just verify the button interaction worked
    await expect(playButton).toBeVisible();
    
    // Test audio playback in editor
    await page.click('[data-testid="voice-event-item"] [data-testid="edit-event-button"]');
    await page.waitForSelector('[data-testid="voice-event-editor"]', { timeout: 3000 });
    
    const editorPlayButton = page.locator('[data-testid="editor-play-audio-button"]');
    if (await editorPlayButton.isVisible()) {
      await editorPlayButton.click();
      await expect(editorPlayButton).toBeVisible();
    }
  });

  test('Error Handling and Recovery', async ({ page }) => {
    await loginUser(page);
    await navigateToVoiceEvents(page);
    
    // Mock network error
    await page.route('**/api/voice-process', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ success: false, error: 'Service unavailable' })
      });
    });
    
    // Try to create voice event
    await page.click('[data-testid="start-voice-recording"]');
    await mockVoiceInput(page, 'Test voice input');
    await page.click('[data-testid="stop-voice-recording"]');
    
    // Wait for error message
    await page.waitForSelector('[data-testid="error-message"]', { timeout: 5000 });
    await expect(page.locator('[data-testid="error-message"]')).toContainText('Service unavailable');
    
    // Test retry functionality
    await page.click('[data-testid="retry-button"]');
    
    // Remove network error mock
    await page.unroute('**/api/voice-process');
    
    // Mock successful response
    await page.route('**/api/voice-process', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            transcription: 'Test voice input',
            confidence_score: 0.85,
            event_id: 'retry-test-event'
          }
        })
      });
    });
    
    // Retry should now succeed
    await page.waitForSelector('[data-testid="voice-event-item"]', { timeout: 5000 });
  });

  test('Real-time Updates', async ({ page }) => {
    await loginUser(page);
    await navigateToVoiceEvents(page);
    
    // Get initial event count
    await page.waitForSelector('[data-testid="voice-event-item"]', { timeout: 5000 });
    const initialCount = await page.locator('[data-testid="voice-event-item"]').count();
    
    // Simulate real-time event addition (in a real test, this might come from another user or system)
    await page.evaluate(() => {
      // Simulate Supabase real-time event
      const mockEvent = {
        id: 'realtime-event-' + Date.now(),
        event_type: 'receiving',
        product_name: 'Real-time Test Product',
        quantity: 25,
        voice_confidence_score: 0.88,
        created_at: new Date().toISOString()
      };
      
      // Trigger real-time update (this would normally come from Supabase)
      window.dispatchEvent(new CustomEvent('voice-event-added', { detail: mockEvent }));
    });
    
    // Wait for new event to appear
    await page.waitForTimeout(2000);
    const newCount = await page.locator('[data-testid="voice-event-item"]').count();
    expect(newCount).toBeGreaterThan(initialCount);
    
    // Verify new event details
    const newEvent = page.locator('[data-testid="voice-event-item"]').first();
    await expect(newEvent.locator('[data-testid="product-name"]')).toContainText('Real-time Test Product');
  });

  test('Mobile Responsiveness', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    await loginUser(page);
    await navigateToVoiceEvents(page);
    
    // Verify mobile layout
    await page.waitForSelector('[data-testid="voice-events-list"]', { timeout: 5000 });
    
    // Check that elements are properly stacked on mobile
    const eventItem = page.locator('[data-testid="voice-event-item"]').first();
    const eventItemBox = await eventItem.boundingBox();
    
    if (eventItemBox) {
      // Verify event item fits within mobile viewport
      expect(eventItemBox.width).toBeLessThanOrEqual(375);
    }
    
    // Test mobile navigation
    const mobileMenuButton = page.locator('[data-testid="mobile-menu-button"]');
    if (await mobileMenuButton.isVisible()) {
      await mobileMenuButton.click();
      await expect(page.locator('[data-testid="mobile-nav-menu"]')).toBeVisible();
    }
    
    // Test voice recording on mobile
    await page.click('[data-testid="start-voice-recording"]');
    await expect(page.locator('[data-testid="recording-indicator"]')).toBeVisible();
    await page.click('[data-testid="stop-voice-recording"]');
  });

  test('Accessibility Features', async ({ page }) => {
    await loginUser(page);
    await navigateToVoiceEvents(page);
    
    // Test keyboard navigation
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    
    // Verify focus is visible
    const focusedElement = await page.locator(':focus');
    await expect(focusedElement).toBeVisible();
    
    // Test screen reader labels
    const searchInput = page.locator('[data-testid="search-input"]');
    await expect(searchInput).toHaveAttribute('aria-label');
    
    const eventTypeFilter = page.locator('[data-testid="event-type-filter"]');
    await expect(eventTypeFilter).toHaveAttribute('aria-label');
    
    // Test high contrast mode (if supported)
    await page.emulateMedia({ colorScheme: 'dark' });
    await page.waitForTimeout(1000);
    
    // Verify elements are still visible in dark mode
    await expect(page.locator('[data-testid="voice-events-list"]')).toBeVisible();
  });

  test('Performance and Loading', async ({ page }) => {
    // Start performance monitoring
    await page.coverage.startJSCoverage();
    
    const startTime = Date.now();
    await loginUser(page);
    await navigateToVoiceEvents(page);
    
    // Wait for events to load
    await page.waitForSelector('[data-testid="voice-event-item"]', { timeout: 10000 });
    const loadTime = Date.now() - startTime;
    
    // Verify reasonable load time (adjust threshold as needed)
    expect(loadTime).toBeLessThan(10000); // 10 seconds max
    
    // Check for loading indicators
    const loadingIndicator = page.locator('[data-testid="loading-indicator"]');
    if (await loadingIndicator.isVisible()) {
      await expect(loadingIndicator).toBeHidden({ timeout: 5000 });
    }
    
    // Stop coverage and check for unused code
    const coverage = await page.coverage.stopJSCoverage();
    const totalBytes = coverage.reduce((sum, entry) => sum + entry.text.length, 0);
    const usedBytes = coverage.reduce((sum, entry) => {
      const usedRanges = entry.ranges.filter(range => range.count > 0);
      return sum + usedRanges.reduce((rangeSum, range) => rangeSum + (range.end - range.start), 0);
    }, 0);
    
    const usagePercentage = (usedBytes / totalBytes) * 100;
    console.log(`JavaScript usage: ${usagePercentage.toFixed(2)}%`);
    
    // Verify reasonable code usage (adjust threshold as needed)
    expect(usagePercentage).toBeGreaterThan(30); // At least 30% of code should be used
  });
});