/**
 * Comprehensive End-to-End tests for voice processing system
 * Tests complete user workflows across different browsers and scenarios
 */

import { test, expect, Page, BrowserContext } from '@playwright/test';

// Test configuration for voice system E2E tests
const TEST_CONFIG = {
  BASE_URL: process.env.VITE_APP_URL || 'http://localhost:5177',
  VOICE_PROCESSING_TIMEOUT: 10000,
  DATABASE_OPERATION_TIMEOUT: 5000,
  UI_INTERACTION_TIMEOUT: 3000
};

// Mock voice recognition data for different scenarios
const VOICE_TEST_SCENARIOS = {
  highConfidence: {
    transcript: 'Received 25 pounds of fresh salmon from Ocean Fresh Seafoods',
    expectedProduct: 'salmon',
    expectedQuantity: '25',
    expectedUnit: 'pounds',
    expectedVendor: 'Ocean Fresh Seafoods',
    expectedConfidence: 0.9
  },
  mediumConfidence: {
    transcript: 'Sold some crab to Marina Restaurant',
    expectedProduct: 'crab',
    expectedQuantity: '0', // Should require manual input
    expectedCustomer: 'Marina Restaurant',
    expectedConfidence: 0.7
  },
  lowConfidence: {
    transcript: 'Something about... fish... from...',
    expectedProduct: 'unknown',
    expectedConfidence: 0.4
  }
};

// Helper functions for E2E tests
async function setupVoiceMocking(page: Page) {
  // Mock the Web Speech API
  await page.addInitScript(() => {
    // Mock SpeechRecognition
    (window as any).SpeechRecognition = class MockSpeechRecognition {
      continuous = false;
      interimResults = false;
      lang = 'en-US';
      onresult: ((event: any) => void) | null = null;
      onerror: ((event: any) => void) | null = null;
      onend: (() => void) | null = null;
      
      start() {
        setTimeout(() => {
          if (this.onresult) {
            const mockEvent = {
              results: [{
                0: { transcript: (window as any).mockTranscript || 'test transcript' },
                isFinal: true
              }],
              resultIndex: 0
            };
            this.onresult(mockEvent);
          }
          if (this.onend) this.onend();
        }, 1000);
      }
      
      stop() {}
      abort() {}
    };
    
    (window as any).webkitSpeechRecognition = (window as any).SpeechRecognition;
    
    // Mock MediaRecorder
    (window as any).MediaRecorder = class MockMediaRecorder {
      ondataavailable: ((event: any) => void) | null = null;
      onstop: (() => void) | null = null;
      state = 'inactive';
      
      constructor() {}
      
      start() {
        this.state = 'recording';
        setTimeout(() => {
          if (this.ondataavailable) {
            const mockBlob = new Blob(['mock audio'], { type: 'audio/webm' });
            this.ondataavailable({ data: mockBlob });
          }
        }, 100);
      }
      
      stop() {
        this.state = 'inactive';
        if (this.onstop) this.onstop();
      }
    };
    
    // Mock getUserMedia
    if (navigator.mediaDevices) {
      navigator.mediaDevices.getUserMedia = () => Promise.resolve(new MediaStream());
    }
  });
}

async function setMockTranscript(page: Page, transcript: string) {
  await page.evaluate((transcript) => {
    (window as any).mockTranscript = transcript;
  }, transcript);
}

async function waitForVoiceProcessing(page: Page) {
  // Wait for processing indicators to appear and disappear
  await page.waitForSelector('[data-testid="voice-processing"]', { 
    timeout: TEST_CONFIG.VOICE_PROCESSING_TIMEOUT 
  });
  await page.waitForSelector('[data-testid="voice-processing"]', { 
    state: 'hidden', 
    timeout: TEST_CONFIG.VOICE_PROCESSING_TIMEOUT 
  });
}

test.describe('Voice System E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    await setupVoiceMocking(page);
    await page.goto(TEST_CONFIG.BASE_URL);
    
    // Wait for app to load and ensure user is authenticated
    await page.waitForSelector('[data-testid="app-layout"]', { 
      timeout: TEST_CONFIG.UI_INTERACTION_TIMEOUT 
    });
  });

  test.describe('Complete Voice Workflow Tests', () => {
    test('should complete high-confidence voice event creation workflow', async ({ page }) => {
      const scenario = VOICE_TEST_SCENARIOS.highConfidence;
      
      // Navigate to voice event management
      await page.click('[data-testid="voice-events-nav"]');
      await page.waitForSelector('[data-testid="voice-events-page"]');
      
      // Set up the mock transcript
      await setMockTranscript(page, scenario.transcript);
      
      // Start voice input
      await page.click('[data-testid="start-voice-input"]');
      
      // Verify voice recording state
      await expect(page.locator('[data-testid="voice-status"]')).toContainText('Listening');
      
      // Wait for voice processing to complete
      await waitForVoiceProcessing(page);
      
      // Verify transcript display
      await expect(page.locator('[data-testid="voice-transcript"]')).toContainText(scenario.transcript);
      
      // Verify parsed voice command
      await expect(page.locator('[data-testid="voice-product"]')).toContainText(scenario.expectedProduct);
      await expect(page.locator('[data-testid="voice-quantity"]')).toContainText(scenario.expectedQuantity);
      await expect(page.locator('[data-testid="voice-unit"]')).toContainText(scenario.expectedUnit);
      await expect(page.locator('[data-testid="voice-vendor"]')).toContainText(scenario.expectedVendor);
      
      // Verify confidence score
      const confidenceElement = page.locator('[data-testid="voice-confidence"]');
      await expect(confidenceElement).toBeVisible();
      
      const confidenceText = await confidenceElement.textContent();
      const confidenceValue = parseFloat(confidenceText?.match(/(\d+(?:\.\d+)?)/)?.[1] || '0');
      expect(confidenceValue).toBeGreaterThan(scenario.expectedConfidence * 100);
      
      // Confirm and save the event
      await page.click('[data-testid="confirm-voice-event"]');
      
      // Wait for database operation
      await page.waitForSelector('[data-testid="save-success"]', {
        timeout: TEST_CONFIG.DATABASE_OPERATION_TIMEOUT
      });
      
      // Verify event appears in the events list
      await page.waitForSelector('[data-testid="voice-events-list"]');
      await expect(page.locator('[data-testid="voice-events-list"] [data-testid^="event-"]').first()).toBeVisible();
      
      // Verify event details
      const firstEvent = page.locator('[data-testid="voice-events-list"] [data-testid^="event-"]').first();
      await expect(firstEvent.locator('[data-testid="event-product"]')).toContainText(scenario.expectedProduct);
      await expect(firstEvent.locator('[data-testid="event-quantity"]')).toContainText(scenario.expectedQuantity);
      await expect(firstEvent.locator('[data-testid="event-type"]')).toContainText('receiving');
    });

    test('should handle medium-confidence voice event with user review', async ({ page }) => {
      const scenario = VOICE_TEST_SCENARIOS.mediumConfidence;
      
      await page.click('[data-testid="voice-events-nav"]');
      await page.waitForSelector('[data-testid="voice-events-page"]');
      
      await setMockTranscript(page, scenario.transcript);
      await page.click('[data-testid="start-voice-input"]');
      await waitForVoiceProcessing(page);
      
      // Verify medium confidence triggers review mode
      await expect(page.locator('[data-testid="review-required"]')).toBeVisible();
      await expect(page.locator('[data-testid="confidence-warning"]')).toContainText('Medium confidence');
      
      // Verify parsed data
      await expect(page.locator('[data-testid="voice-product"]')).toContainText(scenario.expectedProduct);
      await expect(page.locator('[data-testid="voice-customer"]')).toContainText(scenario.expectedCustomer);
      
      // User should need to manually enter quantity
      const quantityInput = page.locator('[data-testid="quantity-input"]');
      await expect(quantityInput).toBeVisible();
      await expect(quantityInput).toHaveValue(''); // Should be empty
      
      // User corrects the quantity
      await quantityInput.fill('12');
      
      // User confirms the corrected event
      await page.click('[data-testid="confirm-voice-event"]');
      
      // Verify save success
      await page.waitForSelector('[data-testid="save-success"]');
      
      // Verify event is marked as reviewed
      const firstEvent = page.locator('[data-testid="voice-events-list"] [data-testid^="event-"]').first();
      await expect(firstEvent.locator('[data-testid="event-reviewed"]')).toBeVisible();
    });

    test('should reject low-confidence voice input appropriately', async ({ page }) => {
      const scenario = VOICE_TEST_SCENARIOS.lowConfidence;
      
      await page.click('[data-testid="voice-events-nav"]');
      await page.waitForSelector('[data-testid="voice-events-page"]');
      
      await setMockTranscript(page, scenario.transcript);
      await page.click('[data-testid="start-voice-input"]');
      await waitForVoiceProcessing(page);
      
      // Verify low confidence is rejected
      await expect(page.locator('[data-testid="confidence-too-low"]')).toBeVisible();
      await expect(page.locator('[data-testid="confidence-error"]')).toContainText('confidence too low');
      
      // Should show option to retry or enter manually
      await expect(page.locator('[data-testid="retry-voice-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="manual-entry"]')).toBeVisible();
      
      // User chooses manual entry
      await page.click('[data-testid="manual-entry"]');
      
      // Verify manual form is displayed
      await expect(page.locator('[data-testid="manual-event-form"]')).toBeVisible();
      
      // User can still reference the original transcript
      await expect(page.locator('[data-testid="original-transcript"]')).toContainText(scenario.transcript);
    });
  });

  test.describe('Voice Assistant Integration Tests', () => {
    test('should integrate voice commands with real-time assistant', async ({ page }) => {
      // Navigate to the real-time assistant page
      await page.click('[data-testid="realtime-assistant-nav"]');
      await page.waitForSelector('[data-testid="realtime-assistant"]');
      
      // Start voice conversation
      await page.click('[data-testid="start-conversation"]');
      await expect(page.locator('[data-testid="assistant-listening"]')).toBeVisible();
      
      // Set complex voice command
      const complexCommand = 'Received 30 pounds of fresh halibut from Pacific Catch at 34 degrees fahrenheit excellent quality batch HC-2024-001';
      await setMockTranscript(page, complexCommand);
      
      // Trigger voice processing
      await page.click('[data-testid="voice-input-trigger"]');
      await waitForVoiceProcessing(page);
      
      // Verify assistant processes complex command
      await expect(page.locator('[data-testid="assistant-response"]')).toContainText('halibut');
      await expect(page.locator('[data-testid="assistant-response"]')).toContainText('30 pounds');
      await expect(page.locator('[data-testid="assistant-response"]')).toContainText('Pacific Catch');
      
      // Verify temperature and quality data extraction
      await expect(page.locator('[data-testid="temperature-extracted"]')).toContainText('34');
      await expect(page.locator('[data-testid="quality-extracted"]')).toContainText('excellent');
      await expect(page.locator('[data-testid="batch-extracted"]')).toContainText('HC-2024-001');
      
      // Confirm the event creation
      await page.click('[data-testid="assistant-confirm"]');
      
      // Verify event was created with all details
      await page.waitForSelector('[data-testid="event-created-success"]');
      
      // Check that assistant continues conversation
      await expect(page.locator('[data-testid="assistant-ready"]')).toBeVisible();
      await expect(page.locator('[data-testid="conversation-active"]')).toContainText('What else can I help you with?');
    });

    test('should handle voice command clarifications', async ({ page }) => {
      await page.click('[data-testid="realtime-assistant-nav"]');
      await page.waitForSelector('[data-testid="realtime-assistant"]');
      
      // Start with ambiguous command
      await page.click('[data-testid="start-conversation"]');
      await setMockTranscript(page, 'Received some fish from the dock');
      
      await page.click('[data-testid="voice-input-trigger"]');
      await waitForVoiceProcessing(page);
      
      // Assistant should ask for clarification
      await expect(page.locator('[data-testid="assistant-clarification"]')).toBeVisible();
      await expect(page.locator('[data-testid="assistant-response"]')).toContainText('What type of fish');
      await expect(page.locator('[data-testid="assistant-response"]')).toContainText('How much');
      
      // User provides clarification
      await setMockTranscript(page, 'Twenty pounds of salmon');
      await page.click('[data-testid="voice-input-trigger"]');
      await waitForVoiceProcessing(page);
      
      // Assistant should ask for vendor clarification
      await expect(page.locator('[data-testid="assistant-response"]')).toContainText('Which vendor');
      
      // User provides vendor
      await setMockTranscript(page, 'Ocean Fresh Seafoods');
      await page.click('[data-testid="voice-input-trigger"]');
      await waitForVoiceProcessing(page);
      
      // Assistant should confirm complete information
      await expect(page.locator('[data-testid="assistant-summary"]')).toBeVisible();
      await expect(page.locator('[data-testid="assistant-summary"]')).toContainText('20 pounds salmon Ocean Fresh');
      
      // User confirms
      await page.click('[data-testid="confirm-clarified-event"]');
      await page.waitForSelector('[data-testid="event-created-success"]');
    });
  });

  test.describe('Error Handling and Recovery Tests', () => {
    test('should handle microphone permission denied', async ({ page }) => {
      // Override getUserMedia to reject
      await page.addInitScript(() => {
        if (navigator.mediaDevices) {
          navigator.mediaDevices.getUserMedia = () => Promise.reject(new Error('Permission denied'));
        }
      });
      
      await page.click('[data-testid="voice-events-nav"]');
      await page.waitForSelector('[data-testid="voice-events-page"]');
      
      await page.click('[data-testid="start-voice-input"]');
      
      // Should show permission error
      await expect(page.locator('[data-testid="microphone-error"]')).toBeVisible();
      await expect(page.locator('[data-testid="error-message"]')).toContainText('microphone permission');
      
      // Should offer alternative input methods
      await expect(page.locator('[data-testid="manual-input-option"]')).toBeVisible();
      await expect(page.locator('[data-testid="permission-help"]')).toBeVisible();
    });

    test('should handle network connectivity issues', async ({ page }) => {
      // Start normal voice processing
      await page.click('[data-testid="voice-events-nav"]');
      await page.waitForSelector('[data-testid="voice-events-page"]');
      
      await setMockTranscript(page, 'Received 25 pounds of salmon');
      await page.click('[data-testid="start-voice-input"]');
      
      // Simulate network failure during processing
      await page.route('**/api/**', route => route.abort());
      
      // Should show network error
      await expect(page.locator('[data-testid="network-error"]')).toBeVisible();
      await expect(page.locator('[data-testid="error-message"]')).toContainText('network');
      
      // Should offer retry option
      await expect(page.locator('[data-testid="retry-button"]')).toBeVisible();
      
      // Restore network and retry
      await page.unroute('**/api/**');
      await page.click('[data-testid="retry-button"]');
      
      // Should complete successfully
      await waitForVoiceProcessing(page);
      await expect(page.locator('[data-testid="voice-transcript"]')).toContainText('salmon');
    });

    test('should handle voice processing timeout', async ({ page }) => {
      // Mock slow voice processing
      await page.addInitScript(() => {
        (window as any).SpeechRecognition = class SlowSpeechRecognition {
          start() {
            // Never call onresult to simulate timeout
          }
          stop() {}
          abort() {}
        };
      });
      
      await page.click('[data-testid="voice-events-nav"]');
      await page.waitForSelector('[data-testid="voice-events-page"]');
      
      await page.click('[data-testid="start-voice-input"]');
      
      // Should show timeout error after waiting
      await expect(page.locator('[data-testid="timeout-error"]')).toBeVisible({ 
        timeout: 15000 
      });
      await expect(page.locator('[data-testid="error-message"]')).toContainText('timeout');
      
      // Should offer to try again
      await expect(page.locator('[data-testid="try-again-button"]')).toBeVisible();
    });
  });

  test.describe('Cross-Browser Compatibility Tests', () => {
    ['chromium', 'firefox', 'webkit'].forEach(browserName => {
      test(`should work correctly in ${browserName}`, async ({ browser }) => {
        const context = await browser.newContext();
        const page = await context.newPage();
        
        await setupVoiceMocking(page);
        await page.goto(TEST_CONFIG.BASE_URL);
        
        // Test basic voice functionality
        await page.click('[data-testid="voice-events-nav"]');
        await page.waitForSelector('[data-testid="voice-events-page"]');
        
        await setMockTranscript(page, 'Received 15 pounds of cod');
        await page.click('[data-testid="start-voice-input"]');
        
        await waitForVoiceProcessing(page);
        
        // Verify cross-browser compatibility
        await expect(page.locator('[data-testid="voice-transcript"]')).toContainText('cod');
        await expect(page.locator('[data-testid="voice-product"]')).toContainText('cod');
        await expect(page.locator('[data-testid="voice-quantity"]')).toContainText('15');
        
        await context.close();
      });
    });
  });

  test.describe('Mobile Responsiveness Tests', () => {
    test('should work on mobile devices', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      
      await page.goto(TEST_CONFIG.BASE_URL);
      
      // Test mobile navigation
      await page.click('[data-testid="mobile-menu-toggle"]');
      await page.click('[data-testid="voice-events-nav-mobile"]');
      
      await page.waitForSelector('[data-testid="voice-events-page"]');
      
      // Test mobile voice input
      await setMockTranscript(page, 'Received 20 pounds of shrimp');
      await page.click('[data-testid="mobile-voice-button"]');
      
      await waitForVoiceProcessing(page);
      
      // Verify mobile layout
      await expect(page.locator('[data-testid="mobile-voice-interface"]')).toBeVisible();
      await expect(page.locator('[data-testid="voice-transcript"]')).toContainText('shrimp');
      
      // Test mobile form interaction
      await page.click('[data-testid="mobile-confirm-button"]');
      await page.waitForSelector('[data-testid="save-success"]');
    });

    test('should handle touch interactions for voice controls', async ({ page }) => {
      await page.setViewportSize({ width: 768, height: 1024 }); // Tablet size
      
      await page.goto(TEST_CONFIG.BASE_URL);
      await page.click('[data-testid="voice-events-nav"]');
      
      // Test touch and hold for voice input
      await page.locator('[data-testid="touch-voice-button"]').dispatchEvent('touchstart');
      await expect(page.locator('[data-testid="voice-recording"]')).toBeVisible();
      
      await page.locator('[data-testid="touch-voice-button"]').dispatchEvent('touchend');
      await expect(page.locator('[data-testid="voice-processing"]')).toBeVisible();
    });
  });

  test.describe('Accessibility Tests', () => {
    test('should be accessible to screen readers', async ({ page }) => {
      await page.goto(TEST_CONFIG.BASE_URL);
      await page.click('[data-testid="voice-events-nav"]');
      
      // Check ARIA labels
      await expect(page.locator('[data-testid="start-voice-input"]')).toHaveAttribute('aria-label');
      await expect(page.locator('[data-testid="voice-status"]')).toHaveAttribute('aria-live', 'polite');
      
      // Test keyboard navigation
      await page.keyboard.press('Tab');
      await page.keyboard.press('Tab');
      await page.keyboard.press('Enter'); // Should activate voice input
      
      await expect(page.locator('[data-testid="voice-status"]')).toContainText('Listening');
    });

    test('should support keyboard shortcuts', async ({ page }) => {
      await page.goto(TEST_CONFIG.BASE_URL);
      await page.click('[data-testid="voice-events-nav"]');
      
      // Test keyboard shortcut for voice input
      await page.keyboard.press('Control+Space');
      await expect(page.locator('[data-testid="voice-listening"]')).toBeVisible();
      
      // Test escape to cancel
      await page.keyboard.press('Escape');
      await expect(page.locator('[data-testid="voice-cancelled"]')).toBeVisible();
    });
  });

  test.describe('Data Persistence Tests', () => {
    test('should persist voice events across page reloads', async ({ page }) => {
      // Create a voice event
      await page.click('[data-testid="voice-events-nav"]');
      await page.waitForSelector('[data-testid="voice-events-page"]');
      
      await setMockTranscript(page, 'Received 25 pounds of salmon from Ocean Fresh');
      await page.click('[data-testid="start-voice-input"]');
      await waitForVoiceProcessing(page);
      await page.click('[data-testid="confirm-voice-event"]');
      await page.waitForSelector('[data-testid="save-success"]');
      
      // Reload the page
      await page.reload();
      await page.waitForSelector('[data-testid="voice-events-page"]');
      
      // Verify event persists
      await expect(page.locator('[data-testid="voice-events-list"] [data-testid^="event-"]').first()).toBeVisible();
      await expect(page.locator('[data-testid="voice-events-list"] [data-testid^="event-"]').first())
        .toContainText('salmon');
    });

    test('should maintain voice event audit trail', async ({ page }) => {
      // Create and then edit a voice event
      await page.click('[data-testid="voice-events-nav"]');
      await page.waitForSelector('[data-testid="voice-events-page"]');
      
      await setMockTranscript(page, 'Received 25 pounds of salmon');
      await page.click('[data-testid="start-voice-input"]');
      await waitForVoiceProcessing(page);
      await page.click('[data-testid="confirm-voice-event"]');
      await page.waitForSelector('[data-testid="save-success"]');
      
      // Edit the event
      await page.click('[data-testid="voice-events-list"] [data-testid^="event-"] [data-testid="edit-button"]');
      await page.waitForSelector('[data-testid="event-editor"]');
      
      // Verify audit information is displayed
      await expect(page.locator('[data-testid="created-by-voice"]')).toBeVisible();
      await expect(page.locator('[data-testid="original-transcript"]')).toContainText('salmon');
      await expect(page.locator('[data-testid="voice-confidence"]')).toBeVisible();
      
      // Make an edit
      await page.fill('[data-testid="quantity-input"]', '30');
      await page.click('[data-testid="save-changes"]');
      
      // Verify edit audit trail
      await expect(page.locator('[data-testid="modification-history"]')).toBeVisible();
      await expect(page.locator('[data-testid="original-quantity"]')).toContainText('25');
      await expect(page.locator('[data-testid="updated-quantity"]')).toContainText('30');
    });
  });
});