/**
 * Cross-Browser Voice Processing Compatibility Tests
 * Tests voice processing functionality across different browsers and devices
 */

import { test, expect, Page, BrowserContext } from '@playwright/test';

// Browser configurations to test
const BROWSER_CONFIGS = [
  { name: 'chromium', userAgent: 'Chrome/120.0.0.0' },
  { name: 'firefox', userAgent: 'Firefox/121.0' },
  { name: 'webkit', userAgent: 'Safari/17.0' }
];

// Device configurations
const DEVICE_CONFIGS = [
  {
    name: 'Desktop',
    viewport: { width: 1920, height: 1080 },
    isMobile: false
  },
  {
    name: 'Tablet',
    viewport: { width: 768, height: 1024 },
    isMobile: true
  },
  {
    name: 'Mobile',
    viewport: { width: 375, height: 667 },
    isMobile: true
  }
];

// Voice API feature detection
async function detectVoiceFeatures(page: Page) {
  return await page.evaluate(() => {
    const features = {
      speechRecognition: 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window,
      speechSynthesis: 'speechSynthesis' in window,
      mediaRecorder: 'MediaRecorder' in window,
      getUserMedia: 'mediaDevices' in navigator && 'getUserMedia' in navigator.mediaDevices,
      webAudio: 'AudioContext' in window || 'webkitAudioContext' in window
    };
    
    return features;
  });
}

// Mock voice input for testing
async function mockVoiceInput(page: Page, transcript: string) {
  await page.evaluate((text) => {
    // Mock SpeechRecognition
    if ('SpeechRecognition' in window || 'webkitSpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      const originalStart = SpeechRecognition.prototype.start;
      
      SpeechRecognition.prototype.start = function() {
        setTimeout(() => {
          if (this.onresult) {
            const mockEvent = {
              results: [{
                0: { transcript: text },
                isFinal: true
              }],
              resultIndex: 0
            };
            this.onresult(mockEvent);
          }
          if (this.onend) {
            this.onend();
          }
        }, 500);
      };
    }
  }, transcript);
}

// Setup mock audio context for browsers that support it
async function setupMockAudio(page: Page) {
  await page.evaluate(() => {
    // Mock MediaRecorder
    if ('MediaRecorder' in window) {
      const OriginalMediaRecorder = window.MediaRecorder;
      window.MediaRecorder = class extends OriginalMediaRecorder {
        constructor(stream: MediaStream, options?: MediaRecorderOptions) {
          super(stream, options);
          this.state = 'inactive';
        }
        
        start() {
          this.state = 'recording';
          setTimeout(() => {
            if (this.ondataavailable) {
              const mockBlob = new Blob(['mock audio data'], { type: 'audio/webm' });
              this.ondataavailable({ data: mockBlob } as BlobEvent);
            }
          }, 100);
        }
        
        stop() {
          this.state = 'inactive';
          if (this.onstop) {
            this.onstop();
          }
        }
      };
    }
    
    // Mock getUserMedia
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      navigator.mediaDevices.getUserMedia = async () => {
        return new MediaStream();
      };
    }
    
    // Mock AudioContext
    if ('AudioContext' in window || 'webkitAudioContext' in window) {
      const AudioContext = window.AudioContext || window.webkitAudioContext;
      window.AudioContext = class extends AudioContext {
        constructor() {
          super();
          this.state = 'running';
        }
        
        createAnalyser() {
          return {
            frequencyBinCount: 1024,
            fftSize: 2048,
            getByteFrequencyData: () => {},
            connect: () => {},
            disconnect: () => {}
          };
        }
        
        createMediaStreamSource() {
          return {
            connect: () => {},
            disconnect: () => {}
          };
        }
      };
    }
  });
}

test.describe('Voice Processing Browser Compatibility', () => {
  for (const browserConfig of BROWSER_CONFIGS) {
    test.describe(`${browserConfig.name.toUpperCase()} Browser`, () => {
      
      test('should detect voice API support', async ({ page }) => {
        await page.goto('/voice-event-management');
        
        const features = await detectVoiceFeatures(page);
        
        // Log feature support for this browser
        console.log(`${browserConfig.name} feature support:`, features);
        
        // Basic expectations based on browser
        if (browserConfig.name === 'chromium') {
          expect(features.speechRecognition).toBeTruthy();
          expect(features.mediaRecorder).toBeTruthy();
          expect(features.getUserMedia).toBeTruthy();
        } else if (browserConfig.name === 'firefox') {
          expect(features.mediaRecorder).toBeTruthy();
          expect(features.getUserMedia).toBeTruthy();
          // Firefox has limited speech recognition support
        } else if (browserConfig.name === 'webkit') {
          expect(features.speechRecognition).toBeTruthy();
          expect(features.getUserMedia).toBeTruthy();
        }
        
        // All browsers should support basic audio APIs
        expect(features.speechSynthesis).toBeTruthy();
        expect(features.webAudio).toBeTruthy();
      });

      test('should handle voice button interaction', async ({ page }) => {
        await page.goto('/voice-event-management');
        await setupMockAudio(page);
        
        // Find voice input button
        const voiceButton = page.getByTestId('voice-input-button').or(
          page.locator('button:has-text("Voice")')
        ).first();
        
        await expect(voiceButton).toBeVisible();
        
        // Check button is enabled
        await expect(voiceButton).toBeEnabled();
        
        // Click voice button
        await voiceButton.click();
        
        // Should show some indication of voice recording
        await expect(
          page.locator('[data-testid="voice-recording-indicator"]').or(
            page.locator('.voice-recording').or(
              page.locator('[class*="recording"]')
            )
          )
        ).toBeVisible({ timeout: 2000 });
      });

      test('should process voice input and display results', async ({ page }) => {
        await page.goto('/voice-event-management');
        await setupMockAudio(page);
        
        const testTranscript = 'Received twenty five pounds of salmon from Ocean Fresh Seafoods';
        await mockVoiceInput(page, testTranscript);
        
        // Start voice input
        const voiceButton = page.getByTestId('voice-input-button').or(
          page.locator('button:has-text("Voice")')
        ).first();
        
        await voiceButton.click();
        
        // Wait for processing to complete
        await page.waitForTimeout(2000);
        
        // Check for results display
        const resultsContainer = page.locator('[data-testid="voice-results"]').or(
          page.locator('.voice-results').or(
            page.locator('[class*="results"]')
          )
        );
        
        await expect(resultsContainer).toBeVisible({ timeout: 5000 });
        
        // Check for extracted information
        await expect(page.locator('text=salmon')).toBeVisible();
        await expect(page.locator('text=25')).toBeVisible();
      });

      test('should handle microphone permission scenarios', async ({ page, context }) => {
        // Test with microphone permission denied
        await context.grantPermissions([], { origin: page.url() });
        
        await page.goto('/voice-event-management');
        await setupMockAudio(page);
        
        const voiceButton = page.getByTestId('voice-input-button').or(
          page.locator('button:has-text("Voice")')
        ).first();
        
        await voiceButton.click();
        
        // Should show permission error or fallback
        await expect(
          page.locator('text=microphone').or(
            page.locator('text=permission').or(
              page.locator('text=denied')
            )
          )
        ).toBeVisible({ timeout: 3000 });
      });

      for (const deviceConfig of DEVICE_CONFIGS) {
        test(`should work on ${deviceConfig.name} devices`, async ({ page, context }) => {
          await page.setViewportSize(deviceConfig.viewport);
          
          await page.goto('/voice-event-management');
          await setupMockAudio(page);
          
          // Check responsive design
          const voiceButton = page.getByTestId('voice-input-button').or(
            page.locator('button:has-text("Voice")')
          ).first();
          
          await expect(voiceButton).toBeVisible();
          
          // On mobile, button might be styled differently
          if (deviceConfig.isMobile) {
            const buttonSize = await voiceButton.boundingBox();
            expect(buttonSize?.width).toBeGreaterThan(40); // Touch-friendly size
            expect(buttonSize?.height).toBeGreaterThan(40);
          }
          
          // Test interaction
          await voiceButton.click();
          
          // Voice interface should adapt to screen size
          const voiceInterface = page.locator('[data-testid="voice-interface"]').or(
            page.locator('.voice-interface')
          );
          
          if (await voiceInterface.isVisible()) {
            const interfaceBox = await voiceInterface.boundingBox();
            expect(interfaceBox?.width).toBeLessThanOrEqual(deviceConfig.viewport.width);
          }
        });
      }
    });
  }

  test.describe('Cross-Browser Feature Parity', () => {
    test('should provide consistent voice processing functionality', async ({ page }) => {
      await page.goto('/voice-event-management');
      await setupMockAudio(page);
      
      const testCases = [
        'Received twenty pounds of salmon',
        'Sold fifteen pounds of cod to Marina Restaurant',
        'Got delivery of thirty pounds of halibut'
      ];
      
      for (const testCase of testCases) {
        await mockVoiceInput(page, testCase);
        
        const voiceButton = page.getByTestId('voice-input-button').or(
          page.locator('button:has-text("Voice")')
        ).first();
        
        await voiceButton.click();
        await page.waitForTimeout(1500);
        
        // Should process and show results regardless of browser
        const hasResults = await page.locator('[data-testid="voice-results"]').or(
          page.locator('.voice-results')
        ).isVisible();
        
        expect(hasResults).toBeTruthy();
        
        // Clear results for next test
        const clearButton = page.locator('[data-testid="clear-results"]').or(
          page.locator('button:has-text("Clear")')
        );
        
        if (await clearButton.isVisible()) {
          await clearButton.click();
        }
      }
    });

    test('should handle errors gracefully across browsers', async ({ page }) => {
      await page.goto('/voice-event-management');
      
      // Test without setting up mock audio (should trigger error handling)
      const voiceButton = page.getByTestId('voice-input-button').or(
        page.locator('button:has-text("Voice")')
      ).first();
      
      await voiceButton.click();
      
      // Should show error message or fallback UI
      const errorMessage = page.locator('[data-testid="voice-error"]').or(
        page.locator('.error').or(
          page.locator('text=error').or(
            page.locator('text=failed')
          )
        )
      );
      
      await expect(errorMessage).toBeVisible({ timeout: 3000 });
    });

    test('should provide fallback for unsupported browsers', async ({ page }) => {
      await page.goto('/voice-event-management');
      
      // Simulate browser without voice support
      await page.evaluate(() => {
        // Remove voice APIs
        delete (window as any).SpeechRecognition;
        delete (window as any).webkitSpeechRecognition;
        delete (window as any).MediaRecorder;
        
        if (navigator.mediaDevices) {
          delete (navigator.mediaDevices as any).getUserMedia;
        }
      });
      
      // Should show fallback UI or disable voice features
      const voiceButton = page.getByTestId('voice-input-button').or(
        page.locator('button:has-text("Voice")')
      ).first();
      
      if (await voiceButton.isVisible()) {
        // Button should be disabled or show not supported message
        const isDisabled = await voiceButton.isDisabled();
        const hasNotSupportedText = await page.locator('text=not supported').isVisible();
        
        expect(isDisabled || hasNotSupportedText).toBeTruthy();
      }
    });
  });

  test.describe('Audio Processing Compatibility', () => {
    test('should handle different audio formats across browsers', async ({ page }) => {
      await page.goto('/voice-event-management');
      await setupMockAudio(page);
      
      // Test different audio MIME types
      const audioFormats = ['audio/webm', 'audio/ogg', 'audio/wav', 'audio/mp4'];
      
      for (const format of audioFormats) {
        const isSupported = await page.evaluate((mimeType) => {
          if ('MediaRecorder' in window) {
            return MediaRecorder.isTypeSupported(mimeType);
          }
          return false;
        }, format);
        
        console.log(`${format} support:`, isSupported);
        
        if (isSupported) {
          // Test recording with this format
          await page.evaluate((mimeType) => {
            const stream = new MediaStream();
            const recorder = new MediaRecorder(stream, { mimeType });
            recorder.start();
            recorder.stop();
          }, format);
        }
      }
    });

    test('should handle audio processing errors consistently', async ({ page }) => {
      await page.goto('/voice-event-management');
      
      // Simulate audio processing errors
      await page.evaluate(() => {
        // Mock MediaRecorder to throw errors
        if ('MediaRecorder' in window) {
          const OriginalMediaRecorder = window.MediaRecorder;
          window.MediaRecorder = class extends OriginalMediaRecorder {
            start() {
              if (this.onerror) {
                this.onerror(new Event('error'));
              }
            }
          };
        }
      });
      
      const voiceButton = page.getByTestId('voice-input-button').or(
        page.locator('button:has-text("Voice")')
      ).first();
      
      await voiceButton.click();
      
      // Should handle error gracefully
      const errorIndicator = page.locator('[data-testid="audio-error"]').or(
        page.locator('.audio-error').or(
          page.locator('text=audio error')
        )
      );
      
      await expect(errorIndicator).toBeVisible({ timeout: 2000 });
    });
  });

  test.describe('Performance Across Browsers', () => {
    test('should maintain acceptable performance across browsers', async ({ page }) => {
      await page.goto('/voice-event-management');
      await setupMockAudio(page);
      
      const testTranscript = 'Received twenty five pounds of salmon from Ocean Fresh Seafoods';
      await mockVoiceInput(page, testTranscript);
      
      const startTime = Date.now();
      
      const voiceButton = page.getByTestId('voice-input-button').or(
        page.locator('button:has-text("Voice")')
      ).first();
      
      await voiceButton.click();
      
      // Wait for processing to complete
      await page.locator('[data-testid="voice-results"]').or(
        page.locator('.voice-results')
      ).waitFor({ timeout: 5000 });
      
      const endTime = Date.now();
      const processingTime = endTime - startTime;
      
      // Should complete within reasonable time (5 seconds)
      expect(processingTime).toBeLessThan(5000);
      
      console.log(`Voice processing time: ${processingTime}ms`);
    });

    test('should not cause memory leaks during extended use', async ({ page }) => {
      await page.goto('/voice-event-management');
      await setupMockAudio(page);
      
      // Simulate multiple voice interactions
      for (let i = 0; i < 5; i++) {
        const testTranscript = `Received ${10 + i} pounds of salmon`;
        await mockVoiceInput(page, testTranscript);
        
        const voiceButton = page.getByTestId('voice-input-button').or(
          page.locator('button:has-text("Voice")')
        ).first();
        
        await voiceButton.click();
        await page.waitForTimeout(1000);
        
        // Clear results
        const clearButton = page.locator('[data-testid="clear-results"]').or(
          page.locator('button:has-text("Clear")')
        );
        
        if (await clearButton.isVisible()) {
          await clearButton.click();
        }
      }
      
      // Check that the page is still responsive
      const voiceButton = page.getByTestId('voice-input-button').or(
        page.locator('button:has-text("Voice")')
      ).first();
      
      await expect(voiceButton).toBeEnabled();
    });
  });

  test.describe('Accessibility Across Browsers', () => {
    test('should be accessible with keyboard navigation', async ({ page }) => {
      await page.goto('/voice-event-management');
      
      // Test keyboard navigation to voice button
      await page.keyboard.press('Tab');
      
      const focusedElement = page.locator(':focus');
      const voiceButton = page.getByTestId('voice-input-button').or(
        page.locator('button:has-text("Voice")')
      ).first();
      
      // Voice button should be focusable
      const isFocused = await focusedElement.evaluate((el, button) => {
        return el === button;
      }, await voiceButton.elementHandle());
      
      if (!isFocused) {
        // Navigate to voice button
        await page.keyboard.press('Tab');
        await page.keyboard.press('Tab');
      }
      
      // Should be able to activate with keyboard
      await page.keyboard.press('Enter');
      
      // Should show voice interface or start recording
      await expect(
        page.locator('[data-testid="voice-recording-indicator"]').or(
          page.locator('.voice-recording')
        )
      ).toBeVisible({ timeout: 2000 });
    });

    test('should support screen readers', async ({ page }) => {
      await page.goto('/voice-event-management');
      
      const voiceButton = page.getByTestId('voice-input-button').or(
        page.locator('button:has-text("Voice")')
      ).first();
      
      // Check for accessibility attributes
      const ariaLabel = await voiceButton.getAttribute('aria-label');
      const title = await voiceButton.getAttribute('title');
      const role = await voiceButton.getAttribute('role');
      
      // Should have proper labeling for screen readers
      expect(ariaLabel || title || await voiceButton.textContent()).toBeTruthy();
      
      // Should have proper role
      expect(role === 'button' || await voiceButton.evaluate(el => el.tagName.toLowerCase()) === 'button').toBeTruthy();
    });
  });
});