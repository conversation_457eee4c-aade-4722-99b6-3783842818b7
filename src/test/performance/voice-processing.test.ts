import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { performance } from 'perf_hooks';
import { enhancedVoiceEventProcessor } from '../../services/EnhancedVoiceEventProcessor';
import { voiceEventService } from '../../services/VoiceEventService';
import { audioStorageService } from '../../services/AudioStorageService';
import { VoiceEvent } from '../../types/schema';

// Mock services for performance testing
vi.mock('../../services/VoiceEventService');
vi.mock('../../services/AudioStorageService');
vi.mock('../../lib/supabase');

// Performance test configuration
const PERFORMANCE_THRESHOLDS = {
  VOICE_PROCESSING_MAX_TIME: 3000, // 3 seconds
  DATABASE_OPERATION_MAX_TIME: 1000, // 1 second
  AUDIO_UPLOAD_MAX_TIME: 5000, // 5 seconds
  BATCH_OPERATION_MAX_TIME: 10000, // 10 seconds
  MEMORY_USAGE_MAX_MB: 100, // 100 MB
  CONCURRENT_REQUESTS_MAX_TIME: 5000 // 5 seconds for 10 concurrent requests
};

const mockVoiceEvent: VoiceEvent = {
  id: 'perf-test-event',
  event_type: 'receiving',
  product_name: 'Performance Test Product',
  quantity: 100,
  unit: 'lbs',
  voice_confidence_score: 0.92,
  voice_confidence_breakdown: {
    product_match: 0.95,
    quantity_extraction: 0.90,
    vendor_match: 0.91,
    overall: 0.92
  },
  raw_transcript: 'Performance test transcript',
  audio_recording_url: 'https://storage.example.com/audio/perf-test.wav',
  occurred_at: '2024-01-15T10:30:00Z',
  created_at: '2024-01-15T10:30:00Z',
  created_by_voice: true
};

// Utility functions for performance testing
function measureExecutionTime<T>(fn: () => Promise<T>): Promise<{ result: T; duration: number }> {
  return new Promise(async (resolve, reject) => {
    const startTime = performance.now();
    try {
      const result = await fn();
      const endTime = performance.now();
      resolve({ result, duration: endTime - startTime });
    } catch (error) {
      reject(error);
    }
  });
}

function measureMemoryUsage(): { heapUsed: number; heapTotal: number } {
  if (typeof process !== 'undefined' && process.memoryUsage) {
    const usage = process.memoryUsage();
    return {
      heapUsed: usage.heapUsed / 1024 / 1024, // Convert to MB
      heapTotal: usage.heapTotal / 1024 / 1024
    };
  }
  return { heapUsed: 0, heapTotal: 0 };
}

function createMockAudioBlob(sizeKB: number = 100): Blob {
  const arrayBuffer = new ArrayBuffer(sizeKB * 1024);
  return new Blob([arrayBuffer], { type: 'audio/webm' });
}

describe('Voice Processing Performance Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup performance-optimized mocks
    vi.mocked(voiceEventService.createVoiceEvent).mockImplementation(async () => {
      // Simulate database operation time
      await new Promise(resolve => setTimeout(resolve, 50));
      return mockVoiceEvent;
    });
    
    vi.mocked(voiceEventService.getVoiceEvents).mockImplementation(async () => {
      await new Promise(resolve => setTimeout(resolve, 30));
      return [mockVoiceEvent];
    });
    
    vi.mocked(voiceEventService.updateVoiceEvent).mockImplementation(async () => {
      await new Promise(resolve => setTimeout(resolve, 40));
      return mockVoiceEvent;
    });
    
    vi.mocked(audioStorageService.uploadAudioRecording).mockImplementation(async () => {
      await new Promise(resolve => setTimeout(resolve, 100));
      return 'https://storage.example.com/audio/test.wav';
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Voice Processing Performance', () => {
    it('should process voice events within acceptable time limits', async () => {
      const audioBlob = createMockAudioBlob(50); // 50KB audio file
      
      const { duration } = await measureExecutionTime(async () => {
        return enhancedVoiceEventProcessor.processVoiceEvent(audioBlob, 'test-user');
      });
      
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.VOICE_PROCESSING_MAX_TIME);
      console.log(`Voice processing completed in ${duration.toFixed(2)}ms`);
    });

    it('should handle large audio files efficiently', async () => {
      const largeAudioBlob = createMockAudioBlob(1000); // 1MB audio file
      
      const { duration } = await measureExecutionTime(async () => {
        return enhancedVoiceEventProcessor.processVoiceEvent(largeAudioBlob, 'test-user');
      });
      
      // Large files may take longer but should still be reasonable
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.VOICE_PROCESSING_MAX_TIME * 2);
      console.log(`Large audio processing completed in ${duration.toFixed(2)}ms`);
    });

    it('should maintain performance with multiple voice processing requests', async () => {
      const audioBlob = createMockAudioBlob(25); // Smaller files for concurrent testing
      const concurrentRequests = 10;
      
      const startTime = performance.now();
      
      const promises = Array.from({ length: concurrentRequests }, () =>
        enhancedVoiceEventProcessor.processVoiceEvent(audioBlob, 'test-user')
      );
      
      await Promise.all(promises);
      
      const totalTime = performance.now() - startTime;
      
      expect(totalTime).toBeLessThan(PERFORMANCE_THRESHOLDS.CONCURRENT_REQUESTS_MAX_TIME);
      console.log(`${concurrentRequests} concurrent requests completed in ${totalTime.toFixed(2)}ms`);
    });
  });

  describe('Database Operation Performance', () => {
    it('should create voice events quickly', async () => {
      const eventData = {
        event_type: 'receiving' as const,
        product_name: 'Test Product',
        quantity: 50,
        unit: 'lbs' as const,
        voice_confidence_score: 0.9,
        voice_confidence_breakdown: {
          product_match: 0.9,
          quantity_extraction: 0.9,
          vendor_match: 0.9,
          overall: 0.9
        },
        raw_transcript: 'Test transcript'
      };
      
      const { duration } = await measureExecutionTime(async () => {
        return voiceEventService.createVoiceEvent(eventData);
      });
      
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.DATABASE_OPERATION_MAX_TIME);
      console.log(`Database create operation completed in ${duration.toFixed(2)}ms`);
    });

    it('should retrieve voice events efficiently', async () => {
      const { duration } = await measureExecutionTime(async () => {
        return voiceEventService.getVoiceEvents({
          confidenceThreshold: 0.7,
          eventType: ['receiving']
        });
      });
      
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.DATABASE_OPERATION_MAX_TIME);
      console.log(`Database query operation completed in ${duration.toFixed(2)}ms`);
    });

    it('should update voice events quickly', async () => {
      const { duration } = await measureExecutionTime(async () => {
        return voiceEventService.updateVoiceEvent(
          'test-event-id',
          { product_name: 'Updated Product Name' },
          'test-user'
        );
      });
      
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.DATABASE_OPERATION_MAX_TIME);
      console.log(`Database update operation completed in ${duration.toFixed(2)}ms`);
    });

    it('should handle batch operations efficiently', async () => {
      const eventIds = Array.from({ length: 50 }, (_, i) => `event-${i}`);
      
      vi.mocked(voiceEventService.batchApproveVoiceEvents).mockImplementation(async () => {
        // Simulate batch operation time
        await new Promise(resolve => setTimeout(resolve, 200));
      });
      
      const { duration } = await measureExecutionTime(async () => {
        return voiceEventService.batchApproveVoiceEvents(eventIds, 'reviewer-id');
      });
      
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.BATCH_OPERATION_MAX_TIME);
      console.log(`Batch operation (50 events) completed in ${duration.toFixed(2)}ms`);
    });
  });

  describe('Audio Storage Performance', () => {
    it('should upload audio files within time limits', async () => {
      const audioBlob = createMockAudioBlob(200); // 200KB audio file
      
      const { duration } = await measureExecutionTime(async () => {
        return audioStorageService.uploadAudioRecording(audioBlob, 'test-event', 'test-user');
      });
      
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.AUDIO_UPLOAD_MAX_TIME);
      console.log(`Audio upload completed in ${duration.toFixed(2)}ms`);
    });

    it('should handle multiple concurrent uploads', async () => {
      const audioBlob = createMockAudioBlob(50); // Smaller files for concurrent testing
      const concurrentUploads = 5;
      
      const startTime = performance.now();
      
      const promises = Array.from({ length: concurrentUploads }, (_, i) =>
        audioStorageService.uploadAudioRecording(audioBlob, `test-event-${i}`, 'test-user')
      );
      
      await Promise.all(promises);
      
      const totalTime = performance.now() - startTime;
      
      expect(totalTime).toBeLessThan(PERFORMANCE_THRESHOLDS.AUDIO_UPLOAD_MAX_TIME);
      console.log(`${concurrentUploads} concurrent uploads completed in ${totalTime.toFixed(2)}ms`);
    });
  });

  describe('Memory Usage Performance', () => {
    it('should not exceed memory limits during voice processing', async () => {
      const initialMemory = measureMemoryUsage();
      const audioBlob = createMockAudioBlob(500); // 500KB audio file
      
      // Process multiple events to test memory usage
      for (let i = 0; i < 10; i++) {
        await enhancedVoiceEventProcessor.processVoiceEvent(audioBlob, 'test-user');
      }
      
      const finalMemory = measureMemoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      
      expect(memoryIncrease).toBeLessThan(PERFORMANCE_THRESHOLDS.MEMORY_USAGE_MAX_MB);
      console.log(`Memory increase: ${memoryIncrease.toFixed(2)}MB`);
    });

    it('should clean up memory after processing large batches', async () => {
      const initialMemory = measureMemoryUsage();
      const audioBlob = createMockAudioBlob(100);
      
      // Process a large batch
      const batchSize = 50;
      const promises = Array.from({ length: batchSize }, () =>
        enhancedVoiceEventProcessor.processVoiceEvent(audioBlob, 'test-user')
      );
      
      await Promise.all(promises);
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      // Wait a bit for cleanup
      await new Promise(resolve => setTimeout(resolve, 100));
      
      const finalMemory = measureMemoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      
      expect(memoryIncrease).toBeLessThan(PERFORMANCE_THRESHOLDS.MEMORY_USAGE_MAX_MB * 2);
      console.log(`Memory increase after batch processing: ${memoryIncrease.toFixed(2)}MB`);
    });
  });

  describe('Scalability Performance', () => {
    it('should maintain performance with increasing data volume', async () => {
      // Simulate increasing number of events in database
      const eventCounts = [10, 50, 100, 500];
      const performanceResults: number[] = [];
      
      for (const count of eventCounts) {
        // Mock database with increasing number of events
        const mockEvents = Array.from({ length: count }, (_, i) => ({
          ...mockVoiceEvent,
          id: `event-${i}`
        }));
        
        vi.mocked(voiceEventService.getVoiceEvents).mockResolvedValue(mockEvents);
        
        const { duration } = await measureExecutionTime(async () => {
          return voiceEventService.getVoiceEvents();
        });
        
        performanceResults.push(duration);
        console.log(`Query with ${count} events: ${duration.toFixed(2)}ms`);
      }
      
      // Performance should not degrade significantly with more data
      // (This is a simplified test - real performance would depend on database indexing)
      const maxDuration = Math.max(...performanceResults);
      expect(maxDuration).toBeLessThan(PERFORMANCE_THRESHOLDS.DATABASE_OPERATION_MAX_TIME * 2);
    });

    it('should handle high-frequency event creation', async () => {
      const eventsPerSecond = 10;
      const testDurationSeconds = 2;
      const totalEvents = eventsPerSecond * testDurationSeconds;
      
      const audioBlob = createMockAudioBlob(25); // Small files for high frequency
      const startTime = performance.now();
      
      // Create events at high frequency
      const promises: Promise<any>[] = [];
      for (let i = 0; i < totalEvents; i++) {
        // Stagger the requests slightly to simulate real-world timing
        setTimeout(() => {
          promises.push(enhancedVoiceEventProcessor.processVoiceEvent(audioBlob, 'test-user'));
        }, (i / eventsPerSecond) * 1000);
      }
      
      await Promise.all(promises);
      
      const totalTime = performance.now() - startTime;
      const actualEventsPerSecond = totalEvents / (totalTime / 1000);
      
      console.log(`Processed ${totalEvents} events in ${totalTime.toFixed(2)}ms`);
      console.log(`Actual rate: ${actualEventsPerSecond.toFixed(2)} events/second`);
      
      // Should maintain reasonable throughput
      expect(actualEventsPerSecond).toBeGreaterThan(eventsPerSecond * 0.5); // At least 50% of target rate
    });
  });

  describe('Performance Regression Tests', () => {
    it('should not regress from baseline performance', async () => {
      // This test would compare against stored baseline metrics
      // For now, we'll just ensure operations complete within thresholds
      
      const audioBlob = createMockAudioBlob(100);
      const operations = [
        () => enhancedVoiceEventProcessor.processVoiceEvent(audioBlob, 'test-user'),
        () => voiceEventService.createVoiceEvent({
          event_type: 'receiving',
          product_name: 'Test Product',
          quantity: 50,
          unit: 'lbs',
          voice_confidence_score: 0.9,
          voice_confidence_breakdown: {
            product_match: 0.9,
            quantity_extraction: 0.9,
            vendor_match: 0.9,
            overall: 0.9
          },
          raw_transcript: 'Test transcript'
        }),
        () => voiceEventService.getVoiceEvents(),
        () => audioStorageService.uploadAudioRecording(audioBlob, 'test-event', 'test-user')
      ];
      
      const results = await Promise.all(
        operations.map(async (operation, index) => {
          const { duration } = await measureExecutionTime(operation);
          return { operation: index, duration };
        })
      );
      
      results.forEach(({ operation, duration }) => {
        console.log(`Operation ${operation} completed in ${duration.toFixed(2)}ms`);
        
        // Each operation should complete within reasonable time
        expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.VOICE_PROCESSING_MAX_TIME);
      });
    });
  });

  describe('Performance Monitoring', () => {
    it('should provide performance metrics', async () => {
      const audioBlob = createMockAudioBlob(100);
      const metrics: Array<{ operation: string; duration: number; memoryUsed: number }> = [];
      
      // Voice processing
      const initialMemory = measureMemoryUsage();
      const { duration: voiceProcessingTime } = await measureExecutionTime(async () => {
        return enhancedVoiceEventProcessor.processVoiceEvent(audioBlob, 'test-user');
      });
      const afterVoiceMemory = measureMemoryUsage();
      
      metrics.push({
        operation: 'voice_processing',
        duration: voiceProcessingTime,
        memoryUsed: afterVoiceMemory.heapUsed - initialMemory.heapUsed
      });
      
      // Database operations
      const { duration: dbCreateTime } = await measureExecutionTime(async () => {
        return voiceEventService.createVoiceEvent({
          event_type: 'receiving',
          product_name: 'Test Product',
          quantity: 50,
          unit: 'lbs',
          voice_confidence_score: 0.9,
          voice_confidence_breakdown: {
            product_match: 0.9,
            quantity_extraction: 0.9,
            vendor_match: 0.9,
            overall: 0.9
          },
          raw_transcript: 'Test transcript'
        });
      });
      
      metrics.push({
        operation: 'database_create',
        duration: dbCreateTime,
        memoryUsed: 0 // Database operations don't significantly impact client memory
      });
      
      // Log performance metrics
      console.log('Performance Metrics:');
      metrics.forEach(metric => {
        console.log(`  ${metric.operation}: ${metric.duration.toFixed(2)}ms, Memory: ${metric.memoryUsed.toFixed(2)}MB`);
      });
      
      // Verify all operations meet performance requirements
      metrics.forEach(metric => {
        expect(metric.duration).toBeLessThan(PERFORMANCE_THRESHOLDS.VOICE_PROCESSING_MAX_TIME);
        if (metric.memoryUsed > 0) {
          expect(metric.memoryUsed).toBeLessThan(PERFORMANCE_THRESHOLDS.MEMORY_USAGE_MAX_MB);
        }
      });
    });
  });
});