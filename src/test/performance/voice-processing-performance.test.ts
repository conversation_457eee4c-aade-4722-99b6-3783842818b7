/**
 * Performance tests for voice processing components
 * Tests latency, throughput, memory usage, and scalability requirements
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { VoiceEventService } from '../../modules/voice-event-storage/VoiceEventService';
import { 
  MockOpenAIClient,
  createMockOpenAIClient,
  generateVoiceTestScenarios,
  SEAFOOD_VOCABULARY 
} from '../mocks/openai-mocks';
import { 
  createMockSupabaseClient,
  resetMockDatabase,
  seedMockDatabase,
  setPerformanceSimulation,
  measureDatabasePerformance
} from '../mocks/supabase-mocks';
import { 
  VOICE_TEST_SAMPLES,
  MockSpeechRecognition,
  createLatencyTestScenarios,
  createConcurrencyTestData
} from '../mocks/voice-mocks';
import { testUtils, TEST_CONFIG } from '../test-config';

// Mock external dependencies
vi.mock('../../lib/supabase', () => ({
  supabase: createMockSupabaseClient()
}));

describe('Voice Processing Performance Tests', () => {
  let voiceEventService: VoiceEventService;
  let mockOpenAI: MockOpenAIClient;
  let mockSpeechRecognition: MockSpeechRecognition;

  beforeEach(() => {
    resetMockDatabase();
    seedMockDatabase();
    
    voiceEventService = new VoiceEventService();
    mockOpenAI = createMockOpenAIClient({
      latencyMs: 800, // Realistic OpenAI latency
      responseQuality: 'high'
    });
    
    mockSpeechRecognition = new MockSpeechRecognition();
    
    // Setup performance simulation
    setPerformanceSimulation(true, 50); // Realistic database latency
    
    global.SpeechRecognition = vi.fn(() => mockSpeechRecognition);
    global.performance = {
      ...global.performance,
      now: vi.fn(() => Date.now())
    };
  });

  afterEach(() => {
    vi.clearAllMocks();
    resetMockDatabase();
    setPerformanceSimulation(false);
  });

  describe('Voice Processing Latency Tests', () => {
    it('should process voice input within 2-second threshold', async () => {
      const testScenario = VOICE_TEST_SAMPLES.highConfidence.salmonReceiving;
      
      const performanceTest = async () => {
        // Step 1: Audio transcription
        const audioBlob = testUtils.createMockAudioBlob(5000);
        const transcription = await mockOpenAI.transcribeAudio(audioBlob);
        
        // Step 2: Process transcript into command
        const voiceCommand = await mockOpenAI.processTranscript(transcription.text);
        
        // Step 3: Save to database
        const voiceEventData = {
          event_type: voiceCommand.event_type!,
          product_name: voiceCommand.product_name!,
          quantity: voiceCommand.quantity!,
          unit: voiceCommand.unit!,
          voice_confidence_score: voiceCommand.confidence_score,
          voice_confidence_breakdown: voiceCommand.confidence_breakdown!,
          raw_transcript: voiceCommand.raw_transcript,
          occurred_at: new Date().toISOString()
        };
        
        return await voiceEventService.createVoiceEvent(voiceEventData);
      };
      
      const { result, duration } = await testUtils.measurePerformance(performanceTest);
      
      // Performance assertions
      expect(duration).toBeLessThan(TEST_CONFIG.PERFORMANCE_THRESHOLDS.VOICE_PROCESSING_MAX_TIME);
      expect(result).toBeDefined();
      expect(result.voice_confidence_score).toBeGreaterThan(0.8);
      
      console.log(`Voice processing completed in ${duration.toFixed(2)}ms`);
    });

    it('should handle different audio sizes with appropriate latency scaling', async () => {
      const latencyScenarios = createLatencyTestScenarios();
      const results: Array<{ size: number; latency: number; withinThreshold: boolean }> = [];
      
      for (const scenario of latencyScenarios) {
        const audioBlob = testUtils.createMockAudioBlob(scenario.audioSize / 1024); // Convert to KB
        
        const performanceTest = async () => {
          const transcription = await mockOpenAI.transcribeAudio(audioBlob);
          const voiceCommand = await mockOpenAI.processTranscript(transcription.text);
          
          const voiceEventData = {
            event_type: 'receiving' as const,
            product_name: 'salmon',
            quantity: 25,
            unit: 'lbs' as const,
            voice_confidence_score: voiceCommand.confidence_score,
            voice_confidence_breakdown: voiceCommand.confidence_breakdown!,
            raw_transcript: voiceCommand.raw_transcript,
            occurred_at: new Date().toISOString()
          };
          
          return await voiceEventService.createVoiceEvent(voiceEventData);
        };
        
        const { duration } = await testUtils.measurePerformance(performanceTest);
        const withinThreshold = duration <= scenario.expectedLatency;
        
        results.push({
          size: scenario.audioSize,
          latency: duration,
          withinThreshold
        });
        
        console.log(`${scenario.name}: ${duration.toFixed(2)}ms (threshold: ${scenario.expectedLatency}ms)`);
      }
      
      // Verify that most scenarios meet their thresholds
      const passedCount = results.filter(r => r.withinThreshold).length;
      const passRate = passedCount / results.length;
      
      expect(passRate).toBeGreaterThan(0.8); // 80% should pass
      
      // Verify latency scaling (larger files should take longer, but within limits)
      const sortedResults = results.sort((a, b) => a.size - b.size);
      for (let i = 1; i < sortedResults.length; i++) {
        const previous = sortedResults[i - 1];
        const current = sortedResults[i];
        
        // Allow some variance, but generally larger files should take longer
        const latencyIncrease = current.latency - previous.latency;
        expect(latencyIncrease).toBeLessThan(2000); // No more than 2s increase per size tier
      }
    });

    it('should maintain consistent performance under load', async () => {
      const iterations = 10;
      const latencies: number[] = [];
      
      for (let i = 0; i < iterations; i++) {
        const performanceTest = async () => {
          const audioBlob = testUtils.createMockAudioBlob(3000);
          const transcription = await mockOpenAI.transcribeAudio(audioBlob);
          const voiceCommand = await mockOpenAI.processTranscript(transcription.text);
          
          const voiceEventData = {
            event_type: 'receiving' as const,
            product_name: SEAFOOD_VOCABULARY.fish[i % SEAFOOD_VOCABULARY.fish.length],
            quantity: 10 + i,
            unit: 'lbs' as const,
            voice_confidence_score: voiceCommand.confidence_score,
            voice_confidence_breakdown: voiceCommand.confidence_breakdown!,
            raw_transcript: voiceCommand.raw_transcript,
            occurred_at: new Date().toISOString()
          };
          
          return await voiceEventService.createVoiceEvent(voiceEventData);
        };
        
        const { duration } = await testUtils.measurePerformance(performanceTest);
        latencies.push(duration);
      }
      
      // Calculate performance metrics
      const averageLatency = latencies.reduce((sum, lat) => sum + lat, 0) / latencies.length;
      const maxLatency = Math.max(...latencies);
      const minLatency = Math.min(...latencies);
      const variance = latencies.reduce((sum, lat) => sum + Math.pow(lat - averageLatency, 2), 0) / latencies.length;
      const standardDeviation = Math.sqrt(variance);
      
      console.log(`Performance metrics over ${iterations} iterations:`);
      console.log(`Average: ${averageLatency.toFixed(2)}ms`);
      console.log(`Min: ${minLatency.toFixed(2)}ms, Max: ${maxLatency.toFixed(2)}ms`);
      console.log(`Standard deviation: ${standardDeviation.toFixed(2)}ms`);
      
      // Performance assertions
      expect(averageLatency).toBeLessThan(TEST_CONFIG.PERFORMANCE_THRESHOLDS.VOICE_PROCESSING_MAX_TIME);
      expect(maxLatency).toBeLessThan(TEST_CONFIG.PERFORMANCE_THRESHOLDS.VOICE_PROCESSING_MAX_TIME * 1.5);
      expect(standardDeviation).toBeLessThan(500); // Consistent performance (< 500ms std dev)
    });
  });

  describe('Concurrent Processing Tests', () => {
    it('should handle multiple simultaneous voice processing requests', async () => {
      const concurrentUsers = 5;
      const testData = createConcurrencyTestData(concurrentUsers);
      
      const promises = testData.map(async (userData, index) => {
        const audioBlob = testUtils.createMockAudioBlob(3000);
        
        const startTime = performance.now();
        
        try {
          const transcription = await mockOpenAI.transcribeAudio(audioBlob);
          const voiceCommand = await mockOpenAI.processTranscript(userData.transcript);
          
          const voiceEventData = {
            event_type: 'receiving' as const,
            product_name: 'salmon',
            quantity: 10 + index,
            unit: 'lbs' as const,
            voice_confidence_score: voiceCommand.confidence_score,
            voice_confidence_breakdown: voiceCommand.confidence_breakdown!,
            raw_transcript: userData.transcript,
            occurred_at: new Date().toISOString(),
            metadata: {
              user_id: userData.userId,
              concurrent_test: true
            }
          };
          
          const result = await voiceEventService.createVoiceEvent(voiceEventData);
          const endTime = performance.now();
          
          return {
            userId: userData.userId,
            success: true,
            duration: endTime - startTime,
            result
          };
        } catch (error) {
          const endTime = performance.now();
          return {
            userId: userData.userId,
            success: false,
            duration: endTime - startTime,
            error: error instanceof Error ? error.message : 'Unknown error'
          };
        }
      });
      
      const results = await Promise.all(promises);
      
      // Verify all requests completed
      expect(results).toHaveLength(concurrentUsers);
      
      // Check success rate
      const successfulRequests = results.filter(r => r.success);
      const successRate = successfulRequests.length / results.length;
      expect(successRate).toBeGreaterThan(0.9); // 90% success rate
      
      // Check performance under load
      const averageLatency = successfulRequests.reduce((sum, r) => sum + r.duration, 0) / successfulRequests.length;
      expect(averageLatency).toBeLessThan(userData.expectedProcessingTime * 1.5); // Allow 50% overhead for concurrency
      
      console.log(`Concurrent processing results:`);
      console.log(`Success rate: ${(successRate * 100).toFixed(1)}%`);
      console.log(`Average latency: ${averageLatency.toFixed(2)}ms`);
      
      // Verify no data corruption
      successfulRequests.forEach((result, index) => {
        expect(result.result).toBeDefined();
        expect(result.result.quantity).toBe(10 + index);
        expect(result.result.metadata?.user_id).toBe(testData[index].userId);
      });
    });

    it('should maintain database performance under concurrent load', async () => {
      const concurrentWrites = 10;
      const promises: Promise<any>[] = [];
      
      for (let i = 0; i < concurrentWrites; i++) {
        const promise = measureDatabasePerformance(async () => {
          const voiceEventData = {
            event_type: 'receiving' as const,
            product_name: `test-product-${i}`,
            quantity: 10 + i,
            unit: 'lbs' as const,
            voice_confidence_score: 0.9,
            voice_confidence_breakdown: {
              product_match: 0.9,
              quantity_extraction: 0.9,
              vendor_match: 0.9,
              overall: 0.9
            },
            raw_transcript: `Test transcript ${i}`,
            occurred_at: new Date().toISOString(),
            metadata: {
              concurrency_test_batch: i
            }
          };
          
          return await voiceEventService.createVoiceEvent(voiceEventData);
        });
        
        promises.push(promise);
      }
      
      const results = await Promise.all(promises);
      
      // Verify all database operations completed successfully
      results.forEach((result, index) => {
        expect(result.result).toBeDefined();
        expect(result.withinThreshold).toBe(true);
        expect(result.duration).toBeLessThan(TEST_CONFIG.PERFORMANCE_THRESHOLDS.DATABASE_OPERATION_MAX_TIME);
      });
      
      const averageDuration = results.reduce((sum, r) => sum + r.duration, 0) / results.length;
      console.log(`Average database operation time under load: ${averageDuration.toFixed(2)}ms`);
    });
  });

  describe('Memory and Resource Usage Tests', () => {
    it('should not leak memory during repeated voice processing', async () => {
      // Note: This is a simplified memory test. In real scenarios, you'd use more sophisticated tools
      const iterations = 20;
      let initialMemory = 0;
      let currentMemory = 0;
      
      // Mock memory measurement (in real tests, use process.memoryUsage() or similar)
      const mockMemoryUsage = () => ({
        used: Math.random() * 50 + 100, // Simulate 100-150MB usage
        total: 500
      });
      
      initialMemory = mockMemoryUsage().used;
      
      for (let i = 0; i < iterations; i++) {
        const audioBlob = testUtils.createMockAudioBlob(5000);
        const transcription = await mockOpenAI.transcribeAudio(audioBlob);
        const voiceCommand = await mockOpenAI.processTranscript(transcription.text);
        
        const voiceEventData = {
          event_type: 'receiving' as const,
          product_name: 'salmon',
          quantity: 25,
          unit: 'lbs' as const,
          voice_confidence_score: voiceCommand.confidence_score,
          voice_confidence_breakdown: voiceCommand.confidence_breakdown!,
          raw_transcript: voiceCommand.raw_transcript,
          occurred_at: new Date().toISOString()
        };
        
        await voiceEventService.createVoiceEvent(voiceEventData);
        
        // Force cleanup
        if (global.gc) {
          global.gc();
        }
        
        currentMemory = mockMemoryUsage().used;
      }
      
      const memoryIncrease = currentMemory - initialMemory;
      const memoryIncreasePercentage = (memoryIncrease / initialMemory) * 100;
      
      console.log(`Memory usage: Initial ${initialMemory.toFixed(2)}MB, Final ${currentMemory.toFixed(2)}MB`);
      console.log(`Memory increase: ${memoryIncrease.toFixed(2)}MB (${memoryIncreasePercentage.toFixed(1)}%)`);
      
      // Memory increase should be reasonable (< 50% increase)
      expect(memoryIncreasePercentage).toBeLessThan(50);
    });

    it('should handle large batches without memory overflow', async () => {
      const batchSize = 50;
      const batchPromises: Promise<any>[] = [];
      
      // Create a large batch of voice events
      for (let i = 0; i < batchSize; i++) {
        const promise = (async () => {
          const voiceEventData = {
            event_type: 'receiving' as const,
            product_name: SEAFOOD_VOCABULARY.fish[i % SEAFOOD_VOCABULARY.fish.length],
            quantity: 10 + (i % 50),
            unit: 'lbs' as const,
            voice_confidence_score: 0.8 + (i % 20) * 0.01,
            voice_confidence_breakdown: {
              product_match: 0.9,
              quantity_extraction: 0.9,
              vendor_match: 0.9,
              overall: 0.9
            },
            raw_transcript: `Batch test transcript ${i}`,
            occurred_at: new Date().toISOString(),
            metadata: {
              batch_id: Math.floor(i / 10),
              batch_index: i
            }
          };
          
          return await voiceEventService.createVoiceEvent(voiceEventData);
        })();
        
        batchPromises.push(promise);
      }
      
      const batchResults = await Promise.all(batchPromises);
      
      // Verify all events were created successfully
      expect(batchResults).toHaveLength(batchSize);
      batchResults.forEach((result, index) => {
        expect(result).toBeDefined();
        expect(result.id).toBeTruthy();
        expect(result.metadata?.batch_index).toBe(index);
      });
      
      console.log(`Successfully processed batch of ${batchSize} voice events`);
    });
  });

  describe('API Response Time Tests', () => {
    it('should meet OpenAI API latency requirements', async () => {
      const apiLatencyTests = [
        { name: 'Small audio file', size: 1000, expectedMaxLatency: 1000 },
        { name: 'Medium audio file', size: 5000, expectedMaxLatency: 1500 },
        { name: 'Large audio file', size: 10000, expectedMaxLatency: 2000 }
      ];
      
      for (const test of apiLatencyTests) {
        mockOpenAI.setLatency(500); // Set base latency
        
        const audioBlob = testUtils.createMockAudioBlob(test.size / 1024);
        
        const { duration } = await testUtils.measurePerformance(async () => {
          return await mockOpenAI.transcribeAudio(audioBlob);
        });
        
        expect(duration).toBeLessThan(test.expectedMaxLatency);
        console.log(`${test.name}: ${duration.toFixed(2)}ms (max: ${test.expectedMaxLatency}ms)`);
      }
    });

    it('should handle API timeouts gracefully', async () => {
      // Set high latency to simulate timeout
      mockOpenAI.setLatency(5000);
      
      const audioBlob = testUtils.createMockAudioBlob(3000);
      
      const timeoutTest = async () => {
        const timeoutPromise = new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Request timeout')), 3000)
        );
        
        const transcriptionPromise = mockOpenAI.transcribeAudio(audioBlob);
        
        return Promise.race([transcriptionPromise, timeoutPromise]);
      };
      
      await expect(timeoutTest()).rejects.toThrow('Request timeout');
    });
  });

  describe('Performance Degradation Tests', () => {
    it('should handle network slowdown gracefully', async () => {
      // Simulate network degradation
      setPerformanceSimulation(true, 500); // Higher database latency
      mockOpenAI.setLatency(2000); // Higher API latency
      
      const degradedPerformanceTest = async () => {
        const audioBlob = testUtils.createMockAudioBlob(3000);
        const transcription = await mockOpenAI.transcribeAudio(audioBlob);
        const voiceCommand = await mockOpenAI.processTranscript(transcription.text);
        
        const voiceEventData = {
          event_type: voiceCommand.event_type!,
          product_name: voiceCommand.product_name!,
          quantity: voiceCommand.quantity!,
          unit: voiceCommand.unit!,
          voice_confidence_score: voiceCommand.confidence_score,
          voice_confidence_breakdown: voiceCommand.confidence_breakdown!,
          raw_transcript: voiceCommand.raw_transcript,
          occurred_at: new Date().toISOString()
        };
        
        return await voiceEventService.createVoiceEvent(voiceEventData);
      };
      
      const { result, duration } = await testUtils.measurePerformance(degradedPerformanceTest);
      
      // Should still complete, even if slower
      expect(result).toBeDefined();
      expect(duration).toBeLessThan(10000); // 10 second absolute max
      
      console.log(`Performance under network degradation: ${duration.toFixed(2)}ms`);
    });

    it('should maintain functionality with reduced performance', async () => {
      // Test with various performance degradation scenarios
      const degradationScenarios = [
        { name: 'Slow API', apiLatency: 1500, dbLatency: 100 },
        { name: 'Slow database', apiLatency: 500, dbLatency: 800 },
        { name: 'Both slow', apiLatency: 1200, dbLatency: 600 }
      ];
      
      for (const scenario of degradationScenarios) {
        mockOpenAI.setLatency(scenario.apiLatency);
        setPerformanceSimulation(true, scenario.dbLatency);
        
        const { result, duration } = await testUtils.measurePerformance(async () => {
          const audioBlob = testUtils.createMockAudioBlob(3000);
          const transcription = await mockOpenAI.transcribeAudio(audioBlob);
          const voiceCommand = await mockOpenAI.processTranscript(transcription.text);
          
          const voiceEventData = {
            event_type: 'receiving' as const,
            product_name: 'salmon',
            quantity: 25,
            unit: 'lbs' as const,
            voice_confidence_score: voiceCommand.confidence_score,
            voice_confidence_breakdown: voiceCommand.confidence_breakdown!,
            raw_transcript: voiceCommand.raw_transcript,
            occurred_at: new Date().toISOString()
          };
          
          return await voiceEventService.createVoiceEvent(voiceEventData);
        });
        
        expect(result).toBeDefined();
        expect(result.voice_confidence_score).toBeGreaterThan(0);
        
        console.log(`${scenario.name}: ${duration.toFixed(2)}ms`);
      }
    });
  });
});