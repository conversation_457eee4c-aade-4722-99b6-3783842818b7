/**
 * Voice Processing Performance Test Suite
 * Tests voice processing latency, database operations, and concurrent processing
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { 
  MockOpenAIAPI, 
  createLatencyTestScenarios,
  createConcurrencyTestData,
  VOICE_TEST_SAMPLES 
} from '../mocks/voice-mocks';
import { EnhancedVoiceEventProcessor } from '../../services/EnhancedVoiceEventProcessor';
import { OptimizedVoiceEventService } from '../../services/OptimizedVoiceEventService';
import { VoicePerformanceMonitor } from '../../services/VoicePerformanceMonitor';

// Performance thresholds (in milliseconds)
const PERFORMANCE_THRESHOLDS = {
  VOICE_PROCESSING_TOTAL: 2000,    // 2 seconds total
  TRANSCRIPTION_LATENCY: 1000,     // 1 second for transcription
  AI_PROCESSING_LATENCY: 800,      // 800ms for AI processing
  DATABASE_WRITE_LATENCY: 200,     // 200ms for database writes
  DATABASE_QUERY_LATENCY: 100,     // 100ms for database queries
  CACHE_LOOKUP_LATENCY: 10,        // 10ms for cache lookups
  CONCURRENT_PROCESSING: 5000      // 5 seconds for 10 concurrent requests
};

// Mock Supabase for performance testing
const mockSupabase = {
  from: vi.fn(() => ({
    insert: vi.fn(() => ({
      select: vi.fn(() => ({
        single: vi.fn(async () => {
          // Simulate variable database latency
          await new Promise(resolve => setTimeout(resolve, Math.random() * 100 + 50));
          return { data: { id: 'test-id' }, error: null };
        })
      }))
    })),
    select: vi.fn(() => ({
      eq: vi.fn(() => ({
        order: vi.fn(() => ({
          range: vi.fn(async () => {
            // Simulate query latency
            await new Promise(resolve => setTimeout(resolve, Math.random() * 50 + 25));
            return { data: [], error: null, count: 0 };
          })
        }))
      }))
    }))
  })),
  channel: vi.fn(() => ({
    on: vi.fn(() => ({
      subscribe: vi.fn(() => ({ unsubscribe: vi.fn() }))
    }))
  }))
};

vi.mock('../../lib/supabase', () => ({
  supabase: mockSupabase
}));

vi.mock('../../lib/voice-processor', () => ({
  default: class MockVoiceProcessor {
    async processAudioBlob(audioBlob: Blob): Promise<any> {
      const mockAPI = new MockOpenAIAPI();
      
      // Simulate variable processing latency based on audio size
      const baseLatency = 300;
      const sizeLatency = Math.min(audioBlob.size / 10, 500);
      mockAPI.setLatency(baseLatency + sizeLatency);
      
      const transcript = await mockAPI.transcribe(audioBlob);
      return await mockAPI.processTranscript(transcript);
    }
    
    getProcessingStatus() {
      return false;
    }
  }
}));

describe('Voice Processing Performance', () => {
  let processor: EnhancedVoiceEventProcessor;
  let voiceService: OptimizedVoiceEventService;
  let performanceMonitor: VoicePerformanceMonitor;

  beforeEach(() => {
    processor = new EnhancedVoiceEventProcessor();
    voiceService = new OptimizedVoiceEventService();
    performanceMonitor = new VoicePerformanceMonitor();
    vi.clearAllMocks();
  });

  afterEach(() => {
    performanceMonitor.clear();
    vi.clearAllMocks();
  });

  describe('Voice Processing Latency', () => {
    it('should meet total processing time threshold', async () => {
      const audioBlob = new Blob(['mock audio data'], { type: 'audio/webm' });
      
      const startTime = performance.now();
      await processor.processVoiceEvent(audioBlob, 'test-user');
      const endTime = performance.now();
      
      const totalLatency = endTime - startTime;
      expect(totalLatency).toBeLessThan(PERFORMANCE_THRESHOLDS.VOICE_PROCESSING_TOTAL);
    });

    it('should handle different audio sizes within thresholds', async () => {
      const scenarios = createLatencyTestScenarios();
      
      for (const scenario of scenarios) {
        const audioBlob = new Blob(
          [new ArrayBuffer(scenario.audioSize)], 
          { type: 'audio/webm' }
        );
        
        const startTime = performance.now();
        await processor.processVoiceEvent(audioBlob, 'test-user');
        const endTime = performance.now();
        
        const latency = endTime - startTime;
        expect(latency).toBeLessThan(scenario.expectedLatency);
      }
    });

    it('should maintain consistent performance across multiple requests', async () => {
      const iterations = 10;
      const latencies: number[] = [];
      
      for (let i = 0; i < iterations; i++) {
        const audioBlob = new Blob(['mock audio data'], { type: 'audio/webm' });
        
        const startTime = performance.now();
        await processor.processVoiceEvent(audioBlob, 'test-user');
        const endTime = performance.now();
        
        latencies.push(endTime - startTime);
      }
      
      // Calculate performance statistics
      const avgLatency = latencies.reduce((sum, lat) => sum + lat, 0) / latencies.length;
      const maxLatency = Math.max(...latencies);
      const minLatency = Math.min(...latencies);
      const variance = latencies.reduce((sum, lat) => sum + Math.pow(lat - avgLatency, 2), 0) / latencies.length;
      const stdDev = Math.sqrt(variance);
      
      expect(avgLatency).toBeLessThan(PERFORMANCE_THRESHOLDS.VOICE_PROCESSING_TOTAL);
      expect(maxLatency).toBeLessThan(PERFORMANCE_THRESHOLDS.VOICE_PROCESSING_TOTAL * 1.5);
      expect(stdDev).toBeLessThan(avgLatency * 0.3); // Standard deviation should be < 30% of average
    });

    it('should track processing time breakdown accurately', async () => {
      performanceMonitor.clear();
      const audioBlob = new Blob(['mock audio data'], { type: 'audio/webm' });
      
      const operationId = 'test-voice-processing';
      performanceMonitor.startTimer(operationId);
      
      await processor.processVoiceEvent(audioBlob, 'test-user');
      
      const totalTime = performanceMonitor.endTimer(operationId, 'voice.total.latency');
      
      expect(totalTime).toBeGreaterThan(0);
      expect(totalTime).toBeLessThan(PERFORMANCE_THRESHOLDS.VOICE_PROCESSING_TOTAL);
      
      // Check that metrics were recorded
      const stats = performanceMonitor.getStats('voice.total.latency');
      expect(stats.totalOperations).toBeGreaterThan(0);
      expect(stats.averageLatency).toBeGreaterThan(0);
    });
  });

  describe('Database Performance', () => {
    it('should meet database write latency thresholds', async () => {
      const startTime = performance.now();
      
      await voiceService.createVoiceEvent({
        event_type: 'receiving',
        quantity: 10,
        unit: 'pounds',
        voice_confidence_score: 0.85,
        voice_confidence_breakdown: {},
        raw_transcript: 'test transcript'
      });
      
      const endTime = performance.now();
      const writeLatency = endTime - startTime;
      
      expect(writeLatency).toBeLessThan(PERFORMANCE_THRESHOLDS.DATABASE_WRITE_LATENCY);
    });

    it('should meet database query latency thresholds', async () => {
      const startTime = performance.now();
      
      await voiceService.getVoiceEventsPaginated({}, 1, 20, { monitor: true });
      
      const endTime = performance.now();
      const queryLatency = endTime - startTime;
      
      expect(queryLatency).toBeLessThan(PERFORMANCE_THRESHOLDS.DATABASE_QUERY_LATENCY);
    });

    it('should optimize query performance with filtering', async () => {
      // Test unfiltered query
      const startUnfiltered = performance.now();
      await voiceService.getVoiceEventsPaginated({}, 1, 50);
      const unfilteredTime = performance.now() - startUnfiltered;
      
      // Test filtered query (should be similar or faster)
      const startFiltered = performance.now();
      await voiceService.getVoiceEventsPaginated({
        eventType: ['receiving'],
        confidenceThreshold: 0.8
      }, 1, 50);
      const filteredTime = performance.now() - startFiltered;
      
      // Filtered queries should not be significantly slower
      expect(filteredTime).toBeLessThan(unfilteredTime * 1.2);
    });

    it('should handle large result sets efficiently', async () => {
      const pageSizes = [10, 50, 100, 200];
      const latencies: number[] = [];
      
      for (const pageSize of pageSizes) {
        const startTime = performance.now();
        await voiceService.getVoiceEventsPaginated({}, 1, pageSize);
        const endTime = performance.now();
        
        latencies.push(endTime - startTime);
      }
      
      // Latency should scale reasonably with page size
      expect(latencies[0]).toBeLessThan(PERFORMANCE_THRESHOLDS.DATABASE_QUERY_LATENCY);
      expect(latencies[3]).toBeLessThan(PERFORMANCE_THRESHOLDS.DATABASE_QUERY_LATENCY * 2);
    });
  });

  describe('Caching Performance', () => {
    it('should meet cache lookup latency thresholds', async () => {
      // Prime the cache
      await voiceService.getVoiceEventsPaginated({}, 1, 20, { useCache: true });
      
      // Test cache lookup performance
      const startTime = performance.now();
      await voiceService.getVoiceEventsPaginated({}, 1, 20, { useCache: true });
      const endTime = performance.now();
      
      const cacheLatency = endTime - startTime;
      expect(cacheLatency).toBeLessThan(PERFORMANCE_THRESHOLDS.CACHE_LOOKUP_LATENCY);
    });

    it('should demonstrate significant performance improvement with caching', async () => {
      const filters = { eventType: ['receiving'] };
      
      // First call (no cache)
      const startUncached = performance.now();
      await voiceService.getVoiceEventsPaginated(filters, 1, 20, { useCache: false });
      const uncachedTime = performance.now() - startUncached;
      
      // Prime cache
      await voiceService.getVoiceEventsPaginated(filters, 1, 20, { useCache: true });
      
      // Second call (cached)
      const startCached = performance.now();
      await voiceService.getVoiceEventsPaginated(filters, 1, 20, { useCache: true });
      const cachedTime = performance.now() - startCached;
      
      // Cached should be significantly faster
      expect(cachedTime).toBeLessThan(uncachedTime * 0.5);
    });

    it('should maintain cache performance under load', async () => {
      const cacheOperations = 100;
      const startTime = performance.now();
      
      // Perform many cache operations
      const promises = [];
      for (let i = 0; i < cacheOperations; i++) {
        promises.push(
          voiceService.getVoiceEventsPaginated(
            { eventType: ['receiving'] }, 
            1, 
            10, 
            { useCache: true }
          )
        );
      }
      
      await Promise.all(promises);
      const endTime = performance.now();
      
      const avgTimePerOperation = (endTime - startTime) / cacheOperations;
      expect(avgTimePerOperation).toBeLessThan(PERFORMANCE_THRESHOLDS.CACHE_LOOKUP_LATENCY * 2);
    });
  });

  describe('Concurrent Processing', () => {
    it('should handle concurrent voice processing requests efficiently', async () => {
      const concurrentUsers = 5;
      const testData = createConcurrencyTestData(concurrentUsers);
      
      const startTime = performance.now();
      
      const promises = testData.map(async (data) => {
        const audioBlob = new Blob([data.transcript], { type: 'audio/webm' });
        return processor.processVoiceEvent(audioBlob, data.userId);
      });
      
      const results = await Promise.allSettled(promises);
      const endTime = performance.now();
      
      const totalTime = endTime - startTime;
      expect(totalTime).toBeLessThan(PERFORMANCE_THRESHOLDS.CONCURRENT_PROCESSING);
      
      // All requests should succeed
      const successes = results.filter(r => r.status === 'fulfilled');
      expect(successes).toHaveLength(concurrentUsers);
    });

    it('should maintain performance under high concurrency', async () => {
      const highConcurrency = 10;
      const testData = createConcurrencyTestData(highConcurrency);
      
      const startTime = performance.now();
      
      const promises = testData.map(async (data, index) => {
        // Stagger requests slightly to simulate real-world usage
        await new Promise(resolve => setTimeout(resolve, index * 50));
        
        const audioBlob = new Blob([data.transcript], { type: 'audio/webm' });
        return processor.processVoiceEvent(audioBlob, data.userId);
      });
      
      const results = await Promise.allSettled(promises);
      const endTime = performance.now();
      
      const totalTime = endTime - startTime;
      const avgTimePerRequest = totalTime / highConcurrency;
      
      expect(avgTimePerRequest).toBeLessThan(PERFORMANCE_THRESHOLDS.VOICE_PROCESSING_TOTAL * 1.5);
      
      // Most requests should succeed (allow for some to fail under extreme load)
      const successes = results.filter(r => r.status === 'fulfilled');
      expect(successes.length).toBeGreaterThan(highConcurrency * 0.8); // 80% success rate minimum
    });

    it('should not degrade database performance under concurrent load', async () => {
      const concurrentQueries = 20;
      
      const startTime = performance.now();
      
      const promises = [];
      for (let i = 0; i < concurrentQueries; i++) {
        promises.push(
          voiceService.getVoiceEventsPaginated(
            { eventType: ['receiving'] }, 
            Math.floor(i / 5) + 1, // Different pages
            10
          )
        );
      }
      
      await Promise.allSettled(promises);
      const endTime = performance.now();
      
      const avgQueryTime = (endTime - startTime) / concurrentQueries;
      expect(avgQueryTime).toBeLessThan(PERFORMANCE_THRESHOLDS.DATABASE_QUERY_LATENCY * 2);
    });
  });

  describe('Memory and Resource Management', () => {
    it('should not leak memory during extended processing sessions', async () => {
      const initialMemory = process.memoryUsage().heapUsed;
      
      // Process many voice events
      for (let i = 0; i < 50; i++) {
        const audioBlob = new Blob(['mock audio data'], { type: 'audio/webm' });
        await processor.processVoiceEvent(audioBlob, 'test-user');
        
        // Force garbage collection every 10 iterations
        if (i % 10 === 0 && global.gc) {
          global.gc();
        }
      }
      
      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;
      
      // Memory increase should be reasonable (less than 50MB)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
    });

    it('should clean up performance monitoring data appropriately', async () => {
      performanceMonitor.clear();
      
      // Generate many metrics
      for (let i = 0; i < 1000; i++) {
        performanceMonitor.recordMetric('test.metric', Math.random() * 100);
      }
      
      const initialCount = performanceMonitor.getMetricsCount();
      expect(initialCount).toBeGreaterThan(0);
      
      // Wait for cleanup (if implemented with intervals)
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Should not grow indefinitely
      expect(performanceMonitor.getMetricsCount()).toBeLessThanOrEqual(10000);
    });

    it('should handle large audio files without memory issues', async () => {
      const largeAudioBlob = new Blob(
        [new ArrayBuffer(1024 * 1024)], // 1MB
        { type: 'audio/webm' }
      );
      
      const startMemory = process.memoryUsage().heapUsed;
      
      await processor.processVoiceEvent(largeAudioBlob, 'test-user');
      
      const endMemory = process.memoryUsage().heapUsed;
      const memoryDelta = endMemory - startMemory;
      
      // Should not use excessive memory relative to file size
      expect(memoryDelta).toBeLessThan(largeAudioBlob.size * 3);
    });
  });

  describe('Performance Degradation Detection', () => {
    it('should detect when processing times exceed thresholds', async () => {
      performanceMonitor.clear();
      
      // Simulate slow processing
      const slowAudioBlob = new Blob(
        [new ArrayBuffer(10 * 1024 * 1024)], // Very large file
        { type: 'audio/webm' }
      );
      
      // Mock slow API response
      const mockAPI = new MockOpenAIAPI();
      mockAPI.setLatency(3000); // 3 seconds
      
      const startTime = performance.now();
      await processor.processVoiceEvent(slowAudioBlob, 'test-user');
      const endTime = performance.now();
      
      const processingTime = endTime - startTime;
      
      // Should detect performance issue
      if (processingTime > PERFORMANCE_THRESHOLDS.VOICE_PROCESSING_TOTAL) {
        const stats = performanceMonitor.getDashboardData();
        expect(stats.alertCount).toBeGreaterThan(0);
      }
    });

    it('should track performance trends over time', async () => {
      performanceMonitor.clear();
      
      const baselineLatencies: number[] = [];
      const stressedLatencies: number[] = [];
      
      // Establish baseline
      for (let i = 0; i < 5; i++) {
        const audioBlob = new Blob(['baseline test'], { type: 'audio/webm' });
        const startTime = performance.now();
        await processor.processVoiceEvent(audioBlob, 'test-user');
        const endTime = performance.now();
        baselineLatencies.push(endTime - startTime);
      }
      
      // Simulate system under stress
      for (let i = 0; i < 5; i++) {
        const audioBlob = new Blob(['stress test'], { type: 'audio/webm' });
        
        // Add concurrent load
        const concurrentPromises = [];
        for (let j = 0; j < 3; j++) {
          concurrentPromises.push(
            processor.processVoiceEvent(
              new Blob(['concurrent'], { type: 'audio/webm' }), 
              `concurrent-${j}`
            )
          );
        }
        
        const startTime = performance.now();
        await Promise.all([
          processor.processVoiceEvent(audioBlob, 'test-user'),
          ...concurrentPromises
        ]);
        const endTime = performance.now();
        stressedLatencies.push(endTime - startTime);
      }
      
      const baselineAvg = baselineLatencies.reduce((sum, lat) => sum + lat, 0) / baselineLatencies.length;
      const stressedAvg = stressedLatencies.reduce((sum, lat) => sum + lat, 0) / stressedLatencies.length;
      
      // Performance degradation should be detectable but not excessive
      expect(stressedAvg).toBeGreaterThan(baselineAvg);
      expect(stressedAvg).toBeLessThan(baselineAvg * 3); // No more than 3x degradation
    });
  });

  describe('Performance Reporting', () => {
    it('should generate comprehensive performance reports', async () => {
      performanceMonitor.clear();
      
      // Generate test data
      await processor.processVoiceEvent(
        new Blob(['test data'], { type: 'audio/webm' }), 
        'test-user'
      );
      
      const report = performanceMonitor.getDashboardData();
      
      expect(report).toMatchObject({
        voiceProcessing: expect.objectContaining({
          averageLatency: expect.any(Number),
          p95Latency: expect.any(Number),
          p99Latency: expect.any(Number),
          throughput: expect.any(Number),
          successRate: expect.any(Number)
        }),
        systemHealth: expect.any(Number)
      });
    });

    it('should export metrics in multiple formats', async () => {
      performanceMonitor.clear();
      performanceMonitor.recordMetric('test.metric', 100);
      
      const jsonExport = performanceMonitor.exportMetrics('json');
      const prometheusExport = performanceMonitor.exportMetrics('prometheus');
      
      expect(jsonExport).toContain('metrics');
      expect(prometheusExport).toContain('voice_processing');
      
      // JSON should be valid
      expect(() => JSON.parse(jsonExport)).not.toThrow();
      
      // Prometheus should follow format
      expect(prometheusExport).toMatch(/^# HELP/m);
      expect(prometheusExport).toMatch(/^# TYPE/m);
    });
  });
});