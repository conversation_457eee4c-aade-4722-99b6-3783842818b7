# Voice Processing System Test Suite

## Overview

This comprehensive test suite validates the voice processing system of the Seafood Manager application, ensuring reliability, performance, and accuracy across all voice-related features.

## Test Architecture

### Test Levels

```
┌─────────────────┐
│   E2E Tests     │  ← Full user workflows, cross-browser
├─────────────────┤
│ Integration     │  ← Voice-to-database workflows
├─────────────────┤
│   Unit Tests    │  ← Individual components/services
└─────────────────┘
```

### Coverage Areas

- **Voice Input Processing**: Speech recognition, audio handling
- **AI Integration**: OpenAI transcription and command parsing
- **Database Operations**: Voice event CRUD operations
- **UI Components**: Voice interfaces and feedback
- **Performance**: Latency, throughput, scalability
- **Error Handling**: Network failures, permission issues
- **Cross-browser**: Compatibility across browsers and devices

## Test Structure

```
src/test/
├── __tests__/           # Legacy test files (being migrated)
├── unit/               # Unit tests
│   ├── voice-processing-components.test.ts
│   └── voice-services.test.ts
├── integration/        # Integration tests
│   ├── voice-database-comprehensive.test.ts
│   └── voice-workflow.test.ts
├── performance/        # Performance tests
│   └── voice-processing-performance.test.ts
├── e2e/               # End-to-end tests
│   └── voice-system-comprehensive.spec.ts
├── mocks/             # Test mocks and utilities
│   ├── supabase-mocks.ts
│   ├── openai-mocks.ts
│   └── voice-mocks.ts
├── utils/             # Test utilities
│   └── test-utils.tsx
├── setup.ts           # Global test setup
├── test-config.ts     # Test configuration
└── voice-test-suite.config.ts  # Voice-specific config
```

## Running Tests

### Prerequisites

```bash
# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.test
```

### Test Commands

```bash
# Run all tests
npm test

# Run specific test categories
npm run test:unit              # Unit tests only
npm run test:integration       # Integration tests only
npm run test:performance       # Performance tests only
npm run test:e2e              # E2E tests only

# Voice-specific test commands
npm run test:voice            # All voice-related tests
npm run test:supabase         # Database integration tests

# Coverage and reporting
npm run test:coverage         # Generate coverage report
npm run test:ui              # Interactive test UI
npm run test:watch           # Watch mode for development

# CI/CD commands
npm run test:ci              # Full test suite for CI
npm run test:all             # All tests including E2E
```

### Environment-Specific Testing

```bash
# Development environment (faster, more verbose)
NODE_ENV=development npm test

# CI environment (more thorough, timeouts adjusted)
CI=true npm test

# Production validation
NODE_ENV=production npm run test:ci
```

## Test Configuration

### Performance Thresholds

| Metric | Development | CI | Production |
|--------|-------------|----|-----------| 
| Voice Processing | 3s | 5s | 2s |
| Database Operations | 500ms | 1s | 300ms |
| UI Responsiveness | 100ms | 200ms | 100ms |
| Test Coverage | 80% | 85% | 90% |

### Quality Gates

- **Unit Test Coverage**: ≥85%
- **Integration Test Coverage**: ≥70%
- **E2E Test Success Rate**: ≥90%
- **Voice Processing Accuracy**: ≥95%
- **Performance Compliance**: 100%

## Test Categories

### 1. Unit Tests

Test individual components and services in isolation.

**Coverage:**
- Voice processing services
- React components (VoiceEventEditor, VoiceAssistant)
- Custom hooks (useVoiceCommands, useVoiceErrorHandling)
- Utility functions

**Example:**
```typescript
// Test voice event service
describe('VoiceEventService', () => {
  it('should create voice events with proper validation', async () => {
    const voiceEventData = {
      event_type: 'receiving',
      product_name: 'salmon',
      quantity: 25,
      unit: 'lbs',
      voice_confidence_score: 0.95,
      // ...
    };

    const result = await voiceEventService.createVoiceEvent(voiceEventData);
    expect(result.voice_confidence_score).toBe(0.95);
  });
});
```

### 2. Integration Tests

Test complete workflows from voice input to database storage.

**Coverage:**
- Voice-to-database workflows
- Real-time subscriptions
- Error handling and recovery
- Concurrent processing

**Example:**
```typescript
// Test complete voice processing workflow
it('should process high-confidence voice input end-to-end', async () => {
  // 1. Voice recognition
  const transcript = 'Received 25 pounds of salmon';
  
  // 2. AI processing
  const voiceCommand = await mockOpenAI.processTranscript(transcript);
  
  // 3. Database storage
  const savedEvent = await voiceEventService.createVoiceEvent(voiceCommand);
  
  expect(savedEvent.voice_confidence_score).toBeGreaterThan(0.9);
});
```

### 3. Performance Tests

Validate latency, throughput, and resource usage.

**Coverage:**
- Voice processing latency
- Concurrent user handling
- Memory usage patterns
- API response times

**Example:**
```typescript
// Test performance under load
it('should handle concurrent voice processing requests', async () => {
  const promises = Array(5).fill(null).map(() => processVoiceEvent());
  const results = await Promise.all(promises);
  
  results.forEach(result => {
    expect(result.duration).toBeLessThan(3000);
  });
});
```

### 4. End-to-End Tests

Test complete user workflows across different browsers.

**Coverage:**
- Full user workflows
- Cross-browser compatibility
- Mobile responsiveness
- Error scenarios

**Example:**
```typescript
// Test complete voice event creation
test('should complete voice event creation workflow', async ({ page }) => {
  await page.click('[data-testid="start-voice-input"]');
  await page.waitForSelector('[data-testid="voice-transcript"]');
  await page.click('[data-testid="confirm-voice-event"]');
  
  await expect(page.locator('[data-testid="save-success"]')).toBeVisible();
});
```

## Mocking Strategy

### OpenAI API Mocking

```typescript
// Realistic OpenAI responses
const mockOpenAI = new MockOpenAIClient({
  latencyMs: 800,
  responseQuality: 'high'
});

// Simulate different confidence levels
mockOpenAI.setResponseQuality('medium'); // 60-80% confidence
mockOpenAI.setShouldFail(true, 'rate_limit'); // Test error handling
```

### Supabase Database Mocking

```typescript
// In-memory database with realistic operations
const mockDatabase = createMockSupabaseClient({
  latencyMs: 100,
  shouldFail: false
});

// Simulate network issues
mockNetworkIssue('timeout');
setPerformanceSimulation(true, 500);
```

### Browser API Mocking

```typescript
// Mock speech recognition
global.SpeechRecognition = MockSpeechRecognition;
global.MediaRecorder = MockMediaRecorder;

// Mock getUserMedia
navigator.mediaDevices.getUserMedia = mockGetUserMedia;
```

## Test Data

### Voice Test Scenarios

```typescript
// High confidence scenarios
const highConfidenceScenarios = [
  'Received 25 pounds of fresh salmon from Ocean Fresh Seafoods',
  'Sold 12 dungeness crabs to Marina Restaurant',
  'Disposed of 5 pounds of old cod due to spoilage'
];

// Medium confidence scenarios
const mediumConfidenceScenarios = [
  'Got some salmon from the dock',
  'Sold twenty crabs to restaurant',
  'Threw away bad fish'
];

// Low confidence scenarios
const lowConfidenceScenarios = [
  'Something about... fish... from...',
  'Can\'t hear clearly... received... salmon',
  'Bad audio quality... sold... restaurant'
];
```

### Seafood Vocabulary

```typescript
const SEAFOOD_VOCABULARY = {
  fish: ['salmon', 'cod', 'halibut', 'tuna', 'bass'],
  shellfish: ['crab', 'lobster', 'shrimp', 'scallops'],
  mollusks: ['oysters', 'clams', 'mussels', 'squid'],
  units: ['pounds', 'lbs', 'kilograms', 'kg', 'pieces', 'dozen'],
  vendors: ['Ocean Fresh Seafoods', 'Pacific Catch', 'Northwest Seafood'],
  customers: ['Marina Restaurant', 'The Fish House', 'Ocean View Cafe']
};
```

## Continuous Integration

### GitHub Actions Workflow

The CI pipeline runs comprehensive tests on every pull request and push:

1. **Setup Phase**: Environment validation, dependency installation
2. **Unit Tests**: Parallel execution across test groups
3. **Integration Tests**: Database setup and workflow testing
4. **Performance Tests**: Latency and throughput validation
5. **E2E Tests**: Cross-browser testing with Playwright
6. **Quality Gates**: Coverage and success rate validation

### Test Artifacts

- Test results (JSON, HTML reports)
- Coverage reports (HTML, XML, JSON)
- Performance profiles
- E2E test traces and screenshots
- Voice processing accuracy metrics

### Quality Gates

```yaml
quality_gates:
  unit_test_coverage: 85%
  integration_test_coverage: 70%
  e2e_test_success_rate: 90%
  voice_processing_accuracy: 95%
  performance_compliance: 100%
```

## Best Practices

### Writing Tests

1. **Follow AAA Pattern**: Arrange, Act, Assert
2. **Use Descriptive Names**: Tests should read like specifications
3. **Test One Thing**: Each test should validate a single behavior
4. **Mock External Dependencies**: Use realistic but controlled mocks
5. **Clean Up**: Ensure tests don't affect each other

### Performance Testing

1. **Set Realistic Thresholds**: Based on production requirements
2. **Test Under Load**: Simulate realistic user concurrency
3. **Monitor Resources**: Track memory, CPU, and network usage
4. **Isolate Performance Tests**: Run separately from functional tests

### E2E Testing

1. **Test Critical Paths**: Focus on high-value user workflows
2. **Use Page Object Model**: Organize page interactions
3. **Handle Async Operations**: Wait for elements and operations
4. **Test Error Scenarios**: Validate error handling and recovery

## Debugging Tests

### Common Issues

1. **Timing Issues**: Use proper wait conditions
2. **Mock Setup**: Verify mocks are configured correctly
3. **Environment Differences**: Check environment-specific config
4. **Test Isolation**: Ensure tests don't share state

### Debugging Tools

```bash
# Run specific test with debug output
npm test -- --testNamePattern="voice processing" --verbose

# Debug E2E tests with browser UI
npx playwright test --debug

# Generate test coverage with details
npm run test:coverage -- --reporter=verbose

# Run tests in watch mode for development
npm run test:watch
```

### Logging and Monitoring

```typescript
// Enable detailed logging in development
if (process.env.NODE_ENV === 'development') {
  console.log('Voice processing performance:', metrics);
}

// Performance monitoring
const { duration } = await measurePerformance(voiceOperation);
expect(duration).toBeLessThan(PERFORMANCE_THRESHOLD);
```

## Contributing

### Adding New Tests

1. Identify the appropriate test level (unit/integration/e2e)
2. Follow existing naming conventions
3. Update test configuration if needed
4. Add documentation for complex test scenarios
5. Ensure tests pass in CI environment

### Test Guidelines

1. **Use TypeScript**: All tests should be strongly typed
2. **Follow Patterns**: Use existing test utilities and patterns
3. **Document Complex Logic**: Add comments for non-obvious test setup
4. **Validate Edge Cases**: Test both success and failure scenarios
5. **Performance Aware**: Consider test execution time

### Review Checklist

- [ ] Tests follow established patterns
- [ ] Appropriate mocking strategy used
- [ ] Performance implications considered
- [ ] Error scenarios covered
- [ ] Documentation updated
- [ ] CI pipeline passes

## Resources

### Documentation

- [Vitest Documentation](https://vitest.dev/)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Playwright Documentation](https://playwright.dev/)
- [Jest DOM Matchers](https://github.com/testing-library/jest-dom)

### Project-Specific

- [Voice Processing Architecture](../docs/voice-processing-architecture.md)
- [Database Schema](../docs/database-schema.md)
- [API Documentation](../docs/api-documentation.md)
- [Performance Requirements](../docs/performance-requirements.md)

---

*This test suite ensures the voice processing system meets all functional, performance, and quality requirements for production deployment.*