/**
 * MSW handlers for Vendor Report Card System API mocking
 * Provides realistic API responses for testing vendor components
 */

import { http, HttpResponse } from 'msw';
import {
  mockVendorDashboardSummary,
  mockVendorMetrics,
  mockVendorRatings,
  mockVendorCompliance,
  mockVendorPerformanceAlerts,
  mockVendorInteractions,
  mockVendor,
  generateVendorDashboardSummary,
  generateVendorMetrics,
  generateVendorAlert,
  mockApiErrors
} from './vendor-data';

const SUPABASE_URL = process.env.VITE_SUPABASE_URL || 'https://test.supabase.co';

export const vendorHandlers = [
  // ===== VENDOR DASHBOARD SUMMARY =====
  
  // Get vendor dashboard summary - RPC call
  http.post(`${SUPABASE_URL}/rest/v1/rpc/get_vendor_dashboard_summary`, () => {
    return HttpResponse.json(mockVendorDashboardSummary);
  }),

  // Refresh vendor dashboard summary - RPC call
  http.post(`${SUPABASE_URL}/rest/v1/rpc/refresh_vendor_dashboard_summary`, () => {
    return HttpResponse.json({ success: true });
  }),

  // ===== VENDOR BASIC INFO =====
  
  // Get vendor by ID
  http.get(`${SUPABASE_URL}/rest/v1/vendors`, ({ request }) => {
    const url = new URL(request.url);
    const vendorId = url.searchParams.get('id');
    
    if (vendorId === 'vendor-001') {
      return HttpResponse.json([mockVendor]);
    }
    
    return HttpResponse.json([{
      id: vendorId,
      name: 'Test Vendor',
      contact_person: 'Test Contact',
      email: '<EMAIL>',
      phone: '(*************',
      created_at: '2025-01-01T00:00:00Z'
    }]);
  }),

  // ===== VENDOR METRICS =====
  
  // Get current vendor metrics
  http.get(`${SUPABASE_URL}/rest/v1/vendor_metrics`, ({ request }) => {
    const url = new URL(request.url);
    const vendorId = url.searchParams.get('vendor_id');
    const period = url.searchParams.get('calculation_period') || 'last_30_days';
    
    if (vendorId === 'vendor-001') {
      return HttpResponse.json([mockVendorMetrics]);
    }
    
    // Return generated metrics for test vendors
    const testMetrics = generateVendorMetrics({
      vendor_id: vendorId || 'test-vendor',
      calculation_period: period
    });
    
    return HttpResponse.json([testMetrics]);
  }),

  // Calculate vendor metrics - RPC call
  http.post(`${SUPABASE_URL}/rest/v1/rpc/calculate_vendor_metrics`, () => {
    return HttpResponse.json({ success: true });
  }),

  // ===== VENDOR INTERACTIONS =====
  
  // Get vendor interactions
  http.get(`${SUPABASE_URL}/rest/v1/vendor_interactions`, ({ request }) => {
    const url = new URL(request.url);
    const vendorId = url.searchParams.get('vendor_id');
    
    if (vendorId === 'vendor-001') {
      return HttpResponse.json(mockVendorInteractions);
    }
    
    return HttpResponse.json([]);
  }),

  // Create vendor interaction
  http.post(`${SUPABASE_URL}/rest/v1/vendor_interactions`, async ({ request }) => {
    const body = await request.json() as any;
    
    const newInteraction = {
      id: 'new-interaction-' + Date.now(),
      ...body,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    return HttpResponse.json([newInteraction]);
  }),

  // Update vendor interaction
  http.patch(`${SUPABASE_URL}/rest/v1/vendor_interactions`, async ({ request }) => {
    const body = await request.json() as any;
    const url = new URL(request.url);
    const id = url.searchParams.get('id');
    
    return HttpResponse.json([{
      id,
      ...body,
      updated_at: new Date().toISOString()
    }]);
  }),

  // ===== VENDOR RATINGS =====
  
  // Get vendor ratings
  http.get(`${SUPABASE_URL}/rest/v1/vendor_ratings`, ({ request }) => {
    const url = new URL(request.url);
    const vendorId = url.searchParams.get('vendor_id');
    
    if (vendorId === 'vendor-001') {
      return HttpResponse.json(mockVendorRatings);
    }
    
    return HttpResponse.json([]);
  }),

  // Create vendor rating
  http.post(`${SUPABASE_URL}/rest/v1/vendor_ratings`, async ({ request }) => {
    const body = await request.json() as any;
    
    const newRating = {
      id: 'new-rating-' + Date.now(),
      ...body,
      rating_date: new Date().toISOString(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    return HttpResponse.json([newRating]);
  }),

  // ===== VENDOR COMPLIANCE =====
  
  // Get vendor compliance
  http.get(`${SUPABASE_URL}/rest/v1/vendor_compliance`, ({ request }) => {
    const url = new URL(request.url);
    const vendorId = url.searchParams.get('vendor_id');
    
    if (vendorId === 'vendor-001') {
      return HttpResponse.json([mockVendorCompliance]);
    }
    
    return HttpResponse.json([]);
  }),

  // Create vendor compliance record
  http.post(`${SUPABASE_URL}/rest/v1/vendor_compliance`, async ({ request }) => {
    const body = await request.json() as any;
    
    const newCompliance = {
      id: 'new-compliance-' + Date.now(),
      ...body,
      assessed_date: new Date().toISOString(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    return HttpResponse.json([newCompliance]);
  }),

  // ===== VENDOR PERFORMANCE ALERTS =====
  
  // Get vendor performance alerts
  http.get(`${SUPABASE_URL}/rest/v1/vendor_performance_alerts`, ({ request }) => {
    const url = new URL(request.url);
    const vendorId = url.searchParams.get('vendor_id');
    const status = url.searchParams.get('status');
    
    let alerts = mockVendorPerformanceAlerts;
    
    if (vendorId) {
      alerts = alerts.filter(alert => alert.vendor_id === vendorId);
    }
    
    if (status) {
      alerts = alerts.filter(alert => alert.status === status);
    }
    
    return HttpResponse.json(alerts);
  }),

  // Update vendor performance alert (acknowledge/resolve)
  http.patch(`${SUPABASE_URL}/rest/v1/vendor_performance_alerts`, async ({ request }) => {
    const body = await request.json() as any;
    const url = new URL(request.url);
    const id = url.searchParams.get('id');
    
    return HttpResponse.json([{
      id,
      ...body,
      updated_at: new Date().toISOString()
    }]);
  }),

  // Check vendor performance alerts - RPC call
  http.post(`${SUPABASE_URL}/rest/v1/rpc/check_vendor_performance_alerts`, () => {
    return HttpResponse.json({ success: true });
  }),

  // ===== ERROR SCENARIOS =====
  
  // Network error simulation
  http.get(`${SUPABASE_URL}/rest/v1/vendor_error_test`, () => {
    return HttpResponse.error();
  }),

  // Server error simulation
  http.get(`${SUPABASE_URL}/rest/v1/vendor_server_error`, () => {
    return HttpResponse.json(
      { error: 'Internal server error', code: 'INTERNAL_ERROR' },
      { status: 500 }
    );
  }),

  // Unauthorized error simulation
  http.get(`${SUPABASE_URL}/rest/v1/vendor_unauthorized`, () => {
    return HttpResponse.json(
      { error: 'Unauthorized', code: 'UNAUTHORIZED' },
      { status: 401 }
    );
  }),

  // Validation error simulation
  http.post(`${SUPABASE_URL}/rest/v1/vendor_validation_error`, () => {
    return HttpResponse.json(
      { 
        error: 'Validation failed', 
        code: 'VALIDATION_ERROR',
        details: 'Missing required field: vendor_id'
      },
      { status: 422 }
    );
  })
];

// ===== DYNAMIC HANDLERS FOR TESTING =====

export function createVendorHandlersWithData(customData: {
  dashboardSummary?: any[];
  metrics?: any;
  ratings?: any[];
  compliance?: any;
  alerts?: any[];
  interactions?: any[];
}) {
  return [
    http.post(`${SUPABASE_URL}/rest/v1/rpc/get_vendor_dashboard_summary`, () => {
      return HttpResponse.json(customData.dashboardSummary || []);
    }),
    
    http.get(`${SUPABASE_URL}/rest/v1/vendor_metrics`, () => {
      return HttpResponse.json(customData.metrics ? [customData.metrics] : []);
    }),
    
    http.get(`${SUPABASE_URL}/rest/v1/vendor_ratings`, () => {
      return HttpResponse.json(customData.ratings || []);
    }),
    
    http.get(`${SUPABASE_URL}/rest/v1/vendor_compliance`, () => {
      return HttpResponse.json(customData.compliance ? [customData.compliance] : []);
    }),
    
    http.get(`${SUPABASE_URL}/rest/v1/vendor_performance_alerts`, () => {
      return HttpResponse.json(customData.alerts || []);
    }),
    
    http.get(`${SUPABASE_URL}/rest/v1/vendor_interactions`, () => {
      return HttpResponse.json(customData.interactions || []);
    })
  ];
}

// ===== HANDLERS FOR SPECIFIC TEST SCENARIOS =====

export const vendorErrorHandlers = [
  // Dashboard loading error
  http.post(`${SUPABASE_URL}/rest/v1/rpc/get_vendor_dashboard_summary`, () => {
    return HttpResponse.json(
      { error: 'Failed to load dashboard data' },
      { status: 500 }
    );
  }),
  
  // Metrics calculation error
  http.post(`${SUPABASE_URL}/rest/v1/rpc/calculate_vendor_metrics`, () => {
    return HttpResponse.json(
      { error: 'Failed to calculate metrics' },
      { status: 500 }
    );
  })
];

export const vendorEmptyHandlers = [
  // Empty dashboard
  http.post(`${SUPABASE_URL}/rest/v1/rpc/get_vendor_dashboard_summary`, () => {
    return HttpResponse.json([]);
  }),
  
  // No metrics data
  http.get(`${SUPABASE_URL}/rest/v1/vendor_metrics`, () => {
    return HttpResponse.json([]);
  }),
  
  // No alerts
  http.get(`${SUPABASE_URL}/rest/v1/vendor_performance_alerts`, () => {
    return HttpResponse.json([]);
  })
];

export const vendorLoadingHandlers = [
  // Slow dashboard loading
  http.post(`${SUPABASE_URL}/rest/v1/rpc/get_vendor_dashboard_summary`, async () => {
    await new Promise(resolve => setTimeout(resolve, 2000));
    return HttpResponse.json(mockVendorDashboardSummary);
  })
];