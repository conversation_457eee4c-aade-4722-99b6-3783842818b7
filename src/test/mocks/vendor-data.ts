/**
 * Mock data generators for Vendor Report Card System testing
 * Provides realistic test data for all vendor components and API operations
 */

import { 
  VendorDashboardSummary, 
  VendorMetrics, 
  VendorRating, 
  VendorCompliance, 
  VendorPerformanceAlert, 
  VendorInteraction 
} from '../../types/schema';

// ===== VENDOR DASHBOARD SUMMARY MOCKS =====

export const mockVendorDashboardSummary: VendorDashboardSummary[] = [
  {
    vendor_id: 'vendor-001',
    vendor_name: 'Pacific Seafood Supply',
    contact_name: '<PERSON>',
    email: '<EMAIL>',
    phone: '(*************',
    overall_letter_grade: 'A',
    completion_rate: 96.5,
    on_time_delivery_rate: 94.2,
    average_quality_score: 8.7,
    total_interactions: 45,
    active_alerts_count: 0,
    last_delivery_date: '2025-08-10T14:30:00Z',
    last_delivery_status: 'completed'
  },
  {
    vendor_id: 'vendor-002',
    vendor_name: 'Alaska Premium Fish',
    contact_name: '<PERSON>',
    email: '<EMAIL>',
    phone: '(*************',
    overall_letter_grade: 'B',
    completion_rate: 87.3,
    on_time_delivery_rate: 82.1,
    average_quality_score: 7.9,
    total_interactions: 32,
    active_alerts_count: 2,
    last_delivery_date: '2025-08-12T09:15:00Z',
    last_delivery_status: 'completed'
  },
  {
    vendor_id: 'vendor-003',
    vendor_name: 'Atlantic Imports LLC',
    contact_name: 'Emma Wilson',
    email: '<EMAIL>',
    phone: '(*************',
    overall_letter_grade: 'D',
    completion_rate: 65.8,
    on_time_delivery_rate: 58.3,
    average_quality_score: 6.2,
    total_interactions: 28,
    active_alerts_count: 5,
    last_delivery_date: '2025-08-08T16:45:00Z',
    last_delivery_status: 'partial'
  }
];

// ===== VENDOR METRICS MOCKS =====

export const mockVendorMetrics: VendorMetrics = {
  id: 'metrics-001',
  vendor_id: 'vendor-001',
  period_type: 'monthly',
  period_start: '2025-07-14T00:00:00Z',
  period_end: '2025-08-14T23:59:59Z',
  calculation_period: 'last_30_days',
  
  // Performance Metrics
  total_interactions: 45,
  successful_deliveries: 43,
  completion_rate: 95.6,
  on_time_delivery_rate: 94.2,
  early_delivery_rate: 12.5,
  late_delivery_rate: 5.8,
  
  // Quality Metrics
  average_quality_score: 8.7,
  quality_variance: 0.8,
  defect_rate: 2.1,
  
  // Financial Metrics
  total_order_value: 125000.00,
  total_delivered_value: 119500.00,
  average_order_value: 2777.78,
  price_variance_percentage: -2.1,
  
  // Issue Resolution
  total_issues_reported: 3,
  resolved_issues: 3,
  issue_resolution_rate: 100.0,
  average_resolution_time_hours: 4.5,
  
  // Compliance Metrics
  documentation_completion_rate: 98.5,
  temperature_compliance_rate: 100.0,
  certification_current: true,
  
  // Calculated Fields
  overall_score: 87.3,
  overall_letter_grade: 'A',
  calculated_at: '2025-08-14T12:00:00Z',
  
  // Metadata
  created_at: '2025-08-14T12:00:00Z',
  updated_at: '2025-08-14T12:00:00Z'
};

export const mockVendorMetricsHistory: VendorMetrics[] = [
  { ...mockVendorMetrics, calculated_at: '2025-08-14T12:00:00Z', overall_score: 87.3 },
  { ...mockVendorMetrics, calculated_at: '2025-07-14T12:00:00Z', overall_score: 85.1 },
  { ...mockVendorMetrics, calculated_at: '2025-06-14T12:00:00Z', overall_score: 83.7 }
];

// ===== VENDOR RATINGS MOCKS =====

export const mockVendorRatings: VendorRating[] = [
  {
    id: 'rating-001',
    vendor_interaction_id: 'interaction-001',
    vendor_id: 'vendor-001',
    quality_score: 9,
    delivery_timeliness_score: 8,
    order_accuracy_score: 10,
    communication_score: 8,
    price_competitiveness_score: 7,
    issue_resolution_score: 9,
    overall_satisfaction: 8.5,
    quality_notes: 'Excellent fresh salmon, perfect temperature',
    delivery_notes: 'Slightly delayed but good communication',
    would_reorder: true,
    would_recommend: true,
    letter_grade: 'A',
    rated_by: 'manager-001',
    rating_date: '2025-08-10T15:00:00Z',
    created_at: '2025-08-10T15:00:00Z'
  },
  {
    id: 'rating-002',
    vendor_interaction_id: 'interaction-002',
    vendor_id: 'vendor-001',
    quality_score: 8,
    delivery_timeliness_score: 9,
    order_accuracy_score: 9,
    communication_score: 9,
    price_competitiveness_score: 8,
    issue_resolution_score: 8,
    overall_satisfaction: 8.5,
    quality_notes: 'Good quality crab, slight ice damage on 2 units',
    delivery_notes: 'On time delivery, professional driver',
    would_reorder: true,
    would_recommend: true,
    letter_grade: 'B',
    rated_by: 'manager-001',
    rating_date: '2025-08-08T11:30:00Z',
    created_at: '2025-08-08T11:30:00Z'
  }
];

// ===== VENDOR COMPLIANCE MOCKS =====

export const mockVendorCompliance: VendorCompliance = {
  id: 'compliance-001',
  vendor_id: 'vendor-001',
  
  // HACCP Compliance
  haccp_certified: true,
  haccp_cert_expiry_date: '2025-12-31',
  temperature_logs_complete: true,
  critical_control_points_met: true,
  sanitation_standards_met: true,
  
  // GDST Traceability
  catch_certificate_provided: true,
  chain_of_custody_complete: true,
  species_verification_done: true,
  fishing_vessel_documented: true,
  harvest_location_verified: true,
  
  // Documentation
  required_permits_current: true,
  insurance_current: true,
  business_license_current: true,
  
  // Calculated Compliance
  compliance_score: 92,
  compliance_status: 'compliant',
  
  // Assessment Info
  assessed_by: 'auditor-001',
  assessed_date: '2025-08-01T10:00:00Z',
  next_assessment_due: '2025-11-01T10:00:00Z',
  notes: 'All compliance requirements met. Excellent documentation.',
  
  // Metadata
  created_at: '2025-08-01T10:00:00Z',
  updated_at: '2025-08-01T10:00:00Z'
};

// ===== VENDOR PERFORMANCE ALERTS MOCKS =====

export const mockVendorPerformanceAlerts: VendorPerformanceAlert[] = [
  {
    id: 'alert-001',
    vendor_id: 'vendor-002',
    alert_type: 'performance_degradation',
    severity: 'medium',
    title: 'Declining On-Time Delivery Rate',
    description: 'On-time delivery rate has dropped below 85% threshold in the last 2 weeks',
    triggered_by_metric: 'on_time_delivery_rate',
    threshold_value: 85.0,
    actual_value: 82.1,
    
    // Status
    status: 'active',
    acknowledged: false,
    
    // Timing
    created_at: '2025-08-12T08:00:00Z',
    updated_at: '2025-08-12T08:00:00Z'
  },
  {
    id: 'alert-002',
    vendor_id: 'vendor-003',
    alert_type: 'compliance_issue',
    severity: 'high',
    title: 'HACCP Certification Expiring Soon',
    description: 'HACCP certification expires in 15 days',
    triggered_by_metric: 'haccp_cert_expiry_date',
    
    // Status
    status: 'active',
    acknowledged: true,
    acknowledged_by: 'manager-001',
    acknowledged_at: '2025-08-13T09:30:00Z',
    
    // Timing
    created_at: '2025-08-13T08:00:00Z',
    updated_at: '2025-08-13T09:30:00Z'
  },
  {
    id: 'alert-003',
    vendor_id: 'vendor-003',
    alert_type: 'quality_issue',
    severity: 'critical',
    title: 'Multiple Quality Failures',
    description: 'Quality score below 7.0 for 3 consecutive deliveries',
    triggered_by_metric: 'average_quality_score',
    threshold_value: 7.0,
    actual_value: 6.2,
    
    // Status
    status: 'active',
    acknowledged: false,
    
    // Timing
    created_at: '2025-08-11T14:30:00Z',
    updated_at: '2025-08-11T14:30:00Z'
  }
];

// ===== VENDOR INTERACTIONS MOCKS =====

export const mockVendorInteractions: VendorInteraction[] = [
  {
    id: 'interaction-001',
    vendor_id: 'vendor-001',
    interaction_type: 'delivery',
    po_number: 'PO-2025-001',
    order_date: '2025-08-08T09:00:00Z',
    expected_delivery_date: '2025-08-10T14:00:00Z',
    actual_delivery_date: '2025-08-10T14:30:00Z',
    order_total_amount: 5500.00,
    delivered_amount: 5500.00,
    status: 'completed',
    temperature_compliant: true,
    documentation_complete: true,
    product_condition: 'excellent',
    notes: 'Perfect delivery, all items in excellent condition',
    is_on_time: true,
    is_complete_delivery: true,
    created_at: '2025-08-08T09:00:00Z'
  },
  {
    id: 'interaction-002',
    vendor_id: 'vendor-002',
    interaction_type: 'delivery',
    po_number: 'PO-2025-002',
    order_date: '2025-08-10T10:00:00Z',
    expected_delivery_date: '2025-08-12T08:00:00Z',
    actual_delivery_date: '2025-08-12T09:15:00Z',
    order_total_amount: 3200.00,
    delivered_amount: 3200.00,
    status: 'completed',
    temperature_compliant: true,
    documentation_complete: true,
    product_condition: 'good',
    notes: 'Delivery slightly delayed but all items acceptable',
    is_on_time: false,
    is_complete_delivery: true,
    created_at: '2025-08-10T10:00:00Z'
  }
];

// ===== MOCK VENDOR BASIC INFO =====

export const mockVendor = {
  id: 'vendor-001',
  name: 'Pacific Seafood Supply',
  contact_person: 'Sarah Johnson',
  email: '<EMAIL>',
  phone: '(*************',
  address: '123 Harbor Way, Seattle, WA 98101',
  created_at: '2025-01-01T00:00:00Z'
};

// ===== TEST DATA GENERATORS =====

export function generateVendorDashboardSummary(overrides: Partial<VendorDashboardSummary> = {}): VendorDashboardSummary {
  return {
    vendor_id: 'test-vendor',
    vendor_name: 'Test Vendor',
    contact_name: 'Test Contact',
    email: '<EMAIL>',
    phone: '(*************',
    overall_letter_grade: 'B',
    completion_rate: 85.0,
    on_time_delivery_rate: 80.0,
    average_quality_score: 7.5,
    total_interactions: 10,
    active_alerts_count: 1,
    last_delivery_date: '2025-08-14T12:00:00Z',
    last_delivery_status: 'completed',
    ...overrides
  };
}

export function generateVendorMetrics(overrides: Partial<VendorMetrics> = {}): VendorMetrics {
  return {
    id: 'test-metrics',
    vendor_id: 'test-vendor',
    period_type: 'monthly',
    period_start: '2025-07-14T00:00:00Z',
    period_end: '2025-08-14T23:59:59Z',
    calculation_period: 'last_30_days',
    total_interactions: 10,
    successful_deliveries: 8,
    completion_rate: 80.0,
    on_time_delivery_rate: 75.0,
    average_quality_score: 7.0,
    total_order_value: 50000.00,
    total_delivered_value: 48000.00,
    average_order_value: 5000.00,
    issue_resolution_rate: 90.0,
    average_resolution_time_hours: 6.0,
    overall_score: 75.0,
    overall_letter_grade: 'B',
    calculated_at: '2025-08-14T12:00:00Z',
    created_at: '2025-08-14T12:00:00Z',
    updated_at: '2025-08-14T12:00:00Z',
    ...overrides
  };
}

export function generateVendorAlert(overrides: Partial<VendorPerformanceAlert> = {}): VendorPerformanceAlert {
  return {
    id: 'test-alert',
    vendor_id: 'test-vendor',
    alert_type: 'performance_degradation',
    severity: 'medium',
    title: 'Test Alert',
    description: 'This is a test alert',
    status: 'active',
    acknowledged: false,
    created_at: '2025-08-14T12:00:00Z',
    updated_at: '2025-08-14T12:00:00Z',
    ...overrides
  };
}

// ===== ERROR SCENARIOS =====

export const mockApiErrors = {
  networkError: new Error('Network request failed'),
  unauthorizedError: new Error('Unauthorized access'),
  serverError: new Error('Internal server error'),
  validationError: new Error('Validation failed: missing required fields')
};

// ===== EMPTY STATE MOCKS =====

export const mockEmptyStates = {
  emptyVendorList: [],
  emptyAlertsList: [],
  emptyRatingsList: [],
  noMetricsData: null,
  noComplianceData: null
};