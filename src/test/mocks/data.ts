// Mock data for testing Seafood Manager application

export const mockSupabaseData = {
  users: [
    {
      id: 'user-123',
      email: '<EMAIL>',
      created_at: '2024-01-01T00:00:00Z',
      app_metadata: {},
      user_metadata: {
        name: 'Test User',
        role: 'inventory_manager'
      }
    }
  ],

  categories: [
    {
      id: 'cat-fresh-fish',
      name: 'Fresh Fish',
      description: 'Fresh fish products',
      created_at: '2024-01-01T00:00:00Z'
    },
    {
      id: 'cat-frozen-fish',
      name: 'Frozen Fish',
      description: 'Frozen fish products',
      created_at: '2024-01-01T00:00:00Z'
    },
    {
      id: 'cat-shellfish',
      name: 'Shellfish',
      description: 'Fresh and frozen shellfish',
      created_at: '2024-01-01T00:00:00Z'
    },
    {
      id: 'cat-processed',
      name: 'Processed',
      description: 'Processed seafood products',
      created_at: '2024-01-01T00:00:00Z'
    }
  ],

  products: [
    {
      id: 'prod-salmon-atlantic',
      name: 'Atlantic Salmon',
      category_id: 'cat-fresh-fish',
      species: 'Salmo salar',
      origin: 'Norway',
      sustainability_rating: 'A',
      storage_requirements: 'Keep refrigerated at 32-38°F',
      allergens: ['Fish'],
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    },
    {
      id: 'prod-crab-dungeness',
      name: 'Dungeness Crab',
      category_id: 'cat-shellfish',
      species: 'Cancer magister',
      origin: 'Alaska',
      sustainability_rating: 'A+',
      storage_requirements: 'Keep live in saltwater tank or refrigerated',
      allergens: ['Shellfish'],
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    },
    {
      id: 'prod-halibut-pacific',
      name: 'Pacific Halibut',
      category_id: 'cat-fresh-fish',
      species: 'Hippoglossus stenolepis',
      origin: 'Alaska',
      sustainability_rating: 'A',
      storage_requirements: 'Keep refrigerated at 32-38°F',
      allergens: ['Fish'],
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    }
  ],

  inventoryEvents: [
    {
      id: 'event-001',
      event_type: 'receiving',
      product_id: 'prod-salmon-atlantic',
      quantity: 100,
      unit: 'lbs',
      unit_cost: 12.50,
      vendor_invoice: 'INV-2024-001',
      batch_number: 'SAL-001-2024',
      catch_date: '2024-01-15',
      created_at: '2024-01-16T10:00:00Z',
      notes: 'Fresh delivery from Norway supplier'
    },
    {
      id: 'event-002',
      event_type: 'sales',
      product_id: 'prod-salmon-atlantic',
      quantity: -25,
      unit: 'lbs',
      unit_price: 18.00,
      customer_order: 'ORD-2024-001',
      batch_number: 'SAL-001-2024',
      created_at: '2024-01-17T14:30:00Z',
      notes: 'Sold to restaurant client'
    },
    {
      id: 'event-003',
      event_type: 'receiving',
      product_id: 'prod-crab-dungeness',
      quantity: 50,
      unit: 'lbs',
      unit_cost: 22.00,
      vendor_invoice: 'INV-2024-002',
      batch_number: 'CRAB-001-2024',
      catch_date: '2024-01-16',
      created_at: '2024-01-17T08:00:00Z',
      notes: 'Live crab delivery from Alaska'
    }
  ],

  haccpEvents: [
    {
      id: 'haccp-001',
      event_type: 'temperature_check',
      product_id: 'prod-salmon-atlantic',
      batch_number: 'SAL-001-2024',
      temperature: 35.5,
      temperature_unit: 'F',
      location: 'Walk-in Cooler A',
      critical_limit: 38,
      within_limits: true,
      corrective_action: null,
      inspector: 'Test User',
      created_at: '2024-01-17T06:00:00Z',
      notes: 'Morning temperature check - within limits'
    },
    {
      id: 'haccp-002',
      event_type: 'receiving_inspection',
      product_id: 'prod-crab-dungeness',
      batch_number: 'CRAB-001-2024',
      inspection_result: 'passed',
      quality_score: 95,
      defects_found: [],
      inspector: 'Test User',
      created_at: '2024-01-17T08:15:00Z',
      notes: 'All crabs active and healthy, no defects found'
    }
  ],

  calendarEvents: [
    {
      id: 'cal-001',
      title: 'Atlantic Salmon Receiving',
      event_type: 'receiving',
      product_id: 'prod-salmon-atlantic',
      start_date: '2024-01-16T10:00:00Z',
      end_date: '2024-01-16T11:00:00Z',
      batch_number: 'SAL-001-2024',
      quantity: 100,
      unit: 'lbs',
      created_at: '2024-01-16T10:00:00Z'
    },
    {
      id: 'cal-002',
      title: 'Temperature Check - Cooler A',
      event_type: 'haccp_check',
      start_date: '2024-01-17T06:00:00Z',
      end_date: '2024-01-17T06:15:00Z',
      location: 'Walk-in Cooler A',
      created_at: '2024-01-17T06:00:00Z'
    }
  ],

  customers: [
    {
      id: 'cust-001',
      name: 'Seaside Restaurant',
      email: '<EMAIL>',
      phone: '+1-************',
      address: '123 Harbor Way, Seattle, WA 98101',
      customer_type: 'restaurant',
      created_at: '2024-01-01T00:00:00Z'
    }
  ],

  vendors: [
    {
      id: 'vend-001',
      name: 'Alaska Fresh Seafood Co.',
      email: '<EMAIL>',
      phone: '******-555-0456',
      address: '456 Fisherman Wharf, Anchorage, AK 99501',
      vendor_type: 'supplier',
      certifications: ['MSC', 'HACCP'],
      created_at: '2024-01-01T00:00:00Z'
    }
  ]
};

// Helper functions for test data manipulation
export const createMockProduct = (overrides: Partial<typeof mockSupabaseData.products[0]> = {}) => ({
  ...mockSupabaseData.products[0],
  id: `prod-${Date.now()}`,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides
});

export const createMockInventoryEvent = (overrides: Partial<typeof mockSupabaseData.inventoryEvents[0]> = {}) => ({
  ...mockSupabaseData.inventoryEvents[0],
  id: `event-${Date.now()}`,
  created_at: new Date().toISOString(),
  ...overrides
});

export const createMockHACCPEvent = (overrides: Partial<typeof mockSupabaseData.haccpEvents[0]> = {}) => ({
  ...mockSupabaseData.haccpEvents[0],
  id: `haccp-${Date.now()}`,
  created_at: new Date().toISOString(),
  ...overrides
});

// Mock CSV data for import testing
export const mockCSVData = {
  validInventoryCSV: `Product Name,Quantity,Unit,Location,Batch Number,Catch Date
Atlantic Salmon,100,lbs,Freezer A,SAL-001-2024,2024-01-15
Dungeness Crab,50,lbs,Fresh Section,CRAB-001-2024,2024-01-16
Pacific Halibut,75,lbs,Freezer B,HAL-001-2024,2024-01-14`,

  invalidInventoryCSV: `Product Name,Quantity,Unit,Location
Atlantic Salmon,invalid_quantity,lbs,Freezer A
,50,lbs,Fresh Section
Pacific Halibut,75,,Freezer B`,

  largeInventoryCSV: Array.from({ length: 1000 }, (_, i) => 
    `Product ${i},${Math.floor(Math.random() * 100)},lbs,Location ${i % 10},BATCH-${i}-2024,2024-01-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`
  ).join('\n')
};

// Voice input test data
export const mockVoiceInputs = {
  validSeafoodCommands: [
    'Add fifty pounds of Atlantic salmon to freezer A batch SAL-001-2024',
    'Remove twenty-five pounds of Dungeness crab from fresh section',
    'Record temperature check for cooler A at thirty-five degrees',
    'Move forty pounds of Pacific halibut from freezer B to processing area',
    'Add HACCP inspection for batch CRAB-001-2024 quality score ninety-five'
  ],
  
  ambiguousCommands: [
    'Add some fish to the freezer',
    'Check the temperature',
    'Move the crab',
    'Record inspection'
  ],
  
  invalidCommands: [
    'Play music',
    'What is the weather today',
    'Order pizza',
    'Send email to customer'
  ]
};

// Performance test data
export const performanceTestData = {
  largeProductList: Array.from({ length: 10000 }, (_, i) => createMockProduct({
    id: `prod-perf-${i}`,
    name: `Test Product ${i}`,
    species: `Species ${i % 100}`
  })),
  
  largeInventoryEventList: Array.from({ length: 50000 }, (_, i) => createMockInventoryEvent({
    id: `event-perf-${i}`,
    quantity: Math.floor(Math.random() * 1000),
    created_at: new Date(2024, 0, Math.floor(Math.random() * 365)).toISOString()
  }))
};