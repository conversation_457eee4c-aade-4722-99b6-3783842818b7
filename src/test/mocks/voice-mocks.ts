/**
 * Mock services and data for voice event testing
 * Provides realistic test doubles for OpenAI API, browser APIs, and audio processing
 */

import { vi } from 'vitest';
import { VoiceCommand, VoiceEventData, VoiceEvent } from '../../types/schema';

// Mock audio samples for different seafood products and scenarios
export const VOICE_TEST_SAMPLES = {
  // High confidence samples
  highConfidence: {
    salmonReceiving: {
      transcript: "Received 25 pounds of fresh salmon from Ocean Fresh Seafoods",
      audioUrl: "data:audio/wav;base64,UklGRnoAAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoAAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmscEkZ+Y2NCfyxv4/DzZ2Pj2EpNqGFdY+/9/TYpn08BAAA=",
      voiceCommand: {
        action_type: 'create_event',
        event_type: 'receiving',
        product_name: 'salmon',
        quantity: 25,
        unit: 'pounds',
        vendor_name: 'Ocean Fresh Seafoods',
        confidence_score: 0.95,
        confidence_breakdown: {
          product_match: 0.98,
          quantity_extraction: 0.95,
          vendor_match: 0.93,
          event_type: 0.97,
          overall: 0.95
        },
        raw_transcript: "Received 25 pounds of fresh salmon from Ocean Fresh Seafoods"
      }
    },
    crabSales: {
      transcript: "Sold 12 dungeness crabs to Marina Restaurant",
      audioUrl: "data:audio/wav;base64,UklGRnoAAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoAAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmscEkZ+Y2NCfyxv4/DzZ2Pj2EpNqGFdY+/9/TYpn08BAAA=",
      voiceCommand: {
        action_type: 'create_event',
        event_type: 'sale',
        product_name: 'dungeness crabs',
        quantity: 12,
        unit: 'pieces',
        customer_name: 'Marina Restaurant',
        confidence_score: 0.92,
        confidence_breakdown: {
          product_match: 0.94,
          quantity_extraction: 0.93,
          customer_match: 0.90,
          event_type: 0.92,
          overall: 0.92
        },
        raw_transcript: "Sold 12 dungeness crabs to Marina Restaurant"
      }
    }
  },

  // Medium confidence samples (require confirmation)
  mediumConfidence: {
    unclearQuantity: {
      transcript: "Received some halibut from Pacific Catch",
      audioUrl: "data:audio/wav;base64,UklGRnoAAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoAAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmscEkZ+Y2NCfyxv4/DzZ2Pj2EpNqGFdY+/9/TYpn08BAAA=",
      voiceCommand: {
        action_type: 'create_event',
        event_type: 'receiving',
        product_name: 'halibut',
        quantity: 0,
        unit: 'pounds',
        vendor_name: 'Pacific Catch',
        confidence_score: 0.75,
        confidence_breakdown: {
          product_match: 0.85,
          quantity_extraction: 0.45, // Low confidence in quantity
          vendor_match: 0.88,
          event_type: 0.82,
          overall: 0.75
        },
        raw_transcript: "Received some halibut from Pacific Catch"
      }
    },
    accentVariation: {
      transcript: "Sold fifteen pound of cod to Morrison's",
      audioUrl: "data:audio/wav;base64,UklGRnoAAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoAAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmscEkZ+Y2NCfyxv4/DzZ2Pj2EpNqGFdY+/9/TYpn08BAAA=",
      voiceCommand: {
        action_type: 'create_event',
        event_type: 'sale',
        product_name: 'cod',
        quantity: 15,
        unit: 'pounds',
        customer_name: "Morrison's",
        confidence_score: 0.78,
        confidence_breakdown: {
          product_match: 0.85,
          quantity_extraction: 0.72, // Grammar variation affects confidence
          customer_match: 0.75,
          event_type: 0.80,
          overall: 0.78
        },
        raw_transcript: "Sold fifteen pound of cod to Morrison's"
      }
    }
  },

  // Low confidence samples (require manual review)
  lowConfidence: {
    noiseBackground: {
      transcript: "Received... pound... tuna... from...",
      audioUrl: "data:audio/wav;base64,UklGRnoAAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoAAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmscEkZ+Y2NCfyxv4/DzZ2Pj2EpNqGFdY+/9/TYpn08BAAA=",
      voiceCommand: {
        action_type: 'create_event',
        event_type: 'receiving',
        product_name: 'tuna',
        quantity: 0,
        unit: 'pounds',
        vendor_name: '',
        confidence_score: 0.45,
        confidence_breakdown: {
          product_match: 0.65,
          quantity_extraction: 0.20,
          vendor_match: 0.15,
          event_type: 0.70,
          overall: 0.45
        },
        raw_transcript: "Received... pound... tuna... from..."
      }
    },
    unknownSpecies: {
      transcript: "Received 20 pounds of geoduck from Northwest Seafood",
      audioUrl: "data:audio/wav;base64,UklGRnoAAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoAAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmscEkZ+Y2NCfyxv4/DzZ2Pj2EpNqGFdY+/9/TYpn08BAAA=",
      voiceCommand: {
        action_type: 'create_event',
        event_type: 'receiving',
        product_name: 'geoduck',
        quantity: 20,
        unit: 'pounds',
        vendor_name: 'Northwest Seafood',
        confidence_score: 0.55,
        confidence_breakdown: {
          product_match: 0.35, // Unusual/specialized product
          quantity_extraction: 0.85,
          vendor_match: 0.80,
          event_type: 0.75,
          overall: 0.55
        },
        raw_transcript: "Received 20 pounds of geoduck from Northwest Seafood"
      }
    }
  },

  // Error scenarios
  errors: {
    apiTimeout: {
      transcript: "API timeout simulation",
      error: new Error('OpenAI API timeout after 10 seconds'),
      type: 'NETWORK_ERROR'
    },
    invalidAudio: {
      transcript: "",
      error: new Error('Invalid audio format'),
      type: 'AUDIO_ERROR'
    },
    rateLimited: {
      transcript: "Rate limit exceeded",
      error: new Error('Rate limit exceeded: 429'),
      type: 'API_ERROR'
    }
  }
};

// Mock OpenAI API responses
export class MockOpenAIAPI {
  private shouldFail: boolean = false;
  private failureType: string = '';
  private latencyMs: number = 500;

  setShouldFail(fail: boolean, type: string = '') {
    this.shouldFail = fail;
    this.failureType = type;
  }

  setLatency(ms: number) {
    this.latencyMs = ms;
  }

  async transcribe(audioBlob: Blob): Promise<string> {
    await this.simulateLatency();

    if (this.shouldFail) {
      throw this.createMockError();
    }

    // Simulate different transcription qualities based on audio characteristics
    const audioSize = audioBlob.size;
    if (audioSize < 1000) {
      return VOICE_TEST_SAMPLES.lowConfidence.noiseBackground.transcript;
    } else if (audioSize < 5000) {
      return VOICE_TEST_SAMPLES.mediumConfidence.unclearQuantity.transcript;
    } else {
      return VOICE_TEST_SAMPLES.highConfidence.salmonReceiving.transcript;
    }
  }

  async processTranscript(transcript: string): Promise<VoiceCommand> {
    await this.simulateLatency();

    if (this.shouldFail) {
      throw this.createMockError();
    }

    // Return corresponding voice command based on transcript
    for (const category of Object.values(VOICE_TEST_SAMPLES)) {
      if (typeof category === 'object' && !Array.isArray(category)) {
        for (const sample of Object.values(category)) {
          if (sample.transcript === transcript) {
            return sample.voiceCommand;
          }
        }
      }
    }

    // Fallback for unknown transcripts
    return {
      action_type: 'create_event',
      event_type: 'receiving',
      product_name: 'unknown',
      quantity: 0,
      unit: 'pounds',
      confidence_score: 0.3,
      raw_transcript: transcript
    };
  }

  private async simulateLatency(): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, this.latencyMs));
  }

  private createMockError(): Error {
    switch (this.failureType) {
      case 'timeout':
        return new Error('OpenAI API timeout after 10 seconds');
      case 'rate_limit':
        return new Error('Rate limit exceeded: 429');
      case 'auth':
        return new Error('Invalid API key');
      case 'audio':
        return new Error('Invalid audio format');
      default:
        return new Error('Unknown API error');
    }
  }
}

// Mock browser APIs
export class MockSpeechRecognition {
  public onresult: ((event: any) => void) | null = null;
  public onerror: ((event: any) => void) | null = null;
  public onend: (() => void) | null = null;
  public continuous: boolean = false;
  public lang: string = 'en-US';

  private isListening: boolean = false;
  private shouldFail: boolean = false;
  private recognitionResult: string = '';

  constructor() {
    // Default to high confidence sample
    this.recognitionResult = VOICE_TEST_SAMPLES.highConfidence.salmonReceiving.transcript;
  }

  setRecognitionResult(result: string) {
    this.recognitionResult = result;
  }

  setShouldFail(fail: boolean) {
    this.shouldFail = fail;
  }

  start() {
    this.isListening = true;
    
    setTimeout(() => {
      if (this.shouldFail && this.onerror) {
        this.onerror({
          error: 'not-allowed',
          message: 'Microphone access denied'
        });
      } else if (this.onresult) {
        const mockEvent = {
          results: [{
            0: { transcript: this.recognitionResult },
            isFinal: true
          }],
          resultIndex: 0
        };
        this.onresult(mockEvent);
      }

      if (this.onend) {
        this.onend();
      }
      this.isListening = false;
    }, 1000);
  }

  stop() {
    this.isListening = false;
  }

  abort() {
    this.isListening = false;
  }
}

// Mock MediaRecorder
export class MockMediaRecorder {
  public ondataavailable: ((event: any) => void) | null = null;
  public onstop: (() => void) | null = null;
  public onerror: ((event: any) => void) | null = null;
  public state: string = 'inactive';

  private chunks: Blob[] = [];
  private shouldFail: boolean = false;

  constructor(stream: MediaStream, options?: MediaRecorderOptions) {
    // Mock implementation
  }

  setShouldFail(fail: boolean) {
    this.shouldFail = fail;
  }

  start() {
    this.state = 'recording';
    
    if (this.shouldFail && this.onerror) {
      this.onerror({ error: 'InvalidState', message: 'Recording failed' });
      return;
    }

    // Simulate recording data
    setTimeout(() => {
      if (this.ondataavailable) {
        const mockBlob = new Blob(['mock audio data'], { type: 'audio/webm' });
        this.ondataavailable({ data: mockBlob });
      }
    }, 100);
  }

  stop() {
    this.state = 'inactive';
    if (this.onstop) {
      this.onstop();
    }
  }
}

// Mock getUserMedia
export const mockGetUserMedia = vi.fn().mockImplementation(() => {
  return Promise.resolve(new MediaStream());
});

// Mock Web Audio API
export class MockAudioContext {
  public state: string = 'running';
  public sampleRate: number = 44100;

  createAnalyser() {
    return {
      frequencyBinCount: 1024,
      fftSize: 2048,
      getByteFrequencyData: vi.fn(),
      connect: vi.fn(),
      disconnect: vi.fn()
    };
  }

  createMediaStreamSource(stream: MediaStream) {
    return {
      connect: vi.fn(),
      disconnect: vi.fn()
    };
  }

  close() {
    this.state = 'closed';
    return Promise.resolve();
  }
}

// Test data generators
export function generateSeafoodTestData() {
  return {
    products: [
      'salmon', 'cod', 'halibut', 'tuna', 'crab', 'lobster', 'shrimp', 'scallops',
      'dungeness crab', 'king crab', 'snow crab', 'alaska pollock', 'sockeye salmon',
      'chinook salmon', 'coho salmon', 'rockfish', 'sole', 'flounder', 'mahi mahi',
      'swordfish', 'oysters', 'mussels', 'clams', 'geoduck', 'sea urchin'
    ],
    units: ['pounds', 'lbs', 'kilograms', 'kg', 'pieces', 'each', 'dozen', 'cases'],
    vendors: [
      'Ocean Fresh Seafoods', 'Pacific Catch', 'Northwest Seafood', 'Marine Harvest',
      'Trident Seafoods', 'Icicle Seafoods', 'Wild Planet', 'Fisherman\'s Market',
      'Coastal Seafood', 'Bay Point Seafood'
    ],
    customers: [
      'Marina Restaurant', 'The Fish House', 'Ocean View Cafe', 'Pier 70',
      'Morrison\'s', 'Fresh Catch Market', 'Seattle Fish Co', 'Harbor Restaurant'
    ],
    eventTypes: ['receiving', 'sale', 'disposal', 'production', 'adjustment']
  };
}

export function createMockVoiceEvent(overrides: Partial<VoiceEvent> = {}): VoiceEvent {
  const baseEvent: VoiceEvent = {
    id: 'test-event-' + Math.random().toString(36).substr(2, 9),
    event_type: 'receiving',
    quantity: 25,
    unit: 'pounds',
    notes: 'Test voice event',
    occurred_at: new Date().toISOString(),
    created_at: new Date().toISOString(),
    voice_confidence_score: 0.85,
    voice_confidence_breakdown: {
      product_match: 0.90,
      quantity_extraction: 0.85,
      vendor_match: 0.80,
      overall: 0.85
    },
    raw_transcript: 'Received 25 pounds of salmon from Ocean Fresh Seafoods',
    audio_recording_url: 'data:audio/wav;base64,mock-audio-data',
    created_by_voice: true,
    metadata: {
      product_name: 'salmon',
      vendor_name: 'Ocean Fresh Seafoods',
      voice_processed: true,
      processing_timestamp: new Date().toISOString()
    }
  };

  return { ...baseEvent, ...overrides };
}

export function generateVoiceTestScenarios() {
  const seafoodData = generateSeafoodTestData();
  const scenarios: Array<{
    name: string;
    transcript: string;
    expectedConfidence: number;
    expectedProduct: string;
    expectedQuantity: number;
    expectedUnit: string;
  }> = [];

  // Generate high confidence scenarios
  for (let i = 0; i < 10; i++) {
    const product = seafoodData.products[i];
    const quantity = Math.floor(Math.random() * 50) + 1;
    const unit = seafoodData.units[Math.floor(Math.random() * 4)]; // Focus on common units
    const vendor = seafoodData.vendors[Math.floor(Math.random() * seafoodData.vendors.length)];

    scenarios.push({
      name: `High confidence ${product} receiving`,
      transcript: `Received ${quantity} ${unit} of ${product} from ${vendor}`,
      expectedConfidence: 0.9,
      expectedProduct: product,
      expectedQuantity: quantity,
      expectedUnit: unit
    });
  }

  // Generate medium confidence scenarios (with variations)
  scenarios.push(
    {
      name: 'Quantity spelled out',
      transcript: 'Received twenty five pounds of cod from Pacific Catch',
      expectedConfidence: 0.75,
      expectedProduct: 'cod',
      expectedQuantity: 25,
      expectedUnit: 'pounds'
    },
    {
      name: 'Abbreviated unit',
      transcript: 'Sold 15 lbs salmon to Marina Restaurant',
      expectedConfidence: 0.8,
      expectedProduct: 'salmon',
      expectedQuantity: 15,
      expectedUnit: 'lbs'
    },
    {
      name: 'Informal quantity',
      transcript: 'Got a dozen crabs from the wharf',
      expectedConfidence: 0.7,
      expectedProduct: 'crabs',
      expectedQuantity: 12,
      expectedUnit: 'pieces'
    }
  );

  // Generate low confidence scenarios
  scenarios.push(
    {
      name: 'Unclear quantity',
      transcript: 'Received some halibut',
      expectedConfidence: 0.5,
      expectedProduct: 'halibut',
      expectedQuantity: 0,
      expectedUnit: 'pounds'
    },
    {
      name: 'Background noise simulation',
      transcript: 'Received... twenty... tuna... Ocean...',
      expectedConfidence: 0.3,
      expectedProduct: 'tuna',
      expectedQuantity: 20,
      expectedUnit: 'pounds'
    }
  );

  return scenarios;
}

// Performance test utilities
export function createLatencyTestScenarios() {
  return [
    { name: 'Small audio (< 1KB)', audioSize: 500, expectedLatency: 800 },
    { name: 'Medium audio (1-5KB)', audioSize: 3000, expectedLatency: 1200 },
    { name: 'Large audio (> 5KB)', audioSize: 8000, expectedLatency: 2000 },
    { name: 'Very large audio (> 10KB)', audioSize: 15000, expectedLatency: 3000 }
  ];
}

export function createConcurrencyTestData(concurrentUsers: number) {
  return Array.from({ length: concurrentUsers }, (_, i) => ({
    userId: `user-${i}`,
    transcript: `Received ${10 + i} pounds of salmon from vendor ${i % 5}`,
    expectedProcessingTime: 2000 // 2 seconds max
  }));
}