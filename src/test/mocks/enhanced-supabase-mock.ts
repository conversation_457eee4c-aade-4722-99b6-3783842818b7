/**
 * Enhanced Supabase Mock for Testing
 * 
 * Provides comprehensive mocking for Supabase client with proper TypeScript types
 * and realistic behavior patterns for the Seafood Manager application.
 */

import { vi } from 'vitest';
import type { 
  SupabaseClient,
  PostgrestResponse,
  AuthResponse,
  RealtimeChannel,
  AuthSession,
  User
} from '@supabase/supabase-js';

// Mock data types matching our database schema
export interface MockProduct {
  id: string;
  name: string;
  category_id: string;
  species?: string;
  origin?: string;
  sustainability_rating?: string;
  storage_requirements?: string;
  allergens?: string[];
  created_at: string;
  updated_at: string;
}

export interface MockSensor {
  id: string;
  sensor_id: string;
  name: string;
  location: string;
  is_online: boolean;
  is_active: boolean;
  battery_level?: number;
  temp_min_threshold?: number;
  temp_max_threshold?: number;
  created_at: string;
  updated_at: string;
}

export interface MockTemperatureReading {
  id: string;
  sensor_id: string;
  temperature: number;
  humidity?: number;
  reading_timestamp: string;
  alert_triggered: boolean;
  created_at: string;
}

export interface MockInventoryEvent {
  id: string;
  event_type: 'receiving' | 'sale' | 'disposal' | 'production' | 'adjustment';
  product_id: string;
  quantity: number;
  unit: string;
  batch_number?: string;
  notes?: string;
  occurred_at: string;
  created_at: string;
  voice_confidence_score?: number;
  voice_confidence_breakdown?: any;
  raw_transcript?: string;
  audio_recording_url?: string;
  created_by_voice?: boolean;
}

// In-memory database for realistic data operations
class MockDatabase {
  private data: Map<string, any[]> = new Map();

  constructor() {
    this.seedInitialData();
  }

  private seedInitialData() {
    // Seed categories
    this.setTable('categories', [
      { id: 'cat-fish', name: 'Fish', description: 'Fresh and frozen fish', created_at: new Date().toISOString() },
      { id: 'cat-shellfish', name: 'Shellfish', description: 'Crabs, lobsters, shrimp', created_at: new Date().toISOString() },
      { id: 'cat-mollusks', name: 'Mollusks', description: 'Oysters, clams, mussels', created_at: new Date().toISOString() }
    ]);

    // Seed products
    this.setTable('products', [
      {
        id: 'prod-salmon-1',
        name: 'Atlantic Salmon',
        category_id: 'cat-fish',
        species: 'Salmo salar',
        origin: 'Norway',
        sustainability_rating: 'A',
        storage_requirements: 'Keep refrigerated at 32-38°F',
        allergens: ['Fish'],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'prod-crab-1',
        name: 'Dungeness Crab',
        category_id: 'cat-shellfish',
        species: 'Cancer magister',
        origin: 'Pacific Northwest',
        sustainability_rating: 'A',
        storage_requirements: 'Keep live or frozen',
        allergens: ['Shellfish'],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ]);

    // Seed sensors
    this.setTable('sensors', [
      {
        id: 'sensor-1',
        sensor_id: 'ts-001',
        name: 'Freezer A',
        location: 'Main Freezer',
        is_online: true,
        is_active: true,
        battery_level: 85,
        temp_min_threshold: -20,
        temp_max_threshold: -10,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'sensor-2',
        sensor_id: 'ts-002',
        name: 'Cooler B',
        location: 'Preparation Area',
        is_online: true,
        is_active: true,
        battery_level: 92,
        temp_min_threshold: 32,
        temp_max_threshold: 38,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'sensor-3',
        sensor_id: 'ts-003',
        name: 'Storage Room',
        location: 'Dry Storage',
        is_online: false,
        is_active: true,
        battery_level: 15,
        temp_min_threshold: 50,
        temp_max_threshold: 70,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ]);

    // Seed temperature readings
    const now = new Date();
    const readings = [];
    for (let i = 0; i < 100; i++) {
      readings.push({
        id: `reading-${i}`,
        sensor_id: 'sensor-1',
        temperature: -15 + Math.random() * 5, // Freezer temps
        humidity: 40 + Math.random() * 20,
        reading_timestamp: new Date(now.getTime() - i * 10 * 60 * 1000).toISOString(),
        alert_triggered: false,
        created_at: new Date().toISOString()
      });
    }
    for (let i = 0; i < 100; i++) {
      readings.push({
        id: `reading-${100 + i}`,
        sensor_id: 'sensor-2',
        temperature: 34 + Math.random() * 4, // Cooler temps
        humidity: 60 + Math.random() * 20,
        reading_timestamp: new Date(now.getTime() - i * 10 * 60 * 1000).toISOString(),
        alert_triggered: false,
        created_at: new Date().toISOString()
      });
    }
    this.setTable('temperature_readings', readings);

    // Seed users
    this.setTable('users', [
      {
        id: 'user-1',
        email: '<EMAIL>',
        user_metadata: { full_name: 'Test User', company: 'Test Company' },
        app_metadata: { role: 'manager' },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ]);
  }

  getTable(tableName: string): any[] {
    return this.data.get(tableName) || [];
  }

  setTable(tableName: string, data: any[]): void {
    this.data.set(tableName, data);
  }

  select(tableName: string, columns?: string[]): any[] {
    const table = this.getTable(tableName);
    if (!columns || columns.includes('*')) {
      return [...table];
    }
    return table.map(row => {
      const filtered: any = {};
      columns.forEach(col => {
        if (row[col] !== undefined) {
          filtered[col] = row[col];
        }
      });
      return filtered;
    });
  }

  insert(tableName: string, data: any | any[]): any[] {
    const table = this.getTable(tableName);
    const records = Array.isArray(data) ? data : [data];
    
    const insertedRecords = records.map(record => ({
      ...record,
      id: record.id || `${tableName}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      created_at: record.created_at || new Date().toISOString(),
      updated_at: record.updated_at || new Date().toISOString()
    }));

    table.push(...insertedRecords);
    this.setTable(tableName, table);
    return insertedRecords;
  }

  update(tableName: string, updates: any, conditions: Record<string, any>): any[] {
    const table = this.getTable(tableName);
    const updatedRecords: any[] = [];

    for (let i = 0; i < table.length; i++) {
      const record = table[i];
      let matches = true;
      
      for (const [key, value] of Object.entries(conditions)) {
        if (record[key] !== value) {
          matches = false;
          break;
        }
      }

      if (matches) {
        table[i] = {
          ...record,
          ...updates,
          updated_at: new Date().toISOString()
        };
        updatedRecords.push(table[i]);
      }
    }

    this.setTable(tableName, table);
    return updatedRecords;
  }

  delete(tableName: string, conditions: Record<string, any>): any[] {
    const table = this.getTable(tableName);
    const deletedRecords: any[] = [];
    
    const newTable = table.filter(record => {
      let matches = true;
      
      for (const [key, value] of Object.entries(conditions)) {
        if (record[key] !== value) {
          matches = false;
          break;
        }
      }

      if (matches) {
        deletedRecords.push(record);
        return false;
      }
      return true;
    });

    this.setTable(tableName, newTable);
    return deletedRecords;
  }

  clear(): void {
    this.data.clear();
    this.seedInitialData();
  }

  // Query builders for filter operations
  applyFilters(data: any[], filters: Array<{ type: string; column: string; value: any }>): any[] {
    return data.filter(record => {
      return filters.every(filter => {
        switch (filter.type) {
          case 'eq':
            return record[filter.column] === filter.value;
          case 'neq':
            return record[filter.column] !== filter.value;
          case 'gt':
            return record[filter.column] > filter.value;
          case 'gte':
            return record[filter.column] >= filter.value;
          case 'lt':
            return record[filter.column] < filter.value;
          case 'lte':
            return record[filter.column] <= filter.value;
          case 'like':
            const regex = new RegExp(filter.value.replace(/%/g, '.*'), 'i');
            return regex.test(record[filter.column]);
          case 'in':
            return filter.value.includes(record[filter.column]);
          case 'is':
            return filter.value === null ? record[filter.column] === null : record[filter.column] !== null;
          default:
            return true;
        }
      });
    });
  }

  applyOrder(data: any[], column: string, ascending: boolean = true): any[] {
    return [...data].sort((a, b) => {
      const aVal = a[column];
      const bVal = b[column];
      
      if (aVal === bVal) return 0;
      if (aVal === null || aVal === undefined) return ascending ? 1 : -1;
      if (bVal === null || bVal === undefined) return ascending ? -1 : 1;
      
      const result = aVal > bVal ? 1 : -1;
      return ascending ? result : -result;
    });
  }

  applyLimit(data: any[], count: number): any[] {
    return data.slice(0, count);
  }
}

// Singleton database instance
const mockDatabase = new MockDatabase();

export interface MockSupabaseConfig {
  shouldFail?: boolean;
  failureType?: 'network' | 'auth' | 'permission' | 'validation';
  latencyMs?: number;
  authUser?: User | null;
}

// Enhanced query builder for realistic Supabase API mocking
class MockPostgrestBuilder {
  private tableName: string;
  private data: any[] = [];
  private filters: Array<{ type: string; column: string; value: any }> = [];
  private orderBy: { column: string; ascending: boolean } | null = null;
  private limitCount: number | null = null;
  private config: MockSupabaseConfig;

  constructor(tableName: string, config: MockSupabaseConfig) {
    this.tableName = tableName;
    this.config = config;
    this.data = mockDatabase.select(tableName);
  }

  select(columns?: string) {
    const columnList = columns ? columns.split(',').map(c => c.trim()) : ['*'];
    this.data = mockDatabase.select(this.tableName, columnList);
    return this;
  }

  insert(data: any | any[]) {
    this.data = mockDatabase.insert(this.tableName, data);
    return this;
  }

  update(data: any) {
    // Store the update data for later use in execution
    (this as any).updateData = data;
    return this;
  }

  delete() {
    (this as any).isDelete = true;
    return this;
  }

  eq(column: string, value: any) {
    this.filters.push({ type: 'eq', column, value });
    return this;
  }

  neq(column: string, value: any) {
    this.filters.push({ type: 'neq', column, value });
    return this;
  }

  gt(column: string, value: any) {
    this.filters.push({ type: 'gt', column, value });
    return this;
  }

  gte(column: string, value: any) {
    this.filters.push({ type: 'gte', column, value });
    return this;
  }

  lt(column: string, value: any) {
    this.filters.push({ type: 'lt', column, value });
    return this;
  }

  lte(column: string, value: any) {
    this.filters.push({ type: 'lte', column, value });
    return this;
  }

  like(column: string, pattern: string) {
    this.filters.push({ type: 'like', column, value: pattern });
    return this;
  }

  in(column: string, values: any[]) {
    this.filters.push({ type: 'in', column, value: values });
    return this;
  }

  is(column: string, value: null | 'null') {
    this.filters.push({ type: 'is', column, value: value });
    return this;
  }

  order(column: string, options?: { ascending?: boolean }) {
    this.orderBy = { column, ascending: options?.ascending !== false };
    return this;
  }

  limit(count: number) {
    this.limitCount = count;
    return this;
  }

  private async simulateLatency(): Promise<void> {
    if (this.config.latencyMs && this.config.latencyMs > 0) {
      await new Promise(resolve => setTimeout(resolve, this.config.latencyMs));
    }
  }

  private executeQuery(): any[] {
    let result = [...this.data];

    // Apply filters
    if (this.filters.length > 0) {
      result = mockDatabase.applyFilters(result, this.filters);
    }

    // Apply ordering
    if (this.orderBy) {
      result = mockDatabase.applyOrder(result, this.orderBy.column, this.orderBy.ascending);
    }

    // Apply limit
    if (this.limitCount) {
      result = mockDatabase.applyLimit(result, this.limitCount);
    }

    return result;
  }

  async single(): Promise<PostgrestResponse<any>> {
    await this.simulateLatency();

    if (this.config.shouldFail) {
      return {
        data: null,
        error: { message: 'Mock error', details: '', hint: '', code: '500' },
        status: 500,
        statusText: 'Internal Server Error'
      };
    }

    const result = this.executeQuery();
    const data = result[0] || null;

    return {
      data,
      error: null,
      status: 200,
      statusText: 'OK'
    };
  }

  async maybeSingle(): Promise<PostgrestResponse<any>> {
    return this.single();
  }

  // Main execution method
  async then(resolve: (value: PostgrestResponse<any>) => any): Promise<any> {
    await this.simulateLatency();

    if (this.config.shouldFail) {
      const result: PostgrestResponse<any> = {
        data: null,
        error: { message: 'Mock error', details: '', hint: '', code: '500' },
        status: 500,
        statusText: 'Internal Server Error'
      };
      return resolve(result);
    }

    let result: PostgrestResponse<any>;

    if ((this as any).updateData) {
      // Handle update operation
      const conditions: Record<string, any> = {};
      this.filters.forEach(filter => {
        if (filter.type === 'eq') {
          conditions[filter.column] = filter.value;
        }
      });

      const updatedRecords = mockDatabase.update(this.tableName, (this as any).updateData, conditions);
      result = {
        data: updatedRecords,
        error: null,
        status: 200,
        statusText: 'OK'
      };
    } else if ((this as any).isDelete) {
      // Handle delete operation
      const conditions: Record<string, any> = {};
      this.filters.forEach(filter => {
        if (filter.type === 'eq') {
          conditions[filter.column] = filter.value;
        }
      });

      const deletedRecords = mockDatabase.delete(this.tableName, conditions);
      result = {
        data: deletedRecords,
        error: null,
        status: 200,
        statusText: 'OK'
      };
    } else {
      // Handle select operation
      const queryResult = this.executeQuery();
      result = {
        data: queryResult,
        error: null,
        status: 200,
        statusText: 'OK'
      };
    }

    return resolve(result);
  }
}

// Mock Supabase client
export class MockSupabaseClient {
  private config: MockSupabaseConfig;

  constructor(config: MockSupabaseConfig = {}) {
    this.config = {
      shouldFail: false,
      latencyMs: 100,
      authUser: null,
      ...config
    };
  }

  setConfig(config: Partial<MockSupabaseConfig>): void {
    this.config = { ...this.config, ...config };
  }

  reset(): void {
    mockDatabase.clear();
    this.config = {
      shouldFail: false,
      latencyMs: 100,
      authUser: null
    };
  }

  from(tableName: string) {
    return new MockPostgrestBuilder(tableName, this.config);
  }

  // Auth mock
  auth = {
    getSession: vi.fn().mockImplementation(async () => {
      await new Promise(resolve => setTimeout(resolve, this.config.latencyMs || 100));
      return {
        data: { 
          session: this.config.authUser ? {
            user: this.config.authUser,
            access_token: 'mock-token',
            refresh_token: 'mock-refresh'
          } : null
        },
        error: null
      };
    }),

    getUser: vi.fn().mockImplementation(async () => {
      await new Promise(resolve => setTimeout(resolve, this.config.latencyMs || 100));
      return {
        data: { user: this.config.authUser },
        error: this.config.authUser ? null : { message: 'No user' }
      };
    }),

    signInWithPassword: vi.fn().mockImplementation(async ({ email, password }) => {
      await new Promise(resolve => setTimeout(resolve, this.config.latencyMs || 100));
      
      if (this.config.shouldFail && this.config.failureType === 'auth') {
        return {
          data: { user: null, session: null },
          error: { message: 'Invalid credentials' }
        };
      }

      const user = mockDatabase.getTable('users').find((u: any) => u.email === email);
      return {
        data: { 
          user, 
          session: user ? { user, access_token: 'mock-token' } : null 
        },
        error: user ? null : { message: 'Invalid credentials' }
      };
    }),

    signOut: vi.fn().mockImplementation(async () => {
      await new Promise(resolve => setTimeout(resolve, this.config.latencyMs || 100));
      this.config.authUser = null;
      return { error: null };
    }),

    onAuthStateChange: vi.fn().mockImplementation((callback) => {
      setTimeout(() => {
        callback('SIGNED_IN', { user: this.config.authUser });
      }, 100);
      
      return {
        data: {
          subscription: {
            unsubscribe: vi.fn()
          }
        }
      };
    })
  };

  // Storage mock
  storage = {
    from: vi.fn().mockImplementation((bucket: string) => ({
      upload: vi.fn().mockImplementation(async (path: string, file: File | Blob) => {
        await new Promise(resolve => setTimeout(resolve, this.config.latencyMs || 100));
        
        if (this.config.shouldFail) {
          return {
            data: null,
            error: { message: 'Upload failed' }
          };
        }

        return {
          data: { 
            path: `${bucket}/${path}`,
            id: `upload-${Date.now()}`,
            fullPath: `${bucket}/${path}`
          },
          error: null
        };
      }),

      createSignedUrl: vi.fn().mockImplementation(async (path: string, expiresIn: number) => {
        await new Promise(resolve => setTimeout(resolve, this.config.latencyMs || 100));
        return {
          data: { signedUrl: `https://mock-storage.example.com/${bucket}/${path}?expires=${expiresIn}` },
          error: null
        };
      }),

      remove: vi.fn().mockImplementation(async (paths: string[]) => {
        await new Promise(resolve => setTimeout(resolve, this.config.latencyMs || 100));
        return {
          data: paths.map(path => ({ name: path })),
          error: null
        };
      }),

      list: vi.fn().mockImplementation(async (path?: string) => {
        await new Promise(resolve => setTimeout(resolve, this.config.latencyMs || 100));
        return {
          data: [
            { name: 'file1.jpg', id: 'file1', updated_at: new Date().toISOString() },
            { name: 'file2.mp3', id: 'file2', updated_at: new Date().toISOString() }
          ],
          error: null
        };
      })
    }))
  };

  // Real-time subscriptions mock
  channel = vi.fn().mockImplementation((channelName: string) => ({
    on: vi.fn().mockImplementation((event: string, filter: any, callback: Function) => {
      setTimeout(() => {
        callback({
          eventType: event,
          new: { id: 'new-record', name: 'Test' },
          old: {},
          errors: null
        });
      }, 200);
      return this;
    }),
    
    subscribe: vi.fn().mockImplementation((callback?: Function) => {
      setTimeout(() => {
        if (callback) callback('SUBSCRIBED', null);
      }, 100);
      return {
        unsubscribe: vi.fn()
      };
    })
  }));

  // RPC mock
  rpc = vi.fn().mockImplementation(async (functionName: string, params: any = {}) => {
    await new Promise(resolve => setTimeout(resolve, this.config.latencyMs || 100));

    if (this.config.shouldFail) {
      return {
        data: null,
        error: { message: `RPC function ${functionName} failed` }
      };
    }

    // Mock common RPC functions
    switch (functionName) {
      case 'get_inventory_summary':
        return {
          data: {
            total_products: mockDatabase.getTable('products').length,
            total_events: mockDatabase.getTable('inventory_events').length,
            recent_events: mockDatabase.getTable('inventory_events').slice(-5)
          },
          error: null
        };
      
      case 'calculate_stock_levels':
        return {
          data: mockDatabase.getTable('products').map((product: any) => ({
            product_id: product.id,
            current_stock: Math.floor(Math.random() * 1000),
            reserved_stock: Math.floor(Math.random() * 100),
            available_stock: Math.floor(Math.random() * 900)
          })),
          error: null
        };

      default:
        return {
          data: { message: `Mock response for ${functionName}`, params },
          error: null
        };
    }
  });
}

// Factory function for creating mock clients
export const createMockSupabaseClient = (config: MockSupabaseConfig = {}): MockSupabaseClient => {
  return new MockSupabaseClient(config);
};

// Export the database for direct manipulation in tests
export { mockDatabase };

// Default mock client instance
export const mockSupabaseClient = createMockSupabaseClient();