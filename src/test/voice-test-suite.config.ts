/**
 * Comprehensive test suite configuration for voice processing system
 * Centralizes test settings, thresholds, and environment-specific configurations
 */

// Test environment detection
export const TEST_ENV = {
  isCI: process.env.CI === 'true',
  isDevelopment: process.env.NODE_ENV === 'development',
  isProduction: process.env.NODE_ENV === 'production',
  browser: process.env.BROWSER || 'chromium'
};

// Performance thresholds for different environments
export const PERFORMANCE_THRESHOLDS = {
  // Voice processing performance
  VOICE_PROCESSING: {
    // Maximum time for complete voice-to-database workflow
    TOTAL_WORKFLOW_MAX_MS: TEST_ENV.isCI ? 5000 : 3000,
    
    // OpenAI API response times
    TRANSCRIPTION_MAX_MS: TEST_ENV.isCI ? 2000 : 1500,
    COMMAND_PROCESSING_MAX_MS: TEST_ENV.isCI ? 1500 : 1000,
    
    // Database operations
    CREATE_EVENT_MAX_MS: TEST_ENV.isCI ? 800 : 500,
    UPDATE_EVENT_MAX_MS: TEST_ENV.isCI ? 600 : 400,
    QUERY_EVENTS_MAX_MS: TEST_ENV.isCI ? 400 : 300,
    
    // UI responsiveness
    COMPONENT_RENDER_MAX_MS: 100,
    USER_INTERACTION_MAX_MS: 200,
    
    // Concurrent processing
    CONCURRENT_USERS_MAX: TEST_ENV.isCI ? 3 : 5,
    CONCURRENT_LATENCY_MULTIPLIER: 1.5
  },
  
  // Quality thresholds
  QUALITY: {
    // Voice confidence thresholds
    HIGH_CONFIDENCE_MIN: 0.85,
    MEDIUM_CONFIDENCE_MIN: 0.6,
    LOW_CONFIDENCE_MAX: 0.6,
    
    // Test coverage requirements
    UNIT_TEST_COVERAGE_MIN: 85,
    INTEGRATION_TEST_COVERAGE_MIN: 70,
    E2E_TEST_COVERAGE_MIN: 60,
    
    // Success rates
    VOICE_PROCESSING_SUCCESS_RATE_MIN: 0.95,
    DATABASE_OPERATION_SUCCESS_RATE_MIN: 0.98,
    E2E_TEST_SUCCESS_RATE_MIN: 0.9
  },
  
  // Resource usage limits
  RESOURCES: {
    MEMORY_INCREASE_MAX_PERCENT: 50,
    CPU_USAGE_MAX_PERCENT: 80,
    NETWORK_REQUESTS_MAX_PER_TEST: 20
  }
};

// Test data configuration
export const TEST_DATA = {
  // Voice test scenarios
  VOICE_SCENARIOS: {
    HIGH_CONFIDENCE: [
      'Received 25 pounds of fresh salmon from Ocean Fresh Seafoods',
      'Sold 12 dungeness crabs to Marina Restaurant for $120',
      'Disposed of 5 pounds of old cod due to spoilage',
      'Adjusted inventory for 15 pounds of halibut'
    ],
    MEDIUM_CONFIDENCE: [
      'Got some salmon from the dock',
      'Sold twenty crabs to restaurant',
      'Threw away bad fish',
      'Fixed count for halibut'
    ],
    LOW_CONFIDENCE: [
      'Something about... fish... from...',
      'Can\'t hear clearly... received... salmon',
      'Bad audio... sold... restaurant',
      'Noise... disposed... spoiled'
    ]
  },
  
  // Seafood products for testing
  PRODUCTS: {
    FISH: ['salmon', 'cod', 'halibut', 'tuna', 'bass', 'snapper', 'mahi mahi'],
    SHELLFISH: ['crab', 'lobster', 'shrimp', 'scallops', 'dungeness crab', 'king crab'],
    MOLLUSKS: ['oysters', 'clams', 'mussels', 'squid', 'abalone']
  },
  
  // Units of measurement
  UNITS: ['pounds', 'lbs', 'kilograms', 'kg', 'pieces', 'dozen', 'cases'],
  
  // Test vendors and customers
  VENDORS: [
    'Ocean Fresh Seafoods',
    'Pacific Catch',
    'Northwest Seafood',
    'Marine Harvest',
    'Trident Seafoods'
  ],
  CUSTOMERS: [
    'Marina Restaurant',
    'The Fish House',
    'Ocean View Cafe',
    'Harbor Restaurant',
    'Fresh Catch Market'
  ]
};

// Environment-specific configurations
export const ENVIRONMENT_CONFIG = {
  development: {
    API_LATENCY_SIMULATION_MS: 100,
    DATABASE_LATENCY_SIMULATION_MS: 50,
    ENABLE_PERFORMANCE_LOGS: true,
    ENABLE_DETAILED_MOCKS: true,
    TIMEOUT_MULTIPLIER: 1
  },
  
  ci: {
    API_LATENCY_SIMULATION_MS: 200,
    DATABASE_LATENCY_SIMULATION_MS: 100,
    ENABLE_PERFORMANCE_LOGS: false,
    ENABLE_DETAILED_MOCKS: false,
    TIMEOUT_MULTIPLIER: 2
  },
  
  production: {
    API_LATENCY_SIMULATION_MS: 0,
    DATABASE_LATENCY_SIMULATION_MS: 0,
    ENABLE_PERFORMANCE_LOGS: false,
    ENABLE_DETAILED_MOCKS: false,
    TIMEOUT_MULTIPLIER: 1
  }
};

// Current environment config
export const CURRENT_CONFIG = ENVIRONMENT_CONFIG[
  TEST_ENV.isCI ? 'ci' : TEST_ENV.isDevelopment ? 'development' : 'production'
];

// Test suite configuration
export const TEST_SUITE_CONFIG = {
  // Test timeouts (adjusted for environment)
  TIMEOUTS: {
    UNIT_TEST: 5000 * CURRENT_CONFIG.TIMEOUT_MULTIPLIER,
    INTEGRATION_TEST: 10000 * CURRENT_CONFIG.TIMEOUT_MULTIPLIER,
    E2E_TEST: 30000 * CURRENT_CONFIG.TIMEOUT_MULTIPLIER,
    PERFORMANCE_TEST: 15000 * CURRENT_CONFIG.TIMEOUT_MULTIPLIER
  },
  
  // Test retry configuration
  RETRIES: {
    UNIT_TESTS: TEST_ENV.isCI ? 2 : 0,
    INTEGRATION_TESTS: TEST_ENV.isCI ? 3 : 1,
    E2E_TESTS: TEST_ENV.isCI ? 3 : 2,
    PERFORMANCE_TESTS: TEST_ENV.isCI ? 2 : 1
  },
  
  // Test isolation settings
  ISOLATION: {
    RESET_DATABASE_BETWEEN_TESTS: true,
    CLEAR_MOCKS_BETWEEN_TESTS: true,
    RESET_PERFORMANCE_COUNTERS: true
  },
  
  // Mock configurations
  MOCKS: {
    OPENAI: {
      DEFAULT_LATENCY_MS: CURRENT_CONFIG.API_LATENCY_SIMULATION_MS,
      ENABLE_DETAILED_RESPONSES: CURRENT_CONFIG.ENABLE_DETAILED_MOCKS,
      SIMULATE_RATE_LIMITS: TEST_ENV.isCI,
      ENABLE_ERROR_SCENARIOS: true
    },
    
    SUPABASE: {
      DEFAULT_LATENCY_MS: CURRENT_CONFIG.DATABASE_LATENCY_SIMULATION_MS,
      ENABLE_RLS_SIMULATION: true,
      SIMULATE_NETWORK_ISSUES: TEST_ENV.isCI,
      ENABLE_PERFORMANCE_TRACKING: CURRENT_CONFIG.ENABLE_PERFORMANCE_LOGS
    },
    
    BROWSER_APIS: {
      SPEECH_RECOGNITION_DELAY_MS: 1000,
      MEDIA_RECORDER_DELAY_MS: 100,
      USER_MEDIA_SUCCESS_RATE: 0.95
    }
  },
  
  // Test reporting configuration
  REPORTING: {
    ENABLE_PERFORMANCE_METRICS: true,
    ENABLE_COVERAGE_REPORTS: true,
    ENABLE_VISUAL_REGRESSION: TEST_ENV.isCI,
    ENABLE_ACCESSIBILITY_REPORTS: true,
    
    // Output formats
    FORMATS: ['json', 'html'],
    OUTPUT_DIR: './test-results',
    
    // Metrics to track
    TRACK_METRICS: [
      'voice_processing_latency',
      'database_operation_time',
      'memory_usage',
      'test_success_rate',
      'api_request_count',
      'error_rates'
    ]
  }
};

// Test data generators
export const TEST_DATA_GENERATORS = {
  // Generate realistic voice event data
  generateVoiceEvent: (overrides: any = {}) => ({
    id: `test-event-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    event_type: 'receiving',
    product_name: TEST_DATA.PRODUCTS.FISH[0],
    quantity: 25,
    unit: 'lbs',
    voice_confidence_score: 0.9,
    voice_confidence_breakdown: {
      product_match: 0.95,
      quantity_extraction: 0.90,
      vendor_match: 0.85,
      overall: 0.90
    },
    raw_transcript: 'Received 25 pounds of salmon from Ocean Fresh Seafoods',
    audio_recording_url: 'data:audio/webm;base64,test-audio',
    occurred_at: new Date().toISOString(),
    created_at: new Date().toISOString(),
    created_by_voice: true,
    ...overrides
  }),
  
  // Generate test audio blob
  generateAudioBlob: (sizeKB: number = 5, format: string = 'audio/webm') => {
    const arrayBuffer = new ArrayBuffer(sizeKB * 1024);
    return new Blob([arrayBuffer], { type: format });
  },
  
  // Generate realistic seafood transcript
  generateSeafoodTranscript: (
    eventType: 'receiving' | 'sale' | 'disposal' = 'receiving',
    confidence: 'high' | 'medium' | 'low' = 'high'
  ) => {
    const product = TEST_DATA.PRODUCTS.FISH[Math.floor(Math.random() * TEST_DATA.PRODUCTS.FISH.length)];
    const quantity = Math.floor(Math.random() * 50) + 1;
    const unit = TEST_DATA.UNITS[Math.floor(Math.random() * 4)]; // Common units
    const vendor = TEST_DATA.VENDORS[Math.floor(Math.random() * TEST_DATA.VENDORS.length)];
    const customer = TEST_DATA.CUSTOMERS[Math.floor(Math.random() * TEST_DATA.CUSTOMERS.length)];
    
    const templates = {
      high: {
        receiving: `Received ${quantity} ${unit} of fresh ${product} from ${vendor}`,
        sale: `Sold ${quantity} ${unit} of ${product} to ${customer}`,
        disposal: `Disposed of ${quantity} ${unit} of ${product} due to spoilage`
      },
      medium: {
        receiving: `Got ${quantity} ${product} from ${vendor}`,
        sale: `Delivered ${quantity} ${product} to ${customer}`,
        disposal: `Threw away ${quantity} ${product}`
      },
      low: {
        receiving: `Something about ${product} from somewhere`,
        sale: `Sold some ${product}`,
        disposal: `Bad ${product} disposed`
      }
    };
    
    return templates[confidence][eventType];
  }
};

// Test assertions and matchers
export const TEST_ASSERTIONS = {
  // Performance assertion helpers
  expectWithinPerformanceThreshold: (
    actual: number,
    threshold: number,
    description: string = 'operation'
  ) => {
    if (actual > threshold) {
      throw new Error(
        `Performance assertion failed: ${description} took ${actual}ms, ` +
        `expected <= ${threshold}ms (${((actual - threshold) / threshold * 100).toFixed(1)}% over)`
      );
    }
  },
  
  // Voice confidence assertions
  expectHighConfidence: (score: number) => {
    if (score < PERFORMANCE_THRESHOLDS.QUALITY.HIGH_CONFIDENCE_MIN) {
      throw new Error(
        `High confidence assertion failed: score ${score} < ${PERFORMANCE_THRESHOLDS.QUALITY.HIGH_CONFIDENCE_MIN}`
      );
    }
  },
  
  expectMediumConfidence: (score: number) => {
    if (score < PERFORMANCE_THRESHOLDS.QUALITY.MEDIUM_CONFIDENCE_MIN || 
        score >= PERFORMANCE_THRESHOLDS.QUALITY.HIGH_CONFIDENCE_MIN) {
      throw new Error(
        `Medium confidence assertion failed: score ${score} not in range ` +
        `[${PERFORMANCE_THRESHOLDS.QUALITY.MEDIUM_CONFIDENCE_MIN}, ${PERFORMANCE_THRESHOLDS.QUALITY.HIGH_CONFIDENCE_MIN})`
      );
    }
  },
  
  expectLowConfidence: (score: number) => {
    if (score >= PERFORMANCE_THRESHOLDS.QUALITY.MEDIUM_CONFIDENCE_MIN) {
      throw new Error(
        `Low confidence assertion failed: score ${score} >= ${PERFORMANCE_THRESHOLDS.QUALITY.MEDIUM_CONFIDENCE_MIN}`
      );
    }
  },
  
  // Data integrity assertions
  expectValidVoiceEvent: (event: any) => {
    const requiredFields = [
      'id', 'event_type', 'quantity', 'unit', 'voice_confidence_score',
      'raw_transcript', 'created_at', 'created_by_voice'
    ];
    
    for (const field of requiredFields) {
      if (event[field] === undefined || event[field] === null) {
        throw new Error(`Voice event missing required field: ${field}`);
      }
    }
    
    if (event.voice_confidence_score < 0 || event.voice_confidence_score > 1) {
      throw new Error(
        `Invalid confidence score: ${event.voice_confidence_score} (must be 0-1)`
      );
    }
    
    if (event.quantity < 0) {
      throw new Error(`Invalid quantity: ${event.quantity} (must be >= 0)`);
    }
  }
};

// Export the complete configuration
export const VOICE_TEST_CONFIG = {
  ENV: TEST_ENV,
  THRESHOLDS: PERFORMANCE_THRESHOLDS,
  DATA: TEST_DATA,
  SUITE: TEST_SUITE_CONFIG,
  GENERATORS: TEST_DATA_GENERATORS,
  ASSERTIONS: TEST_ASSERTIONS,
  CURRENT: CURRENT_CONFIG
};

export default VOICE_TEST_CONFIG;