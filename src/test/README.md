# Testing Infrastructure

This directory contains comprehensive testing infrastructure for the Pacific Cloud Seafoods Manager application, focusing on core functionality, user workflows, and API endpoints.

## Test Structure

### `/integration` - Integration Tests
- **voice-workflow.test.ts** - Complete voice event workflows from creation to management
- **api-endpoints.test.ts** - API endpoint testing with mock server
- **user-workflows.test.tsx** - End-to-end user interaction workflows

### `/e2e` - End-to-End Tests
- **voice-system.spec.ts** - Complete system testing with Playwright
- Tests real browser interactions, mobile responsiveness, and accessibility

### `/performance` - Performance Tests
- **voice-processing.test.ts** - Performance benchmarks and scalability tests
- Memory usage, processing time, and concurrent operation testing

### `/mocks` - Mock Data and Services
Mock implementations and test data for consistent testing across all test suites.

### `/utils` - Testing Utilities
Shared testing utilities, helpers, and custom matchers for consistent testing patterns.

## Test Categories

### 🎤 **Voice Processing Tests**
- Complete voice event creation workflow
- Audio processing and storage
- Confidence scoring and quality review
- Error handling and recovery
- Performance benchmarks

### 📊 **Database Integration Tests**
- CRUD operations for voice events
- Filtering and search functionality
- Audit trail management
- Batch operations
- Real-time subscriptions

### 🔌 **API Endpoint Tests**
- Voice processing endpoints (`/api/voice-process`)
- Command extraction (`/api/voice-command-extract`)
- Health monitoring (`/api/voice-realtime-check`)
- Error handling and rate limiting
- Security and CORS

### 🖥️ **User Interface Tests**
- Component rendering and interactions
- Form validation and submission
- Real-time updates and subscriptions
- Accessibility compliance
- Mobile responsiveness

### ⚡ **Performance Tests**
- Voice processing speed (< 3 seconds)
- Database operations (< 1 second)
- Memory usage monitoring
- Concurrent request handling
- Scalability under load

## Running Tests

### Unit and Integration Tests
```bash
# Run all tests
npm run test

# Run with coverage
npm run test:coverage

# Run integration tests only
npm run test:integration

# Run performance tests
npm run test:performance

# Watch mode for development
npm run test:watch

# UI mode for debugging
npm run test:ui
```

### End-to-End Tests
```bash
# Run all E2E tests
npm run test:e2e

# Run E2E tests with UI
npm run test:e2e:ui

# Run voice system E2E tests only
npm run test:e2e:voice
```

### CI/CD Pipeline
```bash
# Run all tests for CI
npm run test:ci

# Run complete test suite
npm run test:all
```

## Test Configuration

### Environment Variables
```bash
# Test database
TEST_DATABASE_URL=postgresql://localhost:5432/test_db

# API endpoints
VITE_API_BASE_URL=http://localhost:3000

# E2E test credentials
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=testpassword123

# Playwright configuration
PLAYWRIGHT_TEST_BASE_URL=http://localhost:3000
```

### Performance Thresholds
- **Voice Processing**: < 3 seconds
- **Database Operations**: < 1 second
- **API Responses**: < 2 seconds
- **Component Rendering**: < 100ms
- **Memory Usage**: < 100MB increase

## Key Test Workflows

### 1. **Complete Voice Event Creation**
```typescript
// Tests the full workflow:
// 1. User starts voice recording
// 2. Audio is processed via OpenAI Whisper
// 3. Event is created in database
// 4. Audio is stored in Supabase Storage
// 5. User sees confirmation
```

### 2. **Quality Review Process**
```typescript
// Tests quality assurance workflow:
// 1. Low-confidence events are flagged
// 2. Reviewers can approve/reject events
// 3. Batch operations for efficiency
// 4. Audit trail is maintained
```

### 3. **Real-time Updates**
```typescript
// Tests real-time functionality:
// 1. Events update in real-time via Supabase subscriptions
// 2. Multiple users see updates simultaneously
// 3. Connection recovery after network issues
```

### 4. **Error Handling and Recovery**
```typescript
// Tests error scenarios:
// 1. Network failures during voice processing
// 2. Database connection issues
// 3. Storage service unavailability
// 4. User-friendly error messages and recovery
```

## Mock Strategy

### Service Mocks
- **VoiceEventService**: Database operations
- **AudioStorageService**: File storage operations
- **VoiceAnalyticsService**: Analytics and trends
- **Supabase Client**: Database and real-time subscriptions

### API Mocks
- **OpenAI Whisper API**: Speech-to-text processing
- **Supabase Storage API**: File upload/download
- **Real-time subscriptions**: WebSocket connections

### Browser APIs
- **MediaRecorder**: Audio recording
- **getUserMedia**: Microphone access
- **Audio**: Audio playback
- **localStorage/sessionStorage**: Client storage

## Test Data Management

### Mock Voice Events
```typescript
const mockVoiceEvent = {
  id: 'test-event-123',
  event_type: 'receiving',
  product_name: 'Atlantic Salmon',
  quantity: 100,
  unit: 'lbs',
  voice_confidence_score: 0.92,
  voice_confidence_breakdown: {
    product_match: 0.95,
    quantity_extraction: 0.90,
    vendor_match: 0.91,
    overall: 0.92
  },
  raw_transcript: 'Received one hundred pounds of Atlantic salmon',
  audio_recording_url: 'https://storage.example.com/audio.wav',
  created_by_voice: true
};
```

### Test Scenarios
- **High Confidence Events** (>90%): Automatic processing
- **Medium Confidence Events** (70-89%): Optional review
- **Low Confidence Events** (<70%): Required review
- **Error Scenarios**: Network failures, API errors, validation failures

## Accessibility Testing

### WCAG 2.1 AA Compliance
- Keyboard navigation support
- Screen reader compatibility
- Color contrast requirements
- Focus management
- ARIA labels and roles

### Testing Tools
- **jest-axe**: Automated accessibility testing
- **@testing-library/react**: Semantic queries
- **Playwright**: Real browser accessibility testing

## Performance Monitoring

### Metrics Tracked
- **Voice Processing Time**: End-to-end processing duration
- **Database Query Performance**: CRUD operation timing
- **Memory Usage**: Heap usage during operations
- **Network Requests**: API call timing and success rates
- **Component Render Time**: UI responsiveness

### Performance Budgets
- Bundle size limits
- Memory usage thresholds
- Processing time limits
- API response time targets

## Continuous Integration

### GitHub Actions Workflow
```yaml
- name: Run Tests
  run: |
    npm run test:ci
    npm run test:e2e
```

### Test Reports
- Coverage reports with detailed metrics
- Performance benchmarks over time
- E2E test results with screenshots
- Accessibility compliance reports

## Best Practices

### Test Organization
- Group related tests in describe blocks
- Use descriptive test names
- Follow AAA pattern (Arrange, Act, Assert)
- Keep tests focused and independent

### Mock Management
- Reset mocks between tests
- Use realistic mock data
- Mock external dependencies consistently
- Verify mock interactions when relevant

### Performance Testing
- Set realistic performance budgets
- Test under various load conditions
- Monitor memory usage and leaks
- Include performance tests in CI pipeline

### Accessibility Testing
- Test with keyboard navigation
- Verify screen reader compatibility
- Check color contrast ratios
- Test with various assistive technologies

## Troubleshooting

### Common Issues
- **Timeout Errors**: Increase timeout for slow operations
- **Mock Failures**: Verify mock setup and reset between tests
- **Memory Leaks**: Check for proper cleanup in afterEach
- **Flaky Tests**: Add proper wait conditions and error handling

### Debugging Tips
- Use `test.only()` to run specific tests
- Add console.log statements for debugging
- Use browser dev tools in E2E tests
- Check test coverage reports for gaps

## Future Enhancements

### Planned Improvements
- Visual regression testing
- Load testing with realistic data volumes
- Cross-browser compatibility testing
- Mobile device testing
- Internationalization testing
- Security penetration testing