/**
 * Comprehensive integration tests for voice-to-database workflow
 * Tests the complete flow from audio input to database storage with performance validation
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { VoiceEventService } from '../../modules/voice-event-storage/VoiceEventService';
import { 
  createMockSupabaseClient, 
  seedMockDatabase, 
  resetMockDatabase,
  setPerformanceSimulation,
  mockNetworkIssue,
  measureDatabasePerformance
} from '../mocks/supabase-mocks';
import { 
  MockOpenAIClient, 
  createMockOpenAIClient,
  generateVoiceTestScenarios,
  SEAFOOD_VOCABULARY 
} from '../mocks/openai-mocks';
import { 
  VOICE_TEST_SAMPLES,
  MockSpeechRecognition,
  MockMediaRecorder,
  createMockVoiceEvent
} from '../mocks/voice-mocks';
import { testUtils, TEST_CONFIG } from '../test-config';

// Mock the supabase client at module level
vi.mock('../../lib/supabase', () => ({
  supabase: createMockSupabaseClient()
}));

describe('Voice-to-Database Integration Tests', () => {
  let voiceEventService: VoiceEventService;
  let mockOpenAI: MockOpenAIClient;
  let mockSupabase: any;
  let mockSpeechRecognition: MockSpeechRecognition;
  let mockMediaRecorder: MockMediaRecorder;

  beforeEach(async () => {
    // Reset all mocks and test data
    resetMockDatabase();
    seedMockDatabase();
    
    // Initialize services with fresh instances
    voiceEventService = new VoiceEventService();
    mockOpenAI = createMockOpenAIClient({
      latencyMs: 500,
      responseQuality: 'high'
    });
    
    // Setup browser API mocks
    mockSpeechRecognition = new MockSpeechRecognition();
    mockMediaRecorder = new MockMediaRecorder(new MediaStream());
    
    // Mock global APIs
    global.SpeechRecognition = vi.fn(() => mockSpeechRecognition);
    global.MediaRecorder = vi.fn(() => mockMediaRecorder);
    global.navigator.mediaDevices = {
      getUserMedia: vi.fn().mockResolvedValue(new MediaStream())
    } as any;
  });

  afterEach(() => {
    vi.clearAllMocks();
    resetMockDatabase();
  });

  describe('Complete Voice Processing Workflow', () => {
    it('should process high-confidence voice input end-to-end', async () => {
      const testScenario = VOICE_TEST_SAMPLES.highConfidence.salmonReceiving;
      
      // Step 1: Voice recognition
      mockSpeechRecognition.setRecognitionResult(testScenario.transcript);
      
      // Step 2: Audio processing with OpenAI
      const audioBlob = testUtils.createMockAudioBlob(5000); // Large enough for high quality
      const transcription = await mockOpenAI.transcribeAudio(audioBlob);
      expect(transcription.text).toBe(testScenario.transcript);
      
      // Step 3: Process transcript into voice command
      const voiceCommand = await mockOpenAI.processTranscript(testScenario.transcript);
      expect(voiceCommand.confidence_score).toBeGreaterThan(0.9);
      expect(voiceCommand.product_name).toBe(testScenario.voiceCommand.product_name);
      expect(voiceCommand.quantity).toBe(testScenario.voiceCommand.quantity);
      
      // Step 4: Store in database
      const voiceEventData = {
        event_type: voiceCommand.event_type!,
        product_name: voiceCommand.product_name!,
        quantity: voiceCommand.quantity!,
        unit: voiceCommand.unit!,
        vendor_name: voiceCommand.vendor_name,
        voice_confidence_score: voiceCommand.confidence_score,
        voice_confidence_breakdown: voiceCommand.confidence_breakdown!,
        raw_transcript: voiceCommand.raw_transcript,
        occurred_at: new Date().toISOString()
      };
      
      const savedEvent = await voiceEventService.createVoiceEvent(voiceEventData);
      
      // Verify complete workflow
      expect(savedEvent).toBeDefined();
      expect(savedEvent.id).toBeTruthy();
      expect(savedEvent.voice_confidence_score).toBeGreaterThan(0.9);
      expect(savedEvent.created_by_voice).toBe(true);
      expect(savedEvent.raw_transcript).toBe(testScenario.transcript);
    });

    it('should handle medium-confidence voice input with validation', async () => {
      const testScenario = VOICE_TEST_SAMPLES.mediumConfidence.unclearQuantity;
      
      mockSpeechRecognition.setRecognitionResult(testScenario.transcript);
      
      // Process through the pipeline
      const audioBlob = testUtils.createMockAudioBlob(2000); // Medium quality
      const transcription = await mockOpenAI.transcribeAudio(audioBlob);
      const voiceCommand = await mockOpenAI.processTranscript(transcription.text);
      
      // Medium confidence should be flagged for review
      expect(voiceCommand.confidence_score).toBeLessThan(0.8);
      expect(voiceCommand.confidence_score).toBeGreaterThan(0.6);
      
      // Create event data with validation flags
      const voiceEventData = {
        event_type: voiceCommand.event_type!,
        product_name: voiceCommand.product_name!,
        quantity: voiceCommand.quantity || 0, // Handle missing quantity
        unit: voiceCommand.unit!,
        vendor_name: voiceCommand.vendor_name,
        voice_confidence_score: voiceCommand.confidence_score,
        voice_confidence_breakdown: voiceCommand.confidence_breakdown!,
        raw_transcript: voiceCommand.raw_transcript,
        occurred_at: new Date().toISOString(),
        metadata: {
          requires_review: true,
          confidence_threshold: 'medium'
        }
      };
      
      const savedEvent = await voiceEventService.createVoiceEvent(voiceEventData);
      expect(savedEvent.metadata?.requires_review).toBe(true);
    });

    it('should reject low-confidence voice input appropriately', async () => {
      const testScenario = VOICE_TEST_SAMPLES.lowConfidence.noiseBackground;
      
      mockSpeechRecognition.setRecognitionResult(testScenario.transcript);
      
      const audioBlob = testUtils.createMockAudioBlob(500); // Poor quality
      const transcription = await mockOpenAI.transcribeAudio(audioBlob);
      const voiceCommand = await mockOpenAI.processTranscript(transcription.text);
      
      // Low confidence should be below acceptance threshold
      expect(voiceCommand.confidence_score).toBeLessThan(0.6);
      
      // Should not create event automatically
      expect(voiceCommand.quantity).toBe(0);
      expect(voiceCommand.product_name).toBeTruthy(); // Should still extract some data
    });
  });

  describe('Performance and Scalability Tests', () => {
    it('should process voice events within performance thresholds', async () => {
      const testScenario = VOICE_TEST_SAMPLES.highConfidence.crabSales;
      
      // Set realistic latency for performance testing
      mockOpenAI.setLatency(800); // Simulate realistic OpenAI latency
      setPerformanceSimulation(true, 100); // Simulate database latency
      
      const performanceTest = async () => {
        const audioBlob = testUtils.createMockAudioBlob(5000);
        const transcription = await mockOpenAI.transcribeAudio(audioBlob);
        const voiceCommand = await mockOpenAI.processTranscript(transcription.text);
        
        const voiceEventData = {
          event_type: voiceCommand.event_type!,
          product_name: voiceCommand.product_name!,
          quantity: voiceCommand.quantity!,
          unit: voiceCommand.unit!,
          customer_name: voiceCommand.customer_name,
          voice_confidence_score: voiceCommand.confidence_score,
          voice_confidence_breakdown: voiceCommand.confidence_breakdown!,
          raw_transcript: voiceCommand.raw_transcript,
          occurred_at: new Date().toISOString()
        };
        
        return await voiceEventService.createVoiceEvent(voiceEventData);
      };
      
      const { result, duration } = await testUtils.measurePerformance(performanceTest);
      
      // Verify performance meets requirements (< 3 seconds total)
      expect(duration).toBeLessThan(TEST_CONFIG.PERFORMANCE_THRESHOLDS.VOICE_PROCESSING_MAX_TIME);
      expect(result).toBeDefined();
      expect(result.voice_confidence_score).toBeGreaterThan(0.8);
    });

    it('should handle concurrent voice event processing', async () => {
      const concurrentRequests = 5;
      const promises: Promise<any>[] = [];
      
      // Create multiple concurrent voice processing requests
      for (let i = 0; i < concurrentRequests; i++) {
        const promise = (async () => {
          const audioBlob = testUtils.createMockAudioBlob(3000);
          const transcription = await mockOpenAI.transcribeAudio(audioBlob);
          const voiceCommand = await mockOpenAI.processTranscript(transcription.text);
          
          const voiceEventData = {
            event_type: 'receiving' as const,
            product_name: SEAFOOD_VOCABULARY.fish[i % SEAFOOD_VOCABULARY.fish.length],
            quantity: 10 + i,
            unit: 'lbs' as const,
            vendor_name: SEAFOOD_VOCABULARY.vendors[0],
            voice_confidence_score: 0.9,
            voice_confidence_breakdown: {
              product_match: 0.9,
              quantity_extraction: 0.9,
              vendor_match: 0.9,
              overall: 0.9
            },
            raw_transcript: `Received ${10 + i} pounds of ${SEAFOOD_VOCABULARY.fish[i % SEAFOOD_VOCABULARY.fish.length]}`,
            occurred_at: new Date().toISOString()
          };
          
          return await voiceEventService.createVoiceEvent(voiceEventData);
        })();
        
        promises.push(promise);
      }
      
      const results = await Promise.all(promises);
      
      // Verify all requests completed successfully
      expect(results).toHaveLength(concurrentRequests);
      results.forEach(result => {
        expect(result).toBeDefined();
        expect(result.id).toBeTruthy();
        expect(result.created_by_voice).toBe(true);
      });
    });

    it('should handle large voice files efficiently', async () => {
      // Test with large audio file (simulated)
      const largeAudioBlob = testUtils.createMockAudioBlob(50000); // 50KB
      
      const performanceTest = async () => {
        const transcription = await mockOpenAI.transcribeAudio(largeAudioBlob);
        const voiceCommand = await mockOpenAI.processTranscript(transcription.text);
        
        const voiceEventData = {
          event_type: voiceCommand.event_type!,
          product_name: voiceCommand.product_name!,
          quantity: voiceCommand.quantity!,
          unit: voiceCommand.unit!,
          voice_confidence_score: voiceCommand.confidence_score,
          voice_confidence_breakdown: voiceCommand.confidence_breakdown!,
          raw_transcript: voiceCommand.raw_transcript,
          audio_recording_url: 'data:audio/webm;base64,large-file-data',
          occurred_at: new Date().toISOString()
        };
        
        return await voiceEventService.createVoiceEvent(voiceEventData);
      };
      
      const { result, duration } = await testUtils.measurePerformance(performanceTest);
      
      // Even with large files, should complete within threshold
      expect(duration).toBeLessThan(TEST_CONFIG.PERFORMANCE_THRESHOLDS.VOICE_PROCESSING_MAX_TIME);
      expect(result).toBeDefined();
    });
  });

  describe('Error Handling and Resilience', () => {
    it('should handle OpenAI API failures gracefully', async () => {
      // Configure OpenAI to fail
      mockOpenAI.setShouldFail(true, 'rate_limit');
      
      const audioBlob = testUtils.createMockAudioBlob(3000);
      
      await expect(mockOpenAI.transcribeAudio(audioBlob))
        .rejects.toThrow('Rate limit exceeded');
        
      // Reset for recovery test
      mockOpenAI.setShouldFail(false);
      
      // Should work after reset
      const transcription = await mockOpenAI.transcribeAudio(audioBlob);
      expect(transcription.text).toBeTruthy();
    });

    it('should handle database connection failures', async () => {
      // Simulate database connection failure
      mockNetworkIssue('connection');
      
      const voiceEventData = {
        event_type: 'receiving' as const,
        product_name: 'salmon',
        quantity: 25,
        unit: 'lbs' as const,
        voice_confidence_score: 0.9,
        voice_confidence_breakdown: {
          product_match: 0.9,
          quantity_extraction: 0.9,
          vendor_match: 0.9,
          overall: 0.9
        },
        raw_transcript: 'Received 25 pounds of salmon',
        occurred_at: new Date().toISOString()
      };
      
      await expect(voiceEventService.createVoiceEvent(voiceEventData))
        .rejects.toThrow('Connection refused');
    });

    it('should handle malformed voice transcripts', async () => {
      const malformedTranscripts = [
        '', // Empty
        '!@#$%^&*()', // Special characters only
        'zzz yyy xxx', // No recognizable content
        'a'.repeat(1000) // Very long nonsense
      ];
      
      for (const transcript of malformedTranscripts) {
        const voiceCommand = await mockOpenAI.processTranscript(transcript);
        
        // Should return low confidence for malformed input
        expect(voiceCommand.confidence_score).toBeLessThan(0.5);
        expect(voiceCommand.action_type).toBe('create_event'); // Default action
      }
    });

    it('should handle partial voice data gracefully', async () => {
      const partialVoiceCommand = await mockOpenAI.processTranscript('Received some fish');
      
      const voiceEventData = {
        event_type: partialVoiceCommand.event_type!,
        product_name: partialVoiceCommand.product_name || 'unknown',
        quantity: partialVoiceCommand.quantity || 0,
        unit: partialVoiceCommand.unit || 'pounds',
        voice_confidence_score: partialVoiceCommand.confidence_score,
        voice_confidence_breakdown: partialVoiceCommand.confidence_breakdown!,
        raw_transcript: partialVoiceCommand.raw_transcript,
        occurred_at: new Date().toISOString(),
        metadata: {
          incomplete_data: true,
          missing_fields: ['quantity', 'vendor']
        }
      };
      
      const savedEvent = await voiceEventService.createVoiceEvent(voiceEventData);
      expect(savedEvent.metadata?.incomplete_data).toBe(true);
    });
  });

  describe('Data Integrity and Validation', () => {
    it('should maintain audit trail for voice events', async () => {
      const voiceEventData = {
        event_type: 'receiving' as const,
        product_name: 'salmon',
        quantity: 25,
        unit: 'lbs' as const,
        vendor_name: 'Ocean Fresh Seafoods',
        voice_confidence_score: 0.95,
        voice_confidence_breakdown: {
          product_match: 0.98,
          quantity_extraction: 0.95,
          vendor_match: 0.93,
          overall: 0.95
        },
        raw_transcript: 'Received 25 pounds of fresh salmon from Ocean Fresh Seafoods',
        audio_recording_url: 'data:audio/webm;base64,test-audio',
        occurred_at: new Date().toISOString()
      };
      
      const savedEvent = await voiceEventService.createVoiceEvent(voiceEventData);
      
      // Verify audit trail information
      expect(savedEvent.created_by_voice).toBe(true);
      expect(savedEvent.raw_transcript).toBe(voiceEventData.raw_transcript);
      expect(savedEvent.audio_recording_url).toBe(voiceEventData.audio_recording_url);
      expect(savedEvent.voice_confidence_score).toBe(voiceEventData.voice_confidence_score);
      expect(savedEvent.metadata?.voice_processed).toBe(true);
    });

    it('should validate seafood product names against known vocabulary', async () => {
      const validProducts = ['salmon', 'crab', 'halibut', 'tuna'];
      const invalidProducts = ['chicken', 'beef', 'unknown_fish'];
      
      for (const product of validProducts) {
        const voiceCommand = await mockOpenAI.processTranscript(`Received 10 pounds of ${product}`);
        expect(voiceCommand.product_name).toBe(product);
        expect(voiceCommand.confidence_breakdown?.product_match).toBeGreaterThan(0.8);
      }
      
      for (const product of invalidProducts) {
        const voiceCommand = await mockOpenAI.processTranscript(`Received 10 pounds of ${product}`);
        expect(voiceCommand.confidence_breakdown?.product_match).toBeLessThan(0.6);
      }
    });

    it('should handle voice events with temperature and quality data', async () => {
      const complexTranscript = 'Received 30 pounds of fresh salmon from Ocean Fresh at 34 degrees Fahrenheit, excellent quality';
      const voiceCommand = await mockOpenAI.processTranscript(complexTranscript);
      
      const voiceEventData = {
        event_type: voiceCommand.event_type!,
        product_name: voiceCommand.product_name!,
        quantity: voiceCommand.quantity!,
        unit: voiceCommand.unit!,
        vendor_name: voiceCommand.vendor_name,
        voice_confidence_score: voiceCommand.confidence_score,
        voice_confidence_breakdown: voiceCommand.confidence_breakdown!,
        raw_transcript: voiceCommand.raw_transcript,
        temperature: 34,
        temperature_unit: 'fahrenheit' as const,
        condition: 'Excellent' as const,
        occurred_at: new Date().toISOString()
      };
      
      const savedEvent = await voiceEventService.createVoiceEvent(voiceEventData);
      expect(savedEvent.metadata?.temperature_unit).toBe('fahrenheit');
    });
  });

  describe('Real-time Features', () => {
    it('should support real-time voice event updates', async () => {
      const eventCallbacks: any[] = [];
      
      // Mock real-time subscription
      const mockSubscription = {
        on: vi.fn((event: string, callback: Function) => {
          eventCallbacks.push({ event, callback });
          return mockSubscription;
        }),
        subscribe: vi.fn(() => ({ unsubscribe: vi.fn() }))
      };
      
      // Simulate voice event creation triggering real-time update
      const voiceEventData = {
        event_type: 'receiving' as const,
        product_name: 'salmon',
        quantity: 25,
        unit: 'lbs' as const,
        voice_confidence_score: 0.9,
        voice_confidence_breakdown: {
          product_match: 0.9,
          quantity_extraction: 0.9,
          vendor_match: 0.9,
          overall: 0.9
        },
        raw_transcript: 'Received 25 pounds of salmon',
        occurred_at: new Date().toISOString()
      };
      
      const savedEvent = await voiceEventService.createVoiceEvent(voiceEventData);
      
      // Simulate real-time notification
      if (eventCallbacks.length > 0) {
        const insertCallback = eventCallbacks.find(cb => cb.event === 'INSERT');
        if (insertCallback) {
          insertCallback.callback({
            eventType: 'INSERT',
            new: savedEvent,
            old: null
          });
        }
      }
      
      expect(savedEvent).toBeDefined();
    });
  });

  describe('Voice Processing Analytics', () => {
    it('should track voice processing metrics', async () => {
      const scenarios = generateVoiceTestScenarios();
      const metrics = {
        totalRequests: 0,
        successfulProcessing: 0,
        highConfidenceCount: 0,
        averageConfidence: 0,
        totalProcessingTime: 0
      };
      
      for (const scenario of scenarios.slice(0, 3)) { // Test first 3 scenarios
        const startTime = performance.now();
        
        try {
          const transcription = await mockOpenAI.transcribeAudio(scenario.audio);
          const voiceCommand = await mockOpenAI.processTranscript(transcription.text);
          
          metrics.totalRequests++;
          metrics.successfulProcessing++;
          metrics.averageConfidence += voiceCommand.confidence_score;
          
          if (voiceCommand.confidence_score > 0.8) {
            metrics.highConfidenceCount++;
          }
          
          const endTime = performance.now();
          metrics.totalProcessingTime += (endTime - startTime);
        } catch (error) {
          metrics.totalRequests++;
        }
      }
      
      metrics.averageConfidence = metrics.averageConfidence / metrics.successfulProcessing;
      
      // Verify analytics collection
      expect(metrics.totalRequests).toBe(3);
      expect(metrics.successfulProcessing).toBeGreaterThan(0);
      expect(metrics.averageConfidence).toBeGreaterThan(0.5);
      expect(metrics.highConfidenceCount).toBeGreaterThan(0);
    });
  });
});