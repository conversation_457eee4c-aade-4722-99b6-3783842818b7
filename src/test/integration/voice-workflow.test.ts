import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { supabase } from '../../lib/supabase';
import { voiceEventService } from '../../services/VoiceEventService';
import { enhancedVoiceEventProcessor } from '../../services/EnhancedVoiceEventProcessor';
import { audioStorageService } from '../../services/AudioStorageService';
import { VoiceEvent } from '../../types/schema';

// Mock external dependencies
vi.mock('../../lib/supabase');
vi.mock('../../services/VoiceEventService');
vi.mock('../../services/EnhancedVoiceEventProcessor');
vi.mock('../../services/AudioStorageService');

// Mock Web APIs
global.MediaRecorder = vi.fn().mockImplementation(() => ({
  start: vi.fn(),
  stop: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  state: 'inactive'
}));

global.navigator.mediaDevices = {
  getUserMedia: vi.fn().mockResolvedValue({
    getTracks: () => [{ stop: vi.fn() }]
  })
} as any;

global.Audio = vi.fn().mockImplementation(() => ({
  play: vi.fn().mockResolvedValue(undefined),
  pause: vi.fn(),
  load: vi.fn()
}));

const mockVoiceEvent: VoiceEvent = {
  id: 'test-event-1',
  event_type: 'receiving',
  product_name: 'Atlantic Salmon',
  quantity: 100,
  unit: 'lbs',
  vendor_name: 'Ocean Fresh Seafood',
  voice_confidence_score: 0.92,
  voice_confidence_breakdown: {
    product_match: 0.95,
    quantity_extraction: 0.90,
    vendor_match: 0.91,
    overall: 0.92
  },
  raw_transcript: 'Received one hundred pounds of Atlantic salmon from Ocean Fresh Seafood',
  audio_recording_url: 'https://storage.example.com/audio/test-event-1.wav',
  occurred_at: '2024-01-15T10:30:00Z',
  created_at: '2024-01-15T10:30:00Z',
  created_by_voice: true
};

describe('Voice Workflow Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup default mocks
    vi.mocked(voiceEventService.createVoiceEvent).mockResolvedValue(mockVoiceEvent);
    vi.mocked(voiceEventService.getVoiceEvents).mockResolvedValue([mockVoiceEvent]);
    vi.mocked(voiceEventService.updateVoiceEvent).mockResolvedValue(mockVoiceEvent);
    vi.mocked(voiceEventService.deleteVoiceEvent).mockResolvedValue();
    
    vi.mocked(enhancedVoiceEventProcessor.processVoiceEvent).mockResolvedValue({
      eventData: mockVoiceEvent,
      confidence: 0.92,
      requiresConfirmation: false,
      audioConfirmation: 'Successfully recorded receiving of 100 pounds of Atlantic salmon from Ocean Fresh Seafood.'
    });
    
    vi.mocked(audioStorageService.uploadAudioRecording).mockResolvedValue('https://storage.example.com/audio/test-event-1.wav');
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Complete Voice Event Creation Workflow', () => {
    it('should handle end-to-end voice event creation successfully', async () => {
      // This test simulates the complete workflow:
      // 1. User starts voice recording
      // 2. Audio is processed
      // 3. Event is created in database
      // 4. Audio is stored
      // 5. User sees confirmation

      const mockAudioBlob = new Blob(['mock audio data'], { type: 'audio/webm' });
      
      // Simulate voice processing workflow
      const processingResult = await enhancedVoiceEventProcessor.processVoiceEvent(mockAudioBlob, 'test-user-id');
      
      expect(processingResult).toEqual({
        eventData: mockVoiceEvent,
        confidence: 0.92,
        requiresConfirmation: false,
        audioConfirmation: expect.any(String)
      });
      
      // Verify service calls
      expect(enhancedVoiceEventProcessor.processVoiceEvent).toHaveBeenCalledWith(mockAudioBlob, 'test-user-id');
      
      // Verify event was created with correct data
      expect(processingResult.eventData.product_name).toBe('Atlantic Salmon');
      expect(processingResult.eventData.quantity).toBe(100);
      expect(processingResult.eventData.voice_confidence_score).toBe(0.92);
      expect(processingResult.eventData.created_by_voice).toBe(true);
    });

    it('should handle low confidence events requiring review', async () => {
      const lowConfidenceEvent = {
        ...mockVoiceEvent,
        voice_confidence_score: 0.65,
        voice_confidence_breakdown: {
          product_match: 0.60,
          quantity_extraction: 0.70,
          vendor_match: 0.65,
          overall: 0.65
        }
      };

      vi.mocked(enhancedVoiceEventProcessor.processVoiceEvent).mockResolvedValue({
        eventData: lowConfidenceEvent,
        confidence: 0.65,
        requiresConfirmation: true,
        audioConfirmation: 'Event recorded but requires review due to low confidence.'
      });

      const mockAudioBlob = new Blob(['mock audio data'], { type: 'audio/webm' });
      const result = await enhancedVoiceEventProcessor.processVoiceEvent(mockAudioBlob, 'test-user-id');

      expect(result.requiresConfirmation).toBe(true);
      expect(result.confidence).toBe(0.65);
      expect(result.eventData.voice_confidence_score).toBe(0.65);
    });

    it('should handle voice processing errors gracefully', async () => {
      const processingError = new Error('Voice processing failed: API timeout');
      vi.mocked(enhancedVoiceEventProcessor.processVoiceEvent).mockRejectedValue(processingError);

      const mockAudioBlob = new Blob(['mock audio data'], { type: 'audio/webm' });
      
      await expect(enhancedVoiceEventProcessor.processVoiceEvent(mockAudioBlob, 'test-user-id'))
        .rejects.toThrow('Voice processing failed: API timeout');
    });
  });

  describe('Voice Event Management Workflow', () => {
    it('should retrieve and display voice events correctly', async () => {
      const mockEvents = [
        mockVoiceEvent,
        {
          ...mockVoiceEvent,
          id: 'test-event-2',
          product_name: 'Pacific Cod',
          quantity: 75,
          voice_confidence_score: 0.88
        }
      ];

      vi.mocked(voiceEventService.getVoiceEvents).mockResolvedValue(mockEvents);

      const events = await voiceEventService.getVoiceEvents({
        confidenceThreshold: 0.7,
        eventType: ['receiving']
      });

      expect(events).toHaveLength(2);
      expect(events[0].product_name).toBe('Atlantic Salmon');
      expect(events[1].product_name).toBe('Pacific Cod');
      expect(voiceEventService.getVoiceEvents).toHaveBeenCalledWith({
        confidenceThreshold: 0.7,
        eventType: ['receiving']
      });
    });

    it('should update voice events with audit trail', async () => {
      const updatedEvent = {
        ...mockVoiceEvent,
        product_name: 'Atlantic Salmon - Premium Grade',
        quantity: 105
      };

      vi.mocked(voiceEventService.updateVoiceEvent).mockResolvedValue(updatedEvent);

      const result = await voiceEventService.updateVoiceEvent(
        'test-event-1',
        {
          product_name: 'Atlantic Salmon - Premium Grade',
          quantity: 105
        },
        'test-user-id',
        'Corrected product grade and quantity'
      );

      expect(result.product_name).toBe('Atlantic Salmon - Premium Grade');
      expect(result.quantity).toBe(105);
      expect(voiceEventService.updateVoiceEvent).toHaveBeenCalledWith(
        'test-event-1',
        {
          product_name: 'Atlantic Salmon - Premium Grade',
          quantity: 105
        },
        'test-user-id',
        'Corrected product grade and quantity'
      );
    });

    it('should delete voice events and cleanup audio files', async () => {
      vi.mocked(audioStorageService.deleteAudio).mockResolvedValue(true);

      await voiceEventService.deleteVoiceEvent('test-event-1');

      expect(voiceEventService.deleteVoiceEvent).toHaveBeenCalledWith('test-event-1');
    });
  });

  describe('Quality Review Workflow', () => {
    it('should identify events needing quality review', async () => {
      const lowConfidenceEvents = [
        {
          ...mockVoiceEvent,
          id: 'low-conf-1',
          voice_confidence_score: 0.65,
          product_name: 'Unknown Fish'
        },
        {
          ...mockVoiceEvent,
          id: 'low-conf-2',
          voice_confidence_score: 0.58,
          product_name: 'Unclear Product'
        }
      ];

      vi.mocked(voiceEventService.getEventsForQualityReview).mockResolvedValue(lowConfidenceEvents);

      const eventsForReview = await voiceEventService.getEventsForQualityReview(0.7);

      expect(eventsForReview).toHaveLength(2);
      expect(eventsForReview[0].voice_confidence_score).toBe(0.65);
      expect(eventsForReview[1].voice_confidence_score).toBe(0.58);
    });

    it('should approve voice events during quality review', async () => {
      vi.mocked(voiceEventService.approveVoiceEvent).mockResolvedValue();

      await voiceEventService.approveVoiceEvent('test-event-1', 'reviewer-id');

      expect(voiceEventService.approveVoiceEvent).toHaveBeenCalledWith('test-event-1', 'reviewer-id');
    });

    it('should reject voice events with reasons', async () => {
      vi.mocked(voiceEventService.rejectVoiceEvent).mockResolvedValue();

      await voiceEventService.rejectVoiceEvent(
        'test-event-1',
        'Incorrect product identification',
        'reviewer-id'
      );

      expect(voiceEventService.rejectVoiceEvent).toHaveBeenCalledWith(
        'test-event-1',
        'Incorrect product identification',
        'reviewer-id'
      );
    });

    it('should handle batch approval operations', async () => {
      const eventIds = ['event-1', 'event-2', 'event-3'];
      vi.mocked(voiceEventService.batchApproveVoiceEvents).mockResolvedValue();

      await voiceEventService.batchApproveVoiceEvents(eventIds, 'reviewer-id');

      expect(voiceEventService.batchApproveVoiceEvents).toHaveBeenCalledWith(eventIds, 'reviewer-id');
    });
  });

  describe('Audio Storage Workflow', () => {
    it('should upload and retrieve audio recordings', async () => {
      const mockAudioBlob = new Blob(['audio data'], { type: 'audio/webm' });
      const expectedUrl = 'https://storage.example.com/audio/test-event-1.wav';

      vi.mocked(audioStorageService.uploadAudioRecording).mockResolvedValue(expectedUrl);
      vi.mocked(audioStorageService.getAudioRecordingUrl).mockResolvedValue(expectedUrl);

      // Upload audio
      const uploadUrl = await audioStorageService.uploadAudioRecording(
        mockAudioBlob,
        'test-event-1',
        'test-user-id'
      );

      expect(uploadUrl).toBe(expectedUrl);
      expect(audioStorageService.uploadAudioRecording).toHaveBeenCalledWith(
        mockAudioBlob,
        'test-event-1',
        'test-user-id'
      );

      // Retrieve audio URL
      const retrieveUrl = await audioStorageService.getAudioRecordingUrl('test-event-1');
      expect(retrieveUrl).toBe(expectedUrl);
    });

    it('should handle audio upload failures gracefully', async () => {
      const mockAudioBlob = new Blob(['audio data'], { type: 'audio/webm' });
      vi.mocked(audioStorageService.uploadAudioRecording).mockResolvedValue(null);

      const result = await audioStorageService.uploadAudioRecording(mockAudioBlob, 'test-event-1');

      expect(result).toBeNull();
    });

    it('should cleanup audio files when events are deleted', async () => {
      vi.mocked(audioStorageService.deleteAudio).mockResolvedValue(true);

      const result = await audioStorageService.deleteAudio('test-event-1');

      expect(result).toBe(true);
      expect(audioStorageService.deleteAudio).toHaveBeenCalledWith('test-event-1');
    });
  });

  describe('Error Handling and Recovery', () => {
    it('should handle database connection failures', async () => {
      const dbError = new Error('Database connection failed');
      vi.mocked(voiceEventService.createVoiceEvent).mockRejectedValue(dbError);

      const mockAudioBlob = new Blob(['audio data'], { type: 'audio/webm' });
      
      await expect(voiceEventService.createVoiceEvent({
        event_type: 'receiving',
        product_name: 'Test Product',
        quantity: 10,
        unit: 'lbs',
        voice_confidence_score: 0.9,
        voice_confidence_breakdown: {
          product_match: 0.9,
          quantity_extraction: 0.9,
          vendor_match: 0.9,
          overall: 0.9
        },
        raw_transcript: 'Test transcript'
      })).rejects.toThrow('Database connection failed');
    });

    it('should handle API rate limiting', async () => {
      const rateLimitError = new Error('API rate limit exceeded');
      vi.mocked(enhancedVoiceEventProcessor.processVoiceEvent).mockRejectedValue(rateLimitError);

      const mockAudioBlob = new Blob(['audio data'], { type: 'audio/webm' });
      
      await expect(enhancedVoiceEventProcessor.processVoiceEvent(mockAudioBlob, 'test-user-id'))
        .rejects.toThrow('API rate limit exceeded');
    });

    it('should handle storage service failures', async () => {
      const storageError = new Error('Storage service unavailable');
      vi.mocked(audioStorageService.uploadAudioRecording).mockRejectedValue(storageError);

      const mockAudioBlob = new Blob(['audio data'], { type: 'audio/webm' });
      
      await expect(audioStorageService.uploadAudioRecording(mockAudioBlob, 'test-event-1'))
        .rejects.toThrow('Storage service unavailable');
    });
  });

  describe('Real-time Updates and Subscriptions', () => {
    it('should handle real-time voice event updates', async () => {
      const mockChannel = {
        on: vi.fn().mockReturnThis(),
        subscribe: vi.fn().mockReturnThis(),
        unsubscribe: vi.fn()
      };

      vi.mocked(supabase.channel).mockReturnValue(mockChannel as any);

      // Simulate setting up real-time subscription
      const channel = supabase.channel('voice-events-changes');
      channel.on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'inventory_events',
        filter: 'created_by_voice=eq.true'
      }, vi.fn());

      expect(supabase.channel).toHaveBeenCalledWith('voice-events-changes');
      expect(mockChannel.on).toHaveBeenCalledWith(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'inventory_events',
          filter: 'created_by_voice=eq.true'
        },
        expect.any(Function)
      );
    });
  });

  describe('Performance and Optimization', () => {
    it('should handle large batch operations efficiently', async () => {
      const largeEventBatch = Array.from({ length: 100 }, (_, i) => `event-${i}`);
      vi.mocked(voiceEventService.batchApproveVoiceEvents).mockResolvedValue();

      const startTime = Date.now();
      await voiceEventService.batchApproveVoiceEvents(largeEventBatch, 'reviewer-id');
      const endTime = Date.now();

      expect(voiceEventService.batchApproveVoiceEvents).toHaveBeenCalledWith(largeEventBatch, 'reviewer-id');
      
      // Ensure operation completes in reasonable time (mock should be fast)
      expect(endTime - startTime).toBeLessThan(1000);
    });

    it('should handle concurrent voice processing requests', async () => {
      const mockAudioBlob = new Blob(['audio data'], { type: 'audio/webm' });
      const concurrentRequests = 5;

      const promises = Array.from({ length: concurrentRequests }, () =>
        enhancedVoiceEventProcessor.processVoiceEvent(mockAudioBlob, 'test-user-id')
      );

      const results = await Promise.all(promises);

      expect(results).toHaveLength(concurrentRequests);
      results.forEach(result => {
        expect(result.eventData).toBeDefined();
        expect(result.confidence).toBeGreaterThan(0);
      });
    });
  });
});