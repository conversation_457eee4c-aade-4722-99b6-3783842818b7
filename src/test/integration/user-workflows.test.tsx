import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { NavigationProvider } from '../../contexts/NavigationContext';
import { VoiceEventList } from '../../components/voice/VoiceEventList';
import { VoiceEventEditor } from '../../components/voice/VoiceEventEditor';
import { QualityReviewPanel } from '../../components/voice/QualityReviewPanel';
import { voiceEventService } from '../../services/VoiceEventService';
import { voiceAnalyticsService } from '../../services/VoiceAnalyticsService';
import { VoiceEvent } from '../../types/schema';

// Mock services
vi.mock('../../services/VoiceEventService');
vi.mock('../../services/VoiceAnalyticsService');
vi.mock('../../lib/supabase', () => ({
  supabase: {
    channel: vi.fn().mockReturnValue({
      on: vi.fn().mockReturnThis(),
      subscribe: vi.fn().mockReturnThis(),
      unsubscribe: vi.fn()
    }),
    removeChannel: vi.fn()
  }
}));

// Mock Web APIs
global.Audio = vi.fn().mockImplementation(() => ({
  play: vi.fn().mockResolvedValue(undefined),
  pause: vi.fn(),
  load: vi.fn()
}));

const mockVoiceEvents: VoiceEvent[] = [
  {
    id: 'event-1',
    event_type: 'receiving',
    product_name: 'Atlantic Salmon',
    quantity: 100,
    unit: 'lbs',
    vendor_name: 'Ocean Fresh Seafood',
    voice_confidence_score: 0.95,
    voice_confidence_breakdown: {
      product_match: 0.96,
      quantity_extraction: 0.94,
      vendor_match: 0.95,
      overall: 0.95
    },
    raw_transcript: 'Received one hundred pounds of Atlantic salmon from Ocean Fresh Seafood',
    audio_recording_url: 'https://storage.example.com/audio/event-1.wav',
    occurred_at: '2024-01-15T10:30:00Z',
    created_at: '2024-01-15T10:30:00Z',
    created_by_voice: true
  },
  {
    id: 'event-2',
    event_type: 'sale',
    product_name: 'Pacific Cod',
    quantity: 50,
    unit: 'lbs',
    customer_name: 'Fresh Market Co',
    voice_confidence_score: 0.88,
    voice_confidence_breakdown: {
      product_match: 0.90,
      quantity_extraction: 0.85,
      vendor_match: 0.89,
      overall: 0.88
    },
    raw_transcript: 'Sold fifty pounds of Pacific cod to Fresh Market Co',
    audio_recording_url: 'https://storage.example.com/audio/event-2.wav',
    occurred_at: '2024-01-15T14:20:00Z',
    created_at: '2024-01-15T14:20:00Z',
    created_by_voice: true
  },
  {
    id: 'event-3',
    event_type: 'receiving',
    product_name: 'Unknown Fish',
    quantity: 25,
    unit: 'lbs',
    vendor_name: 'Unclear Vendor',
    voice_confidence_score: 0.62,
    voice_confidence_breakdown: {
      product_match: 0.55,
      quantity_extraction: 0.75,
      vendor_match: 0.58,
      overall: 0.62
    },
    raw_transcript: 'Received twenty five pounds of fish from vendor',
    audio_recording_url: 'https://storage.example.com/audio/event-3.wav',
    occurred_at: '2024-01-15T16:45:00Z',
    created_at: '2024-01-15T16:45:00Z',
    created_by_voice: true
  }
];

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });

  return (
    <QueryClientProvider client={queryClient}>
      <NavigationProvider>
        {children}
      </NavigationProvider>
    </QueryClientProvider>
  );
};

describe('User Workflows Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup default service mocks
    vi.mocked(voiceEventService.getVoiceEvents).mockResolvedValue(mockVoiceEvents);
    vi.mocked(voiceEventService.getVoiceEventStatistics).mockResolvedValue({
      totalEvents: 3,
      highConfidenceEvents: 1,
      mediumConfidenceEvents: 1,
      lowConfidenceEvents: 1,
      averageConfidence: 0.82,
      eventsNeedingReview: 1
    });
    vi.mocked(voiceEventService.getEventsForQualityReview).mockResolvedValue([mockVoiceEvents[2]]);
    vi.mocked(voiceAnalyticsService.getAccuracyTrends).mockResolvedValue({
      trend: 'up',
      currentAverage: 0.85,
      previousAverage: 0.80,
      changePercentage: 6.25,
      dailyAverages: []
    });
    vi.mocked(voiceAnalyticsService.getReviewStatistics).mockResolvedValue({
      totalReviewed: 5,
      approved: 4,
      rejected: 1,
      approvalRate: 80,
      commonRejectionReasons: []
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Voice Event Viewing Workflow', () => {
    it('should display voice events with filtering and search', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <VoiceEventList />
        </TestWrapper>
      );

      // Wait for events to load
      await waitFor(() => {
        expect(screen.getByText('Atlantic Salmon')).toBeInTheDocument();
        expect(screen.getByText('Pacific Cod')).toBeInTheDocument();
        expect(screen.getByText('Unknown Fish')).toBeInTheDocument();
      });

      // Test confidence indicators
      expect(screen.getByText('95% high')).toBeInTheDocument();
      expect(screen.getByText('88% medium')).toBeInTheDocument();
      expect(screen.getByText('62% low')).toBeInTheDocument();

      // Test search functionality
      const searchInput = screen.getByPlaceholderText('Search products, vendors...');
      await user.type(searchInput, 'salmon');
      await user.keyboard('{Enter}');

      await waitFor(() => {
        expect(voiceEventService.getVoiceEvents).toHaveBeenCalledWith(
          expect.objectContaining({
            searchQuery: 'salmon'
          })
        );
      });
    });

    it('should filter events by confidence level', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <VoiceEventList />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Atlantic Salmon')).toBeInTheDocument();
      });

      // Filter by high confidence
      const confidenceFilter = screen.getByDisplayValue('All Confidence');
      await user.selectOptions(confidenceFilter, 'High (90%+)');

      await waitFor(() => {
        expect(voiceEventService.getVoiceEvents).toHaveBeenCalledWith(
          expect.objectContaining({
            confidenceThreshold: 0.9
          })
        );
      });
    });

    it('should filter events by event type', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <VoiceEventList />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Atlantic Salmon')).toBeInTheDocument();
      });

      // Filter by receiving events
      const eventTypeFilter = screen.getByDisplayValue('All Types');
      await user.selectOptions(eventTypeFilter, 'Receiving');

      await waitFor(() => {
        expect(voiceEventService.getVoiceEvents).toHaveBeenCalledWith(
          expect.objectContaining({
            eventType: ['receiving']
          })
        );
      });
    });

    it('should play audio recordings', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <VoiceEventList />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Atlantic Salmon')).toBeInTheDocument();
      });

      // Click audio play button
      const audioButtons = screen.getAllByTitle('Play audio recording');
      await user.click(audioButtons[0]);

      expect(global.Audio).toHaveBeenCalledWith('https://storage.example.com/audio/event-1.wav');
    });
  });

  describe('Voice Event Editing Workflow', () => {
    it('should edit voice event with validation', async () => {
      const user = userEvent.setup();
      const mockOnSave = vi.fn().mockResolvedValue(undefined);
      const mockOnCancel = vi.fn();

      vi.mocked(voiceEventService.updateVoiceEvent).mockResolvedValue({
        ...mockVoiceEvents[0],
        product_name: 'Atlantic Salmon - Premium Grade',
        quantity: 105
      });

      render(
        <TestWrapper>
          <VoiceEventEditor
            event={mockVoiceEvents[0]}
            onSave={mockOnSave}
            onCancel={mockOnCancel}
            userId="test-user-id"
          />
        </TestWrapper>
      );

      // Wait for component to load
      await waitFor(() => {
        expect(screen.getByDisplayValue('Atlantic Salmon')).toBeInTheDocument();
      });

      // Edit product name
      const productNameInput = screen.getByDisplayValue('Atlantic Salmon');
      await user.clear(productNameInput);
      await user.type(productNameInput, 'Atlantic Salmon - Premium Grade');

      // Edit quantity
      const quantityInput = screen.getByDisplayValue('100');
      await user.clear(quantityInput);
      await user.type(quantityInput, '105');

      // Save changes
      const saveButton = screen.getByText('Save Changes');
      await user.click(saveButton);

      await waitFor(() => {
        expect(voiceEventService.updateVoiceEvent).toHaveBeenCalledWith(
          'event-1',
          expect.objectContaining({
            product_name: 'Atlantic Salmon - Premium Grade',
            quantity: 105
          }),
          'test-user-id',
          'Manual edit via voice event editor'
        );
        expect(mockOnSave).toHaveBeenCalled();
      });
    });

    it('should validate required fields', async () => {
      const user = userEvent.setup();
      const mockOnSave = vi.fn();
      const mockOnCancel = vi.fn();

      render(
        <TestWrapper>
          <VoiceEventEditor
            event={mockVoiceEvents[0]}
            onSave={mockOnSave}
            onCancel={mockOnCancel}
          />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByDisplayValue('Atlantic Salmon')).toBeInTheDocument();
      });

      // Clear required field
      const productNameInput = screen.getByDisplayValue('Atlantic Salmon');
      await user.clear(productNameInput);

      // Try to save
      const saveButton = screen.getByText('Save Changes');
      await user.click(saveButton);

      // Should show validation error
      await waitFor(() => {
        expect(screen.getByText('Product name is required')).toBeInTheDocument();
      });

      expect(mockOnSave).not.toHaveBeenCalled();
    });

    it('should display confidence breakdown', async () => {
      render(
        <TestWrapper>
          <VoiceEventEditor
            event={mockVoiceEvents[0]}
            onSave={vi.fn()}
            onCancel={vi.fn()}
          />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Voice Processing Confidence')).toBeInTheDocument();
        expect(screen.getByText('Overall: 95%')).toBeInTheDocument();
        expect(screen.getByText('Product Match:')).toBeInTheDocument();
        expect(screen.getByText('96%')).toBeInTheDocument();
        expect(screen.getByText('Quantity:')).toBeInTheDocument();
        expect(screen.getByText('94%')).toBeInTheDocument();
        expect(screen.getByText('Vendor Match:')).toBeInTheDocument();
        expect(screen.getByText('95%')).toBeInTheDocument();
      });
    });

    it('should show original transcript', async () => {
      render(
        <TestWrapper>
          <VoiceEventEditor
            event={mockVoiceEvents[0]}
            onSave={vi.fn()}
            onCancel={vi.fn()}
          />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Original Transcript')).toBeInTheDocument();
        expect(screen.getByText('"Received one hundred pounds of Atlantic salmon from Ocean Fresh Seafood"')).toBeInTheDocument();
      });
    });
  });

  describe('Quality Review Workflow', () => {
    it('should display events needing review', async () => {
      render(
        <TestWrapper>
          <QualityReviewPanel />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Quality Review')).toBeInTheDocument();
        expect(screen.getByText('Unknown Fish')).toBeInTheDocument();
        expect(screen.getByText('62% confidence')).toBeInTheDocument();
      });

      expect(voiceEventService.getEventsForQualityReview).toHaveBeenCalledWith(0.7);
    });

    it('should show analytics when toggled', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <QualityReviewPanel />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Show Analytics')).toBeInTheDocument();
      });

      // Toggle analytics
      const analyticsButton = screen.getByText('Show Analytics');
      await user.click(analyticsButton);

      await waitFor(() => {
        expect(screen.getByText('Review Analytics')).toBeInTheDocument();
        expect(screen.getByText('Pending Review')).toBeInTheDocument();
        expect(screen.getByText('1')).toBeInTheDocument(); // Pending count
        expect(screen.getByText('Avg Confidence')).toBeInTheDocument();
        expect(screen.getByText('62%')).toBeInTheDocument(); // Average confidence
      });
    });

    it('should approve individual events', async () => {
      const user = userEvent.setup();
      const mockOnApprove = vi.fn();
      
      vi.mocked(voiceEventService.approveVoiceEvent).mockResolvedValue();

      render(
        <TestWrapper>
          <QualityReviewPanel onEventApprove={mockOnApprove} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Unknown Fish')).toBeInTheDocument();
      });

      // Click approve button
      const approveButton = screen.getByTitle('Approve event');
      await user.click(approveButton);

      await waitFor(() => {
        expect(voiceEventService.approveVoiceEvent).toHaveBeenCalledWith('event-3');
        expect(mockOnApprove).toHaveBeenCalledWith('event-3');
      });
    });

    it('should reject individual events with reason', async () => {
      const user = userEvent.setup();
      const mockOnReject = vi.fn();
      
      vi.mocked(voiceEventService.rejectVoiceEvent).mockResolvedValue();
      
      // Mock window.prompt
      const mockPrompt = vi.fn().mockReturnValue('Incorrect product identification');
      global.prompt = mockPrompt;

      render(
        <TestWrapper>
          <QualityReviewPanel onEventReject={mockOnReject} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Unknown Fish')).toBeInTheDocument();
      });

      // Click reject button
      const rejectButton = screen.getByTitle('Reject event');
      await user.click(rejectButton);

      await waitFor(() => {
        expect(mockPrompt).toHaveBeenCalledWith('Please provide a reason for rejection:');
        expect(voiceEventService.rejectVoiceEvent).toHaveBeenCalledWith('event-3', 'Incorrect product identification');
        expect(mockOnReject).toHaveBeenCalledWith('event-3', 'Incorrect product identification');
      });
    });

    it('should handle empty review queue', async () => {
      vi.mocked(voiceEventService.getEventsForQualityReview).mockResolvedValue([]);

      render(
        <TestWrapper>
          <QualityReviewPanel />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('All caught up!')).toBeInTheDocument();
        expect(screen.getByText('No voice events require quality review at this time.')).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling in User Workflows', () => {
    it('should handle service errors gracefully in event list', async () => {
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      vi.mocked(voiceEventService.getVoiceEvents).mockRejectedValue(new Error('Service unavailable'));

      render(
        <TestWrapper>
          <VoiceEventList />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Failed to load voice events. Please try again.')).toBeInTheDocument();
      });

      expect(consoleErrorSpy).toHaveBeenCalled();
      consoleErrorSpy.mockRestore();
    });

    it('should handle save errors in event editor', async () => {
      const user = userEvent.setup();
      const mockOnSave = vi.fn();
      
      vi.mocked(voiceEventService.updateVoiceEvent).mockRejectedValue(new Error('Database error'));

      render(
        <TestWrapper>
          <VoiceEventEditor
            event={mockVoiceEvents[0]}
            onSave={mockOnSave}
            onCancel={vi.fn()}
          />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByDisplayValue('Atlantic Salmon')).toBeInTheDocument();
      });

      // Try to save
      const saveButton = screen.getByText('Save Changes');
      await user.click(saveButton);

      await waitFor(() => {
        expect(screen.getByText('Failed to save event. Please try again.')).toBeInTheDocument();
      });

      expect(mockOnSave).not.toHaveBeenCalled();
    });

    it('should handle review service errors', async () => {
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      vi.mocked(voiceEventService.getEventsForQualityReview).mockRejectedValue(new Error('Service error'));

      render(
        <TestWrapper>
          <QualityReviewPanel />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Failed to load events for review. Please try again.')).toBeInTheDocument();
      });

      expect(consoleErrorSpy).toHaveBeenCalled();
      consoleErrorSpy.mockRestore();
    });
  });

  describe('Real-time Updates in Workflows', () => {
    it('should refresh event list when real-time updates occur', async () => {
      const { rerender } = render(
        <TestWrapper>
          <VoiceEventList />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Atlantic Salmon')).toBeInTheDocument();
      });

      // Simulate new event added
      const updatedEvents = [
        ...mockVoiceEvents,
        {
          id: 'event-4',
          event_type: 'receiving',
          product_name: 'Fresh Tuna',
          quantity: 75,
          unit: 'lbs',
          voice_confidence_score: 0.91,
          voice_confidence_breakdown: {
            product_match: 0.92,
            quantity_extraction: 0.90,
            vendor_match: 0.91,
            overall: 0.91
          },
          raw_transcript: 'Received seventy five pounds of fresh tuna',
          occurred_at: '2024-01-15T18:00:00Z',
          created_at: '2024-01-15T18:00:00Z',
          created_by_voice: true
        }
      ];

      vi.mocked(voiceEventService.getVoiceEvents).mockResolvedValue(updatedEvents);

      // Trigger re-render (simulating real-time update)
      rerender(
        <TestWrapper>
          <VoiceEventList />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Fresh Tuna')).toBeInTheDocument();
      });
    });
  });

  describe('Accessibility in User Workflows', () => {
    it('should support keyboard navigation in event list', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <VoiceEventList />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Atlantic Salmon')).toBeInTheDocument();
      });

      // Tab to search input
      await user.tab();
      expect(screen.getByPlaceholderText('Search products, vendors...')).toHaveFocus();

      // Tab to event type filter
      await user.tab();
      expect(screen.getByDisplayValue('All Types')).toHaveFocus();
    });

    it('should have proper ARIA labels and roles', async () => {
      render(
        <TestWrapper>
          <VoiceEventList />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Atlantic Salmon')).toBeInTheDocument();
      });

      // Check for proper labeling
      expect(screen.getByLabelText(/Search/)).toBeInTheDocument();
      expect(screen.getByLabelText(/Event Type/)).toBeInTheDocument();
      expect(screen.getByLabelText(/Min Confidence/)).toBeInTheDocument();
    });
  });
});