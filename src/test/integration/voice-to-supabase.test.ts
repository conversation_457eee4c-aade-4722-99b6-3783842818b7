/**
 * Integration tests for voice processing to database workflow
 * Tests the complete flow from voice input to data persistence
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { createMockSupabaseClient, generateMockInventoryEvent, mockDatabase } from '../mocks/supabase-mocks';
import { MockOpenAIClient, generateVoiceTestScenarios } from '../mocks/openai-mocks';
import { MockSpeechRecognition } from '../mocks/voice-mocks';

// Voice processing workflow simulation
class VoiceWorkflowSimulator {
  private mockSupabase = createMockSupabaseClient();
  private mockOpenAI = new MockOpenAIClient();

  constructor() {
    this.reset();
  }

  reset() {
    this.mockSupabase.reset();
    this.mockOpenAI.reset();
    mockDatabase.clear();
  }

  async simulateVoiceInput(transcript: string): Promise<{
    success: boolean;
    data?: any;
    error?: string;
    confidence?: number;
  }> {
    try {
      // Step 1: OpenAI Processing
      const voiceCommand = await this.mockOpenAI.processTranscript(transcript);

      // Step 2: Validation
      if (voiceCommand.confidence_score < 0.3) {
        return {
          success: false,
          error: 'Confidence too low for processing',
          confidence: voiceCommand.confidence_score
        };
      }

      // Step 3: Database Storage
      const eventData = {
        event_type: voiceCommand.event_type,
        product_id: await this.findOrCreateProduct(voiceCommand.product_name),
        quantity: voiceCommand.quantity,
        unit: voiceCommand.unit,
        vendor_name: voiceCommand.vendor_name,
        customer_name: voiceCommand.customer_name,
        voice_confidence_score: voiceCommand.confidence_score,
        voice_confidence_breakdown: voiceCommand.confidence_breakdown,
        raw_transcript: voiceCommand.raw_transcript,
        created_by_voice: true
      };

      const result = await this.mockSupabase
        .from('inventory_events')
        .insert(eventData);

      if (result.error) {
        return {
          success: false,
          error: result.error.message
        };
      }

      return {
        success: true,
        data: result.data,
        confidence: voiceCommand.confidence_score
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async findOrCreateProduct(productName?: string): Promise<string> {
    if (!productName || productName === 'unknown') {
      return 'unknown-product';
    }

    // Check if product exists
    const existingProduct = await this.mockSupabase
      .from('products')
      .select('id')
      .eq('name', productName)
      .single();

    if (existingProduct.data) {
      return existingProduct.data.id;
    }

    // Create new product
    const newProduct = await this.mockSupabase
      .from('products')
      .insert({
        name: productName,
        category_id: this.getCategoryForProduct(productName)
      });

    return newProduct.data?.[0]?.id || 'fallback-product-id';
  }

  private getCategoryForProduct(productName: string): string {
    const lowerName = productName.toLowerCase();
    
    if (lowerName.includes('salmon') || lowerName.includes('cod') || lowerName.includes('tuna') || lowerName.includes('halibut')) {
      return 'cat-fish';
    }
    
    if (lowerName.includes('crab') || lowerName.includes('lobster') || lowerName.includes('shrimp')) {
      return 'cat-shellfish';
    }
    
    if (lowerName.includes('oyster') || lowerName.includes('clam') || lowerName.includes('mussel')) {
      return 'cat-mollusks';
    }
    
    return 'cat-fish'; // Default category
  }

  getDatabase() {
    return mockDatabase;
  }

  getSupabaseClient() {
    return this.mockSupabase;
  }

  getOpenAIClient() {
    return this.mockOpenAI;
  }
}

describe('Voice-to-Supabase Integration', () => {
  let simulator: VoiceWorkflowSimulator;

  beforeEach(() => {
    simulator = new VoiceWorkflowSimulator();
  });

  afterEach(() => {
    simulator.reset();
  });

  describe('Voice Processing Pipeline', () => {
    it('processes high-confidence voice input successfully', async () => {
      const transcript = 'Received 25 pounds of fresh salmon from Ocean Fresh Seafoods';
      
      const result = await simulator.simulateVoiceInput(transcript);
      
      expect(result.success).toBe(true);
      expect(result.confidence).toBeGreaterThan(0.8);
      
      // Verify data was stored in database
      const events = simulator.getDatabase().getTable('inventory_events');
      expect(events).toHaveLength(1);
      expect(events[0]).toMatchObject({
        event_type: 'receiving',
        quantity: 25,
        unit: 'pounds',
        vendor_name: 'Ocean Fresh Seafoods',
        created_by_voice: true
      });
    });

    it('handles product creation for new products', async () => {
      const transcript = 'Received 15 pounds of rare bluefin tuna';
      
      const result = await simulator.simulateVoiceInput(transcript);
      
      expect(result.success).toBe(true);
      
      const products = simulator.getDatabase().getTable('products');
      const events = simulator.getDatabase().getTable('inventory_events');
      
      expect(products.some(p => p.name === 'rare bluefin tuna')).toBe(true);
      expect(events).toHaveLength(1);
    });

    it('rejects low-confidence voice input', async () => {
      const openAI = simulator.getOpenAIClient();
      openAI.setResponseQuality('low');
      
      const transcript = 'mumbled speech... something... maybe fish...';
      
      const result = await simulator.simulateVoiceInput(transcript);
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('Confidence too low');
      expect(result.confidence).toBeLessThan(0.3);
      
      const events = simulator.getDatabase().getTable('inventory_events');
      expect(events).toHaveLength(0);
    });
  });

  describe('Error Handling', () => {
    it('handles OpenAI API failures', async () => {
      const openAI = simulator.getOpenAIClient();
      openAI.setShouldFail(true, 'timeout');
      
      const transcript = 'Received 25 pounds of salmon';
      
      const result = await simulator.simulateVoiceInput(transcript);
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('timeout');
    });

    it('handles database connection failures', async () => {
      const supabase = simulator.getSupabaseClient();
      supabase.setConfig({ shouldFail: true, failureType: 'network' });
      
      const transcript = 'Received 25 pounds of salmon from Ocean Fresh';
      
      const result = await simulator.simulateVoiceInput(transcript);
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('Network request failed');
    });
  });

  describe('Data Integrity', () => {
    it('maintains referential integrity between products and events', async () => {
      await simulator.simulateVoiceInput('Received 25 pounds of salmon from Ocean Fresh');
      await simulator.simulateVoiceInput('Sold 10 pounds of salmon to Marina Restaurant');
      
      const products = simulator.getDatabase().getTable('products');
      const events = simulator.getDatabase().getTable('inventory_events');
      
      expect(events).toHaveLength(2);
      
      const salmonProduct = products.find(p => p.name === 'salmon');
      expect(salmonProduct).toBeDefined();
      
      events.forEach(event => {
        expect(event.product_id).toBe(salmonProduct.id);
      });
    });

    it('stores complete voice processing metadata', async () => {
      const transcript = 'Received 30 pounds of cod from Pacific Catch';
      
      await simulator.simulateVoiceInput(transcript);
      
      const events = simulator.getDatabase().getTable('inventory_events');
      const event = events[0];
      
      expect(event).toMatchObject({
        raw_transcript: transcript,
        voice_confidence_score: expect.any(Number),
        voice_confidence_breakdown: expect.objectContaining({
          product_match: expect.any(Number),
          quantity_extraction: expect.any(Number),
          overall: expect.any(Number)
        }),
        created_by_voice: true
      });
    });
  });

  describe('Business Logic', () => {
    it('correctly categorizes different seafood types', async () => {
      const testCases = [
        { transcript: 'Received salmon', expectedCategory: 'cat-fish' },
        { transcript: 'Received dungeness crab', expectedCategory: 'cat-shellfish' },
        { transcript: 'Received oysters', expectedCategory: 'cat-mollusks' }
      ];
      
      for (const testCase of testCases) {
        await simulator.simulateVoiceInput(testCase.transcript);
      }
      
      const products = simulator.getDatabase().getTable('products');
      
      expect(products.find(p => p.name === 'salmon')?.category_id).toBe('cat-fish');
      expect(products.find(p => p.name === 'dungeness crab')?.category_id).toBe('cat-shellfish');
      expect(products.find(p => p.name === 'oysters')?.category_id).toBe('cat-mollusks');
    });

    it('handles different event types correctly', async () => {
      const eventTypes = [
        { transcript: 'Received 25 pounds of salmon', expectedType: 'receiving' },
        { transcript: 'Sold 15 pounds of cod', expectedType: 'sale' },
        { transcript: 'Disposed of 5 pounds of spoiled tuna', expectedType: 'disposal' },
        { transcript: 'Produced 20 pounds of filleted salmon', expectedType: 'production' },
        { transcript: 'Adjustment of 2 pounds salmon inventory', expectedType: 'adjustment' }
      ];
      
      for (const test of eventTypes) {
        await simulator.simulateVoiceInput(test.transcript);
      }
      
      const events = simulator.getDatabase().getTable('inventory_events');
      
      eventTypes.forEach((test, index) => {
        expect(events[index].event_type).toBe(test.expectedType);
      });
    });
  });
});