/**
 * Voice-to-Database Integration Test Suite
 * Tests the complete workflow from voice input to database storage with real-time updates
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { 
  MockOpenAIAPI, 
  VOICE_TEST_SAMPLES, 
  createMockVoiceEvent,
  MockSpeechRecognition,
  MockMediaRecorder
} from '../mocks/voice-mocks';
import { EnhancedVoiceEventProcessor } from '../../services/EnhancedVoiceEventProcessor';
import { OptimizedVoiceEventService } from '../../services/OptimizedVoiceEventService';
import { VoiceEventData, VoiceEvent } from '../../types/schema';

// Mock Supabase with realistic database behavior
const mockSupabaseResponses = {
  insertSuccess: { data: createMockVoiceEvent(), error: null },
  insertError: { data: null, error: { message: 'Database connection failed' } },
  querySuccess: { data: [createMockVoiceEvent()], error: null, count: 1 },
  queryError: { data: null, error: { message: 'Query failed' }, count: 0 }
};

const mockSupabase = {
  from: vi.fn(() => ({
    insert: vi.fn(() => ({
      select: vi.fn(() => ({
        single: vi.fn(() => Promise.resolve(mockSupabaseResponses.insertSuccess))
      }))
    })),
    select: vi.fn(() => ({
      eq: vi.fn(() => ({
        order: vi.fn(() => ({
          range: vi.fn(() => Promise.resolve(mockSupabaseResponses.querySuccess))
        }))
      }))
    })),
    update: vi.fn(() => ({
      eq: vi.fn(() => Promise.resolve({ data: createMockVoiceEvent(), error: null }))
    })),
    delete: vi.fn(() => ({
      eq: vi.fn(() => Promise.resolve({ data: null, error: null }))
    }))
  })),
  channel: vi.fn(() => ({
    on: vi.fn(() => ({
      subscribe: vi.fn((callback) => {
        // Simulate subscription success
        callback('SUBSCRIBED');
        return {
          unsubscribe: vi.fn()
        };
      })
    }))
  }))
};

vi.mock('../../lib/supabase', () => ({
  supabase: mockSupabase
}));

vi.mock('../../lib/voice-processor', () => ({
  default: class MockVoiceProcessor {
    async processAudioBlob(audioBlob: Blob): Promise<any> {
      const mockAPI = new MockOpenAIAPI();
      const transcript = await mockAPI.transcribe(audioBlob);
      return await mockAPI.processTranscript(transcript);
    }
    
    getProcessingStatus() {
      return false;
    }
  }
}));

describe('Voice-to-Database Integration', () => {
  let processor: EnhancedVoiceEventProcessor;
  let voiceService: OptimizedVoiceEventService;
  let mockAPI: MockOpenAIAPI;

  beforeEach(() => {
    processor = new EnhancedVoiceEventProcessor();
    voiceService = new OptimizedVoiceEventService();
    mockAPI = new MockOpenAIAPI();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Complete Voice Processing Workflow', () => {
    it('should process voice input and save to database successfully', async () => {
      // Create mock audio blob
      const audioBlob = new Blob(['mock audio data'], { type: 'audio/webm' });
      
      // Process voice event
      const result = await processor.processVoiceEvent(audioBlob, 'test-user');
      
      // Verify processing result
      expect(result.eventData).toBeDefined();
      expect(result.confidence).toBeGreaterThan(0);
      expect(result.audioConfirmation).toBeDefined();
      
      // Verify database interaction
      expect(mockSupabase.from).toHaveBeenCalledWith('inventory_events');
      expect(mockSupabase.from().insert).toHaveBeenCalled();
    });

    it('should handle high confidence events automatically', async () => {
      const highConfidenceSample = VOICE_TEST_SAMPLES.highConfidence.salmonReceiving;
      const audioBlob = new Blob([highConfidenceSample.transcript], { type: 'audio/webm' });
      
      const result = await processor.processVoiceEvent(audioBlob, 'test-user');
      
      expect(result.confidence).toBeGreaterThan(0.85);
      expect(result.requiresConfirmation).toBeFalsy();
      expect(result.audioConfirmation).toContain('Successfully recorded');
    });

    it('should flag medium confidence events for confirmation', async () => {
      const mediumConfidenceSample = VOICE_TEST_SAMPLES.mediumConfidence.unclearQuantity;
      const audioBlob = new Blob([mediumConfidenceSample.transcript], { type: 'audio/webm' });
      
      const result = await processor.processVoiceEvent(audioBlob, 'test-user');
      
      expect(result.confidence).toBeLessThan(0.85);
      expect(result.confidence).toBeGreaterThan(0.6);
      expect(result.requiresConfirmation).toBeTruthy();
    });

    it('should include proper metadata in database records', async () => {
      const audioBlob = new Blob(['mock audio data'], { type: 'audio/webm' });
      
      await processor.processVoiceEvent(audioBlob, 'test-user');
      
      const insertCall = mockSupabase.from().insert.mock.calls[0][0];
      
      expect(insertCall).toMatchObject({
        created_by_voice: true,
        voice_confidence_score: expect.any(Number),
        voice_confidence_breakdown: expect.any(Object),
        raw_transcript: expect.any(String),
        metadata: expect.objectContaining({
          voice_processed: true,
          processing_timestamp: expect.any(String)
        })
      });
    });
  });

  describe('Error Handling and Recovery', () => {
    it('should handle database connection failures gracefully', async () => {
      // Mock database failure
      mockSupabase.from.mockReturnValue({
        insert: vi.fn(() => ({
          select: vi.fn(() => ({
            single: vi.fn(() => Promise.resolve(mockSupabaseResponses.insertError))
          }))
        }))
      });

      const audioBlob = new Blob(['mock audio data'], { type: 'audio/webm' });
      
      await expect(processor.processVoiceEvent(audioBlob, 'test-user'))
        .rejects.toThrow('Database connection failed');
    });

    it('should implement retry logic for transient failures', async () => {
      const audioBlob = new Blob(['mock audio data'], { type: 'audio/webm' });
      
      // Mock API to fail first two attempts, then succeed
      let attemptCount = 0;
      mockAPI.setShouldFail(true, 'timeout');
      
      const originalProcessTranscript = mockAPI.processTranscript;
      mockAPI.processTranscript = async function(transcript: string) {
        attemptCount++;
        if (attemptCount <= 2) {
          throw new Error('OpenAI API timeout');
        }
        mockAPI.setShouldFail(false);
        return originalProcessTranscript.call(this, transcript);
      };

      const result = await processor.processVoiceEventWithRetry(audioBlob, 'test-user');
      
      expect(attemptCount).toBe(3); // Should retry twice before succeeding
      expect(result.eventData).toBeDefined();
    });

    it('should queue events for offline processing when network fails', async () => {
      // Mock network failure
      mockAPI.setShouldFail(true, 'timeout');
      
      const audioBlob = new Blob(['mock audio data'], { type: 'audio/webm' });
      
      const result = await processor.processVoiceEventWithRetry(audioBlob, 'test-user');
      
      // Should return a queued result
      expect(result.audioConfirmation).toContain('saved and will be processed');
    });

    it('should validate voice event data before saving', async () => {
      const audioBlob = new Blob(['invalid data'], { type: 'audio/webm' });
      
      // Mock API to return invalid data
      const invalidCommand = {
        action_type: 'create_event',
        event_type: '', // Invalid empty event type
        product_name: '',
        quantity: -1, // Invalid negative quantity
        confidence_score: 1.5 // Invalid confidence score > 1
      };

      mockAPI.processTranscript = vi.fn().mockResolvedValue(invalidCommand);
      
      // Should handle validation errors appropriately
      const result = await processor.processVoiceEvent(audioBlob, 'test-user');
      expect(result.requiresConfirmation).toBeTruthy();
    });
  });

  describe('Real-time Subscriptions', () => {
    it('should set up real-time subscription for voice events', async () => {
      const callback = vi.fn();
      const filters = { eventType: ['receiving', 'sale'] };
      
      const unsubscribe = voiceService.setupRealtimeSubscription(
        filters,
        callback,
        'test-subscription'
      );
      
      expect(mockSupabase.channel).toHaveBeenCalledWith('voice_events_test-subscription');
      expect(unsubscribe).toBeInstanceOf(Function);
    });

    it('should trigger real-time updates when new events are created', async () => {
      const callback = vi.fn();
      const filters = {};
      
      // Set up subscription
      voiceService.setupRealtimeSubscription(filters, callback, 'test-subscription');
      
      // Simulate new event creation
      const audioBlob = new Blob(['mock audio data'], { type: 'audio/webm' });
      await processor.processVoiceEvent(audioBlob, 'test-user');
      
      // Verify subscription was established
      expect(mockSupabase.channel).toHaveBeenCalled();
    });

    it('should filter real-time events based on subscription criteria', async () => {
      const callback = vi.fn();
      const filters = {
        eventType: ['receiving'],
        confidenceThreshold: 0.8
      };
      
      voiceService.setupRealtimeSubscription(filters, callback, 'filtered-subscription');
      
      // Get the subscription handler
      const subscriptionCall = mockSupabase.channel().on.mock.calls[0];
      const handler = subscriptionCall[2];
      
      // Test filtering with matching event
      const matchingEvent = createMockVoiceEvent({
        event_type: 'receiving',
        voice_confidence_score: 0.9
      });
      
      handler({ new: matchingEvent });
      expect(callback).toHaveBeenCalledWith(matchingEvent);
      
      // Test filtering with non-matching event
      callback.mockClear();
      const nonMatchingEvent = createMockVoiceEvent({
        event_type: 'sale',
        voice_confidence_score: 0.6
      });
      
      handler({ new: nonMatchingEvent });
      expect(callback).not.toHaveBeenCalled();
    });

    it('should clean up subscriptions properly', async () => {
      const callback = vi.fn();
      const unsubscribe = voiceService.setupRealtimeSubscription({}, callback, 'cleanup-test');
      
      const mockUnsubscribe = vi.fn();
      mockSupabase.channel().on().subscribe.mockReturnValue({
        unsubscribe: mockUnsubscribe
      });
      
      unsubscribe();
      
      // Should call the cleanup method
      voiceService.removeRealtimeSubscription('cleanup-test');
    });
  });

  describe('Optimized Database Operations', () => {
    it('should use optimized queries for voice event retrieval', async () => {
      const filters = {
        eventType: ['receiving'],
        dateRange: {
          start: '2024-01-01',
          end: '2024-12-31'
        }
      };
      
      await voiceService.getVoiceEventsPaginated(filters, 1, 20);
      
      // Verify optimized query structure
      expect(mockSupabase.from).toHaveBeenCalledWith('inventory_events');
      const selectCall = mockSupabase.from().select;
      expect(selectCall).toHaveBeenCalledWith(
        expect.stringContaining('voice_confidence_score'),
        { count: 'exact' }
      );
    });

    it('should implement intelligent caching for repeated queries', async () => {
      const filters = { eventType: ['receiving'] };
      
      // First call should hit database
      await voiceService.getVoiceEventsPaginated(filters, 1, 20, { useCache: true });
      
      // Second call should use cache
      await voiceService.getVoiceEventsPaginated(filters, 1, 20, { useCache: true });
      
      // Database should only be called once due to caching
      expect(mockSupabase.from).toHaveBeenCalledTimes(1);
    });

    it('should prefetch next page for better user experience', async () => {
      const filters = { eventType: ['receiving'] };
      
      await voiceService.getVoiceEventsPaginated(
        filters, 
        1, 
        20, 
        { prefetchNext: true }
      );
      
      // Should trigger prefetch of page 2
      setTimeout(() => {
        expect(mockSupabase.from).toHaveBeenCalledTimes(2);
      }, 200);
    });

    it('should invalidate cache when new events are created', async () => {
      const filters = { eventType: ['receiving'] };
      
      // Prime the cache
      await voiceService.getVoiceEventsPaginated(filters, 1, 20, { useCache: true });
      
      // Create new event (should invalidate cache)
      const audioBlob = new Blob(['mock audio data'], { type: 'audio/webm' });
      await processor.processVoiceEvent(audioBlob, 'test-user');
      
      // Next query should hit database again
      await voiceService.getVoiceEventsPaginated(filters, 1, 20, { useCache: true });
      
      expect(mockSupabase.from).toHaveBeenCalledTimes(2);
    });
  });

  describe('Voice Event Search', () => {
    it('should search voice events by transcript content', async () => {
      const searchQuery = 'salmon';
      
      await voiceService.searchVoiceEvents(searchQuery, 10);
      
      const queryCall = mockSupabase.from().select().eq().or;
      expect(queryCall).toHaveBeenCalledWith(
        expect.stringContaining('raw_transcript.ilike')
      );
    });

    it('should search across multiple fields', async () => {
      const searchQuery = 'Ocean Fresh';
      
      await voiceService.searchVoiceEvents(searchQuery, 10);
      
      // Should search in transcript, notes, and metadata
      const queryCall = mockSupabase.from().select().eq().or;
      expect(queryCall).toHaveBeenCalledWith(
        expect.stringMatching(/raw_transcript.*metadata.*product_name/)
      );
    });

    it('should cache search results for performance', async () => {
      const searchQuery = 'salmon';
      
      // First search should hit database
      await voiceService.searchVoiceEvents(searchQuery, 10, { useCache: true });
      
      // Second search should use cache
      await voiceService.searchVoiceEvents(searchQuery, 10, { useCache: true });
      
      expect(mockSupabase.from).toHaveBeenCalledTimes(1);
    });
  });

  describe('Data Consistency and Integrity', () => {
    it('should maintain referential integrity between events and audio recordings', async () => {
      const audioBlob = new Blob(['mock audio data'], { type: 'audio/webm' });
      
      const result = await processor.processVoiceEvent(audioBlob, 'test-user');
      
      expect(result.eventData.id).toBeDefined();
      expect(result.eventData.audio_recording_url).toBeDefined();
      
      // Audio URL should reference the event ID
      if (result.eventData.audio_recording_url) {
        expect(result.eventData.audio_recording_url).toContain('blob:');
      }
    });

    it('should ensure voice confidence data is properly structured', async () => {
      const audioBlob = new Blob(['mock audio data'], { type: 'audio/webm' });
      
      await processor.processVoiceEvent(audioBlob, 'test-user');
      
      const insertCall = mockSupabase.from().insert.mock.calls[0][0];
      
      expect(insertCall.voice_confidence_score).toBeGreaterThanOrEqual(0);
      expect(insertCall.voice_confidence_score).toBeLessThanOrEqual(1);
      expect(insertCall.voice_confidence_breakdown).toMatchObject({
        product_match: expect.any(Number),
        quantity_extraction: expect.any(Number),
        vendor_match: expect.any(Number),
        overall: expect.any(Number)
      });
    });

    it('should preserve original transcript data for audit purposes', async () => {
      const audioBlob = new Blob(['Received 25 pounds of fresh salmon'], { type: 'audio/webm' });
      
      await processor.processVoiceEvent(audioBlob, 'test-user');
      
      const insertCall = mockSupabase.from().insert.mock.calls[0][0];
      
      expect(insertCall.raw_transcript).toBeDefined();
      expect(insertCall.raw_transcript).toBeTruthy();
      expect(typeof insertCall.raw_transcript).toBe('string');
    });

    it('should handle concurrent voice processing requests safely', async () => {
      const promises = [];
      
      // Simulate 5 concurrent voice processing requests
      for (let i = 0; i < 5; i++) {
        const audioBlob = new Blob([`Request ${i}`], { type: 'audio/webm' });
        promises.push(processor.processVoiceEvent(audioBlob, `test-user-${i}`));
      }
      
      const results = await Promise.allSettled(promises);
      
      // All requests should succeed
      expect(results.filter(r => r.status === 'fulfilled')).toHaveLength(5);
      
      // Database should receive all 5 insert calls
      expect(mockSupabase.from().insert).toHaveBeenCalledTimes(5);
    });
  });

  describe('Performance Monitoring Integration', () => {
    it('should track processing latency metrics', async () => {
      const audioBlob = new Blob(['mock audio data'], { type: 'audio/webm' });
      
      const startTime = performance.now();
      await processor.processVoiceEvent(audioBlob, 'test-user');
      const endTime = performance.now();
      
      const processingTime = endTime - startTime;
      expect(processingTime).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should monitor database query performance', async () => {
      const startTime = performance.now();
      await voiceService.getVoiceEventsPaginated({}, 1, 20, { monitor: true });
      const endTime = performance.now();
      
      const queryTime = endTime - startTime;
      expect(queryTime).toBeLessThan(1000); // Queries should be fast
    });

    it('should track cache hit rates', async () => {
      const filters = { eventType: ['receiving'] };
      
      // Prime cache
      await voiceService.getVoiceEventsPaginated(filters, 1, 20, { useCache: true });
      
      // Hit cache
      await voiceService.getVoiceEventsPaginated(filters, 1, 20, { useCache: true });
      
      const stats = voiceService.getPerformanceStats();
      expect(stats.cache).toBeDefined();
    });
  });
});