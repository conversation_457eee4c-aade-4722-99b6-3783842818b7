import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import { BrowserRouter } from 'react-router-dom';
import Dashboard from '../../components/Dashboard';
import HACCPCalendar from '../../components/HACCPCalendar';
import EventsTable from '../../components/EventsTable';

/**
 * Core Component Testing Framework
 */

// Mock Supabase
vi.mock('../../lib/supabase', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          order: vi.fn(() => Promise.resolve({ data: [], error: null }))
        }))
      })),
      insert: vi.fn(() => Promise.resolve({ data: [], error: null })),
      update: vi.fn(() => Promise.resolve({ data: [], error: null })),
      delete: vi.fn(() => Promise.resolve({ data: [], error: null }))
    })),
    auth: {
      getUser: vi.fn(() => Promise.resolve({ 
        data: { user: { id: 'test-user' } }, 
        error: null 
      }))
    }
  }
}));

// Test wrapper with providers
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <BrowserRouter>
    {children}
  </BrowserRouter>
);

describe('Dashboard Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders dashboard with key metrics', async () => {
    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    // Check for dashboard elements
    expect(screen.getByTestId('dashboard')).toBeInTheDocument();
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText(/total inventory/i)).toBeInTheDocument();
    });
    
    // Verify metric cards
    expect(screen.getByTestId('total-inventory')).toBeInTheDocument();
    expect(screen.getByTestId('low-stock-alerts')).toBeInTheDocument();
    expect(screen.getByTestId('recent-events')).toBeInTheDocument();
  });

  test('handles loading states', async () => {
    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    // Should show loading initially
    expect(screen.getByText(/loading/i)).toBeInTheDocument();
    
    // Loading should disappear
    await waitFor(() => {
      expect(screen.queryByText(/loading/i)).not.toBeInTheDocument();
    });
  });

  test('displays charts when data is available', async () => {
    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );

    await waitFor(() => {
      const chartElement = screen.getByTestId('inventory-chart');
      expect(chartElement).toBeInTheDocument();
    });
  });
});

describe('HACCPCalendar Component', () => {
  const mockEvents = [
    {
      id: '1',
      event_type: 'temperature_check',
      event_date: new Date().toISOString().split('T')[0],
      temperature: 38.5,
      location: 'Receiving Dock'
    }
  ];

  test('renders calendar with current month', () => {
    render(<HACCPCalendar events={mockEvents} onDateClick={vi.fn()} />);
    
    const currentMonth = new Date().toLocaleDateString('en-US', { month: 'long' });
    expect(screen.getByText(new RegExp(currentMonth))).toBeInTheDocument();
  });

  test('handles date clicks correctly', async () => {
    const onDateClick = vi.fn();
    const user = userEvent.setup();
    
    render(<HACCPCalendar events={mockEvents} onDateClick={onDateClick} />);
    
    // Find a date cell and click it
    const dateCell = screen.getByText('15'); // Assuming 15th exists
    await user.click(dateCell);
    
    expect(onDateClick).toHaveBeenCalledWith(expect.any(Date));
  });

  test('displays events on correct dates', () => {
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0];
    
    const eventsToday = [
      {
        id: '1',
        event_type: 'temperature_check',
        event_date: todayStr,
        temperature: 38.5
      }
    ];
    
    render(<HACCPCalendar events={eventsToday} onDateClick={vi.fn()} />);
    
    // Should show event indicator
    const eventIndicator = screen.getByTestId('event-indicator');
    expect(eventIndicator).toBeInTheDocument();
  });

  test('navigates between months', async () => {
    const user = userEvent.setup();
    
    render(<HACCPCalendar events={[]} onDateClick={vi.fn()} />);
    
    const nextButton = screen.getByTestId('next-month');
    await user.click(nextButton);
    
    // Should show next month
    const nextMonth = new Date();
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    const expectedMonth = nextMonth.toLocaleDateString('en-US', { month: 'long' });
    
    expect(screen.getByText(new RegExp(expectedMonth))).toBeInTheDocument();
  });
});

describe('EventsTable Component', () => {
  const mockEvents = [
    {
      id: '1',
      event_type: 'receiving',
      product_name: 'Atlantic Salmon',
      quantity: 25,
      unit: 'lbs',
      created_at: new Date().toISOString(),
      batch_number: 'BATCH001'
    },
    {
      id: '2',
      event_type: 'sales',
      product_name: 'Pacific Cod',
      quantity: 15,
      unit: 'lbs',
      created_at: new Date().toISOString(),
      batch_number: 'BATCH002'
    }
  ];

  test('renders table with events data', () => {
    render(<EventsTable events={mockEvents} />);
    
    // Check table structure
    expect(screen.getByRole('table')).toBeInTheDocument();
    expect(screen.getByTestId('events-table')).toBeInTheDocument();
    
    // Check data rows
    expect(screen.getByText('Atlantic Salmon')).toBeInTheDocument();
    expect(screen.getByText('Pacific Cod')).toBeInTheDocument();
    expect(screen.getByText('25')).toBeInTheDocument();
    expect(screen.getByText('15')).toBeInTheDocument();
  });

  test('handles sorting functionality', async () => {
    const user = userEvent.setup();
    
    render(<EventsTable events={mockEvents} />);
    
    const dateHeader = screen.getByTestId('sort-date');
    await user.click(dateHeader);
    
    // Should show sort indicator
    expect(screen.getByTestId('sort-indicator')).toBeInTheDocument();
  });

  test('filters events by type', async () => {
    const user = userEvent.setup();
    
    render(<EventsTable events={mockEvents} />);
    
    const filterSelect = screen.getByTestId('event-type-filter');
    await user.selectOptions(filterSelect, 'receiving');
    
    // Should only show receiving events
    expect(screen.getByText('Atlantic Salmon')).toBeInTheDocument();
    expect(screen.queryByText('Pacific Cod')).not.toBeInTheDocument();
  });

  test('handles empty state', () => {
    render(<EventsTable events={[]} />);
    
    expect(screen.getByText(/no events found/i)).toBeInTheDocument();
  });

  test('supports pagination', async () => {
    const manyEvents = Array.from({ length: 25 }, (_, i) => ({
      id: String(i + 1),
      event_type: 'receiving',
      product_name: `Product ${i + 1}`,
      quantity: Math.random() * 100,
      unit: 'lbs',
      created_at: new Date().toISOString(),
      batch_number: `BATCH${String(i + 1).padStart(3, '0')}`
    }));
    
    const user = userEvent.setup();
    
    render(<EventsTable events={manyEvents} />);
    
    // Should show pagination
    const pagination = screen.getByTestId('pagination');
    expect(pagination).toBeInTheDocument();
    
    // Test next page
    const nextButton = screen.getByTestId('pagination-next');
    await user.click(nextButton);
    
    expect(screen.getByTestId('current-page')).toHaveTextContent('2');
  });
});

describe('Form Components', () => {
  test('validates required fields', async () => {
    const user = userEvent.setup();
    
    // Mock form component (you'll need to adjust based on actual form)
    const MockForm = () => (
      <form data-testid="inventory-form">
        <input 
          data-testid="product-input" 
          required 
          aria-label="Product name"
        />
        <input 
          data-testid="quantity-input" 
          type="number" 
          required 
          aria-label="Quantity"
        />
        <button type="submit" data-testid="submit-button">
          Submit
        </button>
      </form>
    );
    
    render(<MockForm />);
    
    // Try to submit without filling required fields
    const submitButton = screen.getByTestId('submit-button');
    await user.click(submitButton);
    
    // Check for validation (browser native or custom)
    const productInput = screen.getByTestId('product-input');
    expect(productInput).toBeInvalid();
  });

  test('shows real-time validation feedback', async () => {
    const user = userEvent.setup();
    
    const MockFormWithValidation = () => {
      const [quantity, setQuantity] = React.useState('');
      const [error, setError] = React.useState('');
      
      const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setQuantity(value);
        
        if (value && parseFloat(value) <= 0) {
          setError('Quantity must be positive');
        } else {
          setError('');
        }
      };
      
      return (
        <div>
          <input 
            data-testid="quantity-input"
            type="number"
            value={quantity}
            onChange={handleQuantityChange}
          />
          {error && <div data-testid="quantity-error">{error}</div>}
        </div>
      );
    };
    
    render(<MockFormWithValidation />);
    
    const quantityInput = screen.getByTestId('quantity-input');
    await user.type(quantityInput, '-5');
    
    expect(screen.getByTestId('quantity-error')).toHaveTextContent('must be positive');
  });
});

describe('Voice Components', () => {
  beforeEach(() => {
    // Mock speech recognition
    global.SpeechRecognition = vi.fn(() => ({
      start: vi.fn(),
      stop: vi.fn(),
      addEventListener: vi.fn()
    }));
    
    global.webkitSpeechRecognition = global.SpeechRecognition;
  });

  test('shows voice input button when supported', () => {
    const MockVoiceComponent = () => {
      const isSupported = 'SpeechRecognition' in window || 'webkitSpeechRecognition' in window;
      
      return (
        <div>
          {isSupported ? (
            <button data-testid="voice-input-button">Start Voice Input</button>
          ) : (
            <div data-testid="voice-not-supported">Voice not supported</div>
          )}
        </div>
      );
    };
    
    render(<MockVoiceComponent />);
    
    expect(screen.getByTestId('voice-input-button')).toBeInTheDocument();
  });

  test('handles voice permission requests', async () => {
    const user = userEvent.setup();
    
    // Mock getUserMedia
    Object.defineProperty(navigator, 'mediaDevices', {
      value: {
        getUserMedia: vi.fn(() => Promise.resolve(new MediaStream()))
      }
    });
    
    const MockVoiceComponent = () => {
      const [hasPermission, setHasPermission] = React.useState(false);
      
      const requestPermission = async () => {
        try {
          await navigator.mediaDevices.getUserMedia({ audio: true });
          setHasPermission(true);
        } catch (error) {
          setHasPermission(false);
        }
      };
      
      return (
        <div>
          {!hasPermission ? (
            <button data-testid="request-permission" onClick={requestPermission}>
              Allow Microphone
            </button>
          ) : (
            <div data-testid="permission-granted">Microphone ready</div>
          )}
        </div>
      );
    };
    
    render(<MockVoiceComponent />);
    
    const permissionButton = screen.getByTestId('request-permission');
    await user.click(permissionButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('permission-granted')).toBeInTheDocument();
    });
  });
});

describe('Error Handling Components', () => {
  test('displays error boundaries', () => {
    const ThrowError = () => {
      throw new Error('Test error');
    };
    
    const MockErrorBoundary = ({ children }: { children: React.ReactNode }) => {
      const [hasError, setHasError] = React.useState(false);
      
      React.useEffect(() => {
        const errorHandler = () => setHasError(true);
        window.addEventListener('error', errorHandler);
        return () => window.removeEventListener('error', errorHandler);
      }, []);
      
      if (hasError) {
        return <div data-testid="error-boundary">Something went wrong</div>;
      }
      
      return <>{children}</>;
    };
    
    // Suppress console.error for this test
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    
    expect(() => {
      render(
        <MockErrorBoundary>
          <ThrowError />
        </MockErrorBoundary>
      );
    }).not.toThrow();
    
    consoleSpy.mockRestore();
  });

  test('handles API errors gracefully', async () => {
    const MockComponent = () => {
      const [error, setError] = React.useState<string | null>(null);
      const [loading, setLoading] = React.useState(false);
      
      const fetchData = async () => {
        setLoading(true);
        try {
          throw new Error('API Error');
        } catch (err) {
          setError(err instanceof Error ? err.message : 'Unknown error');
        } finally {
          setLoading(false);
        }
      };
      
      React.useEffect(() => {
        fetchData();
      }, []);
      
      if (loading) return <div data-testid="loading">Loading...</div>;
      if (error) return <div data-testid="error-message">{error}</div>;
      
      return <div>Data loaded</div>;
    };
    
    render(<MockComponent />);
    
    await waitFor(() => {
      expect(screen.getByTestId('error-message')).toHaveTextContent('API Error');
    });
  });
});