import React from 'react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor, within } from '@/test/utils/test-utils';
import { server } from '@/test/setup';
import { http, HttpResponse } from 'msw';
import HACCPCalendar from '@/components/HACCPCalendar';
import { mockSupabaseData } from '@/test/mocks/data';

// Mock Modal component
vi.mock('@/components/modals/Modal', () => ({
  default: ({ isOpen, onClose, title, children }: any) => 
    isOpen ? (
      <div data-testid="modal" role="dialog">
        <div data-testid="modal-title">{title}</div>
        <button onClick={onClose} data-testid="modal-close">Close</button>
        {children}
      </div>
    ) : null
}));

// Mock HACCPEventForm component
vi.mock('@/components/forms/HACCPEventForm', () => ({
  default: ({ onSuccess, onCancel, dateOverride }: any) => (
    <div data-testid="haccp-event-form">
      <div data-testid="form-date-override">{dateOverride}</div>
      <button onClick={onSuccess} data-testid="form-submit">Submit</button>
      <button onClick={onCancel} data-testid="form-cancel">Cancel</button>
    </div>
  )
}));

// Mock Supabase client
const mockSupabaseFrom = vi.fn();
const mockSupabaseSelect = vi.fn();

vi.mock('@/lib/supabase', () => ({
  supabase: {
    from: mockSupabaseFrom
  }
}));

describe('HACCPCalendar Component', () => {
  const mockInventoryEvents = [
    {
      id: 'event-1',
      event_type: 'receiving',
      product_id: 'prod-1',
      quantity: 100,
      notes: 'Fresh delivery',
      created_at: '2024-01-15T10:00:00Z'
    },
    {
      id: 'event-2',
      event_type: 'sale',
      product_id: 'prod-2',
      quantity: 25,
      notes: 'Restaurant order',
      created_at: '2024-01-15T14:30:00Z'
    },
    {
      id: 'event-3',
      event_type: 'disposal',
      product_id: 'prod-3',
      quantity: 5,
      notes: 'Expired product',
      created_at: '2024-01-16T09:00:00Z'
    },
    {
      id: 'event-4',
      event_type: 'physical_count',
      product_id: 'prod-1',
      quantity: 95,
      notes: 'Weekly count',
      created_at: '2024-01-17T16:00:00Z'
    }
  ];

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock current date to January 2024 for consistent testing
    vi.useFakeTimers();
    vi.setSystemTime(new Date('2024-01-15T12:00:00Z'));

    // Setup Supabase mocks
    mockSupabaseSelect.mockResolvedValue({
      data: mockInventoryEvents,
      error: null
    });

    mockSupabaseFrom.mockImplementation((table: string) => {
      if (table === 'inventory_events') {
        return {
          select: vi.fn().mockReturnValue({
            gte: vi.fn().mockReturnValue({
              lte: vi.fn().mockReturnValue({
                order: vi.fn().mockResolvedValue({
                  data: mockInventoryEvents,
                  error: null
                })
              })
            })
          })
        };
      }
      return { select: mockSupabaseSelect };
    });
  });

  afterEach(() => {
    vi.resetAllMocks();
    vi.useRealTimers();
  });

  describe('Calendar Rendering', () => {
    it('renders calendar with month navigation', async () => {
      render(<HACCPCalendar />);

      // Check for navigation controls
      expect(screen.getByLabelText('Previous month')).toBeInTheDocument();
      expect(screen.getByLabelText('Today')).toBeInTheDocument();
      expect(screen.getByLabelText('Next month')).toBeInTheDocument();

      // Check for current month display
      expect(screen.getByText('January 2024')).toBeInTheDocument();

      // Check for calendar grid
      expect(screen.getByLabelText('HACCP Events Calendar')).toBeInTheDocument();
      
      // Check for day headers
      expect(screen.getByText('Sun')).toBeInTheDocument();
      expect(screen.getByText('Mon')).toBeInTheDocument();
      expect(screen.getByText('Sat')).toBeInTheDocument();
    });

    it('displays events on calendar grid', async () => {
      render(<HACCPCalendar />);

      await waitFor(() => {
        // Should display event types on their respective days
        expect(screen.getByText('receiving')).toBeInTheDocument();
        expect(screen.getByText('sale')).toBeInTheDocument();
        expect(screen.getByText('disposal')).toBeInTheDocument();
        expect(screen.getByText('physical count')).toBeInTheDocument();
      });
    });

    it('highlights today on the calendar', () => {
      render(<HACCPCalendar />);

      // Should show "Today" indicator on current date
      expect(screen.getByText('Today')).toBeInTheDocument();
    });

    it('shows event type filters', async () => {
      render(<HACCPCalendar />);

      await waitFor(() => {
        // Should show checkboxes for each event type
        expect(screen.getByLabelText(/receiving/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/disposal/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/physical count/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/sale/i)).toBeInTheDocument();
      });
    });
  });

  describe('Navigation', () => {
    it('navigates to previous month', async () => {
      render(<HACCPCalendar />);

      const prevButton = screen.getByLabelText('Previous month');
      fireEvent.click(prevButton);

      await waitFor(() => {
        expect(screen.getByText('December 2023')).toBeInTheDocument();
      });
    });

    it('navigates to next month', async () => {
      render(<HACCPCalendar />);

      const nextButton = screen.getByLabelText('Next month');
      fireEvent.click(nextButton);

      await waitFor(() => {
        expect(screen.getByText('February 2024')).toBeInTheDocument();
      });
    });

    it('navigates to current month with Today button', async () => {
      render(<HACCPCalendar />);

      // Navigate away first
      const nextButton = screen.getByLabelText('Next month');
      fireEvent.click(nextButton);

      await waitFor(() => {
        expect(screen.getByText('February 2024')).toBeInTheDocument();
      });

      // Navigate back to today
      const todayButton = screen.getByLabelText('Today');
      fireEvent.click(todayButton);

      await waitFor(() => {
        expect(screen.getByText('January 2024')).toBeInTheDocument();
      });
    });
  });

  describe('Event Display', () => {
    it('groups events by day correctly', async () => {
      render(<HACCPCalendar />);

      await waitFor(() => {
        // Find the day with multiple events (January 15)
        const dayButtons = screen.getAllByRole('button');
        const jan15Button = dayButtons.find(button => 
          button.textContent?.includes('15') && 
          button.textContent?.includes('receiving')
        );
        
        expect(jan15Button).toBeInTheDocument();
        
        // Should show both receiving and sale events on the same day
        if (jan15Button) {
          expect(within(jan15Button).getByText('receiving')).toBeInTheDocument();
          expect(within(jan15Button).getByText('sale')).toBeInTheDocument();
        }
      });
    });

    it('shows event overflow indicator for days with many events', async () => {
      // Add more events to test overflow
      const manyEvents = Array.from({ length: 5 }, (_, i) => ({
        id: `overflow-event-${i}`,
        event_type: 'receiving',
        product_id: 'prod-1',
        quantity: 10,
        notes: `Event ${i}`,
        created_at: '2024-01-15T10:00:00Z'
      }));

      mockSupabaseFrom.mockImplementation((table: string) => {
        if (table === 'inventory_events') {
          return {
            select: vi.fn().mockReturnValue({
              gte: vi.fn().mockReturnValue({
                lte: vi.fn().mockReturnValue({
                  order: vi.fn().mockResolvedValue({
                    data: [...mockInventoryEvents, ...manyEvents],
                    error: null
                  })
                })
              })
            })
          };
        }
        return { select: mockSupabaseSelect };
      });

      render(<HACCPCalendar />);

      await waitFor(() => {
        // Should show overflow indicator
        expect(screen.getByText(/\+\d+ more/)).toBeInTheDocument();
      });
    });

    it('applies correct color coding to event types', async () => {
      render(<HACCPCalendar />);

      await waitFor(() => {
        const receivingEvent = screen.getByText('receiving');
        const saleEvent = screen.getByText('sale');
        const disposalEvent = screen.getByText('disposal');

        // Check that elements have appropriate CSS classes for color coding
        expect(receivingEvent.className).toMatch(/green/);
        expect(saleEvent.className).toMatch(/blue/);
        expect(disposalEvent.className).toMatch(/red/);
      });
    });
  });

  describe('Event Filtering', () => {
    it('filters events by type when checkboxes are toggled', async () => {
      render(<HACCPCalendar />);

      await waitFor(() => {
        expect(screen.getByText('receiving')).toBeInTheDocument();
        expect(screen.getByText('sale')).toBeInTheDocument();
      });

      // Uncheck receiving events
      const receivingCheckbox = screen.getByLabelText(/receiving/i);
      fireEvent.click(receivingCheckbox);

      await waitFor(() => {
        expect(screen.queryByText('receiving')).not.toBeInTheDocument();
        expect(screen.getByText('sale')).toBeInTheDocument(); // Others should remain
      });
    });

    it('maintains filter state when navigating months', async () => {
      render(<HACCPCalendar />);

      // Disable disposal events
      await waitFor(() => {
        const disposalCheckbox = screen.getByLabelText(/disposal/i);
        fireEvent.click(disposalCheckbox);
      });

      // Navigate to next month
      const nextButton = screen.getByLabelText('Next month');
      fireEvent.click(nextButton);

      // Navigate back
      const prevButton = screen.getByLabelText('Previous month');
      fireEvent.click(prevButton);

      await waitFor(() => {
        const disposalCheckbox = screen.getByLabelText(/disposal/i);
        expect(disposalCheckbox).not.toBeChecked();
      });
    });
  });

  describe('Day Selection and Details', () => {
    it('shows event details when a day is selected', async () => {
      render(<HACCPCalendar />);

      await waitFor(() => {
        // Click on a day with events (January 15)
        const dayButtons = screen.getAllByRole('button');
        const jan15Button = dayButtons.find(button => 
          button.textContent?.includes('15') && 
          button.textContent?.includes('receiving')
        );
        
        if (jan15Button) {
          fireEvent.click(jan15Button);
        }
      });

      await waitFor(() => {
        // Should show event details section
        expect(screen.getByText(/Events on 2024-01-15/)).toBeInTheDocument();
        
        // Should show individual event details
        expect(screen.getByText('Fresh delivery')).toBeInTheDocument();
        expect(screen.getByText('Restaurant order')).toBeInTheDocument();
      });
    });

    it('opens add event modal when clicking on a day', async () => {
      render(<HACCPCalendar />);

      await waitFor(() => {
        // Click on any day
        const dayButtons = screen.getAllByRole('button');
        const dayButton = dayButtons.find(button => 
          button.textContent?.includes('20')
        );
        
        if (dayButton) {
          fireEvent.click(dayButton);
        }
      });

      await waitFor(() => {
        // Modal should open
        expect(screen.getByTestId('modal')).toBeInTheDocument();
        expect(screen.getByTestId('haccp-event-form')).toBeInTheDocument();
      });
    });

    it('passes selected date to event form', async () => {
      render(<HACCPCalendar />);

      await waitFor(() => {
        // Click on January 20th
        const dayButtons = screen.getAllByRole('button');
        const jan20Button = dayButtons.find(button => 
          button.textContent?.includes('20')
        );
        
        if (jan20Button) {
          fireEvent.click(jan20Button);
        }
      });

      await waitFor(() => {
        // Form should receive the correct date override
        const dateOverride = screen.getByTestId('form-date-override');
        expect(dateOverride.textContent).toBe('2024-01-20');
      });
    });
  });

  describe('Modal Interactions', () => {
    it('closes modal when form is submitted successfully', async () => {
      render(<HACCPCalendar />);

      // Open modal
      await waitFor(() => {
        const dayButtons = screen.getAllByRole('button');
        const dayButton = dayButtons.find(button => 
          button.textContent?.includes('20')
        );
        
        if (dayButton) {
          fireEvent.click(dayButton);
        }
      });

      await waitFor(() => {
        expect(screen.getByTestId('modal')).toBeInTheDocument();
      });

      // Submit form
      const submitButton = screen.getByTestId('form-submit');
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.queryByTestId('modal')).not.toBeInTheDocument();
      });
    });

    it('closes modal when cancelled', async () => {
      render(<HACCPCalendar />);

      // Open modal
      await waitFor(() => {
        const dayButtons = screen.getAllByRole('button');
        const dayButton = dayButtons.find(button => 
          button.textContent?.includes('20')
        );
        
        if (dayButton) {
          fireEvent.click(dayButton);
        }
      });

      await waitFor(() => {
        expect(screen.getByTestId('modal')).toBeInTheDocument();
      });

      // Cancel form
      const cancelButton = screen.getByTestId('form-cancel');
      fireEvent.click(cancelButton);

      await waitFor(() => {
        expect(screen.queryByTestId('modal')).not.toBeInTheDocument();
      });
    });

    it('reloads events after successful form submission', async () => {
      render(<HACCPCalendar />);

      const initialCallCount = mockSupabaseFrom.mock.calls.length;

      // Open modal and submit form
      await waitFor(() => {
        const dayButtons = screen.getAllByRole('button');
        const dayButton = dayButtons.find(button => 
          button.textContent?.includes('20')
        );
        
        if (dayButton) {
          fireEvent.click(dayButton);
        }
      });

      await waitFor(() => {
        const submitButton = screen.getByTestId('form-submit');
        fireEvent.click(submitButton);
      });

      await waitFor(() => {
        // Should have made additional API call to reload events
        expect(mockSupabaseFrom.mock.calls.length).toBeGreaterThan(initialCallCount);
      });
    });
  });

  describe('Data Loading', () => {
    it('shows loading state while fetching events', async () => {
      // Mock delayed response
      mockSupabaseFrom.mockImplementation((table: string) => {
        if (table === 'inventory_events') {
          return {
            select: vi.fn().mockReturnValue({
              gte: vi.fn().mockReturnValue({
                lte: vi.fn().mockReturnValue({
                  order: vi.fn().mockImplementation(() => 
                    new Promise(resolve => 
                      setTimeout(() => resolve({
                        data: mockInventoryEvents,
                        error: null
                      }), 1000)
                    )
                  )
                })
              })
            })
          };
        }
        return { select: mockSupabaseSelect };
      });

      render(<HACCPCalendar />);

      // Should show loading state
      expect(screen.getByText('Loading events…')).toBeInTheDocument();

      // Wait for loading to complete
      await waitFor(() => {
        expect(screen.queryByText('Loading events…')).not.toBeInTheDocument();
      }, { timeout: 2000 });
    });

    it('handles API errors gracefully', async () => {
      // Mock API error
      mockSupabaseFrom.mockImplementation((table: string) => {
        if (table === 'inventory_events') {
          return {
            select: vi.fn().mockReturnValue({
              gte: vi.fn().mockReturnValue({
                lte: vi.fn().mockReturnValue({
                  order: vi.fn().mockResolvedValue({
                    data: null,
                    error: { message: 'Database connection failed' }
                  })
                })
              })
            })
          };
        }
        return { select: mockSupabaseSelect };
      });

      render(<HACCPCalendar />);

      await waitFor(() => {
        expect(screen.getByText('Database connection failed')).toBeInTheDocument();
      });
    });

    it('fetches events for the visible calendar range', async () => {
      render(<HACCPCalendar />);

      await waitFor(() => {
        // Should have called the API with date range filters
        expect(mockSupabaseFrom).toHaveBeenCalledWith('inventory_events');
      });

      // Verify that the query includes date range filters
      const mockChain = mockSupabaseFrom.mock.results[0].value;
      expect(mockChain.select).toHaveBeenCalled();
    });
  });

  describe('Performance', () => {
    it('debounces API calls when rapidly changing months', async () => {
      render(<HACCPCalendar />);

      const nextButton = screen.getByLabelText('Next month');
      const prevButton = screen.getByLabelText('Previous month');

      // Rapidly click navigation
      fireEvent.click(nextButton);
      fireEvent.click(prevButton);
      fireEvent.click(nextButton);
      fireEvent.click(prevButton);

      // Wait for final state
      await waitFor(() => {
        expect(screen.getByText('January 2024')).toBeInTheDocument();
      });

      // Should not have made excessive API calls
      expect(mockSupabaseFrom.mock.calls.length).toBeLessThan(10);
    });

    it('cancels previous requests when navigating quickly', async () => {
      render(<HACCPCalendar />);

      const nextButton = screen.getByLabelText('Next month');
      
      // Navigate quickly
      fireEvent.click(nextButton);
      fireEvent.click(nextButton);

      // Should end up in the correct final state
      await waitFor(() => {
        expect(screen.getByText('March 2024')).toBeInTheDocument();
      });
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels and roles', () => {
      render(<HACCPCalendar />);

      expect(screen.getByLabelText('HACCP Events Calendar')).toBeInTheDocument();
      expect(screen.getByLabelText('Previous month')).toBeInTheDocument();
      expect(screen.getByLabelText('Next month')).toBeInTheDocument();
      expect(screen.getByLabelText('Today')).toBeInTheDocument();
    });

    it('supports keyboard navigation for day selection', async () => {
      render(<HACCPCalendar />);

      await waitFor(() => {
        const dayButtons = screen.getAllByRole('button');
        const firstDayButton = dayButtons.find(button => 
          button.textContent?.includes('1')
        );
        
        if (firstDayButton) {
          firstDayButton.focus();
          expect(firstDayButton).toHaveFocus();
          
          // Should be able to activate with Enter
          fireEvent.keyDown(firstDayButton, { key: 'Enter' });
          // Modal should open (tested by other tests)
        }
      });
    });

    it('provides proper focus management for modal', async () => {
      render(<HACCPCalendar />);

      // Open modal
      await waitFor(() => {
        const dayButtons = screen.getAllByRole('button');
        const dayButton = dayButtons.find(button => 
          button.textContent?.includes('20')
        );
        
        if (dayButton) {
          fireEvent.click(dayButton);
        }
      });

      await waitFor(() => {
        const modal = screen.getByTestId('modal');
        expect(modal).toHaveAttribute('role', 'dialog');
      });
    });

    it('provides descriptive tooltips for events', async () => {
      render(<HACCPCalendar />);

      await waitFor(() => {
        const eventElement = screen.getByText('receiving');
        expect(eventElement).toHaveAttribute('title');
        expect(eventElement.getAttribute('title')).toMatch(/receiving.*100/);
      });
    });
  });
});