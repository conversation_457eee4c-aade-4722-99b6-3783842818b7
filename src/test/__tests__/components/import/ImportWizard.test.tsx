import React from 'react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@/test/utils/test-utils';
import { server } from '@/test/setup';
import { http, HttpResponse } from 'msw';
import ImportWizard from '@/components/import/ImportWizard';
import { mockCSVData, createMockFile } from '@/test/mocks/data';

// Mock Papa Parse
vi.mock('papaparse', () => ({
  default: {
    parse: vi.fn()
  }
}));

// Mock Supabase client
const mockSupabaseInsert = vi.fn();
vi.mock('@/lib/supabase', () => ({
  supabase: {
    from: vi.fn(() => ({
      insert: mockSupabaseInsert
    }))
  }
}));

// Mock toast hook
const mockToast = vi.fn();
vi.mock('@/components/ui/use-toast', () => ({
  useToast: () => ({ toast: mockToast })
}));

describe('ImportWizard Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    const Papa = require('papaparse');
    Papa.default.parse.mockImplementation((text: string, options: any) => {
      if (text === mockCSVData.validInventoryCSV) {
        return {
          data: [
            { 'Product Name': 'Atlantic Salmon', 'Quantity': '100', 'Unit': 'lbs', 'Location': 'Freezer A', 'Batch Number': 'SAL-001-2024', 'Catch Date': '2024-01-15' },
            { 'Product Name': 'Dungeness Crab', 'Quantity': '50', 'Unit': 'lbs', 'Location': 'Fresh Section', 'Batch Number': 'CRAB-001-2024', 'Catch Date': '2024-01-16' },
            { 'Product Name': 'Pacific Halibut', 'Quantity': '75', 'Unit': 'lbs', 'Location': 'Freezer B', 'Batch Number': 'HAL-001-2024', 'Catch Date': '2024-01-14' }
          ],
          errors: []
        };
      }
      
      if (text === mockCSVData.invalidInventoryCSV) {
        return {
          data: [
            { 'Product Name': 'Atlantic Salmon', 'Quantity': 'invalid_quantity', 'Unit': 'lbs', 'Location': 'Freezer A' },
            { 'Product Name': '', 'Quantity': '50', 'Unit': 'lbs', 'Location': 'Fresh Section' },
            { 'Product Name': 'Pacific Halibut', 'Quantity': '75', 'Unit': '', 'Location': 'Freezer B' }
          ],
          errors: []
        };
      }
      
      return {
        data: [],
        errors: [{ message: 'Parse error' }]
      };
    });

    mockSupabaseInsert.mockResolvedValue({ 
      error: null, 
      count: 3 
    });
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('Wizard Navigation', () => {
    it('renders import wizard with step navigation', () => {
      render(<ImportWizard />);

      // Check wizard title and steps
      expect(screen.getByText('Import Wizard (Beta)')).toBeInTheDocument();
      expect(screen.getByText('Choose Type')).toBeInTheDocument();
      expect(screen.getByText('Load Data')).toBeInTheDocument();
      expect(screen.getByText('Map Fields')).toBeInTheDocument();
      expect(screen.getByText('Validate & Preview')).toBeInTheDocument();

      // Should start on step 1
      expect(screen.getByText('Select what you are importing.')).toBeInTheDocument();
    });

    it('navigates through wizard steps correctly', async () => {
      render(<ImportWizard />);

      // Step 1: Choose Type
      expect(screen.getByText('Select what you are importing.')).toBeInTheDocument();
      
      const nextButton = screen.getByText('Next');
      fireEvent.click(nextButton);

      // Step 2: Load Data
      await waitFor(() => {
        expect(screen.getByText('Upload CSV')).toBeInTheDocument();
      });

      // Back button should work
      const backButton = screen.getByText('Back');
      fireEvent.click(backButton);

      await waitFor(() => {
        expect(screen.getByText('Select what you are importing.')).toBeInTheDocument();
      });
    });

    it('disables next button when data is not loaded', () => {
      render(<ImportWizard />);

      // Navigate to Load Data step
      fireEvent.click(screen.getByText('Next'));

      // Next button should be disabled when no CSV is loaded
      const nextButton = screen.getByText('Next');
      expect(nextButton).toBeDisabled();
    });
  });

  describe('Import Type Selection', () => {
    it('allows selecting between Products and Inventory Events', () => {
      render(<ImportWizard />);

      const productsButton = screen.getByText('Products');
      const inventoryEventsButton = screen.getByText('inventory_events');

      // Products should be selected by default
      expect(productsButton).toHaveClass('bg-');

      // Switch to inventory events
      fireEvent.click(inventoryEventsButton);
      expect(inventoryEventsButton).toHaveClass('bg-');

      // Switch back to products
      fireEvent.click(productsButton);
      expect(productsButton).toHaveClass('bg-');
    });

    it('shows appropriate field descriptions for each type', () => {
      render(<ImportWizard />);

      // Check Products fields
      expect(screen.getByText(/Fields:.*Name.*SKU.*Category/)).toBeInTheDocument();

      // Switch to inventory events
      fireEvent.click(screen.getByText('inventory_events'));
      expect(screen.getByText(/Fields:.*Event Type.*Product.*Quantity/)).toBeInTheDocument();
    });
  });

  describe('CSV File Upload', () => {
    it('handles file upload and parsing', async () => {
      render(<ImportWizard />);

      // Navigate to Load Data step
      fireEvent.click(screen.getByText('Next'));

      // Mock file upload
      const fileInput = screen.getByLabelText('Upload CSV');
      const file = createMockFile('test.csv', mockCSVData.validInventoryCSV);

      // Mock file.text() method
      Object.defineProperty(file, 'text', {
        value: vi.fn().mockResolvedValue(mockCSVData.validInventoryCSV)
      });

      fireEvent.change(fileInput, { target: { files: [file] } });

      await waitFor(() => {
        expect(screen.getByText('Detected Columns')).toBeInTheDocument();
        expect(screen.getByText('Product Name')).toBeInTheDocument();
        expect(screen.getByText('Quantity')).toBeInTheDocument();
        expect(screen.getByText('Unit')).toBeInTheDocument();
      });
    });

    it('handles CSV text paste and parsing', async () => {
      render(<ImportWizard />);

      // Navigate to Load Data step
      fireEvent.click(screen.getByText('Next'));

      // Paste CSV content
      const textarea = screen.getByLabelText('Or paste CSV');
      fireEvent.change(textarea, { target: { value: mockCSVData.validInventoryCSV } });

      // Click parse button
      const parseButton = screen.getByText('Parse');
      fireEvent.click(parseButton);

      await waitFor(() => {
        expect(screen.getByText('Detected Columns')).toBeInTheDocument();
      });
    });

    it('displays error for invalid CSV', async () => {
      const Papa = require('papaparse');
      Papa.default.parse.mockReturnValue({
        data: [],
        errors: [{ message: 'Invalid CSV format' }]
      });

      render(<ImportWizard />);

      // Navigate to Load Data step
      fireEvent.click(screen.getByText('Next'));

      const textarea = screen.getByLabelText('Or paste CSV');
      fireEvent.change(textarea, { target: { value: 'invalid,csv,content' } });
      fireEvent.click(screen.getByText('Parse'));

      await waitFor(() => {
        expect(screen.getByText(/CSV parse error: Invalid CSV format/)).toBeInTheDocument();
      });
    });

    it('shows column type inference', async () => {
      const Papa = require('papaparse');
      Papa.default.parse.mockReturnValue({
        data: [
          { 'Product Name': 'Salmon', 'Quantity': '100', 'Price': '15.50' }
        ],
        errors: []
      });

      render(<ImportWizard />);

      fireEvent.click(screen.getByText('Next'));
      
      const textarea = screen.getByLabelText('Or paste CSV');
      fireEvent.change(textarea, { target: { value: 'test csv' } });
      fireEvent.click(screen.getByText('Parse'));

      await waitFor(() => {
        expect(screen.getByText('Detected Columns')).toBeInTheDocument();
        // Column inference would show types in parentheses
        expect(screen.getByText(/Product Name/)).toBeInTheDocument();
        expect(screen.getByText(/Quantity/)).toBeInTheDocument();
      });
    });
  });

  describe('Field Mapping', () => {
    beforeEach(async () => {
      render(<ImportWizard />);

      // Navigate to mapping step with data loaded
      fireEvent.click(screen.getByText('Next'));
      
      const textarea = screen.getByLabelText('Or paste CSV');
      fireEvent.change(textarea, { target: { value: mockCSVData.validInventoryCSV } });
      fireEvent.click(screen.getByText('Parse'));

      await waitFor(() => {
        expect(screen.getByText('Detected Columns')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText('Next'));
    });

    it('shows field mapping interface', async () => {
      await waitFor(() => {
        // Should show mapping controls for each field
        expect(screen.getByText('Name *')).toBeInTheDocument();
        expect(screen.getByText('SKU')).toBeInTheDocument();
        expect(screen.getByText('Category')).toBeInTheDocument();
      });
    });

    it('allows mapping CSV columns to target fields', async () => {
      await waitFor(() => {
        // Find a field mapping dropdown
        const nameSelect = screen.getAllByDisplayValue('(Ignore)')[0];
        fireEvent.click(nameSelect);
      });

      // Should show CSV columns as options
      await waitFor(() => {
        expect(screen.getByText('Product Name')).toBeInTheDocument();
      });
    });

    it('allows setting constant values for fields', async () => {
      await waitFor(() => {
        const constantInputs = screen.getAllByPlaceholderText('Constant');
        expect(constantInputs.length).toBeGreaterThan(0);

        // Set a constant value
        fireEvent.change(constantInputs[0], { target: { value: 'Test Constant' } });
        expect(constantInputs[0]).toHaveValue('Test Constant');
      });
    });

    it('shows warning for missing required fields', async () => {
      await waitFor(() => {
        // Should show warning about missing required fields
        expect(screen.getByText('Missing required fields')).toBeInTheDocument();
        expect(screen.getByText(/Provide a constant value/)).toBeInTheDocument();
      });
    });

    it('validates mapping completeness', async () => {
      await waitFor(() => {
        // Upload button should be disabled when validation issues exist
        const uploadButton = screen.getByText('Upload to Database');
        expect(uploadButton).toBeDisabled();
      });
    });
  });

  describe('Data Validation and Preview', () => {
    beforeEach(async () => {
      render(<ImportWizard />);

      // Set up complete wizard flow
      fireEvent.click(screen.getByText('Next')); // To Load Data
      
      const textarea = screen.getByLabelText('Or paste CSV');
      fireEvent.change(textarea, { target: { value: mockCSVData.validInventoryCSV } });
      fireEvent.click(screen.getByText('Parse'));

      await waitFor(() => {
        expect(screen.getByText('Detected Columns')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText('Next')); // To Map Fields
      
      // Map required fields to resolve validation issues
      await waitFor(() => {
        const nameSelect = screen.getAllByDisplayValue('(Ignore)')[0];
        fireEvent.click(nameSelect);
      });

      // Skip field mapping details for validation test
      fireEvent.click(screen.getByText('Next')); // To Validate & Preview
    });

    it('shows validation results', async () => {
      await waitFor(() => {
        expect(screen.getByText('Validation')).toBeInTheDocument();
        // Will show either success or validation issues
        expect(screen.getByText(/Mapping looks good|Missing required/)).toBeInTheDocument();
      });
    });

    it('displays data preview', async () => {
      await waitFor(() => {
        expect(screen.getByText('Preview (first 5 rows)')).toBeInTheDocument();
        // Should show JSON preview of mapped data
        expect(screen.getByRole('textbox')).toBeInTheDocument();
      });
    });

    it('prevents upload with validation errors', async () => {
      await waitFor(() => {
        const uploadButton = screen.getByText('Upload to Database');
        // Button may be disabled if there are validation issues
        if (screen.getByText(/Missing required/)) {
          expect(uploadButton).toBeDisabled();
        }
      });
    });
  });

  describe('Data Import', () => {
    it('successfully imports Products data', async () => {
      render(<ImportWizard />);

      // Complete wizard setup for Products
      fireEvent.click(screen.getByText('Next'));
      
      const textarea = screen.getByLabelText('Or paste CSV');
      fireEvent.change(textarea, { target: { value: mockCSVData.validInventoryCSV } });
      fireEvent.click(screen.getByText('Parse'));

      await waitFor(() => {
        expect(screen.getByText('Detected Columns')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText('Next'));

      // Map a required field to enable upload
      await waitFor(() => {
        const constantInputs = screen.getAllByPlaceholderText('Constant');
        fireEvent.change(constantInputs[0], { target: { value: 'Test Product' } });
      });

      const uploadButton = screen.getByText('Upload to Database');
      fireEvent.click(uploadButton);

      await waitFor(() => {
        expect(mockSupabaseInsert).toHaveBeenCalledWith(
          expect.arrayContaining([
            expect.objectContaining({
              name: expect.any(String)
            })
          ]),
          { count: 'exact' }
        );
        expect(mockToast).toHaveBeenCalledWith({
          title: 'Import completed',
          description: expect.stringContaining('Inserted')
        });
      });
    });

    it('handles import errors gracefully', async () => {
      mockSupabaseInsert.mockRejectedValue(new Error('Database connection failed'));

      render(<ImportWizard />);

      // Setup for failed import
      fireEvent.click(screen.getByText('Next'));
      
      const textarea = screen.getByLabelText('Or paste CSV');
      fireEvent.change(textarea, { target: { value: mockCSVData.validInventoryCSV } });
      fireEvent.click(screen.getByText('Parse'));

      await waitFor(() => {
        fireEvent.click(screen.getByText('Next'));
      });

      await waitFor(() => {
        const constantInputs = screen.getAllByPlaceholderText('Constant');
        fireEvent.change(constantInputs[0], { target: { value: 'Test Product' } });
      });

      const uploadButton = screen.getByText('Upload to Database');
      fireEvent.click(uploadButton);

      await waitFor(() => {
        expect(mockToast).toHaveBeenCalledWith({
          title: 'Import failed',
          description: 'Database connection failed'
        });
        expect(screen.getByText('Database connection failed')).toBeInTheDocument();
      });
    });

    it('handles large datasets with batching', async () => {
      // Mock large dataset
      const Papa = require('papaparse');
      Papa.default.parse.mockReturnValue({
        data: Array.from({ length: 1500 }, (_, i) => ({
          'Product Name': `Product ${i}`,
          'SKU': `SKU${i}`,
          'Category': 'Test Category'
        })),
        errors: []
      });

      render(<ImportWizard />);

      // Complete import flow
      fireEvent.click(screen.getByText('Next'));
      
      const textarea = screen.getByLabelText('Or paste CSV');
      fireEvent.change(textarea, { target: { value: 'large dataset' } });
      fireEvent.click(screen.getByText('Parse'));

      await waitFor(() => {
        fireEvent.click(screen.getByText('Next'));
      });

      await waitFor(() => {
        const constantInputs = screen.getAllByPlaceholderText('Constant');
        fireEvent.change(constantInputs[0], { target: { value: 'Test Product' } });
      });

      const uploadButton = screen.getByText('Upload to Database');
      fireEvent.click(uploadButton);

      await waitFor(() => {
        // Should call insert multiple times for batching (500 per batch)
        expect(mockSupabaseInsert).toHaveBeenCalledTimes(3); // 1500 / 500 = 3 batches
      });
    });
  });

  describe('Data Sanitization', () => {
    it('sanitizes Products data correctly', async () => {
      render(<ImportWizard />);

      // Setup for Products import with extra fields
      const Papa = require('papaparse');
      Papa.default.parse.mockReturnValue({
        data: [{
          'name': 'Test Product',
          'sku': 'TEST123',
          'category': 'Test',
          'dangerous_field': 'should be removed',
          'script_injection': '<script>alert("hack")</script>'
        }],
        errors: []
      });

      fireEvent.click(screen.getByText('Next'));
      
      const textarea = screen.getByLabelText('Or paste CSV');
      fireEvent.change(textarea, { target: { value: 'test data' } });
      fireEvent.click(screen.getByText('Parse'));

      await waitFor(() => {
        fireEvent.click(screen.getByText('Next'));
      });

      await waitFor(() => {
        const constantInputs = screen.getAllByPlaceholderText('Constant');
        fireEvent.change(constantInputs[0], { target: { value: 'Test Product' } });
      });

      const uploadButton = screen.getByText('Upload to Database');
      fireEvent.click(uploadButton);

      await waitFor(() => {
        const insertCall = mockSupabaseInsert.mock.calls[0][0];
        // Should only contain allowed fields
        expect(insertCall[0]).not.toHaveProperty('dangerous_field');
        expect(insertCall[0]).not.toHaveProperty('script_injection');
        expect(insertCall[0]).toHaveProperty('name');
      });
    });

    it('normalizes inventory event types', async () => {
      render(<ImportWizard />);

      // Switch to inventory events
      fireEvent.click(screen.getByText('inventory_events'));

      const Papa = require('papaparse');
      Papa.default.parse.mockReturnValue({
        data: [{
          'event_type': 'SALES',
          'product_id': 'test-uuid',
          'quantity': '100'
        }],
        errors: []
      });

      fireEvent.click(screen.getByText('Next'));
      
      const textarea = screen.getByLabelText('Or paste CSV');
      fireEvent.change(textarea, { target: { value: 'test data' } });
      fireEvent.click(screen.getByText('Parse'));

      await waitFor(() => {
        fireEvent.click(screen.getByText('Next'));
      });

      await waitFor(() => {
        const constantInputs = screen.getAllByPlaceholderText('Constant');
        if (constantInputs.length > 0) {
          fireEvent.change(constantInputs[0], { target: { value: 'test' } });
        }
      });

      const uploadButton = screen.getByText('Upload to Database');
      fireEvent.click(uploadButton);

      await waitFor(() => {
        const insertCall = mockSupabaseInsert.mock.calls[0][0];
        // 'SALES' should be normalized to 'sale'
        expect(insertCall[0]).toHaveProperty('event_type', 'sale');
      });
    });
  });

  describe('User Experience', () => {
    it('shows loading state during upload', async () => {
      mockSupabaseInsert.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({ error: null, count: 1 }), 1000))
      );

      render(<ImportWizard />);

      // Setup for upload
      fireEvent.click(screen.getByText('Next'));
      
      const textarea = screen.getByLabelText('Or paste CSV');
      fireEvent.change(textarea, { target: { value: mockCSVData.validInventoryCSV } });
      fireEvent.click(screen.getByText('Parse'));

      await waitFor(() => {
        fireEvent.click(screen.getByText('Next'));
      });

      await waitFor(() => {
        const constantInputs = screen.getAllByPlaceholderText('Constant');
        fireEvent.change(constantInputs[0], { target: { value: 'Test Product' } });
      });

      const uploadButton = screen.getByText('Upload to Database');
      fireEvent.click(uploadButton);

      // Should show loading state
      expect(screen.getByText('Uploading...')).toBeInTheDocument();
      expect(screen.getByText('Uploading...')).toBeDisabled();
    });

    it('handles wizard state persistence across steps', async () => {
      render(<ImportWizard />);

      // Set target type
      fireEvent.click(screen.getByText('inventory_events'));

      // Navigate to load data
      fireEvent.click(screen.getByText('Next'));
      
      const textarea = screen.getByLabelText('Or paste CSV');
      fireEvent.change(textarea, { target: { value: mockCSVData.validInventoryCSV } });
      fireEvent.click(screen.getByText('Parse'));

      // Go back to step 1
      await waitFor(() => {
        fireEvent.click(screen.getByText('Back'));
      });

      // Target should still be inventory_events
      expect(screen.getByText('inventory_events')).toHaveClass('bg-');
    });
  });

  describe('Accessibility', () => {
    it('has proper form labels and controls', () => {
      render(<ImportWizard />);

      // Navigate to load data step
      fireEvent.click(screen.getByText('Next'));

      // Check for proper labels
      expect(screen.getByLabelText('Upload CSV')).toBeInTheDocument();
      expect(screen.getByLabelText('Or paste CSV')).toBeInTheDocument();

      // Check that form controls are properly associated
      const fileInput = screen.getByLabelText('Upload CSV');
      expect(fileInput).toHaveAttribute('type', 'file');
      expect(fileInput).toHaveAttribute('accept', '.csv');
    });

    it('provides clear error messages', async () => {
      const Papa = require('papaparse');
      Papa.default.parse.mockReturnValue({
        data: [],
        errors: [{ message: 'Detailed parse error' }]
      });

      render(<ImportWizard />);

      fireEvent.click(screen.getByText('Next'));
      
      const textarea = screen.getByLabelText('Or paste CSV');
      fireEvent.change(textarea, { target: { value: 'bad csv' } });
      fireEvent.click(screen.getByText('Parse'));

      await waitFor(() => {
        expect(screen.getByText(/CSV parse error: Detailed parse error/)).toBeInTheDocument();
      });
    });

    it('supports keyboard navigation', () => {
      render(<ImportWizard />);

      const productsButton = screen.getByText('Products');
      const inventoryButton = screen.getByText('inventory_events');
      const nextButton = screen.getByText('Next');

      // All interactive elements should be focusable
      productsButton.focus();
      expect(productsButton).toHaveFocus();

      inventoryButton.focus();
      expect(inventoryButton).toHaveFocus();

      nextButton.focus();
      expect(nextButton).toHaveFocus();
    });
  });
});