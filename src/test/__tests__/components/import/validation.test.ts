import { describe, it, expect, vi, beforeEach } from 'vitest';
import { validateImportData } from '@/components/import/validation';
import type { ImportRow, ValidationError } from '@/components/import/types';

// Mock AI processor
vi.mock('@/components/import/aiProcessor', () => ({
  validateWithAI: vi.fn()
}));

describe('Import Data Validation', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('validateImportData', () => {
    it('validates valid import data successfully', async () => {
      const validData: ImportRow[] = [
        {
          date: '2024-01-15',
          name: 'Atlantic Salmon',
          sku: 'SAL-001',
          category: 'Fresh Fish',
          unit: 'lbs',
          stock: 100,
          cost: 12.50,
          price: 18.00,
          min_stock: 25
        },
        {
          date: '2024-01-16', 
          name: 'Dungeness Crab',
          sku: 'CRAB-001',
          category: 'Shellfish',
          unit: 'lbs',
          stock: 50,
          cost: 22.00,
          price: 32.00,
          min_stock: 10
        }
      ];

      const { validateWithAI } = require('@/components/import/aiProcessor');
      validateWithAI.mockResolvedValue([]);

      const errors = await validateImportData(validData);
      expect(errors).toEqual([]);
      expect(validateWithAI).toHaveBeenCalledWith(validData);
    });

    it('skips AI validation when basic validation fails', async () => {
      const invalidData: ImportRow[] = [
        {
          date: '',
          name: '',
          sku: '',
          category: '',
          unit: '',
          stock: 'invalid',
        }
      ];

      const { validateWithAI } = require('@/components/import/aiProcessor');
      validateWithAI.mockResolvedValue([]);

      const errors = await validateImportData(invalidData);
      
      expect(errors.length).toBeGreaterThan(0);
      expect(validateWithAI).not.toHaveBeenCalled();
    });

    it('combines basic and AI validation errors', async () => {
      const mixedData: ImportRow[] = [
        {
          date: '2024-01-15',
          name: 'Suspicious Product',
          sku: 'SUS-001',
          category: 'Unknown Category',
          unit: 'lbs',
          stock: 100
        }
      ];

      const { validateWithAI } = require('@/components/import/aiProcessor');
      validateWithAI.mockResolvedValue([
        { row: 2, message: 'Product name contains suspicious keywords' }
      ]);

      const errors = await validateImportData(mixedData);
      
      expect(errors).toContainEqual({
        row: 2,
        message: 'Product name contains suspicious keywords'
      });
    });
  });

  describe('Basic Data Validation', () => {
    describe('Required Fields', () => {
      it('validates required date field', async () => {
        const data: ImportRow[] = [
          {
            date: '',
            name: 'Test Product',
            sku: 'TEST-001',
            category: 'Test',
            unit: 'lbs',
            stock: 100
          }
        ];

        const errors = await validateImportData(data);
        expect(errors).toContainEqual({
          row: 2,
          message: 'Date is required'
        });
      });

      it('validates required name field', async () => {
        const data: ImportRow[] = [
          {
            date: '2024-01-15',
            name: '',
            sku: 'TEST-001',
            category: 'Test',
            unit: 'lbs',
            stock: 100
          }
        ];

        const errors = await validateImportData(data);
        expect(errors).toContainEqual({
          row: 2,
          message: 'Product name is required'
        });
      });

      it('validates required sku field', async () => {
        const data: ImportRow[] = [
          {
            date: '2024-01-15',
            name: 'Test Product',
            sku: '',
            category: 'Test',
            unit: 'lbs',
            stock: 100
          }
        ];

        const errors = await validateImportData(data);
        expect(errors).toContainEqual({
          row: 2,
          message: 'SKU is required'
        });
      });

      it('validates required category field', async () => {
        const data: ImportRow[] = [
          {
            date: '2024-01-15',
            name: 'Test Product',
            sku: 'TEST-001',
            category: '',
            unit: 'lbs',
            stock: 100
          }
        ];

        const errors = await validateImportData(data);
        expect(errors).toContainEqual({
          row: 2,
          message: 'Category is required'
        });
      });

      it('validates required unit field', async () => {
        const data: ImportRow[] = [
          {
            date: '2024-01-15',
            name: 'Test Product',
            sku: 'TEST-001',
            category: 'Test',
            unit: '',
            stock: 100
          }
        ];

        const errors = await validateImportData(data);
        expect(errors).toContainEqual({
          row: 2,
          message: 'Unit is required'
        });
      });
    });

    describe('Date Validation', () => {
      it('validates date format', async () => {
        const data: ImportRow[] = [
          {
            date: 'invalid-date',
            name: 'Test Product',
            sku: 'TEST-001',
            category: 'Test',
            unit: 'lbs',
            stock: 100
          }
        ];

        const errors = await validateImportData(data);
        expect(errors).toContainEqual({
          row: 2,
          message: 'Invalid date format'
        });
      });

      it('accepts various valid date formats', async () => {
        const validDates = [
          '2024-01-15',
          '01/15/2024',
          '2024-01-15T10:30:00Z',
          'January 15, 2024'
        ];

        for (const date of validDates) {
          const data: ImportRow[] = [
            {
              date,
              name: 'Test Product',
              sku: 'TEST-001',
              category: 'Test',
              unit: 'lbs',
              stock: 100
            }
          ];

          const errors = await validateImportData(data);
          const dateErrors = errors.filter(e => e.message.includes('Invalid date format'));
          expect(dateErrors).toHaveLength(0);
        }
      });

      it('validates expiry date format', async () => {
        const data: ImportRow[] = [
          {
            date: '2024-01-15',
            name: 'Test Product',
            sku: 'TEST-001',
            category: 'Test',
            unit: 'lbs',
            stock: 100,
            expiry_date: 'not-a-date'
          }
        ];

        const errors = await validateImportData(data);
        expect(errors).toContainEqual({
          row: 2,
          message: 'Invalid expiry date format'
        });
      });
    });

    describe('Numeric Validation', () => {
      it('validates stock as numeric', async () => {
        const data: ImportRow[] = [
          {
            date: '2024-01-15',
            name: 'Test Product',
            sku: 'TEST-001',
            category: 'Test',
            unit: 'lbs',
            stock: 'not-a-number'
          }
        ];

        const errors = await validateImportData(data);
        expect(errors).toContainEqual({
          row: 2,
          message: 'Stock must be a valid number'
        });
      });

      it('accepts string numbers for stock', async () => {
        const data: ImportRow[] = [
          {
            date: '2024-01-15',
            name: 'Test Product',
            sku: 'TEST-001',
            category: 'Test',
            unit: 'lbs',
            stock: '100.5'
          }
        ];

        const errors = await validateImportData(data);
        const stockErrors = errors.filter(e => e.message.includes('Stock must be a valid number'));
        expect(stockErrors).toHaveLength(0);
      });

      it('validates cost as numeric when provided', async () => {
        const data: ImportRow[] = [
          {
            date: '2024-01-15',
            name: 'Test Product',
            sku: 'TEST-001',
            category: 'Test',
            unit: 'lbs',
            stock: 100,
            cost: 'invalid-cost'
          }
        ];

        const errors = await validateImportData(data);
        expect(errors).toContainEqual({
          row: 2,
          message: 'Cost must be a valid number'
        });
      });

      it('validates price as numeric when provided', async () => {
        const data: ImportRow[] = [
          {
            date: '2024-01-15',
            name: 'Test Product',
            sku: 'TEST-001',
            category: 'Test',
            unit: 'lbs',
            stock: 100,
            price: 'invalid-price'
          }
        ];

        const errors = await validateImportData(data);
        expect(errors).toContainEqual({
          row: 2,
          message: 'Price must be a valid number'
        });
      });

      it('validates min_stock as numeric when provided', async () => {
        const data: ImportRow[] = [
          {
            date: '2024-01-15',
            name: 'Test Product',
            sku: 'TEST-001',
            category: 'Test',
            unit: 'lbs',
            stock: 100,
            min_stock: 'invalid-min-stock'
          }
        ];

        const errors = await validateImportData(data);
        expect(errors).toContainEqual({
          row: 2,
          message: 'Minimum stock must be a valid number'
        });
      });
    });

    describe('Sales Amount Validation', () => {
      it('validates gross_amount as numeric when provided', async () => {
        const data: ImportRow[] = [
          {
            date: '2024-01-15',
            name: 'Test Product',
            sku: 'TEST-001',
            category: 'Test',
            unit: 'lbs',
            stock: 100,
            gross_amount: 'invalid-gross'
          }
        ];

        const errors = await validateImportData(data);
        expect(errors).toContainEqual({
          row: 2,
          message: 'Gross sales amount must be a valid number'
        });
      });

      it('validates net_amount as numeric when provided', async () => {
        const data: ImportRow[] = [
          {
            date: '2024-01-15',
            name: 'Test Product',
            sku: 'TEST-001',
            category: 'Test',
            unit: 'lbs',
            stock: 100,
            net_amount: 'invalid-net'
          }
        ];

        const errors = await validateImportData(data);
        expect(errors).toContainEqual({
          row: 2,
          message: 'Net sales amount must be a valid number'
        });
      });

      it('validates net amount is not greater than gross amount', async () => {
        const data: ImportRow[] = [
          {
            date: '2024-01-15',
            name: 'Test Product',
            sku: 'TEST-001',
            category: 'Test',
            unit: 'lbs',
            stock: 100,
            gross_amount: 100.00,
            net_amount: 150.00
          }
        ];

        const errors = await validateImportData(data);
        expect(errors).toContainEqual({
          row: 2,
          message: 'Net sales amount cannot be greater than gross sales amount'
        });
      });

      it('accepts valid gross and net amounts', async () => {
        const data: ImportRow[] = [
          {
            date: '2024-01-15',
            name: 'Test Product',
            sku: 'TEST-001',
            category: 'Test',
            unit: 'lbs',
            stock: 100,
            gross_amount: 150.00,
            net_amount: 130.00
          }
        ];

        const errors = await validateImportData(data);
        const amountErrors = errors.filter(e => 
          e.message.includes('amount') && e.message.includes('greater')
        );
        expect(amountErrors).toHaveLength(0);
      });
    });

    describe('Row Number Tracking', () => {
      it('provides correct row numbers for errors', async () => {
        const data: ImportRow[] = [
          {
            date: '2024-01-15',
            name: 'Valid Product',
            sku: 'VALID-001',
            category: 'Test',
            unit: 'lbs',
            stock: 100
          },
          {
            date: '',
            name: '',
            sku: '',
            category: '',
            unit: '',
            stock: 'invalid'
          },
          {
            date: '2024-01-17',
            name: 'Another Valid Product',
            sku: 'VALID-002',
            category: 'Test',
            unit: 'lbs',
            stock: 50
          },
          {
            date: 'invalid-date',
            name: 'Product with bad date',
            sku: 'BAD-001',
            category: 'Test',
            unit: 'lbs',
            stock: 25
          }
        ];

        const errors = await validateImportData(data);
        
        // Find errors for the second row (index 1, should be row 3)
        const row3Errors = errors.filter(e => e.row === 3);
        expect(row3Errors.length).toBeGreaterThan(0);
        
        // Find errors for the fourth row (index 3, should be row 5)
        const row5Errors = errors.filter(e => e.row === 5);
        expect(row5Errors.length).toBeGreaterThan(0);
      });
    });

    describe('Type Coercion', () => {
      it('handles string numbers correctly', async () => {
        const data: ImportRow[] = [
          {
            date: '2024-01-15',
            name: 'Test Product',
            sku: 'TEST-001',
            category: 'Test',
            unit: 'lbs',
            stock: '100.5',
            cost: '12.50',
            price: '18.00',
            min_stock: '25',
            gross_amount: '150.00',
            net_amount: '130.00'
          }
        ];

        const errors = await validateImportData(data);
        const numericErrors = errors.filter(e => 
          e.message.includes('must be a valid number')
        );
        expect(numericErrors).toHaveLength(0);
      });

      it('handles mixed numeric types', async () => {
        const data: ImportRow[] = [
          {
            date: '2024-01-15',
            name: 'Test Product',
            sku: 'TEST-001',
            category: 'Test',
            unit: 'lbs',
            stock: 100,        // number
            cost: '12.50',     // string number
            price: 18.00,      // number
            min_stock: '25'    // string number
          }
        ];

        const errors = await validateImportData(data);
        const numericErrors = errors.filter(e => 
          e.message.includes('must be a valid number')
        );
        expect(numericErrors).toHaveLength(0);
      });
    });

    describe('Optional Field Validation', () => {
      it('does not require optional numeric fields', async () => {
        const data: ImportRow[] = [
          {
            date: '2024-01-15',
            name: 'Test Product',
            sku: 'TEST-001',
            category: 'Test',
            unit: 'lbs',
            stock: 100
            // cost, price, min_stock, etc. are optional
          }
        ];

        const errors = await validateImportData(data);
        expect(errors).toHaveLength(0);
      });

      it('validates optional fields when provided', async () => {
        const data: ImportRow[] = [
          {
            date: '2024-01-15',
            name: 'Test Product',
            sku: 'TEST-001',
            category: 'Test',
            unit: 'lbs',
            stock: 100,
            cost: 'invalid'  // Optional but invalid when provided
          }
        ];

        const errors = await validateImportData(data);
        expect(errors).toContainEqual({
          row: 2,
          message: 'Cost must be a valid number'
        });
      });
    });

    describe('Edge Cases', () => {
      it('handles empty data array', async () => {
        const errors = await validateImportData([]);
        expect(errors).toHaveLength(0);
      });

      it('handles undefined and null values appropriately', async () => {
        const data: ImportRow[] = [
          {
            date: '2024-01-15',
            name: 'Test Product',
            sku: 'TEST-001',
            category: 'Test',
            unit: 'lbs',
            stock: 100,
            cost: undefined,
            price: null as any,
            min_stock: undefined
          }
        ];

        const errors = await validateImportData(data);
        // Should not error on undefined/null optional fields
        const numericErrors = errors.filter(e => 
          e.message.includes('must be a valid number')
        );
        expect(numericErrors).toHaveLength(0);
      });

      it('handles very large and very small numbers', async () => {
        const data: ImportRow[] = [
          {
            date: '2024-01-15',
            name: 'Test Product',
            sku: 'TEST-001',
            category: 'Test',
            unit: 'lbs',
            stock: 999999999.99,
            cost: 0.01,
            price: 1000000.00
          }
        ];

        const errors = await validateImportData(data);
        const numericErrors = errors.filter(e => 
          e.message.includes('must be a valid number')
        );
        expect(numericErrors).toHaveLength(0);
      });

      it('handles zero values correctly', async () => {
        const data: ImportRow[] = [
          {
            date: '2024-01-15',
            name: 'Test Product',
            sku: 'TEST-001',
            category: 'Test',
            unit: 'lbs',
            stock: 0,
            cost: 0,
            price: 0,
            min_stock: 0
          }
        ];

        const errors = await validateImportData(data);
        const numericErrors = errors.filter(e => 
          e.message.includes('must be a valid number')
        );
        expect(numericErrors).toHaveLength(0);
      });
    });
  });
});