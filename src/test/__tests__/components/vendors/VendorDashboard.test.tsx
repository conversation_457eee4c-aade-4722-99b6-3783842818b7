/**
 * Comprehensive unit tests for VendorDashboard component
 * Tests performance metrics, filtering, sorting, error states, and user interactions
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { server } from '../../../setup';

import VendorDashboard from '../../../../components/vendors/VendorDashboard';
import { 
  vendorHandlers, 
  vendorErrorHandlers, 
  vendorEmptyHandlers, 
  vendorLoadingHandlers,
  createVendorHandlersWithData 
} from '../../../mocks/vendor-handlers';
import { 
  mockVendorDashboardSummary,
  generateVendorDashboardSummary 
} from '../../../mocks/vendor-data';

// Mock the vendor API module
vi.mock('../../../../lib/vendor-api', () => ({
  vendorAPI: {
    dashboard: {
      getSummary: vi.fn(),
      refreshSummary: vi.fn(),
      getVendorDetails: vi.fn()
    }
  }
}));

describe('VendorDashboard Component', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();
    server.use(...vendorHandlers);
  });

  afterEach(() => {
    server.resetHandlers();
  });

  // ===== RENDERING TESTS =====

  describe('Component Rendering', () => {
    it('renders the dashboard header correctly', async () => {
      render(<VendorDashboard />);
      
      expect(screen.getByText('Vendor Performance Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Monitor and evaluate vendor performance across all metrics')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /refresh data/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /add vendor/i })).toBeInTheDocument();
    });

    it('shows loading state initially', async () => {
      render(<VendorDashboard />);
      
      expect(screen.getByText('Loading vendor dashboard...')).toBeInTheDocument();
      expect(screen.getByRole('progressbar', { hidden: true })).toBeInTheDocument();
    });

    it('renders vendor data after loading', async () => {
      render(<VendorDashboard />);
      
      // Wait for data to load
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      });

      // Check if vendor names are displayed
      expect(screen.getByText('Pacific Seafood Supply')).toBeInTheDocument();
      expect(screen.getByText('Alaska Premium Fish')).toBeInTheDocument();
      expect(screen.getByText('Atlantic Imports LLC')).toBeInTheDocument();
    });
  });

  // ===== PERFORMANCE METRICS TESTS =====

  describe('Performance Summary Cards', () => {
    beforeEach(async () => {
      render(<VendorDashboard />);
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      });
    });

    it('displays total vendors count correctly', () => {
      const totalVendorsCard = screen.getByText('Total Vendors').closest('div');
      expect(within(totalVendorsCard!).getByText('3')).toBeInTheDocument();
    });

    it('calculates A-grade vendors correctly', () => {
      const aGradeCard = screen.getByText('A-Grade Vendors').closest('div');
      expect(within(aGradeCard!).getByText('1')).toBeInTheDocument();
    });

    it('shows total active alerts', () => {
      const alertsCard = screen.getByText('Active Alerts').closest('div');
      const totalAlerts = mockVendorDashboardSummary.reduce((sum, vendor) => 
        sum + (vendor.active_alerts_count || 0), 0
      );
      expect(within(alertsCard!).getByText(totalAlerts.toString())).toBeInTheDocument();
    });

    it('calculates average completion rate correctly', () => {
      const avgCompletionCard = screen.getByText('Avg Completion Rate').closest('div');
      const expectedAverage = (96.5 + 87.3 + 65.8) / 3;
      expect(within(avgCompletionCard!).getByText(`${expectedAverage.toFixed(1)}%`)).toBeInTheDocument();
    });
  });

  // ===== GRADE DISTRIBUTION TESTS =====

  describe('Grade Distribution', () => {
    beforeEach(async () => {
      render(<VendorDashboard />);
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      });
    });

    it('displays grade distribution correctly', () => {
      // Check Grade A count
      const gradeASection = screen.getByText('Grade A').closest('div');
      expect(within(gradeASection!).getByText('1')).toBeInTheDocument();

      // Check Grade B count
      const gradeBSection = screen.getByText('Grade B').closest('div');
      expect(within(gradeBSection!).getByText('1')).toBeInTheDocument();

      // Check Grade D count
      const gradeDSection = screen.getByText('Grade D').closest('div');
      expect(within(gradeDSection!).getByText('1')).toBeInTheDocument();
    });

    it('shows correct grade badges', () => {
      expect(screen.getByText('A')).toBeInTheDocument();
      expect(screen.getByText('B')).toBeInTheDocument();
      expect(screen.getByText('D')).toBeInTheDocument();
    });
  });

  // ===== SEARCH FUNCTIONALITY TESTS =====

  describe('Search Functionality', () => {
    beforeEach(async () => {
      render(<VendorDashboard />);
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      });
    });

    it('filters vendors by name', async () => {
      const searchInput = screen.getByPlaceholderText('Search vendors...');
      
      await user.type(searchInput, 'Pacific');
      
      // Should show only Pacific Seafood Supply
      expect(screen.getByText('Pacific Seafood Supply')).toBeInTheDocument();
      expect(screen.queryByText('Alaska Premium Fish')).not.toBeInTheDocument();
      expect(screen.queryByText('Atlantic Imports LLC')).not.toBeInTheDocument();
    });

    it('filters vendors by contact name', async () => {
      const searchInput = screen.getByPlaceholderText('Search vendors...');
      
      await user.type(searchInput, 'Mike');
      
      // Should show only Alaska Premium Fish (Mike Chen)
      expect(screen.getByText('Alaska Premium Fish')).toBeInTheDocument();
      expect(screen.queryByText('Pacific Seafood Supply')).not.toBeInTheDocument();
      expect(screen.queryByText('Atlantic Imports LLC')).not.toBeInTheDocument();
    });

    it('shows no results message when search has no matches', async () => {
      const searchInput = screen.getByPlaceholderText('Search vendors...');
      
      await user.type(searchInput, 'NonExistentVendor');
      
      expect(screen.getByText('No vendors match your filters')).toBeInTheDocument();
    });

    it('clears search results when input is cleared', async () => {
      const searchInput = screen.getByPlaceholderText('Search vendors...');
      
      // First search
      await user.type(searchInput, 'Pacific');
      expect(screen.getByText('Pacific Seafood Supply')).toBeInTheDocument();
      
      // Clear search
      await user.clear(searchInput);
      
      // All vendors should be visible again
      expect(screen.getByText('Pacific Seafood Supply')).toBeInTheDocument();
      expect(screen.getByText('Alaska Premium Fish')).toBeInTheDocument();
      expect(screen.getByText('Atlantic Imports LLC')).toBeInTheDocument();
    });
  });

  // ===== FILTERING TESTS =====

  describe('Filter Functionality', () => {
    beforeEach(async () => {
      render(<VendorDashboard />);
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      });
    });

    it('filters vendors by grade A', async () => {
      const filterSelect = screen.getByDisplayValue('All Vendors');
      
      await user.selectOptions(filterSelect, 'A');
      
      // Should show only Grade A vendors
      expect(screen.getByText('Pacific Seafood Supply')).toBeInTheDocument();
      expect(screen.queryByText('Alaska Premium Fish')).not.toBeInTheDocument();
      expect(screen.queryByText('Atlantic Imports LLC')).not.toBeInTheDocument();
    });

    it('filters vendors by grade B', async () => {
      const filterSelect = screen.getByDisplayValue('All Vendors');
      
      await user.selectOptions(filterSelect, 'B');
      
      // Should show only Grade B vendors
      expect(screen.getByText('Alaska Premium Fish')).toBeInTheDocument();
      expect(screen.queryByText('Pacific Seafood Supply')).not.toBeInTheDocument();
      expect(screen.queryByText('Atlantic Imports LLC')).not.toBeInTheDocument();
    });

    it('filters vendors with alerts', async () => {
      const filterSelect = screen.getByDisplayValue('All Vendors');
      
      await user.selectOptions(filterSelect, 'alerts');
      
      // Should show vendors with alerts (Alaska Premium Fish and Atlantic Imports LLC)
      expect(screen.getByText('Alaska Premium Fish')).toBeInTheDocument();
      expect(screen.getByText('Atlantic Imports LLC')).toBeInTheDocument();
      expect(screen.queryByText('Pacific Seafood Supply')).not.toBeInTheDocument();
    });

    it('resets filter to show all vendors', async () => {
      const filterSelect = screen.getByDisplayValue('All Vendors');
      
      // Filter by Grade A
      await user.selectOptions(filterSelect, 'A');
      expect(screen.getByText('Pacific Seafood Supply')).toBeInTheDocument();
      
      // Reset to All Vendors
      await user.selectOptions(filterSelect, 'all');
      
      // All vendors should be visible
      expect(screen.getByText('Pacific Seafood Supply')).toBeInTheDocument();
      expect(screen.getByText('Alaska Premium Fish')).toBeInTheDocument();
      expect(screen.getByText('Atlantic Imports LLC')).toBeInTheDocument();
    });
  });

  // ===== SORTING TESTS =====

  describe('Sorting Functionality', () => {
    beforeEach(async () => {
      render(<VendorDashboard />);
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      });
    });

    it('sorts vendors by name alphabetically', async () => {
      const sortSelect = screen.getByDisplayValue('Grade');
      
      await user.selectOptions(sortSelect, 'name');
      
      // Get all vendor name elements
      const vendorElements = screen.getAllByText(/Seafood Supply|Premium Fish|Imports LLC/);
      const vendorNames = vendorElements.map(el => el.textContent);
      
      // Should be in alphabetical order
      expect(vendorNames).toEqual([
        'Alaska Premium Fish',
        'Atlantic Imports LLC', 
        'Pacific Seafood Supply'
      ]);
    });

    it('sorts vendors by completion rate (highest first)', async () => {
      const sortSelect = screen.getByDisplayValue('Grade');
      
      await user.selectOptions(sortSelect, 'completion_rate');
      
      // Pacific Seafood Supply (96.5%) should be first
      const vendorElements = screen.getAllByText(/Seafood Supply|Premium Fish|Imports LLC/);
      expect(vendorElements[0]).toHaveTextContent('Pacific Seafood Supply');
    });

    it('sorts vendors by alerts (highest first)', async () => {
      const sortSelect = screen.getByDisplayValue('Grade');
      
      await user.selectOptions(sortSelect, 'alerts');
      
      // Atlantic Imports LLC (5 alerts) should be first
      const vendorElements = screen.getAllByText(/Seafood Supply|Premium Fish|Imports LLC/);
      expect(vendorElements[0]).toHaveTextContent('Atlantic Imports LLC');
    });
  });

  // ===== ALERT DISPLAY TESTS =====

  describe('Alert Display', () => {
    beforeEach(async () => {
      render(<VendorDashboard />);
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      });
    });

    it('displays alert badges for vendors with alerts', () => {
      // Alaska Premium Fish should show 2 alerts
      const alaskaVendor = screen.getByText('Alaska Premium Fish').closest('.p-6');
      expect(within(alaskaVendor!).getByText('2 alerts')).toBeInTheDocument();
      
      // Atlantic Imports should show 5 alerts
      const atlanticVendor = screen.getByText('Atlantic Imports LLC').closest('.p-6');
      expect(within(atlanticVendor!).getByText('5 alerts')).toBeInTheDocument();
    });

    it('does not display alert badges for vendors without alerts', () => {
      // Pacific Seafood Supply should not show alerts badge
      const pacificVendor = screen.getByText('Pacific Seafood Supply').closest('.p-6');
      expect(within(pacificVendor!).queryByText(/alert/)).not.toBeInTheDocument();
    });
  });

  // ===== NAVIGATION TESTS =====

  describe('Navigation and Actions', () => {
    beforeEach(async () => {
      render(<VendorDashboard />);
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      });
    });

    it('opens vendor report card when View Report is clicked', async () => {
      const viewReportButtons = screen.getAllByText('View Report');
      
      await user.click(viewReportButtons[0]);
      
      // Should render VendorReportCard component (we can check for loading state)
      expect(screen.getByText('Loading vendor report card...')).toBeInTheDocument();
    });

    it('calls refresh dashboard when Refresh Data is clicked', async () => {
      const refreshButton = screen.getByRole('button', { name: /refresh data/i });
      
      await user.click(refreshButton);
      
      // The button should be clickable and not cause errors
      expect(refreshButton).toBeInTheDocument();
    });
  });

  // ===== ERROR HANDLING TESTS =====

  describe('Error Handling', () => {
    it('displays error state when API call fails', async () => {
      server.use(...vendorErrorHandlers);
      
      render(<VendorDashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('Error Loading Dashboard')).toBeInTheDocument();
        expect(screen.getByText('Failed to load dashboard data')).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /try again/i })).toBeInTheDocument();
      });
    });

    it('allows retry after error', async () => {
      server.use(...vendorErrorHandlers);
      
      render(<VendorDashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('Error Loading Dashboard')).toBeInTheDocument();
      });

      // Switch back to successful handlers
      server.use(...vendorHandlers);
      
      const retryButton = screen.getByRole('button', { name: /try again/i });
      await user.click(retryButton);
      
      await waitFor(() => {
        expect(screen.queryByText('Error Loading Dashboard')).not.toBeInTheDocument();
        expect(screen.getByText('Pacific Seafood Supply')).toBeInTheDocument();
      });
    });
  });

  // ===== EMPTY STATE TESTS =====

  describe('Empty States', () => {
    it('displays empty state when no vendors exist', async () => {
      server.use(...vendorEmptyHandlers);
      
      render(<VendorDashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('No vendors found')).toBeInTheDocument();
      });
    });

    it('displays filtered empty state when search has no results', async () => {
      render(<VendorDashboard />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      });
      
      const searchInput = screen.getByPlaceholderText('Search vendors...');
      await user.type(searchInput, 'NonExistentVendor');
      
      expect(screen.getByText('No vendors match your filters')).toBeInTheDocument();
    });
  });

  // ===== PERFORMANCE METRICS CALCULATION TESTS =====

  describe('Performance Metrics Calculations', () => {
    it('handles vendors with null/undefined metrics gracefully', async () => {
      const vendorWithNullMetrics = generateVendorDashboardSummary({
        vendor_id: 'null-vendor',
        vendor_name: 'Null Metrics Vendor',
        completion_rate: null as any,
        on_time_delivery_rate: undefined as any,
        average_quality_score: null as any
      });

      server.use(
        ...createVendorHandlersWithData({
          dashboardSummary: [vendorWithNullMetrics]
        })
      );
      
      render(<VendorDashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('Null Metrics Vendor')).toBeInTheDocument();
      });

      // Check that N/A is displayed for null metrics
      const vendorRow = screen.getByText('Null Metrics Vendor').closest('.p-6');
      expect(within(vendorRow!).getByText('N/A%')).toBeInTheDocument();
    });

    it('calculates grade statistics correctly with mixed data', async () => {
      const mixedGradeVendors = [
        generateVendorDashboardSummary({ overall_letter_grade: 'A' }),
        generateVendorDashboardSummary({ overall_letter_grade: 'A' }),
        generateVendorDashboardSummary({ overall_letter_grade: 'B' }),
        generateVendorDashboardSummary({ overall_letter_grade: 'C' }),
        generateVendorDashboardSummary({ overall_letter_grade: null as any })
      ];

      server.use(
        ...createVendorHandlersWithData({
          dashboardSummary: mixedGradeVendors
        })
      );
      
      render(<VendorDashboard />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      });

      // Check grade distribution
      expect(within(screen.getByText('Grade A').closest('div')!).getByText('2')).toBeInTheDocument();
      expect(within(screen.getByText('Grade B').closest('div')!).getByText('1')).toBeInTheDocument();
      expect(within(screen.getByText('Grade C').closest('div')!).getByText('1')).toBeInTheDocument();
      expect(within(screen.getByText('Unrated').closest('div')!).getByText('1')).toBeInTheDocument();
    });
  });

  // ===== RESPONSIVE DESIGN TESTS =====

  describe('Responsive Design', () => {
    it('maintains functionality on smaller screens', async () => {
      // Simulate mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });
      window.dispatchEvent(new Event('resize'));

      render(<VendorDashboard />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      });

      // All major elements should still be accessible
      expect(screen.getByText('Vendor Performance Dashboard')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Search vendors...')).toBeInTheDocument();
      expect(screen.getByText('Pacific Seafood Supply')).toBeInTheDocument();
    });
  });

  // ===== ACCESSIBILITY TESTS =====

  describe('Accessibility', () => {
    beforeEach(async () => {
      render(<VendorDashboard />);
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      });
    });

    it('has proper ARIA labels for interactive elements', () => {
      const searchInput = screen.getByPlaceholderText('Search vendors...');
      expect(searchInput).toHaveAttribute('type', 'text');
      
      const sortSelect = screen.getByDisplayValue('Grade');
      expect(sortSelect).toHaveAccessibleName();
      
      const filterSelect = screen.getByDisplayValue('All Vendors');
      expect(filterSelect).toHaveAccessibleName();
    });

    it('maintains keyboard navigation', async () => {
      const searchInput = screen.getByPlaceholderText('Search vendors...');
      const sortSelect = screen.getByDisplayValue('Grade');
      
      searchInput.focus();
      expect(document.activeElement).toBe(searchInput);
      
      // Tab to next element
      await user.tab();
      // Sort select should be focusable
      expect(document.activeElement).not.toBe(searchInput);
    });

    it('provides proper button labels', () => {
      const refreshButton = screen.getByRole('button', { name: /refresh data/i });
      const addVendorButton = screen.getByRole('button', { name: /add vendor/i });
      const viewReportButtons = screen.getAllByRole('button', { name: /view report/i });
      
      expect(refreshButton).toBeInTheDocument();
      expect(addVendorButton).toBeInTheDocument();
      expect(viewReportButtons.length).toBeGreaterThan(0);
    });
  });
});