/**
 * Comprehensive unit tests for VendorReportCard component
 * Tests tab navigation, alert management, metrics display, and user interactions
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { server } from '../../../setup';

import VendorReportCard from '../../../../components/vendors/VendorReportCard';
import { 
  vendorHandlers,
  vendorErrorHandlers,
  createVendorHandlersWithData 
} from '../../../mocks/vendor-handlers';
import { 
  mockVendor,
  mockVendorMetrics,
  mockVendorRatings,
  mockVendorCompliance,
  mockVendorPerformanceAlerts,
  mockVendorInteractions
} from '../../../mocks/vendor-data';

// Mock the vendor API module
const mockVendorAPI = {
  dashboard: {
    getVendorDetails: vi.fn()
  },
  alerts: {
    acknowledge: vi.fn(),
    resolve: vi.fn()
  },
  metrics: {
    calculate: vi.fn()
  }
};

vi.mock('../../../../lib/vendor-api', () => ({
  vendorAPI: mockVendorAPI
}));

describe('VendorReportCard Component', () => {
  const user = userEvent.setup();
  const mockOnClose = vi.fn();
  const defaultProps = {
    vendorId: 'vendor-001',
    onClose: mockOnClose
  };

  beforeEach(() => {
    vi.clearAllMocks();
    server.use(...vendorHandlers);
    
    // Setup default mock responses
    mockVendorAPI.dashboard.getVendorDetails.mockResolvedValue({
      vendor: mockVendor,
      currentMetrics: mockVendorMetrics,
      recentInteractions: mockVendorInteractions,
      recentRatings: mockVendorRatings,
      complianceStatus: mockVendorCompliance,
      activeAlerts: mockVendorPerformanceAlerts.filter(alert => alert.status === 'active')
    });
  });

  afterEach(() => {
    server.resetHandlers();
    mockOnClose.mockClear();
  });

  // ===== RENDERING TESTS =====

  describe('Component Rendering', () => {
    it('shows loading state initially', () => {
      render(<VendorReportCard {...defaultProps} />);
      
      expect(screen.getByText('Loading vendor report card...')).toBeInTheDocument();
      expect(screen.getByRole('progressbar', { hidden: true })).toBeInTheDocument();
    });

    it('renders vendor header information correctly', async () => {
      render(<VendorReportCard {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor report card...')).not.toBeInTheDocument();
      });

      expect(screen.getByText('Pacific Seafood Supply')).toBeInTheDocument();
      expect(screen.getByText('Sarah Johnson')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText('(555) 123-4567')).toBeInTheDocument();
    });

    it('displays overall grade badge correctly', async () => {
      render(<VendorReportCard {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor report card...')).not.toBeInTheDocument();
      });

      const gradeBadge = screen.getByText('A');
      expect(gradeBadge).toBeInTheDocument();
      expect(gradeBadge.closest('div')).toHaveClass('text-green-600', 'bg-green-100');
    });

    it('shows close button when onClose is provided', async () => {
      render(<VendorReportCard {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor report card...')).not.toBeInTheDocument();
      });

      const closeButton = screen.getByRole('button', { name: '✕' });
      expect(closeButton).toBeInTheDocument();
    });

    it('hides close button when onClose is not provided', async () => {
      render(<VendorReportCard vendorId="vendor-001" />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor report card...')).not.toBeInTheDocument();
      });

      expect(screen.queryByRole('button', { name: '✕' })).not.toBeInTheDocument();
    });
  });

  // ===== PERFORMANCE SUMMARY CARDS TESTS =====

  describe('Performance Summary Cards', () => {
    beforeEach(async () => {
      render(<VendorReportCard {...defaultProps} />);
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor report card...')).not.toBeInTheDocument();
      });
    });

    it('displays completion rate correctly', () => {
      const completionCard = screen.getByText('Completion Rate').closest('div');
      expect(within(completionCard!).getByText('95.6%')).toBeInTheDocument();
    });

    it('displays on-time delivery rate correctly', () => {
      const onTimeCard = screen.getByText('On-Time Delivery').closest('div');
      expect(within(onTimeCard!).getByText('94.2%')).toBeInTheDocument();
    });

    it('displays average quality score correctly', () => {
      const qualityCard = screen.getByText('Avg Quality').closest('div');
      expect(within(qualityCard!).getByText('8.7/10')).toBeInTheDocument();
    });

    it('displays total orders correctly', () => {
      const ordersCard = screen.getByText('Total Orders').closest('div');
      expect(within(ordersCard!).getByText('45')).toBeInTheDocument();
    });

    it('handles null metrics gracefully', async () => {
      mockVendorAPI.dashboard.getVendorDetails.mockResolvedValue({
        vendor: mockVendor,
        currentMetrics: null,
        recentInteractions: [],
        recentRatings: [],
        complianceStatus: null,
        activeAlerts: []
      });

      render(<VendorReportCard {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor report card...')).not.toBeInTheDocument();
      });

      // Should display N/A for null metrics
      expect(screen.getAllByText('N/A')).toHaveLength(4);
    });
  });

  // ===== ALERT DISPLAY TESTS =====

  describe('Alert Display', () => {
    it('shows alert count in header when alerts exist', async () => {
      const alertsData = mockVendorPerformanceAlerts.filter(alert => alert.status === 'active');
      
      mockVendorAPI.dashboard.getVendorDetails.mockResolvedValue({
        vendor: mockVendor,
        currentMetrics: mockVendorMetrics,
        recentInteractions: [],
        recentRatings: [],
        complianceStatus: null,
        activeAlerts: alertsData
      });

      render(<VendorReportCard {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor report card...')).not.toBeInTheDocument();
      });

      expect(screen.getByText(`${alertsData.length} Alert${alertsData.length !== 1 ? 's' : ''}`)).toBeInTheDocument();
    });

    it('does not show alert badge when no alerts', async () => {
      mockVendorAPI.dashboard.getVendorDetails.mockResolvedValue({
        vendor: mockVendor,
        currentMetrics: mockVendorMetrics,
        recentInteractions: [],
        recentRatings: [],
        complianceStatus: null,
        activeAlerts: []
      });

      render(<VendorReportCard {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor report card...')).not.toBeInTheDocument();
      });

      expect(screen.queryByText(/Alert/)).not.toBeInTheDocument();
    });
  });

  // ===== TAB NAVIGATION TESTS =====

  describe('Tab Navigation', () => {
    beforeEach(async () => {
      render(<VendorReportCard {...defaultProps} />);
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor report card...')).not.toBeInTheDocument();
      });
    });

    it('renders all tab buttons', () => {
      expect(screen.getByRole('button', { name: /overview/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /ratings/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /compliance/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /alerts/i })).toBeInTheDocument();
    });

    it('shows overview tab as active by default', () => {
      const overviewTab = screen.getByRole('button', { name: /overview/i });
      expect(overviewTab).toHaveClass('border-blue-500', 'text-blue-600');
    });

    it('switches to ratings tab when clicked', async () => {
      const ratingsTab = screen.getByRole('button', { name: /ratings/i });
      
      await user.click(ratingsTab);
      
      expect(ratingsTab).toHaveClass('border-blue-500', 'text-blue-600');
      expect(screen.getByText('Recent Ratings')).toBeInTheDocument();
    });

    it('switches to compliance tab when clicked', async () => {
      const complianceTab = screen.getByRole('button', { name: /compliance/i });
      
      await user.click(complianceTab);
      
      expect(complianceTab).toHaveClass('border-blue-500', 'text-blue-600');
      expect(screen.getByText('Compliance Status')).toBeInTheDocument();
    });

    it('switches to alerts tab when clicked', async () => {
      const alertsTab = screen.getByRole('button', { name: /alerts/i });
      
      await user.click(alertsTab);
      
      expect(alertsTab).toHaveClass('border-blue-500', 'text-blue-600');
      expect(screen.getByText('Performance Alerts')).toBeInTheDocument();
    });

    it('shows alert count badge on alerts tab', async () => {
      const activeAlerts = mockVendorPerformanceAlerts.filter(alert => alert.status === 'active');
      
      mockVendorAPI.dashboard.getVendorDetails.mockResolvedValue({
        vendor: mockVendor,
        currentMetrics: mockVendorMetrics,
        recentInteractions: [],
        recentRatings: [],
        complianceStatus: null,
        activeAlerts: activeAlerts
      });

      render(<VendorReportCard {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor report card...')).not.toBeInTheDocument();
      });

      const alertsTab = screen.getByRole('button', { name: /alerts/i });
      const badge = within(alertsTab).getByText(activeAlerts.length.toString());
      expect(badge).toHaveClass('bg-red-500', 'text-white');
    });
  });

  // ===== OVERVIEW TAB TESTS =====

  describe('Overview Tab Content', () => {
    beforeEach(async () => {
      render(<VendorReportCard {...defaultProps} />);
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor report card...')).not.toBeInTheDocument();
      });
    });

    it('displays financial performance metrics', () => {
      expect(screen.getByText('Financial Performance')).toBeInTheDocument();
      expect(screen.getByText('Total Order Value')).toBeInTheDocument();
      expect(screen.getByText('$125,000')).toBeInTheDocument();
      expect(screen.getByText('Delivered Value')).toBeInTheDocument();
      expect(screen.getByText('$119,500')).toBeInTheDocument();
      expect(screen.getByText('Avg Order Value')).toBeInTheDocument();
      expect(screen.getByText('$2,778')).toBeInTheDocument();
    });

    it('displays issue resolution metrics', () => {
      expect(screen.getByText('Issue Resolution')).toBeInTheDocument();
      expect(screen.getByText('Resolution Rate')).toBeInTheDocument();
      expect(screen.getByText('100.0%')).toBeInTheDocument();
      expect(screen.getByText('Avg Resolution Time')).toBeInTheDocument();
      expect(screen.getByText('4.5h')).toBeInTheDocument();
    });

    it('displays resolution rate progress bar', () => {
      const progressBar = screen.getByText('100.0%').closest('div')?.querySelector('.bg-green-500');
      expect(progressBar).toBeInTheDocument();
      expect(progressBar).toHaveStyle('width: 100%');
    });
  });

  // ===== RATINGS TAB TESTS =====

  describe('Ratings Tab Content', () => {
    beforeEach(async () => {
      render(<VendorReportCard {...defaultProps} />);
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor report card...')).not.toBeInTheDocument();
      });
    });

    it('switches to ratings tab and displays content', async () => {
      const ratingsTab = screen.getByRole('button', { name: /ratings/i });
      await user.click(ratingsTab);
      
      expect(screen.getByText('Recent Ratings')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /add new rating/i })).toBeInTheDocument();
    });

    it('displays ratings list when ratings exist', async () => {
      const ratingsTab = screen.getByRole('button', { name: /ratings/i });
      await user.click(ratingsTab);
      
      // Should display first 5 ratings
      mockVendorRatings.slice(0, 5).forEach(rating => {
        if (rating.quality_notes) {
          expect(screen.getByText(rating.quality_notes)).toBeInTheDocument();
        }
      });
    });

    it('displays individual rating scores', async () => {
      const ratingsTab = screen.getByRole('button', { name: /ratings/i });
      await user.click(ratingsTab);
      
      expect(screen.getByText('Quality:')).toBeInTheDocument();
      expect(screen.getByText('9/10')).toBeInTheDocument();
      expect(screen.getByText('Delivery:')).toBeInTheDocument();
      expect(screen.getByText('8/10')).toBeInTheDocument();
    });

    it('shows empty state when no ratings exist', async () => {
      mockVendorAPI.dashboard.getVendorDetails.mockResolvedValue({
        vendor: mockVendor,
        currentMetrics: mockVendorMetrics,
        recentInteractions: [],
        recentRatings: [],
        complianceStatus: null,
        activeAlerts: []
      });

      render(<VendorReportCard {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor report card...')).not.toBeInTheDocument();
      });

      const ratingsTab = screen.getByRole('button', { name: /ratings/i });
      await user.click(ratingsTab);
      
      expect(screen.getByText('No ratings available yet')).toBeInTheDocument();
    });
  });

  // ===== COMPLIANCE TAB TESTS =====

  describe('Compliance Tab Content', () => {
    beforeEach(async () => {
      render(<VendorReportCard {...defaultProps} />);
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor report card...')).not.toBeInTheDocument();
      });
    });

    it('displays HACCP compliance information', async () => {
      const complianceTab = screen.getByRole('button', { name: /compliance/i });
      await user.click(complianceTab);
      
      expect(screen.getByText('HACCP Compliance')).toBeInTheDocument();
      expect(screen.getByText('Certified')).toBeInTheDocument();
      expect(screen.getByText('Certificate Expiry:')).toBeInTheDocument();
      expect(screen.getByText('Temperature Logs:')).toBeInTheDocument();
      expect(screen.getByText('Complete')).toBeInTheDocument();
      expect(screen.getByText('CCP Monitoring:')).toBeInTheDocument();
      expect(screen.getByText('Met')).toBeInTheDocument();
    });

    it('displays GDST traceability information', async () => {
      const complianceTab = screen.getByRole('button', { name: /compliance/i });
      await user.click(complianceTab);
      
      expect(screen.getByText('GDST Traceability')).toBeInTheDocument();
      expect(screen.getByText('Catch Certificate:')).toBeInTheDocument();
      expect(screen.getByText('Provided')).toBeInTheDocument();
      expect(screen.getByText('Chain of Custody:')).toBeInTheDocument();
      expect(screen.getByText('Species Verification:')).toBeInTheDocument();
      expect(screen.getByText('Verified')).toBeInTheDocument();
    });

    it('displays overall compliance score', async () => {
      const complianceTab = screen.getByRole('button', { name: /compliance/i });
      await user.click(complianceTab);
      
      expect(screen.getByText('Overall Compliance Score')).toBeInTheDocument();
      expect(screen.getByText('92/100')).toBeInTheDocument();
      
      // Check progress bar
      const progressBar = screen.getByText('92/100').closest('div')?.nextElementSibling?.querySelector('.bg-green-500');
      expect(progressBar).toBeInTheDocument();
      expect(progressBar).toHaveStyle('width: 92%');
    });

    it('shows compliance status and color coding', async () => {
      const complianceTab = screen.getByRole('button', { name: /compliance/i });
      await user.click(complianceTab);
      
      const score92 = screen.getByText('92/100');
      expect(score92.closest('div')).toHaveClass('bg-green-100', 'text-green-800');
    });

    it('shows empty state when no compliance data exists', async () => {
      mockVendorAPI.dashboard.getVendorDetails.mockResolvedValue({
        vendor: mockVendor,
        currentMetrics: mockVendorMetrics,
        recentInteractions: [],
        recentRatings: [],
        complianceStatus: null,
        activeAlerts: []
      });

      render(<VendorReportCard {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor report card...')).not.toBeInTheDocument();
      });

      const complianceTab = screen.getByRole('button', { name: /compliance/i });
      await user.click(complianceTab);
      
      expect(screen.getByText('No compliance data available')).toBeInTheDocument();
    });
  });

  // ===== ALERTS TAB TESTS =====

  describe('Alerts Tab Content', () => {
    const activeAlerts = mockVendorPerformanceAlerts.filter(alert => alert.status === 'active');

    beforeEach(async () => {
      mockVendorAPI.dashboard.getVendorDetails.mockResolvedValue({
        vendor: mockVendor,
        currentMetrics: mockVendorMetrics,
        recentInteractions: [],
        recentRatings: [],
        complianceStatus: null,
        activeAlerts: activeAlerts
      });

      render(<VendorReportCard {...defaultProps} />);
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor report card...')).not.toBeInTheDocument();
      });
    });

    it('displays alert count summary', async () => {
      const alertsTab = screen.getByRole('button', { name: /alerts/i });
      await user.click(alertsTab);
      
      expect(screen.getByText(`${activeAlerts.length} active alert${activeAlerts.length !== 1 ? 's' : ''}`)).toBeInTheDocument();
    });

    it('displays individual alerts with proper information', async () => {
      const alertsTab = screen.getByRole('button', { name: /alerts/i });
      await user.click(alertsTab);
      
      activeAlerts.forEach(alert => {
        expect(screen.getByText(alert.title!)).toBeInTheDocument();
        expect(screen.getByText(alert.description!)).toBeInTheDocument();
        expect(screen.getByText(alert.severity!.toUpperCase())).toBeInTheDocument();
      });
    });

    it('displays severity badges with correct styling', async () => {
      const alertsTab = screen.getByRole('button', { name: /alerts/i });
      await user.click(alertsTab);
      
      const mediumBadge = screen.getByText('MEDIUM');
      expect(mediumBadge).toHaveClass('bg-yellow-500', 'text-white');
      
      const highBadge = screen.getByText('HIGH');
      expect(highBadge).toHaveClass('bg-orange-500', 'text-white');
      
      const criticalBadge = screen.getByText('CRITICAL');
      expect(criticalBadge).toHaveClass('bg-red-500', 'text-white');
    });

    it('shows acknowledge and resolve buttons for each alert', async () => {
      const alertsTab = screen.getByRole('button', { name: /alerts/i });
      await user.click(alertsTab);
      
      const acknowledgeButtons = screen.getAllByText('Acknowledge');
      const resolveButtons = screen.getAllByText('Resolve');
      
      expect(acknowledgeButtons).toHaveLength(activeAlerts.length);
      expect(resolveButtons).toHaveLength(activeAlerts.length);
    });

    it('calls acknowledge API when acknowledge button is clicked', async () => {
      const alertsTab = screen.getByRole('button', { name: /alerts/i });
      await user.click(alertsTab);
      
      const acknowledgeButton = screen.getAllByText('Acknowledge')[0];
      await user.click(acknowledgeButton);
      
      expect(mockVendorAPI.alerts.acknowledge).toHaveBeenCalledWith(activeAlerts[0].id);
    });

    it('calls resolve API when resolve button is clicked', async () => {
      const alertsTab = screen.getByRole('button', { name: /alerts/i });
      await user.click(alertsTab);
      
      const resolveButton = screen.getAllByText('Resolve')[0];
      await user.click(resolveButton);
      
      expect(mockVendorAPI.alerts.resolve).toHaveBeenCalledWith(
        activeAlerts[0].id,
        'Resolved via dashboard'
      );
    });

    it('shows all clear state when no alerts exist', async () => {
      mockVendorAPI.dashboard.getVendorDetails.mockResolvedValue({
        vendor: mockVendor,
        currentMetrics: mockVendorMetrics,
        recentInteractions: [],
        recentRatings: [],
        complianceStatus: null,
        activeAlerts: []
      });

      render(<VendorReportCard {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor report card...')).not.toBeInTheDocument();
      });

      const alertsTab = screen.getByRole('button', { name: /alerts/i });
      await user.click(alertsTab);
      
      expect(screen.getByText('All Clear!')).toBeInTheDocument();
      expect(screen.getByText('No performance alerts for this vendor')).toBeInTheDocument();
    });

    it('displays triggered metric information when available', async () => {
      const alertsTab = screen.getByRole('button', { name: /alerts/i });
      await user.click(alertsTab);
      
      // Find alert with triggered metric data
      const alertWithMetric = activeAlerts.find(alert => alert.triggered_by_metric);
      if (alertWithMetric) {
        expect(screen.getByText(`Triggered by: ${alertWithMetric.triggered_by_metric}`)).toBeInTheDocument();
        
        if (alertWithMetric.actual_value && alertWithMetric.threshold_value) {
          expect(screen.getByText(`(actual: ${alertWithMetric.actual_value}, threshold: ${alertWithMetric.threshold_value})`)).toBeInTheDocument();
        }
      }
    });
  });

  // ===== FOOTER ACTIONS TESTS =====

  describe('Footer Actions', () => {
    beforeEach(async () => {
      render(<VendorReportCard {...defaultProps} />);
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor report card...')).not.toBeInTheDocument();
      });
    });

    it('displays last updated timestamp', () => {
      expect(screen.getByText(/last updated:/i)).toBeInTheDocument();
    });

    it('shows refresh metrics button', () => {
      expect(screen.getByRole('button', { name: /refresh metrics/i })).toBeInTheDocument();
    });

    it('shows export report button', () => {
      expect(screen.getByRole('button', { name: /export report/i })).toBeInTheDocument();
    });

    it('calls metrics calculation when refresh button is clicked', async () => {
      const refreshButton = screen.getByRole('button', { name: /refresh metrics/i });
      
      await user.click(refreshButton);
      
      expect(mockVendorAPI.metrics.calculate).toHaveBeenCalledWith('vendor-001');
    });
  });

  // ===== ERROR HANDLING TESTS =====

  describe('Error Handling', () => {
    it('displays error state when API call fails', async () => {
      mockVendorAPI.dashboard.getVendorDetails.mockRejectedValue(new Error('Failed to fetch vendor details'));
      
      render(<VendorReportCard {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByText('Error Loading Report Card')).toBeInTheDocument();
        expect(screen.getByText('Failed to fetch vendor details')).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /try again/i })).toBeInTheDocument();
      });
    });

    it('allows retry after error', async () => {
      // Start with error
      mockVendorAPI.dashboard.getVendorDetails.mockRejectedValue(new Error('Network error'));
      
      render(<VendorReportCard {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByText('Error Loading Report Card')).toBeInTheDocument();
      });

      // Fix the API
      mockVendorAPI.dashboard.getVendorDetails.mockResolvedValue({
        vendor: mockVendor,
        currentMetrics: mockVendorMetrics,
        recentInteractions: [],
        recentRatings: [],
        complianceStatus: null,
        activeAlerts: []
      });
      
      const retryButton = screen.getByRole('button', { name: /try again/i });
      await user.click(retryButton);
      
      await waitFor(() => {
        expect(screen.queryByText('Error Loading Report Card')).not.toBeInTheDocument();
        expect(screen.getByText('Pacific Seafood Supply')).toBeInTheDocument();
      });
    });

    it('handles null vendor data gracefully', async () => {
      mockVendorAPI.dashboard.getVendorDetails.mockResolvedValue(null);
      
      render(<VendorReportCard {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByText('No vendor data available')).toBeInTheDocument();
      });
    });
  });

  // ===== INTERACTION TESTS =====

  describe('User Interactions', () => {
    it('calls onClose when close button is clicked', async () => {
      render(<VendorReportCard {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor report card...')).not.toBeInTheDocument();
      });

      const closeButton = screen.getByRole('button', { name: '✕' });
      await user.click(closeButton);
      
      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });

    it('maintains tab state when switching between tabs', async () => {
      render(<VendorReportCard {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor report card...')).not.toBeInTheDocument();
      });

      // Switch to ratings tab
      const ratingsTab = screen.getByRole('button', { name: /ratings/i });
      await user.click(ratingsTab);
      
      expect(screen.getByText('Recent Ratings')).toBeInTheDocument();
      
      // Switch to compliance tab
      const complianceTab = screen.getByRole('button', { name: /compliance/i });
      await user.click(complianceTab);
      
      expect(screen.getByText('Compliance Status')).toBeInTheDocument();
      
      // Switch back to overview
      const overviewTab = screen.getByRole('button', { name: /overview/i });
      await user.click(overviewTab);
      
      expect(screen.getByText('Financial Performance')).toBeInTheDocument();
    });
  });

  // ===== ACCESSIBILITY TESTS =====

  describe('Accessibility', () => {
    beforeEach(async () => {
      render(<VendorReportCard {...defaultProps} />);
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor report card...')).not.toBeInTheDocument();
      });
    });

    it('has proper ARIA labels for tab navigation', () => {
      const tabs = screen.getAllByRole('button').filter(button => 
        button.textContent?.includes('Overview') || 
        button.textContent?.includes('Ratings') ||
        button.textContent?.includes('Compliance') ||
        button.textContent?.includes('Alerts')
      );
      
      expect(tabs.length).toBe(4);
      tabs.forEach(tab => {
        expect(tab).toBeInTheDocument();
      });
    });

    it('maintains keyboard navigation between tabs', async () => {
      const overviewTab = screen.getByRole('button', { name: /overview/i });
      const ratingsTab = screen.getByRole('button', { name: /ratings/i });
      
      overviewTab.focus();
      expect(document.activeElement).toBe(overviewTab);
      
      await user.tab();
      expect(document.activeElement).toBe(ratingsTab);
    });

    it('has proper button labels for actions', () => {
      const refreshButton = screen.getByRole('button', { name: /refresh metrics/i });
      const exportButton = screen.getByRole('button', { name: /export report/i });
      
      expect(refreshButton).toBeInTheDocument();
      expect(exportButton).toBeInTheDocument();
    });
  });
});