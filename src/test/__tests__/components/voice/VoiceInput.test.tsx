import React from 'react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@/test/utils/test-utils';
import { server } from '@/test/setup';
import { http, HttpResponse } from 'msw';
import VoiceInput, { TranscriptionData } from '@/components/voice/VoiceInput';
import { mockSupabaseData, createMockSpeechRecognitionEvent } from '@/test/mocks/data';

// Mock react-speech-recognition
const mockUseSpeechRecognition = vi.fn();
const mockSpeechRecognition = {
  startListening: vi.fn(),
  stopListening: vi.fn(),
  abortListening: vi.fn(),
  browserSupportsSpeechRecognition: true
};

vi.mock('react-speech-recognition', () => ({
  default: mockSpeechRecognition,
  useSpeechRecognition: () => mockUseSpeechRecognition()
}));

// Mock the AI processing module
vi.mock('@/lib/ai', () => ({
  processVoiceInput: vi.fn()
}));

// Mock the database setup module
vi.mock('@/lib/setupDatabase', () => ({
  getProductCategories: vi.fn()
}));

describe('VoiceInput Component', () => {
  const mockOnTranscriptionComplete = vi.fn();
  const mockOnError = vi.fn();
  
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Default mock implementation
    mockUseSpeechRecognition.mockReturnValue({
      transcript: '',
      listening: false,
      resetTranscript: vi.fn(),
      browserSupportsSpeechRecognition: true
    });

    // Mock categories endpoint
    server.use(
      http.get('*/rest/v1/categories', () => {
        return HttpResponse.json(mockSupabaseData.categories);
      })
    );
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('Component Initialization', () => {
    it('renders voice input interface correctly', async () => {
      render(
        <VoiceInput 
          onTranscriptionComplete={mockOnTranscriptionComplete}
          onError={mockOnError}
        />
      );

      // Check for main elements
      expect(screen.getByText('Start Recording')).toBeInTheDocument();
      expect(screen.getByText('Select category')).toBeInTheDocument();
      expect(screen.getByText('Voice Command Examples:')).toBeInTheDocument();
      
      // Check for example commands
      expect(screen.getByText(/Received 50 lbs of Atlantic Salmon/)).toBeInTheDocument();
      expect(screen.getByText(/New shipment of Tiger Prawns/)).toBeInTheDocument();
    });

    it('loads categories on mount', async () => {
      render(
        <VoiceInput 
          onTranscriptionComplete={mockOnTranscriptionComplete}
          onError={mockOnError}
        />
      );

      await waitFor(() => {
        expect(screen.getByText('Fresh Fish')).toBeInTheDocument();
        expect(screen.getByText('Frozen Fish')).toBeInTheDocument();
        expect(screen.getByText('Shellfish')).toBeInTheDocument();
      });
    });

    it('shows error when browser does not support speech recognition', () => {
      mockUseSpeechRecognition.mockReturnValue({
        transcript: '',
        listening: false,
        resetTranscript: vi.fn(),
        browserSupportsSpeechRecognition: false
      });

      render(
        <VoiceInput 
          onTranscriptionComplete={mockOnTranscriptionComplete}
          onError={mockOnError}
        />
      );

      expect(mockOnError).toHaveBeenCalledWith('Browser does not support speech recognition.');
    });
  });

  describe('Category Selection', () => {
    it('requires category selection before recording', async () => {
      render(
        <VoiceInput 
          onTranscriptionComplete={mockOnTranscriptionComplete}
          onError={mockOnError}
        />
      );

      const recordButton = screen.getByText('Start Recording');
      expect(recordButton).toBeDisabled();
      
      expect(screen.getByText('Please select a category before recording')).toBeInTheDocument();
    });

    it('enables recording after category selection', async () => {
      render(
        <VoiceInput 
          onTranscriptionComplete={mockOnTranscriptionComplete}
          onError={mockOnError}
        />
      );

      // Wait for categories to load
      await waitFor(() => {
        expect(screen.getByText('Fresh Fish')).toBeInTheDocument();
      });

      // Open category dropdown
      const categorySelect = screen.getByText('Select category');
      fireEvent.click(categorySelect);

      // Select a category
      const freshFishOption = screen.getByText('Fresh Fish');
      fireEvent.click(freshFishOption);

      // Recording button should now be enabled
      const recordButton = screen.getByText('Start Recording');
      expect(recordButton).not.toBeDisabled();
    });

    it('displays category descriptions when loaded', async () => {
      render(
        <VoiceInput 
          onTranscriptionComplete={mockOnTranscriptionComplete}
          onError={mockOnError}
        />
      );

      await waitFor(() => {
        expect(screen.getByText('Fresh Fish - Fresh fish products')).toBeInTheDocument();
        expect(screen.getByText('Shellfish - Fresh and frozen shellfish')).toBeInTheDocument();
      });
    });
  });

  describe('Voice Recording', () => {
    beforeEach(async () => {
      // Setup component with category selected
      render(
        <VoiceInput 
          onTranscriptionComplete={mockOnTranscriptionComplete}
          onError={mockOnError}
        />
      );

      await waitFor(() => {
        expect(screen.getByText('Fresh Fish')).toBeInTheDocument();
      });

      // Select category
      const categorySelect = screen.getByText('Select category');
      fireEvent.click(categorySelect);
      const freshFishOption = screen.getByText('Fresh Fish');
      fireEvent.click(freshFishOption);
    });

    it('starts listening when record button is clicked', () => {
      const recordButton = screen.getByText('Start Recording');
      fireEvent.click(recordButton);

      expect(mockSpeechRecognition.startListening).toHaveBeenCalledWith({ continuous: true });
    });

    it('shows listening state when recording', () => {
      mockUseSpeechRecognition.mockReturnValue({
        transcript: '',
        listening: true,
        resetTranscript: vi.fn(),
        browserSupportsSpeechRecognition: true
      });

      render(
        <VoiceInput 
          onTranscriptionComplete={mockOnTranscriptionComplete}
          onError={mockOnError}
        />
      );

      expect(screen.getByText('Stop Recording')).toBeInTheDocument();
      expect(screen.getByText('Listening...')).toBeInTheDocument();
    });

    it('displays transcript while recording', () => {
      const testTranscript = 'Add fifty pounds of Atlantic salmon';
      
      mockUseSpeechRecognition.mockReturnValue({
        transcript: testTranscript,
        listening: true,
        resetTranscript: vi.fn(),
        browserSupportsSpeechRecognition: true
      });

      render(
        <VoiceInput 
          onTranscriptionComplete={mockOnTranscriptionComplete}
          onError={mockOnError}
        />
      );

      expect(screen.getByText('Transcript:')).toBeInTheDocument();
      expect(screen.getByText(testTranscript)).toBeInTheDocument();
    });
  });

  describe('Voice Processing', () => {
    beforeEach(() => {
      const { processVoiceInput } = require('@/lib/ai');
      processVoiceInput.mockClear();
    });

    it('processes voice input when recording stops', async () => {
      const { processVoiceInput } = require('@/lib/ai');
      const testTranscript = 'Add fifty pounds of Atlantic salmon batch SAL-001-2024';
      
      processVoiceInput.mockResolvedValue({
        product: 'Atlantic Salmon',
        quantity: 50,
        category: 'Fresh Fish'
      });

      mockUseSpeechRecognition.mockReturnValue({
        transcript: testTranscript,
        listening: false,
        resetTranscript: vi.fn(),
        browserSupportsSpeechRecognition: true
      });

      render(
        <VoiceInput 
          onTranscriptionComplete={mockOnTranscriptionComplete}
          onError={mockOnError}
        />
      );

      // Wait for categories and select one
      await waitFor(() => {
        expect(screen.getByText('Fresh Fish')).toBeInTheDocument();
      });

      const categorySelect = screen.getByText('Select category');
      fireEvent.click(categorySelect);
      const freshFishOption = screen.getByText('Fresh Fish');
      fireEvent.click(freshFishOption);

      // Simulate stopping recording with transcript
      const stopButton = screen.getByText('Start Recording');
      fireEvent.click(stopButton);

      await waitFor(() => {
        expect(processVoiceInput).toHaveBeenCalledWith(
          testTranscript,
          expect.objectContaining({
            model: 'gpt-3.5-turbo',
            timeout: 8000,
            retryAttempts: 1
          })
        );
      });
    });

    it('calls onTranscriptionComplete with processed data', async () => {
      const { processVoiceInput } = require('@/lib/ai');
      const testTranscript = 'Add fifty pounds of Atlantic salmon';
      
      processVoiceInput.mockResolvedValue({
        product: 'Atlantic Salmon',
        quantity: 50,
        category: 'Fresh Fish'
      });

      mockUseSpeechRecognition.mockReturnValue({
        transcript: testTranscript,
        listening: false,
        resetTranscript: vi.fn(),
        browserSupportsSpeechRecognition: true
      });

      render(
        <VoiceInput 
          onTranscriptionComplete={mockOnTranscriptionComplete}
          onError={mockOnError}
        />
      );

      // Setup and trigger processing
      await waitFor(() => {
        expect(screen.getByText('Fresh Fish')).toBeInTheDocument();
      });

      const categorySelect = screen.getByText('Select category');
      fireEvent.click(categorySelect);
      const freshFishOption = screen.getByText('Fresh Fish');
      fireEvent.click(freshFishOption);

      const recordButton = screen.getByText('Start Recording');
      fireEvent.click(recordButton);

      await waitFor(() => {
        expect(mockOnTranscriptionComplete).toHaveBeenCalledWith(
          expect.objectContaining({
            product: 'Atlantic Salmon',
            quantity: 50,
            category: 'Fresh Fish',
            timestamp: expect.stringMatching(/\d{4}-\d{2}-\d{2} \d{2}:\d{2}/)
          })
        );
      });
    });

    it('shows processing indicator during AI processing', async () => {
      const { processVoiceInput } = require('@/lib/ai');
      
      // Mock a delayed response
      processVoiceInput.mockImplementation(() => 
        new Promise(resolve => 
          setTimeout(() => resolve({ product: 'Test Product', quantity: 10 }), 1000)
        )
      );

      mockUseSpeechRecognition.mockReturnValue({
        transcript: 'Test transcript',
        listening: false,
        resetTranscript: vi.fn(),
        browserSupportsSpeechRecognition: true
      });

      render(
        <VoiceInput 
          onTranscriptionComplete={mockOnTranscriptionComplete}
          onError={mockOnError}
        />
      );

      // Setup category and trigger processing
      await waitFor(() => {
        expect(screen.getByText('Fresh Fish')).toBeInTheDocument();
      });

      const categorySelect = screen.getByText('Select category');
      fireEvent.click(categorySelect);
      fireEvent.click(screen.getByText('Fresh Fish'));

      const recordButton = screen.getByText('Start Recording');
      fireEvent.click(recordButton);

      // Should show loading state
      await waitFor(() => {
        expect(screen.getByText('Start Recording')).toBeDisabled();
      });
    });

    it('handles processing errors gracefully', async () => {
      const { processVoiceInput } = require('@/lib/ai');
      
      processVoiceInput.mockRejectedValue(new Error('OpenAI service unavailable'));

      mockUseSpeechRecognition.mockReturnValue({
        transcript: 'Test transcript',
        listening: false,
        resetTranscript: vi.fn(),
        browserSupportsSpeechRecognition: true
      });

      render(
        <VoiceInput 
          onTranscriptionComplete={mockOnTranscriptionComplete}
          onError={mockOnError}
        />
      );

      await waitFor(() => {
        expect(screen.getByText('Fresh Fish')).toBeInTheDocument();
      });

      const categorySelect = screen.getByText('Select category');
      fireEvent.click(categorySelect);
      fireEvent.click(screen.getByText('Fresh Fish'));

      const recordButton = screen.getByText('Start Recording');
      fireEvent.click(recordButton);

      await waitFor(() => {
        expect(mockOnError).toHaveBeenCalledWith('OpenAI service unavailable');
      });

      // Should show fallback mode
      expect(screen.getByText(/Using basic voice processing/)).toBeInTheDocument();
    });

    it('shows fallback mode when AI processing returns empty results', async () => {
      const { processVoiceInput } = require('@/lib/ai');
      
      processVoiceInput.mockResolvedValue({});

      mockUseSpeechRecognition.mockReturnValue({
        transcript: 'Unclear audio input',
        listening: false,
        resetTranscript: vi.fn(),
        browserSupportsSpeechRecognition: true
      });

      render(
        <VoiceInput 
          onTranscriptionComplete={mockOnTranscriptionComplete}
          onError={mockOnError}
        />
      );

      await waitFor(() => {
        expect(screen.getByText('Fresh Fish')).toBeInTheDocument();
      });

      const categorySelect = screen.getByText('Select category');
      fireEvent.click(categorySelect);
      fireEvent.click(screen.getByText('Fresh Fish'));

      const recordButton = screen.getByText('Start Recording');
      fireEvent.click(recordButton);

      await waitFor(() => {
        expect(screen.getByText(/Using basic voice processing/)).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling', () => {
    it('handles category loading errors with retry mechanism', async () => {
      const { getProductCategories } = require('@/lib/setupDatabase');
      
      // Mock failed attempts followed by success
      getProductCategories
        .mockRejectedValueOnce(new Error('Network error'))
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({ data: mockSupabaseData.categories, error: null });

      render(
        <VoiceInput 
          onTranscriptionComplete={mockOnTranscriptionComplete}
          onError={mockOnError}
        />
      );

      // Should eventually load categories after retries
      await waitFor(() => {
        expect(screen.getByText('Fresh Fish')).toBeInTheDocument();
      }, { timeout: 10000 });
    });

    it('shows error after maximum retry attempts for category loading', async () => {
      const { getProductCategories } = require('@/lib/setupDatabase');
      
      getProductCategories.mockRejectedValue(new Error('Persistent network error'));

      render(
        <VoiceInput 
          onTranscriptionComplete={mockOnTranscriptionComplete}
          onError={mockOnError}
        />
      );

      await waitFor(() => {
        expect(mockOnError).toHaveBeenCalledWith(
          'Failed to load product categories after multiple attempts'
        );
      }, { timeout: 15000 });
    });
  });

  describe('Processing Interruption', () => {
    it('allows interrupting long-running processing', async () => {
      const { processVoiceInput } = require('@/lib/ai');
      
      // Mock a very long processing time
      processVoiceInput.mockImplementation(() => 
        new Promise(resolve => setTimeout(resolve, 10000))
      );

      mockUseSpeechRecognition.mockReturnValue({
        transcript: 'Test transcript',
        listening: false,
        resetTranscript: vi.fn(),
        browserSupportsSpeechRecognition: true
      });

      render(
        <VoiceInput 
          onTranscriptionComplete={mockOnTranscriptionComplete}
          onError={mockOnError}
        />
      );

      // Setup and start processing
      await waitFor(() => {
        expect(screen.getByText('Fresh Fish')).toBeInTheDocument();
      });

      const categorySelect = screen.getByText('Select category');
      fireEvent.click(categorySelect);
      fireEvent.click(screen.getByText('Fresh Fish'));

      const recordButton = screen.getByText('Start Recording');
      fireEvent.click(recordButton);

      // Wait for interrupt option to become available
      await waitFor(() => {
        // The component should show some indication that interruption is possible
        // This would depend on the actual UI implementation
        expect(screen.getByText('Start Recording')).toBeDisabled();
      });
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels and roles', () => {
      render(
        <VoiceInput 
          onTranscriptionComplete={mockOnTranscriptionComplete}
          onError={mockOnError}
        />
      );

      const recordButton = screen.getByText('Start Recording');
      expect(recordButton).toBeInTheDocument();
      
      // Check that button is focusable
      recordButton.focus();
      expect(recordButton).toHaveFocus();
    });

    it('provides clear feedback for screen readers', async () => {
      mockUseSpeechRecognition.mockReturnValue({
        transcript: 'Test transcript',
        listening: true,
        resetTranscript: vi.fn(),
        browserSupportsSpeechRecognition: true
      });

      render(
        <VoiceInput 
          onTranscriptionComplete={mockOnTranscriptionComplete}
          onError={mockOnError}
        />
      );

      expect(screen.getByText('Listening...')).toBeInTheDocument();
      expect(screen.getByText('Stop Recording')).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    it('cleans up resources on unmount', () => {
      const resetTranscript = vi.fn();
      
      mockUseSpeechRecognition.mockReturnValue({
        transcript: '',
        listening: false,
        resetTranscript,
        browserSupportsSpeechRecognition: true
      });

      const { unmount } = render(
        <VoiceInput 
          onTranscriptionComplete={mockOnTranscriptionComplete}
          onError={mockOnError}
        />
      );

      unmount();

      expect(mockSpeechRecognition.stopListening).toHaveBeenCalled();
      expect(resetTranscript).toHaveBeenCalled();
    });

    it('handles rapid start/stop interactions', async () => {
      render(
        <VoiceInput 
          onTranscriptionComplete={mockOnTranscriptionComplete}
          onError={mockOnError}
        />
      );

      await waitFor(() => {
        expect(screen.getByText('Fresh Fish')).toBeInTheDocument();
      });

      const categorySelect = screen.getByText('Select category');
      fireEvent.click(categorySelect);
      fireEvent.click(screen.getByText('Fresh Fish'));

      const recordButton = screen.getByText('Start Recording');
      
      // Rapid clicks
      fireEvent.click(recordButton);
      fireEvent.click(recordButton);
      fireEvent.click(recordButton);

      // Should handle gracefully without errors
      expect(mockSpeechRecognition.startListening).toHaveBeenCalled();
    });
  });
});