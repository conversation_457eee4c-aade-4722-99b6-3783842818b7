/**
 * Comprehensive test suite for VoiceAssistant component
 * Tests voice processing, UI interactions, error handling, and performance
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { screen, waitFor, fireEvent, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '@/test/utils/test-utils';
import { VoiceAssistant } from '@/components/voice/VoiceAssistant';
import { TEST_CONFIG } from '@/test/test-config';
import { VOICE_TEST_SAMPLES, MockSpeechRecognition, MockMediaRecorder } from '@/test/mocks/voice-mocks';
import { mockOpenAI } from '@/test/mocks/openai-mocks';
import { createMockSupabaseClient } from '@/test/mocks/supabase-mocks';

// Mock dependencies
vi.mock('openai', () => ({ default: mockOpenAI }));
vi.mock('@/lib/supabase', () => ({
  supabase: createMockSupabaseClient()
}));

// Global mocks for browser APIs
let mockSpeechRecognition: MockSpeechRecognition;
let mockMediaRecorder: MockMediaRecorder;
let mockStream: MediaStream;

beforeEach(() => {
  // Reset all mocks
  vi.clearAllMocks();
  
  // Setup browser API mocks
  mockSpeechRecognition = new MockSpeechRecognition();
  mockMediaRecorder = new MockMediaRecorder(new MediaStream());
  mockStream = new MediaStream();

  // Mock navigator.mediaDevices.getUserMedia
  Object.defineProperty(global.navigator, 'mediaDevices', {
    value: {
      getUserMedia: vi.fn().mockResolvedValue(mockStream)
    },
    writable: true
  });

  // Mock Web Speech API
  (global as any).SpeechRecognition = vi.fn(() => mockSpeechRecognition);
  (global as any).webkitSpeechRecognition = (global as any).SpeechRecognition;
  
  // Mock MediaRecorder
  (global as any).MediaRecorder = vi.fn(() => mockMediaRecorder);
  
  // Mock performance for timing tests
  global.performance.mark = vi.fn();
  global.performance.measure = vi.fn();
  global.performance.getEntriesByName = vi.fn().mockReturnValue([
    { duration: 500 }
  ]);
});

afterEach(() => {
  vi.restoreAllMocks();
});

describe('VoiceAssistant Component', () => {
  
  describe('Initialization and Setup', () => {
    it('should render with initial state', () => {
      render(<VoiceAssistant />);
      
      expect(screen.getByRole('button', { name: /start voice input/i })).toBeInTheDocument();
      expect(screen.queryByText(/listening/i)).not.toBeInTheDocument();
      expect(screen.queryByText(/processing/i)).not.toBeInTheDocument();
    });

    it('should check for microphone permissions on mount', async () => {
      render(<VoiceAssistant />);
      
      await waitFor(() => {
        expect(global.navigator.mediaDevices.getUserMedia).toHaveBeenCalledWith({
          audio: true
        });
      });
    });

    it('should display error when microphone access is denied', async () => {
      // Mock permission denied
      global.navigator.mediaDevices.getUserMedia = vi.fn().mockRejectedValue(
        new Error('Permission denied')
      );

      render(<VoiceAssistant />);
      
      await waitFor(() => {
        expect(screen.getByText(/microphone access denied/i)).toBeInTheDocument();
      });
    });

    it('should show unsupported browser message when APIs are unavailable', () => {
      // Mock missing APIs
      delete (global as any).SpeechRecognition;
      delete (global as any).webkitSpeechRecognition;
      delete (global as any).MediaRecorder;

      render(<VoiceAssistant />);
      
      expect(screen.getByText(/voice input not supported/i)).toBeInTheDocument();
    });
  });

  describe('Voice Recording and Recognition', () => {
    it('should start recording when voice button is clicked', async () => {
      const user = userEvent.setup();
      render(<VoiceAssistant />);
      
      const startButton = screen.getByRole('button', { name: /start voice input/i });
      await user.click(startButton);
      
      await waitFor(() => {
        expect(screen.getByText(/listening/i)).toBeInTheDocument();
        expect(mockSpeechRecognition.start).toHaveBeenCalled();
      });
    });

    it('should stop recording when stop button is clicked', async () => {
      const user = userEvent.setup();
      render(<VoiceAssistant />);
      
      // Start recording
      const startButton = screen.getByRole('button', { name: /start voice input/i });
      await user.click(startButton);
      
      // Stop recording
      const stopButton = await screen.findByRole('button', { name: /stop voice input/i });
      await user.click(stopButton);
      
      expect(mockSpeechRecognition.stop).toHaveBeenCalled();
    });

    it('should handle successful speech recognition', async () => {
      const user = userEvent.setup();
      render(<VoiceAssistant />);
      
      // Set up recognition result
      mockSpeechRecognition.setRecognitionResult(
        VOICE_TEST_SAMPLES.highConfidence.salmonReceiving.transcript
      );
      
      const startButton = screen.getByRole('button', { name: /start voice input/i });
      await user.click(startButton);
      
      // Wait for recognition to process
      await waitFor(() => {
        expect(screen.getByText(/processing/i)).toBeInTheDocument();
      }, { timeout: TEST_CONFIG.timeouts.voice });
    });

    it('should handle speech recognition errors gracefully', async () => {
      const user = userEvent.setup();
      render(<VoiceAssistant />);
      
      // Set up recognition to fail
      mockSpeechRecognition.setShouldFail(true);
      
      const startButton = screen.getByRole('button', { name: /start voice input/i });
      await user.click(startButton);
      
      await waitFor(() => {
        expect(screen.getByText(/error/i)).toBeInTheDocument();
      });
    });

    it('should show transcription results', async () => {
      const user = userEvent.setup();
      render(<VoiceAssistant />);
      
      const expectedTranscript = VOICE_TEST_SAMPLES.highConfidence.salmonReceiving.transcript;
      mockSpeechRecognition.setRecognitionResult(expectedTranscript);
      
      const startButton = screen.getByRole('button', { name: /start voice input/i });
      await user.click(startButton);
      
      await waitFor(() => {
        expect(screen.getByText(expectedTranscript)).toBeInTheDocument();
      }, { timeout: TEST_CONFIG.timeouts.voice });
    });
  });

  describe('Voice Command Processing', () => {
    it('should process high confidence voice commands automatically', async () => {
      const user = userEvent.setup();
      const mockOnEventCreate = vi.fn();
      
      render(<VoiceAssistant onEventCreate={mockOnEventCreate} />);
      
      // Set up high confidence sample
      const sample = VOICE_TEST_SAMPLES.highConfidence.salmonReceiving;
      mockSpeechRecognition.setRecognitionResult(sample.transcript);
      
      const startButton = screen.getByRole('button', { name: /start voice input/i });
      await user.click(startButton);
      
      await waitFor(() => {
        expect(mockOnEventCreate).toHaveBeenCalledWith(
          expect.objectContaining({
            event_type: 'receiving',
            product_name: 'salmon',
            quantity: 25,
            unit: 'pounds'
          })
        );
      }, { timeout: TEST_CONFIG.timeouts.voice });
    });

    it('should require confirmation for medium confidence commands', async () => {
      const user = userEvent.setup();
      render(<VoiceAssistant />);
      
      // Set up medium confidence sample
      const sample = VOICE_TEST_SAMPLES.mediumConfidence.unclearQuantity;
      mockSpeechRecognition.setRecognitionResult(sample.transcript);
      
      const startButton = screen.getByRole('button', { name: /start voice input/i });
      await user.click(startButton);
      
      await waitFor(() => {
        expect(screen.getByText(/confirm/i)).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /confirm/i })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
      }, { timeout: TEST_CONFIG.timeouts.voice });
    });

    it('should require manual review for low confidence commands', async () => {
      const user = userEvent.setup();
      render(<VoiceAssistant />);
      
      // Set up low confidence sample
      const sample = VOICE_TEST_SAMPLES.lowConfidence.noiseBackground;
      mockSpeechRecognition.setRecognitionResult(sample.transcript);
      
      const startButton = screen.getByRole('button', { name: /start voice input/i });
      await user.click(startButton);
      
      await waitFor(() => {
        expect(screen.getByText(/needs review/i)).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /edit/i })).toBeInTheDocument();
      }, { timeout: TEST_CONFIG.timeouts.voice });
    });

    it('should display confidence scores', async () => {
      const user = userEvent.setup();
      render(<VoiceAssistant showConfidenceScore={true} />);
      
      const sample = VOICE_TEST_SAMPLES.highConfidence.salmonReceiving;
      mockSpeechRecognition.setRecognitionResult(sample.transcript);
      
      const startButton = screen.getByRole('button', { name: /start voice input/i });
      await user.click(startButton);
      
      await waitFor(() => {
        const confidenceText = screen.getByText(/confidence.*95%/i);
        expect(confidenceText).toBeInTheDocument();
      }, { timeout: TEST_CONFIG.timeouts.voice });
    });
  });

  describe('Audio Storage and Playback', () => {
    it('should record and store audio for voice commands', async () => {
      const user = userEvent.setup();
      render(<VoiceAssistant enableAudioStorage={true} />);
      
      const startButton = screen.getByRole('button', { name: /start voice input/i });
      await user.click(startButton);
      
      // Simulate audio recording
      act(() => {
        mockMediaRecorder.ondataavailable?.({
          data: new Blob(['audio data'], { type: 'audio/webm' })
        } as any);
        mockMediaRecorder.onstop?.();
      });
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /play recording/i })).toBeInTheDocument();
      });
    });

    it('should play back recorded audio', async () => {
      const user = userEvent.setup();
      render(<VoiceAssistant enableAudioStorage={true} />);
      
      // Mock audio playback
      const mockPlay = vi.fn();
      global.HTMLAudioElement.prototype.play = mockPlay;
      
      // Start and complete a recording
      const startButton = screen.getByRole('button', { name: /start voice input/i });
      await user.click(startButton);
      
      act(() => {
        mockMediaRecorder.ondataavailable?.({
          data: new Blob(['audio data'], { type: 'audio/webm' })
        } as any);
        mockMediaRecorder.onstop?.();
      });
      
      // Play the recording
      const playButton = await screen.findByRole('button', { name: /play recording/i });
      await user.click(playButton);
      
      expect(mockPlay).toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should handle OpenAI API errors', async () => {
      const user = userEvent.setup();
      
      // Mock API error
      mockOpenAI.setShouldFail(true, 'rate_limit');
      
      render(<VoiceAssistant />);
      
      mockSpeechRecognition.setRecognitionResult('Test transcript');
      
      const startButton = screen.getByRole('button', { name: /start voice input/i });
      await user.click(startButton);
      
      await waitFor(() => {
        expect(screen.getByText(/rate limit exceeded/i)).toBeInTheDocument();
      }, { timeout: TEST_CONFIG.timeouts.voice });
    });

    it('should handle network connectivity issues', async () => {
      const user = userEvent.setup();
      
      // Mock network error
      global.fetch = vi.fn().mockRejectedValue(new Error('Network error'));
      
      render(<VoiceAssistant />);
      
      mockSpeechRecognition.setRecognitionResult('Test transcript');
      
      const startButton = screen.getByRole('button', { name: /start voice input/i });
      await user.click(startButton);
      
      await waitFor(() => {
        expect(screen.getByText(/network error/i)).toBeInTheDocument();
      }, { timeout: TEST_CONFIG.timeouts.voice });
    });

    it('should provide retry functionality after errors', async () => {
      const user = userEvent.setup();
      
      // Mock initial failure then success
      let callCount = 0;
      mockOpenAI.chat.completions.create.mockImplementation(() => {
        callCount++;
        if (callCount === 1) {
          throw new Error('Temporary error');
        }
        return Promise.resolve({
          choices: [{ message: { content: 'Success' } }]
        });
      });
      
      render(<VoiceAssistant />);
      
      mockSpeechRecognition.setRecognitionResult('Test transcript');
      
      const startButton = screen.getByRole('button', { name: /start voice input/i });
      await user.click(startButton);
      
      // Wait for error and retry button
      const retryButton = await screen.findByRole('button', { name: /retry/i });
      await user.click(retryButton);
      
      await waitFor(() => {
        expect(screen.queryByText(/error/i)).not.toBeInTheDocument();
      });
    });
  });

  describe('Performance and Accessibility', () => {
    it('should complete voice processing within performance threshold', async () => {
      const user = userEvent.setup();
      render(<VoiceAssistant />);
      
      const startTime = performance.now();
      
      mockSpeechRecognition.setRecognitionResult(
        VOICE_TEST_SAMPLES.highConfidence.salmonReceiving.transcript
      );
      
      const startButton = screen.getByRole('button', { name: /start voice input/i });
      await user.click(startButton);
      
      await waitFor(() => {
        const endTime = performance.now();
        const duration = endTime - startTime;
        expect(duration).toBeLessThan(TEST_CONFIG.performance.voice.endToEnd);
      }, { timeout: TEST_CONFIG.timeouts.voice });
    });

    it('should be accessible via keyboard navigation', async () => {
      render(<VoiceAssistant />);
      
      const startButton = screen.getByRole('button', { name: /start voice input/i });
      
      // Test keyboard focus
      startButton.focus();
      expect(startButton).toHaveFocus();
      
      // Test Enter key activation
      fireEvent.keyDown(startButton, { key: 'Enter' });
      
      await waitFor(() => {
        expect(screen.getByText(/listening/i)).toBeInTheDocument();
      });
    });

    it('should provide appropriate ARIA labels and states', () => {
      render(<VoiceAssistant />);
      
      const startButton = screen.getByRole('button', { name: /start voice input/i });
      
      expect(startButton).toHaveAttribute('aria-label');
      expect(startButton).toHaveAttribute('aria-describedby');
    });

    it('should announce status changes to screen readers', async () => {
      const user = userEvent.setup();
      render(<VoiceAssistant />);
      
      const startButton = screen.getByRole('button', { name: /start voice input/i });
      await user.click(startButton);
      
      // Check for live region updates
      await waitFor(() => {
        const liveRegion = screen.getByRole('status');
        expect(liveRegion).toHaveTextContent(/listening/i);
      });
    });
  });

  describe('Integration with Form Components', () => {
    it('should integrate with form fields for voice input', async () => {
      const mockOnFieldUpdate = vi.fn();
      
      render(
        <VoiceAssistant 
          targetForm="inventory-form"
          onFieldUpdate={mockOnFieldUpdate}
        />
      );
      
      const sample = VOICE_TEST_SAMPLES.highConfidence.salmonReceiving;
      mockSpeechRecognition.setRecognitionResult(sample.transcript);
      
      const user = userEvent.setup();
      const startButton = screen.getByRole('button', { name: /start voice input/i });
      await user.click(startButton);
      
      await waitFor(() => {
        expect(mockOnFieldUpdate).toHaveBeenCalledWith({
          product_name: 'salmon',
          quantity: 25,
          unit: 'pounds',
          vendor_name: 'Ocean Fresh Seafoods'
        });
      }, { timeout: TEST_CONFIG.timeouts.voice });
    });

    it('should clear form fields when commanded', async () => {
      const mockOnFormClear = vi.fn();
      
      render(<VoiceAssistant onFormClear={mockOnFormClear} />);
      
      mockSpeechRecognition.setRecognitionResult('clear form');
      
      const user = userEvent.setup();
      const startButton = screen.getByRole('button', { name: /start voice input/i });
      await user.click(startButton);
      
      await waitFor(() => {
        expect(mockOnFormClear).toHaveBeenCalled();
      });
    });
  });

  describe('Multi-language Support', () => {
    it('should support different languages for speech recognition', async () => {
      render(<VoiceAssistant language="es-ES" />);
      
      const user = userEvent.setup();
      const startButton = screen.getByRole('button', { name: /start voice input/i });
      await user.click(startButton);
      
      expect(mockSpeechRecognition.lang).toBe('es-ES');
    });

    it('should handle accented speech recognition', async () => {
      render(<VoiceAssistant />);
      
      const sample = VOICE_TEST_SAMPLES.mediumConfidence.accentVariation;
      mockSpeechRecognition.setRecognitionResult(sample.transcript);
      
      const user = userEvent.setup();
      const startButton = screen.getByRole('button', { name: /start voice input/i });
      await user.click(startButton);
      
      await waitFor(() => {
        expect(screen.getByText(/confirm/i)).toBeInTheDocument();
      }, { timeout: TEST_CONFIG.timeouts.voice });
    });
  });

  describe('Memory Management', () => {
    it('should clean up resources on unmount', () => {
      const { unmount } = render(<VoiceAssistant />);
      
      unmount();
      
      // Verify cleanup calls
      expect(mockSpeechRecognition.stop).toHaveBeenCalled();
      expect(mockMediaRecorder.stop).toHaveBeenCalled();
    });

    it('should handle multiple rapid voice commands without memory leaks', async () => {
      const user = userEvent.setup();
      render(<VoiceAssistant />);
      
      // Simulate rapid commands
      for (let i = 0; i < 5; i++) {
        mockSpeechRecognition.setRecognitionResult(`Command ${i}`);
        
        const startButton = screen.getByRole('button', { name: /start voice input/i });
        await user.click(startButton);
        
        await waitFor(() => {
          expect(screen.getByText(/processing/i)).toBeInTheDocument();
        });
      }
      
      // No specific assertion needed - test passes if no memory errors occur
    });
  });
});

// Custom matchers for voice-specific testing
declare global {
  namespace Vi {
    interface Assertion {
      toHaveValidVoiceCommand(): void;
      toHaveConfidenceAbove(threshold: number): void;
    }
  }
}

expect.extend({
  toHaveValidVoiceCommand(received: any) {
    const requiredFields = ['action_type', 'confidence_score', 'raw_transcript'];
    const hasAllFields = requiredFields.every(field => field in received);
    
    return {
      message: () => 
        hasAllFields 
          ? `Expected ${JSON.stringify(received)} not to be a valid voice command`
          : `Expected ${JSON.stringify(received)} to have all required fields: ${requiredFields.join(', ')}`,
      pass: hasAllFields
    };
  },
  
  toHaveConfidenceAbove(received: any, threshold: number) {
    const confidence = received.confidence_score;
    const pass = typeof confidence === 'number' && confidence > threshold;
    
    return {
      message: () =>
        pass
          ? `Expected confidence ${confidence} not to be above ${threshold}`
          : `Expected confidence ${confidence} to be above ${threshold}`,
      pass
    };
  }
});