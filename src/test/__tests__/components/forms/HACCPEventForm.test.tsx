import React from 'react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@/test/utils/test-utils';
import { server } from '@/test/setup';
import { http, HttpResponse } from 'msw';
import HACCPEventForm from '@/components/forms/HACCPEventForm';
import { mockSupabaseData } from '@/test/mocks/data';

// Mock Supabase client
const mockSupabaseFrom = vi.fn();
const mockSupabaseInsert = vi.fn();
const mockSupabaseSelect = vi.fn();

vi.mock('@/lib/supabase', () => ({
  supabase: {
    from: mockSupabaseFrom
  }
}));

// Mock API functions
const mockCreateReceivingWithLot = vi.fn();
vi.mock('@/lib/api', () => ({
  createReceivingWithLot: mockCreateReceivingWithLot
}));

// Mock BatchNumberGenerator component
vi.mock('@/components/batch/BatchNumberGenerator', () => ({
  default: ({ onGenerate, disabled }: { onGenerate: (val: string) => void; disabled: boolean }) => (
    <div data-testid="batch-number-generator">
      <button 
        type="button" 
        onClick={() => onGenerate('TEST-001-2024')}
        disabled={disabled}
        data-testid="generate-batch-button"
      >
        Generate Batch Number
      </button>
    </div>
  )
}));

describe('HACCPEventForm Component', () => {
  const mockOnSuccess = vi.fn();
  const mockOnCancel = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup default Supabase mocks
    mockSupabaseSelect.mockResolvedValue({
      data: [],
      error: null
    });

    mockSupabaseInsert.mockResolvedValue({
      data: [{ id: 'new-event-id' }],
      error: null
    });

    mockSupabaseFrom.mockImplementation((table: string) => {
      if (table === 'Products') {
        return {
          select: vi.fn().mockReturnValue({
            order: vi.fn().mockResolvedValue({
              data: mockSupabaseData.products,
              error: null
            })
          })
        };
      }
      if (table === 'vendors') {
        return {
          select: vi.fn().mockReturnValue({
            order: vi.fn().mockResolvedValue({
              data: mockSupabaseData.vendors,
              error: null
            })
          })
        };
      }
      if (table === 'customers') {
        return {
          select: vi.fn().mockReturnValue({
            order: vi.fn().mockResolvedValue({
              data: mockSupabaseData.customers,
              error: null
            })
          })
        };
      }
      if (table === 'inventory_events') {
        return {
          insert: mockSupabaseInsert
        };
      }
      return {
        select: mockSupabaseSelect,
        insert: mockSupabaseInsert
      };
    });

    mockCreateReceivingWithLot.mockResolvedValue({
      success: true,
      data: {
        tlc: 'AUTO-GEN-001-2024',
        lot_id: 'lot-123',
        batch_number: 'AUTO-GEN-001-2024'
      }
    });
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('Form Rendering', () => {
    it('renders HACCP event form with all required fields', async () => {
      render(
        <HACCPEventForm 
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );

      // Check for main form elements
      expect(screen.getByLabelText('Event Type')).toBeInTheDocument();
      expect(screen.getByLabelText('Product')).toBeInTheDocument();
      expect(screen.getByLabelText('Quantity')).toBeInTheDocument();
      expect(screen.getByLabelText('Unit')).toBeInTheDocument();
      expect(screen.getByLabelText('Notes')).toBeInTheDocument();

      // Check for buttons
      expect(screen.getByText('Save Event')).toBeInTheDocument();
      expect(screen.getByText('Cancel')).toBeInTheDocument();

      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText('Atlantic Salmon')).toBeInTheDocument();
      });
    });

    it('loads products, vendors, and customers on mount', async () => {
      render(
        <HACCPEventForm 
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );

      await waitFor(() => {
        // Products should be loaded
        expect(screen.getByText('Atlantic Salmon')).toBeInTheDocument();
        expect(screen.getByText('Dungeness Crab')).toBeInTheDocument();
      });

      // Switch to receiving to see vendor dropdown
      const eventTypeSelect = screen.getByLabelText('Event Type');
      fireEvent.change(eventTypeSelect, { target: { value: 'receiving' } });

      await waitFor(() => {
        expect(screen.getByText('Alaska Fresh Seafood Co.')).toBeInTheDocument();
      });
    });

    it('shows conditional fields based on event type', async () => {
      render(
        <HACCPEventForm 
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );

      // Default to receiving - should show vendor fields
      expect(screen.getByLabelText('Vendor')).toBeInTheDocument();
      expect(screen.getByLabelText('Receiving Date')).toBeInTheDocument();
      expect(screen.getByLabelText('Batch Number')).toBeInTheDocument();
      expect(screen.getByLabelText('Condition')).toBeInTheDocument();

      // Switch to sale - should show customer fields
      const eventTypeSelect = screen.getByLabelText('Event Type');
      fireEvent.change(eventTypeSelect, { target: { value: 'sale' } });

      await waitFor(() => {
        expect(screen.getByLabelText('Customer')).toBeInTheDocument();
        expect(screen.getByLabelText('Unit Price')).toBeInTheDocument();
        expect(screen.queryByLabelText('Vendor')).not.toBeInTheDocument();
      });

      // Switch to disposal - should hide both vendor and customer fields
      fireEvent.change(eventTypeSelect, { target: { value: 'disposal' } });

      await waitFor(() => {
        expect(screen.queryByLabelText('Vendor')).not.toBeInTheDocument();
        expect(screen.queryByLabelText('Customer')).not.toBeInTheDocument();
      });
    });
  });

  describe('Event Type Handling', () => {
    it('handles receiving events with vendor and batch number generation', async () => {
      render(
        <HACCPEventForm 
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );

      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText('Atlantic Salmon')).toBeInTheDocument();
      });

      // Fill out receiving form
      const productSelect = screen.getByLabelText('Product');
      fireEvent.change(productSelect, { target: { value: 'prod-salmon-atlantic' } });

      const vendorSelect = screen.getByLabelText('Vendor');
      fireEvent.change(vendorSelect, { target: { value: 'vend-001' } });

      const quantityInput = screen.getByLabelText('Quantity');
      fireEvent.change(quantityInput, { target: { value: '50' } });

      const receivingDateInput = screen.getByLabelText('Receiving Date');
      fireEvent.change(receivingDateInput, { target: { value: '2024-01-15' } });

      // Submit form
      const submitButton = screen.getByText('Save Event');
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockCreateReceivingWithLot).toHaveBeenCalledWith({
          productName: 'Atlantic Salmon',
          quantity: 50,
          unit: 'lbs',
          vendorName: 'Alaska Fresh Seafood Co.',
          receivingDate: '2024-01-15',
          condition: undefined,
          notes: '',
          tlc: undefined
        });
      });

      await waitFor(() => {
        expect(mockOnSuccess).toHaveBeenCalled();
      });
    });

    it('handles sales events with customer selection', async () => {
      render(
        <HACCPEventForm 
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );

      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText('Atlantic Salmon')).toBeInTheDocument();
      });

      // Switch to sale event
      const eventTypeSelect = screen.getByLabelText('Event Type');
      fireEvent.change(eventTypeSelect, { target: { value: 'sale' } });

      // Fill out sale form
      const productSelect = screen.getByLabelText('Product');
      fireEvent.change(productSelect, { target: { value: 'prod-salmon-atlantic' } });

      const customerSelect = screen.getByLabelText('Customer');
      fireEvent.change(customerSelect, { target: { value: 'cust-001' } });

      const quantityInput = screen.getByLabelText('Quantity');
      fireEvent.change(quantityInput, { target: { value: '25' } });

      const unitPriceInput = screen.getByLabelText('Unit Price');
      fireEvent.change(unitPriceInput, { target: { value: '18.00' } });

      // Submit form
      const submitButton = screen.getByText('Save Event');
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockSupabaseInsert).toHaveBeenCalledWith(
          expect.objectContaining({
            event_type: 'sale',
            product_id: 'prod-salmon-atlantic',
            quantity: 25,
            unit_price: 18.00,
            total_amount: 450.00,
            customer_id: 'cust-001'
          })
        );
      });

      await waitFor(() => {
        expect(mockOnSuccess).toHaveBeenCalled();
      });
    });

    it('handles disposal and physical count events', async () => {
      render(
        <HACCPEventForm 
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );

      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText('Atlantic Salmon')).toBeInTheDocument();
      });

      // Test disposal event
      const eventTypeSelect = screen.getByLabelText('Event Type');
      fireEvent.change(eventTypeSelect, { target: { value: 'disposal' } });

      const productSelect = screen.getByLabelText('Product');
      fireEvent.change(productSelect, { target: { value: 'prod-salmon-atlantic' } });

      const quantityInput = screen.getByLabelText('Quantity');
      fireEvent.change(quantityInput, { target: { value: '5' } });

      const notesInput = screen.getByLabelText('Notes');
      fireEvent.change(notesInput, { target: { value: 'Expired product disposal' } });

      // Submit form
      const submitButton = screen.getByText('Save Event');
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockSupabaseInsert).toHaveBeenCalledWith(
          expect.objectContaining({
            event_type: 'disposal',
            product_id: 'prod-salmon-atlantic',
            quantity: 5,
            notes: 'Expired product disposal',
            metadata: expect.objectContaining({
              source: 'haccp-form',
              unit: 'lbs'
            })
          })
        );
      });

      await waitFor(() => {
        expect(mockOnSuccess).toHaveBeenCalled();
      });
    });
  });

  describe('Batch Number Generation', () => {
    it('supports automatic batch number generation', async () => {
      render(
        <HACCPEventForm 
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );

      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText('Atlantic Salmon')).toBeInTheDocument();
      });

      // Batch number mode should default to auto
      const batchModeSelect = screen.getByLabelText('Batch Number');
      expect(batchModeSelect).toHaveValue('auto');

      // Fill required fields and submit
      const productSelect = screen.getByLabelText('Product');
      fireEvent.change(productSelect, { target: { value: 'prod-salmon-atlantic' } });

      const vendorSelect = screen.getByLabelText('Vendor');
      fireEvent.change(vendorSelect, { target: { value: 'vend-001' } });

      const quantityInput = screen.getByLabelText('Quantity');
      fireEvent.change(quantityInput, { target: { value: '50' } });

      const submitButton = screen.getByText('Save Event');
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockCreateReceivingWithLot).toHaveBeenCalledWith(
          expect.objectContaining({
            tlc: undefined // Auto mode doesn't provide manual TLC
          })
        );
      });
    });

    it('supports manual batch number entry', async () => {
      render(
        <HACCPEventForm 
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );

      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText('Atlantic Salmon')).toBeInTheDocument();
      });

      // Switch to manual batch number mode
      const batchModeSelect = screen.getByLabelText('Batch Number');
      fireEvent.change(batchModeSelect, { target: { value: 'manual' } });

      // Manual batch input should appear
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Enter batch number (TLC)')).toBeInTheDocument();
      });

      // Enter manual batch number
      const manualBatchInput = screen.getByPlaceholderText('Enter batch number (TLC)');
      fireEvent.change(manualBatchInput, { target: { value: 'MANUAL-001-2024' } });

      // Fill other required fields
      const productSelect = screen.getByLabelText('Product');
      fireEvent.change(productSelect, { target: { value: 'prod-salmon-atlantic' } });

      const vendorSelect = screen.getByLabelText('Vendor');
      fireEvent.change(vendorSelect, { target: { value: 'vend-001' } });

      const quantityInput = screen.getByLabelText('Quantity');
      fireEvent.change(quantityInput, { target: { value: '50' } });

      const submitButton = screen.getByText('Save Event');
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockCreateReceivingWithLot).toHaveBeenCalledWith(
          expect.objectContaining({
            tlc: 'MANUAL-001-2024'
          })
        );
      });
    });

    it('integrates with batch number generator component', async () => {
      render(
        <HACCPEventForm 
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );

      // Wait for batch number generator to appear
      await waitFor(() => {
        expect(screen.getByTestId('batch-number-generator')).toBeInTheDocument();
      });

      // Click generate button
      const generateButton = screen.getByTestId('generate-batch-button');
      fireEvent.click(generateButton);

      // Should switch to manual mode and populate batch number
      await waitFor(() => {
        const batchModeSelect = screen.getByLabelText('Batch Number');
        expect(batchModeSelect).toHaveValue('manual');
        
        const manualBatchInput = screen.getByPlaceholderText('Enter batch number (TLC)');
        expect(manualBatchInput).toHaveValue('TEST-001-2024');
      });
    });
  });

  describe('Form Validation', () => {
    it('validates required fields', async () => {
      render(
        <HACCPEventForm 
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );

      // Try to submit without filling required fields
      const submitButton = screen.getByText('Save Event');
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Select a product')).toBeInTheDocument();
        expect(screen.getByText('Enter a positive quantity')).toBeInTheDocument();
      });

      expect(mockOnSuccess).not.toHaveBeenCalled();
    });

    it('validates receiving-specific required fields', async () => {
      render(
        <HACCPEventForm 
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );

      // Fill basic fields but omit vendor for receiving
      await waitFor(() => {
        expect(screen.getByText('Atlantic Salmon')).toBeInTheDocument();
      });

      const productSelect = screen.getByLabelText('Product');
      fireEvent.change(productSelect, { target: { value: 'prod-salmon-atlantic' } });

      const quantityInput = screen.getByLabelText('Quantity');
      fireEvent.change(quantityInput, { target: { value: '50' } });

      // Submit without vendor
      const submitButton = screen.getByText('Save Event');
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Missing required fields for selected event')).toBeInTheDocument();
      });
    });

    it('validates manual batch number when required', async () => {
      render(
        <HACCPEventForm 
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );

      // Switch to manual batch mode without entering batch number
      const batchModeSelect = screen.getByLabelText('Batch Number');
      fireEvent.change(batchModeSelect, { target: { value: 'manual' } });

      // Fill other required fields
      await waitFor(() => {
        expect(screen.getByText('Atlantic Salmon')).toBeInTheDocument();
      });

      const productSelect = screen.getByLabelText('Product');
      fireEvent.change(productSelect, { target: { value: 'prod-salmon-atlantic' } });

      const vendorSelect = screen.getByLabelText('Vendor');
      fireEvent.change(vendorSelect, { target: { value: 'vend-001' } });

      const quantityInput = screen.getByLabelText('Quantity');
      fireEvent.change(quantityInput, { target: { value: '50' } });

      // Submit without manual batch number
      const submitButton = screen.getByText('Save Event');
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Batch number is required when using Manual entry')).toBeInTheDocument();
      });
    });

    it('validates numeric inputs', async () => {
      render(
        <HACCPEventForm 
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );

      const quantityInput = screen.getByLabelText('Quantity');
      fireEvent.change(quantityInput, { target: { value: '-5' } });

      const submitButton = screen.getByText('Save Event');
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Enter a positive quantity')).toBeInTheDocument();
      });
    });
  });

  describe('External Overrides', () => {
    it('respects event type override', () => {
      render(
        <HACCPEventForm 
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
          eventTypeOverride="disposal"
        />
      );

      const eventTypeSelect = screen.getByLabelText('Event Type');
      expect(eventTypeSelect).toHaveValue('disposal');
    });

    it('respects batch number mode override', () => {
      render(
        <HACCPEventForm 
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
          batchNumberModeOverride="manual"
        />
      );

      const batchModeSelect = screen.getByLabelText('Batch Number');
      expect(batchModeSelect).toHaveValue('manual');
    });

    it('respects manual batch number override', async () => {
      render(
        <HACCPEventForm 
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
          batchNumberModeOverride="manual"
          manualBatchNumberOverride="OVERRIDE-001-2024"
        />
      );

      await waitFor(() => {
        const manualBatchInput = screen.getByPlaceholderText('Enter batch number (TLC)');
        expect(manualBatchInput).toHaveValue('OVERRIDE-001-2024');
      });
    });

    it('respects date override', () => {
      render(
        <HACCPEventForm 
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
          dateOverride="2024-01-20"
        />
      );

      const receivingDateInput = screen.getByLabelText('Receiving Date');
      expect(receivingDateInput).toHaveValue('2024-01-20');
    });

    it('hides batch controls when useExternalBatchControls is true', () => {
      render(
        <HACCPEventForm 
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
          useExternalBatchControls={true}
        />
      );

      // Batch controls should not be visible
      expect(screen.queryByLabelText('Batch Number')).not.toBeInTheDocument();
      expect(screen.queryByTestId('batch-number-generator')).not.toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('handles API errors gracefully', async () => {
      mockCreateReceivingWithLot.mockRejectedValue(new Error('Database connection failed'));

      render(
        <HACCPEventForm 
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );

      // Fill and submit form
      await waitFor(() => {
        expect(screen.getByText('Atlantic Salmon')).toBeInTheDocument();
      });

      const productSelect = screen.getByLabelText('Product');
      fireEvent.change(productSelect, { target: { value: 'prod-salmon-atlantic' } });

      const vendorSelect = screen.getByLabelText('Vendor');
      fireEvent.change(vendorSelect, { target: { value: 'vend-001' } });

      const quantityInput = screen.getByLabelText('Quantity');
      fireEvent.change(quantityInput, { target: { value: '50' } });

      const submitButton = screen.getByText('Save Event');
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Database connection failed')).toBeInTheDocument();
      });

      expect(mockOnSuccess).not.toHaveBeenCalled();
    });

    it('falls back to inventory_events when traceability fails', async () => {
      // Mock traceability API failure
      mockCreateReceivingWithLot.mockRejectedValue(new Error('Traceability service unavailable'));

      render(
        <HACCPEventForm 
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );

      // Fill and submit form
      await waitFor(() => {
        expect(screen.getByText('Atlantic Salmon')).toBeInTheDocument();
      });

      const productSelect = screen.getByLabelText('Product');
      fireEvent.change(productSelect, { target: { value: 'prod-salmon-atlantic' } });

      const vendorSelect = screen.getByLabelText('Vendor');
      fireEvent.change(vendorSelect, { target: { value: 'vend-001' } });

      const quantityInput = screen.getByLabelText('Quantity');
      fireEvent.change(quantityInput, { target: { value: '50' } });

      const submitButton = screen.getByText('Save Event');
      fireEvent.click(submitButton);

      // Should fall back to inventory_events insert
      await waitFor(() => {
        expect(mockSupabaseInsert).toHaveBeenCalledWith(
          expect.objectContaining({
            event_type: 'receiving',
            product_id: 'prod-salmon-atlantic',
            quantity: 50,
            metadata: expect.objectContaining({
              source: 'haccp-form',
              batch_number_mode: 'auto',
              vendor_id: 'vend-001'
            })
          })
        );
      });

      await waitFor(() => {
        expect(mockOnSuccess).toHaveBeenCalled();
      });
    });

    it('shows loading state during submission', async () => {
      // Mock slow API response
      mockCreateReceivingWithLot.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({
          success: true,
          data: { tlc: 'TEST-001', lot_id: 'lot-123' }
        }), 1000))
      );

      render(
        <HACCPEventForm 
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );

      // Fill and submit form
      await waitFor(() => {
        expect(screen.getByText('Atlantic Salmon')).toBeInTheDocument();
      });

      const productSelect = screen.getByLabelText('Product');
      fireEvent.change(productSelect, { target: { value: 'prod-salmon-atlantic' } });

      const vendorSelect = screen.getByLabelText('Vendor');
      fireEvent.change(vendorSelect, { target: { value: 'vend-001' } });

      const quantityInput = screen.getByLabelText('Quantity');
      fireEvent.change(quantityInput, { target: { value: '50' } });

      const submitButton = screen.getByText('Save Event');
      fireEvent.click(submitButton);

      // Should show loading state
      expect(screen.getByText('Saving…')).toBeInTheDocument();
      expect(screen.getByText('Saving…')).toBeDisabled();
    });
  });

  describe('User Interaction', () => {
    it('calls onCancel when cancel button is clicked', () => {
      render(
        <HACCPEventForm 
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );

      const cancelButton = screen.getByText('Cancel');
      fireEvent.click(cancelButton);

      expect(mockOnCancel).toHaveBeenCalled();
    });

    it('updates form state when switching event types', async () => {
      render(
        <HACCPEventForm 
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );

      // Start with receiving (default)
      expect(screen.getByLabelText('Vendor')).toBeInTheDocument();

      // Switch to sale
      const eventTypeSelect = screen.getByLabelText('Event Type');
      fireEvent.change(eventTypeSelect, { target: { value: 'sale' } });

      // Vendor should disappear, customer should appear
      await waitFor(() => {
        expect(screen.queryByLabelText('Vendor')).not.toBeInTheDocument();
        expect(screen.getByLabelText('Customer')).toBeInTheDocument();
      });
    });

    it('maintains form data when switching between compatible fields', async () => {
      render(
        <HACCPEventForm 
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );

      // Fill common fields
      await waitFor(() => {
        expect(screen.getByText('Atlantic Salmon')).toBeInTheDocument();
      });

      const productSelect = screen.getByLabelText('Product');
      fireEvent.change(productSelect, { target: { value: 'prod-salmon-atlantic' } });

      const quantityInput = screen.getByLabelText('Quantity');
      fireEvent.change(quantityInput, { target: { value: '50' } });

      const notesInput = screen.getByLabelText('Notes');
      fireEvent.change(notesInput, { target: { value: 'Test notes' } });

      // Switch event type
      const eventTypeSelect = screen.getByLabelText('Event Type');
      fireEvent.change(eventTypeSelect, { target: { value: 'disposal' } });

      // Common fields should retain their values
      expect(productSelect).toHaveValue('prod-salmon-atlantic');
      expect(quantityInput).toHaveValue('50');
      expect(notesInput).toHaveValue('Test notes');
    });
  });

  describe('Accessibility', () => {
    it('has proper form labels and associations', () => {
      render(
        <HACCPEventForm 
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );

      // Check that form controls are properly labeled
      expect(screen.getByLabelText('Event Type')).toBeInTheDocument();
      expect(screen.getByLabelText('Product')).toBeInTheDocument();
      expect(screen.getByLabelText('Quantity')).toBeInTheDocument();
      expect(screen.getByLabelText('Unit')).toBeInTheDocument();

      // Check that all inputs have proper IDs matching their labels
      const eventTypeInput = screen.getByLabelText('Event Type');
      expect(eventTypeInput).toHaveAttribute('id', 'eventType');
    });

    it('provides clear error messages', async () => {
      render(
        <HACCPEventForm 
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );

      // Submit form to trigger validation errors
      const submitButton = screen.getByText('Save Event');
      fireEvent.click(submitButton);

      await waitFor(() => {
        const errorMessages = screen.getAllByText(/required|positive/);
        expect(errorMessages.length).toBeGreaterThan(0);
        
        // Error messages should be descriptive
        expect(screen.getByText('Select a product')).toBeInTheDocument();
        expect(screen.getByText('Enter a positive quantity')).toBeInTheDocument();
      });
    });

    it('supports keyboard navigation', () => {
      render(
        <HACCPEventForm 
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );

      const eventTypeSelect = screen.getByLabelText('Event Type');
      const productSelect = screen.getByLabelText('Product');
      const quantityInput = screen.getByLabelText('Quantity');

      // All form controls should be focusable
      eventTypeSelect.focus();
      expect(eventTypeSelect).toHaveFocus();

      productSelect.focus();
      expect(productSelect).toHaveFocus();

      quantityInput.focus();
      expect(quantityInput).toHaveFocus();
    });
  });
});