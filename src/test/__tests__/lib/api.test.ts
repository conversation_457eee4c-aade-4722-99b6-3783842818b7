import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { server } from '@/test/setup';
import { http, HttpResponse } from 'msw';
import {
  createVendor,
  updateVendor,
  createCustomer,
  updateCustomer,
  createProduct,
  updateProduct,
  createEvent,
  createReceivingWithLot,
  batchCreateProducts,
  batchCreateCustomers,
  uploadProductImage,
  deleteProductImage,
  getProductImageUrl
} from '@/lib/api';
import { createMockFile } from '@/test/utils/test-utils';

// Mock Supabase client
const mockSupabaseFrom = vi.fn();
const mockSupabaseAuth = vi.fn();
const mockSupabaseStorage = vi.fn();

vi.mock('@/lib/supabase', () => ({
  supabase: {
    from: mockSupabaseFrom,
    auth: mockSupabaseAuth,
    storage: mockSupabaseStorage
  }
}));

describe('API Layer Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('Vendor API', () => {
    describe('createVendor', () => {
      it('successfully creates a vendor', async () => {
        const mockVendorData = {
          name: 'Alaska Fresh Seafood',
          contactName: 'John Doe',
          email: '<EMAIL>',
          phone: '******-555-0123',
          address: '123 Harbor Way, Anchorage, AK',
          status: 'active',
          paymentTerms: 'NET30',
          creditLimit: 50000
        };

        const mockResponse = {
          id: 'vendor-123',
          ...mockVendorData,
          created_at: '2024-01-15T10:00:00Z',
          updated_at: '2024-01-15T10:00:00Z'
        };

        mockSupabaseFrom.mockReturnValue({
          insert: vi.fn().mockReturnValue({
            select: vi.fn().mockReturnValue({
              single: vi.fn().mockResolvedValue({
                data: mockResponse,
                error: null
              })
            })
          })
        });

        const result = await createVendor(mockVendorData);

        expect(result.success).toBe(true);
        expect(result.data).toEqual(mockResponse);
        expect(mockSupabaseFrom).toHaveBeenCalledWith('vendors');
      });

      it('handles Supabase errors gracefully', async () => {
        mockSupabaseFrom.mockReturnValue({
          insert: vi.fn().mockReturnValue({
            select: vi.fn().mockReturnValue({
              single: vi.fn().mockResolvedValue({
                data: null,
                error: {
                  message: 'Unique constraint violation',
                  details: 'Key (name) already exists',
                  hint: 'Use a different name',
                  code: '23505'
                }
              })
            })
          })
        });

        const result = await createVendor({ name: 'Duplicate Vendor' });

        expect(result.success).toBe(false);
        expect(result.error?.message).toBe('Unique constraint violation');
        expect(result.error?.code).toBe('23505');
      });

      it('handles unexpected errors', async () => {
        mockSupabaseFrom.mockImplementation(() => {
          throw new Error('Network connection failed');
        });

        const result = await createVendor({ name: 'Test Vendor' });

        expect(result.success).toBe(false);
        expect(result.error?.message).toBe('An unexpected error occurred');
        expect(result.error?.details).toBe('Network connection failed');
      });

      it('maps vendor fields correctly', async () => {
        const vendorData = {
          name: 'Test Vendor',
          contactName: 'Jane Smith',
          email: '<EMAIL>',
          metadata: { notes: 'Preferred supplier' }
        };

        const mockInsert = vi.fn().mockReturnValue({
          select: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: { id: 'vendor-123' },
              error: null
            })
          })
        });

        mockSupabaseFrom.mockReturnValue({
          insert: mockInsert
        });

        await createVendor(vendorData);

        expect(mockInsert).toHaveBeenCalledWith([
          expect.objectContaining({
            name: 'Test Vendor',
            contact_name: 'Jane Smith',
            email: '<EMAIL>',
            metadata: { notes: 'Preferred supplier' },
            created_at: expect.any(String),
            updated_at: expect.any(String)
          })
        ]);
      });
    });

    describe('updateVendor', () => {
      it('successfully updates a vendor', async () => {
        const vendorId = 'vendor-123';
        const updateData = {
          name: 'Updated Vendor Name',
          email: '<EMAIL>'
        };

        const mockResponse = {
          id: vendorId,
          ...updateData,
          updated_at: '2024-01-15T11:00:00Z'
        };

        mockSupabaseFrom.mockReturnValue({
          update: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              select: vi.fn().mockReturnValue({
                single: vi.fn().mockResolvedValue({
                  data: mockResponse,
                  error: null
                })
              })
            })
          })
        });

        const result = await updateVendor(vendorId, updateData);

        expect(result.success).toBe(true);
        expect(result.data).toEqual(mockResponse);
      });

      it('handles vendor not found error', async () => {
        mockSupabaseFrom.mockReturnValue({
          update: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              select: vi.fn().mockReturnValue({
                single: vi.fn().mockResolvedValue({
                  data: null,
                  error: {
                    message: 'No rows found',
                    code: 'PGRST116'
                  }
                })
              })
            })
          })
        });

        const result = await updateVendor('nonexistent-id', { name: 'Updated' });

        expect(result.success).toBe(false);
        expect(result.error?.message).toBe('No rows found');
      });
    });
  });

  describe('Customer API', () => {
    describe('createCustomer', () => {
      it('successfully creates a customer', async () => {
        const customerData = {
          name: 'Seaside Restaurant',
          contactName: 'Chef Mike',
          email: '<EMAIL>',
          channelType: 'restaurant',
          customerSource: 'referral'
        };

        const mockResponse = {
          id: 'customer-123',
          ...customerData,
          created_at: '2024-01-15T10:00:00Z'
        };

        mockSupabaseFrom.mockReturnValue({
          insert: vi.fn().mockReturnValue({
            select: vi.fn().mockReturnValue({
              single: vi.fn().mockResolvedValue({
                data: mockResponse,
                error: null
              })
            })
          })
        });

        const result = await createCustomer(customerData);

        expect(result.success).toBe(true);
        expect(result.data).toEqual(mockResponse);
        expect(mockSupabaseFrom).toHaveBeenCalledWith('customers');
      });

      it('maps customer fields correctly', async () => {
        const customerData = {
          name: 'Test Customer',
          channelType: 'retail',
          customerSource: 'website',
          paymentTerms: 'NET15'
        };

        const mockInsert = vi.fn().mockReturnValue({
          select: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: { id: 'customer-123' },
              error: null
            })
          })
        });

        mockSupabaseFrom.mockReturnValue({
          insert: mockInsert
        });

        await createCustomer(customerData);

        expect(mockInsert).toHaveBeenCalledWith([
          expect.objectContaining({
            name: 'Test Customer',
            channel_type: 'retail',
            customer_source: 'website',
            payment_terms: 'NET15'
          })
        ]);
      });
    });

    describe('updateCustomer', () => {
      it('successfully updates a customer', async () => {
        const customerId = 'customer-123';
        const updateData = {
          name: 'Updated Restaurant Name',
          creditLimit: 25000
        };

        mockSupabaseFrom.mockReturnValue({
          update: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              select: vi.fn().mockReturnValue({
                single: vi.fn().mockResolvedValue({
                  data: { id: customerId, ...updateData },
                  error: null
                })
              })
            })
          })
        });

        const result = await updateCustomer(customerId, updateData);

        expect(result.success).toBe(true);
        expect(result.data?.credit_limit).toBe(25000);
      });
    });
  });

  describe('Product API', () => {
    describe('createProduct', () => {
      it('successfully creates a product', async () => {
        const productData = {
          name: 'Atlantic Salmon',
          amount: 100,
          condition: 'Fresh',
          category: 'Fish',
          price: 18.50,
          supplier: 'Alaska Fresh',
          speciesDetails: 'Salmo salar'
        };

        const mockResponse = {
          id: 'product-123',
          ...productData,
          created_at: '2024-01-15T10:00:00Z'
        };

        mockSupabaseFrom.mockReturnValue({
          insert: vi.fn().mockReturnValue({
            select: vi.fn().mockReturnValue({
              single: vi.fn().mockResolvedValue({
                data: mockResponse,
                error: null
              })
            })
          })
        });

        const result = await createProduct(productData);

        expect(result.success).toBe(true);
        expect(result.data).toEqual(mockResponse);
        expect(mockSupabaseFrom).toHaveBeenCalledWith('Products');
      });

      it('handles image array correctly', async () => {
        const productData = {
          name: 'Test Product',
          image: 'products/image123.jpg'
        };

        const mockInsert = vi.fn().mockReturnValue({
          select: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: { id: 'product-123' },
              error: null
            })
          })
        });

        mockSupabaseFrom.mockReturnValue({
          insert: mockInsert
        });

        await createProduct(productData);

        expect(mockInsert).toHaveBeenCalledWith([
          expect.objectContaining({
            images: ['products/image123.jpg']
          })
        ]);
      });

      it('maps product fields correctly', async () => {
        const productData = {
          name: 'Test Product',
          otherCondition: 'Wild-caught',
          subCategory: 'Premium',
          supplierId: 'supplier-123',
          speciesDetails: 'Scientific name'
        };

        const mockInsert = vi.fn().mockReturnValue({
          select: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: { id: 'product-123' },
              error: null
            })
          })
        });

        mockSupabaseFrom.mockReturnValue({
          insert: mockInsert
        });

        await createProduct(productData);

        expect(mockInsert).toHaveBeenCalledWith([
          expect.objectContaining({
            other_condition: 'Wild-caught',
            sub_category: 'Premium',
            supplier_id: 'supplier-123',
            species_details: 'Scientific name'
          })
        ]);
      });
    });

    describe('updateProduct', () => {
      it('successfully updates a product', async () => {
        const productId = 'product-123';
        const updateData = {
          name: 'Updated Product Name',
          price: 22.00
        };

        mockSupabaseFrom.mockReturnValue({
          update: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              select: vi.fn().mockReturnValue({
                single: vi.fn().mockResolvedValue({
                  data: { id: productId, ...updateData },
                  error: null
                })
              })
            })
          })
        });

        const result = await updateProduct(productId, updateData);

        expect(result.success).toBe(true);
        expect(result.data?.price).toBe(22.00);
      });

      it('sets updated_at timestamp', async () => {
        const mockUpdate = vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            select: vi.fn().mockReturnValue({
              single: vi.fn().mockResolvedValue({
                data: { id: 'product-123' },
                error: null
              })
            })
          })
        });

        mockSupabaseFrom.mockReturnValue({
          update: mockUpdate
        });

        await updateProduct('product-123', { name: 'Updated' });

        expect(mockUpdate).toHaveBeenCalledWith(
          expect.objectContaining({
            updated_at: expect.any(String)
          })
        );
      });
    });
  });

  describe('Event API', () => {
    describe('createEvent', () => {
      it('successfully creates an event', async () => {
        const eventData = {
          product_id: 'product-123',
          vendor_id: 'vendor-123',
          quantity: 50,
          unit: 'lbs',
          temperature: 35.5,
          quality_status: 'A',
          notes: 'Fresh delivery',
          metadata: { batch: 'B001' }
        };

        const mockUser = { id: 'user-123' };
        mockSupabaseAuth.getUser = vi.fn().mockResolvedValue({
          data: { user: mockUser }
        });

        const mockResponse = {
          id: 'event-123',
          ...eventData,
          created_by: mockUser.id,
          created_at: '2024-01-15T10:00:00Z'
        };

        mockSupabaseFrom.mockReturnValue({
          insert: vi.fn().mockReturnValue({
            select: vi.fn().mockReturnValue({
              single: vi.fn().mockResolvedValue({
                data: mockResponse,
                error: null
              })
            })
          })
        });

        const result = await createEvent(eventData, 'receiving');

        expect(result.success).toBe(true);
        expect(result.data).toEqual(mockResponse);
        expect(mockSupabaseFrom).toHaveBeenCalledWith('events');
      });

      it('includes current user as created_by', async () => {
        const mockUser = { id: 'current-user-123' };
        mockSupabaseAuth.getUser = vi.fn().mockResolvedValue({
          data: { user: mockUser }
        });

        const mockInsert = vi.fn().mockReturnValue({
          select: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: { id: 'event-123' },
              error: null
            })
          })
        });

        mockSupabaseFrom.mockReturnValue({
          insert: mockInsert
        });

        await createEvent({ product_id: 'product-123' }, 'receiving');

        expect(mockInsert).toHaveBeenCalledWith([
          expect.objectContaining({
            created_by: 'current-user-123'
          })
        ]);
      });

      it('handles auth errors gracefully', async () => {
        mockSupabaseAuth.getUser = vi.fn().mockRejectedValue(
          new Error('Authentication failed')
        );

        const result = await createEvent({ product_id: 'product-123' }, 'receiving');

        expect(result.success).toBe(false);
        expect(result.error?.message).toBe('An unexpected error occurred');
      });
    });
  });

  describe('Traceability API', () => {
    describe('createReceivingWithLot', () => {
      it('successfully creates receiving with lot', async () => {
        const receivingData = {
          productName: 'Atlantic Salmon',
          quantity: 100,
          unit: 'lbs',
          vendorName: 'Alaska Fresh',
          receivingDate: '2024-01-15',
          condition: 'Excellent',
          notes: 'Fresh delivery'
        };

        // Mock product lookup
        mockSupabaseFrom.mockImplementation((table: string) => {
          if (table === 'Products') {
            return {
              select: vi.fn().mockReturnValue({
                eq: vi.fn().mockReturnValue({
                  limit: vi.fn().mockReturnValue({
                    maybeSingle: vi.fn().mockResolvedValue({
                      data: { id: 'product-123', name: 'Atlantic Salmon' },
                      error: null
                    })
                  })
                })
              })
            };
          }

          if (table === 'partners') {
            return {
              select: vi.fn().mockReturnValue({
                eq: vi.fn().mockReturnValue({
                  eq: vi.fn().mockReturnValue({
                    limit: vi.fn().mockReturnValue({
                      maybeSingle: vi.fn().mockResolvedValue({
                        data: null,
                        error: null
                      })
                    })
                  })
                })
              }),
              insert: vi.fn().mockReturnValue({
                select: vi.fn().mockReturnValue({
                  single: vi.fn().mockResolvedValue({
                    data: { id: 'partner-123' },
                    error: null
                  })
                })
              })
            };
          }

          if (table === 'traceability_events') {
            return {
              insert: vi.fn().mockReturnValue({
                select: vi.fn().mockReturnValue({
                  single: vi.fn().mockResolvedValue({
                    data: { id: 'event-123' },
                    error: null
                  })
                })
              })
            };
          }

          if (table === 'lots') {
            return {
              insert: vi.fn().mockReturnValue({
                select: vi.fn().mockReturnValue({
                  single: vi.fn().mockResolvedValue({
                    data: { id: 'lot-123', tlc: 'TLC-001-2024' },
                    error: null
                  })
                })
              })
            };
          }

          if (table === 'event_lots') {
            return {
              insert: vi.fn().mockReturnValue({
                select: vi.fn().mockReturnValue({
                  single: vi.fn().mockResolvedValue({
                    data: { id: 'event-lot-123' },
                    error: null
                  })
                })
              })
            };
          }

          return {};
        });

        const result = await createReceivingWithLot(receivingData);

        expect(result.success).toBe(true);
        expect(result.data).toEqual({
          event_id: 'event-123',
          lot_id: 'lot-123',
          tlc: 'TLC-001-2024'
        });
      });

      it('handles product not found error', async () => {
        mockSupabaseFrom.mockImplementation((table: string) => {
          if (table === 'Products') {
            return {
              select: vi.fn().mockReturnValue({
                eq: vi.fn().mockReturnValue({
                  limit: vi.fn().mockReturnValue({
                    maybeSingle: vi.fn().mockResolvedValue({
                      data: null,
                      error: null
                    })
                  })
                })
              })
            };
          }
          return {};
        });

        const result = await createReceivingWithLot({
          productName: 'Nonexistent Product',
          quantity: 100,
          vendorName: 'Test Vendor'
        });

        expect(result.success).toBe(false);
        expect(result.error?.message).toBe('Product not found: Nonexistent Product');
      });

      it('handles traceability table missing error', async () => {
        mockSupabaseFrom.mockImplementation((table: string) => {
          if (table === 'Products') {
            return {
              select: vi.fn().mockReturnValue({
                eq: vi.fn().mockReturnValue({
                  limit: vi.fn().mockReturnValue({
                    maybeSingle: vi.fn().mockResolvedValue({
                      data: { id: 'product-123' },
                      error: null
                    })
                  })
                })
              })
            };
          }

          if (table === 'partners') {
            const error = new Error('relation "partners" does not exist');
            (error as any).code = '42P01'; // PostgreSQL table not found error
            throw error;
          }

          return {};
        });

        const result = await createReceivingWithLot({
          productName: 'Test Product',
          quantity: 100,
          vendorName: 'Test Vendor'
        });

        expect(result.success).toBe(false);
        expect(result.error?.message).toContain('relation "partners" does not exist');
      });

      it('uses manual TLC when provided', async () => {
        const receivingData = {
          productName: 'Atlantic Salmon',
          quantity: 100,
          vendorName: 'Alaska Fresh',
          tlc: 'MANUAL-TLC-001'
        };

        let lotInsertData: any = null;

        mockSupabaseFrom.mockImplementation((table: string) => {
          if (table === 'Products') {
            return {
              select: vi.fn().mockReturnValue({
                eq: vi.fn().mockReturnValue({
                  limit: vi.fn().mockReturnValue({
                    maybeSingle: vi.fn().mockResolvedValue({
                      data: { id: 'product-123' },
                      error: null
                    })
                  })
                })
              })
            };
          }

          if (table === 'lots') {
            return {
              insert: vi.fn().mockImplementation((data) => {
                lotInsertData = data[0];
                return {
                  select: vi.fn().mockReturnValue({
                    single: vi.fn().mockResolvedValue({
                      data: { id: 'lot-123', tlc: 'MANUAL-TLC-001' },
                      error: null
                    })
                  })
                };
              })
            };
          }

          // Mock other required tables
          if (table === 'partners') {
            return {
              select: vi.fn().mockReturnValue({
                eq: vi.fn().mockReturnValue({
                  eq: vi.fn().mockReturnValue({
                    limit: vi.fn().mockReturnValue({
                      maybeSingle: vi.fn().mockResolvedValue({
                        data: { id: 'partner-123' },
                        error: null
                      })
                    })
                  })
                })
              })
            };
          }

          if (table === 'traceability_events') {
            return {
              insert: vi.fn().mockReturnValue({
                select: vi.fn().mockReturnValue({
                  single: vi.fn().mockResolvedValue({
                    data: { id: 'event-123' },
                    error: null
                  })
                })
              })
            };
          }

          if (table === 'event_lots') {
            return {
              insert: vi.fn().mockReturnValue({
                select: vi.fn().mockReturnValue({
                  single: vi.fn().mockResolvedValue({
                    data: { id: 'event-lot-123' },
                    error: null
                  })
                })
              })
            };
          }

          return {};
        });

        const result = await createReceivingWithLot(receivingData);

        expect(result.success).toBe(true);
        expect(lotInsertData.tlc).toBe('MANUAL-TLC-001');
      });
    });
  });

  describe('Batch Operations', () => {
    describe('batchCreateProducts', () => {
      it('processes products in batches', async () => {
        const products = Array.from({ length: 25 }, (_, i) => ({
          name: `Product ${i}`,
          vendor: 'Test Vendor',
          condition: 'Fresh'
        }));

        let batchCount = 0;
        mockSupabaseFrom.mockReturnValue({
          insert: vi.fn().mockImplementation((data) => {
            batchCount++;
            return {
              select: vi.fn().mockResolvedValue({
                data: data.map((product: any, index: number) => ({
                  id: `product-${batchCount}-${index}`,
                  ...product
                })),
                error: null
              })
            };
          })
        });

        const results = await batchCreateProducts(products);

        // Should process in batches of 10
        expect(batchCount).toBe(3); // 25 products / 10 per batch = 3 batches
        expect(results).toHaveLength(25);
        expect(mockSupabaseFrom).toHaveBeenCalledWith('Products');
      });

      it('continues processing when a batch fails', async () => {
        const products = Array.from({ length: 15 }, (_, i) => ({
          name: `Product ${i}`,
          vendor: 'Test Vendor',
          condition: 'Fresh'
        }));

        let batchCount = 0;
        mockSupabaseFrom.mockReturnValue({
          insert: vi.fn().mockImplementation(() => {
            batchCount++;
            if (batchCount === 1) {
              // First batch fails
              return {
                select: vi.fn().mockResolvedValue({
                  data: null,
                  error: new Error('Database error')
                })
              };
            }
            // Other batches succeed
            return {
              select: vi.fn().mockResolvedValue({
                data: Array.from({ length: 5 }, (_, i) => ({
                  id: `product-${batchCount}-${i}`
                })),
                error: null
              })
            };
          })
        });

        const results = await batchCreateProducts(products);

        // Should process all batches despite first failure
        expect(batchCount).toBe(2); // 15 products / 10 per batch = 2 batches
        expect(results).toHaveLength(5); // Only successful batch results
      });

      it('handles timeout errors', async () => {
        const products = [{ name: 'Test Product', vendor: 'Test', condition: 'Fresh' }];

        mockSupabaseFrom.mockReturnValue({
          insert: vi.fn().mockImplementation(() => {
            return {
              select: vi.fn().mockImplementation(() => 
                new Promise((_, reject) => 
                  setTimeout(() => reject(new Error('Request timed out')), 20000)
                )
              )
            };
          })
        });

        const results = await batchCreateProducts(products);

        // Should handle timeout gracefully
        expect(results).toHaveLength(0);
      });
    });

    describe('batchCreateCustomers', () => {
      it('processes customers in batches with proper field mapping', async () => {
        const customers = [
          {
            name: 'Customer 1',
            contactName: 'John Doe',
            channelType: 'restaurant',
            customerSource: 'referral'
          },
          {
            name: 'Customer 2',
            contactName: 'Jane Smith',
            channelType: 'retail',
            customerSource: 'website'
          }
        ];

        let insertedData: any = null;
        mockSupabaseFrom.mockReturnValue({
          insert: vi.fn().mockImplementation((data) => {
            insertedData = data;
            return {
              select: vi.fn().mockResolvedValue({
                data: data.map((customer: any, index: number) => ({
                  id: `customer-${index}`,
                  ...customer
                })),
                error: null
              })
            };
          })
        });

        const results = await batchCreateCustomers(customers);

        expect(results).toHaveLength(2);
        expect(insertedData[0]).toEqual(
          expect.objectContaining({
            name: 'Customer 1',
            contact_name: 'John Doe',
            channel_type: 'restaurant',
            customer_source: 'referral',
            status: 'active', // Default status
            created_at: expect.any(String),
            updated_at: expect.any(String)
          })
        );
      });
    });
  });

  describe('Image Management', () => {
    describe('uploadProductImage', () => {
      it('successfully uploads an image', async () => {
        const mockFile = createMockFile('test-image.jpg', 'fake image data', 'image/jpeg');
        const expectedPath = expect.stringMatching(/^products\/\d+\.jpg$/);

        mockSupabaseStorage.from = vi.fn().mockReturnValue({
          upload: vi.fn().mockResolvedValue({
            data: { path: 'products/123456.jpg' },
            error: null
          })
        });

        const result = await uploadProductImage(mockFile);

        expect(result).toMatch(expectedPath);
        expect(mockSupabaseStorage.from).toHaveBeenCalledWith('inventory-images');
      });

      it('handles upload errors', async () => {
        const mockFile = createMockFile('test-image.jpg', 'fake image data');

        mockSupabaseStorage.from = vi.fn().mockReturnValue({
          upload: vi.fn().mockResolvedValue({
            data: null,
            error: {
              message: 'Storage quota exceeded'
            }
          })
        });

        await expect(uploadProductImage(mockFile)).rejects.toThrow('Storage quota exceeded');
      });

      it('generates unique file names', async () => {
        const mockFile = createMockFile('duplicate.jpg', 'fake data');

        const uploadedPaths: string[] = [];
        mockSupabaseStorage.from = vi.fn().mockReturnValue({
          upload: vi.fn().mockImplementation((path: string) => {
            uploadedPaths.push(path);
            return Promise.resolve({
              data: { path },
              error: null
            });
          })
        });

        const result1 = await uploadProductImage(mockFile);
        const result2 = await uploadProductImage(mockFile);

        expect(result1).not.toBe(result2);
        expect(uploadedPaths[0]).not.toBe(uploadedPaths[1]);
      });
    });

    describe('deleteProductImage', () => {
      it('successfully deletes an image by path', async () => {
        const imagePath = 'products/test-image.jpg';

        mockSupabaseStorage.from = vi.fn().mockReturnValue({
          remove: vi.fn().mockResolvedValue({
            data: { message: 'File deleted' },
            error: null
          })
        });

        await deleteProductImage(imagePath);

        expect(mockSupabaseStorage.from).toHaveBeenCalledWith('inventory-images');
      });

      it('extracts path from full URL', async () => {
        const imageUrl = 'https://supabase.co/storage/v1/object/public/inventory-images/products/test.jpg';
        let removedPaths: string[] = [];

        mockSupabaseStorage.from = vi.fn().mockReturnValue({
          remove: vi.fn().mockImplementation((paths: string[]) => {
            removedPaths = paths;
            return Promise.resolve({ data: {}, error: null });
          })
        });

        await deleteProductImage(imageUrl);

        expect(removedPaths[0]).toBe('products/test.jpg');
      });

      it('handles deletion errors', async () => {
        mockSupabaseStorage.from = vi.fn().mockReturnValue({
          remove: vi.fn().mockResolvedValue({
            data: null,
            error: {
              message: 'File not found'
            }
          })
        });

        await expect(deleteProductImage('nonexistent.jpg')).rejects.toThrow('File not found');
      });
    });

    describe('getProductImageUrl', () => {
      it('returns existing URL with token unchanged', async () => {
        const existingUrl = 'https://supabase.co/storage/v1/object/sign/bucket/file.jpg?token=abc123';

        const result = await getProductImageUrl(existingUrl);

        expect(result).toBe(existingUrl);
        expect(mockSupabaseStorage.from).not.toHaveBeenCalled();
      });

      it('generates signed URL for path', async () => {
        const imagePath = 'products/test-image.jpg';
        const signedUrl = 'https://supabase.co/storage/v1/object/sign/inventory-images/products/test-image.jpg?token=xyz789';

        mockSupabaseStorage.from = vi.fn().mockReturnValue({
          createSignedUrl: vi.fn().mockResolvedValue({
            data: { signedUrl },
            error: null
          })
        });

        const result = await getProductImageUrl(imagePath);

        expect(result).toBe(signedUrl);
        expect(mockSupabaseStorage.from).toHaveBeenCalledWith('inventory-images');
      });

      it('falls back to public URL on signed URL failure', async () => {
        const imagePath = 'products/test-image.jpg';
        const publicUrl = 'https://supabase.co/storage/v1/object/public/inventory-images/products/test-image.jpg';

        mockSupabaseStorage.from = vi.fn().mockReturnValue({
          createSignedUrl: vi.fn().mockResolvedValue({
            data: null,
            error: { message: 'Could not create signed URL' }
          }),
          getPublicUrl: vi.fn().mockReturnValue({
            data: { publicUrl }
          })
        });

        const result = await getProductImageUrl(imagePath);

        expect(result).toBe(publicUrl);
      });

      it('handles paths with leading slashes', async () => {
        const imagePath = '/products/test-image.jpg';
        const signedUrl = 'https://supabase.co/signed-url';

        let requestedPath: string = '';
        mockSupabaseStorage.from = vi.fn().mockReturnValue({
          createSignedUrl: vi.fn().mockImplementation((path: string) => {
            requestedPath = path;
            return Promise.resolve({
              data: { signedUrl },
              error: null
            });
          })
        });

        await getProductImageUrl(imagePath);

        expect(requestedPath).toBe('products/test-image.jpg');
      });
    });
  });

  describe('Error Handling', () => {
    it('handles network timeouts consistently', async () => {
      mockSupabaseFrom.mockImplementation(() => {
        throw new Error('Request timed out');
      });

      const vendorResult = await createVendor({ name: 'Test' });
      const customerResult = await createCustomer({ name: 'Test' });
      const productResult = await createProduct({ name: 'Test' });

      expect(vendorResult.success).toBe(false);
      expect(customerResult.success).toBe(false);
      expect(productResult.success).toBe(false);

      expect(vendorResult.error?.message).toBe('An unexpected error occurred');
      expect(customerResult.error?.message).toBe('An unexpected error occurred');
      expect(productResult.error?.message).toBe('An unexpected error occurred');
    });

    it('preserves Supabase error details', async () => {
      const mockError = {
        message: 'Invalid input',
        details: 'Column "invalid_field" does not exist',
        hint: 'Check your field names',
        code: '42703'
      };

      mockSupabaseFrom.mockReturnValue({
        insert: vi.fn().mockReturnValue({
          select: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: null,
              error: mockError
            })
          })
        })
      });

      const result = await createVendor({ name: 'Test' });

      expect(result.success).toBe(false);
      expect(result.error).toEqual(mockError);
    });

    it('handles malformed responses gracefully', async () => {
      mockSupabaseFrom.mockReturnValue({
        insert: vi.fn().mockReturnValue({
          select: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue(null) // Malformed response
          })
        })
      });

      const result = await createVendor({ name: 'Test' });

      expect(result.success).toBe(false);
      expect(result.error?.message).toBe('An unexpected error occurred');
    });
  });

  describe('Field Mapping', () => {
    it('maps camelCase to snake_case consistently', async () => {
      const testData = {
        contactName: 'John Doe',
        paymentTerms: 'NET30',
        creditLimit: 50000,
        otherCondition: 'Wild-caught',
        subCategory: 'Premium',
        supplierId: 'supplier-123',
        speciesDetails: 'Scientific name',
        channelType: 'restaurant',
        customerSource: 'referral'
      };

      const mockInsert = vi.fn().mockReturnValue({
        select: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({
            data: { id: 'test-123' },
            error: null
          })
        })
      });

      mockSupabaseFrom.mockReturnValue({ insert: mockInsert });

      await createVendor(testData);
      await createCustomer(testData);
      await createProduct(testData);

      // Check vendor field mapping
      expect(mockInsert.mock.calls[0][0][0]).toEqual(
        expect.objectContaining({
          contact_name: 'John Doe',
          payment_terms: 'NET30',
          credit_limit: 50000
        })
      );

      // Check customer field mapping  
      expect(mockInsert.mock.calls[1][0][0]).toEqual(
        expect.objectContaining({
          contact_name: 'John Doe',
          channel_type: 'restaurant',
          customer_source: 'referral'
        })
      );

      // Check product field mapping
      expect(mockInsert.mock.calls[2][0][0]).toEqual(
        expect.objectContaining({
          other_condition: 'Wild-caught',
          sub_category: 'Premium',
          supplier_id: 'supplier-123',
          species_details: 'Scientific name'
        })
      );
    });
  });
});