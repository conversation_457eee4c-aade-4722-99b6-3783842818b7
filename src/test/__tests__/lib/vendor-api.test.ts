/**
 * Comprehensive unit tests for vendor-api.ts
 * Tests all CRUD operations, metrics calculations, and API integrations
 */

import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { server } from '../../setup';

import { vendorAPI } from '../../../lib/vendor-api';
import { 
  vendorHandlers,
  vendorErrorHandlers,
  createVendorHandlersWithData
} from '../../mocks/vendor-handlers';
import {
  mockVendorDashboardSummary,
  mockVendorMetrics,
  mockVendorRatings,
  mockVendorCompliance,
  mockVendorPerformanceAlerts,
  mockVendorInteractions,
  mockVendor,
  generateVendorDashboardSummary,
  generateVendorMetrics,
  generateVendorAlert
} from '../../mocks/vendor-data';

// Mock Supabase client
const mockSupabase = {
  from: vi.fn(),
  rpc: vi.fn(),
  auth: {
    getUser: vi.fn().mockResolvedValue({
      data: { user: { id: 'test-user-id' } }
    })
  }
};

vi.mock('../../../lib/supabase', () => ({
  supabase: mockSupabase
}));

describe('Vendor API', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    server.use(...vendorHandlers);
    
    // Setup common mock chain
    const mockQuery = {
      select: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      gte: vi.fn().mockReturnThis(),
      lte: vi.fn().mockReturnThis(),
      in: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      limit: vi.fn().mockReturnThis(),
      range: vi.fn().mockReturnThis(),
      single: vi.fn(),
      then: vi.fn()
    };

    mockSupabase.from.mockReturnValue(mockQuery);
    mockSupabase.rpc.mockReturnValue({ data: null, error: null });
  });

  afterEach(() => {
    server.resetHandlers();
  });

  // ===== VENDOR INTERACTIONS API TESTS =====

  describe('Vendor Interactions API', () => {
    describe('create', () => {
      it('creates a new vendor interaction successfully', async () => {
        const newInteraction = {
          vendor_id: 'vendor-001',
          interaction_type: 'delivery' as const,
          order_date: '2025-08-14T09:00:00Z',
          expected_delivery_date: '2025-08-15T14:00:00Z',
          status: 'pending' as const
        };

        const mockResult = {
          data: { id: 'new-interaction', ...newInteraction },
          error: null
        };

        mockSupabase.from().single.mockResolvedValue(mockResult);

        const result = await vendorAPI.interactions.create(newInteraction);

        expect(mockSupabase.from).toHaveBeenCalledWith('vendor_interactions');
        expect(result).toEqual(mockResult.data);
      });

      it('throws error when creation fails', async () => {
        const newInteraction = {
          vendor_id: 'vendor-001',
          interaction_type: 'delivery' as const,
          status: 'pending' as const
        };

        const mockError = { message: 'Database error' };
        mockSupabase.from().single.mockResolvedValue({ data: null, error: mockError });

        await expect(vendorAPI.interactions.create(newInteraction))
          .rejects.toThrow('Failed to create vendor interaction: Database error');
      });
    });

    describe('getByVendor', () => {
      it('fetches interactions for a specific vendor', async () => {
        const mockResult = {
          data: mockVendorInteractions,
          error: null
        };

        mockSupabase.from().mockResolvedValue(mockResult);

        const result = await vendorAPI.interactions.getByVendor('vendor-001');

        expect(mockSupabase.from).toHaveBeenCalledWith('vendor_interactions');
        expect(result).toEqual(mockVendorInteractions);
      });

      it('applies filtering options correctly', async () => {
        const options = {
          limit: 10,
          offset: 0,
          status: 'completed',
          startDate: '2025-08-01T00:00:00Z',
          endDate: '2025-08-14T23:59:59Z'
        };

        const mockResult = { data: [], error: null };
        mockSupabase.from().mockResolvedValue(mockResult);

        await vendorAPI.interactions.getByVendor('vendor-001', options);

        const queryChain = mockSupabase.from();
        expect(queryChain.eq).toHaveBeenCalledWith('vendor_id', 'vendor-001');
        expect(queryChain.eq).toHaveBeenCalledWith('status', 'completed');
        expect(queryChain.gte).toHaveBeenCalledWith('created_at', options.startDate);
        expect(queryChain.lte).toHaveBeenCalledWith('created_at', options.endDate);
        expect(queryChain.limit).toHaveBeenCalledWith(10);
        expect(queryChain.range).toHaveBeenCalledWith(0, 9);
      });

      it('handles empty results', async () => {
        const mockResult = { data: null, error: null };
        mockSupabase.from().mockResolvedValue(mockResult);

        const result = await vendorAPI.interactions.getByVendor('vendor-001');

        expect(result).toEqual([]);
      });
    });

    describe('updateStatus', () => {
      it('updates interaction status successfully', async () => {
        const mockResult = { error: null };
        mockSupabase.from().mockResolvedValue(mockResult);

        await vendorAPI.interactions.updateStatus('interaction-001', 'completed');

        expect(mockSupabase.from).toHaveBeenCalledWith('vendor_interactions');
        expect(mockSupabase.from().update).toHaveBeenCalledWith(
          expect.objectContaining({
            status: 'completed',
            updated_at: expect.any(String)
          })
        );
      });
    });

    describe('getById', () => {
      it('fetches interaction with related vendor data', async () => {
        const mockInteraction = { ...mockVendorInteractions[0], vendors: mockVendor };
        const mockResult = { data: mockInteraction, error: null };
        
        mockSupabase.from().single.mockResolvedValue(mockResult);

        const result = await vendorAPI.interactions.getById('interaction-001');

        expect(mockSupabase.from).toHaveBeenCalledWith('vendor_interactions');
        expect(result).toEqual(mockInteraction);
      });

      it('returns null when interaction not found', async () => {
        const mockResult = { data: null, error: { code: 'PGRST116' } };
        mockSupabase.from().single.mockResolvedValue(mockResult);

        const result = await vendorAPI.interactions.getById('non-existent');

        expect(result).toBeNull();
      });
    });
  });

  // ===== VENDOR RATINGS API TESTS =====

  describe('Vendor Ratings API', () => {
    describe('create', () => {
      it('creates a new vendor rating successfully', async () => {
        const newRating = {
          vendor_interaction_id: 'interaction-001',
          vendor_id: 'vendor-001',
          quality_score: 8,
          delivery_timeliness_score: 9,
          overall_satisfaction: 8.5
        };

        const mockResult = {
          data: { id: 'new-rating', ...newRating },
          error: null
        };

        mockSupabase.from().single.mockResolvedValue(mockResult);

        const result = await vendorAPI.ratings.create(newRating);

        expect(mockSupabase.from).toHaveBeenCalledWith('vendor_ratings');
        expect(result).toEqual(mockResult.data);
      });
    });

    describe('getByVendor', () => {
      it('fetches ratings for a specific vendor', async () => {
        const mockResult = {
          data: mockVendorRatings,
          error: null
        };

        mockSupabase.from().mockResolvedValue(mockResult);

        const result = await vendorAPI.ratings.getByVendor('vendor-001');

        expect(result).toEqual(mockVendorRatings);
      });

      it('applies date filtering and limits', async () => {
        const options = {
          limit: 5,
          startDate: '2025-08-01T00:00:00Z',
          endDate: '2025-08-14T23:59:59Z'
        };

        const mockResult = { data: [], error: null };
        mockSupabase.from().mockResolvedValue(mockResult);

        await vendorAPI.ratings.getByVendor('vendor-001', options);

        const queryChain = mockSupabase.from();
        expect(queryChain.limit).toHaveBeenCalledWith(5);
        expect(queryChain.gte).toHaveBeenCalledWith('rating_date', options.startDate);
        expect(queryChain.lte).toHaveBeenCalledWith('rating_date', options.endDate);
      });
    });

    describe('getByInteraction', () => {
      it('fetches rating by interaction ID', async () => {
        const mockResult = {
          data: mockVendorRatings[0],
          error: null
        };

        mockSupabase.from().single.mockResolvedValue(mockResult);

        const result = await vendorAPI.ratings.getByInteraction('interaction-001');

        expect(result).toEqual(mockVendorRatings[0]);
      });

      it('returns null when rating not found', async () => {
        const mockResult = { data: null, error: { code: 'PGRST116' } };
        mockSupabase.from().single.mockResolvedValue(mockResult);

        const result = await vendorAPI.ratings.getByInteraction('non-existent');

        expect(result).toBeNull();
      });
    });

    describe('update', () => {
      it('updates rating successfully', async () => {
        const updates = { quality_score: 9, overall_satisfaction: 9.0 };
        const mockResult = { error: null };
        mockSupabase.from().mockResolvedValue(mockResult);

        await vendorAPI.ratings.update('rating-001', updates);

        expect(mockSupabase.from).toHaveBeenCalledWith('vendor_ratings');
        expect(mockSupabase.from().update).toHaveBeenCalledWith(
          expect.objectContaining({
            ...updates,
            updated_at: expect.any(String)
          })
        );
      });
    });
  });

  // ===== VENDOR METRICS API TESTS =====

  describe('Vendor Metrics API', () => {
    describe('getCurrent', () => {
      it('fetches current metrics for a vendor', async () => {
        const mockResult = {
          data: mockVendorMetrics,
          error: null
        };

        mockSupabase.from().single.mockResolvedValue(mockResult);

        const result = await vendorAPI.metrics.getCurrent('vendor-001');

        expect(mockSupabase.from).toHaveBeenCalledWith('vendor_metrics');
        expect(result).toEqual(mockVendorMetrics);
      });

      it('uses custom period when provided', async () => {
        const mockResult = { data: null, error: { code: 'PGRST116' } };
        mockSupabase.from().single.mockResolvedValue(mockResult);

        await vendorAPI.metrics.getCurrent('vendor-001', 'last_90_days');

        const queryChain = mockSupabase.from();
        expect(queryChain.eq).toHaveBeenCalledWith('calculation_period', 'last_90_days');
      });

      it('returns null when no metrics found', async () => {
        const mockResult = { data: null, error: { code: 'PGRST116' } };
        mockSupabase.from().single.mockResolvedValue(mockResult);

        const result = await vendorAPI.metrics.getCurrent('vendor-001');

        expect(result).toBeNull();
      });
    });

    describe('getHistory', () => {
      it('fetches metrics history for a vendor', async () => {
        const mockHistory = [mockVendorMetrics];
        const mockResult = { data: mockHistory, error: null };
        
        mockSupabase.from().mockResolvedValue(mockResult);

        const result = await vendorAPI.metrics.getHistory('vendor-001');

        expect(result).toEqual(mockHistory);
        expect(mockSupabase.from().limit).toHaveBeenCalledWith(12);
      });

      it('applies custom limit and period', async () => {
        const mockResult = { data: [], error: null };
        mockSupabase.from().mockResolvedValue(mockResult);

        await vendorAPI.metrics.getHistory('vendor-001', 'last_60_days', 6);

        const queryChain = mockSupabase.from();
        expect(queryChain.eq).toHaveBeenCalledWith('calculation_period', 'last_60_days');
        expect(queryChain.limit).toHaveBeenCalledWith(6);
      });
    });

    describe('calculate', () => {
      it('triggers metrics calculation via RPC', async () => {
        const mockResult = { data: null, error: null };
        mockSupabase.rpc.mockResolvedValue(mockResult);

        await vendorAPI.metrics.calculate('vendor-001', 'last_30_days');

        expect(mockSupabase.rpc).toHaveBeenCalledWith('calculate_vendor_metrics', {
          p_vendor_id: 'vendor-001',
          p_calculation_period: 'last_30_days'
        });
      });

      it('handles calculation for all vendors when no ID provided', async () => {
        const mockResult = { data: null, error: null };
        mockSupabase.rpc.mockResolvedValue(mockResult);

        await vendorAPI.metrics.calculate();

        expect(mockSupabase.rpc).toHaveBeenCalledWith('calculate_vendor_metrics', {
          p_vendor_id: null,
          p_calculation_period: 'last_30_days'
        });
      });

      it('throws error when calculation fails', async () => {
        const mockResult = { data: null, error: { message: 'Calculation failed' } };
        mockSupabase.rpc.mockResolvedValue(mockResult);

        await expect(vendorAPI.metrics.calculate('vendor-001'))
          .rejects.toThrow('Failed to calculate metrics: Calculation failed');
      });
    });

    describe('getTopPerformers', () => {
      it('fetches top performing vendors', async () => {
        const mockResult = { data: [mockVendorMetrics], error: null };
        mockSupabase.from().mockResolvedValue(mockResult);

        const result = await vendorAPI.metrics.getTopPerformers();

        expect(result).toEqual([mockVendorMetrics]);
        expect(mockSupabase.from().order).toHaveBeenCalledWith('completion_rate', { ascending: false });
        expect(mockSupabase.from().limit).toHaveBeenCalledWith(10);
      });

      it('applies custom period and limit', async () => {
        const mockResult = { data: [], error: null };
        mockSupabase.from().mockResolvedValue(mockResult);

        await vendorAPI.metrics.getTopPerformers('last_60_days', 5);

        const queryChain = mockSupabase.from();
        expect(queryChain.eq).toHaveBeenCalledWith('calculation_period', 'last_60_days');
        expect(queryChain.limit).toHaveBeenCalledWith(5);
      });
    });
  });

  // ===== VENDOR COMPLIANCE API TESTS =====

  describe('Vendor Compliance API', () => {
    describe('create', () => {
      it('creates new compliance record successfully', async () => {
        const newCompliance = {
          vendor_id: 'vendor-001',
          haccp_certified: true,
          compliance_score: 95,
          compliance_status: 'compliant' as const
        };

        const mockResult = {
          data: { id: 'new-compliance', ...newCompliance },
          error: null
        };

        mockSupabase.from().single.mockResolvedValue(mockResult);

        const result = await vendorAPI.compliance.create(newCompliance);

        expect(result).toEqual(mockResult.data);
      });
    });

    describe('getLatest', () => {
      it('fetches latest compliance status', async () => {
        const mockResult = {
          data: mockVendorCompliance,
          error: null
        };

        mockSupabase.from().single.mockResolvedValue(mockResult);

        const result = await vendorAPI.compliance.getLatest('vendor-001');

        expect(result).toEqual(mockVendorCompliance);
        expect(mockSupabase.from().order).toHaveBeenCalledWith('assessed_date', { ascending: false });
      });
    });

    describe('getHistory', () => {
      it('fetches compliance history', async () => {
        const mockResult = { data: [mockVendorCompliance], error: null };
        mockSupabase.from().mockResolvedValue(mockResult);

        const result = await vendorAPI.compliance.getHistory('vendor-001');

        expect(result).toEqual([mockVendorCompliance]);
        expect(mockSupabase.from().limit).toHaveBeenCalledWith(10);
      });
    });

    describe('getExpiringCertifications', () => {
      it('fetches vendors with expiring HACCP certifications', async () => {
        const mockResult = { data: [mockVendorCompliance], error: null };
        mockSupabase.from().mockResolvedValue(mockResult);

        const result = await vendorAPI.compliance.getExpiringCertifications(30);

        expect(result).toEqual([mockVendorCompliance]);
        
        const queryChain = mockSupabase.from();
        expect(queryChain.eq).toHaveBeenCalledWith('haccp_certified', true);
      });
    });
  });

  // ===== VENDOR PERFORMANCE ALERTS API TESTS =====

  describe('Vendor Performance Alerts API', () => {
    describe('getActive', () => {
      it('fetches active alerts for all vendors', async () => {
        const activeAlerts = mockVendorPerformanceAlerts.filter(alert => alert.status === 'active');
        const mockResult = { data: activeAlerts, error: null };
        mockSupabase.from().mockResolvedValue(mockResult);

        const result = await vendorAPI.alerts.getActive();

        expect(result).toEqual(activeAlerts);
        expect(mockSupabase.from().eq).toHaveBeenCalledWith('status', 'active');
      });

      it('filters by vendor ID when provided', async () => {
        const mockResult = { data: [], error: null };
        mockSupabase.from().mockResolvedValue(mockResult);

        await vendorAPI.alerts.getActive('vendor-001');

        const queryChain = mockSupabase.from();
        expect(queryChain.eq).toHaveBeenCalledWith('vendor_id', 'vendor-001');
      });
    });

    describe('acknowledge', () => {
      it('acknowledges alert successfully', async () => {
        const mockResult = { error: null };
        mockSupabase.from().mockResolvedValue(mockResult);

        await vendorAPI.alerts.acknowledge('alert-001');

        expect(mockSupabase.from().update).toHaveBeenCalledWith(
          expect.objectContaining({
            status: 'acknowledged',
            acknowledged_by: 'test-user-id',
            acknowledged_at: expect.any(String)
          })
        );
      });
    });

    describe('resolve', () => {
      it('resolves alert with notes', async () => {
        const mockResult = { error: null };
        mockSupabase.from().mockResolvedValue(mockResult);

        await vendorAPI.alerts.resolve('alert-001', 'Issue fixed');

        expect(mockSupabase.from().update).toHaveBeenCalledWith(
          expect.objectContaining({
            status: 'resolved',
            resolved_by: 'test-user-id',
            resolved_at: expect.any(String),
            resolution_notes: 'Issue fixed'
          })
        );
      });
    });

    describe('checkPerformanceAlerts', () => {
      it('triggers performance alert checking via RPC', async () => {
        const mockResult = { data: null, error: null };
        mockSupabase.rpc.mockResolvedValue(mockResult);

        await vendorAPI.alerts.checkPerformanceAlerts('vendor-001');

        expect(mockSupabase.rpc).toHaveBeenCalledWith('check_vendor_performance_alerts', {
          p_vendor_id: 'vendor-001'
        });
      });
    });
  });

  // ===== VENDOR DASHBOARD API TESTS =====

  describe('Vendor Dashboard API', () => {
    describe('getSummary', () => {
      it('fetches vendor dashboard summary via RPC', async () => {
        const mockResult = { data: mockVendorDashboardSummary, error: null };
        mockSupabase.rpc.mockResolvedValue(mockResult);

        const result = await vendorAPI.dashboard.getSummary();

        expect(mockSupabase.rpc).toHaveBeenCalledWith('get_vendor_dashboard_summary');
        expect(result).toEqual(mockVendorDashboardSummary);
      });

      it('handles empty dashboard data', async () => {
        const mockResult = { data: null, error: null };
        mockSupabase.rpc.mockResolvedValue(mockResult);

        const result = await vendorAPI.dashboard.getSummary();

        expect(result).toEqual([]);
      });
    });

    describe('getVendorDetails', () => {
      it('fetches comprehensive vendor details', async () => {
        // Mock individual API calls
        const vendorResult = { data: mockVendor, error: null };
        const metricsResult = { data: mockVendorMetrics, error: null };
        const ratingsResult = { data: mockVendorRatings, error: null };
        const complianceResult = { data: mockVendorCompliance, error: null };
        const alertsResult = { data: [], error: null };
        const interactionsResult = { data: mockVendorInteractions, error: null };

        mockSupabase.from()
          .single.mockResolvedValueOnce(vendorResult)
          .single.mockResolvedValueOnce(metricsResult)
          .single.mockResolvedValueOnce(complianceResult);
        
        mockSupabase.from()
          .mockResolvedValueOnce(interactionsResult)
          .mockResolvedValueOnce(ratingsResult)
          .mockResolvedValueOnce(alertsResult);

        const result = await vendorAPI.dashboard.getVendorDetails('vendor-001');

        expect(result).toEqual({
          vendor: mockVendor,
          currentMetrics: mockVendorMetrics,
          recentInteractions: mockVendorInteractions,
          recentRatings: mockVendorRatings,
          complianceStatus: mockVendorCompliance,
          activeAlerts: []
        });
      });
    });

    describe('refreshSummary', () => {
      it('refreshes dashboard materialized view', async () => {
        const mockResult = { data: null, error: null };
        mockSupabase.rpc.mockResolvedValue(mockResult);

        await vendorAPI.dashboard.refreshSummary();

        expect(mockSupabase.rpc).toHaveBeenCalledWith('refresh_vendor_dashboard_summary');
      });
    });

    describe('compareVendors', () => {
      it('fetches comparison data for multiple vendors', async () => {
        const vendorIds = ['vendor-001', 'vendor-002'];
        const mockResult = { data: [mockVendorMetrics], error: null };
        mockSupabase.from().mockResolvedValue(mockResult);

        const result = await vendorAPI.dashboard.compareVendors(vendorIds);

        expect(result).toEqual([mockVendorMetrics]);
        
        const queryChain = mockSupabase.from();
        expect(queryChain.in).toHaveBeenCalledWith('vendor_id', vendorIds);
        expect(queryChain.eq).toHaveBeenCalledWith('calculation_period', 'last_30_days');
      });
    });
  });

  // ===== INVENTORY INTEGRATION TESTS =====

  describe('Vendor Inventory Integration', () => {
    describe('createFromInventoryEvent', () => {
      it('creates vendor interaction from inventory event', async () => {
        const eventData = {
          vendorId: 'vendor-001',
          eventType: 'receiving',
          productId: 'product-001',
          quantity: 100,
          unitPrice: 15.50,
          totalAmount: 1550.00,
          condition: 'excellent',
          temperature: 2.0,
          notes: 'Fresh salmon delivery',
          batchNumber: 'BATCH-001'
        };

        const mockResult = {
          data: { id: 'new-interaction', ...eventData },
          error: null
        };

        mockSupabase.from().single.mockResolvedValue(mockResult);

        const result = await vendorAPI.integration.createFromInventoryEvent(eventData);

        expect(mockSupabase.from).toHaveBeenCalledWith('vendor_interactions');
        expect(result.vendor_id).toBe(eventData.vendorId);
        expect(result.interaction_type).toBe('delivery');
      });

      it('calculates temperature compliance correctly', async () => {
        const eventData = {
          vendorId: 'vendor-001',
          eventType: 'receiving',
          productId: 'product-001',
          quantity: 100,
          temperature: 8.0 // Too warm for seafood
        };

        const mockResult = {
          data: { id: 'new-interaction', temperature_compliant: false },
          error: null
        };

        mockSupabase.from().single.mockResolvedValue(mockResult);

        await vendorAPI.integration.createFromInventoryEvent(eventData);

        // Should mark as non-compliant for temperature > 4°C
        expect(mockSupabase.from().insert).toHaveBeenCalledWith(
          expect.objectContaining({
            temperature_compliant: false
          })
        );
      });
    });
  });

  // ===== ERROR HANDLING TESTS =====

  describe('Error Handling', () => {
    it('handles Supabase errors gracefully', async () => {
      const mockError = { message: 'Connection timeout' };
      mockSupabase.from().mockResolvedValue({ data: null, error: mockError });

      await expect(vendorAPI.interactions.getByVendor('vendor-001'))
        .rejects.toThrow('Failed to fetch vendor interactions: Connection timeout');
    });

    it('handles RPC errors for metrics calculation', async () => {
      const mockError = { message: 'Function not found' };
      mockSupabase.rpc.mockResolvedValue({ data: null, error: mockError });

      await expect(vendorAPI.metrics.calculate('vendor-001'))
        .rejects.toThrow('Failed to calculate metrics: Function not found');
    });

    it('handles authentication errors', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({ 
        data: { user: null }, 
        error: { message: 'User not authenticated' }
      });

      const newInteraction = {
        vendor_id: 'vendor-001',
        interaction_type: 'delivery' as const,
        status: 'pending' as const
      };

      // Should still attempt to create but with undefined created_by
      const mockResult = { data: { id: 'new-interaction' }, error: null };
      mockSupabase.from().single.mockResolvedValue(mockResult);

      await vendorAPI.interactions.create(newInteraction);

      expect(mockSupabase.from().insert).toHaveBeenCalledWith(
        expect.objectContaining({
          created_by: undefined
        })
      );
    });
  });

  // ===== PERFORMANCE TESTS =====

  describe('Performance Considerations', () => {
    it('handles large result sets for interactions', async () => {
      const largeDataSet = Array.from({ length: 1000 }, (_, i) => ({
        ...mockVendorInteractions[0],
        id: `interaction-${i}`
      }));

      const mockResult = { data: largeDataSet, error: null };
      mockSupabase.from().mockResolvedValue(mockResult);

      const result = await vendorAPI.interactions.getByVendor('vendor-001');

      expect(result).toHaveLength(1000);
      expect(result[0].id).toBe('interaction-0');
      expect(result[999].id).toBe('interaction-999');
    });

    it('uses pagination correctly for large datasets', async () => {
      const options = { limit: 50, offset: 100 };
      const mockResult = { data: [], error: null };
      mockSupabase.from().mockResolvedValue(mockResult);

      await vendorAPI.interactions.getByVendor('vendor-001', options);

      const queryChain = mockSupabase.from();
      expect(queryChain.limit).toHaveBeenCalledWith(50);
      expect(queryChain.range).toHaveBeenCalledWith(100, 149);
    });
  });

  // ===== DATA VALIDATION TESTS =====

  describe('Data Validation', () => {
    it('validates required fields for interactions', async () => {
      const invalidInteraction = {
        // Missing required vendor_id
        interaction_type: 'delivery' as const,
        status: 'pending' as const
      };

      const mockError = { message: 'Missing required field: vendor_id' };
      mockSupabase.from().single.mockResolvedValue({ data: null, error: mockError });

      await expect(vendorAPI.interactions.create(invalidInteraction as any))
        .rejects.toThrow('Failed to create vendor interaction: Missing required field: vendor_id');
    });

    it('validates score ranges for ratings', async () => {
      const invalidRating = {
        vendor_interaction_id: 'interaction-001',
        vendor_id: 'vendor-001',
        quality_score: 15, // Invalid: should be 1-10
        overall_satisfaction: 8.5
      };

      const mockError = { message: 'Score must be between 1 and 10' };
      mockSupabase.from().single.mockResolvedValue({ data: null, error: mockError });

      await expect(vendorAPI.ratings.create(invalidRating))
        .rejects.toThrow('Failed to create vendor rating: Score must be between 1 and 10');
    });
  });
});