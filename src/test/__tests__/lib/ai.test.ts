import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { server } from '@/test/setup';
import { http, HttpResponse } from 'msw';
import { processVoiceInput } from '@/lib/ai';
import { mockVoiceInputs } from '@/test/mocks/data';

describe('AI Voice Processing', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset any mocked implementations
    server.resetHandlers();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('processVoiceInput', () => {
    describe('Successful API Processing', () => {
      it('processes valid seafood inventory commands', async () => {
        // Mock successful API response
        server.use(
          http.post('/api/voice-process', async ({ request }) => {
            const body = await request.json() as any;
            
            return HttpResponse.json({
              product: 'Atlantic Salmon',
              quantity: 50,
              unit: 'lbs',
              category: 'Finfish',
              vendor: 'Ocean Fresh Ltd',
              metadata: {
                processing_method: 'openai_gpt',
                original_text: body.transcript,
                confidence_score: 0.95,
                model: 'gpt-3.5-turbo',
                processing_time_ms: 1250,
                seafood_database_match: true,
                voice_correction_applied: false
              }
            });
          })
        );

        const result = await processVoiceInput(
          'Add fifty pounds of Atlantic salmon from Ocean Fresh Ltd'
        );

        expect(result).toEqual({
          product: 'Atlantic Salmon',
          quantity: 50,
          unit: 'lbs',
          category: 'Finfish',
          vendor: 'Ocean Fresh Ltd',
          metadata: expect.objectContaining({
            processing_method: 'openai_gpt',
            confidence_score: 0.95,
            seafood_database_match: true
          })
        });
      });

      it('handles voice corrections for misheard terms', async () => {
        server.use(
          http.post('/api/voice-process', async ({ request }) => {
            const body = await request.json() as any;
            
            return HttpResponse.json({
              product: 'Dungeness Crab',
              quantity: 25,
              unit: 'lbs',
              category: 'Crustaceans',
              metadata: {
                processing_method: 'openai_gpt',
                original_text: body.transcript,
                confidence_score: 0.92,
                voice_correction_applied: true
              }
            });
          })
        );

        const result = await processVoiceInput(
          'Add twenty-five pounds of dangerous grab' // Common misheard term
        );

        expect(result.product).toBe('Dungeness Crab');
        expect(result.metadata?.voice_correction_applied).toBe(true);
      });

      it('processes complex inventory transactions', async () => {
        server.use(
          http.post('/api/voice-process', async ({ request }) => {
            const body = await request.json() as any;
            
            return HttpResponse.json({
              product: 'Pacific King Salmon',
              quantity: 100,
              unit: 'lbs',
              price: 18.50,
              vendor: 'Alaska Fresh Seafood Co.',
              origin: 'Alaska',
              storageTemp: '32-38°F',
              category: 'Fresh Fish',
              notes: 'Premium grade, wild-caught',
              metadata: {
                processing_method: 'openai_gpt',
                original_text: body.transcript,
                confidence_score: 0.98,
                model: 'gpt-4'
              }
            });
          })
        );

        const result = await processVoiceInput(
          'Received one hundred pounds of Pacific King Salmon from Alaska Fresh Seafood Co at eighteen fifty per pound wild caught premium grade keep refrigerated thirty-two to thirty-eight degrees'
        );

        expect(result).toEqual(expect.objectContaining({
          product: 'Pacific King Salmon',
          quantity: 100,
          unit: 'lbs',
          price: 18.50,
          vendor: 'Alaska Fresh Seafood Co.',
          origin: 'Alaska',
          storageTemp: '32-38°F',
          category: 'Fresh Fish',
          notes: 'Premium grade, wild-caught'
        }));
      });

      it('respects processing options and timeouts', async () => {
        const mockFetch = vi.fn();
        global.fetch = mockFetch;

        mockFetch.mockResolvedValue({
          ok: true,
          json: () => Promise.resolve({
            product: 'Test Product',
            metadata: { processing_method: 'openai_gpt', model: 'gpt-4' }
          })
        });

        await processVoiceInput('test transcript', {
          model: 'gpt-4',
          timeout: 5000,
          retryAttempts: 1
        });

        expect(mockFetch).toHaveBeenCalledWith('/api/voice-process', 
          expect.objectContaining({
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              transcript: 'test transcript',
              options: { model: 'gpt-4' }
            }),
            signal: expect.any(AbortSignal)
          })
        );
      });
    });

    describe('API Error Handling and Fallback', () => {
      it('falls back to local processing when API fails', async () => {
        server.use(
          http.post('/api/voice-process', () => {
            return new HttpResponse(null, { status: 500 });
          })
        );

        const result = await processVoiceInput(
          'Add fifty pounds of Atlantic salmon from Ocean Fresh'
        );

        expect(result.metadata?.processing_method).toBe('enhanced_local');
        expect(result.product).toBe('Atlantic Salmon');
        expect(result.quantity).toBe(50);
        expect(result.vendor).toBe('Ocean Fresh');
      });

      it('retries failed API calls with exponential backoff', async () => {
        let attemptCount = 0;
        
        server.use(
          http.post('/api/voice-process', () => {
            attemptCount++;
            if (attemptCount < 3) {
              return new HttpResponse(null, { status: 503 });
            }
            return HttpResponse.json({
              product: 'Test Product',
              metadata: { processing_method: 'openai_gpt' }
            });
          })
        );

        const startTime = Date.now();
        const result = await processVoiceInput('test transcript', {
          retryAttempts: 2
        });
        const endTime = Date.now();

        expect(attemptCount).toBe(3);
        expect(result.metadata?.processing_method).toBe('openai_gpt');
        // Should have some delay due to exponential backoff
        expect(endTime - startTime).toBeGreaterThan(1000);
      });

      it('handles network timeouts gracefully', async () => {
        server.use(
          http.post('/api/voice-process', () => {
            // Simulate a timeout by never responding
            return new Promise(() => {});
          })
        );

        const result = await processVoiceInput('test transcript', {
          timeout: 1000,
          retryAttempts: 0
        });

        // Should fall back to local processing
        expect(result.metadata?.processing_method).toBe('enhanced_local');
        expect(result.metadata?.error_reason).toBeTruthy();
      });

      it('handles malformed API responses', async () => {
        server.use(
          http.post('/api/voice-process', () => {
            return HttpResponse.text('Invalid JSON response');
          })
        );

        const result = await processVoiceInput('test transcript');
        
        expect(result.metadata?.processing_method).toBe('enhanced_local');
      });
    });

    describe('Local Processing Fallback', () => {
      beforeEach(() => {
        // Force all API calls to fail for local processing tests
        server.use(
          http.post('/api/voice-process', () => {
            return new HttpResponse(null, { status: 500 });
          })
        );
      });

      it('correctly identifies common seafood species', async () => {
        const testCases = [
          { input: 'Add Atlantic Salmon', expected: 'Atlantic Salmon' },
          { input: 'Received Dungeness Crab', expected: 'Dungeness Crab' },
          { input: 'Got Pacific Halibut', expected: 'Pacific Halibut' },
          { input: 'Blue Point Oysters arrived', expected: 'Blue Point Oysters' },
          { input: 'King Crab delivery', expected: 'King Crab' }
        ];

        for (const testCase of testCases) {
          const result = await processVoiceInput(testCase.input);
          expect(result.product).toBe(testCase.expected);
        }
      });

      it('applies voice recognition corrections', async () => {
        const corrections = [
          { input: 'dangerous grab', expected: 'Dungeness Crab' },
          { input: 'dangerous crab', expected: 'Dungeness Crab' },
          { input: 'king grab', expected: 'King Crab' },
          { input: 'dover soul', expected: 'Dover Sole' },
          { input: 'petrel sole', expected: 'Petrale Sole' }
        ];

        for (const correction of corrections) {
          const result = await processVoiceInput(`Add ${correction.input}`);
          expect(result.product).toBe(correction.expected);
          expect(result.metadata?.voice_correction_applied).toBe(true);
        }
      });

      it('extracts quantities with various units', async () => {
        const quantityTests = [
          { input: 'Add 50 pounds of salmon', expectedQty: 50, expectedUnit: 'lbs' },
          { input: 'Received 25.5 kg of cod', expectedQty: 25.5, expectedUnit: 'kg' },
          { input: 'Got 100 pieces of shrimp', expectedQty: 100, expectedUnit: 'units' },
          { input: 'Quantity of 75 cases', expectedQty: 75, expectedUnit: 'cases' },
          { input: 'Received 30.25 lbs', expectedQty: 30.25, expectedUnit: 'lbs' }
        ];

        for (const test of quantityTests) {
          const result = await processVoiceInput(test.input);
          expect(result.quantity).toBe(test.expectedQty);
          expect(result.unit).toBe(test.expectedUnit);
        }
      });

      it('extracts pricing information', async () => {
        const priceTests = [
          { input: 'Salmon at $15.50 per pound', expected: 15.50 },
          { input: 'Cost of $22.75', expected: 22.75 },
          { input: 'Price $30.00 per kg', expected: 30.00 },
          { input: 'For $45.25 per case', expected: 45.25 }
        ];

        for (const test of priceTests) {
          const result = await processVoiceInput(test.input);
          expect(result.price).toBe(test.expected);
        }
      });

      it('extracts vendor information', async () => {
        const vendorTests = [
          { input: 'Salmon from Ocean Fresh Ltd', expected: 'Ocean Fresh Ltd' },
          { input: 'Delivered by Alaska Seafood Co.', expected: 'Alaska Seafood Co.' },
          { input: 'Vendor Pacific Fish & Co', expected: 'Pacific Fish & Co' },
          { input: 'Supplied by Fresh Catch Inc.', expected: 'Fresh Catch Inc.' }
        ];

        for (const test of vendorTests) {
          const result = await processVoiceInput(test.input);
          expect(result.vendor).toBe(test.expected);
        }
      });

      it('handles fuzzy matching for partial product names', async () => {
        const fuzzyTests = [
          { input: 'Add Atlantic salmon', expected: 'Atlantic Salmon' },
          { input: 'Received King crab', expected: 'King Crab' },
          { input: 'Got Pacific halibut', expected: 'Pacific Halibut' }
        ];

        for (const test of fuzzyTests) {
          const result = await processVoiceInput(test.input);
          expect(result.product).toBe(test.expected);
        }
      });

      it('calculates appropriate confidence scores', async () => {
        // High confidence: product + quantity + price + vendor
        const highConfidenceResult = await processVoiceInput(
          'Add 50 pounds of Atlantic Salmon from Ocean Fresh at $15.50'
        );
        expect(highConfidenceResult.metadata?.confidence_score).toBeGreaterThan(0.7);

        // Medium confidence: product + quantity
        const mediumConfidenceResult = await processVoiceInput(
          'Add 50 pounds of salmon'
        );
        expect(mediumConfidenceResult.metadata?.confidence_score).toBeGreaterThan(0.4);
        expect(mediumConfidenceResult.metadata?.confidence_score).toBeLessThan(0.7);

        // Low confidence: unclear input
        const lowConfidenceResult = await processVoiceInput(
          'Add some fish'
        );
        expect(lowConfidenceResult.metadata?.confidence_score).toBeLessThan(0.4);
      });

      it('properly categorizes seafood products', async () => {
        const categoryTests = [
          { input: 'Atlantic Salmon', expectedCategory: 'Finfish' },
          { input: 'Dungeness Crab', expectedCategory: 'Crustaceans' },
          { input: 'Pacific Oysters', expectedCategory: 'Shellfish' },
          { input: 'Sea Urchin', expectedCategory: 'Specialty' }
        ];

        for (const test of categoryTests) {
          const result = await processVoiceInput(`Add ${test.input}`);
          expect(result.category).toBe(test.expectedCategory);
        }
      });
    });

    describe('Performance and Edge Cases', () => {
      it('handles empty or invalid input gracefully', async () => {
        const invalidInputs = ['', '   ', 'xyz123', '!!!'];

        for (const input of invalidInputs) {
          const result = await processVoiceInput(input);
          expect(result).toBeDefined();
          expect(result.metadata?.confidence_score).toBeLessThan(0.5);
        }
      });

      it('processes complex real-world voice commands', async () => {
        for (const command of mockVoiceInputs.validSeafoodCommands) {
          const result = await processVoiceInput(command);
          
          expect(result).toBeDefined();
          expect(result.metadata?.processing_method).toBe('enhanced_local');
          expect(result.metadata?.confidence_score).toBeGreaterThan(0.3);
          
          // Should extract at least product or quantity
          expect(result.product || result.quantity).toBeTruthy();
        }
      });

      it('handles ambiguous commands appropriately', async () => {
        for (const command of mockVoiceInputs.ambiguousCommands) {
          const result = await processVoiceInput(command);
          
          expect(result).toBeDefined();
          expect(result.metadata?.confidence_score).toBeLessThan(0.6);
        }
      });

      it('provides appropriate metadata for all processing methods', async () => {
        const result = await processVoiceInput('Add Atlantic Salmon');
        
        expect(result.metadata).toBeDefined();
        expect(result.metadata?.processing_method).toBeTruthy();
        expect(result.metadata?.original_text).toBe('Add Atlantic Salmon');
        expect(typeof result.metadata?.confidence_score).toBe('number');
        expect(typeof result.metadata?.seafood_database_match).toBe('boolean');
        expect(typeof result.metadata?.voice_correction_applied).toBe('boolean');
      });

      it('completes processing within reasonable time limits', async () => {
        const startTime = Date.now();
        
        await processVoiceInput(
          'Add fifty pounds of Atlantic salmon from Ocean Fresh Ltd at fifteen fifty per pound'
        );
        
        const endTime = Date.now();
        const processingTime = endTime - startTime;
        
        // Local processing should be very fast (under 100ms)
        expect(processingTime).toBeLessThan(1000);
      });
    });

    describe('Integration with Real Voice Recognition Patterns', () => {
      it('handles typical voice recognition artifacts', async () => {
        const voiceArtifacts = [
          'uhm add fifty pounds of salmon uh',
          'received... twenty five pieces of crab',
          'got uh got thirty pounds of halibut',
          'add um add forty pounds of tuna'
        ];

        for (const artifact of voiceArtifacts) {
          const result = await processVoiceInput(artifact);
          expect(result.product || result.quantity).toBeTruthy();
        }
      });

      it('processes spoken numbers correctly', async () => {
        const spokenNumbers = [
          { input: 'twenty five pounds', expectedQty: 25 },
          { input: 'fifty pounds', expectedQty: 50 },
          { input: 'one hundred pieces', expectedQty: 100 },
          { input: 'thirty five kilograms', expectedQty: 35 }
        ];

        for (const test of spokenNumbers) {
          const result = await processVoiceInput(`Add ${test.input} of salmon`);
          // Note: This would require additional number word parsing
          // For now, we test that the system handles these gracefully
          expect(result).toBeDefined();
        }
      });
    });
  });
});