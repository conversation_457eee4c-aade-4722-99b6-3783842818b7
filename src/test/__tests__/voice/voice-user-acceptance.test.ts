/**
 * Voice User Acceptance Test Suite
 * Tests realistic user scenarios with various voice inputs, accents, and edge cases
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { 
  MockOpenAIAPI, 
  MockSpeechRecognition,
  generateSeafoodTestData,
  VOICE_TEST_SAMPLES 
} from '../../mocks/voice-mocks';
import { EnhancedVoiceEventProcessor } from '../../../services/EnhancedVoiceEventProcessor';

// Mock user scenarios with different voice characteristics
const USER_SCENARIOS = {
  standardAmerican: {
    name: 'Standard American English',
    characteristics: { clarity: 0.9, accent: 'standard', speed: 'normal' },
    samples: [
      "Received twenty five pounds of fresh salmon from Ocean Fresh Seafoods",
      "Sold twelve dungeness crabs to Marina Restaurant at two PM",
      "Got a delivery of fifty pounds of cod this morning"
    ]
  },
  
  southern: {
    name: 'Southern American English',
    characteristics: { clarity: 0.85, accent: 'southern', speed: 'slow' },
    samples: [
      "Y'all, we got ourselves thirty pounds of catfish from down at the dock",
      "Sold fifteen pounds of that good shrimp to the restaurant downtown",
      "Delivered about forty pounds of red snapper to the fish market"
    ]
  },
  
  boston: {
    name: 'Boston/New England accent',
    characteristics: { clarity: 0.8, accent: 'boston', speed: 'fast' },
    samples: [
      "Got twenty pounds of lobstah from the hahbah this mornin'",
      "Sold a bunch of clams to that restaurant on the whahf",
      "Picked up some scallops from the dock, about ten pounds"
    ]
  },
  
  hispanic: {
    name: 'Hispanic English (Spanish influence)',
    characteristics: { clarity: 0.75, accent: 'hispanic', speed: 'normal' },
    samples: [
      "We receive twenty pound of the salmon from Pacific Catch today",
      "Sold fifteen piece of the red snapper to Marina Restaurant",
      "Got delivery of thirty kilogram of tuna from the supplier"
    ]
  },
  
  rapid: {
    name: 'Fast/Rushed speech',
    characteristics: { clarity: 0.7, accent: 'standard', speed: 'fast' },
    samples: [
      "Quick-received-twenty-pounds-salmon-from-Ocean-Fresh-gotta-go",
      "Sold-fifteen-crab-Marina-Restaurant-super-busy-today",
      "Got-delivery-cod-thirty-pounds-Pacific-Catch-thanks"
    ]
  },
  
  unclear: {
    name: 'Unclear/Background noise',
    characteristics: { clarity: 0.5, accent: 'standard', speed: 'normal' },
    samples: [
      "Received... *cough* ...twenty... salmon... Ocean Fresh...",
      "Sold some... *background noise* ...pounds... restaurant...",
      "Got... delivery... *phone ringing* ...fish... supplier..."
    ]
  },
  
  elderly: {
    name: 'Elderly speaker (slower, deliberate)',
    characteristics: { clarity: 0.8, accent: 'standard', speed: 'slow' },
    samples: [
      "We... received... twenty... five... pounds... of... fresh... salmon",
      "Sold... twelve... crab... to... the... Marina... Restaurant",
      "Got... a... delivery... of... cod... this... morning"
    ]
  },
  
  technical: {
    name: 'Technical/Precise language',
    characteristics: { clarity: 0.95, accent: 'standard', speed: 'normal' },
    samples: [
      "Received twenty-five point zero pounds of Atlantic salmon, scientific name Salmo salar",
      "Processed twelve units of Metacarcinus magister, commonly known as dungeness crab",
      "Inventory adjustment: added thirty point five kilograms of Pacific cod, Gadus macrocephalus"
    ]
  }
};

// Environmental conditions that affect voice recognition
const ENVIRONMENTAL_CONDITIONS = {
  quiet: { backgroundNoise: 0.1, acoustics: 'good' },
  office: { backgroundNoise: 0.3, acoustics: 'average' },
  warehouse: { backgroundNoise: 0.5, acoustics: 'poor' },
  kitchen: { backgroundNoise: 0.7, acoustics: 'poor' },
  dock: { backgroundNoise: 0.8, acoustics: 'very_poor' }
};

vi.mock('../../../lib/supabase', () => ({
  supabase: {
    from: vi.fn(() => ({
      insert: vi.fn(() => ({
        select: vi.fn(() => ({
          single: vi.fn(() => Promise.resolve({ data: { id: 'test-id' }, error: null }))
        }))
      }))
    }))
  }
}));

vi.mock('../../../lib/voice-processor', () => ({
  default: class MockVoiceProcessor {
    async processAudioBlob(audioBlob: Blob): Promise<any> {
      const mockAPI = new MockOpenAIAPI();
      
      // Simulate accent and clarity effects on processing
      const transcript = await mockAPI.transcribe(audioBlob);
      const result = await mockAPI.processTranscript(transcript);
      
      // Adjust confidence based on simulated audio characteristics
      const audioText = audioBlob.size > 100 ? 'clear' : 'unclear';
      if (audioText === 'unclear') {
        result.confidence_score *= 0.7;
        result.confidence_breakdown = {
          ...result.confidence_breakdown,
          overall: result.confidence_score
        };
      }
      
      return result;
    }
    
    getProcessingStatus() {
      return false;
    }
  }
}));

describe('Voice User Acceptance Tests', () => {
  let processor: EnhancedVoiceEventProcessor;

  beforeEach(() => {
    processor = new EnhancedVoiceEventProcessor();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Accent and Dialect Recognition', () => {
    Object.entries(USER_SCENARIOS).forEach(([scenarioKey, scenario]) => {
      it(`should handle ${scenario.name} effectively`, async () => {
        for (const sample of scenario.samples) {
          const audioBlob = new Blob([sample], { type: 'audio/webm' });
          
          try {
            const result = await processor.processVoiceEvent(audioBlob, 'test-user');
            
            // Adjust expectations based on accent characteristics
            const expectedMinConfidence = scenario.characteristics.clarity * 0.7;
            
            expect(result.confidence).toBeGreaterThan(expectedMinConfidence);
            expect(result.eventData).toBeDefined();
            
            // For clearer speech, expect better extraction
            if (scenario.characteristics.clarity > 0.8) {
              expect(result.eventData.quantity).toBeGreaterThan(0);
              expect(result.eventData.product_name).toBeTruthy();
            }
            
            // For unclear speech, should require confirmation
            if (scenario.characteristics.clarity < 0.6) {
              expect(result.requiresConfirmation).toBeTruthy();
            }
            
          } catch (error) {
            // Some very unclear samples might fail - that's acceptable
            expect(scenario.characteristics.clarity).toBeLessThan(0.6);
          }
        }
      });
    });
  });

  describe('Real-world Seafood Product Scenarios', () => {
    const seafoodData = generateSeafoodTestData();
    
    it('should handle common receiving scenarios', async () => {
      const receivingScenarios = [
        "Received twenty five pounds of fresh salmon from Ocean Fresh Seafoods this morning",
        "Got a delivery of thirty pounds of cod from Pacific Catch",
        "Incoming shipment: fifteen pounds of halibut from Northwest Seafood",
        "Truck delivery of fifty pounds of dungeness crab from the dock",
        "Fresh catch arrived: twelve pounds of rockfish from local fisherman"
      ];
      
      for (const scenario of receivingScenarios) {
        const audioBlob = new Blob([scenario], { type: 'audio/webm' });
        const result = await processor.processVoiceEvent(audioBlob, 'dock-worker');
        
        expect(result.eventData.event_type).toBe('receiving');
        expect(result.eventData.quantity).toBeGreaterThan(0);
        expect(result.eventData.product_name).toBeTruthy();
        expect(result.confidence).toBeGreaterThan(0.7);
      }
    });
    
    it('should handle common sales scenarios', async () => {
      const salesScenarios = [
        "Sold twelve pounds of salmon to Marina Restaurant",
        "Delivered fifteen pounds of cod to The Fish House",
        "Order out: twenty pounds of halibut to Ocean View Cafe",
        "Customer pickup: eight pounds of dungeness crab for Morrison's",
        "Shipped ten pounds of rockfish to Harbor Restaurant"
      ];
      
      for (const scenario of salesScenarios) {
        const audioBlob = new Blob([scenario], { type: 'audio/webm' });
        const result = await processor.processVoiceEvent(audioBlob, 'sales-staff');
        
        expect(result.eventData.event_type).toBe('sale');
        expect(result.eventData.quantity).toBeGreaterThan(0);
        expect(result.eventData.product_name).toBeTruthy();
        expect(result.confidence).toBeGreaterThan(0.7);
      }
    });

    it('should handle specialized seafood terminology', async () => {
      const specializedTerms = [
        "Received five pounds of sockeye salmon from Alaska",
        "Got two dungeness crabs, size extra large",
        "Delivered six pounds of king crab legs to customer",
        "Fresh atlantic cod, twenty pounds from Boston supplier",
        "Pacific rockfish delivery, fifteen pounds, premium grade"
      ];
      
      for (const term of specializedTerms) {
        const audioBlob = new Blob([term], { type: 'audio/webm' });
        const result = await processor.processVoiceEvent(audioBlob, 'expert-user');
        
        expect(result.eventData.product_name).toBeTruthy();
        expect(result.confidence).toBeGreaterThan(0.6);
      }
    });
  });

  describe('Environmental Condition Handling', () => {
    Object.entries(ENVIRONMENTAL_CONDITIONS).forEach(([environment, conditions]) => {
      it(`should perform adequately in ${environment} environment`, async () => {
        const sample = "Received twenty pounds of salmon from Ocean Fresh Seafoods";
        
        // Simulate environmental impact on audio quality
        const noisyAudio = conditions.backgroundNoise > 0.5 ? 
          sample.replace(/\s/g, '...') : sample;
        
        const audioBlob = new Blob([noisyAudio], { type: 'audio/webm' });
        const result = await processor.processVoiceEvent(audioBlob, 'test-user');
        
        // Adjust expectations based on environment
        if (conditions.backgroundNoise < 0.4) {
          // Good conditions - expect high performance
          expect(result.confidence).toBeGreaterThan(0.8);
          expect(result.requiresConfirmation).toBeFalsy();
        } else if (conditions.backgroundNoise < 0.7) {
          // Moderate conditions - expect medium performance
          expect(result.confidence).toBeGreaterThan(0.6);
        } else {
          // Poor conditions - expect lower confidence, may require confirmation
          expect(result.confidence).toBeGreaterThan(0.3);
          expect(result.requiresConfirmation).toBeTruthy();
        }
      });
    });
  });

  describe('Edge Cases and Error Recovery', () => {
    it('should handle interrupted speech gracefully', async () => {
      const interruptedSamples = [
        "Received twenty... sorry, received twenty five pounds of salmon",
        "Sold fifteen... wait... sold twelve pounds of cod",
        "Got delivery of... um... thirty pounds of halibut"
      ];
      
      for (const sample of interruptedSamples) {
        const audioBlob = new Blob([sample], { type: 'audio/webm' });
        const result = await processor.processVoiceEvent(audioBlob, 'test-user');
        
        // Should still extract meaningful information
        expect(result.eventData.quantity).toBeGreaterThan(0);
        expect(result.eventData.product_name).toBeTruthy();
        
        // May have lower confidence due to interruptions
        if (result.confidence < 0.7) {
          expect(result.requiresConfirmation).toBeTruthy();
        }
      }
    });

    it('should handle very long descriptions', async () => {
      const longDescription = "So today we received from our regular supplier Ocean Fresh Seafoods, which has been our primary vendor for the past five years, a shipment of twenty five pounds of really fresh Atlantic salmon that was caught yesterday morning off the coast of Maine and was immediately put on ice and shipped overnight to our facility here in Seattle where it arrived this morning at approximately eight thirty AM and was immediately processed and put into our cold storage unit";
      
      const audioBlob = new Blob([longDescription], { type: 'audio/webm' });
      const result = await processor.processVoiceEvent(audioBlob, 'chatty-user');
      
      // Should extract key information despite verbosity
      expect(result.eventData.product_name).toContain('salmon');
      expect(result.eventData.quantity).toBe(25);
      expect(result.eventData.vendor_name).toContain('Ocean Fresh');
      expect(result.eventData.event_type).toBe('receiving');
    });

    it('should handle multiple products mentioned', async () => {
      const multiProductSamples = [
        "We received salmon and cod today, but mainly twenty pounds of salmon",
        "Got delivery of fish - salmon, cod, and halibut - but recording twenty pounds of salmon",
        "Among the salmon, cod, and other fish, recording fifteen pounds of salmon specifically"
      ];
      
      for (const sample of multiProductSamples) {
        const audioBlob = new Blob([sample], { type: 'audio/webm' });
        const result = await processor.processVoiceEvent(audioBlob, 'test-user');
        
        // Should focus on the main product and quantity mentioned
        expect(result.eventData.product_name).toContain('salmon');
        expect(result.eventData.quantity).toBeGreaterThan(0);
      }
    });

    it('should handle unclear quantities gracefully', async () => {
      const unclearQuantities = [
        "Received a bunch of salmon from Ocean Fresh",
        "Got some cod today, not sure exactly how much",
        "Delivered several pounds of halibut to the restaurant",
        "Received a couple bags of shrimp"
      ];
      
      for (const sample of unclearQuantities) {
        const audioBlob = new Blob([sample], { type: 'audio/webm' });
        const result = await processor.processVoiceEvent(audioBlob, 'test-user');
        
        expect(result.eventData.product_name).toBeTruthy();
        expect(result.requiresConfirmation).toBeTruthy();
        expect(result.confidence).toBeLessThan(0.8);
      }
    });
  });

  describe('User Experience Scenarios', () => {
    it('should provide appropriate feedback for different confidence levels', async () => {
      const confidenceScenarios = [
        {
          input: "Received twenty five pounds of fresh salmon from Ocean Fresh Seafoods",
          expectedConfidence: 'high',
          expectedFeedback: 'Successfully recorded'
        },
        {
          input: "Got some fish from the supplier",
          expectedConfidence: 'medium',
          expectedFeedback: 'verify'
        },
        {
          input: "Received... um... fish... from...",
          expectedConfidence: 'low',
          expectedFeedback: 'unclear'
        }
      ];
      
      for (const scenario of confidenceScenarios) {
        const audioBlob = new Blob([scenario.input], { type: 'audio/webm' });
        const result = await processor.processVoiceEvent(audioBlob, 'test-user');
        
        if (scenario.expectedConfidence === 'high') {
          expect(result.confidence).toBeGreaterThan(0.8);
          expect(result.audioConfirmation).toContain(scenario.expectedFeedback);
          expect(result.requiresConfirmation).toBeFalsy();
        } else if (scenario.expectedConfidence === 'medium') {
          expect(result.confidence).toBeGreaterThan(0.6);
          expect(result.confidence).toBeLessThanOrEqual(0.8);
          expect(result.audioConfirmation).toContain(scenario.expectedFeedback);
        } else {
          expect(result.confidence).toBeLessThanOrEqual(0.6);
          expect(result.requiresConfirmation).toBeTruthy();
        }
      }
    });

    it('should handle rapid successive voice inputs', async () => {
      const rapidInputs = [
        "Received ten pounds salmon",
        "Sold five pounds cod", 
        "Got delivery halibut fifteen pounds",
        "Shipped out twelve pounds crab"
      ];
      
      const results = [];
      
      for (const input of rapidInputs) {
        const audioBlob = new Blob([input], { type: 'audio/webm' });
        const result = await processor.processVoiceEvent(audioBlob, 'busy-user');
        results.push(result);
        
        // Small delay to simulate rapid but not simultaneous input
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      // All inputs should be processed successfully
      expect(results).toHaveLength(4);
      results.forEach(result => {
        expect(result.eventData).toBeDefined();
        expect(result.eventData.quantity).toBeGreaterThan(0);
      });
    });

    it('should maintain context across related transactions', async () => {
      const contextualInputs = [
        "Received twenty five pounds of salmon from Ocean Fresh Seafoods",
        "Also got fifteen pounds of cod from the same supplier",
        "And they delivered ten pounds of halibut too"
      ];
      
      const results = [];
      
      for (const input of contextualInputs) {
        const audioBlob = new Blob([input], { type: 'audio/webm' });
        const result = await processor.processVoiceEvent(audioBlob, 'contextual-user');
        results.push(result);
      }
      
      // First input should establish vendor
      expect(results[0].eventData.vendor_name).toContain('Ocean Fresh');
      
      // Subsequent inputs should maintain reasonable context
      results.forEach(result => {
        expect(result.eventData.event_type).toBe('receiving');
        expect(result.eventData.quantity).toBeGreaterThan(0);
      });
    });
  });

  describe('Accessibility and Inclusivity', () => {
    it('should work with speech impediments simulation', async () => {
      const impedimentSamples = [
        "Weceived twenty pounds of thalmon", // R/L substitution
        "Received twendy poundth of thalmon", // TH substitution
        "Weceiwed twenty pounz salmon", // Various substitutions
      ];
      
      for (const sample of impedimentSamples) {
        const audioBlob = new Blob([sample], { type: 'audio/webm' });
        const result = await processor.processVoiceEvent(audioBlob, 'inclusive-test');
        
        // Should still recognize core information
        expect(result.eventData.product_name).toContain('salmon');
        expect(result.eventData.quantity).toBe(20);
        expect(result.eventData.event_type).toBe('receiving');
        
        // May require confirmation due to unclear pronunciation
        if (result.confidence < 0.7) {
          expect(result.requiresConfirmation).toBeTruthy();
        }
      }
    });

    it('should handle non-native English speakers', async () => {
      const nonNativeSamples = [
        "We receive twenty kilo of the salmon today morning",
        "Sold fifteen piece fish to restaurant customer",
        "Got new delivery from supplier, is thirty pound cod"
      ];
      
      for (const sample of nonNativeSamples) {
        const audioBlob = new Blob([sample], { type: 'audio/webm' });
        const result = await processor.processVoiceEvent(audioBlob, 'non-native-speaker');
        
        // Should extract meaning despite grammatical variations
        expect(result.eventData.quantity).toBeGreaterThan(0);
        expect(result.eventData.product_name).toBeTruthy();
        
        // May need confirmation for clarity
        expect(result.confidence).toBeGreaterThan(0.4);
      }
    });

    it('should work across different age groups', async () => {
      const ageGroupSamples = {
        young: "Like, we totally got twenty pounds of salmon today",
        middle: "Received twenty pounds of salmon from our supplier today",
        elderly: "We... received... twenty... pounds... of... salmon... today"
      };
      
      for (const [ageGroup, sample] of Object.entries(ageGroupSamples)) {
        const audioBlob = new Blob([sample], { type: 'audio/webm' });
        const result = await processor.processVoiceEvent(audioBlob, `${ageGroup}-user`);
        
        expect(result.eventData.product_name).toContain('salmon');
        expect(result.eventData.quantity).toBe(20);
        expect(result.confidence).toBeGreaterThan(0.6);
      }
    });
  });

  describe('Industry-Specific Scenarios', () => {
    it('should handle dock worker terminology', async () => {
      const dockWorkerSamples = [
        "Boat came in with a good haul - twenty five pounds of fresh salmon",
        "Unloaded fifteen pounds of cod from the morning catch",
        "Got thirty pounds of rockfish straight off the boat"
      ];
      
      for (const sample of dockWorkerSamples) {
        const audioBlob = new Blob([sample], { type: 'audio/webm' });
        const result = await processor.processVoiceEvent(audioBlob, 'dock-worker');
        
        expect(result.eventData.event_type).toBe('receiving');
        expect(result.eventData.quantity).toBeGreaterThan(0);
        expect(result.eventData.product_name).toBeTruthy();
      }
    });

    it('should handle restaurant order terminology', async () => {
      const restaurantSamples = [
        "Order for table twelve: two pounds of salmon filets",
        "Kitchen needs fifteen pounds of cod for tonight's special",
        "Prep order: ten pounds of halibut for tomorrow's service"
      ];
      
      for (const sample of restaurantSamples) {
        const audioBlob = new Blob([sample], { type: 'audio/webm' });
        const result = await processor.processVoiceEvent(audioBlob, 'restaurant-staff');
        
        expect(result.eventData.quantity).toBeGreaterThan(0);
        expect(result.eventData.product_name).toBeTruthy();
      }
    });

    it('should handle retail fish market terminology', async () => {
      const retailSamples = [
        "Customer wants two pounds of fresh salmon fillets",
        "Sold three dungeness crabs to the lady in the blue coat",
        "Special order: five pounds of king crab legs for pickup tomorrow"
      ];
      
      for (const sample of retailSamples) {
        const audioBlob = new Blob([sample], { type: 'audio/webm' });
        const result = await processor.processVoiceEvent(audioBlob, 'retail-staff');
        
        expect(result.eventData.event_type).toBe('sale');
        expect(result.eventData.quantity).toBeGreaterThan(0);
        expect(result.eventData.product_name).toBeTruthy();
      }
    });
  });
});