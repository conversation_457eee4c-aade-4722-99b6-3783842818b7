/**
 * Voice Recognition Regression Test Suite
 * Automated testing to detect accuracy degradation over time with known good samples
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { 
  MockOpenAIAPI, 
  VOICE_TEST_SAMPLES,
  generateVoiceTestScenarios 
} from '../../mocks/voice-mocks';
import { EnhancedVoiceEventProcessor } from '../../../services/EnhancedVoiceEventProcessor';

// Baseline accuracy metrics established from initial testing
const BASELINE_ACCURACY_METRICS = {
  productRecognition: 0.95,      // 95% accuracy for product names
  quantityExtraction: 0.92,      // 92% accuracy for quantities
  vendorRecognition: 0.88,       // 88% accuracy for vendor names
  eventTypeClassification: 0.94, // 94% accuracy for event types
  overallConfidence: 0.89,       // 89% average confidence score
  highConfidenceRate: 0.75,      // 75% of samples should be high confidence (>0.8)
  processingLatency: 1500        // 1.5 seconds average processing time
};

// Acceptable degradation thresholds (percentage points)
const DEGRADATION_THRESHOLDS = {
  productRecognition: 0.05,      // Max 5% degradation
  quantityExtraction: 0.05,
  vendorRecognition: 0.08,       // Slightly higher tolerance for vendor names
  eventTypeClassification: 0.03,
  overallConfidence: 0.05,
  highConfidenceRate: 0.10,
  processingLatency: 500         // Max 500ms increase
};

// Regression test data set - these samples should consistently perform well
const REGRESSION_TEST_SET = [
  // High confidence samples that should always work well
  {
    id: 'R001',
    category: 'high_confidence',
    transcript: 'Received twenty five pounds of fresh salmon from Ocean Fresh Seafoods',
    expectedResults: {
      product: 'salmon',
      quantity: 25,
      unit: 'pounds',
      vendor: 'Ocean Fresh Seafoods',
      eventType: 'receiving',
      minConfidence: 0.9
    }
  },
  {
    id: 'R002',
    category: 'high_confidence',
    transcript: 'Sold twelve dungeness crabs to Marina Restaurant',
    expectedResults: {
      product: 'dungeness crabs',
      quantity: 12,
      unit: 'pieces',
      customer: 'Marina Restaurant',
      eventType: 'sale',
      minConfidence: 0.85
    }
  },
  {
    id: 'R003',
    category: 'high_confidence',
    transcript: 'Delivered thirty pounds of cod to The Fish House',
    expectedResults: {
      product: 'cod',
      quantity: 30,
      unit: 'pounds',
      customer: 'The Fish House',
      eventType: 'sale',
      minConfidence: 0.9
    }
  },
  
  // Medium confidence samples - should be stable
  {
    id: 'R004',
    category: 'medium_confidence',
    transcript: 'Got some halibut from Pacific Catch today',
    expectedResults: {
      product: 'halibut',
      vendor: 'Pacific Catch',
      eventType: 'receiving',
      minConfidence: 0.7,
      maxConfidence: 0.85
    }
  },
  {
    id: 'R005',
    category: 'medium_confidence',
    transcript: 'Sold fifteen pound of cod to Morrison\'s',
    expectedResults: {
      product: 'cod',
      quantity: 15,
      customer: 'Morrison\'s',
      eventType: 'sale',
      minConfidence: 0.65
    }
  },
  
  // Edge cases - should handle consistently
  {
    id: 'R006',
    category: 'edge_case',
    transcript: 'Received twenty-five point five kilograms of sockeye salmon',
    expectedResults: {
      product: 'sockeye salmon',
      quantity: 25.5,
      unit: 'kilograms',
      eventType: 'receiving',
      minConfidence: 0.75
    }
  },
  {
    id: 'R007',
    category: 'edge_case',
    transcript: 'Order out two dozen live dungeness crabs',
    expectedResults: {
      product: 'dungeness crabs',
      quantity: 24,
      eventType: 'sale',
      minConfidence: 0.7
    }
  },
  
  // Specialized terminology
  {
    id: 'R008',
    category: 'specialized',
    transcript: 'Received fifty pounds of alaska pollock from Trident Seafoods',
    expectedResults: {
      product: 'alaska pollock',
      quantity: 50,
      unit: 'pounds',
      vendor: 'Trident Seafoods',
      eventType: 'receiving',
      minConfidence: 0.8
    }
  },
  {
    id: 'R009',
    category: 'specialized',
    transcript: 'Sold eight pounds of king crab legs to Harbor Restaurant',
    expectedResults: {
      product: 'king crab legs',
      quantity: 8,
      unit: 'pounds',
      customer: 'Harbor Restaurant',
      eventType: 'sale',
      minConfidence: 0.8
    }
  },
  
  // Low confidence samples - should consistently flag for review
  {
    id: 'R010',
    category: 'low_confidence',
    transcript: 'Got some fish from the supplier',
    expectedResults: {
      eventType: 'receiving',
      maxConfidence: 0.6,
      shouldRequireConfirmation: true
    }
  }
];

vi.mock('../../../lib/supabase', () => ({
  supabase: {
    from: vi.fn(() => ({
      insert: vi.fn(() => ({
        select: vi.fn(() => ({
          single: vi.fn(() => Promise.resolve({ data: { id: 'test-id' }, error: null }))
        }))
      }))
    }))
  }
}));

vi.mock('../../../lib/voice-processor', () => ({
  default: class MockVoiceProcessor {
    async processAudioBlob(audioBlob: Blob): Promise<any> {
      const mockAPI = new MockOpenAIAPI();
      const transcript = await mockAPI.transcribe(audioBlob);
      return await mockAPI.processTranscript(transcript);
    }
    
    getProcessingStatus() {
      return false;
    }
  }
}));

describe('Voice Recognition Regression Tests', () => {
  let processor: EnhancedVoiceEventProcessor;
  let testResults: Array<{
    testId: string;
    category: string;
    results: any;
    accuracy: any;
    passed: boolean;
  }> = [];

  beforeEach(() => {
    processor = new EnhancedVoiceEventProcessor();
    testResults = [];
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Baseline Accuracy Maintenance', () => {
    it('should maintain product recognition accuracy above baseline', async () => {
      let correctRecognitions = 0;
      let totalTests = 0;

      for (const testCase of REGRESSION_TEST_SET) {
        if (!testCase.expectedResults.product) continue;

        const audioBlob = new Blob([testCase.transcript], { type: 'audio/webm' });
        const result = await processor.processVoiceEvent(audioBlob, 'regression-test');

        totalTests++;
        const recognizedCorrectly = result.eventData.product_name?.toLowerCase()
          .includes(testCase.expectedResults.product.toLowerCase());

        if (recognizedCorrectly) correctRecognitions++;

        testResults.push({
          testId: testCase.id,
          category: testCase.category,
          results: result,
          accuracy: { productRecognition: recognizedCorrectly },
          passed: recognizedCorrectly
        });
      }

      const actualAccuracy = correctRecognitions / totalTests;
      const minimumAccuracy = BASELINE_ACCURACY_METRICS.productRecognition - 
                             DEGRADATION_THRESHOLDS.productRecognition;

      expect(actualAccuracy).toBeGreaterThanOrEqual(minimumAccuracy);
      
      if (actualAccuracy < BASELINE_ACCURACY_METRICS.productRecognition) {
        console.warn(`Product recognition accuracy degraded from ${BASELINE_ACCURACY_METRICS.productRecognition} to ${actualAccuracy}`);
      }
    });

    it('should maintain quantity extraction accuracy above baseline', async () => {
      let correctExtractions = 0;
      let totalTests = 0;

      for (const testCase of REGRESSION_TEST_SET) {
        if (testCase.expectedResults.quantity === undefined) continue;

        const audioBlob = new Blob([testCase.transcript], { type: 'audio/webm' });
        const result = await processor.processVoiceEvent(audioBlob, 'regression-test');

        totalTests++;
        const extractedCorrectly = Math.abs(
          result.eventData.quantity - testCase.expectedResults.quantity
        ) <= 1; // Allow 1 unit tolerance

        if (extractedCorrectly) correctExtractions++;

        testResults.push({
          testId: testCase.id,
          category: testCase.category,
          results: result,
          accuracy: { quantityExtraction: extractedCorrectly },
          passed: extractedCorrectly
        });
      }

      const actualAccuracy = correctExtractions / totalTests;
      const minimumAccuracy = BASELINE_ACCURACY_METRICS.quantityExtraction - 
                             DEGRADATION_THRESHOLDS.quantityExtraction;

      expect(actualAccuracy).toBeGreaterThanOrEqual(minimumAccuracy);
    });

    it('should maintain event type classification accuracy above baseline', async () => {
      let correctClassifications = 0;
      let totalTests = 0;

      for (const testCase of REGRESSION_TEST_SET) {
        const audioBlob = new Blob([testCase.transcript], { type: 'audio/webm' });
        const result = await processor.processVoiceEvent(audioBlob, 'regression-test');

        totalTests++;
        const classifiedCorrectly = result.eventData.event_type === testCase.expectedResults.eventType;

        if (classifiedCorrectly) correctClassifications++;

        testResults.push({
          testId: testCase.id,
          category: testCase.category,
          results: result,
          accuracy: { eventTypeClassification: classifiedCorrectly },
          passed: classifiedCorrectly
        });
      }

      const actualAccuracy = correctClassifications / totalTests;
      const minimumAccuracy = BASELINE_ACCURACY_METRICS.eventTypeClassification - 
                             DEGRADATION_THRESHOLDS.eventTypeClassification;

      expect(actualAccuracy).toBeGreaterThanOrEqual(minimumAccuracy);
    });

    it('should maintain overall confidence scores above baseline', async () => {
      const confidenceScores: number[] = [];

      for (const testCase of REGRESSION_TEST_SET) {
        const audioBlob = new Blob([testCase.transcript], { type: 'audio/webm' });
        const result = await processor.processVoiceEvent(audioBlob, 'regression-test');

        confidenceScores.push(result.confidence);
      }

      const averageConfidence = confidenceScores.reduce((sum, score) => sum + score, 0) / confidenceScores.length;
      const minimumConfidence = BASELINE_ACCURACY_METRICS.overallConfidence - 
                               DEGRADATION_THRESHOLDS.overallConfidence;

      expect(averageConfidence).toBeGreaterThanOrEqual(minimumConfidence);
    });
  });

  describe('Consistency Checks', () => {
    it('should produce consistent results across multiple runs', async () => {
      const testCase = REGRESSION_TEST_SET.find(t => t.id === 'R001')!;
      const results: any[] = [];

      // Run the same test 5 times
      for (let i = 0; i < 5; i++) {
        const audioBlob = new Blob([testCase.transcript], { type: 'audio/webm' });
        const result = await processor.processVoiceEvent(audioBlob, 'consistency-test');
        results.push(result);
      }

      // Check consistency of key extractions
      const products = results.map(r => r.eventData.product_name?.toLowerCase());
      const quantities = results.map(r => r.eventData.quantity);
      const eventTypes = results.map(r => r.eventData.event_type);

      // All results should be the same or very similar
      expect(new Set(eventTypes).size).toBeLessThanOrEqual(1); // Event type should be consistent
      expect(Math.max(...quantities) - Math.min(...quantities)).toBeLessThanOrEqual(1); // Quantities within 1 unit

      // Confidence scores should be similar (within 10%)
      const confidences = results.map(r => r.confidence);
      const avgConfidence = confidences.reduce((sum, c) => sum + c, 0) / confidences.length;
      confidences.forEach(conf => {
        expect(Math.abs(conf - avgConfidence)).toBeLessThan(0.1);
      });
    });

    it('should maintain performance for edge cases', async () => {
      const edgeCases = REGRESSION_TEST_SET.filter(t => t.category === 'edge_case');
      
      for (const testCase of edgeCases) {
        const audioBlob = new Blob([testCase.transcript], { type: 'audio/webm' });
        const result = await processor.processVoiceEvent(audioBlob, 'edge-case-test');

        // Should meet minimum confidence thresholds
        expect(result.confidence).toBeGreaterThanOrEqual(testCase.expectedResults.minConfidence);

        // Should extract key information correctly
        if (testCase.expectedResults.product) {
          expect(result.eventData.product_name?.toLowerCase())
            .toContain(testCase.expectedResults.product.toLowerCase());
        }

        if (testCase.expectedResults.quantity !== undefined) {
          expect(Math.abs(result.eventData.quantity - testCase.expectedResults.quantity))
            .toBeLessThanOrEqual(1);
        }
      }
    });

    it('should correctly flag low confidence samples for review', async () => {
      const lowConfidenceCases = REGRESSION_TEST_SET.filter(t => t.category === 'low_confidence');
      
      for (const testCase of lowConfidenceCases) {
        const audioBlob = new Blob([testCase.transcript], { type: 'audio/webm' });
        const result = await processor.processVoiceEvent(audioBlob, 'low-confidence-test');

        if (testCase.expectedResults.maxConfidence) {
          expect(result.confidence).toBeLessThanOrEqual(testCase.expectedResults.maxConfidence);
        }

        if (testCase.expectedResults.shouldRequireConfirmation) {
          expect(result.requiresConfirmation).toBeTruthy();
        }
      }
    });
  });

  describe('Performance Regression', () => {
    it('should maintain processing latency within acceptable bounds', async () => {
      const latencies: number[] = [];
      
      for (const testCase of REGRESSION_TEST_SET.slice(0, 5)) { // Test first 5 cases
        const audioBlob = new Blob([testCase.transcript], { type: 'audio/webm' });
        
        const startTime = performance.now();
        await processor.processVoiceEvent(audioBlob, 'latency-test');
        const endTime = performance.now();
        
        latencies.push(endTime - startTime);
      }

      const averageLatency = latencies.reduce((sum, lat) => sum + lat, 0) / latencies.length;
      const maxAcceptableLatency = BASELINE_ACCURACY_METRICS.processingLatency + 
                                   DEGRADATION_THRESHOLDS.processingLatency;

      expect(averageLatency).toBeLessThan(maxAcceptableLatency);
    });

    it('should not degrade under repeated processing', async () => {
      const testCase = REGRESSION_TEST_SET.find(t => t.id === 'R001')!;
      const latencies: number[] = [];
      const confidences: number[] = [];

      // Process the same input 10 times
      for (let i = 0; i < 10; i++) {
        const audioBlob = new Blob([testCase.transcript], { type: 'audio/webm' });
        
        const startTime = performance.now();
        const result = await processor.processVoiceEvent(audioBlob, 'repeated-test');
        const endTime = performance.now();
        
        latencies.push(endTime - startTime);
        confidences.push(result.confidence);
      }

      // Performance should not degrade significantly
      const firstHalfAvgLatency = latencies.slice(0, 5).reduce((sum, lat) => sum + lat, 0) / 5;
      const secondHalfAvgLatency = latencies.slice(5).reduce((sum, lat) => sum + lat, 0) / 5;
      
      expect(secondHalfAvgLatency).toBeLessThan(firstHalfAvgLatency * 1.2); // Max 20% degradation

      // Confidence should remain stable
      const confidenceVariance = confidences.reduce((sum, conf) => {
        const avg = confidences.reduce((s, c) => s + c, 0) / confidences.length;
        return sum + Math.pow(conf - avg, 2);
      }, 0) / confidences.length;
      
      expect(Math.sqrt(confidenceVariance)).toBeLessThan(0.05); // Low variance in confidence
    });
  });

  describe('Regression Test Reporting', () => {
    it('should generate comprehensive regression test report', async () => {
      // Run all regression tests
      const report = {
        timestamp: new Date().toISOString(),
        totalTests: REGRESSION_TEST_SET.length,
        passedTests: 0,
        failedTests: 0,
        categoryBreakdown: {} as Record<string, any>,
        accuracyMetrics: {
          productRecognition: 0,
          quantityExtraction: 0,
          eventTypeClassification: 0,
          overallConfidence: 0
        },
        regressionStatus: 'PASS'
      };

      const categoryStats: Record<string, any> = {};

      for (const testCase of REGRESSION_TEST_SET) {
        const audioBlob = new Blob([testCase.transcript], { type: 'audio/webm' });
        const result = await processor.processVoiceEvent(audioBlob, 'report-test');

        // Track category statistics
        if (!categoryStats[testCase.category]) {
          categoryStats[testCase.category] = {
            total: 0,
            passed: 0,
            avgConfidence: 0,
            confidences: []
          };
        }

        categoryStats[testCase.category].total++;
        categoryStats[testCase.category].confidences.push(result.confidence);

        // Check if test passed based on expected results
        let testPassed = true;

        if (testCase.expectedResults.minConfidence) {
          testPassed = testPassed && result.confidence >= testCase.expectedResults.minConfidence;
        }

        if (testCase.expectedResults.maxConfidence) {
          testPassed = testPassed && result.confidence <= testCase.expectedResults.maxConfidence;
        }

        if (testCase.expectedResults.product) {
          testPassed = testPassed && result.eventData.product_name?.toLowerCase()
            .includes(testCase.expectedResults.product.toLowerCase());
        }

        if (testCase.expectedResults.eventType) {
          testPassed = testPassed && result.eventData.event_type === testCase.expectedResults.eventType;
        }

        if (testPassed) {
          report.passedTests++;
          categoryStats[testCase.category].passed++;
        } else {
          report.failedTests++;
        }
      }

      // Calculate category averages
      Object.keys(categoryStats).forEach(category => {
        const stats = categoryStats[category];
        stats.avgConfidence = stats.confidences.reduce((sum: number, c: number) => sum + c, 0) / stats.confidences.length;
        stats.passRate = stats.passed / stats.total;
      });

      report.categoryBreakdown = categoryStats;

      // Determine overall regression status
      const passRate = report.passedTests / report.totalTests;
      if (passRate < 0.9) {
        report.regressionStatus = 'FAIL';
      } else if (passRate < 0.95) {
        report.regressionStatus = 'WARNING';
      }

      // Verify report structure
      expect(report.totalTests).toBe(REGRESSION_TEST_SET.length);
      expect(report.passedTests + report.failedTests).toBe(report.totalTests);
      expect(Object.keys(report.categoryBreakdown)).toContain('high_confidence');
      expect(report.regressionStatus).toBeOneOf(['PASS', 'WARNING', 'FAIL']);

      // Log report for analysis
      console.log('Regression Test Report:', JSON.stringify(report, null, 2));
    });

    it('should detect accuracy degradation trends', async () => {
      // Simulate historical test results (in real implementation, this would come from a database)
      const historicalResults = [
        { date: '2024-01-01', productAccuracy: 0.95, overallConfidence: 0.89 },
        { date: '2024-01-15', productAccuracy: 0.94, overallConfidence: 0.88 },
        { date: '2024-02-01', productAccuracy: 0.93, overallConfidence: 0.87 }
      ];

      // Run current tests
      let currentProductAccuracy = 0;
      let currentOverallConfidence = 0;
      let totalTests = 0;

      for (const testCase of REGRESSION_TEST_SET.slice(0, 5)) {
        const audioBlob = new Blob([testCase.transcript], { type: 'audio/webm' });
        const result = await processor.processVoiceEvent(audioBlob, 'trend-test');

        totalTests++;
        currentOverallConfidence += result.confidence;

        if (testCase.expectedResults.product && 
            result.eventData.product_name?.toLowerCase().includes(testCase.expectedResults.product.toLowerCase())) {
          currentProductAccuracy++;
        }
      }

      currentProductAccuracy /= totalTests;
      currentOverallConfidence /= totalTests;

      // Check for degradation trends
      const latestHistorical = historicalResults[historicalResults.length - 1];
      const productDegradation = latestHistorical.productAccuracy - currentProductAccuracy;
      const confidenceDegradation = latestHistorical.overallConfidence - currentOverallConfidence;

      expect(productDegradation).toBeLessThan(DEGRADATION_THRESHOLDS.productRecognition);
      expect(confidenceDegradation).toBeLessThan(DEGRADATION_THRESHOLDS.overallConfidence);

      // Alert if degradation trend is detected
      if (productDegradation > DEGRADATION_THRESHOLDS.productRecognition * 0.5) {
        console.warn(`Potential degradation trend detected in product accuracy: ${productDegradation}`);
      }
    });
  });
});