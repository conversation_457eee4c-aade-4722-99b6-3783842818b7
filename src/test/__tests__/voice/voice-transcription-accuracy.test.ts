/**
 * Voice Transcription Accuracy Test Suite
 * Tests the accuracy of voice transcription and AI processing for seafood terminology
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { 
  MockOpenAIAPI, 
  VOICE_TEST_SAMPLES, 
  generateSeafoodTestData,
  generateVoiceTestScenarios
} from '../../mocks/voice-mocks';
import { EnhancedVoiceEventProcessor } from '../../../services/EnhancedVoiceEventProcessor';
import { VoiceCommand } from '../../../types/schema';

// Mock external dependencies
vi.mock('../../../lib/supabase', () => ({
  supabase: {
    from: vi.fn(() => ({
      insert: vi.fn(() => ({
        select: vi.fn(() => ({
          single: vi.fn(() => Promise.resolve({ data: { id: 'test-id' }, error: null }))
        }))
      }))
    }))
  }
}));

vi.mock('../../../lib/voice-processor', () => ({
  default: class MockVoiceProcessor {
    async processAudioBlob(audioBlob: Blob): Promise<VoiceCommand> {
      const mockAPI = new MockOpenAIAPI();
      const transcript = await mockAPI.transcribe(audioBlob);
      return await mockAPI.processTranscript(transcript);
    }
    
    getProcessingStatus() {
      return false;
    }
  }
}));

describe('Voice Transcription Accuracy', () => {
  let processor: EnhancedVoiceEventProcessor;
  let mockAPI: MockOpenAIAPI;

  beforeEach(() => {
    processor = new EnhancedVoiceEventProcessor();
    mockAPI = new MockOpenAIAPI();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Seafood Product Recognition', () => {
    const seafoodData = generateSeafoodTestData();

    it('should accurately recognize common seafood products', async () => {
      const commonProducts = ['salmon', 'cod', 'halibut', 'tuna', 'crab'];
      
      for (const product of commonProducts) {
        const transcript = `Received 10 pounds of ${product}`;
        const result = await mockAPI.processTranscript(transcript);
        
        expect(result.product_name?.toLowerCase()).toContain(product.toLowerCase());
        expect(result.confidence_score).toBeGreaterThan(0.8);
        expect(result.confidence_breakdown?.product_match).toBeGreaterThan(0.8);
      }
    });

    it('should handle specialized seafood terminology', async () => {
      const specializedProducts = [
        { input: 'dungeness crab', expected: 'dungeness' },
        { input: 'sockeye salmon', expected: 'sockeye' },
        { input: 'alaska pollock', expected: 'pollock' },
        { input: 'king crab', expected: 'king crab' }
      ];

      for (const { input, expected } of specializedProducts) {
        const transcript = `Received 5 pounds of ${input}`;
        const result = await mockAPI.processTranscript(transcript);
        
        expect(result.product_name?.toLowerCase()).toContain(expected.toLowerCase());
        expect(result.confidence_score).toBeGreaterThan(0.7);
      }
    });

    it('should properly identify product variations and synonyms', async () => {
      const variations = [
        { input: 'king crab', alternatives: ['alaskan king crab', 'red king crab'] },
        { input: 'salmon', alternatives: ['atlantic salmon', 'pacific salmon'] },
        { input: 'shrimp', alternatives: ['prawns', 'tiger shrimp'] }
      ];

      for (const { input, alternatives } of variations) {
        for (const alt of alternatives) {
          const transcript = `Received 10 pounds of ${alt}`;
          const result = await mockAPI.processTranscript(transcript);
          
          // Should recognize as the base product or maintain the specific variety
          expect(
            result.product_name?.toLowerCase().includes(input.toLowerCase()) ||
            result.product_name?.toLowerCase().includes(alt.toLowerCase())
          ).toBeTruthy();
        }
      }
    });

    it('should handle misspelled or mispronounced product names', async () => {
      const misspellings = [
        { input: 'salmin', expected: 'salmon' },
        { input: 'halibut', expected: 'halibut' }, // Common mispronunciation
        { input: 'dungness', expected: 'dungeness' }
      ];

      for (const { input, expected } of misspellings) {
        const transcript = `Received 10 pounds of ${input}`;
        const result = await mockAPI.processTranscript(transcript);
        
        // Should either correct to expected or have low confidence
        if (result.confidence_score > 0.6) {
          expect(result.product_name?.toLowerCase()).toContain(expected.toLowerCase());
        } else {
          expect(result.confidence_score).toBeLessThan(0.7);
        }
      }
    });
  });

  describe('Quantity and Unit Extraction', () => {
    it('should accurately extract numeric quantities', async () => {
      const quantities = [1, 5, 10, 25, 50, 100];
      
      for (const quantity of quantities) {
        const transcript = `Received ${quantity} pounds of salmon`;
        const result = await mockAPI.processTranscript(transcript);
        
        expect(result.quantity).toBe(quantity);
        expect(result.confidence_breakdown?.quantity_extraction).toBeGreaterThan(0.8);
      }
    });

    it('should handle spelled-out numbers', async () => {
      const spelledNumbers = [
        { input: 'one', expected: 1 },
        { input: 'five', expected: 5 },
        { input: 'ten', expected: 10 },
        { input: 'twenty', expected: 20 },
        { input: 'twenty five', expected: 25 },
        { input: 'fifty', expected: 50 }
      ];

      for (const { input, expected } of spelledNumbers) {
        const transcript = `Received ${input} pounds of salmon`;
        const result = await mockAPI.processTranscript(transcript);
        
        expect(result.quantity).toBe(expected);
        expect(result.confidence_breakdown?.quantity_extraction).toBeGreaterThan(0.7);
      }
    });

    it('should recognize various unit formats', async () => {
      const units = [
        { input: 'pounds', expected: 'pounds' },
        { input: 'lbs', expected: 'lbs' },
        { input: 'kg', expected: 'kg' },
        { input: 'kilograms', expected: 'kilograms' },
        { input: 'pieces', expected: 'pieces' },
        { input: 'each', expected: 'each' },
        { input: 'dozen', expected: 'dozen' }
      ];

      for (const { input, expected } of units) {
        const transcript = `Received 10 ${input} of salmon`;
        const result = await mockAPI.processTranscript(transcript);
        
        expect(result.unit?.toLowerCase()).toBe(expected.toLowerCase());
      }
    });

    it('should handle fractional quantities', async () => {
      const fractions = [
        { input: 'one and a half', expected: 1.5 },
        { input: 'two point five', expected: 2.5 },
        { input: 'half a pound', expected: 0.5 },
        { input: 'quarter pound', expected: 0.25 }
      ];

      for (const { input, expected } of fractions) {
        const transcript = `Received ${input} pounds of salmon`;
        const result = await mockAPI.processTranscript(transcript);
        
        expect(result.quantity).toBeCloseTo(expected, 1);
      }
    });

    it('should handle unclear or missing quantities', async () => {
      const unclearQuantities = [
        'Received some salmon',
        'Got a bunch of crab',
        'Delivered salmon to customer',
        'Processed the fish'
      ];

      for (const transcript of unclearQuantities) {
        const result = await mockAPI.processTranscript(transcript);
        
        // Should have low confidence in quantity extraction
        expect(result.confidence_breakdown?.quantity_extraction).toBeLessThan(0.6);
        expect(result.quantity).toBeLessThanOrEqual(0);
      }
    });
  });

  describe('Vendor and Customer Recognition', () => {
    const seafoodData = generateSeafoodTestData();

    it('should recognize known vendors', async () => {
      for (const vendor of seafoodData.vendors.slice(0, 5)) {
        const transcript = `Received 10 pounds of salmon from ${vendor}`;
        const result = await mockAPI.processTranscript(transcript);
        
        expect(result.vendor_name?.toLowerCase()).toContain(
          vendor.toLowerCase().split(' ')[0] // At least first word should match
        );
        expect(result.confidence_breakdown?.vendor_match).toBeGreaterThan(0.7);
      }
    });

    it('should recognize known customers', async () => {
      for (const customer of seafoodData.customers.slice(0, 5)) {
        const transcript = `Sold 10 pounds of salmon to ${customer}`;
        const result = await mockAPI.processTranscript(transcript);
        
        expect(result.customer_name?.toLowerCase()).toContain(
          customer.toLowerCase().split(' ')[0] // At least first word should match
        );
        expect(result.confidence_breakdown?.customer_match || 0).toBeGreaterThan(0.7);
      }
    });

    it('should handle partial vendor/customer names', async () => {
      const partialNames = [
        { full: 'Ocean Fresh Seafoods', partial: 'Ocean Fresh' },
        { full: 'Marina Restaurant', partial: 'Marina' },
        { full: 'Pacific Catch', partial: 'Pacific' }
      ];

      for (const { full, partial } of partialNames) {
        const transcript = `Received 10 pounds of salmon from ${partial}`;
        const result = await mockAPI.processTranscript(transcript);
        
        expect(result.vendor_name?.toLowerCase()).toContain(partial.toLowerCase());
      }
    });

    it('should handle new/unknown vendors with lower confidence', async () => {
      const unknownVendors = [
        'New Supplier Co',
        'Unknown Fish Market',
        'Local Fisherman'
      ];

      for (const vendor of unknownVendors) {
        const transcript = `Received 10 pounds of salmon from ${vendor}`;
        const result = await mockAPI.processTranscript(transcript);
        
        // Should capture the name but with lower confidence
        expect(result.vendor_name).toBeTruthy();
        expect(result.confidence_breakdown?.vendor_match || 0).toBeLessThan(0.8);
      }
    });
  });

  describe('Event Type Classification', () => {
    it('should accurately classify receiving events', async () => {
      const receivingPhrases = [
        'Received 10 pounds of salmon',
        'Got delivery of 5 pounds cod',
        'Incoming shipment 25 pounds halibut',
        'Delivered to us 15 pounds tuna'
      ];

      for (const phrase of receivingPhrases) {
        const result = await mockAPI.processTranscript(phrase);
        
        expect(result.event_type).toBe('receiving');
        expect(result.confidence_breakdown?.event_type).toBeGreaterThan(0.8);
      }
    });

    it('should accurately classify sales events', async () => {
      const salesPhrases = [
        'Sold 10 pounds of salmon to Marina Restaurant',
        'Delivered 5 pounds cod to customer',
        'Order out 25 pounds halibut',
        'Shipped 15 pounds tuna'
      ];

      for (const phrase of salesPhrases) {
        const result = await mockAPI.processTranscript(phrase);
        
        expect(result.event_type).toBe('sale');
        expect(result.confidence_breakdown?.event_type).toBeGreaterThan(0.8);
      }
    });

    it('should handle ambiguous event types', async () => {
      const ambiguousPhrases = [
        'Processed 10 pounds of salmon',
        'Moved 5 pounds cod',
        'Handled 25 pounds halibut'
      ];

      for (const phrase of ambiguousPhrases) {
        const result = await mockAPI.processTranscript(phrase);
        
        // Should have lower confidence in event type
        expect(result.confidence_breakdown?.event_type).toBeLessThan(0.7);
      }
    });
  });

  describe('Confidence Scoring Accuracy', () => {
    it('should assign high confidence to clear, complete inputs', async () => {
      const highConfidenceInputs = [
        'Received 25 pounds of fresh salmon from Ocean Fresh Seafoods',
        'Sold 12 dungeness crabs to Marina Restaurant',
        'Delivered 50 pounds of cod to The Fish House'
      ];

      for (const input of highConfidenceInputs) {
        const result = await mockAPI.processTranscript(input);
        
        expect(result.confidence_score).toBeGreaterThan(0.85);
        expect(result.confidence_breakdown?.overall).toBeGreaterThan(0.85);
      }
    });

    it('should assign medium confidence to partial inputs', async () => {
      const mediumConfidenceInputs = [
        'Received some salmon from Ocean Fresh',
        'Sold 12 crab to Marina',
        'Got halibut delivery'
      ];

      for (const input of mediumConfidenceInputs) {
        const result = await mockAPI.processTranscript(input);
        
        expect(result.confidence_score).toBeGreaterThanOrEqual(0.6);
        expect(result.confidence_score).toBeLessThanOrEqual(0.85);
      }
    });

    it('should assign low confidence to unclear inputs', async () => {
      const lowConfidenceInputs = [
        'Got some fish',
        'Delivered... uh... stuff',
        'Received... from...',
        'Fish thing happened'
      ];

      for (const input of lowConfidenceInputs) {
        const result = await mockAPI.processTranscript(input);
        
        expect(result.confidence_score).toBeLessThan(0.6);
      }
    });

    it('should provide detailed confidence breakdown', async () => {
      const input = 'Received 25 pounds of salmon from Ocean Fresh Seafoods';
      const result = await mockAPI.processTranscript(input);
      
      expect(result.confidence_breakdown).toBeDefined();
      expect(result.confidence_breakdown?.product_match).toBeDefined();
      expect(result.confidence_breakdown?.quantity_extraction).toBeDefined();
      expect(result.confidence_breakdown?.vendor_match).toBeDefined();
      expect(result.confidence_breakdown?.event_type).toBeDefined();
      expect(result.confidence_breakdown?.overall).toBeDefined();
      
      // Overall should be reasonable average of components
      const components = [
        result.confidence_breakdown.product_match,
        result.confidence_breakdown.quantity_extraction,
        result.confidence_breakdown.vendor_match,
        result.confidence_breakdown.event_type
      ].filter(Boolean);
      
      const expectedOverall = components.reduce((sum, val) => sum + val, 0) / components.length;
      expect(result.confidence_breakdown.overall).toBeCloseTo(expectedOverall, 0.1);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle empty transcripts gracefully', async () => {
      const result = await mockAPI.processTranscript('');
      
      expect(result.confidence_score).toBeLessThan(0.3);
      expect(result.product_name).toBeDefined();
      expect(result.event_type).toBeDefined();
    });

    it('should handle very long transcripts', async () => {
      const longTranscript = 'Received '.repeat(100) + '10 pounds of salmon from Ocean Fresh Seafoods';
      const result = await mockAPI.processTranscript(longTranscript);
      
      expect(result.product_name).toContain('salmon');
      expect(result.quantity).toBe(10);
      expect(result.vendor_name).toContain('Ocean Fresh');
    });

    it('should handle non-English words mixed with English', async () => {
      const mixedInputs = [
        'Received 10 libras of salmon', // Spanish for pounds
        'Got 5 kg de cod', // Mixed Spanish/English
        'Delivered saumon to restaurant' // French for salmon
      ];

      for (const input of mixedInputs) {
        const result = await mockAPI.processTranscript(input);
        
        // Should either recognize or have appropriately low confidence
        expect(result.confidence_score).toBeGreaterThan(0);
      }
    });

    it('should handle numbers in different formats', async () => {
      const numberFormats = [
        { input: '1,000 pounds', expected: 1000 },
        { input: '2.5 kg', expected: 2.5 },
        { input: 'twenty-five lbs', expected: 25 },
        { input: '1st shipment 10 pounds', expected: 10 }
      ];

      for (const { input, expected } of numberFormats) {
        const transcript = `Received ${input} of salmon`;
        const result = await mockAPI.processTranscript(transcript);
        
        expect(result.quantity).toBeCloseTo(expected, 1);
      }
    });
  });

  describe('Accuracy Benchmarks', () => {
    it('should meet accuracy thresholds for product recognition', async () => {
      const scenarios = generateVoiceTestScenarios();
      const productAccuracy: number[] = [];

      for (const scenario of scenarios) {
        const result = await mockAPI.processTranscript(scenario.transcript);
        const isAccurate = result.product_name?.toLowerCase().includes(
          scenario.expectedProduct.toLowerCase()
        );
        productAccuracy.push(isAccurate ? 1 : 0);
      }

      const overallAccuracy = productAccuracy.reduce((sum, val) => sum + val, 0) / productAccuracy.length;
      expect(overallAccuracy).toBeGreaterThan(0.85); // 85% accuracy threshold
    });

    it('should meet accuracy thresholds for quantity extraction', async () => {
      const scenarios = generateVoiceTestScenarios();
      const quantityAccuracy: number[] = [];

      for (const scenario of scenarios) {
        const result = await mockAPI.processTranscript(scenario.transcript);
        const isAccurate = Math.abs(result.quantity - scenario.expectedQuantity) <= 1;
        quantityAccuracy.push(isAccurate ? 1 : 0);
      }

      const overallAccuracy = quantityAccuracy.reduce((sum, val) => sum + val, 0) / quantityAccuracy.length;
      expect(overallAccuracy).toBeGreaterThan(0.80); // 80% accuracy threshold
    });

    it('should meet confidence calibration standards', async () => {
      const scenarios = generateVoiceTestScenarios();
      const confidenceCalibration: Array<{predicted: number, actual: number}> = [];

      for (const scenario of scenarios) {
        const result = await mockAPI.processTranscript(scenario.transcript);
        
        // Calculate actual accuracy based on correct extractions
        const productCorrect = result.product_name?.toLowerCase().includes(
          scenario.expectedProduct.toLowerCase()
        ) ? 1 : 0;
        const quantityCorrect = Math.abs(result.quantity - scenario.expectedQuantity) <= 1 ? 1 : 0;
        const actualAccuracy = (productCorrect + quantityCorrect) / 2;

        confidenceCalibration.push({
          predicted: result.confidence_score,
          actual: actualAccuracy
        });
      }

      // Calculate calibration error (difference between predicted and actual accuracy)
      const calibrationErrors = confidenceCalibration.map(
        item => Math.abs(item.predicted - item.actual)
      );
      const meanCalibrationError = calibrationErrors.reduce((sum, error) => sum + error, 0) / calibrationErrors.length;

      expect(meanCalibrationError).toBeLessThan(0.2); // Within 20% calibration error
    });
  });
});