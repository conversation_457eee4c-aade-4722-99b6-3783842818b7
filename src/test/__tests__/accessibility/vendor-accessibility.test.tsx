/**
 * Accessibility tests for vendor components
 * Tests ARIA compliance, keyboard navigation, screen reader support, and WCAG guidelines
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { server } from '../../setup';
import { axe, toHaveNoViolations } from 'jest-axe';

import VendorDashboard from '../../../components/vendors/VendorDashboard';
import VendorReportCard from '../../../components/vendors/VendorReportCard';
import { 
  vendorHandlers,
  createVendorHandlersWithData 
} from '../../mocks/vendor-handlers';
import { 
  mockVendorDashboardSummary,
  generateVendorDashboardSummary,
  generateVendorAlert
} from '../../mocks/vendor-data';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

describe('Vendor Components Accessibility Tests', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();
    server.use(...vendorHandlers);
  });

  afterEach(() => {
    server.resetHandlers();
  });

  // ===== AUTOMATED ACCESSIBILITY TESTING =====

  describe('Automated Accessibility (axe-core)', () => {
    it('VendorDashboard has no accessibility violations', async () => {
      const { container } = render(<VendorDashboard />);
      
      // Wait for content to load
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      });

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('VendorReportCard has no accessibility violations', async () => {
      const { container } = render(<VendorReportCard vendorId="vendor-001" />);
      
      // Wait for content to load
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor report card...')).not.toBeInTheDocument();
      });

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('VendorDashboard with empty state has no accessibility violations', async () => {
      server.use(
        ...createVendorHandlersWithData({
          dashboardSummary: []
        })
      );

      const { container } = render(<VendorDashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('No vendors found')).toBeInTheDocument();
      });

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('VendorDashboard error state has no accessibility violations', async () => {
      server.use(
        ...createVendorHandlersWithData({
          dashboardSummary: null as any // This will cause an error
        })
      );

      // Mock console.error to avoid test noise
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      const { container } = render(<VendorDashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('Error Loading Dashboard')).toBeInTheDocument();
      });

      const results = await axe(container);
      expect(results).toHaveNoViolations();

      consoleSpy.mockRestore();
    });
  });

  // ===== KEYBOARD NAVIGATION TESTS =====

  describe('Keyboard Navigation', () => {
    it('supports full keyboard navigation in VendorDashboard', async () => {
      render(<VendorDashboard />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      });

      // Start navigation from top
      const searchInput = screen.getByPlaceholderText('Search vendors...');
      searchInput.focus();
      expect(document.activeElement).toBe(searchInput);

      // Tab through form controls
      await user.tab();
      const sortSelect = screen.getByDisplayValue('Grade');
      expect(document.activeElement).toBe(sortSelect);

      await user.tab();
      const filterSelect = screen.getByDisplayValue('All Vendors');
      expect(document.activeElement).toBe(filterSelect);

      await user.tab();
      const refreshButton = screen.getByRole('button', { name: /refresh data/i });
      expect(document.activeElement).toBe(refreshButton);

      await user.tab();
      const addVendorButton = screen.getByRole('button', { name: /add vendor/i });
      expect(document.activeElement).toBe(addVendorButton);

      // Continue to vendor action buttons
      await user.tab();
      const firstViewButton = screen.getAllByRole('button', { name: /view report/i })[0];
      expect(document.activeElement).toBe(firstViewButton);
    });

    it('supports keyboard navigation in VendorReportCard tabs', async () => {
      render(<VendorReportCard vendorId="vendor-001" />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor report card...')).not.toBeInTheDocument();
      });

      // Navigate to tab buttons
      const overviewTab = screen.getByRole('button', { name: /overview/i });
      overviewTab.focus();
      expect(document.activeElement).toBe(overviewTab);

      // Use arrow keys for tab navigation (if implemented)
      await user.keyboard('{ArrowRight}');
      const ratingsTab = screen.getByRole('button', { name: /ratings/i });
      // Note: This test assumes arrow key navigation. Adjust if using Tab key navigation instead.
      
      // Use Enter or Space to activate tabs
      await user.keyboard('{Enter}');
      expect(screen.getByText('Recent Ratings')).toBeInTheDocument();
    });

    it('supports keyboard interaction with interactive elements', async () => {
      render(<VendorDashboard />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      });

      // Test search input keyboard interaction
      const searchInput = screen.getByPlaceholderText('Search vendors...');
      await user.type(searchInput, 'Pacific');
      expect(searchInput).toHaveValue('Pacific');

      // Test select keyboard interaction
      const sortSelect = screen.getByDisplayValue('Grade');
      sortSelect.focus();
      await user.keyboard('{ArrowDown}');
      // Note: Browser behavior for select elements varies

      // Test button activation with keyboard
      const refreshButton = screen.getByRole('button', { name: /refresh data/i });
      refreshButton.focus();
      await user.keyboard('{Enter}');
      // Should not cause errors
    });

    it('maintains focus indicators throughout navigation', async () => {
      render(<VendorDashboard />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      });

      const searchInput = screen.getByPlaceholderText('Search vendors...');
      
      // Focus should be visible
      await user.tab(); // This should focus the search input or first focusable element
      
      // Check that focused element has proper focus indicators
      const focusedElement = document.activeElement;
      expect(focusedElement).toBeTruthy();
      
      // In a real implementation, you might check for CSS focus styles
      // expect(focusedElement).toHaveClass('focus:ring-2'); // Example Tailwind focus class
    });
  });

  // ===== SCREEN READER SUPPORT TESTS =====

  describe('Screen Reader Support', () => {
    it('provides proper labels for all interactive elements', async () => {
      render(<VendorDashboard />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      });

      // Search input should be properly labeled
      const searchInput = screen.getByRole('textbox');
      expect(searchInput).toHaveAttribute('placeholder', 'Search vendors...');

      // Buttons should have accessible names
      const refreshButton = screen.getByRole('button', { name: /refresh data/i });
      expect(refreshButton).toBeInTheDocument();

      const addButton = screen.getByRole('button', { name: /add vendor/i });
      expect(addButton).toBeInTheDocument();

      // View report buttons should be properly labeled
      const viewButtons = screen.getAllByRole('button', { name: /view report/i });
      expect(viewButtons.length).toBeGreaterThan(0);

      // Selects should be properly labeled
      const sortSelect = screen.getByDisplayValue('Grade');
      expect(sortSelect.closest('div')).toHaveTextContent('Sort by:');

      const filterSelect = screen.getByDisplayValue('All Vendors');
      expect(filterSelect.closest('div')).toHaveTextContent('Filter:');
    });

    it('provides meaningful headings hierarchy', async () => {
      render(<VendorDashboard />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      });

      // Main heading should be h1
      const mainHeading = screen.getByRole('heading', { level: 1 });
      expect(mainHeading).toHaveTextContent('Vendor Performance Dashboard');

      // Section headings should follow hierarchy
      const gradeDistributionHeading = screen.getByRole('heading', { level: 3 });
      expect(gradeDistributionHeading).toHaveTextContent('Grade Distribution');
    });

    it('provides proper ARIA labels for complex UI elements', async () => {
      render(<VendorReportCard vendorId="vendor-001" />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor report card...')).not.toBeInTheDocument();
      });

      // Tab list should have proper ARIA roles
      const overviewTab = screen.getByRole('button', { name: /overview/i });
      const ratingsTab = screen.getByRole('button', { name: /ratings/i });

      // Tabs should be in a tablist (if implemented with proper ARIA)
      // const tabList = screen.getByRole('tablist');
      // expect(tabList).toContain(overviewTab);

      // Close button should be properly labeled
      const closeButton = screen.getByRole('button', { name: '✕' });
      expect(closeButton).toBeInTheDocument();
    });

    it('provides status information for dynamic content', async () => {
      render(<VendorDashboard />);
      
      // Loading state should be announced
      expect(screen.getByText('Loading vendor dashboard...')).toBeInTheDocument();
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      });

      // Performance metrics should be properly announced
      expect(screen.getByText('Total Vendors')).toBeInTheDocument();
      expect(screen.getByText('A-Grade Vendors')).toBeInTheDocument();

      // Alert information should be accessible
      const alertElements = screen.getAllByText(/alert/i);
      alertElements.forEach(element => {
        expect(element).toBeInTheDocument();
      });
    });

    it('handles empty states accessibly', async () => {
      server.use(
        ...createVendorHandlersWithData({
          dashboardSummary: []
        })
      );

      render(<VendorDashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('No vendors found')).toBeInTheDocument();
      });

      // Empty state should be announced properly
      const emptyMessage = screen.getByText('No vendors found');
      expect(emptyMessage).toBeInTheDocument();
    });
  });

  // ===== COLOR CONTRAST AND VISUAL ACCESSIBILITY =====

  describe('Visual Accessibility', () => {
    it('uses appropriate color contrast for grade indicators', async () => {
      render(<VendorDashboard />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      });

      // Grade badges should have appropriate styling classes
      const gradeBadges = screen.getAllByText(/^[A-F]$/);
      gradeBadges.forEach(badge => {
        expect(badge).toBeInTheDocument();
        // In a real implementation, you might check for specific contrast classes
      });
    });

    it('provides alternative ways to convey information beyond color', async () => {
      const vendorWithAlerts = generateVendorDashboardSummary({
        vendor_name: 'Test Vendor',
        active_alerts_count: 3,
        overall_letter_grade: 'D'
      });

      server.use(
        ...createVendorHandlersWithData({
          dashboardSummary: [vendorWithAlerts]
        })
      );

      render(<VendorDashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('Test Vendor')).toBeInTheDocument();
      });

      // Alert information should be conveyed through text, not just color
      expect(screen.getByText('3 alerts')).toBeInTheDocument();

      // Grade should be explicitly shown as text
      expect(screen.getByText('D')).toBeInTheDocument();
    });
  });

  // ===== FOCUS MANAGEMENT =====

  describe('Focus Management', () => {
    it('manages focus appropriately during tab switching', async () => {
      render(<VendorReportCard vendorId="vendor-001" />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor report card...')).not.toBeInTheDocument();
      });

      const ratingsTab = screen.getByRole('button', { name: /ratings/i });
      
      // Click tab and verify focus is managed
      await user.click(ratingsTab);
      
      // Focus should remain on tab or move to content appropriately
      expect(screen.getByText('Recent Ratings')).toBeInTheDocument();
    });

    it('maintains focus when filtering results', async () => {
      render(<VendorDashboard />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      });

      const filterSelect = screen.getByDisplayValue('All Vendors');
      filterSelect.focus();
      
      await user.selectOptions(filterSelect, 'A');
      
      // Focus should remain on the filter select
      expect(document.activeElement).toBe(filterSelect);
    });

    it('provides skip links for keyboard users', async () => {
      render(<VendorDashboard />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      });

      // Check if skip links are available (implementation dependent)
      // const skipLink = screen.queryByText(/skip to main content/i);
      // if (skipLink) {
      //   expect(skipLink).toBeInTheDocument();
      // }
    });
  });

  // ===== ERROR STATE ACCESSIBILITY =====

  describe('Error State Accessibility', () => {
    it('announces errors appropriately to screen readers', async () => {
      // Mock console.error to avoid test noise
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      server.use(
        ...createVendorHandlersWithData({
          dashboardSummary: null as any
        })
      );

      render(<VendorDashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('Error Loading Dashboard')).toBeInTheDocument();
      });

      // Error message should be properly announced
      const errorHeading = screen.getByRole('heading', { name: /error loading dashboard/i });
      expect(errorHeading).toBeInTheDocument();

      // Retry button should be accessible
      const retryButton = screen.getByRole('button', { name: /try again/i });
      expect(retryButton).toBeInTheDocument();

      consoleSpy.mockRestore();
    });
  });

  // ===== MOBILE ACCESSIBILITY =====

  describe('Mobile Accessibility', () => {
    it('maintains accessibility on mobile viewports', async () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });
      window.dispatchEvent(new Event('resize'));

      const { container } = render(<VendorDashboard />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      });

      // Run accessibility tests on mobile layout
      const results = await axe(container);
      expect(results).toHaveNoViolations();

      // Touch targets should be large enough (this is more of a design concern)
      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(button).toBeInTheDocument();
        // In a real implementation, you might check minimum touch target sizes
      });
    });
  });

  // ===== LIVE REGIONS AND ANNOUNCEMENTS =====

  describe('Live Regions', () => {
    it('announces search results changes', async () => {
      render(<VendorDashboard />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      });

      const searchInput = screen.getByPlaceholderText('Search vendors...');
      
      // Search for specific vendor
      await user.type(searchInput, 'Pacific');
      
      // Results should be visible
      expect(screen.getByText('Pacific Seafood Supply')).toBeInTheDocument();
      expect(screen.queryByText('Alaska Premium Fish')).not.toBeInTheDocument();

      // In a full implementation, you might have an aria-live region announcing result counts
    });

    it('announces loading states appropriately', async () => {
      render(<VendorReportCard vendorId="vendor-001" />);
      
      // Loading message should be announced
      expect(screen.getByText('Loading vendor report card...')).toBeInTheDocument();
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor report card...')).not.toBeInTheDocument();
      });

      // Content should be available to screen readers
      expect(screen.getByText('Pacific Seafood Supply')).toBeInTheDocument();
    });
  });

  // ===== COMPLEX WIDGET ACCESSIBILITY =====

  describe('Complex Widget Accessibility', () => {
    it('makes data tables accessible', async () => {
      render(<VendorDashboard />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      });

      // Vendor list should be structured accessibly
      // If it's a table, it should have proper headers
      // If it's a list, it should use proper list semantics
      
      const vendorNames = screen.getAllByText(/Seafood Supply|Premium Fish|Imports LLC/);
      expect(vendorNames.length).toBeGreaterThan(0);

      // Each vendor entry should be accessible
      vendorNames.forEach(name => {
        expect(name).toBeInTheDocument();
      });
    });

    it('makes progress indicators accessible', async () => {
      render(<VendorReportCard vendorId="vendor-001" />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor report card...')).not.toBeInTheDocument();
      });

      // Navigate to overview tab to see progress bars
      expect(screen.getByText('Financial Performance')).toBeInTheDocument();

      // Progress bars should have appropriate labels and values
      // In a full implementation, progress elements should have proper ARIA attributes
    });
  });
});