/**
 * Test Configuration Validation
 * Verifies that all test setup and configuration works correctly
 */

import { describe, it, expect, vi } from 'vitest';
import { TEST_CONFIG } from '../test-config';
import { VOICE_TEST_CONFIG } from '../voice-test-config';

describe('Test Configuration Validation', () => {
  describe('Global Mocks', () => {
    it('should have File API mocked', () => {
      expect(global.File).toBeDefined();
      expect(typeof global.File).toBe('function');
      
      const testFile = new File(['test content'], 'test.txt', { type: 'text/plain' });
      expect(testFile.name).toBe('test.txt');
      expect(testFile.type).toBe('text/plain');
    });

    it('should have FileReader API mocked', () => {
      expect(global.FileReader).toBeDefined();
      expect(typeof global.FileReader).toBe('function');
      
      const reader = new FileReader();
      expect(reader.readAsText).toBeDefined();
      expect(reader.readyState).toBe(0);
    });

    it('should have IntersectionObserver mocked', () => {
      expect(global.IntersectionObserver).toBeDefined();
      expect(typeof global.IntersectionObserver).toBe('function');
      
      const observer = new IntersectionObserver(() => {});
      expect(observer.observe).toBeDefined();
      expect(observer.disconnect).toBeDefined();
    });

    it('should have Speech Recognition mocked', () => {
      expect(global.SpeechRecognition).toBeDefined();
      expect(global.webkitSpeechRecognition).toBeDefined();
      
      const recognition = new SpeechRecognition();
      expect(recognition.start).toBeDefined();
      expect(recognition.stop).toBeDefined();
    });

    it('should have fetch mocked', () => {
      expect(global.fetch).toBeDefined();
      expect(typeof global.fetch).toBe('function');
    });
  });

  describe('Test Configurations', () => {
    it('should have valid TEST_CONFIG structure', () => {
      expect(TEST_CONFIG).toBeDefined();
      expect(TEST_CONFIG.timeouts).toBeDefined();
      expect(TEST_CONFIG.performance).toBeDefined();
      expect(TEST_CONFIG.database).toBeDefined();
      expect(TEST_CONFIG.voice).toBeDefined();
      expect(TEST_CONFIG.import).toBeDefined();
      expect(TEST_CONFIG.security).toBeDefined();
    });

    it('should have valid VOICE_TEST_CONFIG structure', () => {
      expect(VOICE_TEST_CONFIG).toBeDefined();
      expect(VOICE_TEST_CONFIG.environment).toBeDefined();
      expect(VOICE_TEST_CONFIG.performance).toBeDefined();
      expect(VOICE_TEST_CONFIG.accuracy).toBeDefined();
      expect(VOICE_TEST_CONFIG.testData).toBeDefined();
      expect(VOICE_TEST_CONFIG.browsers).toBeDefined();
    });

    it('should have reasonable timeout values', () => {
      expect(TEST_CONFIG.timeouts.unit).toBeLessThan(TEST_CONFIG.timeouts.integration);
      expect(TEST_CONFIG.timeouts.integration).toBeLessThan(TEST_CONFIG.timeouts.e2e);
      expect(VOICE_TEST_CONFIG.environment.testTimeout).toBeGreaterThan(5000);
    });

    it('should have valid performance thresholds', () => {
      expect(TEST_CONFIG.performance.api.fast).toBeLessThan(TEST_CONFIG.performance.api.standard);
      expect(TEST_CONFIG.performance.api.standard).toBeLessThan(TEST_CONFIG.performance.api.bulk);
      expect(VOICE_TEST_CONFIG.accuracy.minOverallConfidence).toBeGreaterThan(0.5);
    });
  });

  describe('Environment Variables', () => {
    it('should have test environment variables set', () => {
      expect(process.env.VITE_SUPABASE_URL).toBe('https://test.supabase.co');
      expect(process.env.VITE_SUPABASE_ANON_KEY).toBe('test-anon-key');
      expect(process.env.VITE_OPENAI_API_KEY).toBe('test-openai-key');
    });
  });

  describe('Mock Utilities', () => {
    it('should provide audio blob creation utility', () => {
      const audioBlob = new Blob(['mock audio data'], { type: 'audio/webm' });
      expect(audioBlob).toBeInstanceOf(Blob);
      expect(audioBlob.type).toBe('audio/webm');
    });

    it('should handle localStorage mocking', () => {
      expect(localStorage.clear).toBeDefined();
      expect(localStorage.getItem).toBeDefined();
      expect(localStorage.setItem).toBeDefined();
    });

    it('should handle URL mocking', () => {
      expect(global.URL.createObjectURL).toBeDefined();
      expect(global.URL.revokeObjectURL).toBeDefined();
      expect(vi.isMockFunction(global.URL.createObjectURL)).toBe(true);
    });
  });

  describe('MSW Server', () => {
    it('should have MSW server configured', () => {
      // Basic test to ensure MSW is set up - server should be defined
      expect(typeof fetch).toBe('function');
    });
  });
});