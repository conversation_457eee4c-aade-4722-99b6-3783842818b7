/**
 * Integration tests for vendor dashboard data flow and component interactions
 * Tests the complete data flow from API to UI components
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { server } from '../../setup';

import VendorDashboard from '../../../components/vendors/VendorDashboard';
import VendorReportCard from '../../../components/vendors/VendorReportCard';
import { vendorAPI } from '../../../lib/vendor-api';
import { 
  vendorHandlers,
  vendorErrorHandlers,
  createVendorHandlersWithData,
  vendorEmptyHandlers,
  vendorLoadingHandlers
} from '../../mocks/vendor-handlers';
import {
  mockVendorDashboardSummary,
  mockVendorMetrics,
  mockVendorRatings,
  mockVendorCompliance,
  mockVendorPerformanceAlerts,
  mockVendorInteractions,
  mockVendor,
  generateVendorDashboardSummary,
  generateVendorMetrics,
  generateVendorAlert
} from '../../mocks/vendor-data';

// Create a wrapper component that manages the vendor dashboard state
function VendorDashboardWrapper() {
  const [selectedVendorId, setSelectedVendorId] = React.useState<string | null>(null);

  if (selectedVendorId) {
    return (
      <VendorReportCard 
        vendorId={selectedVendorId} 
        onClose={() => setSelectedVendorId(null)}
      />
    );
  }

  return <VendorDashboard />;
}

describe('Vendor Dashboard Integration Tests', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();
    server.use(...vendorHandlers);
  });

  afterEach(() => {
    server.resetHandlers();
  });

  // ===== COMPLETE DATA FLOW TESTS =====

  describe('Complete Data Flow', () => {
    it('loads dashboard data and displays all components correctly', async () => {
      render(<VendorDashboard />);

      // 1. Should show loading state initially
      expect(screen.getByText('Loading vendor dashboard...')).toBeInTheDocument();

      // 2. Wait for data to load and verify summary cards
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      });

      // 3. Verify performance summary cards are populated
      expect(screen.getByText('3')).toBeInTheDocument(); // Total vendors
      expect(screen.getByText('1')).toBeInTheDocument(); // A-grade vendors

      // 4. Verify grade distribution is calculated correctly
      const gradeASection = screen.getByText('Grade A').closest('div');
      expect(within(gradeASection!).getByText('1')).toBeInTheDocument();

      // 5. Verify vendor list displays with proper data
      expect(screen.getByText('Pacific Seafood Supply')).toBeInTheDocument();
      expect(screen.getByText('Alaska Premium Fish')).toBeInTheDocument();
      expect(screen.getByText('Atlantic Imports LLC')).toBeInTheDocument();

      // 6. Verify performance metrics are displayed
      const pacificVendor = screen.getByText('Pacific Seafood Supply').closest('.p-6');
      expect(within(pacificVendor!).getByText('96.5%')).toBeInTheDocument(); // Completion rate
      expect(within(pacificVendor!).getByText('94.2%')).toBeInTheDocument(); // On-time rate
    });

    it('handles real-time data updates correctly', async () => {
      render(<VendorDashboard />);

      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByText('Pacific Seafood Supply')).toBeInTheDocument();
      });

      // Simulate data update with new vendor
      const updatedSummary = [
        ...mockVendorDashboardSummary,
        generateVendorDashboardSummary({
          vendor_id: 'vendor-004',
          vendor_name: 'New Seafood Co.',
          overall_letter_grade: 'A'
        })
      ];

      server.use(
        ...createVendorHandlersWithData({
          dashboardSummary: updatedSummary
        })
      );

      // Click refresh
      const refreshButton = screen.getByRole('button', { name: /refresh data/i });
      await user.click(refreshButton);

      // Verify new data is displayed
      await waitFor(() => {
        expect(screen.getByText('New Seafood Co.')).toBeInTheDocument();
        expect(screen.getByText('4')).toBeInTheDocument(); // Updated total count
      });
    });
  });

  // ===== COMPONENT INTERACTION TESTS =====

  describe('Component Interactions', () => {
    it('navigates from dashboard to report card and back', async () => {
      render(<VendorDashboardWrapper />);

      // Wait for dashboard to load
      await waitFor(() => {
        expect(screen.getByText('Vendor Performance Dashboard')).toBeInTheDocument();
      });

      // Click "View Report" for the first vendor
      const viewReportButtons = screen.getAllByText('View Report');
      await user.click(viewReportButtons[0]);

      // Should navigate to report card
      await waitFor(() => {
        expect(screen.getByText('Loading vendor report card...')).toBeInTheDocument();
      });

      // Wait for report card to load
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor report card...')).not.toBeInTheDocument();
        expect(screen.getByText('Pacific Seafood Supply')).toBeInTheDocument();
        expect(screen.getByText('Sarah Johnson')).toBeInTheDocument();
      });

      // Click close button to go back
      const closeButton = screen.getByRole('button', { name: '✕' });
      await user.click(closeButton);

      // Should be back at dashboard
      expect(screen.getByText('Vendor Performance Dashboard')).toBeInTheDocument();
    });

    it('maintains filter state during navigation', async () => {
      render(<VendorDashboard />);

      // Wait for dashboard to load
      await waitFor(() => {
        expect(screen.getByText('Alaska Premium Fish')).toBeInTheDocument();
      });

      // Apply a filter
      const filterSelect = screen.getByDisplayValue('All Vendors');
      await user.selectOptions(filterSelect, 'A');

      // Only Grade A vendor should be visible
      expect(screen.getByText('Pacific Seafood Supply')).toBeInTheDocument();
      expect(screen.queryByText('Alaska Premium Fish')).not.toBeInTheDocument();

      // Filter state should persist after component updates
      const refreshButton = screen.getByRole('button', { name: /refresh data/i });
      await user.click(refreshButton);

      await waitFor(() => {
        // Filter should still be applied
        expect(screen.getByText('Pacific Seafood Supply')).toBeInTheDocument();
        expect(screen.queryByText('Alaska Premium Fish')).not.toBeInTheDocument();
      });
    });

    it('synchronizes search and filter interactions', async () => {
      render(<VendorDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Alaska Premium Fish')).toBeInTheDocument();
      });

      // Search for a vendor
      const searchInput = screen.getByPlaceholderText('Search vendors...');
      await user.type(searchInput, 'Pacific');

      // Only Pacific vendor should be visible
      expect(screen.getByText('Pacific Seafood Supply')).toBeInTheDocument();
      expect(screen.queryByText('Alaska Premium Fish')).not.toBeInTheDocument();

      // Apply a filter that would exclude the searched vendor
      const filterSelect = screen.getByDisplayValue('All Vendors');
      await user.selectOptions(filterSelect, 'B');

      // Should show no results (Pacific is Grade A, filter is Grade B)
      expect(screen.getByText('No vendors match your filters')).toBeInTheDocument();

      // Clear search
      await user.clear(searchInput);

      // Should now show Grade B vendors
      expect(screen.getByText('Alaska Premium Fish')).toBeInTheDocument();
      expect(screen.queryByText('Pacific Seafood Supply')).not.toBeInTheDocument();
    });
  });

  // ===== ERROR RECOVERY TESTS =====

  describe('Error Recovery and Resilience', () => {
    it('recovers from initial load errors', async () => {
      // Start with error state
      server.use(...vendorErrorHandlers);
      
      render(<VendorDashboard />);

      // Should show error state
      await waitFor(() => {
        expect(screen.getByText('Error Loading Dashboard')).toBeInTheDocument();
      });

      // Fix the API
      server.use(...vendorHandlers);

      // Click retry
      const retryButton = screen.getByRole('button', { name: /try again/i });
      await user.click(retryButton);

      // Should recover and show data
      await waitFor(() => {
        expect(screen.queryByText('Error Loading Dashboard')).not.toBeInTheDocument();
        expect(screen.getByText('Pacific Seafood Supply')).toBeInTheDocument();
      });
    });

    it('handles partial data loading gracefully', async () => {
      // Create scenario where some data loads but others fail
      server.use(
        ...createVendorHandlersWithData({
          dashboardSummary: mockVendorDashboardSummary.slice(0, 1) // Only one vendor
        })
      );

      render(<VendorDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Pacific Seafood Supply')).toBeInTheDocument();
      });

      // Should display available data
      expect(screen.getByText('1')).toBeInTheDocument(); // Total vendors
      expect(screen.queryByText('Alaska Premium Fish')).not.toBeInTheDocument();

      // Performance cards should calculate correctly with partial data
      const avgCompletionCard = screen.getByText('Avg Completion Rate').closest('div');
      expect(within(avgCompletionCard!).getByText('96.5%')).toBeInTheDocument();
    });

    it('handles network interruptions during user interactions', async () => {
      render(<VendorDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Pacific Seafood Supply')).toBeInTheDocument();
      });

      // Simulate network error for refresh
      server.use(...vendorErrorHandlers);

      const refreshButton = screen.getByRole('button', { name: /refresh data/i });
      await user.click(refreshButton);

      // Should maintain current data when refresh fails
      expect(screen.getByText('Pacific Seafood Supply')).toBeInTheDocument();
      
      // Note: In a real implementation, you might show a toast or error indicator
      // while keeping the existing data visible
    });
  });

  // ===== PERFORMANCE INTEGRATION TESTS =====

  describe('Performance Integration', () => {
    it('handles large datasets efficiently', async () => {
      // Create large dataset
      const largeVendorList = Array.from({ length: 100 }, (_, i) => 
        generateVendorDashboardSummary({
          vendor_id: `vendor-${i}`,
          vendor_name: `Vendor ${i}`,
          overall_letter_grade: ['A', 'B', 'C', 'D', 'F'][i % 5] as any
        })
      );

      server.use(
        ...createVendorHandlersWithData({
          dashboardSummary: largeVendorList
        })
      );

      const startTime = performance.now();
      render(<VendorDashboard />);

      await waitFor(() => {
        expect(screen.getByText('100')).toBeInTheDocument(); // Total vendors
      });

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // Should render large dataset within reasonable time (< 2 seconds)
      expect(renderTime).toBeLessThan(2000);

      // Verify search still works efficiently with large dataset
      const searchInput = screen.getByPlaceholderText('Search vendors...');
      await user.type(searchInput, 'Vendor 50');

      await waitFor(() => {
        expect(screen.getByText('Vendor 50')).toBeInTheDocument();
      });

      // Should filter results correctly
      expect(screen.queryByText('Vendor 51')).not.toBeInTheDocument();
    });

    it('performs efficient filtering and sorting operations', async () => {
      render(<VendorDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Pacific Seafood Supply')).toBeInTheDocument();
      });

      // Test rapid filter changes
      const filterSelect = screen.getByDisplayValue('All Vendors');
      const sortSelect = screen.getByDisplayValue('Grade');

      // Rapidly change filters and sorts
      await user.selectOptions(filterSelect, 'A');
      await user.selectOptions(sortSelect, 'name');
      await user.selectOptions(filterSelect, 'B');
      await user.selectOptions(sortSelect, 'completion_rate');

      // Should handle rapid changes without crashes
      await waitFor(() => {
        expect(screen.getByText('Alaska Premium Fish')).toBeInTheDocument();
      });
    });
  });

  // ===== REAL-TIME UPDATE TESTS =====

  describe('Real-Time Data Updates', () => {
    it('updates metrics when vendor performance changes', async () => {
      render(<VendorDashboard />);

      await waitFor(() => {
        expect(screen.getByText('1')).toBeInTheDocument(); // Initial A-grade count
      });

      // Simulate vendor performance improvement
      const updatedVendors = mockVendorDashboardSummary.map(vendor => 
        vendor.vendor_id === 'vendor-002' 
          ? { ...vendor, overall_letter_grade: 'A' as const }
          : vendor
      );

      server.use(
        ...createVendorHandlersWithData({
          dashboardSummary: updatedVendors
        })
      );

      // Refresh data
      const refreshButton = screen.getByRole('button', { name: /refresh data/i });
      await user.click(refreshButton);

      await waitFor(() => {
        // A-grade count should increase to 2
        const aGradeCard = screen.getByText('A-Grade Vendors').closest('div');
        expect(within(aGradeCard!).getByText('2')).toBeInTheDocument();
      });
    });

    it('updates alert counts when alerts are resolved', async () => {
      render(<VendorDashboard />);

      await waitFor(() => {
        const totalAlerts = mockVendorDashboardSummary.reduce(
          (sum, vendor) => sum + (vendor.active_alerts_count || 0), 0
        );
        expect(screen.getByText(totalAlerts.toString())).toBeInTheDocument();
      });

      // Simulate alert resolution
      const updatedVendors = mockVendorDashboardSummary.map(vendor => ({
        ...vendor,
        active_alerts_count: 0
      }));

      server.use(
        ...createVendorHandlersWithData({
          dashboardSummary: updatedVendors
        })
      );

      const refreshButton = screen.getByRole('button', { name: /refresh data/i });
      await user.click(refreshButton);

      await waitFor(() => {
        const alertsCard = screen.getByText('Active Alerts').closest('div');
        expect(within(alertsCard!).getByText('0')).toBeInTheDocument();
      });
    });
  });

  // ===== ACCESSIBILITY INTEGRATION TESTS =====

  describe('Accessibility Integration', () => {
    it('maintains focus management during navigation', async () => {
      render(<VendorDashboardWrapper />);

      await waitFor(() => {
        expect(screen.getByText('Vendor Performance Dashboard')).toBeInTheDocument();
      });

      // Focus on first "View Report" button
      const viewReportButtons = screen.getAllByText('View Report');
      viewReportButtons[0].focus();
      expect(document.activeElement).toBe(viewReportButtons[0]);

      // Navigate to report card
      await user.click(viewReportButtons[0]);

      await waitFor(() => {
        expect(screen.queryByText('Vendor Performance Dashboard')).not.toBeInTheDocument();
      });

      // Focus should be managed appropriately in report card
      const closeButton = screen.getByRole('button', { name: '✕' });
      expect(closeButton).toBeInTheDocument();
    });

    it('provides proper screen reader announcements', async () => {
      render(<VendorDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Pacific Seafood Supply')).toBeInTheDocument();
      });

      // Search functionality should be accessible
      const searchInput = screen.getByPlaceholderText('Search vendors...');
      expect(searchInput).toHaveAttribute('type', 'text');
      
      // Filter and sort controls should have proper labels
      const sortSelect = screen.getByDisplayValue('Grade');
      expect(sortSelect).toBeInTheDocument();
      
      const filterSelect = screen.getByDisplayValue('All Vendors');
      expect(filterSelect).toBeInTheDocument();

      // Performance metrics should be readable
      expect(screen.getByText('Total Vendors')).toBeInTheDocument();
      expect(screen.getByText('A-Grade Vendors')).toBeInTheDocument();
    });
  });

  // ===== DATA CONSISTENCY TESTS =====

  describe('Data Consistency', () => {
    it('maintains data consistency across components', async () => {
      render(<VendorDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Pacific Seafood Supply')).toBeInTheDocument();
      });

      // Count vendors in the list
      const vendorElements = screen.getAllByText(/Seafood Supply|Premium Fish|Imports LLC/);
      const vendorCount = vendorElements.length;

      // Should match total vendors count in summary card
      const totalVendorsCard = screen.getByText('Total Vendors').closest('div');
      expect(within(totalVendorsCard!).getByText(vendorCount.toString())).toBeInTheDocument();

      // Grade distribution should add up to total
      const gradeStats = { A: 1, B: 1, C: 0, D: 1, F: 0 };
      const totalFromGrades = Object.values(gradeStats).reduce((sum, count) => sum + count, 0);
      expect(totalFromGrades).toBe(vendorCount);
    });

    it('validates calculated metrics accuracy', async () => {
      render(<VendorDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Pacific Seafood Supply')).toBeInTheDocument();
      });

      // Calculate expected average completion rate
      const expectedAverage = mockVendorDashboardSummary
        .reduce((sum, vendor) => sum + (vendor.completion_rate || 0), 0) / mockVendorDashboardSummary.length;

      const avgCompletionCard = screen.getByText('Avg Completion Rate').closest('div');
      expect(within(avgCompletionCard!).getByText(`${expectedAverage.toFixed(1)}%`)).toBeInTheDocument();

      // Total alerts should sum correctly
      const expectedAlerts = mockVendorDashboardSummary
        .reduce((sum, vendor) => sum + (vendor.active_alerts_count || 0), 0);

      const alertsCard = screen.getByText('Active Alerts').closest('div');
      expect(within(alertsCard!).getByText(expectedAlerts.toString())).toBeInTheDocument();
    });
  });
});