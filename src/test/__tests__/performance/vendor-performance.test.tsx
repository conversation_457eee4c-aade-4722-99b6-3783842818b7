/**
 * Performance tests for vendor components
 * Tests rendering performance, memory usage, and optimization effectiveness
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { server } from '../../setup';

import VendorDashboard from '../../../components/vendors/VendorDashboard';
import VendorReportCard from '../../../components/vendors/VendorReportCard';
import { 
  vendorHandlers,
  createVendorHandlersWithData 
} from '../../mocks/vendor-handlers';
import { 
  generateVendorDashboardSummary,
  generateVendorMetrics 
} from '../../mocks/vendor-data';

// Performance measurement utilities
function measureRenderTime(renderFn: () => void): number {
  const startTime = performance.now();
  renderFn();
  const endTime = performance.now();
  return endTime - startTime;
}

function measureMemoryUsage(): number {
  if ('memory' in performance) {
    return (performance as any).memory.usedJSHeapSize;
  }
  return 0; // Fallback if memory API not available
}

// Mock console methods to capture performance warnings
const mockConsoleWarn = vi.spyOn(console, 'warn').mockImplementation(() => {});
const mockConsoleError = vi.spyOn(console, 'error').mockImplementation(() => {});

describe('Vendor Components Performance Tests', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();
    server.use(...vendorHandlers);
    mockConsoleWarn.mockClear();
    mockConsoleError.mockClear();
  });

  afterEach(() => {
    server.resetHandlers();
  });

  // ===== RENDERING PERFORMANCE TESTS =====

  describe('Rendering Performance', () => {
    it('renders VendorDashboard within acceptable time limits', async () => {
      const renderTime = measureRenderTime(() => {
        render(<VendorDashboard />);
      });

      // Should render initial component within 100ms
      expect(renderTime).toBeLessThan(100);

      // Wait for data loading and measure total time to interactive
      const startTime = performance.now();
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      });
      const totalTime = performance.now() - startTime;

      // Total time to interactive should be under 2 seconds
      expect(totalTime).toBeLessThan(2000);
    });

    it('renders VendorReportCard within acceptable time limits', async () => {
      const renderTime = measureRenderTime(() => {
        render(<VendorReportCard vendorId="vendor-001" />);
      });

      // Initial render should be fast
      expect(renderTime).toBeLessThan(100);

      // Wait for data loading
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor report card...')).not.toBeInTheDocument();
      });

      // No console errors should be logged during render
      expect(mockConsoleError).not.toHaveBeenCalled();
    });

    it('handles large datasets efficiently', async () => {
      // Create large dataset (1000 vendors)
      const largeDataset = Array.from({ length: 1000 }, (_, i) => 
        generateVendorDashboardSummary({
          vendor_id: `vendor-${i}`,
          vendor_name: `Vendor ${i}`,
          overall_letter_grade: ['A', 'B', 'C', 'D', 'F'][i % 5] as any
        })
      );

      server.use(
        ...createVendorHandlersWithData({
          dashboardSummary: largeDataset
        })
      );

      const initialMemory = measureMemoryUsage();
      
      const renderTime = measureRenderTime(() => {
        render(<VendorDashboard />);
      });

      // Even with large dataset, initial render should be fast
      expect(renderTime).toBeLessThan(200);

      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      });

      // Memory usage should not increase dramatically
      const finalMemory = measureMemoryUsage();
      if (finalMemory > 0 && initialMemory > 0) {
        const memoryIncrease = finalMemory - initialMemory;
        // Should not use more than 10MB additional memory
        expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024);
      }

      // Should display correct total count
      expect(screen.getByText('1000')).toBeInTheDocument();
    });
  });

  // ===== INTERACTION PERFORMANCE TESTS =====

  describe('Interaction Performance', () => {
    beforeEach(async () => {
      render(<VendorDashboard />);
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      });
    });

    it('performs search operations efficiently', async () => {
      const searchInput = screen.getByPlaceholderText('Search vendors...');
      
      const startTime = performance.now();
      await user.type(searchInput, 'Pacific');
      const endTime = performance.now();

      const searchTime = endTime - startTime;
      // Search should respond within 500ms
      expect(searchTime).toBeLessThan(500);

      // Results should be filtered correctly
      expect(screen.getByText('Pacific Seafood Supply')).toBeInTheDocument();
      expect(screen.queryByText('Alaska Premium Fish')).not.toBeInTheDocument();
    });

    it('performs filtering operations efficiently', async () => {
      const filterSelect = screen.getByDisplayValue('All Vendors');
      
      const startTime = performance.now();
      await user.selectOptions(filterSelect, 'A');
      const endTime = performance.now();

      const filterTime = endTime - startTime;
      // Filtering should be nearly instantaneous
      expect(filterTime).toBeLessThan(100);

      // Only Grade A vendors should be visible
      expect(screen.getByText('Pacific Seafood Supply')).toBeInTheDocument();
    });

    it('performs sorting operations efficiently', async () => {
      const sortSelect = screen.getByDisplayValue('Grade');
      
      const startTime = performance.now();
      await user.selectOptions(sortSelect, 'name');
      const endTime = performance.now();

      const sortTime = endTime - startTime;
      // Sorting should be fast
      expect(sortTime).toBeLessThan(100);

      // Results should be sorted alphabetically
      const vendorElements = screen.getAllByText(/Seafood Supply|Premium Fish|Imports LLC/);
      expect(vendorElements[0]).toHaveTextContent('Alaska Premium Fish');
    });

    it('handles rapid consecutive operations without performance degradation', async () => {
      const searchInput = screen.getByPlaceholderText('Search vendors...');
      const filterSelect = screen.getByDisplayValue('All Vendors');
      const sortSelect = screen.getByDisplayValue('Grade');

      const startTime = performance.now();

      // Perform rapid consecutive operations
      await user.type(searchInput, 'a');
      await user.clear(searchInput);
      await user.selectOptions(filterSelect, 'A');
      await user.selectOptions(filterSelect, 'all');
      await user.selectOptions(sortSelect, 'name');
      await user.selectOptions(sortSelect, 'grade');

      const endTime = performance.now();
      const totalTime = endTime - startTime;

      // All operations should complete within 1 second
      expect(totalTime).toBeLessThan(1000);

      // Component should still be functional
      expect(screen.getByText('Pacific Seafood Supply')).toBeInTheDocument();
    });
  });

  // ===== TAB SWITCHING PERFORMANCE =====

  describe('Tab Switching Performance', () => {
    it('switches between report card tabs efficiently', async () => {
      render(<VendorReportCard vendorId="vendor-001" />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor report card...')).not.toBeInTheDocument();
      });

      const ratingsTab = screen.getByRole('button', { name: /ratings/i });
      const complianceTab = screen.getByRole('button', { name: /compliance/i });
      const alertsTab = screen.getByRole('button', { name: /alerts/i });
      const overviewTab = screen.getByRole('button', { name: /overview/i });

      const startTime = performance.now();

      // Rapidly switch between tabs
      await user.click(ratingsTab);
      await user.click(complianceTab);
      await user.click(alertsTab);
      await user.click(overviewTab);

      const endTime = performance.now();
      const switchTime = endTime - startTime;

      // Tab switching should be fast
      expect(switchTime).toBeLessThan(200);

      // Should end up on overview tab
      expect(screen.getByText('Financial Performance')).toBeInTheDocument();
    });
  });

  // ===== MEMORY LEAK TESTS =====

  describe('Memory Management', () => {
    it('does not create memory leaks with repeated renders', async () => {
      const initialMemory = measureMemoryUsage();
      
      // Render and unmount component multiple times
      for (let i = 0; i < 10; i++) {
        const { unmount } = render(<VendorDashboard />);
        await waitFor(() => {
          expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
        });
        unmount();
      }

      // Trigger garbage collection if available
      if (global.gc) {
        global.gc();
      }

      const finalMemory = measureMemoryUsage();
      
      if (finalMemory > 0 && initialMemory > 0) {
        const memoryIncrease = finalMemory - initialMemory;
        // Memory increase should be minimal (less than 5MB)
        expect(memoryIncrease).toBeLessThan(5 * 1024 * 1024);
      }
    });

    it('cleans up event listeners and timers', async () => {
      const { unmount } = render(<VendorDashboard />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      });

      // Unmount component
      unmount();

      // No console warnings should be logged about unmounted components
      expect(mockConsoleWarn).not.toHaveBeenCalledWith(
        expect.stringMatching(/memory leak|unmounted component/i)
      );
    });
  });

  // ===== COMPONENT UPDATE PERFORMANCE =====

  describe('Component Update Performance', () => {
    it('optimizes re-renders when props change', async () => {
      let renderCount = 0;
      const TestWrapper = ({ vendorId }: { vendorId: string }) => {
        renderCount++;
        return <VendorReportCard vendorId={vendorId} />;
      };

      const { rerender } = render(<TestWrapper vendorId="vendor-001" />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor report card...')).not.toBeInTheDocument();
      });

      const initialRenderCount = renderCount;

      // Change props - should trigger re-render
      rerender(<TestWrapper vendorId="vendor-002" />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor report card...')).not.toBeInTheDocument();
      });

      // Should have re-rendered, but efficiently
      expect(renderCount).toBeGreaterThan(initialRenderCount);
      expect(renderCount - initialRenderCount).toBeLessThan(5); // Reasonable number of re-renders
    });

    it('avoids unnecessary re-renders with stable data', async () => {
      const stableData = generateVendorDashboardSummary();
      
      server.use(
        ...createVendorHandlersWithData({
          dashboardSummary: [stableData]
        })
      );

      const { rerender } = render(<VendorDashboard />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      });

      // Re-render with same props
      rerender(<VendorDashboard />);

      // Should not cause performance issues
      expect(screen.getByText(stableData.vendor_name!)).toBeInTheDocument();
    });
  });

  // ===== VIRTUAL SCROLLING PERFORMANCE =====

  describe('Large List Performance', () => {
    it('handles very large vendor lists efficiently', async () => {
      // Create extremely large dataset (10,000 vendors)
      const massiveDataset = Array.from({ length: 10000 }, (_, i) => 
        generateVendorDashboardSummary({
          vendor_id: `vendor-${i}`,
          vendor_name: `Vendor ${String(i).padStart(5, '0')}`,
          overall_letter_grade: ['A', 'B', 'C', 'D', 'F'][i % 5] as any
        })
      );

      server.use(
        ...createVendorHandlersWithData({
          dashboardSummary: massiveDataset
        })
      );

      const startTime = performance.now();
      render(<VendorDashboard />);

      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      }, { timeout: 10000 });

      const endTime = performance.now();
      const loadTime = endTime - startTime;

      // Should load even massive datasets within reasonable time (10 seconds)
      expect(loadTime).toBeLessThan(10000);

      // Should display correct count
      expect(screen.getByText('10000')).toBeInTheDocument();

      // Search should still work efficiently
      const searchInput = screen.getByPlaceholderText('Search vendors...');
      const searchStartTime = performance.now();
      await user.type(searchInput, 'Vendor 05000');
      const searchEndTime = performance.now();

      expect(searchEndTime - searchStartTime).toBeLessThan(1000);
      expect(screen.getByText('Vendor 05000')).toBeInTheDocument();
    });
  });

  // ===== CONCURRENT OPERATIONS PERFORMANCE =====

  describe('Concurrent Operations', () => {
    it('handles multiple simultaneous operations efficiently', async () => {
      render(<VendorDashboard />);
      
      await waitFor(() => {
        expect(screen.queryByText('Loading vendor dashboard...')).not.toBeInTheDocument();
      });

      const searchInput = screen.getByPlaceholderText('Search vendors...');
      const filterSelect = screen.getByDisplayValue('All Vendors');
      const refreshButton = screen.getByRole('button', { name: /refresh data/i });

      const startTime = performance.now();

      // Perform multiple operations concurrently
      const operations = Promise.all([
        user.type(searchInput, 'Pacific'),
        user.selectOptions(filterSelect, 'A'),
        user.click(refreshButton)
      ]);

      await operations;

      const endTime = performance.now();
      const concurrentTime = endTime - startTime;

      // Concurrent operations should complete within 2 seconds
      expect(concurrentTime).toBeLessThan(2000);

      // Results should be consistent
      expect(screen.getByText('Pacific Seafood Supply')).toBeInTheDocument();
    });
  });
});