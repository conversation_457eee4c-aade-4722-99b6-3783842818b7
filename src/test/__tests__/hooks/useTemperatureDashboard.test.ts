/**
 * Comprehensive Test Suite for useTemperatureDashboard Hook
 * 
 * Tests the core dashboard data management logic including:
 * - Data fetching and transformation
 * - Real-time updates and subscriptions
 * - Filter state management
 * - Auto-refresh functionality
 * - Error handling and recovery
 * - Data mode switching (mock vs real)
 * - Performance optimizations
 */

import { renderHook, act, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { useTemperatureDashboard } from '@/hooks/useTemperatureDashboard';
import { tempStickService } from '@/lib/tempstick-service';
import { supabase } from '@/lib/supabase';
import type { DashboardFilters } from '@/hooks/useTemperatureDashboard';

// Mock dependencies
vi.mock('@/lib/tempstick-service');
vi.mock('@/lib/supabase');
// Mock file removed - tests now use service stubs

// Mock timers for auto-refresh testing
vi.useFakeTimers();

describe('useTemperatureDashboard Hook', () => {
  const mockTempStickService = vi.mocked(tempStickService);
  const mockSupabase = vi.mocked(supabase);

  // Mock data - create simple test data instead of using deleted mock generators
  const mockSensorStatuses = [
    {
      sensor: { id: 'sensor-1', name: 'Test Sensor 1', location: 'Test Location' },
      latestReading: { temperature: 35, humidity: 60, reading_timestamp: new Date().toISOString() },
      status: 'online' as const,
      activeAlerts: [],
      lastSyncTime: new Date().toISOString(),
      batteryLevel: 85,
      signalStrength: 90
    }
  ];
  const mockDashboardSummary = {
    totalSensors: 1,
    onlineSensors: 1,
    offlineSensors: 0,
    activeAlerts: 0,
    criticalAlerts: 0,
    averageTemperature: 35,
    lastUpdated: new Date().toISOString()
  };
  const mockTemperatureTrends = [
    {
      timestamp: new Date().toISOString(),
      temperature: 35,
      humidity: 60,
      sensorId: 'sensor-1',
      sensorName: 'Test Sensor 1',
      alertLevel: 'normal' as const
    }
  ];

  // Mock Supabase response structure
  const mockSupabaseFrom = {
    select: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    gte: vi.fn().mockReturnThis(),
    lte: vi.fn().mockReturnThis(),
    order: vi.fn().mockReturnThis(),
    limit: vi.fn().mockReturnThis(),
    in: vi.fn().mockReturnThis(),
    is: vi.fn().mockReturnThis(),
    single: vi.fn(),
  };

  const mockSupabaseChannel = {
    on: vi.fn().mockReturnThis(),
    subscribe: vi.fn().mockReturnValue({ unsubscribe: vi.fn() }),
    unsubscribe: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.clearAllTimers();

    // Setup default mock implementations
    mockTempStickService.getSensors.mockResolvedValue([]);
    mockTempStickService.getLatestReadings.mockResolvedValue([]);
    mockTempStickService.getSensorHealth.mockResolvedValue({
      online: true,
      batteryLevel: 80,
      signalStrength: 90
    });
    mockTempStickService.performHealthCheck.mockResolvedValue({
      tempstickApi: { status: 'healthy', latency: 100, lastCheck: new Date().toISOString() },
      database: { status: 'healthy', activeConnections: 1, lastMigration: new Date().toISOString() },
      sensors: { total: 4, online: 3, offline: 1, lowBattery: 0 },
      alerts: { active: 1, unresolved: 1, critical: 0 }
    });

    // Mock Supabase
    mockSupabase.from.mockReturnValue(mockSupabaseFrom as any);
    mockSupabase.channel.mockReturnValue(mockSupabaseChannel as any);
  });

  afterEach(() => {
    vi.clearAllTimers();
  });

  describe('1. Hook Initialization and Default State', () => {
    it('initializes with correct default state', async () => {
      const { result } = renderHook(() => useTemperatureDashboard());

      expect(result.current.loading).toBe(true);
      expect(result.current.error).toBe(null);
      expect(result.current.sensorStatuses).toEqual([]);
      expect(result.current.dashboardSummary).toBe(null);
      expect(result.current.temperatureTrends).toEqual([]);
      expect(result.current.systemHealth).toBe(null);
      expect(result.current.autoRefresh).toBe(true);
      expect(result.current.filters).toEqual({
        timeRange: '24h',
        selectedSensors: [],
        selectedStorageAreas: [],
        showOfflineSensors: true,
        alertsOnly: false
      });
    });

    it('accepts custom options', () => {
      const options = {
        autoRefreshInterval: 15000,
        enableRealTimeUpdates: false,
        maxTrendDataPoints: 500
      };

      const { result } = renderHook(() => useTemperatureDashboard(options));

      // Should initialize with custom settings
      expect(result.current.autoRefresh).toBe(true);
      expect(result.current.loading).toBe(true);
    });

    it('triggers initial data load on mount', async () => {
      mockSupabaseFrom.single.mockResolvedValue({ data: null, error: null });

      renderHook(() => useTemperatureDashboard());

      await waitFor(() => {
        expect(mockTempStickService.getSensors).toHaveBeenCalled();
      });
    });
  });

  describe('2. Data Fetching and Transformation', () => {
    it('loads sensor statuses from TempStick service', async () => {
      const mockSensors = [
        {
          id: 'sensor-1',
          name: 'Test Sensor',
          location: 'Test Location',
          status: 'online' as const,
          battery_level: 85,
          signal_strength: 95,
          last_reading: new Date().toISOString()
        }
      ];
      
      mockTempStickService.getSensors.mockResolvedValue(mockSensors);
      mockTempStickService.getLatestReadings.mockResolvedValue([
        {
          temperature: 35.2,
          humidity: 65,
          timestamp: new Date().toISOString(),
          sensor_id: 'sensor-1',
          battery_level: 85,
          signal_strength: 95
        }
      ]);
      mockTempStickService.getSensorHealth.mockResolvedValue({
        online: true,
        batteryLevel: 85,
        signalStrength: 95
      });

      const { result } = renderHook(() => useTemperatureDashboard());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.sensorStatuses).toHaveLength(1);
      expect(result.current.sensorStatuses[0].sensor.name).toBe('Test Sensor');
      expect(result.current.sensorStatuses[0].status).toBe('online');
    });

    it('handles service errors correctly', async () => {
      mockTempStickService.getSensors.mockRejectedValue(new Error('API Error'));

      const { result } = renderHook(() => useTemperatureDashboard());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Should show error when API fails
      expect(result.current.error).toBeTruthy();
      expect(result.current.sensorStatuses.length).toBe(0);
    });

    it('calculates dashboard summary correctly', async () => {
      const mockSensors = [
        {
          id: 'sensor-1',
          name: 'Online Sensor',
          status: 'online' as const,
          battery_level: 85,
          signal_strength: 95
        },
        {
          id: 'sensor-2', 
          name: 'Offline Sensor',
          status: 'offline' as const,
          battery_level: 20,
          signal_strength: 0
        }
      ];
      
      mockTempStickService.getSensors.mockResolvedValue(mockSensors);
      mockTempStickService.getLatestReadings.mockImplementation((sensorId) => {
        if (sensorId === 'sensor-1') {
          return Promise.resolve([{
            temperature: 35.2,
            humidity: 65,
            timestamp: new Date().toISOString(),
            sensor_id: 'sensor-1',
            battery_level: 85,
            signal_strength: 95
          }]);
        }
        return Promise.resolve([]);
      });

      const { result } = renderHook(() => useTemperatureDashboard());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.dashboardSummary).toBeDefined();
      expect(result.current.dashboardSummary!.totalSensors).toBe(2);
      expect(result.current.dashboardSummary!.onlineSensors).toBe(1);
    });
  });

  describe('3. Filter Management', () => {
    it('updates filters correctly', async () => {
      const { result } = renderHook(() => useTemperatureDashboard());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      const newFilters: Partial<DashboardFilters> = {
        timeRange: '1h',
        showOfflineSensors: false
      };

      act(() => {
        result.current.setFilters(newFilters);
      });

      expect(result.current.filters).toEqual({
        timeRange: '1h',
        selectedSensors: [],
        selectedStorageAreas: [],
        showOfflineSensors: false,
        alertsOnly: false
      });
    });

    it('triggers data refresh when filters change', async () => {
      const { result } = renderHook(() => useTemperatureDashboard());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Clear initial calls
      vi.clearAllMocks();

      act(() => {
        result.current.setFilters({ timeRange: '6h' });
      });

      await waitFor(() => {
        expect(mockTempStickService.getSensors).toHaveBeenCalled();
      });
    });

    it('applies filters to sensor data correctly', async () => {
      const mockSensors = [
        {
          id: 'online-sensor',
          name: 'Online Sensor',
          status: 'online' as const
        },
        {
          id: 'offline-sensor',
          name: 'Offline Sensor', 
          status: 'offline' as const
        }
      ];

      mockTempStickService.getSensors.mockResolvedValue(mockSensors);

      const { result } = renderHook(() => useTemperatureDashboard());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Initially should show both sensors
      expect(result.current.sensorStatuses).toHaveLength(2);

      // Filter out offline sensors
      act(() => {
        result.current.setFilters({ showOfflineSensors: false });
      });

      await waitFor(() => {
        expect(result.current.sensorStatuses).toHaveLength(1);
        expect(result.current.sensorStatuses[0].sensor.name).toBe('Online Sensor');
      });
    });
  });

  describe('4. Auto-refresh Functionality', () => {
    it('enables auto-refresh by default', () => {
      const { result } = renderHook(() => useTemperatureDashboard());

      expect(result.current.autoRefresh).toBe(true);
    });

    it('toggles auto-refresh state', () => {
      const { result } = renderHook(() => useTemperatureDashboard());

      act(() => {
        result.current.setAutoRefresh(false);
      });

      expect(result.current.autoRefresh).toBe(false);
    });

    it('executes auto-refresh at specified intervals', async () => {
      mockSupabaseFrom.single.mockResolvedValue({ data: null, error: null });
      
      renderHook(() => useTemperatureDashboard({
        autoRefreshInterval: 5000 // 5 seconds
      }));

      // Clear initial calls
      await waitFor(() => {
        expect(mockTempStickService.getSensors).toHaveBeenCalled();
      });
      vi.clearAllMocks();

      // Fast-forward time by 5 seconds
      act(() => {
        vi.advanceTimersByTime(5000);
      });

      // Should have triggered refresh
      await waitFor(() => {
        expect(mockTempStickService.getSensors).toHaveBeenCalled();
      });
    });

    it('stops auto-refresh when disabled', async () => {
      const { result } = renderHook(() => useTemperatureDashboard({
        autoRefreshInterval: 5000
      }));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Disable auto-refresh
      act(() => {
        result.current.setAutoRefresh(false);
      });

      vi.clearAllMocks();

      // Fast-forward time
      act(() => {
        vi.advanceTimersByTime(10000);
      });

      // Should not have triggered refresh
      expect(mockTempStickService.getSensors).not.toHaveBeenCalled();
    });
  });

  describe('5. Real-time Updates', () => {
    it('sets up real-time subscriptions', () => {
      renderHook(() => useTemperatureDashboard({
        enableRealTimeUpdates: true
      }));

      expect(mockSupabase.channel).toHaveBeenCalledWith('temperature_dashboard');
      expect(mockSupabaseChannel.on).toHaveBeenCalled();
      expect(mockSupabaseChannel.subscribe).toHaveBeenCalled();
    });

    it('does not set up subscriptions when disabled', () => {
      renderHook(() => useTemperatureDashboard({
        enableRealTimeUpdates: false
      }));

      // Channel should still be created but subscriptions might be different
      expect(mockSupabase.channel).toHaveBeenCalled();
    });

    it('cleans up subscriptions on unmount', () => {
      const mockUnsubscribe = vi.fn();
      mockSupabaseChannel.subscribe.mockReturnValue({ unsubscribe: mockUnsubscribe });

      const { unmount } = renderHook(() => useTemperatureDashboard());

      unmount();

      expect(mockUnsubscribe).toHaveBeenCalled();
    });
  });

  describe('6. Error Handling', () => {
    it('handles service errors gracefully', async () => {
      const errorMessage = 'TempStick API is down';
      mockTempStickService.getSensors.mockRejectedValue(new Error(errorMessage));

      const { result } = renderHook(() => useTemperatureDashboard());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.error).toContain(errorMessage);
    });

    it('clears errors when requested', async () => {
      mockTempStickService.getSensors.mockRejectedValue(new Error('Test error'));

      const { result } = renderHook(() => useTemperatureDashboard());

      await waitFor(() => {
        expect(result.current.error).toBeTruthy();
      });

      act(() => {
        result.current.clearError();
      });

      expect(result.current.error).toBe(null);
    });

    it('handles partial failures gracefully', async () => {
      mockTempStickService.getSensors.mockResolvedValue([
        { id: 'sensor-1', name: 'Working Sensor', status: 'online' as const }
      ]);
      
      // Health check fails for this sensor
      mockTempStickService.getSensorHealth.mockRejectedValue(new Error('Health check failed'));
      mockTempStickService.getLatestReadings.mockResolvedValue([]);

      const { result } = renderHook(() => useTemperatureDashboard());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Should still have sensor data despite health check failure
      expect(result.current.sensorStatuses).toHaveLength(1);
      expect(result.current.error).toBe(null); // Partial failures shouldn't cause total error
    });
  });

  describe('7. Performance Optimizations', () => {
    it('debounces filter changes to avoid excessive API calls', async () => {
      const { result } = renderHook(() => useTemperatureDashboard());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      vi.clearAllMocks();

      // Make rapid filter changes
      act(() => {
        result.current.setFilters({ timeRange: '1h' });
        result.current.setFilters({ timeRange: '6h' });
        result.current.setFilters({ timeRange: '24h' });
      });

      // Should only trigger one refresh after all changes
      await waitFor(() => {
        expect(mockTempStickService.getSensors).toHaveBeenCalledTimes(1);
      });
    });

    it('limits trend data points to prevent memory issues', async () => {
      const largeTrendDataset = Array.from({ length: 10000 }, (_, i) => ({
        timestamp: new Date(Date.now() - i * 60000).toISOString(),
        temperature: 20 + Math.random() * 10,
        humidity: 50 + Math.random() * 20,
        sensorName: 'Test Sensor',
        sensorId: 'test-sensor',
        alertLevel: 'normal' as const
      }));

      // Mock database query to return large dataset
      mockSupabaseFrom.single.mockResolvedValue({ 
        data: largeTrendDataset,
        error: null 
      });

      const { result } = renderHook(() => useTemperatureDashboard({
        maxTrendDataPoints: 1000
      }));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Should be limited to maxTrendDataPoints
      expect(result.current.temperatureTrends.length).toBeLessThanOrEqual(1000);
    });

    it('memoizes expensive calculations', async () => {
      const { result, rerender } = renderHook(() => useTemperatureDashboard());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      const initialSummary = result.current.dashboardSummary;

      // Trigger re-render without changing data
      rerender();

      // Summary should be the same object reference (memoized)
      expect(result.current.dashboardSummary).toBe(initialSummary);
    });
  });

  describe('9. Manual Refresh', () => {
    it('provides manual refresh functionality', async () => {
      const { result } = renderHook(() => useTemperatureDashboard());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      vi.clearAllMocks();

      act(() => {
        result.current.refreshData();
      });

      await waitFor(() => {
        expect(mockTempStickService.getSensors).toHaveBeenCalled();
      });
    });

    it('updates last update timestamp after refresh', async () => {
      const { result } = renderHook(() => useTemperatureDashboard());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      const initialLastUpdate = result.current.lastUpdate;

      // Wait a bit then refresh
      await new Promise(resolve => setTimeout(resolve, 100));

      act(() => {
        result.current.refreshData();
      });

      await waitFor(() => {
        expect(result.current.lastUpdate).not.toBe(initialLastUpdate);
      });
    });

    it('handles refresh during existing refresh', async () => {
      let resolveGetSensors: () => void;
      const getSensorsPromise = new Promise<any[]>(resolve => {
        resolveGetSensors = () => resolve([]);
      });

      mockTempStickService.getSensors.mockReturnValue(getSensorsPromise);

      const { result } = renderHook(() => useTemperatureDashboard());

      // Trigger multiple refreshes before first completes
      act(() => {
        result.current.refreshData();
        result.current.refreshData();
        result.current.refreshData();
      });

      // Complete the pending request
      resolveGetSensors!();

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Should handle concurrent refreshes gracefully
      expect(result.current.error).toBe(null);
    });
  });

  describe('10. Memory Management', () => {
    it('cleans up intervals and subscriptions on unmount', () => {
      const mockUnsubscribe = vi.fn();
      mockSupabaseChannel.subscribe.mockReturnValue({ unsubscribe: mockUnsubscribe });

      const { unmount } = renderHook(() => useTemperatureDashboard({
        autoRefreshInterval: 5000,
        enableRealTimeUpdates: true
      }));

      unmount();

      expect(mockUnsubscribe).toHaveBeenCalled();

      // Advance timers to ensure no memory leaks
      act(() => {
        vi.advanceTimersByTime(10000);
      });

      // Should not trigger any more API calls
      expect(mockTempStickService.getSensors).not.toHaveBeenCalled();
    });

    it('handles component re-mounting correctly', () => {
      const { unmount, remount } = renderHook(() => useTemperatureDashboard());

      unmount();
      
      // Re-mount should work without errors
      expect(() => remount()).not.toThrow();
    });
  });
});