export interface PlatformIntegration {
  id: string;
  platform: 'shopify' | 'square' | 'woocommerce';
  config: Record<string, unknown>;
  status: 'active' | 'inactive' | 'error';
  lastSync: string;
  errorLog: Error[];
  createdAt: string;
  updatedAt: string;
}

export interface SyncLog {
  id: string;
  platformId: string;
  direction: 'import' | 'export';
  status: 'success' | 'partial' | 'failed';
  recordsProcessed: number;
  recordsFailed: number;
  errorDetails: Error[];
  createdAt: string;
}

export interface GDSTData {
  certificationScheme?: string;
  certificationStatus?: string;
  harvestDate?: string;
  harvestLocation?: string;
  vesselIdentification?: string;
  processingFacility?: string;
  processingDate?: string;
  productIdentification?: string;
  batchNumber?: string;
  [key: string]: string | undefined;
}

export interface ExternalRefs {
  shopifyId?: string;
  squareId?: string;
  woocommerceId?: string;
  [key: string]: string | undefined;
}

export interface Vendor {
  id?: string;
  name: string;
  contactName?: string;
  email?: string;
  phone?: string;
  address?: string;
  status?: 'active' | 'inactive';
  paymentTerms?: string;
  creditLimit?: number;
  metadata?: Record<string, unknown>;
  created_at?: string;
  updated_at?: string;
}

export interface Customer {
  id?: string;
  name: string;
  contactName?: string;
  email?: string;
  phone?: string;
  address?: string;
  channelType: 'wholesale' | 'retail' | 'distributor';
  customerSource?: string;
  status: 'active' | 'inactive';
  paymentTerms?: string;
  creditLimit?: number;
  metadata?: Record<string, unknown>;
  created_at?: string;
  updated_at?: string;
}

export interface Product {
  date?: string;
  id?: string;
  name: string;
  amount: number;
  condition: 'fresh' | 'frozen' | 'other';
  otherCondition?: string;
  category?: string;
  subCategory?: string;
  price?: number;
  supplierId?: string;
  supplier?: string;
  speciesDetails?: {
    scientificName?: string;
    habitat?: string;
    sustainabilityRating?: string;
  };
  notes?: string;
  image?: string;
  images?: string[];
  created_at?: string;
  updated_at?: string;
}

export interface ImportProductData {
  name: string;
  vendor: string;
  condition: string;
}

export interface Event {
  event_type: string;
  product_id?: string;
  vendor_id?: string;
  customer_id?: string;
  quantity?: number;
  unit?: string;
  temperature?: number;
  quality_status?: string;
  notes?: string;
  metadata?: Record<string, unknown>;
  gdst_data?: Record<string, unknown>;
  created_by?: string;
  created_at?: string;
}

// ===== Phase 1 Traceability Types =====
export interface Partner {
  id?: string;
  type: 'supplier' | 'customer' | 'carrier' | 'facility';
  name: string;
  gln?: string | null;
  contact?: Record<string, unknown>;
  created_at?: string;
  updated_at?: string;
}

export interface Location {
  id?: string;
  partner_id: string;
  name: string;
  role?: string | null;
  gln?: string | null;
  address?: Record<string, unknown>;
  created_at?: string;
  updated_at?: string;
}

export interface Lot {
  id?: string;
  tlc?: string;
  product_id: string;
  origin_country?: string | null;
  harvest_or_prod_date?: string | null; // ISO date
  landing_date?: string | null; // ISO date
  initial_qty?: number | null;
  uom?: string | null;
  status?: 'active' | 'consumed' | 'expired';
  expiry_date?: string | null;
  notes?: string | null;
  created_at?: string;
  updated_at?: string;
}

export interface TraceabilityEvent {
  id?: string;
  event_type: 'harvest' | 'landing' | 'shipping' | 'receiving' | 'transformation';
  event_time?: string; // ISO timestamp
  actor_partner_id?: string | null;
  actor_location_id?: string | null;
  transporter_partner_id?: string | null;
  reference_doc?: string | null;
  temperature_data?: Record<string, unknown>;
  notes?: string | null;
  created_at?: string;
  updated_at?: string;
}

export interface ReceivingWithLotInput {
  productName: string;
  quantity: number;
  unit?: string;
  vendorName: string;
  receivingDate?: string; // YYYY-MM-DD
  condition?: string;
  notes?: string;
  tlc?: string; // Optional manual batch/lot code
}
