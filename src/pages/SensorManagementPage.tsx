/**
 * Comprehensive Sensor Management Page
 * 
 * Complete sensor management interface with:
 * - Sensor discovery and registration
 * - Configuration and settings
 * - Calibration tracking
 * - Maintenance scheduling
 */

import React, { useState } from 'react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  SensorManagement, 
  SensorDiscovery, 
  SensorCalibration 
} from '@/components/sensors';
import { 
  Settings, 
  Search, 
  Wrench, 
  InfoIcon,
  Thermometer,
  Wifi,
  Battery,
  Calendar
} from 'lucide-react';

const SensorManagementPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <div className="container mx-auto px-4 py-6 space-y-6">
      {/* Page Header */}
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Sensor Management</h1>
        <p className="text-muted-foreground">
          Comprehensive management of temperature sensors for HACCP compliance
        </p>
      </div>

      {/* Info Alert */}
      <Alert>
        <InfoIcon className="h-4 w-4" />
        <AlertDescription>
          Manage your TempStick temperature sensors, configure thresholds, schedule calibrations, 
          and ensure HACCP compliance across all storage areas.
        </AlertDescription>
      </Alert>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <Thermometer className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="discovery" className="flex items-center gap-2">
            <Search className="h-4 w-4" />
            Discovery
          </TabsTrigger>
          <TabsTrigger value="management" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Management
          </TabsTrigger>
          <TabsTrigger value="calibration" className="flex items-center gap-2">
            <Wrench className="h-4 w-4" />
            Calibration
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Active Sensors</p>
                    <p className="text-2xl font-bold">12</p>
                  </div>
                  <Thermometer className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Online</p>
                    <p className="text-2xl font-bold text-green-600">11</p>
                  </div>
                  <Wifi className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Low Battery</p>
                    <p className="text-2xl font-bold text-yellow-600">2</p>
                  </div>
                  <Battery className="h-8 w-8 text-yellow-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Due for Calibration</p>
                    <p className="text-2xl font-bold text-orange-600">3</p>
                  </div>
                  <Calendar className="h-8 w-8 text-orange-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="cursor-pointer hover:shadow-md transition-shadow" 
                      onClick={() => setActiveTab('discovery')}>
                  <CardContent className="p-6 text-center">
                    <Search className="h-12 w-12 mx-auto mb-4 text-blue-500" />
                    <h3 className="font-medium mb-2">Discover New Sensors</h3>
                    <p className="text-sm text-muted-foreground">
                      Find and register new TempStick sensors automatically
                    </p>
                  </CardContent>
                </Card>

                <Card className="cursor-pointer hover:shadow-md transition-shadow"
                      onClick={() => setActiveTab('management')}>
                  <CardContent className="p-6 text-center">
                    <Settings className="h-12 w-12 mx-auto mb-4 text-green-500" />
                    <h3 className="font-medium mb-2">Configure Sensors</h3>
                    <p className="text-sm text-muted-foreground">
                      Set thresholds, assign storage areas, and manage settings
                    </p>
                  </CardContent>
                </Card>

                <Card className="cursor-pointer hover:shadow-md transition-shadow"
                      onClick={() => setActiveTab('calibration')}>
                  <CardContent className="p-6 text-center">
                    <Wrench className="h-12 w-12 mx-auto mb-4 text-orange-500" />
                    <h3 className="font-medium mb-2">Schedule Calibration</h3>
                    <p className="text-sm text-muted-foreground">
                      Track calibration schedules and maintain accuracy
                    </p>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>

          {/* System Status */}
          <Card>
            <CardHeader>
              <CardTitle>System Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">TempStick API Connection</span>
                  <Badge className="bg-green-100 text-green-800">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    Connected
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Database Sync</span>
                  <Badge className="bg-green-100 text-green-800">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    Synchronized
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Last Sync</span>
                  <span className="text-sm text-muted-foreground">2 minutes ago</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">HACCP Compliance</span>
                  <Badge className="bg-yellow-100 text-yellow-800">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
                    3 sensors need calibration
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Discovery Tab */}
        <TabsContent value="discovery" className="space-y-6">
          <SensorDiscovery 
            onSensorRegistered={(sensor) => {
              console.log('Sensor registered:', sensor);
              // Could show a toast notification here
            }}
          />
        </TabsContent>

        {/* Management Tab */}
        <TabsContent value="management" className="space-y-6">
          <SensorManagement />
        </TabsContent>

        {/* Calibration Tab */}
        <TabsContent value="calibration" className="space-y-6">
          <SensorCalibration />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SensorManagementPage;