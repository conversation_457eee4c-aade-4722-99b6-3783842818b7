/**
 * Temperature Monitoring Page
 * 
 * Main page for temperature monitoring dashboard with:
 * - Real-time sensor status
 * - Temperature trend charts
 * - Alert management
 * - Responsive layout
 */

import React from 'react';
import { TemperatureDashboard } from '@/components/sensors';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { InfoIcon } from 'lucide-react';

const TemperatureMonitoring: React.FC = () => {
  return (
    <div className="container mx-auto px-4 py-6 space-y-6">
      {/* Page Header */}
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Temperature Monitoring</h1>
        <p className="text-muted-foreground">
          Monitor temperature sensors across all storage areas in real-time
        </p>
      </div>

      {/* Info Alert */}
      <Alert>
        <InfoIcon className="h-4 w-4" />
        <AlertDescription>
          This dashboard provides real-time temperature monitoring for HACCP compliance. 
          Sensors automatically sync data every 5 minutes, and alerts are generated when 
          temperatures exceed safe ranges.
        </AlertDescription>
      </Alert>

      {/* Main Dashboard */}
      <TemperatureDashboard 
        autoRefreshInterval={30000} // 30 seconds
        showFilters={true}
        compactMode={false}
        className="w-full"
      />
    </div>
  );
};

export default TemperatureMonitoring;