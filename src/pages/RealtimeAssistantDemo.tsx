import React, { useState } from 'react';
import { Lightbulb, Database, MessageSquare, Settings } from 'lucide-react';
import RealtimeVoiceAssistant from '../components/voice/RealtimeVoiceAssistant';

const RealtimeAssistantDemo: React.FC = () => {
  const [recentEvents, setRecentEvents] = useState<any[]>([]);
  const [errors, setErrors] = useState<string[]>([]);

  const handleEventCreated = (event: any) => {
    console.log('New event created:', event);
    setRecentEvents(prev => [event, ...prev.slice(0, 4)]);
  };

  const handleError = (error: string) => {
    console.error('Assistant error:', error);
    setErrors(prev => [error, ...prev.slice(0, 2)]);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            OpenAI Realtime Voice Assistant
          </h1>
          <p className="text-gray-600">
            Real-time voice interaction with Supabase database and Zep memory integration
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Voice Assistant */}
          <div className="lg:col-span-2">
            <RealtimeVoiceAssistant
              userId="demo-user"
              onEventCreated={handleEventCreated}
              onError={handleError}
            />
          </div>

          {/* Status Panel */}
          <div className="space-y-6">
            {/* Recent Events */}
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center space-x-2 mb-4">
                <Database className="w-5 h-5 text-green-600" />
                <h3 className="font-semibold text-gray-900">Recent Events</h3>
              </div>
              
              {recentEvents.length === 0 ? (
                <p className="text-gray-500 text-sm">No events created yet</p>
              ) : (
                <div className="space-y-3">
                  {recentEvents.map((event, index) => (
                    <div key={index} className="border border-gray-200 rounded p-3">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium text-gray-900">
                          {event.event_type}
                        </span>
                        <span className="text-xs text-gray-500">
                          {new Date(event.created_at).toLocaleTimeString()}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600">
                        {event.metadata?.product_name}: {event.quantity} {event.unit}
                      </p>
                      {event.metadata?.vendor_name && (
                        <p className="text-xs text-gray-500">
                          Vendor: {event.metadata.vendor_name}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Errors */}
            {errors.length > 0 && (
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center space-x-2 mb-4">
                  <MessageSquare className="w-5 h-5 text-red-600" />
                  <h3 className="font-semibold text-gray-900">Recent Errors</h3>
                </div>
                
                <div className="space-y-2">
                  {errors.map((error, index) => (
                    <div key={index} className="bg-red-50 border border-red-200 rounded p-2">
                      <p className="text-sm text-red-700">{error}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Instructions */}
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center space-x-2 mb-4">
                <Lightbulb className="w-5 h-5 text-yellow-600" />
                <h3 className="font-semibold text-gray-900">Try These Commands</h3>
              </div>
              
              <div className="space-y-3 text-sm">
                <div className="bg-blue-50 border border-blue-200 rounded p-3">
                  <p className="font-medium text-blue-900 mb-1">Receiving:</p>
                  <p className="text-blue-700">"We received 50 pounds of Atlantic salmon from Pacific Seafoods"</p>
                </div>
                
                <div className="bg-green-50 border border-green-200 rounded p-3">
                  <p className="font-medium text-green-900 mb-1">Sales:</p>
                  <p className="text-green-700">"Sold 25 pounds of Dungeness crab to Harbor Restaurant"</p>
                </div>
                
                <div className="bg-purple-50 border border-purple-200 rounded p-3">
                  <p className="font-medium text-purple-900 mb-1">Inventory Check:</p>
                  <p className="text-purple-700">"What's our current salmon inventory?"</p>
                </div>
                
                <div className="bg-orange-50 border border-orange-200 rounded p-3">
                  <p className="font-medium text-orange-900 mb-1">Physical Count:</p>
                  <p className="text-orange-700">"Physical count shows 75 pounds of halibut in freezer 2"</p>
                </div>
              </div>
            </div>

            {/* Configuration */}
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center space-x-2 mb-4">
                <Settings className="w-5 h-5 text-gray-600" />
                <h3 className="font-semibold text-gray-900">Configuration</h3>
              </div>
              
              <div className="space-y-2 text-xs">
                <div className="flex justify-between">
                  <span className="text-gray-600">OpenAI Model:</span>
                  <span className="text-gray-900">gpt-4o-realtime</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Database:</span>
                  <span className="text-gray-900">Supabase</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Memory:</span>
                  <span className="text-gray-900">Zep</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Voice Detection:</span>
                  <span className="text-gray-900">Server VAD</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Feature Overview */}
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Available Tools & Capabilities
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="border border-gray-200 rounded p-4">
              <h4 className="font-medium text-gray-900 mb-2">Inventory Events</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Create receiving events</li>
                <li>• Record sales transactions</li>
                <li>• Log disposal/waste</li>
                <li>• Physical count updates</li>
              </ul>
            </div>
            
            <div className="border border-gray-200 rounded p-4">
              <h4 className="font-medium text-gray-900 mb-2">Database Search</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Product lookup</li>
                <li>• Vendor/supplier search</li>
                <li>• Customer search</li>
                <li>• Inventory status check</li>
              </ul>
            </div>
            
            <div className="border border-gray-200 rounded p-4">
              <h4 className="font-medium text-gray-900 mb-2">Memory & Context</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Conversation history</li>
                <li>• User preferences</li>
                <li>• Session context</li>
                <li>• Personalized responses</li>
              </ul>
            </div>
            
            <div className="border border-gray-200 rounded p-4">
              <h4 className="font-medium text-gray-900 mb-2">Analytics & Reports</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Recent event history</li>
                <li>• Inventory trends</li>
                <li>• Vendor performance</li>
                <li>• Activity summaries</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RealtimeAssistantDemo;