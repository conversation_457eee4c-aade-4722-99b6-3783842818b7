import React, { useState, useEffect } from 'react';
import { useAuth } from '../hooks/useAuth';
import { Activity, Settings, Bar<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Play, Pause } from 'lucide-react';
import { But<PERSON> } from '../components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '../components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '../components/ui/tabs';

// Import voice management components
import VoiceAssistant from '../components/voice/VoiceAssistant';
import RealtimeVoiceAssistant from '../components/voice/RealtimeVoiceAssistant';
import VoicePerformanceDashboard from '../components/voice/VoicePerformanceDashboard';
import VoiceErrorDisplay from '../components/voice/VoiceErrorDisplay';
import VoiceQueueStatus from '../components/voice/VoiceQueueStatus';

// Import existing components
import EventsTable from '../components/EventsTable';
import EventsView from '../components/EventsView';

// Import services
import { optimizedVoiceEventService } from '../services/OptimizedVoiceEventService';

interface VoiceManagementStats {
  totalVoiceEvents: number;
  successRate: number;
  avgConfidenceScore: number;
  recentActivity: any[];
}

const VoiceEventManagement: React.FC = () => {
  console.log('VoiceEventManagement: Component rendering started');
  
  // ALL HOOKS MUST BE CALLED FIRST - React Rules of Hooks
  const { user, isLoading: authLoading } = useAuth();
  const [activeTab, setActiveTab] = useState('live');
  const [voiceSystemEnabled, setVoiceSystemEnabled] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [stats, setStats] = useState<VoiceManagementStats | null>(null);
  const [recentEvents, setRecentEvents] = useState<any[]>([]);

  console.log('VoiceEventManagement: Auth state', { user: !!user, authLoading });

  useEffect(() => {
    loadStats();
    loadRecentEvents();
  }, []);

  const loadStats = async () => {
    try {
      const performanceStats = optimizedVoiceEventService.getPerformanceStats();
      
      // Mock stats for now - in real implementation, would query the database
      setStats({
        totalVoiceEvents: 245,
        successRate: performanceStats.performance.voiceProcessing.successRate || 0.95,
        avgConfidenceScore: 0.87,
        recentActivity: []
      });
    } catch (error) {
      console.error('Failed to load voice management stats:', error);
    }
  };

  const loadRecentEvents = async () => {
    try {
      const result = await optimizedVoiceEventService.getVoiceEventsPaginated(
        {}, // No filters
        1,  // First page
        10  // Limit to 10 recent events
      );
      setRecentEvents(result.data);
    } catch (error) {
      console.error('Failed to load recent voice events:', error);
    }
  };

  const handleVoiceSystemToggle = () => {
    setVoiceSystemEnabled(!voiceSystemEnabled);
  };

  const handleEventCreated = (event: any) => {
    console.log('New voice event created:', event);
    setRecentEvents(prev => [event, ...prev.slice(0, 9)]);
    loadStats(); // Refresh stats
  };

  const handleError = (error: any) => {
    console.error('Voice processing error:', error);
  };

  const formatNumber = (num: number): string => {
    return new Intl.NumberFormat().format(num);
  };

  const formatPercentage = (value: number): string => {
    return `${Math.round(value * 100)}%`;
  };

  // Authentication checks AFTER all hooks
  if (authLoading) {
    console.log('VoiceEventManagement: Auth is loading...');
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p className="ml-4">Loading authentication...</p>
      </div>
    );
  }

  if (!user) {
    console.log('VoiceEventManagement: No user found, showing auth required message');
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Authentication Required</h2>
          <p className="text-gray-600">Please sign in to access Voice Management.</p>
        </div>
      </div>
    );
  }

  console.log('VoiceEventManagement: About to render main component');
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Debug Header */}
      <div style={{ backgroundColor: 'yellow', padding: '10px', marginBottom: '20px' }}>
        DEBUG: Voice Management Page Loaded - User: {user?.email || 'No user'} - Stats: {JSON.stringify(stats)}
      </div>
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-3">
          <Activity className="w-8 h-8 text-blue-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Voice Event Management</h1>
            <p className="text-gray-600">AI-powered voice processing for seafood inventory</p>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <Button
            type="button"
            onClick={handleVoiceSystemToggle}
            variant={voiceSystemEnabled ? "default" : "outline"}
            className="flex items-center space-x-2"
          >
            {voiceSystemEnabled ? (
              <>
                <Mic className="w-4 h-4" />
                <span>Voice Active</span>
              </>
            ) : (
              <>
                <MicOff className="w-4 h-4" />
                <span>Voice Inactive</span>
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Voice Events</p>
                  <p className="text-2xl font-bold text-gray-900">{formatNumber(stats.totalVoiceEvents)}</p>
                </div>
                <Activity className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Success Rate</p>
                  <p className="text-2xl font-bold text-green-600">{formatPercentage(stats.successRate)}</p>
                </div>
                <BarChart3 className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Avg Confidence</p>
                  <p className="text-2xl font-bold text-purple-600">{formatPercentage(stats.avgConfidenceScore)}</p>
                </div>
                <Settings className="w-8 h-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Processing</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {isProcessing ? 'Active' : 'Idle'}
                  </p>
                </div>
                {isProcessing ? (
                  <Play className="w-8 h-8 text-green-600" />
                ) : (
                  <Pause className="w-8 h-8 text-gray-400" />
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Error Display */}
      <VoiceErrorDisplay />

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="live">Live Processing</TabsTrigger>
          <TabsTrigger value="realtime">Realtime Assistant</TabsTrigger>
          <TabsTrigger value="events">Voice Events</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="queue">Queue Status</TabsTrigger>
        </TabsList>

        {/* Live Processing Tab */}
        <TabsContent value="live" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Mic className="w-5 h-5" />
                <span>Voice Event Processor</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {voiceSystemEnabled ? (
                <VoiceAssistant
                  onEventCreated={(eventType) => {
                    // Convert the eventType string to match expected format
                    const mockEvent = {
                      id: `voice-${Date.now()}`,
                      event_type: eventType,
                      quantity: 0,
                      unit: 'lbs',
                      created_at: new Date().toISOString(),
                      voice_confidence_score: 0.95
                    };
                    handleEventCreated(mockEvent);
                  }}
                />
              ) : (
                <div className="text-center py-12">
                  <MicOff className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Voice System Inactive</h3>
                  <p className="text-gray-600 mb-4">Enable voice processing to start recording inventory events</p>
                  <Button type="button" onClick={handleVoiceSystemToggle}>
                    <Mic className="w-4 h-4 mr-2" />
                    Enable Voice Processing
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Recent Events Summary */}
          {recentEvents.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Recent Voice Events</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {recentEvents.slice(0, 5).map((event, index) => (
                    <div key={event.id || index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <div>
                          <p className="font-medium text-gray-900">{event.event_type}</p>
                          <p className="text-sm text-gray-600">
                            {event.quantity} {event.unit} - Confidence: {Math.round((event.voice_confidence_score || 0) * 100)}%
                          </p>
                        </div>
                      </div>
                      <span className="text-xs text-gray-500">
                        {new Date(event.created_at || Date.now()).toLocaleTimeString()}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Realtime Assistant Tab */}
        <TabsContent value="realtime" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="w-5 h-5" />
                <span>OpenAI Realtime Assistant</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <RealtimeVoiceAssistant
                userId={user.id}
                onEventCreated={handleEventCreated}
                onError={handleError}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Voice Events Tab */}
        <TabsContent value="events" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Voice Event History</CardTitle>
            </CardHeader>
            <CardContent>
              <EventsTable />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-6">
          <VoicePerformanceDashboard />
        </TabsContent>

        {/* Queue Status Tab */}
        <TabsContent value="queue" className="space-y-6">
          <VoiceQueueStatus />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default VoiceEventManagement;