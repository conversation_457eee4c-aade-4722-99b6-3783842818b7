// Debug script to test events fetching
import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables
config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testEventsFetching() {
  console.log('Testing events fetching...');
  
  try {
    // Test 1: Simple count query
    console.log('\n1. Testing inventory_events table access...');
    const { count, error: countError } = await supabase
      .from('inventory_events')
      .select('*', { count: 'exact', head: true });
    
    if (countError) {
      console.error('Count query error:', countError);
    } else {
      console.log(`Found ${count} events in database`);
    }

    // Test 2: Simple select
    console.log('\n2. Testing simple select...');
    const { data: simpleData, error: simpleError } = await supabase
      .from('inventory_events')
      .select('id, event_type, created_at')
      .limit(5);
    
    if (simpleError) {
      console.error('Simple select error:', simpleError);
    } else {
      console.log('Simple select results:', simpleData);
    }

    // Test 3: Full EventsTable query (without voice fields)
    console.log('\n3. Testing EventsTable query...');
    const { data: eventsData, error: eventsError } = await supabase
      .from('inventory_events')
      .select(`
        id,
        created_at,
        event_type,
        quantity,
        unit_price,
        total_amount,
        notes,
        metadata,
        product_id,
        name
      `)
      .order('created_at', { ascending: false })
      .limit(10);
    
    if (eventsError) {
      console.error('EventsTable query error:', eventsError);
    } else {
      console.log('EventsTable query results:', eventsData?.length || 0, 'events');
    }

    // Test 4: Skip voice events query - columns don't exist yet
    console.log('\n4. Skipping voice events query - voice columns not migrated yet');

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Create some test data if none exists
async function createTestData() {
  console.log('\n5. Creating basic test data (without voice fields)...');
  
  const testEvents = [
    {
      event_type: 'receiving',
      name: 'Atlantic Salmon',
      quantity: 25,
      notes: 'Fresh delivery from supplier'
    },
    {
      event_type: 'sale',
      name: 'Dungeness Crab',
      quantity: 10,
      notes: 'Sold to restaurant'
    },
    {
      event_type: 'disposal',
      name: 'Expired Tuna',
      quantity: 5,
      notes: 'Past expiration date'
    }
  ];

  for (const event of testEvents) {
    try {
      const { data, error } = await supabase
        .from('inventory_events')
        .insert(event)
        .select()
        .single();
      
      if (error) {
        console.error('Error creating test event:', error);
      } else {
        console.log('Created test event:', data.id);
      }
    } catch (err) {
      console.error('Failed to create test event:', err);
    }
  }
}

async function main() {
  await testEventsFetching();
  await createTestData();
  console.log('\nTest complete. Check the frontend now.');
}

main().catch(console.error);