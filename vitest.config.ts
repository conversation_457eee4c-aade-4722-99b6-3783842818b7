/// <reference types="vitest" />
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import tsconfigPaths from 'vite-tsconfig-paths';
import { resolve } from 'path';

export default defineConfig({
  plugins: [
    react({
      babel: {
        plugins: ['@babel/plugin-transform-runtime']
      }
    }),
    tsconfigPaths()
  ],
  test: {
    globals: true,
    environment: 'happy-dom',
    setupFiles: ['./src/__tests__/setup.ts'],
    css: true,
    testTimeout: 10000,
    // Enable experimental features for better testing
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false,
        minThreads: 2,
        maxThreads: 4
      }
    },
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      reportsDirectory: './coverage',
      exclude: [
        'node_modules/**',
        'src/test/**',
        '**/*.d.ts',
        '**/*.config.ts',
        '**/*.config.js',
        'dist/**',
        'migrations/**',
        'scripts/**',
        'src/workers/**', // Workers are tested separately
        'src/types/**', // Type definitions don't need coverage
        '**/index.ts' // Re-export files
      ],
      include: [
        'src/**/*.{ts,tsx}',
        '!src/**/*.test.{ts,tsx}',
        '!src/**/*.spec.{ts,tsx}'
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 85,
          statements: 85
        },
        // Critical components need higher coverage
        'src/components/voice/**': {
          branches: 90,
          functions: 90,
          lines: 95,
          statements: 95
        },
        'src/lib/voice-processor.ts': {
          branches: 95,
          functions: 95,
          lines: 98,
          statements: 98
        },
        'src/services/VoiceEventService.ts': {
          branches: 90,
          functions: 90,
          lines: 95,
          statements: 95
        }
      }
    },
    server: {
      deps: {
        inline: [
          '@supabase/supabase-js', 
          'openai',
          'react-speech-recognition',
          'date-fns'
        ]
      }
    },
    // Test organization
    include: [
      'src/**/*.{test,spec}.{ts,tsx}',
      'src/test/**/*.{test,spec}.{ts,tsx}'
    ],
    exclude: [
      'node_modules/**',
      'dist/**',
      'e2e/**',
      'playwright/**'
    ],
    // Performance monitoring
    benchmark: {
      outputFile: './test-results/benchmark.json'
    },
    // Reporter configuration  
    reporter: process.env.CI ? ['default', 'json'] : ['default'],
    outputFile: './test-results/test-results.json'
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '@/test': resolve(__dirname, './src/test')
    }
  },
  define: {
    'process.env': {}
  }
});