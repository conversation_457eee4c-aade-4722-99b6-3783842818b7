# Database Context

## Supabase Instance

- Instance: puzjricwpsjusj<PERSON><PERSON><PERSON>wen (remote only)
- Tables:
  - products
  - events
  - inventory_events (unified table)
  - categories
  - product_categories
- Storage:
  - Image storage bucket
  - File type validation
  - Size limits
  - Access control

## Events Table

- UUID primary keys
- Event types:
  - receiving
  - sales
- Product references
- Flexible metadata storage
- Timestamp tracking
- Transaction support
- Audit logging

## Units Handling

- Units managed at event level
- Supports multiple units per product:
  - lbs (default)
  - kg
  - cases
  - units
- Context-specific to inventory transactions
- Allows different units for different events of same product
- Unit conversion support
- Validation rules

## Data Storage

1. **Product Catalog**
   - Name
   - Category
   - Subcategory
   - Default unit
   - Price history
   - Supplier info
   - Origin data
   - Storage requirements

2. **Inventory Events**
   - Event type
   - Product reference
   - Quantity and unit
   - Price/cost information
   - Transaction metadata
   - Source tracking
   - Audit information

3. **Image Storage**
   - Product images
   - Document scans
   - File validation
   - Size optimization
   - Access control
   - Backup support

## Integration Points

1. **Voice Input**
   - Direct database insertion
   - Transaction support
   - Validation checks
   - Error handling
   - Retry mechanism
   - Audit logging

2. **File Import**
   - Batch processing
   - Data transformation
   - Schema validation
   - Error collection
   - Progress tracking
   - Transaction management

3. **Data Export**
   - CSV generation
   - Report formatting
   - Data filtering
   - Custom queries
   - Aggregation support

## Data Consistency

1. **Validation Rules**
   - Required fields
   - Data types
   - Value ranges
   - Unit compatibility
   - Cross-references
   - Business logic

2. **Transaction Management**
   - Atomic operations
   - Rollback support
   - Error recovery
   - Consistency checks
   - Audit trails

3. **Error Handling**
   - Validation errors
   - Network issues
   - Timeout handling
   - Retry logic
   - User feedback

## Categories Tables

- **categories**
  - Purpose: general app categories (e.g., Receiving, Disposal, Physical Count, Re-processing)
  - Accessed by UI and checks in `src/lib/setupDatabase.ts`
  - Seeded by Node-only script `scripts/seed-categories.mjs` using a Supabase service role key

- **product_categories**
  - Purpose: product taxonomy used by `"Products"` via FK `product_category_id`
  - Created/seeded via Supabase migrations under `supabase/migrations/*product_categories*.sql`
  - Queried by helper `getProductCategories()` in `src/lib/setupDatabase.ts`

## Authentication

- Supabase Auth integration
- Role-based access
- Token management
- Session handling
- Security rules

## Environment Setup

1. **Supabase Instance**
   - Production instance at puzjricwpsjusjlgrwen.supabase.co (remote only)
   - Direct connection to remote database
   - Managed service handles backups and maintenance

2. **Configuration**
   - Remote Database URL: VITE_SUPABASE_URL
   - Anonymous API Key: VITE_SUPABASE_ANON_KEY
   - Service Role Key: VITE_SUPABASE_SERVICE_ROLE_KEY
   - Environment variables in .env file
   - Storage bucket configuration
   - Auth settings managed through Supabase dashboard

3. **Development Workflow**
   - Direct development against remote Supabase instance
   - Migration management through Supabase dashboard
   - Testing in production environment
   - Backup strategy handled by Supabase
   - Schema changes through migration files
