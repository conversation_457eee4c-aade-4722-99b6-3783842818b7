# Inventory Events System

## Unified Events Table

- Table Name: inventory_events
- Primary Keys: UUID
- Event Types: received, sale, disposal, physical_count
- Product References
- Flexible Metadata Storage
- Timestamp Tracking

## Event Structure

1. **Core Fields**
   - event_type: Type of inventory event
   - product_id: Reference to products table
   - quantity: Amount affected
   - unit: Unit of measurement
   - unit_price: Price per unit
   - unit_cost: Cost per unit
   - total_amount: Total transaction value
   - notes: Additional information
   - timestamp: Event occurrence time

2. **Metadata**
   - source: Origin of event (voice, file, manual)
   - raw_input: Original input data
   - processing_method: How data was processed
   - row_number: For batch imports
   - confidence_score: For AI processing
   - model: AI model used (if applicable)

## Unit Handling

- Units managed at event level
- Context-specific to transactions
- Supported Units:
  - Pounds (lbs)
  - Kilograms (kg)
  - Cases
  - Individual units
- Allows different units for same product
- Unit conversion support

## Event Types

1. **Receiving Events**
   - Product intake
   - Supplier information
   - Cost tracking
   - Origin data
   - Storage instructions

2. **Sales Events**
   - Quantity sold
   - Revenue tracking
   - Customer information
   - Pricing details
   - Transaction metadata

3. **Disposal Events**
   - Wastage or spoilage
   - Compliance documentation
   - Cost impact

4. **Physical Count Events**
   - Stock reconciliation
   - Adjust variances
   - Audit trail

> Note: Canonical event type identifiers used by the UI and filters are `received`, `sale`, `disposal`, and `physical_count`. Some legacy data/migrations may use `receiving`/`sales`. Ensure queries and migrations are consistent or provide a mapping.

## Data Sources

1. **Voice Input**
   - Speech recognition
   - AI processing
   - Data extraction
   - Validation

2. **File Import**
   - CSV parsing
   - Batch processing
   - Data transformation
   - Validation

3. **Manual Entry**
   - Form submission
   - Direct input
   - Real-time validation
   - Error checking

## Integration Points

1. **Product Catalog**
   - Product references
   - Category information
   - Unit compatibility
   - Price history

2. **Inventory Management**
   - Stock levels
   - Unit tracking
   - Cost basis
   - Value calculation

3. **Reporting System**

- Event filtering
- Data aggregation
- Trend analysis
- Custom queries

### Filter Behavior

- When no event types are selected in the Dashboard filter UI, the app intentionally returns empty datasets and zeroed stats to avoid fetching all events accidentally.
- Selecting at least one event type will re-enable queries for charts and KPIs.

## Business Rules

1. **Validation**
   - Required fields
   - Data types
   - Unit consistency
   - Value ranges
   - Cross-references

2. **Processing**
   - Transaction atomicity
   - Data consistency
   - Error handling
   - Audit trails

## Future Enhancements

1. **Bulk Operations**
   - Event editing
   - Mass updates
   - Batch processing
   - Data migration

2. **Advanced Features**
   - Custom event types
   - Advanced filtering
   - Complex queries
   - Analytics integration
