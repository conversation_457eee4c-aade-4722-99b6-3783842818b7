## Code Style and Structure

### CSS1. DO write concise, technical TypeScript code without comments
### CSS2. DO keep all DB operations inside src/trigger files
### CSS3. DO NOT move DB operations to clients.ts
### CSS4. DO use descriptive variable and function names
### CSS5. DO use Zod schemas for type definitions and validation
### CSS6. DO use functional and declarative programming patterns
### CSS7. DO prefer functions over classes for utility implementations unless state management is required
### CSS8. DO keep all imports at the top of files
### CSS9. DO prefer iteration and modularization over code duplication
### CSS10. DO structure repository files as follows:
.
├── src/
│   ├── schemas/        # Zod schemas
│   ├── tests/          # Test files
│   ├── trigger/        # Trigger.dev tasks
│   ├── types/          # TypeScript types
│   ├── utils/          # Helper functions
│   └── workflows/      # Trigger.dev cron jobs
├── scripts/            # Build and deployment scripts
├── migrations/         # Geni database migrations
├── public/             # Public assets
├── test/               # Test files
├── flowcharts/         # Documentation diagrams
└── directives/         # Project directives