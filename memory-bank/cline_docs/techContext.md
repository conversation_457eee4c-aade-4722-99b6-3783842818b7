# Tech Context

This file will contain information about the technologies used, development setup, and technical constraints.

## Database Infrastructure

The application uses Supabase as a remote database and backend service, hosted at `puzjricwpsjusjlgrwen.supabase.co`. This is a production Supabase instance, not a local development setup. Key points:

- **Database:** All database operations connect to the remote Supabase instance
- **Authentication:** User authentication is handled by Supabase Auth
- **Environment Configuration:** Connection details and API keys are managed through environment variables:
  - `VITE_SUPABASE_URL`: Remote Supabase instance URL
  - `VITE_SUPABASE_ANON_KEY`: Public API key for anonymous access
  - `VITE_SUPABASE_SERVICE_ROLE_KEY`: Admin API key for privileged operations

## Units Handling in Inventory Database

Units are crucial for accurate inventory tracking in this seafood company database. To ensure data integrity and flexibility, units are associated with inventory *events* rather than directly with *products*.

- **Products Table:** The `products` table stores general product information (name, category, price, etc.) but does *not* include a `unit` column. Products are defined at a high level, representing the type of seafood.
- **Events Table:** The `events` table is where inventory transactions are recorded. Each event (e.g., receiving, sale, disposal) tracks the `quantity` of a product involved in the event, along with the corresponding `unit` (e.g., lbs, kg, cases). This allows for tracking inventory movements in various units and provides a detailed history of inventory changes.

This design decision ensures that:

- **Units are context-specific:** Units are relevant to specific inventory transactions, not to the product definition itself. A product can be received in cases, sold in lbs, and disposed of in kg.
- **Accurate inventory tracking:** By recording units at the event level, we can accurately track inventory levels for different units and gain insights into unit-specific inventory movements.
- **Flexibility:** The system can handle various units for different inventory events and product types, accommodating the diverse needs of a seafood business.

Therefore, when working with inventory data, remember that units are associated with inventory *events* in the `events` table, and product definitions in the `products` table do not include unit information.

## Database Schema Updates

**New Unified Inventory Events Table**
```sql
CREATE TABLE inventory_events (
    id uuid PRIMARY KEY,
    event_type TEXT NOT NULL,
    product_id uuid REFERENCES products(id),
    name TEXT,          -- Mapped from form 'Product'
    quantity NUMERIC,
    total_amount NUMERIC,
    unit_price NUMERIC,
    notes TEXT,
    images TEXT[],
    category TEXT NOT NULL DEFAULT 'Uncategorized',
    metadata JSONB
);
```

Key Relationships:
- `product_id` → products.id
- `event_type` ← voice form category selection
