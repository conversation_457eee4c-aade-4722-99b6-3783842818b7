# Node.js Upgrade Instructions

The project requires Node.js version 20.x but the current version is 18.20.5. Follow these steps to upgrade:

1. Install Node Version Manager (nvm) if not already installed:
```bash
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash
```

2. Restart your terminal or run:
```bash
source ~/.nvm/nvm.sh
```

3. Install Node.js 20.x:
```bash
nvm install 20
```

4. Verify installation:
```bash
node -v  # Should show v20.x.x
```

5. Reinstall project dependencies:
```bash
npm install
```

6. Start the development server:
```bash
npm run dev
```

After completing these steps, the app should run without the engine compatibility warning.
