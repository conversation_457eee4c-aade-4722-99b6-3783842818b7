# Clines Rules: AI Project Roadmap Creation Guide

## Purpose
This document outlines the specific process my AI assistant should follow when creating project roadmaps. Following these guidelines ensures all features are properly scoped, planned, and implemented according to specifications.

## Roadmap Creation Process

### Step 1: Feature Definition Gathering
Before creating any roadmap, the AI should:
- Request a clear description of what the feature should do
- Ask specifically what the feature should NOT do
- Identify the core problem this feature solves
- Determine how this feature integrates with existing functionality
- Clarify the target users or use cases

### Step 2: Roadmap Structure Requirements
Each roadmap.md must include:

1. **Feature Overview**
   - Concise summary of the feature purpose
   - Problem statement it addresses
   - Value proposition and expected outcomes

2. **Scope Definition**
   - Clear boundaries of what is included
   - Explicit list of what is excluded or out of scope
   - Acceptance criteria for completion

3. **Implementation Phases**
   - Break down into logical development stages
   - Order steps from foundation to refinement
   - Indicate dependencies between steps

4. **Technical Considerations**
   - Required technology stack components
   - Potential integration points
   - Foreseeable technical challenges
   - Performance expectations

5. **Testing Strategy**
   - Types of tests needed (unit, integration, user)
   - Test scenarios that validate acceptance criteria
   - Edge cases to be covered

6. **Documentation Requirements**
   - User-facing documentation needs
   - Code documentation standards
   - Knowledge transfer considerations

### Step 3: Timeline and Resource Estimation
- Provide reasonable time estimates for each phase
- Identify potential bottlenecks or high-risk components
- Note resource dependencies (APIs, libraries, services)

### Step 4: Success Metrics
- Define how we'll measure successful implementation
- Establish quality thresholds that must be met
- Create verification checklist for feature completion

## Implementation Guidelines

1. **AI Questioning Approach**
   - If requirements are vague, ask specific clarifying questions
   - Seek examples of similar features for reference
   - Verify understanding before proceeding with roadmap creation

2. **Roadmap Verification**
   - After creating the initial roadmap, the AI should review against the original requirements
   - Highlight any discrepancies or uncertainties
   - Ask follow-up questions to resolve ambiguities

3. **Roadmap Evolution**
   - Establish process for updating the roadmap if requirements change
   - Version control for roadmap revisions
   - Document rationale for significant changes

## Roadmap Usage
- All implementation work must reference the roadmap
- Feature is not considered complete until all roadmap items are addressed
- Deviations from roadmap require explicit approval and documentation
