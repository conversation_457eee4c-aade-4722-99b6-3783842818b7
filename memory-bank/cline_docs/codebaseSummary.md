## Key Components
- AuthService: Handles user authentication and authorization
- DataSyncModule: Manages real-time data synchronization
- APIGateway: Routes requests to appropriate microservices

## Data Flow
1. Client sends request to APIGateway
2. APIGateway authenticates request using AuthService
3. Request is routed to appropriate microservice
4. DataSyncModule ensures real-time updates across connected clients

## External Dependencies
- Supabase for database and authentication
- Stripe for payment processing
- SendGrid for email notifications

## Recent Significant Changes
- [ ] Migrated from MongoDB to PostgreSQL
- [ ] Implemented JWT-based authentication

## User Feedback Integration
