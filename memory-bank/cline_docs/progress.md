# Progress

This file will contain information about what works, what's left to build, and the progress status.

## 2025-08-08

- Updated client Supabase initialization to use anon key only in `src/lib/supabase.ts`.
- Removed any client usage of `process.env`; switched to `import.meta.env` in `src/lib/sync/externalSync.ts`.
- Added Node-only guard to `src/lib/supabase-fetch.js` to prevent accidental browser import.
- Cleaned up log message in `src/lib/setupDatabase.ts` to remove service-role mention.
- Added `VITE_SUPABASE_SERVICE_ROLE_KEY` to `.env.example` for server-side scripts.
- Updated memory-bank docs:
  - `memory-bank/activeContext.md` to reflect anon-key-only usage on the client.
  - `memory-bank/techContext.md` with a Supabase client configuration note.
- Outstanding: some TypeScript/ESLint warnings in `externalSync.ts` (untyped any, unused vars) to address later.
- Implemented auth-first initialization (Option A): `setupDatabase()` is now called only after authentication.
  - Changes in `src/App.tsx`: split auth init and DB init; DB runs post-auth.
  - Changes in `src/components/voice/VoiceInventory.tsx`: fetch categories only after auth.
  - Reason: avoid RLS blocking pre-auth reads that caused false "missing categories" errors.
- Added server-side seeding script `scripts/seed-categories.mjs` and npm script `seed:categories`.
  - Verified run: categories already present.
  - Keeps service-role usage strictly in Node, never in browser.
  
  - Applied remote DB migrations via Supabase CLI to create/seed `public.product_categories` and add FK to `"Products"`.
  - Verified remotely:
    - Table exists with 4 seeded rows.
    - RLS enabled with authenticated SELECT/INSERT/UPDATE policies.
    - `"Products"` has `product_category_id` and `old_category` columns.
    - FK `Products_product_category_id_fkey` references `product_categories`.
  - Repaired migration history to mark applied entries and revert an empty local migration to keep local/remote in sync.
  - Fixed `ProductForm.tsx` accessibility: standardized error rendering and corrected `aria-invalid` usage.