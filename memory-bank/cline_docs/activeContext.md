# Active Context

## Current Focus
✅ Completed:
- Read tables from Supabase database
- Added toast notification components

⚠️ Monitoring:
- None

▶️ Next Steps:
- None

## Recent Changes
- Installed `dotenv` package.
- Created `read_tables.ts` script to query Supabase database.
- Debugged Supabase authentication and schema query errors.
- Added toast, use-toast, and toaster components for notifications
- Installed @radix-ui/react-toast and class-variance-authority packages

## Next Steps
- None
