# System Patterns

This file will contain information about the system's architecture, key technical decisions, and architecture patterns.

## Unified Inventory Event Handling

All inventory actions (receiving, counting, disposal) now flow through:

```mermaid
flowchart LR
    Form[Voice Form] --> EventPayload
    EventPayload --> inventory_events
    inventory_events --> Reporting[Reporting System]
```

Key Features:
- Single source of truth for all inventory actions
- Event_type driven processing
- JSONB metadata field for flexible extensions