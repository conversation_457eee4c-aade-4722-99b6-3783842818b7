# Voice Input System

## Components
1. **VoiceInput Component**
   - Handles voice recording and transcription
   - Uses react-speech-recognition for voice capture
   - Integrates with OpenAI for processing
   - Provides real-time feedback via toast notifications
   - Supports category selection via dropdown
   - Shows voice command examples
   - Displays live transcript
   - Indicates processing status

2. **TranscriptionData Interface**
   - product: Product name
   - quantity: Numerical quantity
   - category: Product category
   - unit: Unit of measurement
   - price: Price per unit
   - vendor: Supplier information
   - origin: Product origin
   - notes: Additional information
   - timestamp: Recording timestamp

## Integration Points
1. **Import Inventory**
   - Voice data processing with error handling
   - Database insertion with transaction support
   - Loading state management
   - Success/error notifications
   - Category synchronization
   - Fallback processing support

2. **Data Processing**
   - OpenAI GPT-3.5 Turbo for primary processing
   - Fallback to basic text processing
   - Validation against product schema
   - Category and unit standardization
   - Timestamp tracking

## Error Handling
- Browser compatibility check
- Transcription errors
- Processing failures
- Database validation errors
- Network connectivity issues
- Retry mechanism with exponential backoff
- Fallback processing mode
- User-friendly error messages

## UI Features
- Record/Stop button
- Processing indicator
- Fallback mode warning
- Live transcript display
- Category selector
- Example commands
- Status indicators
- Loading states

## Dependencies
- react-speech-recognition
- @radix-ui/react-select
- openai
- supabase client
- lucide-react (for icons)
- date-fns (for timestamp formatting)
