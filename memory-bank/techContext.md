# Technical Context

## Development Environment

1. **Frontend**
   - Framework: React.js with TypeScript
   - Bundler: Vite (v5.4.11)
   - Development Server: Port 5177 (strictPort, host:true)
   - State Management: React Hooks
   - UI Components: Radix UI
   - Hot Module Reloading
   - TypeScript Strict Mode
   - ESLint Integration

2. **Backend**
    - Database: Supabase (remote only)
    - Storage: Supabase Storage
    - Authentication: Supabase Auth
    - API: RESTful endpoints
    - Real-time subscriptions
    - Row Level Security
    - Automatic migrations

### Supabase Client Configuration

- Client-side uses the Supabase anon key only via `import.meta.env.VITE_SUPABASE_ANON_KEY`.
- Service role key (`VITE_SUPABASE_SERVICE_ROLE_KEY`) is strictly server-side (Node scripts only).
- Required environment variables:

  - `VITE_SUPABASE_URL`
  - `VITE_SUPABASE_ANON_KEY`
  - `VITE_SUPABASE_SERVICE_ROLE_KEY` (server-side scripts)
- Frontend client is created in `src/lib/supabase.ts`; privileged operations are not performed in the browser.
- All DB queries use exact case for table names. The products table is named "Products" (quoted) in the remote DB.
- Migrations updated to create/seed `product_categories` and add FK to "Products" with quoted identifiers for case sensitivity.

### Server-side Tools

- Seeding: `scripts/seed-categories.mjs` populates required categories using the service role key.
  - Run: `npm run seed:categories`
  - Env: `SUPABASE_SERVICE_ROLE_KEY` (preferred) or `VITE_SUPABASE_SERVICE_ROLE_KEY`
- TempStick migration runner: `apply-tempstick-migration.js` verifies schema/tables/views, applies sample data, performs CRUD tests, and checks RLS policies.
  - Run: `node apply-tempstick-migration.js`
  - Env: `VITE_SUPABASE_URL`, `VITE_SUPABASE_SERVICE_ROLE_KEY` (server-side only)
  - Notes: Run in Node environment only; never expose service role key to the browser.
- App init is gated: database checks run only after auth in `src/App.tsx` to avoid RLS blocks pre-auth.

1. **AI Services**
   - OpenAI Integration
   - Voice Processing
   - Data Validation
   - Error Recovery
   - Fallback Systems

## Core Features

1. **Voice-based Inventory Management**
   - Speech-to-text conversion
   - AI-powered data extraction
   - Real-time validation
   - Database integration
   - Error handling
   - Loading states
   - User feedback

2. **Data Entry**
   - Voice input
   - File import (CSV)
   - Manual entry
   - Batch processing
   - Validation rules
   - Error reporting
   - Success feedback

3. **Validation System**
   - Data type checks
   - Required field validation
   - Business rule enforcement
   - Error reporting
   - Toast notifications
   - Loading indicators
   - Status updates

4. **Reporting**
   - Event type filters
   - Data aggregation
   - Export capabilities
   - Visual representations
   - Custom queries
   - Date ranges
   - Unit conversion

## Development Pipeline

1. **Database Setup**
   - Table creation
   - Schema validation
   - Index optimization
   - Data migrations
   - Backup strategy
   - Recovery plans
   - Performance tuning

2. **AI Integration**
   - Voice input validation
   - Data extraction
   - Error handling
   - Fallback mechanisms
   - Performance optimization
   - Model tuning
   - Context management

3. **Database Operations**
   - CRUD functionality
   - Transaction management
   - Error recovery
   - Data consistency
   - Audit logging
   - Cache management
   - Query optimization

4. **Testing**
   - Voice input pipeline
   - Database operations
   - UI components
   - Integration tests
   - Unit tests
   - End-to-end tests
   - Performance testing

## Dependencies

1. **Core**
   - react: ^18.2.0
   - react-dom: ^18.2.0
   - typescript: ^5.7.3
   - vite: ^5.0.12
   - @supabase/supabase-js: ^2.39.7
   - openai: ^4.28.0

2. **UI Components**
   - @radix-ui/react-toast: ^1.2.6
   - @radix-ui/react-tabs: ^1.1.3
   - @radix-ui/react-select: ^2.1.6
   - @radix-ui/react-alert-dialog: ^1.1.6
   - lucide-react: ^0.469.0

3. **Voice Processing**
   - react-speech-recognition: ^3.10.0
   - regenerator-runtime: ^0.14.1

4. **Utilities**
   - class-variance-authority: ^0.7.1
   - clsx: ^2.1.1
   - tailwind-merge: ^2.6.0
   - date-fns: ^3.6.0
   - zod: ^3.22.4

## Development Tools

1. **Build Tools**
   - vite
   - @vitejs/plugin-react
   - vite-plugin-commonjs
   - rollup
   - parcel

2. **Testing Tools**
   - @playwright/test
   - @testing-library/react
   - @testing-library/jest-dom
   - jest

3. **Code Quality**
   - eslint
   - typescript-eslint
   - prettier
   - autoprefixer
   - postcss
   - tailwindcss

## Known Issues

1. **Dependencies**
   - 5 vulnerabilities in npm packages
   - 4 moderate, 1 high severity
   - Requires dependency review
   - Alternative dependencies needed

2. **Development Server**
    - Dev server pinned to 5177 with strictPort (no auto-switch)
    - host:true replaces CLI --host
    - Hot reload optimization needed
    - WebSocket connection handling
