# Inventory Management System

## Unified Events Table
- Primary table: inventory_events
- UUID primary keys
- Event-based tracking
- Unit handling at event level
- Flexible metadata storage

## Event Types
1. **Receiving**
   - Product intake
   - Quantity tracking
   - Supplier information
   - Cost recording
   - Origin tracking

2. **Sales**
   - Quantity sold
   - Price information
   - Customer details
   - Gross/net amounts
   - Transaction metadata

## Unit Management
- Context-specific units
- Multiple units per product
- Supported units:
  - Pounds (lbs)
  - Kilograms (kg)
  - Cases
  - Individual units
- Unit conversion handling

## Data Entry Methods
1. **Voice Input**
   - Speech recognition
   - AI processing
   - Data validation
   - Error handling

2. **File Import**
   - CSV file support
   - Data transformation
   - Batch processing
   - Validation checks

3. **Manual Entry**
   - Form-based input
   - Real-time validation
   - Error feedback
   - Auto-completion

## Reporting Features
1. **Event Filtering**
   - Type-based filters
   - Date ranges
   - Product categories
   - Custom queries

2. **Data Analysis**
   - Inventory levels
   - Sales trends
   - Cost tracking
   - Profit margins

## Business Rules
1. **Validation**
   - Required fields
   - Data type checks
   - Business logic
   - Cross-reference validation

2. **Error Handling**
   - Input validation
   - Processing errors
   - Database errors
   - Network issues

## Future Enhancements
1. **Bulk Operations**
   - Event editing
   - Batch updates
   - Mass imports
   - Bulk validations

2. **Advanced Features**
   - Event type filters
   - Custom reports
   - Analytics dashboard
   - Export options
