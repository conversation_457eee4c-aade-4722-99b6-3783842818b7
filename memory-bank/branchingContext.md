# Branching Strategy and CI/CD Policy

## Branch Model
- Default branch: `main`
- Development branch: `dev`
- Workflow:
  - Feature work happens on short-lived feature branches from `dev`
  - Open PRs into `dev` for integration
  - Promotion to `main` via PR from `dev` after CI passes and review

## Recent Migration
- 2025-08-08: Promoted local `dev` to `main` (exact match)
  - Command: `git reset --hard dev` on local `main`
  - Pushed with `--force-with-lease` to update `origin/main`
- Safety backup tag:
  - `backup/main-before-2025-08-08` created and pushed
- `dev` reset to match new `main` and force-pushed to `origin/dev`

## Branch Protection (configure in GitHub UI)
- `main` (strict):
  - Require PRs before merging
  - Require status checks to pass:
    - `lint`
    - `typecheck`
    - `build`
  - Dismiss stale approvals on new commits
  - Restrict who can push (no direct pushes; no force pushes)
  - Optional: Require linear history
- `dev` (recommended):
  - Require PRs before merging
  - Require status checks to pass: `lint`, `typecheck`, `build`
  - Allow force pushes: disabled (recommended)

## CI Configuration (GitHub Actions)
- Workflow file: `.github/workflows/ci.yml`
- Triggers: push and pull_request to `main`, `dev`
- Jobs:
  - `lint`: ESLint against repo
  - `typecheck`: TypeScript `tsc --noEmit`
  - `build`: Vite build (requires secrets)
- Required secrets for build job:
  - `VITE_SUPABASE_URL`
  - `VITE_SUPABASE_ANON_KEY`
  - `VITE_SUPABASE_SERVICE_ROLE_KEY` (if used in build-scripts)
  - `VITE_OPENAI_API_KEY`

## Developer Commands
- Sync `dev` to latest `main` (fast-forward):
  ```bash
  git fetch origin
  git switch dev
  git merge --ff-only origin/main
  ```
- Create feature branch:
  ```bash
  git switch -c feature/<topic> dev
  ```
- Update feature from dev:
  ```bash
  git fetch origin
  git switch feature/<topic>
  git merge --ff-only origin/dev
  ```
- Promote dev -> main via PR (preferred) or exact match (admin only when necessary)

## Notes
- Avoid force-pushing protected branches except during approved maintenance windows.
- Keep feature branches small and focused; prefer frequent PRs.
- Ensure docs are updated alongside changes impacting workflows or CI.
