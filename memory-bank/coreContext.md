# Core Project Context

## Primary Focus
1. **Voice-Based Inventory Management**
   - Speech-to-text conversion
   - AI-powered data processing
   - Real-time validation
   - Database integration
   - User feedback system
   - Error handling
   - Fallback mechanisms

2. **Database Integration**
   - Unified inventory_events table
   - Event-based tracking
   - Unit-level management
   - Transaction support
   - Data consistency
   - Audit logging

3. **Data Entry & Validation**
   - Voice input processing
   - File import support
   - Real-time validation
   - Error reporting
   - Business rule enforcement
   - Data consistency checks

## Development Pipeline
1. **Database Setup**
   - Table structure
   - Schema validation
   - Index optimization
   - Migration support
   - Backup strategy

2. **AI Integration**
   - Voice input validation
   - Data extraction
   - Error handling
   - Fallback processing
   - Performance optimization

3. **Database Operations**
   - CRUD functionality
   - Transaction management
   - Error recovery
   - Data consistency
   - Audit trails

4. **Testing Pipeline**
   - Voice input testing
   - Database operations
   - Integration tests
   - Error scenarios
   - Performance metrics

## Key Features
1. **Voice Processing**
   - OpenAI integration
   - Speech recognition
   - Data extraction
   - Category matching
   - Unit standardization

2. **Data Management**
   - Event tracking
   - Unit handling
   - Product references
   - Metadata storage
   - Image management

3. **User Interface**
   - Toast notifications
   - Loading states
   - Error feedback
   - Success messages
   - Progress tracking

## Monitoring Areas
1. **Data Consistency**
   - Validation rules
   - Transaction integrity
   - Error tracking
   - Data quality
   - Audit logging

2. **Image Upload**
   - File validation
   - Storage reliability
   - Access control
   - Size optimization
   - Backup support

## Next Steps
1. **Event Management**
   - Type filters
   - Reporting dashboard
   - Bulk editing
   - Custom queries
   - Data export

2. **Voice System**
   - Command optimization
   - Error reduction
   - Processing speed
   - Accuracy improvement
   - User feedback

## Environment
1. **Development Server**
   - Vite bundler
   - Port 5176
   - Hot reloading
   - TypeScript support
   - React integration

2. **External Services**
   - Supabase (puzjricwpsjusjlgrwen)
   - OpenAI API
   - Image storage
   - Authentication
   - Database access

3. **Dependencies**
   - React with TypeScript
   - Radix UI components
   - OpenAI integration
   - Supabase client
   - Voice processing
