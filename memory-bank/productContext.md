# Product Context

## Categories
- Finfish
- Shellfish
- Crustaceans
- Specialty

## Units
- kg
- lbs
- units
- cases

## Product Information
Each product in the inventory system includes:
- Name
- Category
- Subcategory (optional)
- Quantity
- Price
- Vendor (optional)
- Origin (optional)
- Storage Temperature (optional)
- Handling Instructions (optional)
- Unit of Measurement
- Notes (optional)

## Voice Input Processing
Voice input is processed using:
1. OpenAI GPT-3.5 Turbo for primary processing
2. Fallback to basic text processing if AI fails
3. Validation of required fields and data types

## Data Validation
- Required fields: date, product name, SKU, category, unit
- Numeric validations for: stock, cost, price, min_stock
- Date format validation
- Sales amount validations (gross vs net)