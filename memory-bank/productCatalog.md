# Product Catalog System

## Categories
1. **Finfish**
   - Salmon
   - Tuna
   - Cod
   - Halibut
   - Other finfish species

2. **Shellfish**
   - Oysters
   - Clams
   - Mussels
   - Scallops
   - Other shellfish species

3. **Crustaceans**
   - Lobster
   - Crab
   - Shrimp
   - Crayfish
   - Other crustacean species

4. **Specialty**
   - Sea Urchin
   - Caviar
   - Escargot
   - Specialty items

## Product Information
1. **Core Fields**
   - Name
   - Category
   - Subcategory
   - Default unit
   - Current stock
   - Minimum stock level
   - Price
   - Cost

2. **Additional Fields**
   - Supplier
   - Origin
   - Storage temperature
   - Handling instructions
   - Notes
   - Images

3. **Metadata**
   - Created date
   - Last updated
   - Last inventory check
   - Stock history
   - Price history

## Units Management
1. **Standard Units**
   - Pounds (lbs) - default
   - Kilograms (kg)
   - Cases
   - Individual units

2. **Unit Features**
   - Unit conversion
   - Default unit per product
   - Multiple units per product
   - Unit validation
   - Price per unit

## Data Entry Methods
1. **Voice Input**
   - Product recognition
   - Quantity parsing
   - Unit detection
   - Price extraction
   - Supplier identification

2. **File Import**
   - CSV format
   - Batch processing
   - Field mapping
   - Data validation
   - Error handling

3. **Manual Entry**
   - Form-based input
   - Real-time validation
   - Category selection
   - Unit specification
   - Price/cost entry

## Integration Points
1. **Inventory Events**
   - Stock updates
   - Price changes
   - Location tracking
   - Movement history
   - Audit trail

2. **Voice System**
   - Product matching
   - Category validation
   - Unit standardization
   - Price verification
   - Data extraction

3. **Reporting**
   - Stock levels
   - Value calculation
   - Movement analysis
   - Price trends
   - Category breakdown

## Business Rules
1. **Validation**
   - Required fields
   - Unit compatibility
   - Price/cost ranges
   - Stock thresholds
   - Category constraints

2. **Stock Management**
   - Minimum levels
   - Maximum levels
   - Reorder points
   - Stock alerts
   - Movement tracking

## Future Enhancements
1. **Product Features**
   - Seasonal tracking
   - Quality ratings
   - Vendor ratings
   - Location tracking
   - Batch tracking

2. **Integration**
   - Barcode scanning
   - Image recognition
   - Supplier portal
   - Customer portal
   - Analytics dashboard
