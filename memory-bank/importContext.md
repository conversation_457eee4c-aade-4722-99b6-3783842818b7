# Import System

## Import Methods

1. **Voice Import**
   - Speech-to-text conversion
   - AI-powered data extraction
   - Category selection
   - Live transcript display
   - Example commands
   - Fallback processing
   - Error handling
   - Loading states

2. **File Import (CSV)**
   - File type validation
   - Header mapping
   - Data preview
   - Batch processing
   - Error reporting
   - Progress tracking

## Data Processing

1. **Voice Processing**
   - OpenAI GPT-3.5 Turbo integration
   - Basic text processing fallback
   - Category validation
   - Unit standardization
   - Timestamp tracking

2. **File Processing**
   - CSV parsing
   - Data transformation
   - Field mapping
   - Validation checks
   - Error collection

## CSV Mapping UI

- **Auto-mapping with synonyms**: Attempts to match CSV headers to fields (`name`, `category`, `supplier`, `amount`, `price`, `cost`, `origin`, `notes`) using common synonyms.
- **Manual mapping & constants**: Users can map a CSV column or provide a constant value per field. If the CSV lacks a required field, supplying a constant satisfies validation.
- **Required fields**:
  - Products: `name`, `sku`, `category`, `unit`
  - Inventory Events: `event_type`, `quantity`
- **Missing required helper**: The wizard shows a "Missing required fields" panel listing unmet fields and provides constant inputs to fill them.
- **Validation gating**: Import is blocked until all required fields have either a mapped column or a constant.
- **Mapped preview**: Shows the first few transformed rows for verification before import.
- **Paste CSV fallback**: Textarea allows pasting raw CSV; parsing triggers the same mapping UI and validations.

## Submission to Database

- **Submit button**: A "Submit to Database" button is available on the "Validate & Preview" step in `ImportWizard`.
- **Validation gating**: Submission is disabled until required mappings pass validation and rows are present.
- **Targets**:
  - `Products`: Inserts mapped fields (`name`, `sku`, `category`, `unit`, `price`, `cost`, `description`, `origin`, `notes`, `metadata`).
  - `inventory_events`: Inserts mapped fields, normalizing `event_type` (`"sales"` → `"sale"`), supports optional `timestamp` mapped to `created_at`, and includes `product_id`, `name`, `quantity`, `unit_price`, `total_amount`, `category`, `images`, `notes`, `metadata`.
- **Chunking**: Rows are inserted in batches of 500 per request for reliability.
- **Feedback**: Success and error toasts indicate completion and row counts; loading state shown during submission.

## Database Integration

1. **Transaction Support**
   - Atomic operations
   - Rollback on failure
   - Data consistency
   - Error recovery

2. **Data Validation**
   - Required fields
   - Data types
   - Business rules
   - Cross-references

## User Interface

1. **Voice Import Tab**
   - Record/Stop button
   - Category selector
   - Processing indicator
   - Transcript display
   - Example commands
   - Status messages

2. **File Import Tab**
   - File upload button
   - Preview table
   - Import button
   - Progress indicator
   - Error display

## Feedback System

1. **Toast Notifications**
   - Success messages
   - Error alerts
   - Processing status
   - Warning notices

2. **Visual Feedback**
   - Loading spinners
   - Progress indicators
   - Error highlights
   - Success states

## Error Handling

1. **Voice Import**
   - Browser compatibility
   - Network issues
   - Processing failures
   - Database errors
   - Validation failures

2. **File Import**
   - File type errors
   - Format issues
   - Data validation
   - Import failures
   - System errors

## Dependencies

- react-speech-recognition
- openai
- @radix-ui/react-tabs
- @radix-ui/react-select
- lucide-react
- date-fns
- supabase client
