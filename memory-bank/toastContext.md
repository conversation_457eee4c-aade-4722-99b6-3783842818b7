# Toast Notification System

## Components
1. **Toast Component (`toast.tsx`)**
   - Base component for displaying notifications
   - Supports variants: default, destructive
   - Customizable styling using class-variance-authority
   - Includes title, description, and action support

2. **Toast Hook (`use-toast.ts`)**
   - Custom hook for managing toast state
   - Provides methods: toast(), dismiss()
   - <PERSON>les toast lifecycle and positioning
   - Supports multiple concurrent toasts

3. **Toaster Component (`toaster.tsx`)**
   - Container for rendering active toasts
   - Manages toast viewport and positioning
   - Integrated into main App component
   - Handles toast animations and transitions

## Usage
Toasts are used throughout the application for:
- Success confirmations
- Error notifications
- Warning messages
- Processing status updates
- User action feedback

## Integration
The toast system is integrated with:
- Voice input processing feedback
- Data validation results
- File import status updates
- Database operation results

## Dependencies
- @radix-ui/react-toast
- class-variance-authority
- clsx
- tailwind-merge
