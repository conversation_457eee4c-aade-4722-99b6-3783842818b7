# Traceability & Compliance Context (FSMA 204 + HACCP)

This document summarizes the regulatory requirements from FSMA 204 (Food Traceability Rule), GDST 1.2, and Seafood HACCP recordkeeping, and maps them to our database design.

## Local Standards in repo
- `Traceability standards/GDST-1.2-Technical-Implementation-Guidance.pdf`
- `Traceability standards/GDST-1.2-Core-Normative-Standards-1.pdf`
- `Traceability standards/GDST-1.2-Basic-Universal-List-of-KDEs-1.xlsx`
- `Traceability standards/GS1_Foundation_for_Fish_Seafood_Aquaculture_Traceability_Guideline.pdf`

## FSMA 204 essentials
- Foods on FDA FTL (includes finfish, crustaceans, molluscan shellfish, smoked fish).
- Maintain records of KDEs at CTEs and provide to FDA within 24 hours.
- Assign and carry a Traceability Lot Code (TLC); maintain linkage across shipping/receiving and transformation.
- Traceability plan: procedures for TLC assignment, CTE/KDE capture, contact person(s), and record storage/export.

### Critical Tracking Events (CTEs)
- Harvest/Catch (seafood-specific)
- Landing/First Receiver
- Shipping
- Receiving
- Transformation (repack, combine, cut, cook, smoke, reprocess)

### Key Data Elements (KDE) categories
- Product identifiers (GTIN, product description, species/scientific name)
- Quantity and unit
- Dates/times of each CTE
- TLC (source TLCs and output TLC for transformation)
- Source and destination identifiers (entity + location; GLN where applicable)
- Reference docs (BOL, invoice, COA), transporter, temperature/handling where relevant

## Seafood HACCP essentials
- Hazard analysis per product/process.
- CCP definitions with critical limits.
- Monitoring logs (what/when/who/measurement/outcome).
- Corrective actions for deviations.
- Verification (reviews, calibrations) and validation records.
- Sanitation SSOP logs; receiving controls (e.g., histamine species time/temperature, parasite destruction, c. botulinum for ROP/smoked, shellfish tags).

## Proposed database blueprint (additions)

### Master data
- `Partners` (id, type: supplier/customer/carrier, name, GLN, contact)
- `Locations` (id, partner_id, GLN, name, address, role)
- `Vessels` (id, name, flag, registration, gear_type, owner_partner_id)

### Product extensions
- `Products` add: gtin, scientific_name, market_name, catch_method, fao_area/area_code, aquaculture_farm_id, storage_temp_min/max

### Lots & TLC
- `Lots` (id, tlc UNIQUE, product_id, origin_country, harvest_or_prod_date, vessel_id/farm_id, landing_date, initial_qty, uom, status, expiry_date, notes)
- `LotDocuments` (id, lot_id, type, url/hash, metadata)

### CTE Events (FSMA 204)
- `TraceabilityEvents` (id, event_type ENUM: harvest|landing|shipping|receiving|transformation, event_time, actor_partner_id, actor_location_id, reference_doc, transporter_partner_id, temperature_data jsonb, notes)
- `EventLots` (event_id, lot_id, role ENUM: input|output, qty, uom)
  - Transformation: one event with multiple input rows and one output row (new TLC).
  - Shipping/Receiving: role='output' (ship) at source, role='input' (receive) at destination.

### HACCP records
- `Hazards` (id, product_id/process_id, hazard, rationale)
- `CCPs` (id, product_id/process_id, name, critical_limits jsonb, monitoring_method)
- `CCPMonitoringLogs` (id, ccp_id, lot_id, timestamp, measurement jsonb, result within_limits bool, monitored_by)
- `CorrectiveActions` (id, monitoring_log_id, action, disposition, performed_by, timestamp)
- `Verifications` (id, ccp_id, type, details, performed_by, timestamp)
- `Calibrations` (id, instrument, details, performed_by, timestamp)
- `SanitationLogs` (id, area, procedure, verified_by, timestamp, notes)

### Views/exports
- `fsma_traceability_view` (materialized): flattened join by TLC to support 24-hour FDA request.

## Mapping to current schema
- We already have `Products` and `inventory_events` (receiving/sales). Gaps:
  - No formal `Lots`/`TLC` and no input/output linkage for transformation.
  - No master data for `Partners`/`Locations` with GLNs.
  - No `TraceabilityEvents`/`EventLots` to capture KDEs at CTEs.
  - No HACCP structures (CCP, monitoring, corrective, verification, sanitation).

## Minimal viable compliance (phase 1)
1. Add `Lots` with auto-generated `tlc` at receiving; require lot selection for sales/shipments.
2. Add `TraceabilityEvents` + `EventLots` for receiving and shipping; store KDEs (qty/uom, dates, source/destination partner/location).
3. Add `Partners`/`Locations` master data and connect to events.
4. Add `fsma_traceability_view` for export by `tlc`.

## Phase 2 (seafood, transformation, HACCP)
- Support `transformation` events (repack, combine) with new output TLC and input linkage.
- Extend product attributes (scientific name, catch method, FAO area, GTIN).
- Add HACCP tables and UI for CCP monitoring logs, corrective actions, and verification.
- Temperature logging at receiving, storage, and shipping.

## Policies & security
- Enforce that sales/shipments require a `lot_id` (cannot deplete inventory without lot linkage).
- RLS by tenant and least-privilege policies for event writes.
- Server-side functions: `assign_tlc(event)` on receiving/transformation; `validate_kdes()`.

## Open questions for business fit
- Level of GS1/GDST adoption (GTIN/GLN/SSCC/EPCIS now vs later).
- Granularity of lots (per supplier shipment, per day, per production batch).
- Specific HACCP CCPs to model first (e.g., cold storage temp, receiving temp for histamine species, smoke process parameters).
