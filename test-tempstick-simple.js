#!/usr/bin/env node

/**
 * Simple TempStick API Test
 * Tests API connectivity without importing TypeScript files
 */

import dotenv from 'dotenv'

dotenv.config()

const TEMPSTICK_API_BASE = 'https://api.tempstick.com/v1'

async function testTempStickAPI() {
  console.log('🌡️  TempStick API Direct Test')
  console.log('=============================')
  
  const apiKey = process.env.VITE_TEMPSTICK_API_KEY
  
  if (!apiKey) {
    console.log('❌ VITE_TEMPSTICK_API_KEY not found in environment')
    console.log('Please set your TempStick API key in the .env file')
    return false
  }
  
  console.log('🔑 API key found, testing real API...')
  
  try {
    console.log('\n📊 Test 1: GET /sensors')
    console.log('-----------------------')
    
    const sensorsResponse = await fetch(`${TEMPSTICK_API_BASE}/sensors`, {
      headers: {
        'X-API-KEY': apiKey,
        'Content-Type': 'application/json'
      }
    })
    
    console.log(`   Status: ${sensorsResponse.status} ${sensorsResponse.statusText}`)
    
    if (sensorsResponse.ok) {
      const sensorsData = await sensorsResponse.json()
      console.log(`   ✅ API responded successfully`)
      console.log(`   📊 Sensors found: ${sensorsData.length || 0}`)
      
      if (sensorsData.length > 0) {
        const sensor = sensorsData[0]
        console.log(`   📍 Sample sensor: ${sensor.name || sensor.device_name}`)
        
        // Test specific sensor readings
        await testSensorReadings(sensor.id, apiKey)
      }
      
    } else if (sensorsResponse.status === 401) {
      console.log('   ❌ Authentication failed - check API key')
    } else if (sensorsResponse.status === 429) {
      console.log('   ⚠️  Rate limited - API working but too many requests')
    } else {
      console.log(`   ❌ API error: ${sensorsResponse.status}`)
      const errorText = await sensorsResponse.text()
      console.log(`   Error details: ${errorText}`)
    }
    
    return sensorsResponse.ok
    
  } catch (error) {
    console.log(`   ❌ Network error: ${error.message}`)
    return false
  }
}

async function testSensorReadings(sensorId, apiKey) {
  try {
    console.log('\n🌡️  Test 2: GET /sensors/{id}/readings')
    console.log('------------------------------------')
    
    const readingsResponse = await fetch(`${TEMPSTICK_API_BASE}/sensors/${sensorId}/readings?limit=5`, {
      headers: {
        'X-API-KEY': apiKey,
        'Content-Type': 'application/json'
      }
    })
    
    console.log(`   Status: ${readingsResponse.status} ${readingsResponse.statusText}`)
    
    if (readingsResponse.ok) {
      const readings = await readingsResponse.json()
      console.log(`   ✅ Readings retrieved: ${readings.length || 0}`)
      
      if (readings.length > 0) {
        const latest = readings[0]
        console.log(`   📊 Latest: ${latest.temperature}°F, ${latest.humidity}% humidity`)
        console.log(`   ⏰ Time: ${latest.timestamp}`)
      }
    } else {
      console.log(`   ⚠️  Could not retrieve readings: ${readingsResponse.status}`)
    }
    
  } catch (error) {
    console.log(`   ❌ Readings error: ${error.message}`)
  }
}

async function testServiceFiles() {
  console.log('\n📁 Testing Service Files')
  console.log('------------------------')

  try {
    // Check if service files exist
    const fs = await import('fs')
    const serviceFiles = [
      './src/types/tempstick.ts',
      './src/lib/config/tempstick-config.ts',
      './src/lib/tempstick-service.ts'
    ]

    let foundFiles = 0
    for (const file of serviceFiles) {
      if (fs.existsSync(file)) {
        console.log(`   ✅ ${file}`)
        foundFiles++
      } else {
        console.log(`   ❌ ${file} - missing`)
      }
    }

    console.log(`\n   📊 Service files: ${foundFiles}/${serviceFiles.length} available`)

    if (foundFiles === serviceFiles.length) {
      console.log('   ✅ Service files ready')
      return true
    } else {
      console.log('   ⚠️  Some service files missing')
      return false
    }

  } catch (error) {
    console.log(`   ❌ Service files test error: ${error.message}`)
    return false
  }
}

async function testConfigurationFiles() {
  console.log('\n⚙️  Test 3: Configuration Files')
  console.log('------------------------------')
  
  try {
    const fs = await import('fs')
    const configFiles = [
      { path: './src/lib/tempstick-service.ts', desc: 'Main service' },
      { path: './src/types/tempstick.ts', desc: 'Type definitions' },
      { path: './src/lib/config/tempstick-config.ts', desc: 'Configuration' },
      { path: './src/lib/mock-tempstick-data.ts', desc: 'Mock data' }
    ]
    
    let readyFiles = 0
    for (const file of configFiles) {
      if (fs.existsSync(file.path)) {
        console.log(`   ✅ ${file.desc}: ${file.path}`)
        readyFiles++
      } else {
        console.log(`   ❌ ${file.desc}: ${file.path} - missing`)
      }
    }
    
    console.log(`\n   📊 Service files: ${readyFiles}/${configFiles.length} ready`)
    return readyFiles === configFiles.length
    
  } catch (error) {
    console.log(`   ❌ Configuration test error: ${error.message}`)
    return false
  }
}

async function main() {
  console.log('Starting TempStick API connectivity tests...\n')
  
  const apiWorking = await testTempStickAPI()
  const serviceFilesReady = await testServiceFiles()
  const configReady = await testConfigurationFiles()
  
  console.log('\n🎯 Test Results Summary')
  console.log('=======================')
  
  console.log(`📡 TempStick API: ${apiWorking ? '✅ Working' : '❌ Not accessible'}`)
  console.log(`📁 Service files: ${serviceFilesReady ? '✅ Available' : '❌ Missing files'}`)
  console.log(`⚙️  Configuration: ${configReady ? '✅ Ready' : '❌ Files missing'}`)

  if (configReady && serviceFilesReady) {
    console.log('\n✅ TempStick service layer is ready')
    if (apiWorking) {
      console.log('✅ Real API connectivity confirmed')
    } else {
      console.log('❌ API not accessible - please check your API key')
    }
    console.log('\n🚀 Ready for dashboard integration!')
  } else {
    console.log('\n❌ Service layer needs setup')
    console.log('⚠️  Check file paths and configuration')
  }
  
  console.log('\n📋 Next Steps:')
  console.log('   1. ✅ TempStick API connectivity - TESTED')
  console.log('   2. ⏳ Validate service layer operations')  
  console.log('   3. ⏳ Test dashboard real-time updates')
  console.log('   4. ⏳ Create database tables (manual step)')
  
  return { apiWorking, serviceFilesReady, configReady }
}

main().catch(console.error)