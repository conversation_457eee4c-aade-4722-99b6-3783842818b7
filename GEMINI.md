# GEMINI.md

## Project Overview

This project is a comprehensive seafood inventory management and compliance tracking system named "Pacific Cloud Seafoods Manager". It's a modern web application built with a focus on voice-enabled data entry, real-time traceability, and HACCP compliance monitoring.

**Key Technologies:**

*   **Frontend:** React 18, TypeScript, Vite, Tailwind CSS, Radix UI
*   **Backend & Database:** Supabase (PostgreSQL), Row Level Security (RLS)
*   **Voice Processing:** OpenAI Whisper API
*   **Infrastructure:** Vercel (Serverless Functions, Global CDN)
*   **Testing:** Vitest, Playwright

**Architecture:**

The project follows a standard monorepo-like structure with separate directories for the main application source code (`src`), serverless API endpoints (`api`), database migrations (`supabase`), documentation (`docs`), and end-to-end tests (`e2e`).

## Building and Running

### Prerequisites

*   Node.js 18+
*   npm or yarn
*   Supabase account
*   OpenAI API key

### Installation and Setup

1.  **Clone the repository:**
    ```bash
    git clone https://github.com/paccloud/PCS-Seafood-Manager.git
    cd PCS-Seafood-Manager
    ```

2.  **Install dependencies:**
    ```bash
    npm install
    ```

3.  **Set up environment variables:**
    ```bash
    cp .env.example .env
    # Edit .env with your Supabase and OpenAI credentials
    ```

4.  **Run database migrations and seed data:**
    ```bash
    npm run db:migrate
    npm run seed:categories
    npm run seed:products
    ```

### Development

*   **Start the development server:**
    ```bash
    npm run dev
    ```

### Testing

*   **Run all tests:**
    ```bash
    npm run test:all
    ```

*   **Run unit tests:**
    ```bash
    npm run test
    ```

*   **Run end-to-end tests:**
    ```bash
    npm run test:e2e
    ```

### Building for Production

*   **Create a production build:**
    ```bash
    npm run build:production
    ```

## Development Conventions

*   **Coding Style:** The project uses ESLint and Prettier for code formatting and linting. Run `npm run quality:fix` to automatically format and fix code.
*   **Testing:** The project has a comprehensive testing strategy, including unit, integration, and end-to-end tests. All new features should be accompanied by corresponding tests.
*   **Commits:** Use semantic commit messages.
*   **Branching:** Create feature branches from `main` and submit pull requests for review.
