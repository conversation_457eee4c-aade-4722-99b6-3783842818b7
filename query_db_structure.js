const fetch = require('node-fetch');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

async function fetchData(endpoint, query = '') {
  try {
    const response = await fetch(`${supabaseUrl}${endpoint}${query}`, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseKey}`,
        'apikey': supabaseKey
      }
    });
    
    if (!response.ok) {
      throw new Error(`HTTP Error: ${response.status} - ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error(`Error fetching from ${endpoint}:`, error);
    return null;
  }
}

async function fetchTableNames() {
  try {
    // Get list of tables using information_schema
    const result = await fetchData('/rest/v1/rpc/execute_sql', '');
    
    const postData = {
      sql: `
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        ORDER BY table_name
      `
    };
    
    const response = await fetch(`${supabaseUrl}/rest/v1/rpc/execute_sql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseKey}`,
        'apikey': supabaseKey
      },
      body: JSON.stringify(postData)
    });
    
    if (!response.ok) {
      throw new Error(`HTTP Error: ${response.status} - ${response.statusText}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching table names:', error);
    
    // Fallback to listing all tables from REST API
    console.log('Attempting to list tables directly from REST API...');
    
    // Try to get data from a known table
    const knownTables = ['products', 'categories', 'vendors', 'events'];
    
    for (const table of knownTables) {
      const tableData = await fetchData(`/rest/v1/${table}`, '?limit=1');
      if (tableData) {
        console.log(`Found table: ${table}`);
      }
    }
    
    return null;
  }
}

async function fetchTableColumns(tableName) {
  try {
    const postData = {
      sql: `
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns
        WHERE table_schema = 'public' AND table_name = '${tableName}'
        ORDER BY ordinal_position
      `
    };
    
    const response = await fetch(`${supabaseUrl}/rest/v1/rpc/execute_sql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseKey}`,
        'apikey': supabaseKey
      },
      body: JSON.stringify(postData)
    });
    
    if (!response.ok) {
      throw new Error(`HTTP Error: ${response.status} - ${response.statusText}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`Error fetching columns for table ${tableName}:`, error);
    return null;
  }
}

async function fetchSampleData(tableName) {
  try {
    const data = await fetchData(`/rest/v1/${tableName}`, '?limit=2');
    return data;
  } catch (error) {
    console.error(`Error fetching sample data for table ${tableName}:`, error);
    return null;
  }
}

async function generateTypeScriptInterface(tableName, columns) {
  if (!columns || columns.length === 0) return null;
  
  const typeMapping = {
    'character varying': 'string',
    'varchar': 'string',
    'text': 'string',
    'integer': 'number',
    'smallint': 'number',
    'bigint': 'number',
    'decimal': 'number',
    'numeric': 'number',
    'real': 'number',
    'double precision': 'number',
    'boolean': 'boolean',
    'date': 'string', // or Date in runtime
    'timestamp with time zone': 'string', // or Date in runtime
    'timestamp without time zone': 'string', // or Date in runtime
    'jsonb': 'Record<string, unknown>',
    'json': 'Record<string, unknown>',
    'uuid': 'string'
  };
  
  // Convert snake_case to PascalCase
  const interfaceName = tableName
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join('');
  
  let interfaceCode = `export interface ${interfaceName} {\n`;
  
  for (const column of columns) {
    const { column_name, data_type, is_nullable } = column;
    const tsType = typeMapping[data_type] || 'any';
    const nullable = is_nullable === 'YES' ? '?' : '';
    
    interfaceCode += `  ${column_name}${nullable}: ${tsType};\n`;
  }
  
  interfaceCode += '}\n';
  return interfaceCode;
}

async function main() {
  console.log('Fetching database structure from Supabase...');
  
  const tables = await fetchTableNames();
  
  if (tables && tables.length > 0) {
    console.log(`Found ${tables.length} tables in the database.`);
    
    let interfaces = '';
    
    for (const tableRow of tables) {
      const tableName = tableRow.table_name;
      console.log(`\nTable: ${tableName}`);
      
      const columns = await fetchTableColumns(tableName);
      if (columns && columns.length > 0) {
        console.log(`Columns for ${tableName}:`);
        console.table(columns);
        
        const interfaceCode = await generateTypeScriptInterface(tableName, columns);
        if (interfaceCode) {
          interfaces += interfaceCode + '\n';
        }
      }
      
      const sampleData = await fetchSampleData(tableName);
      if (sampleData && sampleData.length > 0) {
        console.log(`Sample data from ${tableName}:`);
        console.log(sampleData);
      }
    }
    
    console.log('\nGenerated TypeScript Interfaces:');
    console.log(interfaces);
  } else {
    console.log('Could not retrieve table names. Trying alternative approach...');
    
    // Fallback to listing known tables
    const knownTables = ['products', 'categories', 'vendors', 'events', 'customers', 'batches'];
    
    for (const tableName of knownTables) {
      console.log(`\nChecking for table: ${tableName}`);
      
      const sampleData = await fetchSampleData(tableName);
      if (sampleData) {
        console.log(`Found table: ${tableName}`);
        console.log(`Sample data from ${tableName}:`);
        console.log(sampleData);
      } else {
        console.log(`Table '${tableName}' not found or empty.`);
      }
    }
  }
}

main().catch(error => {
  console.error('Unhandled error:', error);
});
