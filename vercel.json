{"version": 2, "buildCommand": "npm run build:production", "outputDirectory": "dist", "framework": "vite", "installCommand": "npm ci --prefer-offline --no-audit", "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(self), geolocation=(), payment=(), usb=()"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.openai.com https://api.openai.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://*.supabase.co https://api.openai.com wss://*.supabase.co; media-src 'self' blob:; worker-src 'self' blob:; font-src 'self' https://fonts.gstatic.com"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "X-Robots-Tag", "value": "noindex, nofollow"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*\\.(?:css|js))", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}], "rewrites": [{"source": "/((?!api/).*)", "destination": "/index.html"}], "functions": {"app/api/**": {"runtime": "nodejs18.x", "maxDuration": 30, "memory": 1024}}, "env": {"NODE_ENV": "production", "VITE_APP_ENV": "production"}, "regions": ["iad1", "sfo1"], "github": {"enabled": true, "autoAlias": true, "silent": true}, "cleanUrls": true, "trailingSlash": false, "routes": [{"src": "/health", "dest": "/api/health"}, {"src": "/metrics", "dest": "/api/metrics"}]}