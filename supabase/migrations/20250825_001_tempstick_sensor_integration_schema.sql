-- ============================================================================
-- TEMPSTICK SENSOR INTEGRATION SCHEMA
-- ============================================================================
-- This migration creates a comprehensive database schema for TempStick sensor
-- integration in the Seafood Manager application. The schema supports:
-- - Multiple sensors per storage area
-- - Real-time temperature and humidity monitoring
-- - HACCP compliance temperature tracking
-- - Alert escalation and notifications
-- - Historical data retention with performance optimization
-- - Multi-tenant security with RLS policies
-- ============================================================================

-- Step 1: Create storage_areas table for physical location management
-- ============================================================================

CREATE TABLE IF NOT EXISTS storage_areas (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  
  -- Area identification
  name VARCHAR(255) NOT NULL,
  description TEXT,
  area_code VARCHAR(50), -- Internal reference code
  location VARCHAR(255), -- Physical location description
  
  -- Area classification
  area_type VARCHAR(50) NOT NULL CHECK (area_type IN (
    'walk_in_cooler', 'walk_in_freezer', 'reach_in_cooler', 'reach_in_freezer',
    'dry_storage', 'processing_area', 'shipping_dock', 'receiving_area',
    'blast_chiller', 'ice_storage', 'custom'
  )),
  custom_area_type VARCHAR(100), -- For 'custom' area_type
  
  -- HACCP requirements
  haccp_required BOOLEAN DEFAULT false,
  haccp_ccp_number VARCHAR(50), -- Critical Control Point number
  
  -- Temperature requirements
  temp_min_celsius DECIMAL(5,2), -- Minimum safe temperature in Celsius
  temp_max_celsius DECIMAL(5,2), -- Maximum safe temperature in Celsius  
  temp_min_fahrenheit DECIMAL(5,2), -- Minimum safe temperature in Fahrenheit
  temp_max_fahrenheit DECIMAL(5,2), -- Maximum safe temperature in Fahrenheit
  temp_unit VARCHAR(10) DEFAULT 'fahrenheit' CHECK (temp_unit IN ('celsius', 'fahrenheit')),
  
  -- Humidity requirements (optional)
  humidity_min DECIMAL(5,2), -- Minimum humidity percentage
  humidity_max DECIMAL(5,2), -- Maximum humidity percentage
  
  -- Alert configuration
  alert_enabled BOOLEAN DEFAULT true,
  alert_threshold_minutes INTEGER DEFAULT 15, -- Minutes before alerting
  escalation_minutes INTEGER DEFAULT 60, -- Minutes before escalation
  
  -- Compliance settings
  monitoring_frequency_minutes INTEGER DEFAULT 15, -- How often to check readings
  record_retention_days INTEGER DEFAULT 730, -- 2 years default retention
  
  -- Status and metadata
  is_active BOOLEAN DEFAULT true,
  capacity_info JSONB, -- Storage capacity, shelving info, etc.
  compliance_notes TEXT,
  metadata JSONB,
  
  -- Audit timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  
  UNIQUE(user_id, area_code)
);

-- Step 2: Create sensors table for TempStick sensor metadata
-- ============================================================================

CREATE TABLE IF NOT EXISTS sensors (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  storage_area_id UUID REFERENCES storage_areas(id) ON DELETE CASCADE,
  
  -- TempStick API fields
  sensor_id VARCHAR(100) NOT NULL, -- TempStick device ID from API
  device_name VARCHAR(255) NOT NULL,
  device_type VARCHAR(100) DEFAULT 'TempStick',
  firmware_version VARCHAR(50),
  
  -- Sensor configuration
  name VARCHAR(255) NOT NULL, -- User-friendly name
  description TEXT,
  location_description TEXT, -- Specific location within storage area
  
  -- Connection status
  is_online BOOLEAN DEFAULT true,
  last_seen_at TIMESTAMPTZ,
  connection_status VARCHAR(50) DEFAULT 'online' CHECK (connection_status IN (
    'online', 'offline', 'maintenance', 'error'
  )),
  
  -- Battery and hardware status
  battery_level INTEGER, -- 0-100 percentage
  battery_voltage DECIMAL(4,2),
  signal_strength INTEGER, -- WiFi signal strength
  
  -- Calibration settings
  temp_offset_celsius DECIMAL(4,2) DEFAULT 0.0,
  temp_offset_fahrenheit DECIMAL(4,2) DEFAULT 0.0,
  humidity_offset DECIMAL(4,2) DEFAULT 0.0,
  last_calibrated_at TIMESTAMPTZ,
  calibrated_by UUID REFERENCES auth.users(id),
  
  -- Alert thresholds (override storage area defaults if set)
  custom_temp_min_celsius DECIMAL(5,2),
  custom_temp_max_celsius DECIMAL(5,2),
  custom_temp_min_fahrenheit DECIMAL(5,2),
  custom_temp_max_fahrenheit DECIMAL(5,2),
  custom_humidity_min DECIMAL(5,2),
  custom_humidity_max DECIMAL(5,2),
  
  -- Data collection settings
  reading_interval_minutes INTEGER DEFAULT 5,
  data_sync_enabled BOOLEAN DEFAULT true,
  
  -- Status and metadata
  is_active BOOLEAN DEFAULT true,
  installation_date TIMESTAMPTZ,
  last_maintenance_date TIMESTAMPTZ,
  next_maintenance_due TIMESTAMPTZ,
  maintenance_notes TEXT,
  metadata JSONB,
  
  -- Audit timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  
  UNIQUE(user_id, sensor_id) -- Prevent duplicate sensor_ids per user
);

-- Step 3: Create temperature_readings table for time-series data
-- ============================================================================

CREATE TABLE IF NOT EXISTS temperature_readings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  sensor_id UUID REFERENCES sensors(id) ON DELETE CASCADE NOT NULL,
  storage_area_id UUID REFERENCES storage_areas(id) ON DELETE CASCADE,
  
  -- Reading data
  recorded_at TIMESTAMPTZ NOT NULL,
  temp_celsius DECIMAL(6,2) NOT NULL,
  temp_fahrenheit DECIMAL(6,2) NOT NULL,
  humidity DECIMAL(5,2), -- Percentage
  
  -- Data quality indicators
  reading_quality VARCHAR(20) DEFAULT 'good' CHECK (reading_quality IN (
    'good', 'acceptable', 'poor', 'error'
  )),
  signal_strength INTEGER,
  battery_level INTEGER,
  
  -- HACCP compliance flags
  within_safe_range BOOLEAN NOT NULL DEFAULT true,
  temp_violation BOOLEAN DEFAULT false,
  humidity_violation BOOLEAN DEFAULT false,
  
  -- TempStick API metadata
  api_reading_id VARCHAR(100), -- TempStick's internal reading ID
  sync_status VARCHAR(20) DEFAULT 'synced' CHECK (sync_status IN (
    'pending', 'synced', 'error', 'manual'
  )),
  
  -- Data source tracking
  data_source VARCHAR(50) DEFAULT 'tempstick_api' CHECK (data_source IN (
    'tempstick_api', 'manual_entry', 'calibration', 'estimated'
  )),
  
  -- Metadata
  metadata JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Step 4: Create temperature_alerts table for violations and notifications
-- ============================================================================

CREATE TABLE IF NOT EXISTS temperature_alerts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  sensor_id UUID REFERENCES sensors(id) ON DELETE CASCADE NOT NULL,
  storage_area_id UUID REFERENCES storage_areas(id) ON DELETE CASCADE,
  reading_id UUID REFERENCES temperature_readings(id) ON DELETE SET NULL,
  
  -- Alert classification
  alert_type VARCHAR(50) NOT NULL CHECK (alert_type IN (
    'temp_high', 'temp_low', 'humidity_high', 'humidity_low',
    'sensor_offline', 'battery_low', 'calibration_due', 'maintenance_due',
    'data_gap', 'system_error'
  )),
  
  -- Alert severity and status
  severity VARCHAR(20) NOT NULL CHECK (severity IN (
    'info', 'warning', 'critical', 'emergency'
  )),
  alert_status VARCHAR(20) DEFAULT 'active' CHECK (alert_status IN (
    'active', 'acknowledged', 'investigating', 'resolved', 'dismissed'
  )),
  
  -- Alert details
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  threshold_value DECIMAL(8,2), -- The threshold that was breached
  actual_value DECIMAL(8,2), -- The actual reading that triggered alert
  deviation DECIMAL(8,2), -- How far outside safe range
  
  -- Timing information
  first_detected_at TIMESTAMPTZ NOT NULL,
  last_detected_at TIMESTAMPTZ,
  duration_minutes INTEGER, -- How long the condition has persisted
  
  -- HACCP compliance impact
  haccp_violation BOOLEAN DEFAULT false,
  regulatory_notification_required BOOLEAN DEFAULT false,
  product_safety_risk VARCHAR(20) CHECK (product_safety_risk IN (
    'none', 'low', 'medium', 'high', 'critical'
  )),
  
  -- Response tracking
  acknowledged_at TIMESTAMPTZ,
  acknowledged_by UUID REFERENCES auth.users(id),
  resolved_at TIMESTAMPTZ,
  resolved_by UUID REFERENCES auth.users(id),
  resolution_notes TEXT,
  corrective_actions_taken TEXT,
  
  -- Escalation tracking
  escalated BOOLEAN DEFAULT false,
  escalated_at TIMESTAMPTZ,
  escalated_to VARCHAR(255), -- Email or user ID
  escalation_level INTEGER DEFAULT 1,
  
  -- Notification tracking
  email_sent BOOLEAN DEFAULT false,
  email_sent_at TIMESTAMPTZ,
  sms_sent BOOLEAN DEFAULT false,
  sms_sent_at TIMESTAMPTZ,
  notification_attempts INTEGER DEFAULT 0,
  
  -- Auto-resolution settings
  auto_resolve_enabled BOOLEAN DEFAULT true,
  auto_resolve_after_minutes INTEGER DEFAULT 60,
  
  -- Affected inventory tracking
  affected_products JSONB, -- Array of product IDs that may be affected
  inventory_impact_assessed BOOLEAN DEFAULT false,
  
  -- Metadata and audit
  metadata JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Step 5: Create performance indexes for optimal query performance
-- ============================================================================

-- Storage areas indexes
CREATE INDEX IF NOT EXISTS idx_storage_areas_user_id ON storage_areas(user_id);
CREATE INDEX IF NOT EXISTS idx_storage_areas_area_type ON storage_areas(area_type);
CREATE INDEX IF NOT EXISTS idx_storage_areas_haccp_required ON storage_areas(haccp_required) WHERE haccp_required = true;
CREATE INDEX IF NOT EXISTS idx_storage_areas_active ON storage_areas(is_active) WHERE is_active = true;

-- Sensors indexes
CREATE INDEX IF NOT EXISTS idx_sensors_user_id ON sensors(user_id);
CREATE INDEX IF NOT EXISTS idx_sensors_storage_area_id ON sensors(storage_area_id);
CREATE INDEX IF NOT EXISTS idx_sensors_sensor_id ON sensors(sensor_id);
CREATE INDEX IF NOT EXISTS idx_sensors_online_status ON sensors(is_online, connection_status);
CREATE INDEX IF NOT EXISTS idx_sensors_active ON sensors(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_sensors_last_seen ON sensors(last_seen_at DESC);

-- Temperature readings indexes (optimized for time-series queries)
CREATE INDEX IF NOT EXISTS idx_temperature_readings_user_id ON temperature_readings(user_id);
CREATE INDEX IF NOT EXISTS idx_temperature_readings_sensor_id_time ON temperature_readings(sensor_id, recorded_at DESC);
CREATE INDEX IF NOT EXISTS idx_temperature_readings_storage_area_time ON temperature_readings(storage_area_id, recorded_at DESC);
CREATE INDEX IF NOT EXISTS idx_temperature_readings_recorded_at ON temperature_readings(recorded_at DESC);
CREATE INDEX IF NOT EXISTS idx_temperature_readings_violations ON temperature_readings(temp_violation, humidity_violation) WHERE temp_violation = true OR humidity_violation = true;
CREATE INDEX IF NOT EXISTS idx_temperature_readings_quality ON temperature_readings(reading_quality);
CREATE INDEX IF NOT EXISTS idx_temperature_readings_sync_status ON temperature_readings(sync_status) WHERE sync_status != 'synced';

-- Temperature alerts indexes
CREATE INDEX IF NOT EXISTS idx_temperature_alerts_user_id ON temperature_alerts(user_id);
CREATE INDEX IF NOT EXISTS idx_temperature_alerts_sensor_id ON temperature_alerts(sensor_id);
CREATE INDEX IF NOT EXISTS idx_temperature_alerts_storage_area_id ON temperature_alerts(storage_area_id);
CREATE INDEX IF NOT EXISTS idx_temperature_alerts_status ON temperature_alerts(alert_status) WHERE alert_status = 'active';
CREATE INDEX IF NOT EXISTS idx_temperature_alerts_severity ON temperature_alerts(severity);
CREATE INDEX IF NOT EXISTS idx_temperature_alerts_type ON temperature_alerts(alert_type);
CREATE INDEX IF NOT EXISTS idx_temperature_alerts_created_at ON temperature_alerts(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_temperature_alerts_haccp_violation ON temperature_alerts(haccp_violation) WHERE haccp_violation = true;
CREATE INDEX IF NOT EXISTS idx_temperature_alerts_escalated ON temperature_alerts(escalated) WHERE escalated = true;

-- Composite indexes for common dashboard queries
CREATE INDEX IF NOT EXISTS idx_readings_sensor_recent ON temperature_readings(sensor_id, recorded_at DESC) WHERE recorded_at > (NOW() - INTERVAL '24 hours');
CREATE INDEX IF NOT EXISTS idx_alerts_active_by_severity ON temperature_alerts(user_id, alert_status, severity, created_at DESC) WHERE alert_status = 'active';

-- Step 6: Enable Row Level Security (RLS) for multi-tenant data isolation
-- ============================================================================

ALTER TABLE storage_areas ENABLE ROW LEVEL SECURITY;
ALTER TABLE sensors ENABLE ROW LEVEL SECURITY;
ALTER TABLE temperature_readings ENABLE ROW LEVEL SECURITY;
ALTER TABLE temperature_alerts ENABLE ROW LEVEL SECURITY;

-- Step 7: Create comprehensive RLS policies for user data isolation
-- ============================================================================

-- STORAGE_AREAS TABLE POLICIES
CREATE POLICY "Users can view their own storage areas" 
  ON storage_areas FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own storage areas" 
  ON storage_areas FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own storage areas" 
  ON storage_areas FOR UPDATE 
  USING (auth.uid() = user_id) 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own storage areas" 
  ON storage_areas FOR DELETE 
  USING (auth.uid() = user_id);

-- SENSORS TABLE POLICIES
CREATE POLICY "Users can view their own sensors" 
  ON sensors FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own sensors" 
  ON sensors FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own sensors" 
  ON sensors FOR UPDATE 
  USING (auth.uid() = user_id) 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own sensors" 
  ON sensors FOR DELETE 
  USING (auth.uid() = user_id);

-- TEMPERATURE_READINGS TABLE POLICIES
CREATE POLICY "Users can view their own temperature readings" 
  ON temperature_readings FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own temperature readings" 
  ON temperature_readings FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own temperature readings" 
  ON temperature_readings FOR UPDATE 
  USING (auth.uid() = user_id) 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own temperature readings" 
  ON temperature_readings FOR DELETE 
  USING (auth.uid() = user_id);

-- TEMPERATURE_ALERTS TABLE POLICIES
CREATE POLICY "Users can view their own temperature alerts" 
  ON temperature_alerts FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own temperature alerts" 
  ON temperature_alerts FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own temperature alerts" 
  ON temperature_alerts FOR UPDATE 
  USING (auth.uid() = user_id) 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own temperature alerts" 
  ON temperature_alerts FOR DELETE 
  USING (auth.uid() = user_id);

-- Step 8: Create database functions for common operations
-- ============================================================================

-- Function to automatically calculate temperature violations
CREATE OR REPLACE FUNCTION check_temperature_violation()
RETURNS TRIGGER AS $$
BEGIN
  -- Get the temperature thresholds for this sensor/storage area
  WITH thresholds AS (
    SELECT 
      COALESCE(s.custom_temp_min_celsius, sa.temp_min_celsius) as min_celsius,
      COALESCE(s.custom_temp_max_celsius, sa.temp_max_celsius) as max_celsius,
      COALESCE(s.custom_temp_min_fahrenheit, sa.temp_min_fahrenheit) as min_fahrenheit,
      COALESCE(s.custom_temp_max_fahrenheit, sa.temp_max_fahrenheit) as max_fahrenheit,
      COALESCE(s.custom_humidity_min, sa.humidity_min) as min_humidity,
      COALESCE(s.custom_humidity_max, sa.humidity_max) as max_humidity,
      sa.temp_unit
    FROM sensors s
    JOIN storage_areas sa ON sa.id = s.storage_area_id
    WHERE s.id = NEW.sensor_id
  )
  SELECT 
    -- Check temperature violations based on storage area's preferred unit
    CASE 
      WHEN thresholds.temp_unit = 'celsius' THEN
        NEW.temp_celsius < thresholds.min_celsius OR NEW.temp_celsius > thresholds.max_celsius
      ELSE
        NEW.temp_fahrenheit < thresholds.min_fahrenheit OR NEW.temp_fahrenheit > thresholds.max_fahrenheit
    END,
    -- Check humidity violations
    CASE 
      WHEN NEW.humidity IS NOT NULL AND thresholds.min_humidity IS NOT NULL AND thresholds.max_humidity IS NOT NULL THEN
        NEW.humidity < thresholds.min_humidity OR NEW.humidity > thresholds.max_humidity
      ELSE false
    END,
    -- Overall safe range check
    NOT (
      CASE 
        WHEN thresholds.temp_unit = 'celsius' THEN
          NEW.temp_celsius >= thresholds.min_celsius AND NEW.temp_celsius <= thresholds.max_celsius
        ELSE
          NEW.temp_fahrenheit >= thresholds.min_fahrenheit AND NEW.temp_fahrenheit <= thresholds.max_fahrenheit
      END AND
      CASE 
        WHEN NEW.humidity IS NOT NULL AND thresholds.min_humidity IS NOT NULL AND thresholds.max_humidity IS NOT NULL THEN
          NEW.humidity >= thresholds.min_humidity AND NEW.humidity <= thresholds.max_humidity
        ELSE true
      END
    )
  INTO NEW.temp_violation, NEW.humidity_violation, NEW.within_safe_range
  FROM thresholds;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply the trigger to automatically check violations
DROP TRIGGER IF EXISTS trigger_check_temperature_violation ON temperature_readings;
CREATE TRIGGER trigger_check_temperature_violation
  BEFORE INSERT OR UPDATE ON temperature_readings
  FOR EACH ROW EXECUTE FUNCTION check_temperature_violation();

-- Function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply update timestamp triggers
DROP TRIGGER IF EXISTS trigger_storage_areas_updated_at ON storage_areas;
CREATE TRIGGER trigger_storage_areas_updated_at
  BEFORE UPDATE ON storage_areas
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_sensors_updated_at ON sensors;
CREATE TRIGGER trigger_sensors_updated_at
  BEFORE UPDATE ON sensors
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_temperature_alerts_updated_at ON temperature_alerts;
CREATE TRIGGER trigger_temperature_alerts_updated_at
  BEFORE UPDATE ON temperature_alerts
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Step 9: Create views for common dashboard queries
-- ============================================================================

-- View for current sensor status dashboard
CREATE OR REPLACE VIEW sensor_status_dashboard AS
SELECT 
  s.id,
  s.user_id,
  s.sensor_id,
  s.name,
  s.device_name,
  s.connection_status,
  s.is_online,
  s.battery_level,
  s.last_seen_at,
  
  -- Storage area info
  sa.name as storage_area_name,
  sa.area_type,
  sa.haccp_required,
  
  -- Latest reading
  lr.recorded_at as last_reading_at,
  lr.temp_celsius,
  lr.temp_fahrenheit,
  lr.humidity,
  lr.within_safe_range,
  lr.temp_violation,
  lr.humidity_violation,
  
  -- Active alerts count
  COALESCE(alert_counts.active_count, 0) as active_alerts_count,
  COALESCE(alert_counts.critical_count, 0) as critical_alerts_count
  
FROM sensors s
LEFT JOIN storage_areas sa ON sa.id = s.storage_area_id
LEFT JOIN LATERAL (
  SELECT *
  FROM temperature_readings tr
  WHERE tr.sensor_id = s.id
  ORDER BY tr.recorded_at DESC
  LIMIT 1
) lr ON true
LEFT JOIN (
  SELECT 
    sensor_id,
    COUNT(*) as active_count,
    COUNT(*) FILTER (WHERE severity IN ('critical', 'emergency')) as critical_count
  FROM temperature_alerts
  WHERE alert_status = 'active'
  GROUP BY sensor_id
) alert_counts ON alert_counts.sensor_id = s.id
WHERE s.is_active = true;

-- View for HACCP compliance dashboard
CREATE OR REPLACE VIEW haccp_compliance_dashboard AS
SELECT 
  sa.id as storage_area_id,
  sa.user_id,
  sa.name as storage_area_name,
  sa.area_type,
  sa.haccp_ccp_number,
  sa.temp_min_celsius,
  sa.temp_max_celsius,
  sa.temp_min_fahrenheit,
  sa.temp_max_fahrenheit,
  
  -- Sensor count
  sensor_counts.total_sensors,
  sensor_counts.online_sensors,
  sensor_counts.offline_sensors,
  
  -- Violation summary (last 24 hours)
  violation_summary.total_readings,
  violation_summary.violation_readings,
  violation_summary.violation_percentage,
  violation_summary.last_violation_at,
  
  -- Current compliance status
  CASE 
    WHEN sensor_counts.offline_sensors > 0 THEN 'sensors_offline'
    WHEN violation_summary.violation_percentage > 5 THEN 'non_compliant'
    WHEN violation_summary.violation_percentage > 1 THEN 'marginal'
    ELSE 'compliant'
  END as compliance_status

FROM storage_areas sa
LEFT JOIN (
  SELECT 
    storage_area_id,
    COUNT(*) as total_sensors,
    COUNT(*) FILTER (WHERE is_online = true) as online_sensors,
    COUNT(*) FILTER (WHERE is_online = false) as offline_sensors
  FROM sensors
  WHERE is_active = true
  GROUP BY storage_area_id
) sensor_counts ON sensor_counts.storage_area_id = sa.id
LEFT JOIN (
  SELECT 
    storage_area_id,
    COUNT(*) as total_readings,
    COUNT(*) FILTER (WHERE temp_violation = true OR humidity_violation = true) as violation_readings,
    ROUND(
      (COUNT(*) FILTER (WHERE temp_violation = true OR humidity_violation = true) * 100.0 / NULLIF(COUNT(*), 0)), 
      2
    ) as violation_percentage,
    MAX(recorded_at) FILTER (WHERE temp_violation = true OR humidity_violation = true) as last_violation_at
  FROM temperature_readings
  WHERE recorded_at > (NOW() - INTERVAL '24 hours')
  GROUP BY storage_area_id
) violation_summary ON violation_summary.storage_area_id = sa.id
WHERE sa.haccp_required = true AND sa.is_active = true;

-- Step 10: Add sample data and comments
-- ============================================================================

-- Add helpful comments to tables
COMMENT ON TABLE storage_areas IS 'Physical storage locations with temperature monitoring requirements';
COMMENT ON TABLE sensors IS 'TempStick sensors with configuration and status tracking';
COMMENT ON TABLE temperature_readings IS 'Time-series temperature and humidity data from sensors';
COMMENT ON TABLE temperature_alerts IS 'Temperature violations and alert management';

COMMENT ON VIEW sensor_status_dashboard IS 'Real-time dashboard view of all sensor statuses and latest readings';
COMMENT ON VIEW haccp_compliance_dashboard IS 'HACCP compliance summary for areas requiring monitoring';

-- Grant permissions for real-time subscriptions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT ON temperature_readings TO anon, authenticated;
GRANT SELECT ON temperature_alerts TO anon, authenticated;
GRANT SELECT ON sensor_status_dashboard TO anon, authenticated;
GRANT SELECT ON haccp_compliance_dashboard TO anon, authenticated;

-- Migration completed successfully
-- ============================================================================
-- This migration provides a robust foundation for TempStick sensor integration
-- with proper security, performance optimization, and HACCP compliance support.
-- Next steps: Implement TempStick API integration service and dashboard UI.
-- ============================================================================