-- ENHANCED TRACEABILITY SYSTEM - GDST 1.2 COMPLIANCE
-- Implements complete supply chain traceability with GDST Key Data Elements (KDEs)
-- Supports FDA FSMA 204, SIMP, MSC/ASC chain of custody requirements

-- ============================================================================
-- VESSEL & FISHING OPERATIONS (GDST Extensions)
-- ============================================================================

-- Fishing vessels and aquaculture farms
CREATE TABLE IF NOT EXISTS vessels (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  vessel_name text NOT NULL,
  vessel_registration text,
  flag_country text NOT NULL,
  vessel_type text CHECK (vessel_type IN ('fishing_vessel', 'transport_vessel', 'aquaculture_farm')),
  gear_type text, -- trawl, longline, gillnet, cage, pond, etc
  imo_number text, -- International Maritime Organization number
  call_sign text,
  owner_partner_id uuid REFERENCES partners(id) ON DELETE SET NULL,
  operator_partner_id uuid REFERENCES partners(id) ON DELETE SET NULL,
  gross_tonnage numeric,
  length_meters numeric,
  fishing_authorization text[], -- license numbers, permits
  is_active boolean NOT NULL DEFAULT true,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Fishing areas and aquaculture locations
CREATE TABLE IF NOT EXISTS fishing_areas (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  area_code text NOT NULL, -- FAO area code (e.g., 21, 37.1.2)
  area_name text NOT NULL,
  area_type text CHECK (area_type IN ('fao_area', 'eez', 'rfmo_area', 'local_area')),
  coordinates jsonb, -- GeoJSON polygon or point
  management_authority text,
  regulations text[],
  created_at timestamptz DEFAULT now()
);

-- ============================================================================
-- ENHANCED TRACEABILITY EVENTS (GDST KDEs)
-- ============================================================================

-- Extend traceability_events with GDST-specific fields
ALTER TABLE traceability_events 
ADD COLUMN IF NOT EXISTS vessel_id uuid REFERENCES vessels(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS fishing_area_id uuid REFERENCES fishing_areas(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS gear_type text,
ADD COLUMN IF NOT EXISTS catch_method text,
ADD COLUMN IF NOT EXISTS landing_location text,
ADD COLUMN IF NOT EXISTS species_scientific_name text,
ADD COLUMN IF NOT EXISTS species_fao_code text,
ADD COLUMN IF NOT EXISTS production_method text CHECK (production_method IN ('wild_caught', 'farmed', 'enhanced')),
ADD COLUMN IF NOT EXISTS catch_certificate_number text,
ADD COLUMN IF NOT EXISTS health_certificate_number text,
ADD COLUMN IF NOT EXISTS chain_of_custody_certificate text,
ADD COLUMN IF NOT EXISTS gdst_kde_version text DEFAULT '1.2';

-- Certifications and compliance documents
CREATE TABLE IF NOT EXISTS certifications (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  certification_type text NOT NULL CHECK (certification_type IN ('msc', 'asc', 'bap', 'global_gap', 'organic', 'fair_trade', 'dolphin_safe')),
  certificate_number text NOT NULL,
  certification_body text NOT NULL,
  product_id uuid REFERENCES "Products"(id) ON DELETE CASCADE,
  partner_id uuid REFERENCES partners(id) ON DELETE CASCADE,
  vessel_id uuid REFERENCES vessels(id) ON DELETE SET NULL,
  issue_date date NOT NULL,
  expiry_date date NOT NULL,
  scope_description text,
  certificate_url text,
  is_valid boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- DNA/Genetic analysis records
CREATE TABLE IF NOT EXISTS genetic_analysis (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  lot_id uuid NOT NULL REFERENCES lots(id) ON DELETE CASCADE,
  sample_id text NOT NULL,
  analysis_type text NOT NULL CHECK (analysis_type IN ('species_id', 'origin_verification', 'genetic_fingerprint')),
  laboratory text NOT NULL,
  analysis_date date NOT NULL,
  results jsonb NOT NULL,
  species_confirmed text,
  origin_confirmed text,
  confidence_level numeric CHECK (confidence_level >= 0 AND confidence_level <= 100),
  certificate_number text,
  is_verified boolean NOT NULL DEFAULT false,
  verified_by text,
  verified_at timestamptz,
  created_at timestamptz DEFAULT now()
);

-- ============================================================================
-- REGULATORY COMPLIANCE & REPORTING
-- ============================================================================

-- SIMP Import documentation
CREATE TABLE IF NOT EXISTS simp_documentation (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  import_entry_number text NOT NULL,
  lot_id uuid NOT NULL REFERENCES lots(id) ON DELETE CASCADE,
  vessel_id uuid REFERENCES vessels(id) ON DELETE SET NULL,
  harvest_date date NOT NULL,
  harvest_location text NOT NULL,
  species_scientific_name text NOT NULL,
  species_common_name text NOT NULL,
  product_form text NOT NULL,
  production_method text NOT NULL,
  wild_catch_method text,
  aquaculture_method text,
  point_of_first_sale text,
  chain_of_custody_number text,
  importer_name text NOT NULL,
  importer_address text NOT NULL,
  customs_entry_type text,
  customs_district text,
  created_at timestamptz DEFAULT now()
);

-- FDA 24-hour rule compliance tracking
CREATE TABLE IF NOT EXISTS fda_requests (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  request_number text NOT NULL UNIQUE,
  request_type text NOT NULL CHECK (request_type IN ('24_hour_rule', 'inspection', 'recall', 'investigation')),
  requested_at timestamptz NOT NULL DEFAULT now(),
  requested_by text NOT NULL,
  request_details text NOT NULL,
  response_deadline timestamptz NOT NULL,
  status text NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'overdue')),
  response_data jsonb,
  response_files text[],
  responded_at timestamptz,
  responded_by text,
  notes text,
  created_at timestamptz DEFAULT now()
);

-- Link FDA requests to specific lots/products
CREATE TABLE IF NOT EXISTS fda_request_lots (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  fda_request_id uuid NOT NULL REFERENCES fda_requests(id) ON DELETE CASCADE,
  lot_id uuid NOT NULL REFERENCES lots(id) ON DELETE CASCADE,
  requested_data_elements text[],
  provided_data jsonb,
  UNIQUE(fda_request_id, lot_id)
);

-- ============================================================================
-- ALLERGEN MANAGEMENT
-- ============================================================================

-- Product allergen information
CREATE TABLE IF NOT EXISTS product_allergens (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id uuid NOT NULL REFERENCES "Products"(id) ON DELETE CASCADE,
  allergen_type text NOT NULL CHECK (allergen_type IN ('crustaceans', 'mollusks', 'fish', 'eggs', 'milk', 'peanuts', 'tree_nuts', 'soy', 'wheat', 'sesame')),
  allergen_status text NOT NULL CHECK (allergen_status IN ('contains', 'may_contain', 'free_from')),
  declaration_required boolean DEFAULT false,
  source_of_contamination text,
  control_measures text,
  testing_required boolean NOT NULL DEFAULT false,
  created_at timestamptz DEFAULT now(),
  UNIQUE(product_id, allergen_type)
);

-- Allergen testing records
CREATE TABLE IF NOT EXISTS allergen_testing (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  lot_id uuid NOT NULL REFERENCES lots(id) ON DELETE CASCADE,
  allergen_type text NOT NULL,
  test_method text NOT NULL,
  laboratory text NOT NULL,
  test_date date NOT NULL,
  result text NOT NULL CHECK (result IN ('detected', 'not_detected', 'below_threshold')),
  quantitative_result numeric,
  detection_limit numeric,
  certificate_number text,
  compliant boolean NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- ============================================================================
-- SHELF LIFE & QUALITY MANAGEMENT
-- ============================================================================

-- Shelf life studies and parameters
CREATE TABLE IF NOT EXISTS shelf_life_parameters (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id uuid NOT NULL REFERENCES "Products"(id) ON DELETE CASCADE,
  storage_conditions text NOT NULL, -- refrigerated, frozen, ambient
  temperature_range text NOT NULL, -- e.g., "0-4°C", "-18°C or below"
  shelf_life_days integer NOT NULL,
  quality_indicators jsonb, -- {"color": "bright", "texture": "firm", "odor": "fresh"}
  degradation_factors text[],
  packaging_requirements text,
  study_reference text,
  validated_by text,
  validation_date date,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(product_id, storage_conditions)
);

-- Dynamic shelf life calculation for lots
CREATE TABLE IF NOT EXISTS lot_shelf_life (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  lot_id uuid NOT NULL REFERENCES lots(id) ON DELETE CASCADE,
  shelf_life_parameters_id uuid NOT NULL REFERENCES shelf_life_parameters(id) ON DELETE CASCADE,
  production_date date NOT NULL,
  expiry_date date NOT NULL,
  remaining_shelf_life_days integer,
  quality_status text NOT NULL DEFAULT 'fresh' CHECK (quality_status IN ('fresh', 'good', 'acceptable', 'expired', 'condemned')),
  temperature_abuse_incidents integer DEFAULT 0,
  quality_adjustments jsonb, -- adjustments based on temperature excursions
  last_quality_check timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(lot_id, shelf_life_parameters_id)
);

-- ============================================================================
-- RECALL MANAGEMENT
-- ============================================================================

-- Recall events and management
CREATE TABLE IF NOT EXISTS recalls (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  recall_number text NOT NULL UNIQUE,
  recall_type text NOT NULL CHECK (recall_type IN ('voluntary', 'fda_requested', 'fda_mandated')),
  recall_class text NOT NULL CHECK (recall_class IN ('class_i', 'class_ii', 'class_iii')),
  initiated_date date NOT NULL,
  reason_for_recall text NOT NULL,
  health_hazard_evaluation text,
  distribution_pattern text,
  recalled_product_description text NOT NULL,
  quantity_produced numeric,
  quantity_distributed numeric,
  quantity_recovered numeric,
  recovery_percentage numeric DEFAULT 0,
  status text NOT NULL DEFAULT 'ongoing' CHECK (status IN ('ongoing', 'completed', 'terminated')),
  fda_recall_number text,
  public_notification_required boolean NOT NULL DEFAULT true,
  public_notification_date date,
  recall_coordinator text NOT NULL,
  effectiveness_checks_completed boolean NOT NULL DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Link recalls to specific lots
CREATE TABLE IF NOT EXISTS recall_lots (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  recall_id uuid NOT NULL REFERENCES recalls(id) ON DELETE CASCADE,
  lot_id uuid NOT NULL REFERENCES lots(id) ON DELETE CASCADE,
  quantity_recalled numeric NOT NULL,
  customer_notifications_sent integer DEFAULT 0,
  product_recovered boolean NOT NULL DEFAULT false,
  recovery_date date,
  disposition text CHECK (disposition IN ('returned', 'destroyed', 'corrected', 'diverted')),
  created_at timestamptz DEFAULT now(),
  UNIQUE(recall_id, lot_id)
);

-- Customer notification tracking
CREATE TABLE IF NOT EXISTS recall_notifications (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  recall_id uuid NOT NULL REFERENCES recalls(id) ON DELETE CASCADE,
  customer_id uuid NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
  notification_method text NOT NULL CHECK (notification_method IN ('email', 'phone', 'fax', 'mail', 'in_person')),
  notification_date timestamptz NOT NULL DEFAULT now(),
  acknowledged boolean NOT NULL DEFAULT false,
  acknowledged_at timestamptz,
  response_received text,
  follow_up_required boolean NOT NULL DEFAULT false,
  follow_up_completed boolean NOT NULL DEFAULT false,
  created_at timestamptz DEFAULT now()
);

-- ============================================================================
-- GDST DATA EXCHANGE FORMATS
-- ============================================================================

-- GDST-compatible data export view
CREATE OR REPLACE VIEW gdst_traceability_export AS
SELECT 
  e.id as event_id,
  e.event_type,
  e.event_time,
  -- Product Information (GDST KDE)
  p.id as product_id,
  p.name as product_name,
  p.gtin,
  p.scientific_name,
  p.fao_area as product_fao_area,
  e.species_scientific_name,
  e.species_fao_code,
  -- Lot Information
  l.id as lot_id,
  l.tlc as traceability_lot_code,
  l.origin_country,
  l.harvest_or_prod_date,
  l.landing_date,
  l.initial_qty as quantity,
  l.uom as unit_of_measure,
  -- Location and Actor Information
  ap.name as actor_name,
  ap.gln as actor_gln,
  al.name as location_name,
  al.gln as location_gln,
  -- Vessel and Catch Information
  v.vessel_name,
  v.vessel_registration,
  v.flag_country,
  e.gear_type,
  e.catch_method,
  fa.area_code as fao_area,
  fa.area_name as fishing_area_name,
  -- Production Method
  e.production_method,
  -- Transport Information
  tp.name as transporter_name,
  e.reference_doc,
  -- Certifications
  COALESCE(
    json_agg(
      json_build_object(
        'type', c.certification_type,
        'number', c.certificate_number,
        'body', c.certification_body,
        'valid_until', c.expiry_date
      )
    ) FILTER (WHERE c.id IS NOT NULL),
    '[]'::json
  ) as certifications,
  -- Temperature Data
  e.temperature_data,
  -- Event Lot Details
  el.role as lot_role,
  el.qty as event_quantity,
  el.uom as event_unit,
  -- GDST Metadata
  e.gdst_kde_version,
  e.created_at as record_created_at
FROM traceability_events e
JOIN event_lots el ON el.event_id = e.id
JOIN lots l ON l.id = el.lot_id
JOIN "Products" p ON p.id = l.product_id
LEFT JOIN partners ap ON ap.id = e.actor_partner_id
LEFT JOIN locations al ON al.id = e.actor_location_id
LEFT JOIN partners tp ON tp.id = e.transporter_partner_id
LEFT JOIN vessels v ON v.id = e.vessel_id
LEFT JOIN fishing_areas fa ON fa.id = e.fishing_area_id
LEFT JOIN certifications c ON c.product_id = p.id AND c.is_valid = true
GROUP BY
  e.id, e.event_type, e.event_time, p.id, p.name, p.gtin, p.scientific_name,
  p.fao_area, e.species_scientific_name, e.species_fao_code,
  l.id, l.tlc, l.origin_country, l.harvest_or_prod_date, l.landing_date,
  l.initial_qty, l.uom, ap.name, ap.gln, al.name, al.gln,
  v.vessel_name, v.vessel_registration, v.flag_country, e.gear_type, 
  e.catch_method, fa.area_code, fa.area_name, e.production_method,
  tp.name, e.reference_doc, e.temperature_data, el.role, el.qty, el.uom,
  e.gdst_kde_version, e.created_at;

-- ============================================================================
-- PERFORMANCE INDEXES
-- ============================================================================

CREATE INDEX IF NOT EXISTS idx_vessels_flag_country ON vessels(flag_country);
CREATE INDEX IF NOT EXISTS idx_vessels_gear_type ON vessels(gear_type);
CREATE INDEX IF NOT EXISTS idx_certifications_expiry ON certifications(expiry_date, is_valid);
CREATE INDEX IF NOT EXISTS idx_genetic_analysis_lot ON genetic_analysis(lot_id, analysis_type);
CREATE INDEX IF NOT EXISTS idx_simp_documentation_lot ON simp_documentation(lot_id);
CREATE INDEX IF NOT EXISTS idx_fda_requests_deadline ON fda_requests(response_deadline, status);
CREATE INDEX IF NOT EXISTS idx_product_allergens_declaration ON product_allergens(declaration_required, allergen_type);
CREATE INDEX IF NOT EXISTS idx_shelf_life_parameters_product ON shelf_life_parameters(product_id, storage_conditions);
CREATE INDEX IF NOT EXISTS idx_lot_shelf_life_remaining ON lot_shelf_life(remaining_shelf_life_days, quality_status);
CREATE INDEX IF NOT EXISTS idx_recalls_status ON recalls(status, recall_class);
CREATE INDEX IF NOT EXISTS idx_recall_lots_recovery ON recall_lots(product_recovered, recovery_date);

-- ============================================================================
-- AUTOMATED COMPLIANCE FUNCTIONS
-- ============================================================================

-- Function to validate GDST KDE completeness
CREATE OR REPLACE FUNCTION validate_gdst_kdes(event_id uuid)
RETURNS jsonb AS $$
DECLARE
  event_rec record;
  missing_kdes text[] := '{}';
  completeness_score numeric;
BEGIN
  SELECT * INTO event_rec FROM traceability_events WHERE id = event_id;
  
  IF NOT FOUND THEN
    RETURN jsonb_build_object('error', 'Event not found');
  END IF;
  
  -- Check required KDEs based on event type
  CASE event_rec.event_type
    WHEN 'harvest' THEN
      IF event_rec.vessel_id IS NULL THEN
        missing_kdes := array_append(missing_kdes, 'vessel_information');
      END IF;
      IF event_rec.fishing_area_id IS NULL THEN
        missing_kdes := array_append(missing_kdes, 'fishing_area');
      END IF;
      IF event_rec.gear_type IS NULL THEN
        missing_kdes := array_append(missing_kdes, 'gear_type');
      END IF;
    WHEN 'receiving' THEN
      IF event_rec.actor_partner_id IS NULL THEN
        missing_kdes := array_append(missing_kdes, 'receiver_information');
      END IF;
      IF event_rec.reference_doc IS NULL THEN
        missing_kdes := array_append(missing_kdes, 'reference_document');
      END IF;
    WHEN 'shipping' THEN
      IF event_rec.actor_partner_id IS NULL THEN
        missing_kdes := array_append(missing_kdes, 'shipper_information');
      END IF;
      IF event_rec.transporter_partner_id IS NULL THEN
        missing_kdes := array_append(missing_kdes, 'transporter_information');
      END IF;
  END CASE;
  
  -- Calculate completeness score
  completeness_score := CASE 
    WHEN array_length(missing_kdes, 1) IS NULL THEN 100
    ELSE GREATEST(0, 100 - (array_length(missing_kdes, 1) * 20))
  END;
  
  RETURN jsonb_build_object(
    'event_id', event_id,
    'event_type', event_rec.event_type,
    'missing_kdes', missing_kdes,
    'completeness_score', completeness_score,
    'gdst_compliant', completeness_score >= 80
  );
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- UPDATED AT TRIGGERS
-- ============================================================================

DROP TRIGGER IF EXISTS update_vessels_updated_at ON vessels;
CREATE TRIGGER update_vessels_updated_at
  BEFORE UPDATE ON vessels
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_certifications_updated_at ON certifications;
CREATE TRIGGER update_certifications_updated_at
  BEFORE UPDATE ON certifications
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_shelf_life_parameters_updated_at ON shelf_life_parameters;
CREATE TRIGGER update_shelf_life_parameters_updated_at
  BEFORE UPDATE ON shelf_life_parameters
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_lot_shelf_life_updated_at ON lot_shelf_life;
CREATE TRIGGER update_lot_shelf_life_updated_at
  BEFORE UPDATE ON lot_shelf_life
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_recalls_updated_at ON recalls;
CREATE TRIGGER update_recalls_updated_at
  BEFORE UPDATE ON recalls
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE vessels IS 'GDST: Fishing vessels and aquaculture farms with gear and ownership details';
COMMENT ON TABLE fishing_areas IS 'GDST: FAO fishing areas and management zones';
COMMENT ON TABLE certifications IS 'GDST: Third-party certifications (MSC, ASC, etc.) for products and facilities';
COMMENT ON TABLE genetic_analysis IS 'DNA/genetic verification for species identification and origin verification';
COMMENT ON TABLE simp_documentation IS 'SIMP (Seafood Import Monitoring Program) compliance documentation';
COMMENT ON TABLE fda_requests IS 'FDA 24-hour rule and other regulatory data requests';
COMMENT ON TABLE product_allergens IS 'Allergen management and declaration requirements';
COMMENT ON TABLE shelf_life_parameters IS 'Product shelf life studies and quality parameters';
COMMENT ON TABLE recalls IS 'Recall management and tracking system';
COMMENT ON VIEW gdst_traceability_export IS 'GDST 1.2 compliant data export format for supply chain partners';