-- ============================================================================
-- TEMPSTICK SENSOR INTEGRATION - SAMPLE DATA
-- ============================================================================
-- This migration adds sample data for testing TempStick sensor integration.
-- It creates realistic storage areas, sensors, and sample temperature data
-- for development and testing purposes.
-- ============================================================================

-- Step 1: Insert sample storage areas
-- ============================================================================

-- Insert sample storage areas (using placeholder user_id - should be updated in production)
INSERT INTO storage_areas (
  user_id, name, description, area_code, location, area_type, 
  haccp_required, haccp_ccp_number, 
  temp_min_celsius, temp_max_celsius, temp_min_fahrenheit, temp_max_fahrenheit, 
  temp_unit, humidity_min, humidity_max,
  alert_enabled, alert_threshold_minutes, escalation_minutes,
  monitoring_frequency_minutes, record_retention_days, is_active,
  created_by
) VALUES 
-- Walk-in Cooler
(
  '00000000-0000-0000-0000-000000000000'::uuid,
  'Walk-in Cooler #1',
  'Primary walk-in cooler for fresh seafood storage',
  'WIC-001',
  'Processing Floor, North Wall',
  'walk_in_cooler',
  true,
  'CCP-1',
  -1.0, 4.0, 30.0, 39.0, -- 30-39°F / -1-4°C
  'fahrenheit',
  80.0, 95.0, -- 80-95% humidity
  true, 15, 60, -- Alert after 15 min, escalate after 60 min
  5, 730, true, -- Check every 5 min, retain 2 years
  '00000000-0000-0000-0000-000000000000'::uuid
),
-- Walk-in Freezer
(
  '00000000-0000-0000-0000-000000000000'::uuid,
  'Walk-in Freezer #1',
  'Primary walk-in freezer for frozen seafood storage',
  'WIF-001',
  'Processing Floor, South Wall',
  'walk_in_freezer',
  true,
  'CCP-2',
  -23.0, -18.0, -10.0, 0.0, -- -10-0°F / -23--18°C
  'fahrenheit',
  null, null, -- No humidity requirements for freezer
  true, 10, 30, -- More aggressive alerting for freezer
  5, 730, true,
  '00000000-0000-0000-0000-000000000000'::uuid
),
-- Reach-in Cooler
(
  '00000000-0000-0000-0000-000000000000'::uuid,
  'Reach-in Display Cooler',
  'Customer-facing display cooler for retail sales',
  'RIC-001',
  'Retail Area, Front Counter',
  'reach_in_cooler',
  true,
  'CCP-3',
  -1.0, 4.0, 30.0, 39.0,
  'fahrenheit',
  85.0, 95.0,
  true, 10, 45,
  10, 365, true, -- Check more frequently, shorter retention
  '00000000-0000-0000-0000-000000000000'::uuid
),
-- Dry Storage
(
  '00000000-0000-0000-0000-000000000000'::uuid,
  'Dry Storage Room',
  'Ambient storage for dry goods and packaging',
  'DRY-001',
  'Storage Building, Room 101',
  'dry_storage',
  false, -- HACCP not required for dry storage
  null,
  15.0, 25.0, 60.0, 77.0, -- 60-77°F / 15-25°C
  'fahrenheit',
  40.0, 70.0, -- Lower humidity for dry goods
  true, 30, 120, -- Less aggressive alerting
  15, 365, true,
  '00000000-0000-0000-0000-000000000000'::uuid
),
-- Processing Area
(
  '00000000-0000-0000-0000-000000000000'::uuid,
  'Fish Processing Station',
  'Temperature-controlled processing area',
  'PROC-001',
  'Processing Floor, Center',
  'processing_area',
  true,
  'CCP-4',
  10.0, 15.0, 50.0, 59.0, -- 50-59°F / 10-15°C
  'fahrenheit',
  75.0, 85.0,
  true, 20, 60,
  10, 730, true,
  '00000000-0000-0000-0000-000000000000'::uuid
);

-- Step 2: Insert sample TempStick sensors
-- ============================================================================

-- Insert sample sensors linked to storage areas
INSERT INTO sensors (
  user_id, storage_area_id, sensor_id, device_name, device_type, 
  name, description, location_description,
  is_online, last_seen_at, connection_status,
  battery_level, battery_voltage, signal_strength,
  reading_interval_minutes, data_sync_enabled, is_active,
  installation_date, created_by
) VALUES
-- Sensor for Walk-in Cooler
(
  '00000000-0000-0000-0000-000000000000'::uuid,
  (SELECT id FROM storage_areas WHERE area_code = 'WIC-001' LIMIT 1),
  'TS-WIC-001-A',
  'TempStick Pro #4521',
  'TempStick',
  'Cooler Center Sensor',
  'Primary temperature sensor for walk-in cooler center',
  'Mounted on center shelf, 4 feet from floor',
  true,
  NOW() - INTERVAL '2 minutes',
  'online',
  87, 3.2, -45, -- Good battery, strong signal
  5, true, true,
  NOW() - INTERVAL '30 days',
  '00000000-0000-0000-0000-000000000000'::uuid
),
-- Sensor for Walk-in Freezer  
(
  '00000000-0000-0000-0000-000000000000'::uuid,
  (SELECT id FROM storage_areas WHERE area_code = 'WIF-001' LIMIT 1),
  'TS-WIF-001-A',
  'TempStick Pro #4522', 
  'TempStick',
  'Freezer Main Sensor',
  'Primary temperature sensor for walk-in freezer',
  'Mounted on back wall, center height',
  true,
  NOW() - INTERVAL '1 minute',
  'online', 
  92, 3.3, -42,
  5, true, true,
  NOW() - INTERVAL '30 days',
  '00000000-0000-0000-0000-000000000000'::uuid
),
-- Sensor for Reach-in Cooler
(
  '00000000-0000-0000-0000-000000000000'::uuid,
  (SELECT id FROM storage_areas WHERE area_code = 'RIC-001' LIMIT 1),
  'TS-RIC-001-A',
  'TempStick Basic #3401',
  'TempStick',
  'Display Cooler Sensor',
  'Temperature monitoring for customer display cooler',
  'Inside display case, protected from opening',
  true,
  NOW() - INTERVAL '3 minutes', 
  'online',
  74, 3.1, -48,
  10, true, true,
  NOW() - INTERVAL '15 days',
  '00000000-0000-0000-0000-000000000000'::uuid
),
-- Sensor for Dry Storage (lower battery example)
(
  '00000000-0000-0000-0000-000000000000'::uuid,
  (SELECT id FROM storage_areas WHERE area_code = 'DRY-001' LIMIT 1), 
  'TS-DRY-001-A',
  'TempStick Basic #3402',
  'TempStick',
  'Dry Storage Monitor',
  'Ambient monitoring for dry storage room',
  'Mounted on east wall, away from doors',
  true,
  NOW() - INTERVAL '10 minutes',
  'online',
  23, 2.7, -52, -- Low battery example
  15, true, true,
  NOW() - INTERVAL '45 days',
  '00000000-0000-0000-0000-000000000000'::uuid
),
-- Offline sensor example
(
  '00000000-0000-0000-0000-000000000000'::uuid,
  (SELECT id FROM storage_areas WHERE area_code = 'PROC-001' LIMIT 1),
  'TS-PROC-001-A', 
  'TempStick Pro #4523',
  'TempStick',
  'Processing Area Sensor',
  'Temperature monitoring for processing station',
  'Mounted above processing table',
  false, -- Offline sensor
  NOW() - INTERVAL '2 hours', -- Last seen 2 hours ago
  'offline',
  0, 0.0, 0, -- Dead battery
  10, true, true,
  NOW() - INTERVAL '60 days',
  '00000000-0000-0000-0000-000000000000'::uuid
);

-- Step 3: Insert sample temperature readings
-- ============================================================================

-- Function to generate realistic temperature readings
CREATE OR REPLACE FUNCTION generate_sample_temp_readings()
RETURNS void AS $$
DECLARE
  sensor_record RECORD;
  storage_record RECORD;
  base_time TIMESTAMP;
  reading_time TIMESTAMP;
  temp_f DECIMAL;
  temp_c DECIMAL;
  humidity_val DECIMAL;
  i INTEGER;
BEGIN
  -- Generate readings for the last 24 hours
  FOR sensor_record IN 
    SELECT s.id as sensor_id, s.user_id, s.storage_area_id, s.is_online, s.reading_interval_minutes
    FROM sensors s 
    WHERE s.is_active = true
  LOOP
    -- Get storage area temperature ranges
    SELECT temp_min_fahrenheit, temp_max_fahrenheit, humidity_min, humidity_max
    INTO storage_record
    FROM storage_areas 
    WHERE id = sensor_record.storage_area_id;
    
    base_time := NOW() - INTERVAL '24 hours';
    
    -- Generate readings every interval for online sensors
    IF sensor_record.is_online THEN
      FOR i IN 0..(24 * 60 / sensor_record.reading_interval_minutes) LOOP
        reading_time := base_time + (i * (sensor_record.reading_interval_minutes || ' minutes')::INTERVAL);
        
        -- Skip if reading time is in the future
        EXIT WHEN reading_time > NOW();
        
        -- Generate realistic temperature (mostly within range with occasional spikes)
        IF random() < 0.95 THEN -- 95% of readings within safe range
          temp_f := storage_record.temp_min_fahrenheit + 
                    (storage_record.temp_max_fahrenheit - storage_record.temp_min_fahrenheit) * random() +
                    (random() - 0.5) * 2; -- Small variation
        ELSE -- 5% outside range (violations)
          IF random() < 0.5 THEN
            temp_f := storage_record.temp_min_fahrenheit - (random() * 5 + 1); -- Below range
          ELSE  
            temp_f := storage_record.temp_max_fahrenheit + (random() * 5 + 1); -- Above range
          END IF;
        END IF;
        
        temp_c := (temp_f - 32) * 5 / 9;
        
        -- Generate humidity if applicable
        IF storage_record.humidity_min IS NOT NULL THEN
          humidity_val := storage_record.humidity_min + 
                         (storage_record.humidity_max - storage_record.humidity_min) * random();
        ELSE
          humidity_val := NULL;
        END IF;
        
        -- Insert reading
        INSERT INTO temperature_readings (
          user_id, sensor_id, storage_area_id, recorded_at,
          temp_celsius, temp_fahrenheit, humidity,
          reading_quality, signal_strength, battery_level,
          data_source, sync_status, api_reading_id
        ) VALUES (
          sensor_record.user_id, 
          sensor_record.sensor_id,
          sensor_record.storage_area_id,
          reading_time,
          ROUND(temp_c, 2),
          ROUND(temp_f, 2), 
          CASE WHEN humidity_val IS NULL THEN NULL ELSE ROUND(humidity_val, 1) END,
          'good',
          -45 + (random() * 10)::INTEGER, -- Signal strength variation
          70 + (random() * 25)::INTEGER,   -- Battery level variation
          'tempstick_api',
          'synced',
          'api_' || generate_random_uuid()::text
        );
      END LOOP;
    END IF;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Generate sample readings
SELECT generate_sample_temp_readings();

-- Drop the temporary function
DROP FUNCTION generate_sample_temp_readings();

-- Step 4: Insert sample temperature alerts
-- ============================================================================

-- Create some sample alerts based on violations
INSERT INTO temperature_alerts (
  user_id, sensor_id, storage_area_id, reading_id,
  alert_type, severity, alert_status, title, message,
  threshold_value, actual_value, deviation,
  first_detected_at, last_detected_at, duration_minutes,
  haccp_violation, regulatory_notification_required, product_safety_risk,
  escalated, escalation_level,
  email_sent, notification_attempts
)
SELECT 
  tr.user_id,
  tr.sensor_id, 
  tr.storage_area_id,
  tr.id,
  CASE 
    WHEN tr.temp_fahrenheit > sa.temp_max_fahrenheit THEN 'temp_high'
    ELSE 'temp_low'
  END as alert_type,
  CASE 
    WHEN ABS(tr.temp_fahrenheit - COALESCE(sa.temp_max_fahrenheit, sa.temp_min_fahrenheit)) > 10 THEN 'critical'
    WHEN ABS(tr.temp_fahrenheit - COALESCE(sa.temp_max_fahrenheit, sa.temp_min_fahrenheit)) > 5 THEN 'warning'
    ELSE 'info'
  END as severity,
  'resolved' as alert_status,
  'Temperature ' || CASE 
    WHEN tr.temp_fahrenheit > sa.temp_max_fahrenheit THEN 'Exceeded'
    ELSE 'Below'
  END || ' Safe Range' as title,
  'Temperature reading of ' || tr.temp_fahrenheit || '°F detected in ' || sa.name ||
  '. Safe range is ' || sa.temp_min_fahrenheit || '-' || sa.temp_max_fahrenheit || '°F.' as message,
  CASE 
    WHEN tr.temp_fahrenheit > sa.temp_max_fahrenheit THEN sa.temp_max_fahrenheit
    ELSE sa.temp_min_fahrenheit
  END as threshold_value,
  tr.temp_fahrenheit as actual_value,
  ABS(tr.temp_fahrenheit - COALESCE(sa.temp_max_fahrenheit, sa.temp_min_fahrenheit)) as deviation,
  tr.recorded_at as first_detected_at,
  tr.recorded_at + INTERVAL '10 minutes' as last_detected_at,
  10 as duration_minutes,
  sa.haccp_required as haccp_violation,
  false as regulatory_notification_required,
  CASE 
    WHEN ABS(tr.temp_fahrenheit - COALESCE(sa.temp_max_fahrenheit, sa.temp_min_fahrenheit)) > 10 THEN 'high'
    WHEN ABS(tr.temp_fahrenheit - COALESCE(sa.temp_max_fahrenheit, sa.temp_min_fahrenheit)) > 5 THEN 'medium'
    ELSE 'low'
  END as product_safety_risk,
  false as escalated,
  1 as escalation_level,
  true as email_sent,
  1 as notification_attempts
FROM temperature_readings tr
JOIN storage_areas sa ON sa.id = tr.storage_area_id  
WHERE tr.temp_violation = true
LIMIT 5; -- Just create a few sample alerts

-- Create one active alert for testing
INSERT INTO temperature_alerts (
  user_id, sensor_id, storage_area_id,
  alert_type, severity, alert_status, title, message,
  threshold_value, actual_value, deviation,
  first_detected_at, duration_minutes,
  haccp_violation, product_safety_risk,
  escalated, escalation_level, notification_attempts
) VALUES (
  '00000000-0000-0000-0000-000000000000'::uuid,
  (SELECT id FROM sensors WHERE sensor_id = 'TS-DRY-001-A' LIMIT 1),
  (SELECT id FROM storage_areas WHERE area_code = 'DRY-001' LIMIT 1),
  'battery_low',
  'warning', 
  'active',
  'Sensor Battery Low',
  'TempStick sensor "Dry Storage Monitor" battery level is at 23%. Consider replacing battery soon.',
  25.0, -- Threshold for low battery
  23.0, -- Current battery level
  2.0,  -- Deviation from threshold
  NOW() - INTERVAL '1 hour',
  60, -- 1 hour duration
  false, -- Not a HACCP violation
  'low', -- Low safety risk
  false, -- Not escalated yet
  1, -- First escalation level
  1  -- One notification attempt
);

-- Step 5: Update sensor last_seen times and battery levels to match sample data
-- ============================================================================

UPDATE sensors SET 
  battery_level = 23,
  battery_voltage = 2.7
WHERE sensor_id = 'TS-DRY-001-A';

UPDATE sensors SET 
  is_online = false,
  connection_status = 'offline',
  battery_level = 0,
  battery_voltage = 0.0,
  last_seen_at = NOW() - INTERVAL '2 hours'
WHERE sensor_id = 'TS-PROC-001-A';

-- Step 6: Add comments and completion message
-- ============================================================================

COMMENT ON FUNCTION update_updated_at_column() IS 'Automatically updates the updated_at timestamp when records are modified';
COMMENT ON FUNCTION check_temperature_violation() IS 'Automatically checks temperature readings against safe ranges and sets violation flags';

-- Sample data migration completed
-- ============================================================================
-- Sample data includes:
-- - 5 storage areas (cooler, freezer, display, dry storage, processing)
-- - 5 sensors (4 online, 1 offline) 
-- - 24 hours of realistic temperature readings with some violations
-- - Sample temperature alerts (resolved and active)
-- - Battery low alert for testing
-- ============================================================================