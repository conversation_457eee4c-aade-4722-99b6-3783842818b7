
-- Create templates table for shipping calculator
CREATE TABLE public.shipping_templates (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  template_data JSONB NOT NULL,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create templates table for processing calculator
CREATE TABLE public.processing_templates (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  template_data JSONB NOT NULL,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS on both tables
ALTER TABLE public.shipping_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.processing_templates ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for shipping templates
CREATE POLICY "Users can view all shipping templates" 
  ON public.shipping_templates 
  FOR SELECT 
  USING (true);

CREATE POLICY "Users can create shipping templates" 
  ON public.shipping_templates 
  FOR INSERT 
  WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Users can update their own shipping templates" 
  ON public.shipping_templates 
  FOR UPDATE 
  USING (auth.uid() = created_by);

CREATE POLICY "Users can delete their own shipping templates" 
  ON public.shipping_templates 
  FOR DELETE 
  USING (auth.uid() = created_by);

-- Create RLS policies for processing templates
CREATE POLICY "Users can view all processing templates" 
  ON public.processing_templates 
  FOR SELECT 
  USING (true);

CREATE POLICY "Users can create processing templates" 
  ON public.processing_templates 
  FOR INSERT 
  WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Users can update their own processing templates" 
  ON public.processing_templates 
  FOR UPDATE 
  USING (auth.uid() = created_by);

CREATE POLICY "Users can delete their own processing templates" 
  ON public.processing_templates 
  FOR DELETE 
  USING (auth.uid() = created_by);

-- Add indexes for better performance
CREATE INDEX idx_shipping_templates_created_by ON public.shipping_templates(created_by);
CREATE INDEX idx_processing_templates_created_by ON public.processing_templates(created_by);
;
