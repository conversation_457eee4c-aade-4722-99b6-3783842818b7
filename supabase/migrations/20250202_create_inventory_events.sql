-- Test with 5MB PNG
-- Submit form with large image → Check storage bucket
-- Create unified inventory events table
CREATE TABLE inventory_events (
    id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
    event_type TEXT NOT NULL,
    product_id uuid REFERENCES products(id),
    name TEXT,
    quantity NUMERIC,
    total_amount NUMERIC,
    unit_price NUMERIC,
    notes TEXT,
    images TEXT[],
    category TEXT NOT NULL DEFAULT 'Uncategorized',
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
-- Create an index on event_type for faster queries
CREATE INDEX idx_inventory_events_event_type ON inventory_events(event_type);
-- Create an index on product_id for faster joins
CREATE INDEX idx_inventory_events_product_id ON inventory_events(product_id);
-- Create a trigger to update the updated_at column
CREATE OR R<PERSON>LACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';
CREATE TRIGGER update_inventory_events_updated_at
    BEFORE UPDATE ON inventory_events
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
