-- ============================================================================
-- SECURITY VERIFICATION TESTS
-- ============================================================================
-- This file contains SQL commands to verify that the RLS policies are working
-- correctly and users cannot access each other's data.
--
-- IMPORTANT: These are TEST commands - DO NOT run in production with real data
-- Use these tests in a development/staging environment only
-- ============================================================================

-- Test Setup: Create test functions that simulate different user contexts
-- ============================================================================

-- Function to test user isolation by simulating different auth contexts
CREATE OR REPLACE FUNCTION test_user_isolation()
RETURNS TABLE(test_name TEXT, result TEXT, details TEXT) AS $$
DECLARE
    test_user_1 UUID := '11111111-1111-1111-1111-111111111111';
    test_user_2 UUID := '*************-2222-2222-************';
    product_1_id UUID;
    product_2_id UUID;
    event_1_id UUID;
    event_2_id UUID;
    isolation_test_passed BOOLEAN := TRUE;
    error_msg TEXT;
BEGIN
    -- ========================================================================
    -- Test 1: Basic RLS Policy Existence Check
    -- ========================================================================
    
    -- Check if RLS is enabled on critical tables
    RETURN QUERY SELECT 
        'RLS_ENABLED_products'::TEXT,
        CASE WHEN relrowsecurity THEN 'PASS' ELSE 'FAIL' END::TEXT,
        CASE WHEN relrowsecurity THEN 'RLS enabled on products table' 
             ELSE 'CRITICAL: RLS not enabled on products table' END::TEXT
    FROM pg_class WHERE relname = 'products';
    
    RETURN QUERY SELECT 
        'RLS_ENABLED_inventory_events'::TEXT,
        CASE WHEN relrowsecurity THEN 'PASS' ELSE 'FAIL' END::TEXT,
        CASE WHEN relrowsecurity THEN 'RLS enabled on inventory_events table' 
             ELSE 'CRITICAL: RLS not enabled on inventory_events table' END::TEXT
    FROM pg_class WHERE relname = 'inventory_events';
    
    -- ========================================================================
    -- Test 2: Policy Existence Check
    -- ========================================================================
    
    RETURN QUERY SELECT 
        'POLICIES_EXIST_products'::TEXT,
        CASE WHEN COUNT(*) >= 4 THEN 'PASS' ELSE 'FAIL' END::TEXT,
        'Found ' || COUNT(*)::TEXT || ' policies on products table (expected 4+)'::TEXT
    FROM pg_policies WHERE tablename = 'products';
    
    RETURN QUERY SELECT 
        'POLICIES_EXIST_inventory_events'::TEXT,
        CASE WHEN COUNT(*) >= 4 THEN 'PASS' ELSE 'FAIL' END::TEXT,
        'Found ' || COUNT(*)::TEXT || ' policies on inventory_events table (expected 4+)'::TEXT
    FROM pg_policies WHERE tablename = 'inventory_events';
    
    -- ========================================================================
    -- Test 3: User Column Existence Check
    -- ========================================================================
    
    RETURN QUERY SELECT 
        'USER_ID_COLUMN_products'::TEXT,
        CASE WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'products' AND column_name = 'user_id'
        ) THEN 'PASS' ELSE 'FAIL' END::TEXT,
        CASE WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'products' AND column_name = 'user_id'
        ) THEN 'user_id column exists on products table' 
           ELSE 'CRITICAL: user_id column missing on products table' END::TEXT;
    
    RETURN QUERY SELECT 
        'USER_ID_COLUMN_inventory_events'::TEXT,
        CASE WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'inventory_events' AND column_name = 'user_id'
        ) THEN 'PASS' ELSE 'FAIL' END::TEXT,
        CASE WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'inventory_events' AND column_name = 'user_id'
        ) THEN 'user_id column exists on inventory_events table' 
           ELSE 'CRITICAL: user_id column missing on inventory_events table' END::TEXT;
    
    -- ========================================================================
    -- Test 4: Index Existence Check
    -- ========================================================================
    
    RETURN QUERY SELECT 
        'INDEX_EXISTS_products_user_id'::TEXT,
        CASE WHEN EXISTS (
            SELECT 1 FROM pg_indexes 
            WHERE tablename = 'products' AND indexname = 'idx_products_user_id'
        ) THEN 'PASS' ELSE 'WARN' END::TEXT,
        CASE WHEN EXISTS (
            SELECT 1 FROM pg_indexes 
            WHERE tablename = 'products' AND indexname = 'idx_products_user_id'
        ) THEN 'Performance index exists on products.user_id' 
           ELSE 'WARNING: Performance index missing on products.user_id' END::TEXT;
    
    -- ========================================================================
    -- Test 5: Trigger Existence Check
    -- ========================================================================
    
    RETURN QUERY SELECT 
        'TRIGGER_EXISTS_products'::TEXT,
        CASE WHEN EXISTS (
            SELECT 1 FROM information_schema.triggers 
            WHERE event_object_table = 'products' AND trigger_name = 'set_products_user_id'
        ) THEN 'PASS' ELSE 'FAIL' END::TEXT,
        CASE WHEN EXISTS (
            SELECT 1 FROM information_schema.triggers 
            WHERE event_object_table = 'products' AND trigger_name = 'set_products_user_id'
        ) THEN 'Auto user_id trigger exists on products' 
           ELSE 'CRITICAL: Auto user_id trigger missing on products' END::TEXT;
    
    -- ========================================================================
    -- Test 6: Helper Function Existence Check
    -- ========================================================================
    
    RETURN QUERY SELECT 
        'HELPER_FUNCTION_user_owns_product'::TEXT,
        CASE WHEN EXISTS (
            SELECT 1 FROM pg_proc WHERE proname = 'user_owns_product'
        ) THEN 'PASS' ELSE 'WARN' END::TEXT,
        CASE WHEN EXISTS (
            SELECT 1 FROM pg_proc WHERE proname = 'user_owns_product'
        ) THEN 'Security helper function user_owns_product exists' 
           ELSE 'WARNING: Security helper function user_owns_product missing' END::TEXT;
    
    RETURN QUERY SELECT 
        'SECURITY_SETUP_COMPLETE'::TEXT,
        'INFO'::TEXT,
        'Basic security infrastructure tests completed. Run manual isolation tests next.'::TEXT;

EXCEPTION WHEN OTHERS THEN
    GET STACKED DIAGNOSTICS error_msg = MESSAGE_TEXT;
    RETURN QUERY SELECT 
        'TEST_ERROR'::TEXT,
        'FAIL'::TEXT,
        'Test execution failed: ' || error_msg::TEXT;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- Manual Test Instructions (Run these manually in different user contexts)
-- ============================================================================

/*

MANUAL TESTING INSTRUCTIONS:
============================

1. SETUP PHASE:
   Run this migration first, then follow these steps manually:

2. CREATE TEST USERS:
   -- In Supabase dashboard or via API, create two test users:
   -- User A: <EMAIL>  
   -- User B: <EMAIL>

3. TEST USER A ISOLATION:
   -- Login as User A, then run:
   
   INSERT INTO products (name, unit, user_id) 
   VALUES ('User A Product', 'lbs', auth.uid());
   
   INSERT INTO inventory_events (event_type, name, quantity, user_id) 
   VALUES ('receiving', 'User A Event', 100, auth.uid());
   
   -- Verify User A can see their data:
   SELECT count(*) FROM products;  -- Should show 1
   SELECT count(*) FROM inventory_events;  -- Should show 1

4. TEST USER B ISOLATION:
   -- Login as User B, then run:
   
   INSERT INTO products (name, unit, user_id) 
   VALUES ('User B Product', 'lbs', auth.uid());
   
   INSERT INTO inventory_events (event_type, name, quantity, user_id) 
   VALUES ('receiving', 'User B Event', 50, auth.uid());
   
   -- Verify User B can only see their data:
   SELECT count(*) FROM products;  -- Should show 1 (only User B's)
   SELECT count(*) FROM inventory_events;  -- Should show 1 (only User B's)

5. TEST CROSS-USER ACCESS BLOCKED:
   -- While logged in as User B, try to access User A's data:
   -- This should return 0 results due to RLS:
   SELECT count(*) FROM products WHERE name = 'User A Product';  -- Should show 0
   SELECT count(*) FROM inventory_events WHERE name = 'User A Event';  -- Should show 0

6. TEST UPDATE ISOLATION:
   -- User B should not be able to update User A's records
   -- Get User A's product ID first (while logged in as User A):
   SELECT id FROM products WHERE name = 'User A Product';
   
   -- Then as User B, try to update it (should fail):
   UPDATE products SET name = 'Hacked Product' WHERE id = '<User A product ID>';
   -- Should affect 0 rows due to RLS

7. AUTOMATED TEST RUN:
   SELECT * FROM test_user_isolation();

EXPECTED RESULTS:
================
- Each user sees only their own data
- Cross-user data access returns empty results  
- Cross-user updates affect 0 rows
- All test_user_isolation() tests return PASS
- No user can see or modify another user's products or inventory events

SECURITY VALIDATION CHECKLIST:
==============================
□ RLS enabled on all core tables
□ User isolation policies active
□ Triggers auto-set user_id
□ Cross-user access blocked
□ Performance indexes created
□ Helper functions available
□ Calendar events isolated
□ Audit trail preserved

*/

-- ============================================================================
-- Emergency Security Audit Function
-- ============================================================================

CREATE OR REPLACE FUNCTION emergency_security_audit()
RETURNS TABLE(
    security_issue TEXT, 
    severity TEXT, 
    affected_table TEXT, 
    recommendation TEXT
) AS $$
BEGIN
    -- Check for tables without RLS
    RETURN QUERY SELECT 
        'Missing RLS Protection'::TEXT,
        'CRITICAL'::TEXT,
        relname::TEXT,
        'Enable RLS: ALTER TABLE ' || relname || ' ENABLE ROW LEVEL SECURITY;'::TEXT
    FROM pg_class 
    WHERE relkind = 'r' 
    AND relname IN ('products', 'inventory_events', 'calendar_events')
    AND NOT relrowsecurity;
    
    -- Check for tables with RLS but no policies
    RETURN QUERY SELECT 
        'RLS Enabled But No Policies'::TEXT,
        'CRITICAL'::TEXT,
        c.relname::TEXT,
        'Create user isolation policies for table: ' || c.relname::TEXT
    FROM pg_class c
    WHERE c.relkind = 'r' 
    AND c.relname IN ('products', 'inventory_events', 'calendar_events')
    AND c.relrowsecurity = true
    AND NOT EXISTS (
        SELECT 1 FROM pg_policies p WHERE p.tablename = c.relname
    );
    
    -- Check for missing user_id columns
    RETURN QUERY SELECT 
        'Missing User ID Column'::TEXT,
        'CRITICAL'::TEXT,
        table_name::TEXT,
        'Add user ownership: ALTER TABLE ' || table_name || ' ADD COLUMN user_id UUID REFERENCES auth.users(id);'::TEXT
    FROM information_schema.tables t
    WHERE t.table_name IN ('products', 'inventory_events', 'calendar_events')
    AND NOT EXISTS (
        SELECT 1 FROM information_schema.columns c 
        WHERE c.table_name = t.table_name AND c.column_name = 'user_id'
    );
    
    -- If no issues found
    IF NOT FOUND THEN
        RETURN QUERY SELECT 
            'Security Audit Complete'::TEXT,
            'INFO'::TEXT,
            'All Tables'::TEXT,
            'Core security policies appear to be in place'::TEXT;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION test_user_isolation TO authenticated;
GRANT EXECUTE ON FUNCTION emergency_security_audit TO authenticated;

-- Comments
COMMENT ON FUNCTION test_user_isolation IS 'Automated test suite for RLS policy verification';
COMMENT ON FUNCTION emergency_security_audit IS 'Quick security audit to identify missing RLS protection';

-- ============================================================================
-- RUN BASIC TESTS IMMEDIATELY
-- ============================================================================

-- Execute basic security verification
SELECT * FROM test_user_isolation();

-- Execute emergency audit
SELECT * FROM emergency_security_audit();