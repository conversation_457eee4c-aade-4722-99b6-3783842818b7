-- ================================================================
-- VOICE EVENT MANAGEMENT SYSTEM - DATABASE SCHEMA EXTENSIONS
-- ================================================================
-- Created: 2025-08-15
-- Purpose: Extend inventory_events table with voice-specific columns and create audit trail
-- Requirements: 4.1, 4.2, 4.4, 4.5
-- ================================================================

BEGIN;

-- ================================================================
-- EXTEND INVENTORY_EVENTS TABLE WITH VOICE-SPECIFIC COLUMNS
-- ================================================================

-- Add voice processing confidence score (0.0 to 1.0)
ALTER TABLE inventory_events 
ADD COLUMN IF NOT EXISTS voice_confidence_score DECIMAL(3,2) CHECK (voice_confidence_score >= 0.0 AND voice_confidence_score <= 1.0);

-- Add detailed confidence breakdown as JSONB
ALTER TABLE inventory_events 
ADD COLUMN IF NOT EXISTS voice_confidence_breakdown JSONB;

-- Add raw transcript from voice processing
ALTER TABLE inventory_events 
ADD COLUMN IF NOT EXISTS raw_transcript TEXT;

-- Add URL to stored audio recording
ALTER TABLE inventory_events 
ADD COLUMN IF NOT EXISTS audio_recording_url TEXT;

-- Add flag to indicate if event was created via voice
ALTER TABLE inventory_events 
ADD COLUMN IF NOT EXISTS created_by_voice BOOLEAN DEFAULT FALSE;

-- ================================================================
-- CREATE EVENT AUDIT TRAIL TABLE
-- ================================================================

CREATE TABLE IF NOT EXISTS event_audit_trail (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_id UUID NOT NULL REFERENCES inventory_events(id) ON DELETE CASCADE,
    field_name TEXT NOT NULL,
    old_value JSONB,
    new_value JSONB,
    changed_by UUID REFERENCES auth.users(id),
    changed_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    change_reason TEXT,
    
    -- Metadata for tracking change context
    change_source TEXT DEFAULT 'manual', -- 'manual', 'voice', 'api', 'system'
    session_id TEXT, -- For tracking related changes in same session
    
    CONSTRAINT valid_change_source CHECK (change_source IN ('manual', 'voice', 'api', 'system'))
);

-- ================================================================
-- CREATE INDEXES FOR EFFICIENT QUERYING
-- ================================================================

-- Index for voice events filtering
CREATE INDEX IF NOT EXISTS idx_inventory_events_voice_created 
ON inventory_events(created_by_voice) 
WHERE created_by_voice = TRUE;

-- Index for confidence score filtering (for quality review)
CREATE INDEX IF NOT EXISTS idx_inventory_events_voice_confidence 
ON inventory_events(voice_confidence_score) 
WHERE voice_confidence_score IS NOT NULL;

-- Composite index for voice events with low confidence (quality review queue)
CREATE INDEX IF NOT EXISTS idx_inventory_events_voice_low_confidence 
ON inventory_events(created_by_voice, voice_confidence_score) 
WHERE created_by_voice = TRUE AND voice_confidence_score < 0.7;

-- Index for audio recordings (for cleanup and retrieval)
CREATE INDEX IF NOT EXISTS idx_inventory_events_audio_url 
ON inventory_events(audio_recording_url) 
WHERE audio_recording_url IS NOT NULL;

-- Indexes for audit trail table
CREATE INDEX IF NOT EXISTS idx_event_audit_trail_event_id 
ON event_audit_trail(event_id);

CREATE INDEX IF NOT EXISTS idx_event_audit_trail_changed_by 
ON event_audit_trail(changed_by);

CREATE INDEX IF NOT EXISTS idx_event_audit_trail_changed_at 
ON event_audit_trail(changed_at);

CREATE INDEX IF NOT EXISTS idx_event_audit_trail_field_name 
ON event_audit_trail(field_name);

-- Composite index for user activity tracking
CREATE INDEX IF NOT EXISTS idx_event_audit_trail_user_activity 
ON event_audit_trail(changed_by, changed_at);

-- ================================================================
-- CREATE AUDIT TRAIL TRIGGER FUNCTION
-- ================================================================

CREATE OR REPLACE FUNCTION create_event_audit_trail()
RETURNS TRIGGER AS $
DECLARE
    field_name TEXT;
    old_val JSONB;
    new_val JSONB;
    current_user_id UUID;
BEGIN
    -- Get current user ID (if available)
    current_user_id := auth.uid();
    
    -- Only create audit trail for UPDATE operations
    IF TG_OP = 'UPDATE' THEN
        -- Check each field for changes and create audit records
        
        -- Event type
        IF OLD.event_type IS DISTINCT FROM NEW.event_type THEN
            INSERT INTO event_audit_trail (event_id, field_name, old_value, new_value, changed_by, change_source)
            VALUES (NEW.id, 'event_type', to_jsonb(OLD.event_type), to_jsonb(NEW.event_type), current_user_id, 
                   CASE WHEN NEW.created_by_voice THEN 'voice' ELSE 'manual' END);
        END IF;
        
        -- Product ID
        IF OLD.product_id IS DISTINCT FROM NEW.product_id THEN
            INSERT INTO event_audit_trail (event_id, field_name, old_value, new_value, changed_by, change_source)
            VALUES (NEW.id, 'product_id', to_jsonb(OLD.product_id), to_jsonb(NEW.product_id), current_user_id,
                   CASE WHEN NEW.created_by_voice THEN 'voice' ELSE 'manual' END);
        END IF;
        
        -- Name
        IF OLD.name IS DISTINCT FROM NEW.name THEN
            INSERT INTO event_audit_trail (event_id, field_name, old_value, new_value, changed_by, change_source)
            VALUES (NEW.id, 'name', to_jsonb(OLD.name), to_jsonb(NEW.name), current_user_id,
                   CASE WHEN NEW.created_by_voice THEN 'voice' ELSE 'manual' END);
        END IF;
        
        -- Quantity
        IF OLD.quantity IS DISTINCT FROM NEW.quantity THEN
            INSERT INTO event_audit_trail (event_id, field_name, old_value, new_value, changed_by, change_source)
            VALUES (NEW.id, 'quantity', to_jsonb(OLD.quantity), to_jsonb(NEW.quantity), current_user_id,
                   CASE WHEN NEW.created_by_voice THEN 'voice' ELSE 'manual' END);
        END IF;
        
        -- Total amount
        IF OLD.total_amount IS DISTINCT FROM NEW.total_amount THEN
            INSERT INTO event_audit_trail (event_id, field_name, old_value, new_value, changed_by, change_source)
            VALUES (NEW.id, 'total_amount', to_jsonb(OLD.total_amount), to_jsonb(NEW.total_amount), current_user_id,
                   CASE WHEN NEW.created_by_voice THEN 'voice' ELSE 'manual' END);
        END IF;
        
        -- Unit price
        IF OLD.unit_price IS DISTINCT FROM NEW.unit_price THEN
            INSERT INTO event_audit_trail (event_id, field_name, old_value, new_value, changed_by, change_source)
            VALUES (NEW.id, 'unit_price', to_jsonb(OLD.unit_price), to_jsonb(NEW.unit_price), current_user_id,
                   CASE WHEN NEW.created_by_voice THEN 'voice' ELSE 'manual' END);
        END IF;
        
        -- Notes
        IF OLD.notes IS DISTINCT FROM NEW.notes THEN
            INSERT INTO event_audit_trail (event_id, field_name, old_value, new_value, changed_by, change_source)
            VALUES (NEW.id, 'notes', to_jsonb(OLD.notes), to_jsonb(NEW.notes), current_user_id,
                   CASE WHEN NEW.created_by_voice THEN 'voice' ELSE 'manual' END);
        END IF;
        
        -- Vendor ID
        IF OLD.vendor_id IS DISTINCT FROM NEW.vendor_id THEN
            INSERT INTO event_audit_trail (event_id, field_name, old_value, new_value, changed_by, change_source)
            VALUES (NEW.id, 'vendor_id', to_jsonb(OLD.vendor_id), to_jsonb(NEW.vendor_id), current_user_id,
                   CASE WHEN NEW.created_by_voice THEN 'voice' ELSE 'manual' END);
        END IF;
        
        -- Batch number
        IF OLD.batch_number IS DISTINCT FROM NEW.batch_number THEN
            INSERT INTO event_audit_trail (event_id, field_name, old_value, new_value, changed_by, change_source)
            VALUES (NEW.id, 'batch_number', to_jsonb(OLD.batch_number), to_jsonb(NEW.batch_number), current_user_id,
                   CASE WHEN NEW.created_by_voice THEN 'voice' ELSE 'manual' END);
        END IF;
        
        -- Condition on receipt
        IF OLD.condition_on_receipt IS DISTINCT FROM NEW.condition_on_receipt THEN
            INSERT INTO event_audit_trail (event_id, field_name, old_value, new_value, changed_by, change_source)
            VALUES (NEW.id, 'condition_on_receipt', to_jsonb(OLD.condition_on_receipt), to_jsonb(NEW.condition_on_receipt), current_user_id,
                   CASE WHEN NEW.created_by_voice THEN 'voice' ELSE 'manual' END);
        END IF;
        
        -- Temperature at receipt
        IF OLD.temperature_at_receipt IS DISTINCT FROM NEW.temperature_at_receipt THEN
            INSERT INTO event_audit_trail (event_id, field_name, old_value, new_value, changed_by, change_source)
            VALUES (NEW.id, 'temperature_at_receipt', to_jsonb(OLD.temperature_at_receipt), to_jsonb(NEW.temperature_at_receipt), current_user_id,
                   CASE WHEN NEW.created_by_voice THEN 'voice' ELSE 'manual' END);
        END IF;
        
        -- Occurred at
        IF OLD.occurred_at IS DISTINCT FROM NEW.occurred_at THEN
            INSERT INTO event_audit_trail (event_id, field_name, old_value, new_value, changed_by, change_source)
            VALUES (NEW.id, 'occurred_at', to_jsonb(OLD.occurred_at), to_jsonb(NEW.occurred_at), current_user_id,
                   CASE WHEN NEW.created_by_voice THEN 'voice' ELSE 'manual' END);
        END IF;
        
        -- Voice-specific fields
        IF OLD.voice_confidence_score IS DISTINCT FROM NEW.voice_confidence_score THEN
            INSERT INTO event_audit_trail (event_id, field_name, old_value, new_value, changed_by, change_source)
            VALUES (NEW.id, 'voice_confidence_score', to_jsonb(OLD.voice_confidence_score), to_jsonb(NEW.voice_confidence_score), current_user_id, 'voice');
        END IF;
        
        IF OLD.voice_confidence_breakdown IS DISTINCT FROM NEW.voice_confidence_breakdown THEN
            INSERT INTO event_audit_trail (event_id, field_name, old_value, new_value, changed_by, change_source)
            VALUES (NEW.id, 'voice_confidence_breakdown', OLD.voice_confidence_breakdown, NEW.voice_confidence_breakdown, current_user_id, 'voice');
        END IF;
        
        IF OLD.raw_transcript IS DISTINCT FROM NEW.raw_transcript THEN
            INSERT INTO event_audit_trail (event_id, field_name, old_value, new_value, changed_by, change_source)
            VALUES (NEW.id, 'raw_transcript', to_jsonb(OLD.raw_transcript), to_jsonb(NEW.raw_transcript), current_user_id, 'voice');
        END IF;
        
        IF OLD.created_by_voice IS DISTINCT FROM NEW.created_by_voice THEN
            INSERT INTO event_audit_trail (event_id, field_name, old_value, new_value, changed_by, change_source)
            VALUES (NEW.id, 'created_by_voice', to_jsonb(OLD.created_by_voice), to_jsonb(NEW.created_by_voice), current_user_id, 'system');
        END IF;
    END IF;
    
    RETURN NEW;
END;
$ LANGUAGE plpgsql;

-- ================================================================
-- CREATE AUDIT TRAIL TRIGGER
-- ================================================================

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS inventory_events_audit_trail_trigger ON inventory_events;

-- Create trigger for audit trail
CREATE TRIGGER inventory_events_audit_trail_trigger
    AFTER UPDATE ON inventory_events
    FOR EACH ROW
    EXECUTE FUNCTION create_event_audit_trail();

-- ================================================================
-- CREATE HELPER FUNCTIONS FOR VOICE EVENT MANAGEMENT
-- ================================================================

-- Function to get voice events with confidence filtering
CREATE OR REPLACE FUNCTION get_voice_events(
    confidence_threshold DECIMAL DEFAULT 0.0,
    limit_count INTEGER DEFAULT 100,
    offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    event_type TEXT,
    name TEXT,
    quantity NUMERIC,
    voice_confidence_score DECIMAL,
    voice_confidence_breakdown JSONB,
    raw_transcript TEXT,
    audio_recording_url TEXT,
    created_at TIMESTAMPTZ,
    occurred_at TIMESTAMPTZ
) AS $
BEGIN
    RETURN QUERY
    SELECT 
        ie.id,
        ie.event_type,
        ie.name,
        ie.quantity,
        ie.voice_confidence_score,
        ie.voice_confidence_breakdown,
        ie.raw_transcript,
        ie.audio_recording_url,
        ie.created_at,
        ie.occurred_at
    FROM inventory_events ie
    WHERE ie.created_by_voice = TRUE
      AND (ie.voice_confidence_score IS NULL OR ie.voice_confidence_score >= confidence_threshold)
    ORDER BY ie.created_at DESC
    LIMIT limit_count
    OFFSET offset_count;
END;
$ LANGUAGE plpgsql;

-- Function to get events requiring quality review (low confidence)
CREATE OR REPLACE FUNCTION get_events_for_quality_review(
    confidence_threshold DECIMAL DEFAULT 0.7
)
RETURNS TABLE (
    id UUID,
    event_type TEXT,
    name TEXT,
    quantity NUMERIC,
    voice_confidence_score DECIMAL,
    voice_confidence_breakdown JSONB,
    raw_transcript TEXT,
    audio_recording_url TEXT,
    created_at TIMESTAMPTZ,
    days_pending INTEGER
) AS $
BEGIN
    RETURN QUERY
    SELECT 
        ie.id,
        ie.event_type,
        ie.name,
        ie.quantity,
        ie.voice_confidence_score,
        ie.voice_confidence_breakdown,
        ie.raw_transcript,
        ie.audio_recording_url,
        ie.created_at,
        EXTRACT(DAY FROM (CURRENT_TIMESTAMP - ie.created_at))::INTEGER as days_pending
    FROM inventory_events ie
    WHERE ie.created_by_voice = TRUE
      AND ie.voice_confidence_score < confidence_threshold
    ORDER BY ie.created_at ASC; -- Oldest first for review queue
END;
$ LANGUAGE plpgsql;

-- Function to get audit trail for a specific event
CREATE OR REPLACE FUNCTION get_event_audit_trail(event_uuid UUID)
RETURNS TABLE (
    id UUID,
    field_name TEXT,
    old_value JSONB,
    new_value JSONB,
    changed_by UUID,
    changed_at TIMESTAMPTZ,
    change_reason TEXT,
    change_source TEXT
) AS $
BEGIN
    RETURN QUERY
    SELECT 
        eat.id,
        eat.field_name,
        eat.old_value,
        eat.new_value,
        eat.changed_by,
        eat.changed_at,
        eat.change_reason,
        eat.change_source
    FROM event_audit_trail eat
    WHERE eat.event_id = event_uuid
    ORDER BY eat.changed_at DESC;
END;
$ LANGUAGE plpgsql;

-- ================================================================
-- CREATE VIEWS FOR VOICE EVENT MANAGEMENT
-- ================================================================

-- View for voice events with user information
CREATE OR REPLACE VIEW voice_events_with_users AS
SELECT 
    ie.id,
    ie.event_type,
    ie.name,
    ie.quantity,
    ie.total_amount,
    ie.unit_price,
    ie.notes,
    ie.voice_confidence_score,
    ie.voice_confidence_breakdown,
    ie.raw_transcript,
    ie.audio_recording_url,
    ie.created_at,
    ie.occurred_at,
    ie.updated_at,
    
    -- Product information
    p.name as product_name,
    p.category as product_category,
    
    -- Vendor information
    v.name as vendor_name,
    v.contact_name as vendor_contact,
    
    -- User information (if available)
    u.email as created_by_email,
    
    -- Confidence level categorization
    CASE 
        WHEN ie.voice_confidence_score >= 0.9 THEN 'high'
        WHEN ie.voice_confidence_score >= 0.7 THEN 'medium'
        WHEN ie.voice_confidence_score < 0.7 THEN 'low'
        ELSE 'unknown'
    END as confidence_level,
    
    -- Quality review status
    CASE 
        WHEN ie.voice_confidence_score < 0.7 THEN true
        ELSE false
    END as requires_review
    
FROM inventory_events ie
LEFT JOIN products p ON ie.product_id = p.id
LEFT JOIN vendors v ON ie.vendor_id = v.id
LEFT JOIN auth.users u ON ie.metadata->>'created_by_user_id' = u.id::text
WHERE ie.created_by_voice = TRUE;

-- ================================================================
-- ADD COMMENTS FOR DOCUMENTATION
-- ================================================================

COMMENT ON COLUMN inventory_events.voice_confidence_score IS 'Confidence score (0.0-1.0) from voice processing system';
COMMENT ON COLUMN inventory_events.voice_confidence_breakdown IS 'Detailed confidence breakdown by component (product_match, quantity_extraction, etc.)';
COMMENT ON COLUMN inventory_events.raw_transcript IS 'Original transcript from voice-to-text processing';
COMMENT ON COLUMN inventory_events.audio_recording_url IS 'URL to stored audio recording for review and verification';
COMMENT ON COLUMN inventory_events.created_by_voice IS 'Flag indicating if this event was created through voice processing';

COMMENT ON TABLE event_audit_trail IS 'Audit trail for tracking changes to inventory events, especially voice-created ones';
COMMENT ON FUNCTION create_event_audit_trail IS 'Trigger function to automatically create audit trail entries when inventory events are modified';
COMMENT ON FUNCTION get_voice_events IS 'Helper function to retrieve voice events with confidence filtering';
COMMENT ON FUNCTION get_events_for_quality_review IS 'Helper function to get voice events requiring quality review (low confidence)';
COMMENT ON FUNCTION get_event_audit_trail IS 'Helper function to retrieve audit trail for a specific event';
COMMENT ON VIEW voice_events_with_users IS 'Comprehensive view of voice events with related user, product, and vendor information';

-- ================================================================
-- SET UP ROW LEVEL SECURITY (RLS) POLICIES
-- ================================================================

-- Enable RLS on audit trail table
ALTER TABLE event_audit_trail ENABLE ROW LEVEL SECURITY;

-- Policy for authenticated users to read audit trails for events they can access
CREATE POLICY "Users can view audit trails for accessible events" ON event_audit_trail
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM inventory_events ie 
            WHERE ie.id = event_audit_trail.event_id
            -- Add your existing RLS logic here - this assumes users can access events they created
            -- or events in their organization/tenant
        )
    );

-- Policy for authenticated users to create audit trails (handled by trigger)
CREATE POLICY "System can create audit trails" ON event_audit_trail
    FOR INSERT WITH CHECK (true);

-- Grant permissions
GRANT SELECT ON event_audit_trail TO authenticated;
GRANT SELECT ON voice_events_with_users TO authenticated;
GRANT EXECUTE ON FUNCTION get_voice_events TO authenticated;
GRANT EXECUTE ON FUNCTION get_events_for_quality_review TO authenticated;
GRANT EXECUTE ON FUNCTION get_event_audit_trail TO authenticated;

-- ================================================================
-- VALIDATION AND CONSTRAINTS
-- ================================================================

-- Add constraint to ensure voice confidence breakdown has required fields when confidence score is present
ALTER TABLE inventory_events 
ADD CONSTRAINT voice_confidence_breakdown_structure 
CHECK (
    (voice_confidence_score IS NULL AND voice_confidence_breakdown IS NULL) OR
    (voice_confidence_score IS NOT NULL AND voice_confidence_breakdown IS NOT NULL AND
     voice_confidence_breakdown ? 'overall' AND
     voice_confidence_breakdown ? 'product_match' AND
     voice_confidence_breakdown ? 'quantity_extraction')
);

-- Add constraint to ensure created_by_voice is true when voice fields are present
ALTER TABLE inventory_events 
ADD CONSTRAINT voice_fields_consistency 
CHECK (
    (created_by_voice = FALSE AND voice_confidence_score IS NULL AND raw_transcript IS NULL) OR
    (created_by_voice = TRUE)
);

COMMIT;