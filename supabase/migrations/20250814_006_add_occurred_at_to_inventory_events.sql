-- Add occurred_at to inventory_events for voice processing integration
-- This field stores when the actual event occurred (vs when it was recorded)

BEGIN;

-- Add occurred_at column if it doesn't exist
ALTER TABLE inventory_events
  ADD COLUMN IF NOT EXISTS occurred_at TIMESTAMPTZ;

-- Backfill occurred_at from created_at for existing rows
UPDATE inventory_events
SET occurred_at = created_at
WHERE occurred_at IS NULL;

-- Set default value for new rows
ALTER TABLE inventory_events
  ALTER COLUMN occurred_at SET DEFAULT CURRENT_TIMESTAMP;

-- Add index for time-based queries
CREATE INDEX IF NOT EXISTS idx_inventory_events_occurred_at
  ON inventory_events(occurred_at);

-- Add comment for documentation
COMMENT ON COLUMN inventory_events.occurred_at IS 'When the inventory event actually occurred (may differ from created_at which is when it was recorded)';

COMMIT;