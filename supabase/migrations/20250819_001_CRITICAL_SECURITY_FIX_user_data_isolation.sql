-- ============================================================================
-- CRITICAL SECURITY FIX: USER DATA ISOLATION
-- ============================================================================
-- This migration addresses a critical security vulnerability where authenticated
-- users could access ALL data from ALL other users. This implements proper
-- Row Level Security (RLS) policies for user-based data isolation.
--
-- SECURITY IMPACT: HIGH - Prevents data breaches between users
-- TABLES AFFECTED: products, inventory_events, calendar_events
-- ============================================================================

-- Step 1: Add user ownership tracking to core tables
-- ============================================================================

-- Add user_id to products table for ownership tracking
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE;

-- Add user_id to inventory_events table for ownership tracking  
ALTER TABLE inventory_events 
ADD COLUMN IF NOT EXISTS user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE;

-- Add user_id to calendar_events table for ownership tracking
ALTER TABLE calendar_events 
ADD COLUMN IF NOT EXISTS user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE;

-- Step 2: Create indexes for performance on user_id columns
-- ============================================================================

CREATE INDEX IF NOT EXISTS idx_products_user_id ON products(user_id);
CREATE INDEX IF NOT EXISTS idx_inventory_events_user_id ON inventory_events(user_id);
CREATE INDEX IF NOT EXISTS idx_calendar_events_user_id ON calendar_events(user_id);

-- Step 3: Backfill existing data with current user context
-- ============================================================================
-- WARNING: In production, you should run a data migration script to properly
-- assign ownership of existing records to appropriate users.
-- For now, we'll set a default so the migration doesn't break existing data.

-- Set user_id to a placeholder for existing records (should be updated manually)
UPDATE products 
SET user_id = '00000000-0000-0000-0000-000000000000'::uuid 
WHERE user_id IS NULL;

UPDATE inventory_events 
SET user_id = '00000000-0000-0000-0000-000000000000'::uuid 
WHERE user_id IS NULL;

UPDATE calendar_events 
SET user_id = '00000000-0000-0000-0000-000000000000'::uuid 
WHERE user_id IS NULL;

-- Step 4: Enable Row Level Security on core tables
-- ============================================================================

ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE calendar_events ENABLE ROW LEVEL SECURITY;

-- Step 5: Create comprehensive RLS policies for user data isolation
-- ============================================================================

-- PRODUCTS TABLE POLICIES
-- Users can only see their own products
CREATE POLICY "Users can view their own products" 
  ON products FOR SELECT 
  USING (auth.uid() = user_id);

-- Users can only insert products they own
CREATE POLICY "Users can create their own products" 
  ON products FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- Users can only update their own products
CREATE POLICY "Users can update their own products" 
  ON products FOR UPDATE 
  USING (auth.uid() = user_id) 
  WITH CHECK (auth.uid() = user_id);

-- Users can only delete their own products
CREATE POLICY "Users can delete their own products" 
  ON products FOR DELETE 
  USING (auth.uid() = user_id);

-- INVENTORY_EVENTS TABLE POLICIES
-- Users can only see their own inventory events
CREATE POLICY "Users can view their own inventory events" 
  ON inventory_events FOR SELECT 
  USING (auth.uid() = user_id);

-- Users can only insert inventory events they own
CREATE POLICY "Users can create their own inventory events" 
  ON inventory_events FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- Users can only update their own inventory events
CREATE POLICY "Users can update their own inventory events" 
  ON inventory_events FOR UPDATE 
  USING (auth.uid() = user_id) 
  WITH CHECK (auth.uid() = user_id);

-- Users can only delete their own inventory events
CREATE POLICY "Users can delete their own inventory events" 
  ON inventory_events FOR DELETE 
  USING (auth.uid() = user_id);

-- CALENDAR_EVENTS TABLE POLICIES
-- Users can only see their own calendar events
CREATE POLICY "Users can view their own calendar events" 
  ON calendar_events FOR SELECT 
  USING (auth.uid() = user_id);

-- Users can only insert calendar events they own
CREATE POLICY "Users can create their own calendar events" 
  ON calendar_events FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- Users can only update their own calendar events
CREATE POLICY "Users can update their own calendar events" 
  ON calendar_events FOR UPDATE 
  USING (auth.uid() = user_id) 
  WITH CHECK (auth.uid() = user_id);

-- Users can only delete their own calendar events
CREATE POLICY "Users can delete their own calendar events" 
  ON calendar_events FOR DELETE 
  USING (auth.uid() = user_id);

-- Step 6: Update triggers to automatically set user_id
-- ============================================================================

-- Create function to automatically set user_id on insert
CREATE OR REPLACE FUNCTION set_user_id()
RETURNS TRIGGER AS $$
BEGIN
  -- Set user_id to current authenticated user
  NEW.user_id = auth.uid();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Apply trigger to products table
CREATE TRIGGER set_products_user_id
  BEFORE INSERT ON products
  FOR EACH ROW
  EXECUTE FUNCTION set_user_id();

-- Apply trigger to inventory_events table
CREATE TRIGGER set_inventory_events_user_id
  BEFORE INSERT ON inventory_events
  FOR EACH ROW
  EXECUTE FUNCTION set_user_id();

-- Apply trigger to calendar_events table
CREATE TRIGGER set_calendar_events_user_id
  BEFORE INSERT ON calendar_events
  FOR EACH ROW
  EXECUTE FUNCTION set_user_id();

-- Step 7: Update calendar sync function to respect user isolation
-- ============================================================================

-- Update the calendar sync function to properly set user_id
CREATE OR REPLACE FUNCTION sync_calendar_from_inventory_events()
RETURNS TRIGGER AS $$
DECLARE
  v_title text;
  v_desc text;
BEGIN
  IF (TG_OP = 'INSERT') THEN
    -- Generate title based on event type and product
    v_title := CASE 
      WHEN NEW.event_type = 'receiving' THEN 'Received: ' || COALESCE(NEW.name, 'Product')
      WHEN NEW.event_type = 'sales' THEN 'Sale: ' || COALESCE(NEW.name, 'Product')
      WHEN NEW.event_type = 'disposal' THEN 'Disposal: ' || COALESCE(NEW.name, 'Product')
      WHEN NEW.event_type = 'production' THEN 'Production: ' || COALESCE(NEW.name, 'Product')
      WHEN NEW.event_type = 'adjustment' THEN 'Adjustment: ' || COALESCE(NEW.name, 'Product')
      ELSE NEW.event_type || ': ' || COALESCE(NEW.name, 'Product')
    END;
    
    v_desc := NULLIF(NEW.notes, '');
    INSERT INTO calendar_events (
      title, description, start_at, end_at, all_day,
      source, source_id, inventory_event_id,
      event_type, product_id, user_id, metadata
    ) VALUES (
      v_title, v_desc, NEW.created_at, NULL, false,
      'inventory_events', NEW.id, NEW.id,
      NEW.event_type, NEW.product_id, NEW.user_id,
      coalesce(NEW.metadata, '{}'::jsonb) || jsonb_build_object(
        'quantity', NEW.quantity,
        'total_amount', NEW.total_amount,
        'unit_price', NEW.unit_price,
        'category', NEW.category,
        'batch_number', NEW.batch_number,
        'images', coalesce(NEW.images, ARRAY[]::text[])
      )
    );
    RETURN NEW;
  ELSIF (TG_OP = 'UPDATE') THEN
    v_title := CASE 
      WHEN NEW.event_type = 'receiving' THEN 'Received: ' || COALESCE(NEW.name, 'Product')
      WHEN NEW.event_type = 'sales' THEN 'Sale: ' || COALESCE(NEW.name, 'Product')
      WHEN NEW.event_type = 'disposal' THEN 'Disposal: ' || COALESCE(NEW.name, 'Product')
      WHEN NEW.event_type = 'production' THEN 'Production: ' || COALESCE(NEW.name, 'Product')
      WHEN NEW.event_type = 'adjustment' THEN 'Adjustment: ' || COALESCE(NEW.name, 'Product')
      ELSE NEW.event_type || ': ' || COALESCE(NEW.name, 'Product')
    END;
    
    v_desc := NULLIF(NEW.notes, '');
    UPDATE calendar_events SET
      title = v_title,
      description = v_desc,
      start_at = NEW.created_at,
      event_type = NEW.event_type,
      product_id = NEW.product_id,
      user_id = NEW.user_id,  -- Ensure user_id is synchronized
      metadata = coalesce(NEW.metadata, '{}'::jsonb) || jsonb_build_object(
        'quantity', NEW.quantity,
        'total_amount', NEW.total_amount,
        'unit_price', NEW.unit_price,
        'category', NEW.category,
        'batch_number', NEW.batch_number,
        'images', coalesce(NEW.images, ARRAY[]::text[])
      ),
      updated_at = now()
    WHERE inventory_event_id = NEW.id;
    RETURN NEW;
  ELSIF (TG_OP = 'DELETE') THEN
    DELETE FROM calendar_events WHERE inventory_event_id = OLD.id;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 8: Create helper functions for user data verification
-- ============================================================================

-- Function to check if current user owns a product
CREATE OR REPLACE FUNCTION user_owns_product(product_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM products 
    WHERE id = product_uuid AND user_id = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if current user owns an inventory event
CREATE OR REPLACE FUNCTION user_owns_inventory_event(event_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM inventory_events 
    WHERE id = event_uuid AND user_id = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION user_owns_product TO authenticated;
GRANT EXECUTE ON FUNCTION user_owns_inventory_event TO authenticated;

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON COLUMN products.user_id IS 'User ownership for data isolation - prevents cross-user data access';
COMMENT ON COLUMN inventory_events.user_id IS 'User ownership for data isolation - prevents cross-user data access';
COMMENT ON COLUMN calendar_events.user_id IS 'User ownership for data isolation - prevents cross-user data access';

COMMENT ON FUNCTION set_user_id IS 'Automatically sets user_id to current authenticated user on insert';
COMMENT ON FUNCTION user_owns_product IS 'Security helper: verifies if current user owns the specified product';
COMMENT ON FUNCTION user_owns_inventory_event IS 'Security helper: verifies if current user owns the specified inventory event';

-- ============================================================================
-- CRITICAL SECURITY NOTICES
-- ============================================================================

-- WARNING: After applying this migration, you MUST update your application code to:
-- 1. Handle the new user_id columns in INSERT statements
-- 2. Update any existing data assignment scripts
-- 3. Test all functionality with multiple users to ensure proper isolation
--
-- TESTING CHECKLIST:
-- □ Create test users A and B
-- □ User A creates products - verify User B cannot see them
-- □ User A creates inventory events - verify User B cannot see them  
-- □ User A updates own data - verify successful
-- □ User A attempts to update User B's data - verify blocked
-- □ Check calendar events isolation
-- □ Verify triggers properly set user_id on new records
--
-- DATABASE SECURITY STATUS: CRITICAL VULNERABILITY FIXED