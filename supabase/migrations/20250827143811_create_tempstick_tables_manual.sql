-- ============================================================================
-- TempStick Sensor Integration Tables
-- ============================================================================
-- Creates core tables for temperature monitoring and HACCP compliance
-- ============================================================================

-- Step 1: Create storage_areas table
-- ============================================================================

CREATE TABLE IF NOT EXISTS storage_areas (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  
  -- Area identification
  name VARCHAR(255) NOT NULL,
  description TEXT,
  area_code VARCHAR(50),
  location VARCHAR(255),
  
  -- Area classification
  area_type VARCHAR(50) NOT NULL CHECK (area_type IN (
    'walk_in_cooler', 'walk_in_freezer', 'reach_in_cooler', 'reach_in_freezer',
    'dry_storage', 'processing_area', 'shipping_dock', 'receiving_area',
    'blast_chiller', 'ice_storage', 'custom'
  )),
  custom_area_type VARCHAR(100),
  
  -- HACCP requirements
  haccp_required BOOLEAN DEFAULT false,
  haccp_ccp_number VARCHAR(50),
  
  -- Temperature requirements
  temp_min_celsius DECIMAL(5,2),
  temp_max_celsius DECIMAL(5,2),  
  temp_min_fahrenheit DECIMAL(5,2),
  temp_max_fahrenheit DECIMAL(5,2),
  temp_unit VARCHAR(10) DEFAULT 'fahrenheit' CHECK (temp_unit IN ('celsius', 'fahrenheit')),
  
  -- Humidity requirements (optional)
  humidity_min DECIMAL(5,2),
  humidity_max DECIMAL(5,2),
  
  -- Alert configuration
  alert_enabled BOOLEAN DEFAULT true,
  alert_threshold_minutes INTEGER DEFAULT 15,
  escalation_minutes INTEGER DEFAULT 60,
  
  -- Status and metadata
  is_active BOOLEAN DEFAULT true,
  capacity_info JSONB,
  compliance_notes TEXT,
  metadata JSONB,
  
  -- Audit timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  
  UNIQUE(user_id, area_code)
);

-- Step 2: Create sensors table
-- ============================================================================

CREATE TABLE IF NOT EXISTS sensors (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  storage_area_id UUID REFERENCES storage_areas(id) ON DELETE CASCADE,
  
  -- TempStick API fields
  sensor_id VARCHAR(100) NOT NULL,
  device_name VARCHAR(255) NOT NULL,
  device_type VARCHAR(100) DEFAULT 'TempStick',
  firmware_version VARCHAR(50),
  
  -- Sensor configuration
  name VARCHAR(255) NOT NULL,
  description TEXT,
  location_description TEXT,
  
  -- Connection status
  is_online BOOLEAN DEFAULT true,
  last_seen_at TIMESTAMPTZ,
  connection_status VARCHAR(50) DEFAULT 'online' CHECK (connection_status IN (
    'online', 'offline', 'maintenance', 'error'
  )),
  
  -- Battery and hardware status
  battery_level INTEGER,
  battery_voltage DECIMAL(4,2),
  signal_strength INTEGER,
  
  -- Calibration settings
  temp_offset_celsius DECIMAL(4,2) DEFAULT 0.0,
  temp_offset_fahrenheit DECIMAL(4,2) DEFAULT 0.0,
  humidity_offset DECIMAL(4,2) DEFAULT 0.0,
  last_calibrated_at TIMESTAMPTZ,
  calibrated_by UUID REFERENCES auth.users(id),
  
  -- Alert thresholds (override storage area defaults if set)
  custom_temp_min_celsius DECIMAL(5,2),
  custom_temp_max_celsius DECIMAL(5,2),
  custom_temp_min_fahrenheit DECIMAL(5,2),
  custom_temp_max_fahrenheit DECIMAL(5,2),
  custom_humidity_min DECIMAL(5,2),
  custom_humidity_max DECIMAL(5,2),
  
  -- Data collection settings
  reading_interval_minutes INTEGER DEFAULT 5,
  data_sync_enabled BOOLEAN DEFAULT true,
  
  -- Status and metadata
  is_active BOOLEAN DEFAULT true,
  installation_date TIMESTAMPTZ,
  last_maintenance_date TIMESTAMPTZ,
  next_maintenance_due TIMESTAMPTZ,
  maintenance_notes TEXT,
  metadata JSONB,
  
  -- Audit timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  
  UNIQUE(user_id, sensor_id)
);

-- Step 3: Create temperature_readings table
-- ============================================================================

CREATE TABLE IF NOT EXISTS temperature_readings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  sensor_id UUID REFERENCES sensors(id) ON DELETE CASCADE NOT NULL,
  storage_area_id UUID REFERENCES storage_areas(id) ON DELETE CASCADE,
  
  -- Reading data
  recorded_at TIMESTAMPTZ NOT NULL,
  temp_celsius DECIMAL(6,2) NOT NULL,
  temp_fahrenheit DECIMAL(6,2) NOT NULL,
  humidity DECIMAL(5,2),
  
  -- Data quality indicators
  reading_quality VARCHAR(20) DEFAULT 'good' CHECK (reading_quality IN (
    'good', 'acceptable', 'poor', 'error'
  )),
  signal_strength INTEGER,
  battery_level INTEGER,
  
  -- HACCP compliance flags
  within_safe_range BOOLEAN NOT NULL DEFAULT true,
  temp_violation BOOLEAN DEFAULT false,
  humidity_violation BOOLEAN DEFAULT false,
  
  -- TempStick API metadata
  api_reading_id VARCHAR(100),
  sync_status VARCHAR(20) DEFAULT 'synced' CHECK (sync_status IN (
    'pending', 'synced', 'error', 'manual'
  )),
  
  -- Data source tracking
  data_source VARCHAR(50) DEFAULT 'tempstick_api' CHECK (data_source IN (
    'tempstick_api', 'manual_entry', 'calibration', 'estimated'
  )),
  
  -- Metadata
  metadata JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Step 4: Create temperature_alerts table
-- ============================================================================

CREATE TABLE IF NOT EXISTS temperature_alerts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  sensor_id UUID REFERENCES sensors(id) ON DELETE CASCADE NOT NULL,
  storage_area_id UUID REFERENCES storage_areas(id) ON DELETE CASCADE,
  reading_id UUID REFERENCES temperature_readings(id) ON DELETE SET NULL,
  
  -- Alert classification
  alert_type VARCHAR(50) NOT NULL CHECK (alert_type IN (
    'temp_high', 'temp_low', 'humidity_high', 'humidity_low',
    'sensor_offline', 'battery_low', 'calibration_due', 'maintenance_due',
    'data_gap', 'system_error'
  )),
  
  -- Alert severity and status
  severity VARCHAR(20) NOT NULL CHECK (severity IN (
    'info', 'warning', 'critical', 'emergency'
  )),
  alert_status VARCHAR(20) DEFAULT 'active' CHECK (alert_status IN (
    'active', 'acknowledged', 'investigating', 'resolved', 'dismissed'
  )),
  
  -- Alert details
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  threshold_value DECIMAL(8,2),
  actual_value DECIMAL(8,2),
  duration_minutes INTEGER,
  
  -- Resolution tracking
  acknowledged_at TIMESTAMPTZ,
  acknowledged_by UUID REFERENCES auth.users(id),
  resolved_at TIMESTAMPTZ,
  resolved_by UUID REFERENCES auth.users(id),
  resolution_notes TEXT,
  
  -- HACCP compliance
  haccp_critical BOOLEAN DEFAULT false,
  corrective_action_required BOOLEAN DEFAULT false,
  corrective_action_taken TEXT,
  verification_required BOOLEAN DEFAULT false,
  verified_at TIMESTAMPTZ,
  verified_by UUID REFERENCES auth.users(id),
  
  -- Escalation tracking
  escalation_level INTEGER DEFAULT 1,
  escalated_at TIMESTAMPTZ,
  escalated_to UUID REFERENCES auth.users(id),
  
  -- Metadata
  metadata JSONB,
  
  -- Audit timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ============================================================================
-- Create Performance Indexes
-- ============================================================================

-- Storage areas indexes
CREATE INDEX IF NOT EXISTS idx_storage_areas_user_id ON storage_areas(user_id);
CREATE INDEX IF NOT EXISTS idx_storage_areas_area_type ON storage_areas(area_type);
CREATE INDEX IF NOT EXISTS idx_storage_areas_is_active ON storage_areas(is_active);

-- Sensors indexes
CREATE INDEX IF NOT EXISTS idx_sensors_user_id ON sensors(user_id);
CREATE INDEX IF NOT EXISTS idx_sensors_storage_area_id ON sensors(storage_area_id);
CREATE INDEX IF NOT EXISTS idx_sensors_sensor_id ON sensors(sensor_id);
CREATE INDEX IF NOT EXISTS idx_sensors_is_online ON sensors(is_online);
CREATE INDEX IF NOT EXISTS idx_sensors_connection_status ON sensors(connection_status);

-- Temperature readings indexes
CREATE INDEX IF NOT EXISTS idx_temperature_readings_user_id ON temperature_readings(user_id);
CREATE INDEX IF NOT EXISTS idx_temperature_readings_sensor_id ON temperature_readings(sensor_id);
CREATE INDEX IF NOT EXISTS idx_temperature_readings_recorded_at ON temperature_readings(recorded_at);
CREATE INDEX IF NOT EXISTS idx_temperature_readings_temp_violation ON temperature_readings(temp_violation);
CREATE INDEX IF NOT EXISTS idx_temperature_readings_within_safe_range ON temperature_readings(within_safe_range);

-- Temperature alerts indexes
CREATE INDEX IF NOT EXISTS idx_temperature_alerts_user_id ON temperature_alerts(user_id);
CREATE INDEX IF NOT EXISTS idx_temperature_alerts_sensor_id ON temperature_alerts(sensor_id);
CREATE INDEX IF NOT EXISTS idx_temperature_alerts_alert_type ON temperature_alerts(alert_type);
CREATE INDEX IF NOT EXISTS idx_temperature_alerts_alert_status ON temperature_alerts(alert_status);
CREATE INDEX IF NOT EXISTS idx_temperature_alerts_severity ON temperature_alerts(severity);
CREATE INDEX IF NOT EXISTS idx_temperature_alerts_created_at ON temperature_alerts(created_at);

-- ============================================================================
-- Create RLS Policies
-- ============================================================================

-- Enable RLS on all tables
ALTER TABLE storage_areas ENABLE ROW LEVEL SECURITY;
ALTER TABLE sensors ENABLE ROW LEVEL SECURITY;
ALTER TABLE temperature_readings ENABLE ROW LEVEL SECURITY;
ALTER TABLE temperature_alerts ENABLE ROW LEVEL SECURITY;

-- Storage areas RLS policies
CREATE POLICY "Users can view their own storage areas" ON storage_areas
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own storage areas" ON storage_areas
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own storage areas" ON storage_areas
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own storage areas" ON storage_areas
  FOR DELETE USING (auth.uid() = user_id);

-- Sensors RLS policies
CREATE POLICY "Users can view their own sensors" ON sensors
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own sensors" ON sensors
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own sensors" ON sensors
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own sensors" ON sensors
  FOR DELETE USING (auth.uid() = user_id);

-- Temperature readings RLS policies
CREATE POLICY "Users can view their own temperature readings" ON temperature_readings
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own temperature readings" ON temperature_readings
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own temperature readings" ON temperature_readings
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own temperature readings" ON temperature_readings
  FOR DELETE USING (auth.uid() = user_id);

-- Temperature alerts RLS policies
CREATE POLICY "Users can view their own temperature alerts" ON temperature_alerts
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own temperature alerts" ON temperature_alerts
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own temperature alerts" ON temperature_alerts
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own temperature alerts" ON temperature_alerts
  FOR DELETE USING (auth.uid() = user_id);