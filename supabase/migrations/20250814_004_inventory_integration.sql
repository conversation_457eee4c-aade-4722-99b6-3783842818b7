-- ================================================================
-- VENDOR REPORT CARD SYSTEM - INVENTORY EVENTS INTEGRATION
-- ================================================================
-- Created: 2025-08-14
-- Purpose: Integrate vendor report card system with existing inventory_events table
-- Automatically creates vendor interactions from inventory events and links them
-- ================================================================

-- ================================================================
-- ADD VENDOR TRACKING TO INVENTORY_EVENTS
-- ================================================================

-- Add vendor_id column to inventory_events if it doesn't exist
ALTER TABLE inventory_events 
ADD COLUMN IF NOT EXISTS vendor_id UUID REFERENCES vendors(id);

-- Add batch tracking columns for better traceability
ALTER TABLE inventory_events 
ADD COLUMN IF NOT EXISTS batch_number VARCHAR(100),
ADD COLUMN IF NOT EXISTS lot_number VARCHAR(100),
ADD COLUMN IF NOT EXISTS expiry_date DATE,
ADD COLUMN IF NOT EXISTS temperature_at_receipt NUMERIC(5,2),
ADD COLUMN IF NOT EXISTS condition_on_receipt VARCHAR(50) DEFAULT 'good';

-- Create index on vendor_id for performance
CREATE INDEX IF NOT EXISTS idx_inventory_events_vendor_id ON inventory_events(vendor_id);
CREATE INDEX IF NOT EXISTS idx_inventory_events_batch ON inventory_events(batch_number) WHERE batch_number IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_inventory_events_expiry ON inventory_events(expiry_date) WHERE expiry_date IS NOT NULL;

-- ================================================================
-- AUTOMATIC VENDOR INTERACTION CREATION FROM INVENTORY EVENTS
-- ================================================================

CREATE OR REPLACE FUNCTION create_vendor_interaction_from_inventory_event()
RETURNS TRIGGER AS $$
DECLARE
    interaction_id UUID;
    product_info RECORD;
BEGIN
    -- Only process receiving events that have a vendor
    IF NEW.event_type = 'receiving' AND NEW.vendor_id IS NOT NULL THEN
        
        -- Get product information
        SELECT name, category INTO product_info
        FROM products 
        WHERE id = NEW.product_id;
        
        -- Create vendor interaction record
        INSERT INTO vendor_interactions (
            vendor_id,
            inventory_event_id,
            interaction_type,
            interaction_date,
            actual_delivery_date,
            products_delivered,
            delivery_amount,
            delivery_status,
            notes,
            metadata
        ) VALUES (
            NEW.vendor_id,
            NEW.id,
            'delivery',
            NEW.created_at,
            NEW.created_at,
            jsonb_build_array(
                jsonb_build_object(
                    'product_id', NEW.product_id,
                    'product_name', COALESCE(product_info.name, NEW.name),
                    'quantity_delivered', NEW.quantity,
                    'unit_price', NEW.unit_price,
                    'batch_number', NEW.batch_number,
                    'lot_number', NEW.lot_number,
                    'condition', NEW.condition_on_receipt,
                    'temperature', NEW.temperature_at_receipt,
                    'expiry_date', NEW.expiry_date
                )
            ),
            NEW.total_amount,
            'complete', -- Default to complete, can be updated later
            NEW.notes,
            jsonb_build_object(
                'auto_generated', true,
                'source', 'inventory_event',
                'category', NEW.category,
                'original_event_type', NEW.event_type
            )
        ) RETURNING id INTO interaction_id;
        
        -- If temperature or condition indicates issues, create compliance record
        IF NEW.temperature_at_receipt IS NOT NULL OR NEW.condition_on_receipt != 'good' THEN
            INSERT INTO vendor_compliance (
                vendor_id,
                vendor_interaction_id,
                temperature_compliance,
                temperature_at_delivery,
                audit_notes,
                metadata
            ) VALUES (
                NEW.vendor_id,
                interaction_id,
                CASE 
                    WHEN NEW.temperature_at_receipt BETWEEN -2 AND 4 THEN true  -- Safe seafood temp range
                    WHEN NEW.temperature_at_receipt IS NULL THEN NULL
                    ELSE false
                END,
                NEW.temperature_at_receipt,
                CASE 
                    WHEN NEW.condition_on_receipt != 'good' THEN 
                        'Product condition on receipt: ' || NEW.condition_on_receipt
                    WHEN NEW.temperature_at_receipt NOT BETWEEN -2 AND 4 THEN
                        'Temperature out of safe range: ' || NEW.temperature_at_receipt::text || '°C'
                    ELSE NULL
                END,
                jsonb_build_object(
                    'auto_generated', true,
                    'source', 'inventory_event',
                    'condition_noted', NEW.condition_on_receipt
                )
            );
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically create vendor interactions
DROP TRIGGER IF EXISTS create_vendor_interaction_trigger ON inventory_events;
CREATE TRIGGER create_vendor_interaction_trigger
    AFTER INSERT ON inventory_events
    FOR EACH ROW EXECUTE FUNCTION create_vendor_interaction_from_inventory_event();

-- ================================================================
-- VENDOR INTERACTION UPDATE FROM INVENTORY EVENT CHANGES
-- ================================================================

CREATE OR REPLACE FUNCTION update_vendor_interaction_from_inventory_event()
RETURNS TRIGGER AS $$
BEGIN
    -- Only process receiving events that have vendor interactions
    IF OLD.event_type = 'receiving' AND OLD.vendor_id IS NOT NULL THEN
        
        -- Update the linked vendor interaction
        UPDATE vendor_interactions SET
            products_delivered = jsonb_build_array(
                jsonb_build_object(
                    'product_id', NEW.product_id,
                    'product_name', NEW.name,
                    'quantity_delivered', NEW.quantity,
                    'unit_price', NEW.unit_price,
                    'batch_number', NEW.batch_number,
                    'lot_number', NEW.lot_number,
                    'condition', NEW.condition_on_receipt,
                    'temperature', NEW.temperature_at_receipt,
                    'expiry_date', NEW.expiry_date
                )
            ),
            delivery_amount = NEW.total_amount,
            notes = NEW.notes,
            updated_at = CURRENT_TIMESTAMP
        WHERE inventory_event_id = NEW.id;
        
        -- Update compliance record if temperature changed
        IF OLD.temperature_at_receipt != NEW.temperature_at_receipt 
           OR OLD.condition_on_receipt != NEW.condition_on_receipt THEN
            UPDATE vendor_compliance SET
                temperature_compliance = CASE 
                    WHEN NEW.temperature_at_receipt BETWEEN -2 AND 4 THEN true
                    WHEN NEW.temperature_at_receipt IS NULL THEN NULL
                    ELSE false
                END,
                temperature_at_delivery = NEW.temperature_at_receipt,
                audit_notes = CASE 
                    WHEN NEW.condition_on_receipt != 'good' THEN 
                        'Product condition on receipt: ' || NEW.condition_on_receipt
                    WHEN NEW.temperature_at_receipt NOT BETWEEN -2 AND 4 THEN
                        'Temperature out of safe range: ' || NEW.temperature_at_receipt::text || '°C'
                    ELSE 'Product received in good condition'
                END,
                updated_at = CURRENT_TIMESTAMP
            WHERE vendor_interaction_id IN (
                SELECT id FROM vendor_interactions WHERE inventory_event_id = NEW.id
            );
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update vendor interactions when inventory events change
DROP TRIGGER IF EXISTS update_vendor_interaction_trigger ON inventory_events;
CREATE TRIGGER update_vendor_interaction_trigger
    AFTER UPDATE ON inventory_events
    FOR EACH ROW EXECUTE FUNCTION update_vendor_interaction_from_inventory_event();

-- ================================================================
-- BACKFILL FUNCTION FOR EXISTING INVENTORY EVENTS
-- ================================================================

CREATE OR REPLACE FUNCTION backfill_vendor_interactions()
RETURNS TABLE(processed_count INTEGER, error_count INTEGER, details TEXT) AS $$
DECLARE
    event_record RECORD;
    processed INTEGER := 0;
    errors INTEGER := 0;
    error_details TEXT := '';
BEGIN
    -- Process existing inventory events that have vendor_id but no vendor_interaction
    FOR event_record IN 
        SELECT ie.*
        FROM inventory_events ie
        LEFT JOIN vendor_interactions vi ON vi.inventory_event_id = ie.id
        WHERE ie.event_type = 'receiving' 
          AND ie.vendor_id IS NOT NULL
          AND vi.id IS NULL
    LOOP
        BEGIN
            -- Manually trigger the function for each record
            PERFORM create_vendor_interaction_from_inventory_event() 
            FROM (SELECT event_record.*) AS NEW;
            
            processed := processed + 1;
        EXCEPTION WHEN OTHERS THEN
            errors := errors + 1;
            error_details := error_details || 'Error processing event ' || event_record.id || ': ' || SQLERRM || '; ';
        END;
    END LOOP;
    
    processed_count := processed;
    error_count := errors;
    details := CASE 
        WHEN errors = 0 THEN 'All records processed successfully'
        ELSE 'Errors: ' || error_details
    END;
    
    RETURN NEXT;
END;
$$ LANGUAGE plpgsql;

-- ================================================================
-- ENHANCED INVENTORY EVENT VIEWS FOR VENDOR ANALYSIS
-- ================================================================

-- View to join inventory events with vendor performance data
CREATE OR REPLACE VIEW vendor_inventory_performance AS
SELECT 
    ie.id as inventory_event_id,
    ie.event_type,
    ie.created_at as event_date,
    ie.quantity,
    ie.total_amount,
    ie.unit_price,
    ie.batch_number,
    ie.condition_on_receipt,
    ie.temperature_at_receipt,
    
    v.id as vendor_id,
    v.name as vendor_name,
    v.status as vendor_status,
    
    p.name as product_name,
    p.category as product_category,
    
    vi.id as vendor_interaction_id,
    vi.delivery_status,
    vi.is_on_time,
    vi.is_complete_delivery,
    
    vr.overall_satisfaction,
    vr.quality_score,
    
    vc.temperature_compliance,
    vc.traceability_complete,
    vc.haccp_certified
    
FROM inventory_events ie
LEFT JOIN vendors v ON ie.vendor_id = v.id
LEFT JOIN products p ON ie.product_id = p.id
LEFT JOIN vendor_interactions vi ON vi.inventory_event_id = ie.id
LEFT JOIN vendor_ratings vr ON vr.vendor_interaction_id = vi.id
LEFT JOIN vendor_compliance vc ON vc.vendor_interaction_id = vi.id
WHERE ie.event_type = 'receiving';

-- View for vendor dashboard metrics
CREATE OR REPLACE VIEW vendor_dashboard_summary AS
SELECT 
    v.id as vendor_id,
    v.name as vendor_name,
    v.status as vendor_status,
    v.contact_name,
    v.email,
    v.phone,
    
    -- Current month metrics
    vm_current.total_orders as current_month_orders,
    vm_current.completion_rate as current_month_completion_rate,
    vm_current.on_time_rate as current_month_on_time_rate,
    vm_current.avg_overall_satisfaction as current_month_satisfaction,
    vm_current.performance_grade as current_month_grade,
    vm_current.overall_rank as current_rank,
    
    -- All-time metrics
    vm_all.total_orders as total_lifetime_orders,
    vm_all.completion_rate as lifetime_completion_rate,
    vm_all.avg_overall_satisfaction as lifetime_satisfaction,
    vm_all.performance_grade as lifetime_grade,
    
    -- Recent activity
    vm_current.last_interaction_date,
    
    -- Alert counts
    (SELECT COUNT(*) FROM vendor_performance_alerts 
     WHERE vendor_id = v.id AND status = 'open') as open_alerts,
    
    -- Compliance status
    (SELECT COUNT(*) FROM vendor_compliance vc 
     WHERE vc.vendor_id = v.id AND vc.haccp_certified = true) as haccp_compliant_deliveries,
    (SELECT COUNT(*) FROM vendor_compliance vc 
     WHERE vc.vendor_id = v.id AND vc.temperature_compliance = false) as temperature_violations
    
FROM vendors v
LEFT JOIN vendor_metrics vm_current ON (
    vm_current.vendor_id = v.id 
    AND vm_current.period_type = 'monthly'
    AND vm_current.period_end = (SELECT period_end FROM get_period_bounds('monthly', CURRENT_DATE))
)
LEFT JOIN vendor_metrics vm_all ON (
    vm_all.vendor_id = v.id 
    AND vm_all.period_type = 'all_time'
);

-- ================================================================
-- INDEXES FOR PERFORMANCE
-- ================================================================

-- Additional indexes for the new columns
CREATE INDEX IF NOT EXISTS idx_inventory_events_condition ON inventory_events(condition_on_receipt);
CREATE INDEX IF NOT EXISTS idx_inventory_events_temperature ON inventory_events(temperature_at_receipt) 
    WHERE temperature_at_receipt IS NOT NULL;

-- ================================================================
-- COMMENTS
-- ================================================================

COMMENT ON FUNCTION create_vendor_interaction_from_inventory_event IS 'Automatically creates vendor interaction records from receiving inventory events';
COMMENT ON FUNCTION update_vendor_interaction_from_inventory_event IS 'Updates vendor interactions when inventory events are modified';
COMMENT ON FUNCTION backfill_vendor_interactions IS 'Backfill function to process existing inventory events and create missing vendor interactions';
COMMENT ON VIEW vendor_inventory_performance IS 'Comprehensive view joining inventory events with vendor performance data';
COMMENT ON VIEW vendor_dashboard_summary IS 'Summary view for vendor dashboard with key metrics and status information';

-- ================================================================
-- GRANT PERMISSIONS
-- ================================================================

GRANT SELECT ON vendor_inventory_performance TO authenticated;
GRANT SELECT ON vendor_dashboard_summary TO authenticated;