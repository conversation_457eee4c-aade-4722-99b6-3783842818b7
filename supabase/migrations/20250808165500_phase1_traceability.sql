-- Phase 1: FSMA 204 core traceability schema and product extensions
-- Creates: partners, locations, lots, traceability_events, event_lots
-- Extends: "Products"
-- Adds: materialized view fsma_traceability_view

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Partners (suppliers, customers, carriers, facilities)
CREATE TABLE IF NOT EXISTS partners (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  type TEXT NOT NULL CHECK (type IN ('supplier','customer','carrier','facility')),
  name TEXT NOT NULL,
  gln TEXT, -- optional GS1 Global Location Number
  contact JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Locations (tied to partners; can represent docks, warehouses, stores)
CREATE TABLE IF NOT EXISTS locations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  partner_id uuid NOT NULL REFERENCES partners(id) ON DELETE CASCADE,
  gln TEXT, -- optional GS1 GLN at location level
  name TEXT NOT NULL,
  address JSONB DEFAULT '{}'::jsonb,
  role TEXT, -- free text: dock, warehouse, storefront, etc.
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Lots (Traceability Lot Code)
CREATE TABLE IF NOT EXISTS lots (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  tlc TEXT UNIQUE,
  product_id uuid NOT NULL REFERENCES "Products"(id) ON DELETE RESTRICT,
  origin_country TEXT,
  harvest_or_prod_date DATE,
  landing_date DATE,
  initial_qty NUMERIC,
  uom TEXT,
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active','consumed','expired')),
  expiry_date DATE,
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Auto-assign a TLC if not provided
CREATE OR REPLACE FUNCTION ensure_tlc()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.tlc IS NULL OR length(trim(NEW.tlc)) = 0 THEN
    NEW.tlc := 'TLC-' || to_char(now(), 'YYMMDD') || '-' || replace(gen_random_uuid()::text, '-', '')::text;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS lots_assign_tlc ON lots;
CREATE TRIGGER lots_assign_tlc
  BEFORE INSERT ON lots
  FOR EACH ROW
  EXECUTE FUNCTION ensure_tlc();

-- Traceability Events (CTEs)
CREATE TABLE IF NOT EXISTS traceability_events (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  event_type TEXT NOT NULL CHECK (event_type IN ('harvest','landing','shipping','receiving','transformation')),
  event_time TIMESTAMPTZ NOT NULL DEFAULT now(),
  actor_partner_id uuid REFERENCES partners(id) ON DELETE SET NULL,
  actor_location_id uuid REFERENCES locations(id) ON DELETE SET NULL,
  transporter_partner_id uuid REFERENCES partners(id) ON DELETE SET NULL,
  reference_doc TEXT,
  temperature_data JSONB DEFAULT '{}'::jsonb,
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Event ↔ Lots linkage (inputs/outputs per event)
CREATE TABLE IF NOT EXISTS event_lots (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  event_id uuid NOT NULL REFERENCES traceability_events(id) ON DELETE CASCADE,
  lot_id uuid NOT NULL REFERENCES lots(id) ON DELETE RESTRICT,
  role TEXT NOT NULL CHECK (role IN ('input','output')),
  qty NUMERIC CHECK (qty IS NULL OR qty >= 0),
  uom TEXT,
  UNIQUE(event_id, lot_id, role)
);

-- Update updated_at triggers
CREATE OR REPLACE FUNCTION set_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at := now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS partners_set_updated_at ON partners;
CREATE TRIGGER partners_set_updated_at BEFORE UPDATE ON partners FOR EACH ROW EXECUTE FUNCTION set_updated_at();

DROP TRIGGER IF EXISTS locations_set_updated_at ON locations;
CREATE TRIGGER locations_set_updated_at BEFORE UPDATE ON locations FOR EACH ROW EXECUTE FUNCTION set_updated_at();

DROP TRIGGER IF EXISTS lots_set_updated_at ON lots;
CREATE TRIGGER lots_set_updated_at BEFORE UPDATE ON lots FOR EACH ROW EXECUTE FUNCTION set_updated_at();

DROP TRIGGER IF EXISTS traceability_events_set_updated_at ON traceability_events;
CREATE TRIGGER traceability_events_set_updated_at BEFORE UPDATE ON traceability_events FOR EACH ROW EXECUTE FUNCTION set_updated_at();

-- Indexes
CREATE INDEX IF NOT EXISTS idx_traceability_events_type ON traceability_events(event_type);
CREATE INDEX IF NOT EXISTS idx_event_lots_event ON event_lots(event_id);
CREATE INDEX IF NOT EXISTS idx_event_lots_lot ON event_lots(lot_id);
CREATE INDEX IF NOT EXISTS idx_lots_product ON lots(product_id);
CREATE INDEX IF NOT EXISTS idx_partners_type ON partners(type);

-- Extend Products with seafood & storage attributes
ALTER TABLE "Products" ADD COLUMN IF NOT EXISTS gtin TEXT;
ALTER TABLE "Products" ADD COLUMN IF NOT EXISTS scientific_name TEXT;
ALTER TABLE "Products" ADD COLUMN IF NOT EXISTS market_name TEXT;
ALTER TABLE "Products" ADD COLUMN IF NOT EXISTS catch_method TEXT; -- e.g., hook & line, trawl
ALTER TABLE "Products" ADD COLUMN IF NOT EXISTS fao_area TEXT;     -- FAO area/region code
ALTER TABLE "Products" ADD COLUMN IF NOT EXISTS origin_country TEXT;
ALTER TABLE "Products" ADD COLUMN IF NOT EXISTS storage_temp_min NUMERIC;
ALTER TABLE "Products" ADD COLUMN IF NOT EXISTS storage_temp_max NUMERIC;

-- Flattened view for FDA 24-hour request
DROP MATERIALIZED VIEW IF EXISTS fsma_traceability_view;
CREATE MATERIALIZED VIEW fsma_traceability_view AS
SELECT 
  e.id              AS event_id,
  e.event_type,
  e.event_time,
  e.reference_doc,
  e.notes,
  p.id              AS product_id,
  p.name            AS product_name,
  l.id              AS lot_id,
  l.tlc,
  el.role,
  el.qty,
  el.uom,
  e.actor_partner_id,
  e.actor_location_id,
  e.transporter_partner_id
FROM traceability_events e
JOIN event_lots el ON el.event_id = e.id
JOIN lots l ON l.id = el.lot_id
JOIN "Products" p ON p.id = l.product_id;

-- Helpful comments
COMMENT ON TABLE partners IS 'Business entities: suppliers, customers, carriers, facilities (FSMA partner master)';
COMMENT ON TABLE locations IS 'Physical locations tied to partners; may have GLNs';
COMMENT ON TABLE lots IS 'Traceability Lots with TLC and product linkage';
COMMENT ON TABLE traceability_events IS 'FSMA CTEs: receiving, shipping, transformation, etc.';
COMMENT ON TABLE event_lots IS 'Inputs/outputs per traceability event, with quantities and UoM';
