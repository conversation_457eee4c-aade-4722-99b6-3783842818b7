-- Add occurred_at to inventory_events and update calendar sync to use it
BEGIN;

-- 1) Add occurred_at column (when not present)
ALTER TABLE inventory_events
  ADD COLUMN IF NOT EXISTS occurred_at timestamptz;

-- 2) Backfill occurred_at from created_at for existing rows
UPDATE inventory_events
SET occurred_at = created_at
WHERE occurred_at IS NULL;

-- 3) Helpful index for time-range queries
CREATE INDEX IF NOT EXISTS idx_inventory_events_occurred_at
  ON inventory_events(occurred_at);

-- 4) Update calendar sync function to use occurred_at as the event start
CREATE OR REPLACE FUNCTION sync_calendar_from_inventory_events()
RETURNS TRIGGER AS $$
DECLARE
  v_title text;
  v_desc text;
  v_start timestamptz;
BEGIN
  v_start := COALESCE(NEW.occurred_at, NEW.created_at, now());

  IF (TG_OP = 'INSERT') THEN
    v_title := coalesce(NEW.event_type, 'event');
    v_desc := NULLIF(NEW.notes, '');
    INSERT INTO calendar_events (
      title, description, start_at, end_at, all_day,
      source, source_id, inventory_event_id,
      event_type, product_id, metadata
    ) VALUES (
      v_title, v_desc, v_start, NULL, false,
      'inventory_events', NEW.id, NEW.id,
      NEW.event_type, NEW.product_id,
      coalesce(NEW.metadata, '{}'::jsonb) || jsonb_build_object(
        'quantity', NEW.quantity,
        'unit_price', NEW.unit_price,
        'total_amount', NEW.total_amount,
        'images', coalesce(NEW.images, ARRAY[]::text[])
      )
    );
    RETURN NEW;
  ELSIF (TG_OP = 'UPDATE') THEN
    v_title := coalesce(NEW.event_type, 'event');
    v_desc := NULLIF(NEW.notes, '');
    UPDATE calendar_events SET
      title = v_title,
      description = v_desc,
      start_at = v_start,
      end_at = NULL,
      all_day = false,
      event_type = NEW.event_type,
      product_id = NEW.product_id,
      metadata = coalesce(NEW.metadata, '{}'::jsonb) || jsonb_build_object(
        'quantity', NEW.quantity,
        'unit_price', NEW.unit_price,
        'total_amount', NEW.total_amount,
        'images', coalesce(NEW.images, ARRAY[]::text[])
      ),
      updated_at = now()
    WHERE inventory_event_id = NEW.id;
    RETURN NEW;
  ELSIF (TG_OP = 'DELETE') THEN
    DELETE FROM calendar_events WHERE inventory_event_id = OLD.id;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

COMMIT;
