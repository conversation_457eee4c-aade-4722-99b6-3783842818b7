-- Create product categories table
CREATE TABLE IF NOT EXISTS product_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add default product categories
INSERT INTO product_categories (name, description) 
VALUES 
    ('Finfish', 'Fish with fins including salmon, tuna, cod, etc.'),
    ('Shellfish', 'Mollusks including oysters, clams, mussels, etc.'),
    ('Crustaceans', 'Shell-bearing seafood including lobster, crab, shrimp, etc.'),
    ('Specialty', 'Specialty items including caviar, roe, sea urchin, etc.')
ON CONFLICT (name) DO NOTHING;

-- Add foreign key to products table
ALTER TABLE "Products" 
    ADD COLUMN IF NOT EXISTS product_category_id UUID REFERENCES product_categories(id),
    ADD COLUMN IF NOT EXISTS old_category TEXT;

-- Copy existing categories to old_category
UPDATE "Products" 
SET old_category = category 
WHERE category IS NOT NULL;

-- Update product_category_id based on existing categories
UPDATE "Products" 
SET product_category_id = (
    SELECT id FROM product_categories 
    WHERE 
        CASE 
            WHEN LOWER("Products".category) LIKE '%salmon%' THEN name = 'Finfish'
            WHEN LOWER("Products".category) = 'shellfish' THEN name = 'Shellfish'
            WHEN LOWER("Products".category) = 'whitefish' THEN name = 'Finfish'
            ELSE name = 'Specialty'
        END
    LIMIT 1
)
WHERE category IS NOT NULL;

-- Create trigger to update updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_product_categories_updated_at
    BEFORE UPDATE ON product_categories
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add RLS policies
ALTER TABLE product_categories ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Enable read access for all users" ON product_categories
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Enable insert for authenticated users only" ON product_categories
    FOR INSERT
    TO authenticated
    WITH CHECK (true);

CREATE POLICY "Enable update for authenticated users only" ON product_categories
    FOR UPDATE
    TO authenticated
    USING (true)
    WITH CHECK (true);
