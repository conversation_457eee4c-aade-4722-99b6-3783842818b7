-- Vendor Report Card System - Core Schema Migration
-- Creates comprehensive vendor performance tracking and reporting system

-- 1. Vendor Interactions Table
-- Tracks every delivery, order, and interaction with vendors
CREATE TABLE vendor_interactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    vendor_id UUID NOT NULL REFERENCES vendors(id) ON DELETE CASCADE,
    interaction_type VARCHAR(50) NOT NULL CHECK (interaction_type IN ('delivery', 'order', 'quality_check', 'issue_resolution', 'communication')),
    
    -- Delivery/Order Details
    po_number VARCHAR(100), -- Purchase order number
    expected_delivery_date TIMESTAMPTZ,
    actual_delivery_date TIMESTAMPTZ,
    order_total_amount DECIMAL(10,2),
    delivered_amount DECIMAL(10,2),
    
    -- Status and Completion
    status VARCHAR(30) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'cancelled', 'partial', 'delayed', 'failed')),
    completion_rate DECIMAL(5,2) GENERATED ALWAYS AS (
        CASE 
            WHEN order_total_amount IS NULL OR order_total_amount = 0 THEN 0
            ELSE ROUND((delivered_amount / order_total_amount) * 100, 2)
        END
    ) STORED,
    
    -- Performance Indicators
    on_time_delivery BOOLEAN GENERATED ALWAYS AS (
        CASE 
            WHEN actual_delivery_date IS NULL OR expected_delivery_date IS NULL THEN NULL
            ELSE actual_delivery_date <= expected_delivery_date
        END
    ) STORED,
    
    -- Compliance and Quality
    temperature_compliant BOOLEAN DEFAULT NULL,
    documentation_complete BOOLEAN DEFAULT NULL,
    product_condition VARCHAR(20) CHECK (product_condition IN ('Excellent', 'Good', 'Fair', 'Poor', 'Damaged')),
    
    -- Additional Context
    notes TEXT,
    issue_reported BOOLEAN DEFAULT FALSE,
    issue_resolved BOOLEAN DEFAULT NULL,
    issue_resolution_time_hours INTEGER,
    
    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    
    -- Indexes for performance
    INDEX idx_vendor_interactions_vendor_id (vendor_id),
    INDEX idx_vendor_interactions_type_status (interaction_type, status),
    INDEX idx_vendor_interactions_delivery_date (actual_delivery_date),
    INDEX idx_vendor_interactions_created_at (created_at)
);

-- 2. Vendor Ratings Table  
-- Manager assessments and ratings for vendor interactions
CREATE TABLE vendor_ratings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    vendor_interaction_id UUID NOT NULL REFERENCES vendor_interactions(id) ON DELETE CASCADE,
    vendor_id UUID NOT NULL REFERENCES vendors(id) ON DELETE CASCADE,
    
    -- Rating Categories (1-10 scale)
    quality_score INTEGER CHECK (quality_score BETWEEN 1 AND 10),
    delivery_timeliness_score INTEGER CHECK (delivery_timeliness_score BETWEEN 1 AND 10),
    communication_score INTEGER CHECK (communication_score BETWEEN 1 AND 10),
    price_competitiveness_score INTEGER CHECK (price_competitiveness_score BETWEEN 1 AND 10),
    issue_resolution_score INTEGER CHECK (issue_resolution_score BETWEEN 1 AND 10),
    overall_satisfaction INTEGER CHECK (overall_satisfaction BETWEEN 1 AND 10),
    
    -- Calculated weighted average (A-F grade)
    composite_score DECIMAL(3,1) GENERATED ALWAYS AS (
        ROUND((
            COALESCE(quality_score, 0) * 0.30 +
            COALESCE(delivery_timeliness_score, 0) * 0.25 +
            COALESCE(communication_score, 0) * 0.15 +
            COALESCE(price_competitiveness_score, 0) * 0.15 +
            COALESCE(issue_resolution_score, 0) * 0.10 +
            COALESCE(overall_satisfaction, 0) * 0.05
        ), 1)
    ) STORED,
    
    -- Letter Grade
    letter_grade CHAR(1) GENERATED ALWAYS AS (
        CASE 
            WHEN (
                COALESCE(quality_score, 0) * 0.30 +
                COALESCE(delivery_timeliness_score, 0) * 0.25 +
                COALESCE(communication_score, 0) * 0.15 +
                COALESCE(price_competitiveness_score, 0) * 0.15 +
                COALESCE(issue_resolution_score, 0) * 0.10 +
                COALESCE(overall_satisfaction, 0) * 0.05
            ) >= 9 THEN 'A'
            WHEN (
                COALESCE(quality_score, 0) * 0.30 +
                COALESCE(delivery_timeliness_score, 0) * 0.25 +
                COALESCE(communication_score, 0) * 0.15 +
                COALESCE(price_competitiveness_score, 0) * 0.15 +
                COALESCE(issue_resolution_score, 0) * 0.10 +
                COALESCE(overall_satisfaction, 0) * 0.05
            ) >= 8 THEN 'B'
            WHEN (
                COALESCE(quality_score, 0) * 0.30 +
                COALESCE(delivery_timeliness_score, 0) * 0.25 +
                COALESCE(communication_score, 0) * 0.15 +
                COALESCE(price_competitiveness_score, 0) * 0.15 +
                COALESCE(issue_resolution_score, 0) * 0.10 +
                COALESCE(overall_satisfaction, 0) * 0.05
            ) >= 7 THEN 'C'
            WHEN (
                COALESCE(quality_score, 0) * 0.30 +
                COALESCE(delivery_timeliness_score, 0) * 0.25 +
                COALESCE(communication_score, 0) * 0.15 +
                COALESCE(price_competitiveness_score, 0) * 0.15 +
                COALESCE(issue_resolution_score, 0) * 0.10 +
                COALESCE(overall_satisfaction, 0) * 0.05
            ) >= 6 THEN 'D'
            ELSE 'F'
        END
    ) STORED,
    
    -- Comments and Feedback
    strengths TEXT,
    areas_for_improvement TEXT,
    manager_comments TEXT,
    would_recommend BOOLEAN,
    
    -- Metadata
    rating_date TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID NOT NULL REFERENCES auth.users(id),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints and Indexes
    UNIQUE(vendor_interaction_id), -- One rating per interaction
    INDEX idx_vendor_ratings_vendor_id (vendor_id),
    INDEX idx_vendor_ratings_composite_score (composite_score),
    INDEX idx_vendor_ratings_letter_grade (letter_grade),
    INDEX idx_vendor_ratings_date (rating_date)
);

-- 3. Vendor Metrics Table
-- Calculated performance metrics and KPIs for each vendor
CREATE TABLE vendor_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    vendor_id UUID NOT NULL REFERENCES vendors(id) ON DELETE CASCADE,
    
    -- Time Periods
    calculation_period VARCHAR(20) NOT NULL CHECK (calculation_period IN ('current_month', 'last_30_days', 'current_quarter', 'last_90_days', 'current_year', 'all_time')),
    period_start_date DATE NOT NULL,
    period_end_date DATE NOT NULL,
    
    -- Delivery Performance Metrics
    total_interactions INTEGER DEFAULT 0,
    completed_deliveries INTEGER DEFAULT 0,
    on_time_deliveries INTEGER DEFAULT 0,
    late_deliveries INTEGER DEFAULT 0,
    failed_deliveries INTEGER DEFAULT 0,
    
    -- Calculated Rates (as percentages)
    completion_rate DECIMAL(5,2) GENERATED ALWAYS AS (
        CASE 
            WHEN total_interactions = 0 THEN 0
            ELSE ROUND((completed_deliveries::DECIMAL / total_interactions) * 100, 2)
        END
    ) STORED,
    
    on_time_delivery_rate DECIMAL(5,2) GENERATED ALWAYS AS (
        CASE 
            WHEN completed_deliveries = 0 THEN 0
            ELSE ROUND((on_time_deliveries::DECIMAL / completed_deliveries) * 100, 2)
        END
    ) STORED,
    
    -- Financial Metrics
    total_order_value DECIMAL(12,2) DEFAULT 0,
    total_delivered_value DECIMAL(12,2) DEFAULT 0,
    average_order_value DECIMAL(10,2) GENERATED ALWAYS AS (
        CASE 
            WHEN total_interactions = 0 THEN 0
            ELSE ROUND(total_order_value / total_interactions, 2)
        END
    ) STORED,
    
    -- Quality Metrics
    total_ratings INTEGER DEFAULT 0,
    average_quality_score DECIMAL(3,1) DEFAULT 0,
    average_delivery_score DECIMAL(3,1) DEFAULT 0,
    average_communication_score DECIMAL(3,1) DEFAULT 0,
    average_overall_satisfaction DECIMAL(3,1) DEFAULT 0,
    overall_letter_grade CHAR(1),
    
    -- Issue Tracking
    total_issues_reported INTEGER DEFAULT 0,
    issues_resolved INTEGER DEFAULT 0,
    average_resolution_time_hours DECIMAL(6,1) DEFAULT 0,
    issue_resolution_rate DECIMAL(5,2) GENERATED ALWAYS AS (
        CASE 
            WHEN total_issues_reported = 0 THEN 100
            ELSE ROUND((issues_resolved::DECIMAL / total_issues_reported) * 100, 2)
        END
    ) STORED,
    
    -- Compliance Metrics
    temperature_compliance_rate DECIMAL(5,2) DEFAULT 0,
    documentation_completeness_rate DECIMAL(5,2) DEFAULT 0,
    
    -- Ranking (within all vendors for this period)
    overall_rank INTEGER,
    total_vendors_in_period INTEGER,
    
    -- Metadata
    calculated_at TIMESTAMPTZ DEFAULT NOW(),
    last_updated TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints and Indexes
    UNIQUE(vendor_id, calculation_period), -- One record per vendor per period
    INDEX idx_vendor_metrics_vendor_period (vendor_id, calculation_period),
    INDEX idx_vendor_metrics_completion_rate (completion_rate),
    INDEX idx_vendor_metrics_overall_grade (overall_letter_grade),
    INDEX idx_vendor_metrics_rank (overall_rank)
);

-- 4. Vendor Compliance Tracking Table
-- HACCP, GDST, and food safety compliance monitoring
CREATE TABLE vendor_compliance (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    vendor_id UUID NOT NULL REFERENCES vendors(id) ON DELETE CASCADE,
    vendor_interaction_id UUID REFERENCES vendor_interactions(id) ON DELETE SET NULL,
    
    -- Compliance Categories
    compliance_type VARCHAR(50) NOT NULL CHECK (compliance_type IN ('haccp', 'gdst_traceability', 'temperature_control', 'documentation', 'certification', 'audit')),
    
    -- HACCP Compliance
    haccp_certified BOOLEAN DEFAULT NULL,
    haccp_cert_expiry_date DATE,
    critical_control_points_met BOOLEAN DEFAULT NULL,
    temperature_logs_complete BOOLEAN DEFAULT NULL,
    corrective_actions_documented BOOLEAN DEFAULT NULL,
    
    -- GDST Traceability
    catch_certificate_provided BOOLEAN DEFAULT NULL,
    chain_of_custody_complete BOOLEAN DEFAULT NULL,
    species_verification_done BOOLEAN DEFAULT NULL,
    origin_documentation_complete BOOLEAN DEFAULT NULL,
    processing_facility_certified BOOLEAN DEFAULT NULL,
    
    -- Temperature Control
    temp_monitoring_device_used BOOLEAN DEFAULT NULL,
    temp_within_safe_range BOOLEAN DEFAULT NULL,
    cold_chain_maintained BOOLEAN DEFAULT NULL,
    temp_deviation_reported BOOLEAN DEFAULT NULL,
    temp_log_provided BOOLEAN DEFAULT NULL,
    
    -- Compliance Score (1-100)
    compliance_score INTEGER CHECK (compliance_score BETWEEN 0 AND 100),
    compliance_status VARCHAR(20) GENERATED ALWAYS AS (
        CASE 
            WHEN compliance_score >= 90 THEN 'Excellent'
            WHEN compliance_score >= 80 THEN 'Good'
            WHEN compliance_score >= 70 THEN 'Satisfactory'
            WHEN compliance_score >= 60 THEN 'Needs Improvement'
            ELSE 'Non-Compliant'
        END
    ) STORED,
    
    -- Issues and Actions
    compliance_issues TEXT,
    corrective_actions_required TEXT,
    corrective_actions_taken TEXT,
    next_review_date DATE,
    
    -- Audit Information
    auditor_name VARCHAR(100),
    audit_date DATE,
    audit_type VARCHAR(50),
    audit_result VARCHAR(50),
    
    -- Metadata
    assessed_date TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID NOT NULL REFERENCES auth.users(id),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Indexes
    INDEX idx_vendor_compliance_vendor_id (vendor_id),
    INDEX idx_vendor_compliance_type_status (compliance_type, compliance_status),
    INDEX idx_vendor_compliance_score (compliance_score),
    INDEX idx_vendor_compliance_cert_expiry (haccp_cert_expiry_date)
);

-- 5. Vendor Performance Alerts Table
-- Automated alerts for performance issues and compliance problems
CREATE TABLE vendor_performance_alerts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    vendor_id UUID NOT NULL REFERENCES vendors(id) ON DELETE CASCADE,
    
    -- Alert Details
    alert_type VARCHAR(50) NOT NULL CHECK (alert_type IN ('performance_decline', 'late_delivery', 'quality_issue', 'compliance_violation', 'certification_expiry', 'cost_increase')),
    severity VARCHAR(10) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    
    -- Alert Triggers
    triggered_by_metric VARCHAR(100), -- Which metric triggered this alert
    threshold_value DECIMAL(10,2), -- The threshold that was crossed
    actual_value DECIMAL(10,2), -- The actual value that triggered the alert
    
    -- Alert Status
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'acknowledged', 'resolved', 'dismissed')),
    acknowledged_at TIMESTAMPTZ,
    acknowledged_by UUID REFERENCES auth.users(id),
    resolved_at TIMESTAMPTZ,
    resolved_by UUID REFERENCES auth.users(id),
    resolution_notes TEXT,
    
    -- Alert Rules
    auto_generated BOOLEAN DEFAULT TRUE,
    recurring BOOLEAN DEFAULT FALSE,
    next_check_date TIMESTAMPTZ,
    
    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Indexes
    INDEX idx_vendor_alerts_vendor_id (vendor_id),
    INDEX idx_vendor_alerts_type_severity (alert_type, severity),
    INDEX idx_vendor_alerts_status (status),
    INDEX idx_vendor_alerts_created_at (created_at)
);

-- Add updated_at trigger for all tables
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers
CREATE TRIGGER update_vendor_interactions_updated_at BEFORE UPDATE ON vendor_interactions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_vendor_ratings_updated_at BEFORE UPDATE ON vendor_ratings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_vendor_metrics_updated_at BEFORE UPDATE ON vendor_metrics FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_vendor_compliance_updated_at BEFORE UPDATE ON vendor_compliance FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_vendor_performance_alerts_updated_at BEFORE UPDATE ON vendor_performance_alerts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create materialized view for vendor dashboard summary
CREATE MATERIALIZED VIEW vendor_dashboard_summary AS
SELECT 
    v.id as vendor_id,
    v.name as vendor_name,
    v.contact_person,
    v.email,
    v.phone,
    
    -- Current period metrics (last 30 days)
    vm.completion_rate,
    vm.on_time_delivery_rate,
    vm.average_quality_score,
    vm.overall_letter_grade,
    vm.total_interactions,
    vm.overall_rank,
    
    -- Alert counts
    COALESCE(active_alerts.count, 0) as active_alerts_count,
    COALESCE(critical_alerts.count, 0) as critical_alerts_count,
    
    -- Last interaction
    li.actual_delivery_date as last_delivery_date,
    li.status as last_delivery_status,
    
    -- Compliance status
    vc.compliance_score as latest_compliance_score,
    vc.compliance_status as compliance_status,
    vc.haccp_cert_expiry_date,
    
    -- Calculated at
    vm.calculated_at as metrics_updated_at

FROM vendors v
LEFT JOIN vendor_metrics vm ON (v.id = vm.vendor_id AND vm.calculation_period = 'last_30_days')
LEFT JOIN (
    SELECT vendor_id, COUNT(*) as count 
    FROM vendor_performance_alerts 
    WHERE status = 'active' 
    GROUP BY vendor_id
) active_alerts ON v.id = active_alerts.vendor_id
LEFT JOIN (
    SELECT vendor_id, COUNT(*) as count 
    FROM vendor_performance_alerts 
    WHERE status = 'active' AND severity = 'critical'
    GROUP BY vendor_id
) critical_alerts ON v.id = critical_alerts.vendor_id
LEFT JOIN (
    SELECT DISTINCT ON (vendor_id) 
        vendor_id, actual_delivery_date, status
    FROM vendor_interactions 
    WHERE status = 'completed'
    ORDER BY vendor_id, actual_delivery_date DESC
) li ON v.id = li.vendor_id
LEFT JOIN (
    SELECT DISTINCT ON (vendor_id)
        vendor_id, compliance_score, compliance_status, haccp_cert_expiry_date
    FROM vendor_compliance
    ORDER BY vendor_id, assessed_date DESC
) vc ON v.id = vc.vendor_id

ORDER BY 
    CASE 
        WHEN vm.overall_letter_grade IS NOT NULL THEN vm.overall_letter_grade
        ELSE 'Z'
    END,
    vm.completion_rate DESC NULLS LAST,
    v.name;

-- Create index on materialized view
CREATE UNIQUE INDEX idx_vendor_dashboard_summary_vendor_id ON vendor_dashboard_summary (vendor_id);

-- Grant appropriate permissions
GRANT SELECT ON vendor_dashboard_summary TO authenticated;
GRANT ALL ON vendor_interactions TO authenticated;
GRANT ALL ON vendor_ratings TO authenticated;
GRANT ALL ON vendor_metrics TO authenticated;
GRANT ALL ON vendor_compliance TO authenticated;
GRANT ALL ON vendor_performance_alerts TO authenticated;