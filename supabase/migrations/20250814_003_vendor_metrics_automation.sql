-- ================================================================
-- VENDOR REPORT CARD SYSTEM - AUTOMATED METRICS CALCULATION
-- ================================================================
-- Created: 2025-08-14
-- Purpose: Implement automated vendor metrics calculation and performance tracking
-- Updates vendor_metrics table when interactions, ratings, or inventory events change
-- ================================================================

-- ================================================================
-- HELPER FUNCTIONS FOR METRIC CALCULATIONS
-- ================================================================

-- Function to calculate performance grade based on scores
CREATE OR REPLACE FUNCTION calculate_performance_grade(
    completion_rate NUMERIC,
    on_time_rate NUMERIC,
    avg_quality NUMERIC,
    avg_overall NUMERIC
) RETURNS CHAR(1) AS $$
BEGIN
    -- Calculate weighted score (completion: 30%, on_time: 25%, quality: 25%, overall: 20%)
    DECLARE
        weighted_score NUMERIC;
    BEGIN
        weighted_score := 
            COALESCE(completion_rate, 0) * 0.30 +
            COALESCE(on_time_rate, 0) * 0.25 +
            COALESCE(avg_quality * 10, 0) * 0.25 +  -- Convert 1-10 to percentage
            COALESCE(avg_overall * 10, 0) * 0.20;
        
        CASE 
            WHEN weighted_score >= 90 THEN RETURN 'A';
            WHEN weighted_score >= 80 THEN RETURN 'B';
            WHEN weighted_score >= 70 THEN RETURN 'C';
            WHEN weighted_score >= 60 THEN RETURN 'D';
            ELSE RETURN 'F';
        END CASE;
    END;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to get date range for period type
CREATE OR REPLACE FUNCTION get_period_bounds(
    period_type VARCHAR,
    reference_date DATE DEFAULT CURRENT_DATE
) RETURNS TABLE(period_start DATE, period_end DATE) AS $$
BEGIN
    CASE period_type
        WHEN 'daily' THEN
            RETURN QUERY SELECT reference_date, reference_date;
        WHEN 'weekly' THEN
            RETURN QUERY SELECT 
                (reference_date - INTERVAL '1 week' * EXTRACT(DOW FROM reference_date)::int)::DATE,
                (reference_date - INTERVAL '1 week' * EXTRACT(DOW FROM reference_date)::int + INTERVAL '6 days')::DATE;
        WHEN 'monthly' THEN
            RETURN QUERY SELECT 
                DATE_TRUNC('month', reference_date)::DATE,
                (DATE_TRUNC('month', reference_date) + INTERVAL '1 month - 1 day')::DATE;
        WHEN 'quarterly' THEN
            RETURN QUERY SELECT 
                DATE_TRUNC('quarter', reference_date)::DATE,
                (DATE_TRUNC('quarter', reference_date) + INTERVAL '3 months - 1 day')::DATE;
        WHEN 'yearly' THEN
            RETURN QUERY SELECT 
                DATE_TRUNC('year', reference_date)::DATE,
                (DATE_TRUNC('year', reference_date) + INTERVAL '1 year - 1 day')::DATE;
        WHEN 'all_time' THEN
            RETURN QUERY SELECT 
                '2020-01-01'::DATE,  -- Reasonable start date for all-time metrics
                CURRENT_DATE;
        ELSE
            RAISE EXCEPTION 'Invalid period_type: %', period_type;
    END CASE;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- ================================================================
-- MAIN METRIC CALCULATION FUNCTION
-- ================================================================

CREATE OR REPLACE FUNCTION calculate_vendor_metrics(
    input_vendor_id UUID,
    input_period_type VARCHAR DEFAULT 'monthly',
    input_reference_date DATE DEFAULT CURRENT_DATE
) RETURNS VOID AS $$
DECLARE
    period_bounds RECORD;
    delivery_stats RECORD;
    rating_stats RECORD;
    financial_stats RECORD;
    perf_grade CHAR(1);
BEGIN
    -- Get period bounds
    SELECT * INTO period_bounds FROM get_period_bounds(input_period_type, input_reference_date);
    
    -- Calculate delivery performance statistics
    SELECT 
        COUNT(*) as total_orders,
        COUNT(*) FILTER (WHERE delivery_status = 'complete') as completed_orders,
        COUNT(*) FILTER (WHERE delivery_status = 'partial') as partial_orders,
        COUNT(*) FILTER (WHERE delivery_status = 'rejected') as rejected_orders,
        COUNT(*) FILTER (WHERE is_on_time = true) as on_time_deliveries,
        COUNT(*) FILTER (WHERE is_on_time = false) as late_deliveries,
        MAX(interaction_date) as last_interaction
    INTO delivery_stats
    FROM vendor_interactions vi
    WHERE vi.vendor_id = input_vendor_id
      AND vi.interaction_date::DATE BETWEEN period_bounds.period_start AND period_bounds.period_end
      AND vi.interaction_type = 'delivery';
    
    -- Calculate rating statistics
    SELECT 
        AVG(quality_score) as avg_quality,
        AVG(delivery_timeliness_score) as avg_delivery,
        AVG(order_accuracy_score) as avg_accuracy,
        AVG(communication_score) as avg_communication,
        AVG(price_competitiveness_score) as avg_pricing,
        AVG(issue_resolution_score) as avg_resolution,
        AVG(overall_satisfaction) as avg_overall
    INTO rating_stats
    FROM vendor_ratings vr
    JOIN vendor_interactions vi ON vr.vendor_interaction_id = vi.id
    WHERE vi.vendor_id = input_vendor_id
      AND vr.rating_date::DATE BETWEEN period_bounds.period_start AND period_bounds.period_end;
    
    -- Calculate financial statistics
    SELECT 
        COALESCE(SUM(order_amount), 0) as total_order_value,
        COALESCE(SUM(delivery_amount), 0) as total_delivered_value,
        COALESCE(AVG(order_amount), 0) as avg_order_value,
        COALESCE(SUM(discrepancy_amount), 0) as total_discrepancy_value
    INTO financial_stats
    FROM vendor_interactions vi
    WHERE vi.vendor_id = input_vendor_id
      AND vi.interaction_date::DATE BETWEEN period_bounds.period_start AND period_bounds.period_end;
    
    -- Calculate performance grade
    perf_grade := calculate_performance_grade(
        CASE WHEN delivery_stats.total_orders > 0 THEN 
            (delivery_stats.completed_orders::numeric / delivery_stats.total_orders::numeric) * 100
        ELSE NULL END,
        CASE WHEN (delivery_stats.completed_orders + delivery_stats.partial_orders) > 0 THEN 
            (delivery_stats.on_time_deliveries::numeric / (delivery_stats.completed_orders + delivery_stats.partial_orders)::numeric) * 100
        ELSE NULL END,
        rating_stats.avg_quality,
        rating_stats.avg_overall
    );
    
    -- Insert or update metrics record
    INSERT INTO vendor_metrics (
        vendor_id, period_type, period_start, period_end,
        total_orders, completed_orders, partial_orders, rejected_orders,
        on_time_deliveries, late_deliveries,
        total_order_value, total_delivered_value, average_order_value, total_discrepancy_value,
        avg_quality_score, avg_delivery_score, avg_accuracy_score,
        avg_communication_score, avg_pricing_score, avg_resolution_score, avg_overall_satisfaction,
        performance_grade, last_interaction_date, calculated_at
    ) VALUES (
        input_vendor_id, input_period_type, period_bounds.period_start, period_bounds.period_end,
        COALESCE(delivery_stats.total_orders, 0),
        COALESCE(delivery_stats.completed_orders, 0),
        COALESCE(delivery_stats.partial_orders, 0),
        COALESCE(delivery_stats.rejected_orders, 0),
        COALESCE(delivery_stats.on_time_deliveries, 0),
        COALESCE(delivery_stats.late_deliveries, 0),
        financial_stats.total_order_value,
        financial_stats.total_delivered_value,
        financial_stats.avg_order_value,
        financial_stats.total_discrepancy_value,
        rating_stats.avg_quality,
        rating_stats.avg_delivery,
        rating_stats.avg_accuracy,
        rating_stats.avg_communication,
        rating_stats.avg_pricing,
        rating_stats.avg_resolution,
        rating_stats.avg_overall,
        perf_grade,
        delivery_stats.last_interaction,
        CURRENT_TIMESTAMP
    ) ON CONFLICT (vendor_id, period_type, period_start, period_end)
    DO UPDATE SET
        total_orders = EXCLUDED.total_orders,
        completed_orders = EXCLUDED.completed_orders,
        partial_orders = EXCLUDED.partial_orders,
        rejected_orders = EXCLUDED.rejected_orders,
        on_time_deliveries = EXCLUDED.on_time_deliveries,
        late_deliveries = EXCLUDED.late_deliveries,
        total_order_value = EXCLUDED.total_order_value,
        total_delivered_value = EXCLUDED.total_delivered_value,
        average_order_value = EXCLUDED.average_order_value,
        total_discrepancy_value = EXCLUDED.total_discrepancy_value,
        avg_quality_score = EXCLUDED.avg_quality_score,
        avg_delivery_score = EXCLUDED.avg_delivery_score,
        avg_accuracy_score = EXCLUDED.avg_accuracy_score,
        avg_communication_score = EXCLUDED.avg_communication_score,
        avg_pricing_score = EXCLUDED.avg_pricing_score,
        avg_resolution_score = EXCLUDED.avg_resolution_score,
        avg_overall_satisfaction = EXCLUDED.avg_overall_satisfaction,
        performance_grade = EXCLUDED.performance_grade,
        last_interaction_date = EXCLUDED.last_interaction_date,
        calculated_at = EXCLUDED.calculated_at,
        updated_at = CURRENT_TIMESTAMP;
        
END;
$$ LANGUAGE plpgsql;

-- ================================================================
-- VENDOR RANKING CALCULATION FUNCTION
-- ================================================================

CREATE OR REPLACE FUNCTION update_vendor_rankings(
    input_period_type VARCHAR DEFAULT 'monthly'
) RETURNS VOID AS $$
DECLARE
    vendor_record RECORD;
    rank_counter INTEGER := 1;
BEGIN
    -- Update overall rankings based on performance grade and completion rate
    FOR vendor_record IN
        SELECT vendor_id, 
               ROW_NUMBER() OVER (
                   ORDER BY 
                       performance_grade ASC NULLS LAST,
                       completion_rate DESC NULLS LAST,
                       on_time_rate DESC NULLS LAST,
                       avg_overall_satisfaction DESC NULLS LAST
               ) as new_rank
        FROM vendor_metrics
        WHERE period_type = input_period_type
          AND period_end = (
              SELECT period_end FROM get_period_bounds(input_period_type, CURRENT_DATE)
          )
    LOOP
        UPDATE vendor_metrics
        SET overall_rank = vendor_record.new_rank,
            updated_at = CURRENT_TIMESTAMP
        WHERE vendor_id = vendor_record.vendor_id
          AND period_type = input_period_type
          AND period_end = (
              SELECT period_end FROM get_period_bounds(input_period_type, CURRENT_DATE)
          );
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- ================================================================
-- PERFORMANCE ALERT GENERATION FUNCTION
-- ================================================================

CREATE OR REPLACE FUNCTION check_vendor_performance_alerts(
    input_vendor_id UUID,
    interaction_id UUID DEFAULT NULL
) RETURNS VOID AS $$
DECLARE
    current_metrics RECORD;
    previous_metrics RECORD;
    alert_threshold RECORD;
BEGIN
    -- Get current month metrics
    SELECT * INTO current_metrics
    FROM vendor_metrics
    WHERE vendor_id = input_vendor_id
      AND period_type = 'monthly'
      AND period_end = (SELECT period_end FROM get_period_bounds('monthly', CURRENT_DATE));
    
    -- Get previous month metrics for comparison
    SELECT * INTO previous_metrics
    FROM vendor_metrics
    WHERE vendor_id = input_vendor_id
      AND period_type = 'monthly'
      AND period_end = (SELECT period_end FROM get_period_bounds('monthly', CURRENT_DATE - INTERVAL '1 month'));
    
    -- Define alert thresholds
    alert_threshold := ROW(
        75.0,  -- completion_rate_threshold
        80.0,  -- on_time_rate_threshold
        7.0,   -- quality_score_threshold
        7.0,   -- overall_satisfaction_threshold
        15.0   -- decline_percentage_threshold
    );
    
    -- Check for completion rate decline
    IF current_metrics.completion_rate IS NOT NULL 
       AND current_metrics.completion_rate < 75.0 THEN
        INSERT INTO vendor_performance_alerts (
            vendor_id, alert_type, severity, title, description,
            triggered_by_interaction_id, trigger_condition,
            threshold_value, actual_value
        ) VALUES (
            input_vendor_id, 'quality_decline', 'high',
            'Low Completion Rate Alert',
            'Vendor completion rate has fallen below acceptable threshold',
            interaction_id, 'completion_rate < 75%',
            75.0, current_metrics.completion_rate
        ) ON CONFLICT DO NOTHING;
    END IF;
    
    -- Check for on-time delivery issues
    IF current_metrics.on_time_rate IS NOT NULL 
       AND current_metrics.on_time_rate < 80.0 THEN
        INSERT INTO vendor_performance_alerts (
            vendor_id, alert_type, severity, title, description,
            triggered_by_interaction_id, trigger_condition,
            threshold_value, actual_value
        ) VALUES (
            input_vendor_id, 'delivery_delay', 'medium',
            'Poor On-Time Delivery Performance',
            'Vendor on-time delivery rate has fallen below acceptable threshold',
            interaction_id, 'on_time_rate < 80%',
            80.0, current_metrics.on_time_rate
        ) ON CONFLICT DO NOTHING;
    END IF;
    
    -- Check for quality score decline
    IF current_metrics.avg_quality_score IS NOT NULL 
       AND current_metrics.avg_quality_score < 7.0 THEN
        INSERT INTO vendor_performance_alerts (
            vendor_id, alert_type, severity, title, description,
            triggered_by_interaction_id, trigger_condition,
            threshold_value, actual_value
        ) VALUES (
            input_vendor_id, 'quality_decline', 'high',
            'Quality Score Below Standard',
            'Average quality score has dropped below minimum acceptable level',
            interaction_id, 'avg_quality_score < 7.0',
            7.0, current_metrics.avg_quality_score
        ) ON CONFLICT DO NOTHING;
    END IF;
    
    -- Check for significant performance decline compared to previous period
    IF previous_metrics.completion_rate IS NOT NULL 
       AND current_metrics.completion_rate IS NOT NULL
       AND ((previous_metrics.completion_rate - current_metrics.completion_rate) / previous_metrics.completion_rate * 100) > 15.0 THEN
        INSERT INTO vendor_performance_alerts (
            vendor_id, alert_type, severity, title, description,
            triggered_by_interaction_id, trigger_condition,
            threshold_value, actual_value
        ) VALUES (
            input_vendor_id, 'quality_decline', 'critical',
            'Significant Performance Decline',
            'Vendor performance has declined significantly compared to previous period',
            interaction_id, 'completion_rate declined > 15%',
            previous_metrics.completion_rate, current_metrics.completion_rate
        ) ON CONFLICT DO NOTHING;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- ================================================================
-- AUTOMATED TRIGGER FUNCTIONS
-- ================================================================

-- Trigger function to recalculate metrics when interactions change
CREATE OR REPLACE FUNCTION trigger_vendor_metrics_recalc()
RETURNS TRIGGER AS $$
BEGIN
    -- Recalculate metrics for the affected vendor
    PERFORM calculate_vendor_metrics(
        COALESCE(NEW.vendor_id, OLD.vendor_id),
        'monthly'
    );
    PERFORM calculate_vendor_metrics(
        COALESCE(NEW.vendor_id, OLD.vendor_id),
        'all_time'
    );
    
    -- Check for performance alerts
    PERFORM check_vendor_performance_alerts(
        COALESCE(NEW.vendor_id, OLD.vendor_id),
        COALESCE(NEW.id, OLD.id)
    );
    
    -- Update rankings
    PERFORM update_vendor_rankings('monthly');
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Trigger function for rating changes
CREATE OR REPLACE FUNCTION trigger_rating_metrics_recalc()
RETURNS TRIGGER AS $$
DECLARE
    affected_vendor_id UUID;
BEGIN
    -- Get vendor_id from the interaction
    SELECT vendor_id INTO affected_vendor_id
    FROM vendor_interactions
    WHERE id = COALESCE(NEW.vendor_interaction_id, OLD.vendor_interaction_id);
    
    -- Recalculate metrics
    PERFORM calculate_vendor_metrics(affected_vendor_id, 'monthly');
    PERFORM calculate_vendor_metrics(affected_vendor_id, 'all_time');
    
    -- Check for alerts
    PERFORM check_vendor_performance_alerts(affected_vendor_id);
    
    -- Update rankings
    PERFORM update_vendor_rankings('monthly');
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- ================================================================
-- CREATE TRIGGERS
-- ================================================================

-- Triggers for vendor_interactions
DROP TRIGGER IF EXISTS vendor_interactions_metrics_trigger ON vendor_interactions;
CREATE TRIGGER vendor_interactions_metrics_trigger
    AFTER INSERT OR UPDATE OR DELETE ON vendor_interactions
    FOR EACH ROW EXECUTE FUNCTION trigger_vendor_metrics_recalc();

-- Triggers for vendor_ratings
DROP TRIGGER IF EXISTS vendor_ratings_metrics_trigger ON vendor_ratings;
CREATE TRIGGER vendor_ratings_metrics_trigger
    AFTER INSERT OR UPDATE OR DELETE ON vendor_ratings
    FOR EACH ROW EXECUTE FUNCTION trigger_rating_metrics_recalc();

-- ================================================================
-- UTILITY FUNCTIONS FOR BATCH PROCESSING
-- ================================================================

-- Function to recalculate all vendor metrics
CREATE OR REPLACE FUNCTION recalculate_all_vendor_metrics(
    input_period_type VARCHAR DEFAULT 'monthly'
) RETURNS TABLE(vendor_id UUID, status TEXT) AS $$
DECLARE
    vendor_record RECORD;
BEGIN
    FOR vendor_record IN SELECT id FROM vendors LOOP
        BEGIN
            PERFORM calculate_vendor_metrics(vendor_record.id, input_period_type);
            vendor_id := vendor_record.id;
            status := 'SUCCESS';
            RETURN NEXT;
        EXCEPTION WHEN OTHERS THEN
            vendor_id := vendor_record.id;
            status := 'ERROR: ' || SQLERRM;
            RETURN NEXT;
        END;
    END LOOP;
    
    -- Update rankings after all calculations
    PERFORM update_vendor_rankings(input_period_type);
END;
$$ LANGUAGE plpgsql;

-- Function to cleanup old metrics (keep last 24 months of monthly data)
CREATE OR REPLACE FUNCTION cleanup_old_vendor_metrics()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM vendor_metrics
    WHERE period_type = 'monthly'
      AND period_end < CURRENT_DATE - INTERVAL '24 months';
      
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- ================================================================
-- COMMENTS
-- ================================================================

COMMENT ON FUNCTION calculate_vendor_metrics IS 'Main function to calculate and store vendor performance metrics for a given period';
COMMENT ON FUNCTION update_vendor_rankings IS 'Updates vendor rankings based on performance metrics';
COMMENT ON FUNCTION check_vendor_performance_alerts IS 'Generates performance alerts when vendors fall below thresholds';
COMMENT ON FUNCTION recalculate_all_vendor_metrics IS 'Batch function to recalculate metrics for all vendors';
COMMENT ON FUNCTION cleanup_old_vendor_metrics IS 'Maintenance function to remove old metric records';