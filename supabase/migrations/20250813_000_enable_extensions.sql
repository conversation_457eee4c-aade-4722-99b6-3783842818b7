-- Enable required PostgreSQL extensions for HACCP and traceability features
-- This must be run before other migrations

-- Enable UUID generation
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Enable pgcrypto for cryptographic functions
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Enable unaccent for text search (useful for seafood species matching)
CREATE EXTENSION IF NOT EXISTS "unaccent";

-- Enable pg_trgm for fuzzy text matching (useful for product name matching)
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Enable btree_gist for advanced indexing (useful for time ranges in HACCP)
CREATE EXTENSION IF NOT EXISTS "btree_gist";