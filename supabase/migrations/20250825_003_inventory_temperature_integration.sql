-- ============================================================================
-- INVENTORY-TEMPERATURE INTEGRATION ENHANCEMENT
-- ============================================================================
-- This migration enhances the integration between temperature monitoring
-- and the existing inventory system. It adds temperature tracking to 
-- inventory events and creates connections for HACCP compliance.
-- ============================================================================

-- Step 1: Add temperature fields to inventory_events table
-- ============================================================================

-- Add temperature monitoring fields to existing inventory_events
ALTER TABLE inventory_events 
ADD COLUMN IF NOT EXISTS storage_area_id UUID REFERENCES storage_areas(id),
ADD COLUMN IF NOT EXISTS temp_at_event_celsius DECIMAL(6,2),
ADD COLUMN IF NOT EXISTS temp_at_event_fahrenheit DECIMAL(6,2),
ADD COLUMN IF NOT EXISTS humidity_at_event DECIMAL(5,2),
ADD COLUMN IF NOT EXISTS temp_compliant BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS temp_sensor_id UUID REFERENCES sensors(id),
ADD COLUMN IF NOT EXISTS temp_reading_id UUID REFERENCES temperature_readings(id),
ADD COLUMN IF NOT EXISTS cold_chain_maintained BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS temp_violation_notes TEXT;

-- Add indexes for the new temperature fields
CREATE INDEX IF NOT EXISTS idx_inventory_events_storage_area ON inventory_events(storage_area_id);
CREATE INDEX IF NOT EXISTS idx_inventory_events_temp_compliance ON inventory_events(temp_compliant) WHERE temp_compliant = false;
CREATE INDEX IF NOT EXISTS idx_inventory_events_cold_chain ON inventory_events(cold_chain_maintained) WHERE cold_chain_maintained = false;

-- Step 2: Create product_storage_requirements table
-- ============================================================================

CREATE TABLE IF NOT EXISTS product_storage_requirements (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  product_id UUID REFERENCES products(id) ON DELETE CASCADE NOT NULL,
  
  -- Temperature requirements specific to this product
  required_temp_min_celsius DECIMAL(5,2),
  required_temp_max_celsius DECIMAL(5,2),
  required_temp_min_fahrenheit DECIMAL(5,2),
  required_temp_max_fahrenheit DECIMAL(5,2),
  temp_unit VARCHAR(10) DEFAULT 'fahrenheit',
  
  -- Humidity requirements
  required_humidity_min DECIMAL(5,2),
  required_humidity_max DECIMAL(5,2),
  
  -- Cold chain requirements
  cold_chain_critical BOOLEAN DEFAULT true,
  max_temp_excursion_minutes INTEGER DEFAULT 30,
  
  -- HACCP requirements
  haccp_monitoring_required BOOLEAN DEFAULT false,
  ccp_designation VARCHAR(50),
  
  -- Storage preferences
  preferred_storage_areas UUID[] DEFAULT '{}', -- Array of storage_area IDs
  prohibited_storage_areas UUID[] DEFAULT '{}',
  
  -- Shelf life impact
  temp_affects_shelf_life BOOLEAN DEFAULT true,
  shelf_life_temp_factor DECIMAL(4,3) DEFAULT 1.0, -- Multiplier for shelf life calculation
  
  -- Metadata
  special_handling_notes TEXT,
  regulatory_requirements TEXT,
  metadata JSONB,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  
  UNIQUE(user_id, product_id)
);

-- Enable RLS for product storage requirements
ALTER TABLE product_storage_requirements ENABLE ROW LEVEL SECURITY;

-- RLS policies for product_storage_requirements
CREATE POLICY "Users can view their own product storage requirements" 
  ON product_storage_requirements FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own product storage requirements" 
  ON product_storage_requirements FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own product storage requirements" 
  ON product_storage_requirements FOR UPDATE 
  USING (auth.uid() = user_id) 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own product storage requirements" 
  ON product_storage_requirements FOR DELETE 
  USING (auth.uid() = user_id);

-- Add indexes for product storage requirements
CREATE INDEX IF NOT EXISTS idx_product_storage_requirements_user_id ON product_storage_requirements(user_id);
CREATE INDEX IF NOT EXISTS idx_product_storage_requirements_product_id ON product_storage_requirements(product_id);

-- Step 3: Create temperature_events table for linking temps to inventory
-- ============================================================================

CREATE TABLE IF NOT EXISTS temperature_events (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  
  -- Link to inventory event
  inventory_event_id UUID REFERENCES inventory_events(id) ON DELETE CASCADE,
  
  -- Temperature monitoring context
  sensor_id UUID REFERENCES sensors(id) ON DELETE SET NULL,
  storage_area_id UUID REFERENCES storage_areas(id) ON DELETE SET NULL,
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  
  -- Event type and timing
  event_type VARCHAR(50) NOT NULL CHECK (event_type IN (
    'receiving_temp_check', 'storage_temp_verification', 'pre_sale_temp_check',
    'cold_chain_verification', 'haccp_monitoring', 'quality_assessment'
  )),
  
  -- Temperature data at time of event
  recorded_at TIMESTAMPTZ NOT NULL,
  temp_celsius DECIMAL(6,2) NOT NULL,
  temp_fahrenheit DECIMAL(6,2) NOT NULL, 
  humidity DECIMAL(5,2),
  
  -- Compliance assessment
  within_product_requirements BOOLEAN NOT NULL,
  within_storage_requirements BOOLEAN NOT NULL,
  cold_chain_maintained BOOLEAN DEFAULT true,
  
  -- Required vs actual temperature ranges
  required_min_temp DECIMAL(6,2),
  required_max_temp DECIMAL(6,2),
  temp_deviation DECIMAL(6,2), -- How far outside acceptable range
  
  -- Quality impact assessment
  quality_impact VARCHAR(20) CHECK (quality_impact IN ('none', 'minimal', 'moderate', 'significant', 'severe')),
  shelf_life_impact_hours INTEGER DEFAULT 0,
  corrective_action_required BOOLEAN DEFAULT false,
  corrective_action_taken TEXT,
  
  -- Personnel and documentation
  checked_by UUID REFERENCES auth.users(id),
  verification_method VARCHAR(50) DEFAULT 'sensor_reading',
  documentation_complete BOOLEAN DEFAULT true,
  
  -- Notes and metadata
  notes TEXT,
  metadata JSONB,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);

-- Enable RLS and create policies for temperature_events
ALTER TABLE temperature_events ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own temperature events" 
  ON temperature_events FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own temperature events" 
  ON temperature_events FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own temperature events" 
  ON temperature_events FOR UPDATE 
  USING (auth.uid() = user_id) 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own temperature events" 
  ON temperature_events FOR DELETE 
  USING (auth.uid() = user_id);

-- Add indexes for temperature_events
CREATE INDEX IF NOT EXISTS idx_temperature_events_user_id ON temperature_events(user_id);
CREATE INDEX IF NOT EXISTS idx_temperature_events_inventory_event ON temperature_events(inventory_event_id);
CREATE INDEX IF NOT EXISTS idx_temperature_events_sensor_id ON temperature_events(sensor_id);
CREATE INDEX IF NOT EXISTS idx_temperature_events_storage_area ON temperature_events(storage_area_id);
CREATE INDEX IF NOT EXISTS idx_temperature_events_product_id ON temperature_events(product_id);
CREATE INDEX IF NOT EXISTS idx_temperature_events_recorded_at ON temperature_events(recorded_at DESC);
CREATE INDEX IF NOT EXISTS idx_temperature_events_compliance ON temperature_events(within_product_requirements, within_storage_requirements);

-- Step 4: Create enhanced inventory view with temperature data
-- ============================================================================

CREATE OR REPLACE VIEW inventory_with_temperature AS
SELECT 
  ie.*,
  
  -- Storage area information
  sa.name as storage_area_name,
  sa.area_type,
  sa.temp_min_fahrenheit,
  sa.temp_max_fahrenheit,
  sa.haccp_required,
  
  -- Product storage requirements
  psr.required_temp_min_fahrenheit,
  psr.required_temp_max_fahrenheit,
  psr.cold_chain_critical,
  psr.haccp_monitoring_required,
  
  -- Latest temperature reading from storage area
  latest_temp.temp_fahrenheit as current_storage_temp,
  latest_temp.humidity as current_storage_humidity,
  latest_temp.recorded_at as last_temp_reading_at,
  latest_temp.within_safe_range as storage_temp_compliant,
  
  -- Temperature event summary
  temp_events.total_temp_events,
  temp_events.non_compliant_events,
  temp_events.last_temp_check_at,
  temp_events.corrective_actions_required,
  
  -- Compliance status
  CASE 
    WHEN sa.haccp_required AND latest_temp.within_safe_range = false THEN 'temp_violation'
    WHEN psr.cold_chain_critical AND ie.cold_chain_maintained = false THEN 'cold_chain_broken'
    WHEN ie.temp_compliant = false THEN 'temp_non_compliant'
    WHEN sa.haccp_required AND latest_temp.recorded_at < (NOW() - INTERVAL '1 hour') THEN 'monitoring_gap'
    ELSE 'compliant'
  END as temperature_compliance_status

FROM inventory_events ie
LEFT JOIN storage_areas sa ON sa.id = ie.storage_area_id
LEFT JOIN product_storage_requirements psr ON psr.product_id = ie.product_id
LEFT JOIN LATERAL (
  SELECT tr.*
  FROM temperature_readings tr
  JOIN sensors s ON s.id = tr.sensor_id
  WHERE s.storage_area_id = ie.storage_area_id
    AND tr.user_id = ie.user_id
  ORDER BY tr.recorded_at DESC
  LIMIT 1
) latest_temp ON true
LEFT JOIN (
  SELECT 
    te.inventory_event_id,
    COUNT(*) as total_temp_events,
    COUNT(*) FILTER (WHERE NOT te.within_product_requirements) as non_compliant_events,
    MAX(te.recorded_at) as last_temp_check_at,
    COUNT(*) FILTER (WHERE te.corrective_action_required) as corrective_actions_required
  FROM temperature_events te
  GROUP BY te.inventory_event_id
) temp_events ON temp_events.inventory_event_id = ie.id;

-- Step 5: Create functions for automatic temperature compliance checking
-- ============================================================================

-- Function to check product temperature requirements during inventory events
CREATE OR REPLACE FUNCTION check_inventory_temperature_compliance()
RETURNS TRIGGER AS $$
DECLARE
  storage_temp RECORD;
  product_reqs RECORD;
BEGIN
  -- Only check if storage_area_id is set
  IF NEW.storage_area_id IS NOT NULL THEN
    
    -- Get latest temperature reading for this storage area
    SELECT tr.temp_fahrenheit, tr.temp_celsius, tr.humidity, tr.within_safe_range, tr.recorded_at
    INTO storage_temp
    FROM temperature_readings tr
    JOIN sensors s ON s.id = tr.sensor_id
    WHERE s.storage_area_id = NEW.storage_area_id 
      AND tr.user_id = NEW.user_id
    ORDER BY tr.recorded_at DESC
    LIMIT 1;
    
    -- Get product-specific temperature requirements
    SELECT required_temp_min_fahrenheit, required_temp_max_fahrenheit, cold_chain_critical
    INTO product_reqs
    FROM product_storage_requirements
    WHERE product_id = NEW.product_id AND user_id = NEW.user_id;
    
    -- Update temperature fields if we have recent data
    IF storage_temp.recorded_at IS NOT NULL THEN
      NEW.temp_at_event_fahrenheit := storage_temp.temp_fahrenheit;
      NEW.temp_at_event_celsius := storage_temp.temp_celsius;
      NEW.humidity_at_event := storage_temp.humidity;
      
      -- Check compliance based on product requirements or storage area defaults
      IF product_reqs.required_temp_min_fahrenheit IS NOT NULL THEN
        NEW.temp_compliant := (
          storage_temp.temp_fahrenheit >= product_reqs.required_temp_min_fahrenheit AND
          storage_temp.temp_fahrenheit <= product_reqs.required_temp_max_fahrenheit
        );
      ELSE
        NEW.temp_compliant := storage_temp.within_safe_range;
      END IF;
      
      -- Check cold chain maintenance for critical products
      IF product_reqs.cold_chain_critical = true THEN
        NEW.cold_chain_maintained := (NEW.temp_compliant AND storage_temp.within_safe_range);
      END IF;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply trigger to inventory_events
DROP TRIGGER IF EXISTS trigger_inventory_temperature_compliance ON inventory_events;
CREATE TRIGGER trigger_inventory_temperature_compliance
  BEFORE INSERT OR UPDATE ON inventory_events
  FOR EACH ROW EXECUTE FUNCTION check_inventory_temperature_compliance();

-- Step 6: Add temperature compliance to HACCP dashboard view
-- ============================================================================

-- Update the HACCP compliance dashboard to include inventory temperature data
DROP VIEW IF EXISTS haccp_compliance_dashboard;
CREATE OR REPLACE VIEW haccp_compliance_dashboard AS
SELECT 
  sa.id as storage_area_id,
  sa.user_id,
  sa.name as storage_area_name,
  sa.area_type,
  sa.haccp_ccp_number,
  sa.temp_min_celsius,
  sa.temp_max_celsius,
  sa.temp_min_fahrenheit,
  sa.temp_max_fahrenheit,
  
  -- Sensor count
  sensor_counts.total_sensors,
  sensor_counts.online_sensors,
  sensor_counts.offline_sensors,
  
  -- Temperature violation summary (last 24 hours)
  violation_summary.total_readings,
  violation_summary.violation_readings,
  violation_summary.violation_percentage,
  violation_summary.last_violation_at,
  
  -- Inventory temperature compliance (last 7 days)
  inventory_temp.total_inventory_events,
  inventory_temp.temp_non_compliant_events,
  inventory_temp.cold_chain_violations,
  inventory_temp.last_temp_event_at,
  
  -- Overall compliance status
  CASE 
    WHEN sensor_counts.offline_sensors > 0 THEN 'sensors_offline'
    WHEN violation_summary.violation_percentage > 5 OR 
         (inventory_temp.temp_non_compliant_events > 0 AND inventory_temp.total_inventory_events > 0 AND
          (inventory_temp.temp_non_compliant_events::DECIMAL / inventory_temp.total_inventory_events) > 0.1) THEN 'non_compliant'
    WHEN violation_summary.violation_percentage > 1 OR
         (inventory_temp.cold_chain_violations > 0) THEN 'marginal'
    ELSE 'compliant'
  END as compliance_status

FROM storage_areas sa
LEFT JOIN (
  SELECT 
    storage_area_id,
    COUNT(*) as total_sensors,
    COUNT(*) FILTER (WHERE is_online = true) as online_sensors,
    COUNT(*) FILTER (WHERE is_online = false) as offline_sensors
  FROM sensors
  WHERE is_active = true
  GROUP BY storage_area_id
) sensor_counts ON sensor_counts.storage_area_id = sa.id
LEFT JOIN (
  SELECT 
    storage_area_id,
    COUNT(*) as total_readings,
    COUNT(*) FILTER (WHERE temp_violation = true OR humidity_violation = true) as violation_readings,
    ROUND(
      (COUNT(*) FILTER (WHERE temp_violation = true OR humidity_violation = true) * 100.0 / NULLIF(COUNT(*), 0)), 
      2
    ) as violation_percentage,
    MAX(recorded_at) FILTER (WHERE temp_violation = true OR humidity_violation = true) as last_violation_at
  FROM temperature_readings
  WHERE recorded_at > (NOW() - INTERVAL '24 hours')
  GROUP BY storage_area_id
) violation_summary ON violation_summary.storage_area_id = sa.id
LEFT JOIN (
  SELECT 
    storage_area_id,
    COUNT(*) as total_inventory_events,
    COUNT(*) FILTER (WHERE temp_compliant = false) as temp_non_compliant_events,
    COUNT(*) FILTER (WHERE cold_chain_maintained = false) as cold_chain_violations,
    MAX(occurred_at) as last_temp_event_at
  FROM inventory_events
  WHERE occurred_at > (NOW() - INTERVAL '7 days') AND storage_area_id IS NOT NULL
  GROUP BY storage_area_id
) inventory_temp ON inventory_temp.storage_area_id = sa.id
WHERE sa.haccp_required = true AND sa.is_active = true;

-- Step 7: Grant permissions for new tables and views
-- ============================================================================

GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT ON product_storage_requirements TO anon, authenticated;
GRANT SELECT ON temperature_events TO anon, authenticated;
GRANT SELECT ON inventory_with_temperature TO anon, authenticated;

-- Add table comments
COMMENT ON TABLE product_storage_requirements IS 'Product-specific temperature and storage requirements for HACCP compliance';
COMMENT ON TABLE temperature_events IS 'Temperature verification events linked to inventory operations';
COMMENT ON VIEW inventory_with_temperature IS 'Inventory events enhanced with real-time temperature monitoring data';

-- Update timestamp trigger for new tables
CREATE TRIGGER trigger_product_storage_requirements_updated_at
  BEFORE UPDATE ON product_storage_requirements
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Integration enhancement completed
-- ============================================================================
-- This migration adds comprehensive integration between temperature monitoring
-- and inventory management, including:
-- - Temperature fields in inventory_events
-- - Product-specific storage requirements
-- - Temperature event tracking for HACCP compliance  
-- - Enhanced dashboard views with temperature compliance
-- - Automatic compliance checking triggers
-- ============================================================================