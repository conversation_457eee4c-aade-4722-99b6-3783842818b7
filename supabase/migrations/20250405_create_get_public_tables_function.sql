CREATE OR REPLACE FUNCTION get_public_tables()
RETURNS TABLE(table_name name) AS $$
BEGIN
  RETURN QUERY
  SELECT t.table_name
  FROM information_schema.tables t
  WHERE t.table_schema = 'public'
    AND t.table_type = 'BASE TABLE'
    -- Exclude Supabase internal tables and potential system tables
    AND t.table_name <> 'supabase_migrations' 
    AND t.table_name NOT LIKE 'pg_%'
    AND t.table_name NOT LIKE 'sql_%'
    AND t.table_name NOT LIKE '_sql%'
    AND t.table_name NOT LIKE 'supabase_%'; -- More robust exclusion
END;
$$ LANGUAGE plpgsql;
