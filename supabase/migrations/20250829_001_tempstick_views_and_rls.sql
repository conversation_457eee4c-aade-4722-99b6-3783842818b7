-- ============================================================================
-- TEMPSTICK VIEWS AND RLS POLICIES
-- ============================================================================
-- This migration adds missing dashboard views and fixes RLS policies for
-- TempStick sensor integration tables
-- ============================================================================

-- Step 1: Create dashboard views
-- ============================================================================

-- Sensor Status Dashboard View
CREATE OR REPLACE VIEW sensor_status_dashboard AS
SELECT 
  s.id,
  s.sensor_id,
  s.name,
  s.device_name,
  s.location_description,
  s.is_online,
  s.last_seen_at,
  s.connection_status,
  s.battery_level,
  s.signal_strength,
  sa.name as storage_area_name,
  sa.area_type,
  sa.location as storage_area_location,
  -- Latest reading
  tr.temp_celsius as latest_temp_celsius,
  tr.temp_fahrenheit as latest_temp_fahrenheit,
  tr.humidity as latest_humidity,
  tr.recorded_at as latest_reading_at,
  tr.within_safe_range,
  -- Alert counts
  (SELECT COUNT(*) FROM temperature_alerts ta 
   WHERE ta.sensor_id = s.id AND ta.alert_status = 'active') as active_alerts,
  s.user_id
FROM sensors s
LEFT JOIN storage_areas sa ON s.storage_area_id = sa.id
LEFT JOIN LATERAL (
  SELECT temp_celsius, temp_fahrenheit, humidity, recorded_at, within_safe_range
  FROM temperature_readings tr2 
  WHERE tr2.sensor_id = s.id 
  ORDER BY tr2.recorded_at DESC 
  LIMIT 1
) tr ON true;

-- HACCP Compliance Dashboard View
CREATE OR REPLACE VIEW haccp_compliance_dashboard AS
SELECT 
  sa.id as storage_area_id,
  sa.name as storage_area_name,
  sa.area_type,
  sa.location,
  sa.haccp_required,
  sa.haccp_ccp_number,
  sa.temp_min_celsius,
  sa.temp_max_celsius,
  sa.temp_min_fahrenheit,
  sa.temp_max_fahrenheit,
  -- Compliance status
  CASE 
    WHEN sa.haccp_required = false THEN 'not_required'
    WHEN COUNT(s.id) = 0 THEN 'no_sensors'
    WHEN COUNT(CASE WHEN s.is_online = true THEN 1 END) = 0 THEN 'offline'
    WHEN COUNT(CASE WHEN ta.id IS NOT NULL AND ta.haccp_violation = true THEN 1 END) > 0 THEN 'violation'
    ELSE 'compliant'
  END as compliance_status,
  -- Sensor counts
  COUNT(s.id) as total_sensors,
  COUNT(CASE WHEN s.is_online = true THEN 1 END) as online_sensors,
  -- Alert counts
  COUNT(CASE WHEN ta.id IS NOT NULL AND ta.alert_status = 'active' THEN 1 END) as active_alerts,
  COUNT(CASE WHEN ta.id IS NOT NULL AND ta.haccp_violation = true THEN 1 END) as haccp_violations,
  -- Latest readings
  MAX(tr.recorded_at) as latest_reading_at,
  AVG(tr.temp_celsius) as avg_temp_celsius,
  MIN(tr.temp_celsius) as min_temp_celsius,
  MAX(tr.temp_celsius) as max_temp_celsius,
  sa.user_id
FROM storage_areas sa
LEFT JOIN sensors s ON sa.id = s.storage_area_id
LEFT JOIN temperature_readings tr ON s.id = tr.sensor_id 
  AND tr.recorded_at > NOW() - INTERVAL '24 hours'
LEFT JOIN temperature_alerts ta ON s.id = ta.sensor_id 
  AND ta.alert_status = 'active'
GROUP BY sa.id, sa.name, sa.area_type, sa.location, sa.haccp_required, 
         sa.haccp_ccp_number, sa.temp_min_celsius, sa.temp_max_celsius,
         sa.temp_min_fahrenheit, sa.temp_max_fahrenheit, sa.user_id;

-- Step 2: Create RLS policies for temperature tracking tables
-- ============================================================================

-- Enable RLS on all tables
ALTER TABLE storage_areas ENABLE ROW LEVEL SECURITY;
ALTER TABLE sensors ENABLE ROW LEVEL SECURITY;
ALTER TABLE temperature_readings ENABLE ROW LEVEL SECURITY;
ALTER TABLE temperature_alerts ENABLE ROW LEVEL SECURITY;

-- Storage Areas RLS Policies
CREATE POLICY "Users can view their own storage areas" ON storage_areas
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own storage areas" ON storage_areas
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own storage areas" ON storage_areas
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own storage areas" ON storage_areas
  FOR DELETE USING (auth.uid() = user_id);

-- Sensors RLS Policies
CREATE POLICY "Users can view their own sensors" ON sensors
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own sensors" ON sensors
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own sensors" ON sensors
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own sensors" ON sensors
  FOR DELETE USING (auth.uid() = user_id);

-- Temperature Readings RLS Policies
CREATE POLICY "Users can view their own temperature readings" ON temperature_readings
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own temperature readings" ON temperature_readings
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own temperature readings" ON temperature_readings
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own temperature readings" ON temperature_readings
  FOR DELETE USING (auth.uid() = user_id);

-- Temperature Alerts RLS Policies
CREATE POLICY "Users can view their own temperature alerts" ON temperature_alerts
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own temperature alerts" ON temperature_alerts
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own temperature alerts" ON temperature_alerts
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own temperature alerts" ON temperature_alerts
  FOR DELETE USING (auth.uid() = user_id);

-- Step 3: Grant permissions on views
-- ============================================================================

-- Grant access to dashboard views
GRANT SELECT ON sensor_status_dashboard TO authenticated;
GRANT SELECT ON haccp_compliance_dashboard TO authenticated;

-- Step 4: Create indexes for dashboard performance
-- ============================================================================

-- Additional indexes for dashboard queries
CREATE INDEX IF NOT EXISTS idx_sensors_user_online ON sensors(user_id, is_online) WHERE is_online = true;
CREATE INDEX IF NOT EXISTS idx_storage_areas_user_haccp ON storage_areas(user_id, haccp_required) WHERE haccp_required = true;
CREATE INDEX IF NOT EXISTS idx_temperature_alerts_active_haccp ON temperature_alerts(user_id, alert_status, haccp_violation) 
  WHERE alert_status = 'active' AND haccp_violation = true;

-- Comment for migration tracking
COMMENT ON VIEW sensor_status_dashboard IS 'Dashboard view for real-time sensor status monitoring';
COMMENT ON VIEW haccp_compliance_dashboard IS 'Dashboard view for HACCP compliance monitoring across storage areas';
