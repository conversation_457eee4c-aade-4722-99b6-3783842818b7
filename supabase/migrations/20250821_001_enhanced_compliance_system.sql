-- ENHANCED HACCP COMPLIANCE SYSTEM - PRODUCTION GRADE
-- Implements comprehensive regulatory compliance with IoT integration, digital signatures, and real-time monitoring
-- Supports FDA, USDC, GDST, and international seafood safety standards

-- ============================================================================
-- COMPLIANCE DOCUMENTS MANAGEMENT
-- ============================================================================

-- Document storage with digital signatures and retention policies
CREATE TABLE IF NOT EXISTS compliance_documents (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  document_type text NOT NULL CHECK (document_type IN (
    'haccp_plan', 'sop', 'calibration_cert', 'audit_report', 
    'training_record', 'corrective_action', 'verification_report', 'regulatory_filing'
  )),
  regulatory_category text NOT NULL CHECK (regulatory_category IN (
    'fda_haccp', 'gdst_traceability', 'usdc_inspection', 'state_requirement', 'facility_license'
  )),
  file_path text NOT NULL,
  file_size integer NOT NULL,
  mime_type text NOT NULL,
  version integer NOT NULL DEFAULT 1,
  status text NOT NULL DEFAULT 'draft' CHECK (status IN (
    'draft', 'under_review', 'approved', 'superseded', 'archived'
  )),
  retention_period_years integer NOT NULL DEFAULT 7,
  created_by text NOT NULL,
  approved_by text,
  approved_at timestamptz,
  digital_signature jsonb, -- Stores digital signature data
  metadata jsonb DEFAULT '{}'::jsonb,
  tags text[] DEFAULT ARRAY[]::text[],
  access_level text NOT NULL DEFAULT 'restricted' CHECK (access_level IN ('public', 'restricted', 'confidential')),
  encryption_status boolean NOT NULL DEFAULT true,
  blockchain_hash text, -- For tamper-evident storage
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  retention_end_date timestamptz NOT NULL,
  auto_delete_eligible boolean NOT NULL DEFAULT true
);

-- Document audit trail for compliance tracking
CREATE TABLE IF NOT EXISTS document_audit_trail (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  document_id uuid NOT NULL REFERENCES compliance_documents(id) ON DELETE CASCADE,
  action text NOT NULL CHECK (action IN (
    'created', 'viewed', 'downloaded', 'modified', 'approved', 'superseded', 'archived', 'deleted'
  )),
  performed_by text NOT NULL,
  timestamp timestamptz NOT NULL DEFAULT now(),
  ip_address inet,
  user_agent text,
  details text,
  created_at timestamptz DEFAULT now()
);

-- Document templates for regulatory compliance
CREATE TABLE IF NOT EXISTS document_templates (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text,
  template_type text NOT NULL,
  regulatory_framework text[] NOT NULL,
  required_fields text[] NOT NULL,
  template_content text NOT NULL,
  is_active boolean NOT NULL DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Retention policies for automated document management
CREATE TABLE IF NOT EXISTS retention_policies (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  document_type text NOT NULL,
  regulatory_framework text NOT NULL,
  retention_period_years integer NOT NULL,
  destruction_method text NOT NULL CHECK (destruction_method IN (
    'secure_delete', 'physical_destruction', 'archive_transfer'
  )),
  review_frequency_months integer NOT NULL DEFAULT 12,
  automatic_destruction boolean NOT NULL DEFAULT false,
  legal_hold_override boolean NOT NULL DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(document_type, regulatory_framework)
);

-- ============================================================================
-- IOT SENSOR MANAGEMENT
-- ============================================================================

-- IoT sensors for real-time monitoring
CREATE TABLE IF NOT EXISTS iot_sensors (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  sensor_id text NOT NULL UNIQUE,
  name text NOT NULL,
  location jsonb NOT NULL, -- {zone, area, coordinates, description}
  sensor_type text NOT NULL CHECK (sensor_type IN (
    'temperature', 'humidity', 'ph', 'pressure', 'weight', 'flow_rate', 'dissolved_oxygen'
  )),
  measurement_unit text NOT NULL,
  status text NOT NULL DEFAULT 'online' CHECK (status IN ('online', 'offline', 'error', 'maintenance')),
  last_reading numeric,
  last_reading_timestamp timestamptz,
  battery_level integer CHECK (battery_level >= 0 AND battery_level <= 100),
  signal_strength integer CHECK (signal_strength >= 0 AND signal_strength <= 100),
  calibration_due_date date,
  critical_limits jsonb NOT NULL, -- {min, max, warning_min, warning_max}
  alert_settings jsonb NOT NULL, -- Email, SMS, escalation settings
  metadata jsonb DEFAULT '{}'::jsonb, -- Manufacturer, model, firmware, etc.
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Real-time sensor readings
CREATE TABLE IF NOT EXISTS sensor_readings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  sensor_id uuid NOT NULL REFERENCES iot_sensors(id) ON DELETE CASCADE,
  measurement_value numeric NOT NULL,
  measurement_timestamp timestamptz NOT NULL DEFAULT now(),
  quality_score integer DEFAULT 100 CHECK (quality_score >= 0 AND quality_score <= 100),
  within_limits boolean NOT NULL,
  deviation_severity text CHECK (deviation_severity IN ('none', 'warning', 'critical')),
  automated_response_triggered boolean DEFAULT false,
  created_at timestamptz DEFAULT now()
);

-- Monitoring zones for facility management
CREATE TABLE IF NOT EXISTS monitoring_zones (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text,
  zone_type text NOT NULL CHECK (zone_type IN (
    'cold_storage', 'processing_floor', 'receiving_dock', 'packaging_area', 'warehouse', 'office'
  )),
  target_conditions jsonb NOT NULL, -- Target temperature, humidity, pH ranges
  compliance_requirements text[] NOT NULL,
  regulatory_classification text NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Zone-sensor assignments
CREATE TABLE IF NOT EXISTS zone_sensors (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  zone_id uuid NOT NULL REFERENCES monitoring_zones(id) ON DELETE CASCADE,
  sensor_id uuid NOT NULL REFERENCES iot_sensors(id) ON DELETE CASCADE,
  assigned_at timestamptz DEFAULT now(),
  assigned_by text NOT NULL,
  UNIQUE(zone_id, sensor_id)
);

-- Monitoring alerts for real-time notifications
CREATE TABLE IF NOT EXISTS monitoring_alerts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  alert_type text NOT NULL CHECK (alert_type IN (
    'sensor_offline', 'limit_exceeded', 'battery_low', 'calibration_due', 'data_quality_poor'
  )),
  sensor_id uuid REFERENCES iot_sensors(id) ON DELETE CASCADE,
  severity text NOT NULL CHECK (severity IN ('info', 'warning', 'critical')),
  title text NOT NULL,
  description text NOT NULL,
  triggered_at timestamptz NOT NULL DEFAULT now(),
  acknowledged boolean NOT NULL DEFAULT false,
  acknowledged_by text,
  acknowledged_at timestamptz,
  resolved boolean NOT NULL DEFAULT false,
  resolved_at timestamptz,
  auto_resolved boolean NOT NULL DEFAULT false,
  escalated boolean NOT NULL DEFAULT false,
  created_at timestamptz DEFAULT now()
);

-- ============================================================================
-- ENHANCED COMPLIANCE ASSESSMENTS
-- ============================================================================

-- Comprehensive compliance assessments
CREATE TABLE IF NOT EXISTS compliance_assessments (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  framework_id text NOT NULL, -- Reference to regulatory framework
  assessment_type text NOT NULL CHECK (assessment_type IN ('self', 'internal', 'third_party', 'regulatory')),
  overall_score integer NOT NULL CHECK (overall_score >= 0 AND overall_score <= 100),
  category_scores jsonb NOT NULL, -- Scores by category (haccp, traceability, etc.)
  critical_issues integer NOT NULL DEFAULT 0,
  major_issues integer NOT NULL DEFAULT 0,
  minor_issues integer NOT NULL DEFAULT 0,
  last_assessed timestamptz NOT NULL,
  next_assessment_due timestamptz NOT NULL,
  assessed_by text NOT NULL,
  recommendations text[] DEFAULT ARRAY[]::text[],
  corrective_actions_required text[] DEFAULT ARRAY[]::text[],
  certification_status text CHECK (certification_status IN ('certified', 'conditional', 'suspended', 'revoked')),
  certificate_expiry_date date,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Regulatory alerts and notifications
CREATE TABLE IF NOT EXISTS regulatory_alerts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  framework_id text NOT NULL,
  alert_type text NOT NULL CHECK (alert_type IN (
    'deadline_approaching', 'non_compliance', 'regulation_update', 'inspection_scheduled'
  )),
  severity text NOT NULL CHECK (severity IN ('info', 'warning', 'critical')),
  title text NOT NULL,
  description text NOT NULL,
  due_date timestamptz,
  action_required text NOT NULL,
  assigned_to text,
  created timestamptz NOT NULL DEFAULT now(),
  resolved boolean NOT NULL DEFAULT false,
  resolved_at timestamptz,
  created_at timestamptz DEFAULT now()
);

-- Compliance reports generation
CREATE TABLE IF NOT EXISTS compliance_reports (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  framework_id text NOT NULL,
  report_type text NOT NULL CHECK (report_type IN (
    'assessment', 'audit', 'regulatory_filing', 'internal_review'
  )),
  report_data jsonb NOT NULL,
  generated_by text NOT NULL,
  generated_at timestamptz NOT NULL DEFAULT now(),
  file_path text,
  submitted_to_authority boolean DEFAULT false,
  submission_date timestamptz,
  authority_response text,
  created_at timestamptz DEFAULT now()
);

-- ============================================================================
-- ADVANCED CCP ENHANCEMENTS
-- ============================================================================

-- Enhanced corrective actions with workflow
ALTER TABLE corrective_actions 
ADD COLUMN IF NOT EXISTS workflow_status text DEFAULT 'pending' CHECK (workflow_status IN ('pending', 'in_progress', 'awaiting_verification', 'completed', 'cancelled')),
ADD COLUMN IF NOT EXISTS priority_level text DEFAULT 'medium' CHECK (priority_level IN ('low', 'medium', 'high', 'critical')),
ADD COLUMN IF NOT EXISTS estimated_completion_time interval,
ADD COLUMN IF NOT EXISTS actual_completion_time interval,
ADD COLUMN IF NOT EXISTS cost_impact numeric,
ADD COLUMN IF NOT EXISTS regulatory_notification_required boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS regulatory_notification_sent boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS follow_up_actions text[],
ADD COLUMN IF NOT EXISTS lessons_learned text;

-- CCP monitoring with IoT integration
ALTER TABLE ccp_monitoring_logs 
ADD COLUMN IF NOT EXISTS sensor_id uuid REFERENCES iot_sensors(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS data_source text DEFAULT 'manual' CHECK (data_source IN ('manual', 'iot_sensor', 'data_logger', 'laboratory')),
ADD COLUMN IF NOT EXISTS quality_assurance_level text DEFAULT 'standard' CHECK (quality_assurance_level IN ('basic', 'standard', 'enhanced', 'validated')),
ADD COLUMN IF NOT EXISTS environmental_conditions jsonb, -- Ambient temp, humidity during monitoring
ADD COLUMN IF NOT EXISTS photographic_evidence text[], -- URLs to photos
ADD COLUMN IF NOT EXISTS gps_coordinates point, -- Location coordinates
ADD COLUMN IF NOT EXISTS regulatory_witness text; -- Regulatory official present

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Document management indexes
CREATE INDEX IF NOT EXISTS idx_compliance_documents_type_category ON compliance_documents(document_type, regulatory_category);
CREATE INDEX IF NOT EXISTS idx_compliance_documents_status ON compliance_documents(status);
CREATE INDEX IF NOT EXISTS idx_compliance_documents_retention_date ON compliance_documents(retention_end_date);
CREATE INDEX IF NOT EXISTS idx_compliance_documents_created_by ON compliance_documents(created_by);
CREATE INDEX IF NOT EXISTS idx_document_audit_trail_document_timestamp ON document_audit_trail(document_id, timestamp DESC);

-- IoT sensor indexes
CREATE INDEX IF NOT EXISTS idx_iot_sensors_status ON iot_sensors(status);
CREATE INDEX IF NOT EXISTS idx_iot_sensors_type ON iot_sensors(sensor_type);
CREATE INDEX IF NOT EXISTS idx_iot_sensors_location_zone ON iot_sensors USING GIN ((location->>'zone'));
CREATE INDEX IF NOT EXISTS idx_sensor_readings_sensor_timestamp ON sensor_readings(sensor_id, measurement_timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_sensor_readings_within_limits ON sensor_readings(within_limits, measurement_timestamp DESC);

-- Monitoring and alerts indexes
CREATE INDEX IF NOT EXISTS idx_monitoring_alerts_unresolved ON monitoring_alerts(resolved, severity, triggered_at DESC);
CREATE INDEX IF NOT EXISTS idx_monitoring_alerts_sensor ON monitoring_alerts(sensor_id, triggered_at DESC);

-- Compliance assessment indexes
CREATE INDEX IF NOT EXISTS idx_compliance_assessments_framework ON compliance_assessments(framework_id, last_assessed DESC);
CREATE INDEX IF NOT EXISTS idx_compliance_assessments_due ON compliance_assessments(next_assessment_due);

-- ============================================================================
-- ADVANCED FUNCTIONS AND TRIGGERS
-- ============================================================================

-- Function to automatically calculate compliance scores
CREATE OR REPLACE FUNCTION calculate_compliance_score(
  p_framework_id text,
  p_assessment_date timestamptz DEFAULT now()
) RETURNS integer AS $$
DECLARE
  total_requirements integer := 0;
  completed_requirements integer := 0;
  weighted_score numeric := 0;
  requirement_record record;
  compliance_score integer;
BEGIN
  -- This is a simplified calculation - in production, implement based on specific framework requirements
  -- Get total CCPs and their compliance status
  SELECT COUNT(*) INTO total_requirements 
  FROM critical_control_points 
  WHERE is_active = true;
  
  IF total_requirements = 0 THEN
    RETURN 0;
  END IF;
  
  -- Get compliant CCPs (those with recent successful monitoring)
  SELECT COUNT(*) INTO completed_requirements
  FROM critical_control_points ccp
  WHERE ccp.is_active = true
    AND EXISTS (
      SELECT 1 FROM ccp_monitoring_logs logs
      WHERE logs.ccp_id = ccp.id
        AND logs.within_critical_limits = true
        AND logs.monitoring_timestamp > (p_assessment_date - INTERVAL '24 hours')
    );
  
  compliance_score := ROUND((completed_requirements::numeric / total_requirements::numeric) * 100);
  
  RETURN compliance_score;
END;
$$ LANGUAGE plpgsql;

-- Function to create automated compliance alerts
CREATE OR REPLACE FUNCTION create_automated_compliance_alert(
  p_alert_type text,
  p_sensor_id uuid,
  p_severity text,
  p_title text,
  p_description text
) RETURNS uuid AS $$
DECLARE
  alert_id uuid;
  sensor_settings jsonb;
BEGIN
  -- Get sensor alert settings
  SELECT alert_settings INTO sensor_settings
  FROM iot_sensors
  WHERE id = p_sensor_id;
  
  -- Create the alert
  INSERT INTO monitoring_alerts (
    alert_type, sensor_id, severity, title, description
  ) VALUES (
    p_alert_type, p_sensor_id, p_severity, p_title, p_description
  ) RETURNING id INTO alert_id;
  
  -- TODO: Trigger external notifications based on sensor_settings
  -- This would integrate with email/SMS services
  
  RETURN alert_id;
END;
$$ LANGUAGE plpgsql;

-- Trigger for automatic sensor alert creation
CREATE OR REPLACE FUNCTION trigger_sensor_alert()
RETURNS TRIGGER AS $$
DECLARE
  sensor_info record;
  alert_title text;
  alert_description text;
BEGIN
  -- Get sensor information
  SELECT name, critical_limits INTO sensor_info
  FROM iot_sensors
  WHERE id = NEW.sensor_id;
  
  -- Check if reading is outside critical limits
  IF NOT NEW.within_limits THEN
    alert_title := format('Critical Limit Exceeded: %s', sensor_info.name);
    alert_description := format(
      'Sensor reading of %s is outside critical limits (%s - %s)',
      NEW.measurement_value,
      sensor_info.critical_limits->>'min',
      sensor_info.critical_limits->>'max'
    );
    
    PERFORM create_automated_compliance_alert(
      'limit_exceeded',
      NEW.sensor_id,
      CASE 
        WHEN NEW.deviation_severity = 'critical' THEN 'critical'
        ELSE 'warning'
      END,
      alert_title,
      alert_description
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply sensor alert trigger
DROP TRIGGER IF EXISTS trg_sensor_reading_alert ON sensor_readings;
CREATE TRIGGER trg_sensor_reading_alert
  AFTER INSERT ON sensor_readings
  FOR EACH ROW
  EXECUTE FUNCTION trigger_sensor_alert();

-- Function to check document retention and mark for deletion
CREATE OR REPLACE FUNCTION check_document_retention()
RETURNS void AS $$
BEGIN
  -- Mark documents eligible for auto-deletion
  UPDATE compliance_documents 
  SET auto_delete_eligible = true
  WHERE retention_end_date <= now()
    AND status = 'archived'
    AND auto_delete_eligible = false;
    
  -- Log retention check
  INSERT INTO document_audit_trail (
    document_id, action, performed_by, details
  )
  SELECT 
    id, 
    'retention_check', 
    'system',
    'Document marked for auto-deletion due to retention period expiry'
  FROM compliance_documents
  WHERE retention_end_date <= now() 
    AND status = 'archived'
    AND auto_delete_eligible = true;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- UPDATED AT TRIGGERS
-- ============================================================================

DROP TRIGGER IF EXISTS update_compliance_documents_updated_at ON compliance_documents;
CREATE TRIGGER update_compliance_documents_updated_at
  BEFORE UPDATE ON compliance_documents
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_iot_sensors_updated_at ON iot_sensors;
CREATE TRIGGER update_iot_sensors_updated_at
  BEFORE UPDATE ON iot_sensors
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_monitoring_zones_updated_at ON monitoring_zones;
CREATE TRIGGER update_monitoring_zones_updated_at
  BEFORE UPDATE ON monitoring_zones
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_compliance_assessments_updated_at ON compliance_assessments;
CREATE TRIGGER update_compliance_assessments_updated_at
  BEFORE UPDATE ON compliance_assessments
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- SEED DATA FOR ENHANCED COMPLIANCE
-- ============================================================================

-- Insert document templates
INSERT INTO document_templates (name, description, template_type, regulatory_framework, required_fields, template_content) VALUES
(
  'HACCP Plan Template',
  'Standard HACCP plan template for seafood processors',
  'haccp_plan',
  ARRAY['FDA HACCP', 'USDC'],
  ARRAY['product_name', 'hazard_analysis', 'ccps', 'critical_limits', 'monitoring_procedures'],
  '# HACCP Plan for {product_name}\n\n## Hazard Analysis\n{hazard_analysis}\n\n## Critical Control Points\n{ccps}\n\n## Critical Limits\n{critical_limits}\n\n## Monitoring Procedures\n{monitoring_procedures}'
),
(
  'Corrective Action Report',
  'Template for documenting corrective actions taken for CCP deviations',
  'corrective_action',
  ARRAY['FDA HACCP'],
  ARRAY['deviation_description', 'root_cause', 'immediate_action', 'preventive_measures'],
  '# Corrective Action Report\n\n## Deviation Description\n{deviation_description}\n\n## Root Cause Analysis\n{root_cause}\n\n## Immediate Actions Taken\n{immediate_action}\n\n## Preventive Measures\n{preventive_measures}'
);

-- Insert retention policies
INSERT INTO retention_policies (document_type, regulatory_framework, retention_period_years, destruction_method, review_frequency_months) VALUES
('haccp_plan', 'FDA HACCP', 7, 'secure_delete', 12),
('audit_report', 'FDA HACCP', 7, 'secure_delete', 12),
('calibration_cert', 'FDA HACCP', 3, 'secure_delete', 12),
('corrective_action', 'FDA HACCP', 7, 'secure_delete', 12),
('training_record', 'FDA HACCP', 3, 'secure_delete', 12),
('verification_report', 'FDA HACCP', 7, 'secure_delete', 12),
('regulatory_filing', 'FDA HACCP', 10, 'archive_transfer', 12);

-- Insert sample monitoring zones
INSERT INTO monitoring_zones (name, description, zone_type, target_conditions, compliance_requirements, regulatory_classification) VALUES
(
  'Cold Storage Facility A',
  'Primary cold storage for fresh seafood products',
  'cold_storage',
  '{"temperature_range": {"min": -2.0, "max": 2.0}, "humidity_range": {"min": 85.0, "max": 95.0}}'::jsonb,
  ARRAY['FDA HACCP Cold Storage', 'USDC Temperature Requirements'],
  'Critical Control Point'
),
(
  'Processing Floor - Main Line',
  'Primary seafood processing and packaging line',
  'processing_floor',
  '{"temperature_range": {"min": 8.0, "max": 15.0}, "humidity_range": {"min": 45.0, "max": 75.0}}'::jsonb,
  ARRAY['FDA HACCP Processing', 'GMP Standards', 'Sanitation Requirements'],
  'Monitored Processing Area'
),
(
  'Receiving Dock',
  'Incoming seafood receiving and inspection area',
  'receiving_dock',
  '{"temperature_range": {"min": 0.0, "max": 4.0}}'::jsonb,
  ARRAY['FDA HACCP Receiving', 'Temperature Monitoring'],
  'Critical Control Point'
);

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE compliance_documents IS 'Comprehensive document management system with digital signatures and 7-year retention';
COMMENT ON TABLE document_audit_trail IS 'Complete audit trail for all document access and modifications';
COMMENT ON TABLE iot_sensors IS 'IoT sensor registry for real-time environmental monitoring';
COMMENT ON TABLE sensor_readings IS 'Real-time sensor data with automated compliance checking';
COMMENT ON TABLE monitoring_zones IS 'Facility zones with specific monitoring and compliance requirements';
COMMENT ON TABLE monitoring_alerts IS 'Real-time alerts for compliance deviations and system issues';
COMMENT ON TABLE compliance_assessments IS 'Comprehensive compliance assessments with scoring and recommendations';
COMMENT ON TABLE regulatory_alerts IS 'Regulatory deadline and compliance requirement alerts';
COMMENT ON TABLE compliance_reports IS 'Generated compliance reports for regulatory submission';

-- Grant permissions for application access
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO authenticated;
-- GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;