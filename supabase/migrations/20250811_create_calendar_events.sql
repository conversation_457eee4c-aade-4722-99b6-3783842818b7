-- Create calendar_events table to link calendar with HACCP/Sales (inventory) events
-- Design notes:
-- - Mirrors key fields from inventory_events for fast calendar queries
-- - Stores linkage via inventory_event_id and generic source fields
-- - Adds indexes on start_at and (source, inventory_event_id)
-- - <PERSON><PERSON> keeps this table in sync on INSERT/UPDATE/DELETE of inventory_events

-- Ensure extension for UUIDs (usually present in Supabase projects)
-- CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

CREATE TABLE IF NOT EXISTS calendar_events (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  -- Presentation
  title text NOT NULL,
  description text,
  -- Scheduling
  start_at timestamptz NOT NULL,
  end_at timestamptz,
  all_day boolean NOT NULL DEFAULT false,
  -- Linkage to source
  source text NOT NULL DEFAULT 'inventory_events',
  source_id uuid,
  inventory_event_id uuid REFERENCES inventory_events(id) ON DELETE CASCADE,
  -- Domain attributes (duplicated for fast filtering)
  event_type text,
  product_id uuid,
  -- Free-form
  metadata jsonb NOT NULL DEFAULT '{}'::jsonb,
  -- Timestamps
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now()
);

-- Useful indexes
CREATE INDEX IF NOT EXISTS idx_calendar_events_start_at ON calendar_events(start_at);
CREATE INDEX IF NOT EXISTS idx_calendar_events_source_inventory ON calendar_events(source, inventory_event_id);
CREATE INDEX IF NOT EXISTS idx_calendar_events_event_type ON calendar_events(event_type);
CREATE INDEX IF NOT EXISTS idx_calendar_events_product_id ON calendar_events(product_id);
CREATE INDEX IF NOT EXISTS idx_calendar_events_metadata_gin ON calendar_events USING gin (metadata);

-- Updated-at trigger helper (shared)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_calendar_events_updated_at ON calendar_events;
CREATE TRIGGER update_calendar_events_updated_at
BEFORE UPDATE ON calendar_events
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Sync function to mirror inventory_events into calendar_events
CREATE OR REPLACE FUNCTION sync_calendar_from_inventory_events()
RETURNS TRIGGER AS $$
DECLARE
  v_title text;
  v_desc text;
BEGIN
  IF (TG_OP = 'INSERT') THEN
    v_title := coalesce(NEW.event_type, 'event');
    v_desc := NULLIF(NEW.notes, '');
    INSERT INTO calendar_events (
      title, description, start_at, end_at, all_day,
      source, source_id, inventory_event_id,
      event_type, product_id, metadata
    ) VALUES (
      v_title, v_desc, NEW.created_at, NULL, false,
      'inventory_events', NEW.id, NEW.id,
      NEW.event_type, NEW.product_id,
      coalesce(NEW.metadata, '{}'::jsonb) || jsonb_build_object(
        'quantity', NEW.quantity,
        'unit_price', NEW.unit_price,
        'total_amount', NEW.total_amount,
        'images', coalesce(NEW.images, ARRAY[]::text[])
      )
    );
    RETURN NEW;
  ELSIF (TG_OP = 'UPDATE') THEN
    v_title := coalesce(NEW.event_type, 'event');
    v_desc := NULLIF(NEW.notes, '');
    UPDATE calendar_events SET
      title = v_title,
      description = v_desc,
      start_at = NEW.created_at,
      end_at = NULL,
      all_day = false,
      event_type = NEW.event_type,
      product_id = NEW.product_id,
      metadata = coalesce(NEW.metadata, '{}'::jsonb) || jsonb_build_object(
        'quantity', NEW.quantity,
        'unit_price', NEW.unit_price,
        'total_amount', NEW.total_amount,
        'images', coalesce(NEW.images, ARRAY[]::text[])
      ),
      updated_at = now()
    WHERE inventory_event_id = NEW.id;
    RETURN NEW;
  ELSIF (TG_OP = 'DELETE') THEN
    DELETE FROM calendar_events WHERE inventory_event_id = OLD.id;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Recreate triggers to avoid duplicates
DROP TRIGGER IF EXISTS trg_inventory_events_calendar_sync_ins ON inventory_events;
DROP TRIGGER IF EXISTS trg_inventory_events_calendar_sync_upd ON inventory_events;
DROP TRIGGER IF EXISTS trg_inventory_events_calendar_sync_del ON inventory_events;

CREATE TRIGGER trg_inventory_events_calendar_sync_ins
AFTER INSERT ON inventory_events
FOR EACH ROW
EXECUTE FUNCTION sync_calendar_from_inventory_events();

CREATE TRIGGER trg_inventory_events_calendar_sync_upd
AFTER UPDATE ON inventory_events
FOR EACH ROW
EXECUTE FUNCTION sync_calendar_from_inventory_events();

CREATE TRIGGER trg_inventory_events_calendar_sync_del
AFTER DELETE ON inventory_events
FOR EACH ROW
EXECUTE FUNCTION sync_calendar_from_inventory_events();
