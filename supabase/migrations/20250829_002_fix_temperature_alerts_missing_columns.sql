-- ============================================================================
-- FIX TEMPERATURE_ALERTS MISSING COLUMNS
-- ============================================================================
-- This migration adds missing columns to temperature_alerts table that are
-- required by the application code but were missing from the manual migration
-- ============================================================================

-- Step 1: Check if the columns exist and add them if missing
-- ============================================================================

-- Add first_detected_at column if it doesn't exist
DO $$ 
BEGIN 
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'temperature_alerts' 
        AND column_name = 'first_detected_at'
    ) THEN
        ALTER TABLE temperature_alerts ADD COLUMN first_detected_at TIMESTAMPTZ NOT NULL DEFAULT NOW();
        
        -- Update existing records to use created_at as first_detected_at if created_at exists
        UPDATE temperature_alerts 
        SET first_detected_at = COALESCE(created_at, NOW()) 
        WHERE first_detected_at IS NULL;
        
        RAISE NOTICE 'Added first_detected_at column to temperature_alerts';
    ELSE
        RAISE NOTICE 'Column first_detected_at already exists in temperature_alerts';
    END IF;
END $$;

-- Add other missing columns that may be referenced in the code
DO $$ 
BEGIN 
    -- Add last_detected_at if missing
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'temperature_alerts' 
        AND column_name = 'last_detected_at'
    ) THEN
        ALTER TABLE temperature_alerts ADD COLUMN last_detected_at TIMESTAMPTZ;
        RAISE NOTICE 'Added last_detected_at column to temperature_alerts';
    END IF;
    
    -- Add deviation column if missing (used in the comprehensive schema)
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'temperature_alerts' 
        AND column_name = 'deviation'
    ) THEN
        ALTER TABLE temperature_alerts ADD COLUMN deviation DECIMAL(8,2);
        RAISE NOTICE 'Added deviation column to temperature_alerts';
    END IF;
    
    -- Add haccp_violation column if missing (used in views)
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'temperature_alerts' 
        AND column_name = 'haccp_violation'
    ) THEN
        ALTER TABLE temperature_alerts ADD COLUMN haccp_violation BOOLEAN DEFAULT false;
        RAISE NOTICE 'Added haccp_violation column to temperature_alerts';
    END IF;
    
    -- Add regulatory_notification_required if missing
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'temperature_alerts' 
        AND column_name = 'regulatory_notification_required'
    ) THEN
        ALTER TABLE temperature_alerts ADD COLUMN regulatory_notification_required BOOLEAN DEFAULT false;
        RAISE NOTICE 'Added regulatory_notification_required column to temperature_alerts';
    END IF;
    
    -- Add product_safety_risk if missing
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'temperature_alerts' 
        AND column_name = 'product_safety_risk'
    ) THEN
        ALTER TABLE temperature_alerts ADD COLUMN product_safety_risk VARCHAR(20) 
        CHECK (product_safety_risk IN ('none', 'low', 'medium', 'high', 'critical'));
        RAISE NOTICE 'Added product_safety_risk column to temperature_alerts';
    END IF;
    
    -- Add corrective_actions_taken if missing 
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'temperature_alerts' 
        AND column_name = 'corrective_actions_taken'
    ) THEN
        ALTER TABLE temperature_alerts ADD COLUMN corrective_actions_taken TEXT;
        RAISE NOTICE 'Added corrective_actions_taken column to temperature_alerts';
    END IF;
    
    -- Add escalated if missing
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'temperature_alerts' 
        AND column_name = 'escalated'
    ) THEN
        ALTER TABLE temperature_alerts ADD COLUMN escalated BOOLEAN DEFAULT false;
        RAISE NOTICE 'Added escalated column to temperature_alerts';
    END IF;
    
    -- Add escalated_to if missing (note: different from existing escalated_to UUID field)
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'temperature_alerts' 
        AND column_name = 'escalated_to'
        AND data_type = 'character varying'
    ) THEN
        -- Check if there's a UUID version and rename it first
        IF EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'temperature_alerts' 
            AND column_name = 'escalated_to'
            AND data_type = 'uuid'
        ) THEN
            ALTER TABLE temperature_alerts RENAME COLUMN escalated_to TO escalated_to_user_id;
            RAISE NOTICE 'Renamed existing escalated_to UUID column to escalated_to_user_id';
        END IF;
        
        ALTER TABLE temperature_alerts ADD COLUMN escalated_to VARCHAR(255);
        RAISE NOTICE 'Added escalated_to VARCHAR column to temperature_alerts';
    END IF;
    
    -- Add email_sent and related notification fields if missing
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'temperature_alerts' 
        AND column_name = 'email_sent'
    ) THEN
        ALTER TABLE temperature_alerts ADD COLUMN email_sent BOOLEAN DEFAULT false;
        RAISE NOTICE 'Added email_sent column to temperature_alerts';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'temperature_alerts' 
        AND column_name = 'email_sent_at'
    ) THEN
        ALTER TABLE temperature_alerts ADD COLUMN email_sent_at TIMESTAMPTZ;
        RAISE NOTICE 'Added email_sent_at column to temperature_alerts';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'temperature_alerts' 
        AND column_name = 'sms_sent'
    ) THEN
        ALTER TABLE temperature_alerts ADD COLUMN sms_sent BOOLEAN DEFAULT false;
        RAISE NOTICE 'Added sms_sent column to temperature_alerts';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'temperature_alerts' 
        AND column_name = 'sms_sent_at'
    ) THEN
        ALTER TABLE temperature_alerts ADD COLUMN sms_sent_at TIMESTAMPTZ;
        RAISE NOTICE 'Added sms_sent_at column to temperature_alerts';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'temperature_alerts' 
        AND column_name = 'notification_attempts'
    ) THEN
        ALTER TABLE temperature_alerts ADD COLUMN notification_attempts INTEGER DEFAULT 0;
        RAISE NOTICE 'Added notification_attempts column to temperature_alerts';
    END IF;
    
    -- Add auto_resolve fields if missing
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'temperature_alerts' 
        AND column_name = 'auto_resolve_enabled'
    ) THEN
        ALTER TABLE temperature_alerts ADD COLUMN auto_resolve_enabled BOOLEAN DEFAULT true;
        RAISE NOTICE 'Added auto_resolve_enabled column to temperature_alerts';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'temperature_alerts' 
        AND column_name = 'auto_resolve_after_minutes'
    ) THEN
        ALTER TABLE temperature_alerts ADD COLUMN auto_resolve_after_minutes INTEGER DEFAULT 60;
        RAISE NOTICE 'Added auto_resolve_after_minutes column to temperature_alerts';
    END IF;
    
    -- Add affected_products if missing
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'temperature_alerts' 
        AND column_name = 'affected_products'
    ) THEN
        ALTER TABLE temperature_alerts ADD COLUMN affected_products JSONB;
        RAISE NOTICE 'Added affected_products column to temperature_alerts';
    END IF;
    
    -- Add inventory_impact_assessed if missing
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'temperature_alerts' 
        AND column_name = 'inventory_impact_assessed'
    ) THEN
        ALTER TABLE temperature_alerts ADD COLUMN inventory_impact_assessed BOOLEAN DEFAULT false;
        RAISE NOTICE 'Added inventory_impact_assessed column to temperature_alerts';
    END IF;

END $$;

-- Step 2: Update existing indexes to include new columns
-- ============================================================================

-- Update the order by index to use first_detected_at
CREATE INDEX IF NOT EXISTS idx_temperature_alerts_first_detected_at ON temperature_alerts(first_detected_at DESC);

-- Add index for active alerts with first_detected_at ordering
CREATE INDEX IF NOT EXISTS idx_temperature_alerts_active_first_detected ON temperature_alerts(alert_status, first_detected_at DESC) 
WHERE alert_status = 'active';

-- Add index for HACCP violations
CREATE INDEX IF NOT EXISTS idx_temperature_alerts_haccp_violation ON temperature_alerts(haccp_violation) 
WHERE haccp_violation = true;

-- Add index for escalated alerts
CREATE INDEX IF NOT EXISTS idx_temperature_alerts_escalated ON temperature_alerts(escalated) 
WHERE escalated = true;

-- Step 3: Update any existing data to ensure consistency
-- ============================================================================

-- For any existing alerts without first_detected_at, use created_at
UPDATE temperature_alerts 
SET first_detected_at = COALESCE(created_at, NOW())
WHERE first_detected_at IS NULL;

-- Step 4: Add helpful comments
-- ============================================================================

COMMENT ON COLUMN temperature_alerts.first_detected_at IS 'When the alert condition was first detected - replaces alert_timestamp from TypeScript interface';
COMMENT ON COLUMN temperature_alerts.resolved_at IS 'When the alert was resolved - replaces resolved_timestamp from TypeScript interface';
COMMENT ON COLUMN temperature_alerts.haccp_violation IS 'Whether this alert constitutes a HACCP violation';
COMMENT ON COLUMN temperature_alerts.deviation IS 'How far the reading deviated from acceptable range';

-- Step 5: Grant permissions
-- ============================================================================

-- Ensure authenticated users can access the updated table
GRANT SELECT ON temperature_alerts TO authenticated;

-- Migration completed successfully
-- ============================================================================
-- This migration ensures that the temperature_alerts table has all the columns
-- expected by the application code, resolving the "column does not exist" errors
-- ============================================================================