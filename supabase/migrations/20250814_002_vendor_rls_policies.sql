-- Vendor Report Card System - Row Level Security Policies
-- Ensures data isolation and proper access control

-- Enable RLS on all vendor report card tables
ALTER TABLE vendor_interactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE vendor_ratings ENABLE ROW LEVEL SECURITY;
ALTER TABLE vendor_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE vendor_compliance ENABLE ROW LEVEL SECURITY;
ALTER TABLE vendor_performance_alerts ENABLE ROW LEVEL SECURITY;

-- ===== VENDOR INTERACTIONS POLICIES =====

-- Users can view all vendor interactions (for reporting and analytics)
CREATE POLICY "Users can view vendor interactions" 
ON vendor_interactions FOR SELECT 
TO authenticated 
USING (true);

-- Users can insert vendor interactions 
CREATE POLICY "Users can create vendor interactions" 
ON vendor_interactions FOR INSERT 
TO authenticated 
WITH CHECK (auth.uid() = created_by);

-- Users can update vendor interactions they created
CREATE POLICY "Users can update their vendor interactions" 
ON vendor_interactions FOR UPDATE 
TO authenticated 
USING (auth.uid() = created_by OR auth.uid() IN (
    SELECT id FROM auth.users WHERE email LIKE '%@%' -- Allow authenticated users
))
WITH CHECK (true);

-- Users can delete vendor interactions they created
CREATE POLICY "Users can delete their vendor interactions" 
ON vendor_interactions FOR DELETE 
TO authenticated 
USING (auth.uid() = created_by);

-- ===== VENDOR RATINGS POLICIES =====

-- Users can view all vendor ratings (for performance analysis)
CREATE POLICY "Users can view vendor ratings" 
ON vendor_ratings FOR SELECT 
TO authenticated 
USING (true);

-- Users can create vendor ratings
CREATE POLICY "Users can create vendor ratings" 
ON vendor_ratings FOR INSERT 
TO authenticated 
WITH CHECK (auth.uid() = created_by);

-- Users can update vendor ratings they created
CREATE POLICY "Users can update their vendor ratings" 
ON vendor_ratings FOR UPDATE 
TO authenticated 
USING (auth.uid() = created_by)
WITH CHECK (auth.uid() = created_by);

-- Users can delete vendor ratings they created
CREATE POLICY "Users can delete their vendor ratings" 
ON vendor_ratings FOR DELETE 
TO authenticated 
USING (auth.uid() = created_by);

-- ===== VENDOR METRICS POLICIES =====

-- Users can view all vendor metrics (read-only for most users)
CREATE POLICY "Users can view vendor metrics" 
ON vendor_metrics FOR SELECT 
TO authenticated 
USING (true);

-- Only system/service role can insert/update vendor metrics (automated calculations)
CREATE POLICY "System can manage vendor metrics" 
ON vendor_metrics FOR ALL 
TO service_role 
USING (true)
WITH CHECK (true);

-- Allow authenticated users to insert metrics for testing/manual calculations
CREATE POLICY "Users can create vendor metrics for testing" 
ON vendor_metrics FOR INSERT 
TO authenticated 
WITH CHECK (true);

-- ===== VENDOR COMPLIANCE POLICIES =====

-- Users can view all vendor compliance records
CREATE POLICY "Users can view vendor compliance" 
ON vendor_compliance FOR SELECT 
TO authenticated 
USING (true);

-- Users can create compliance records
CREATE POLICY "Users can create vendor compliance records" 
ON vendor_compliance FOR INSERT 
TO authenticated 
WITH CHECK (auth.uid() = created_by);

-- Users can update compliance records they created
CREATE POLICY "Users can update their compliance records" 
ON vendor_compliance FOR UPDATE 
TO authenticated 
USING (auth.uid() = created_by)
WITH CHECK (auth.uid() = created_by);

-- Users can delete compliance records they created
CREATE POLICY "Users can delete their compliance records" 
ON vendor_compliance FOR DELETE 
TO authenticated 
USING (auth.uid() = created_by);

-- ===== VENDOR PERFORMANCE ALERTS POLICIES =====

-- Users can view all vendor performance alerts
CREATE POLICY "Users can view vendor alerts" 
ON vendor_performance_alerts FOR SELECT 
TO authenticated 
USING (true);

-- System can create alerts (automated)
CREATE POLICY "System can create vendor alerts" 
ON vendor_performance_alerts FOR INSERT 
TO service_role 
USING (true)
WITH CHECK (true);

-- Users can create manual alerts
CREATE POLICY "Users can create manual vendor alerts" 
ON vendor_performance_alerts FOR INSERT 
TO authenticated 
WITH CHECK (auto_generated = false);

-- Users can update alert status (acknowledge, resolve)
CREATE POLICY "Users can update vendor alert status" 
ON vendor_performance_alerts FOR UPDATE 
TO authenticated 
USING (true)
WITH CHECK (
    -- Allow updating status fields and resolution info
    (OLD.status != NEW.status AND NEW.status IN ('acknowledged', 'resolved', 'dismissed'))
    OR (OLD.acknowledged_at IS NULL AND NEW.acknowledged_at IS NOT NULL)
    OR (OLD.resolved_at IS NULL AND NEW.resolved_at IS NOT NULL)
    OR (OLD.resolution_notes IS NULL AND NEW.resolution_notes IS NOT NULL)
);

-- System can update all alert fields
CREATE POLICY "System can update all vendor alert fields" 
ON vendor_performance_alerts FOR UPDATE 
TO service_role 
USING (true)
WITH CHECK (true);

-- Users can delete manual alerts they created
CREATE POLICY "Users can delete manual vendor alerts" 
ON vendor_performance_alerts FOR DELETE 
TO authenticated 
USING (auto_generated = false);

-- System can delete any alerts
CREATE POLICY "System can delete vendor alerts" 
ON vendor_performance_alerts FOR DELETE 
TO service_role 
USING (true);

-- ===== ADDITIONAL SECURITY MEASURES =====

-- Create security definer functions for sensitive operations
CREATE OR REPLACE FUNCTION get_vendor_dashboard_summary()
RETURNS SETOF vendor_dashboard_summary
LANGUAGE sql
SECURITY DEFINER
AS $$
    SELECT * FROM vendor_dashboard_summary ORDER BY vendor_name;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_vendor_dashboard_summary() TO authenticated;

-- Create function to refresh materialized view (admin only)
CREATE OR REPLACE FUNCTION refresh_vendor_dashboard_summary()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    REFRESH MATERIALIZED VIEW vendor_dashboard_summary;
END;
$$;

-- Grant execute permission to service role only
GRANT EXECUTE ON FUNCTION refresh_vendor_dashboard_summary() TO service_role;

-- Create function to calculate vendor metrics (system function)
CREATE OR REPLACE FUNCTION calculate_vendor_metrics(
    p_vendor_id UUID DEFAULT NULL,
    p_calculation_period VARCHAR DEFAULT 'last_30_days'
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_start_date DATE;
    v_end_date DATE;
    v_vendor_record RECORD;
BEGIN
    -- Calculate date range based on period
    CASE p_calculation_period
        WHEN 'current_month' THEN
            v_start_date := DATE_TRUNC('month', CURRENT_DATE);
            v_end_date := DATE_TRUNC('month', CURRENT_DATE) + INTERVAL '1 month' - INTERVAL '1 day';
        WHEN 'last_30_days' THEN
            v_start_date := CURRENT_DATE - INTERVAL '30 days';
            v_end_date := CURRENT_DATE;
        WHEN 'current_quarter' THEN
            v_start_date := DATE_TRUNC('quarter', CURRENT_DATE);
            v_end_date := DATE_TRUNC('quarter', CURRENT_DATE) + INTERVAL '3 months' - INTERVAL '1 day';
        WHEN 'last_90_days' THEN
            v_start_date := CURRENT_DATE - INTERVAL '90 days';
            v_end_date := CURRENT_DATE;
        WHEN 'current_year' THEN
            v_start_date := DATE_TRUNC('year', CURRENT_DATE);
            v_end_date := DATE_TRUNC('year', CURRENT_DATE) + INTERVAL '1 year' - INTERVAL '1 day';
        WHEN 'all_time' THEN
            v_start_date := '2020-01-01'::DATE;
            v_end_date := CURRENT_DATE;
        ELSE
            RAISE EXCEPTION 'Invalid calculation period: %', p_calculation_period;
    END CASE;

    -- Loop through vendors (either specific vendor or all vendors)
    FOR v_vendor_record IN 
        SELECT v.id as vendor_id
        FROM vendors v
        WHERE (p_vendor_id IS NULL OR v.id = p_vendor_id)
    LOOP
        -- Calculate and insert/update vendor metrics
        INSERT INTO vendor_metrics (
            vendor_id,
            calculation_period,
            period_start_date,
            period_end_date,
            total_interactions,
            completed_deliveries,
            on_time_deliveries,
            late_deliveries,
            failed_deliveries,
            total_order_value,
            total_delivered_value,
            total_ratings,
            average_quality_score,
            average_delivery_score,
            average_communication_score,
            average_overall_satisfaction,
            overall_letter_grade,
            total_issues_reported,
            issues_resolved,
            average_resolution_time_hours,
            temperature_compliance_rate,
            documentation_completeness_rate
        )
        SELECT 
            v_vendor_record.vendor_id,
            p_calculation_period,
            v_start_date,
            v_end_date,
            -- Interaction counts
            COUNT(vi.*) as total_interactions,
            COUNT(CASE WHEN vi.status = 'completed' THEN 1 END) as completed_deliveries,
            COUNT(CASE WHEN vi.status = 'completed' AND vi.on_time_delivery = true THEN 1 END) as on_time_deliveries,
            COUNT(CASE WHEN vi.status = 'completed' AND vi.on_time_delivery = false THEN 1 END) as late_deliveries,
            COUNT(CASE WHEN vi.status = 'failed' THEN 1 END) as failed_deliveries,
            -- Financial metrics
            COALESCE(SUM(vi.order_total_amount), 0) as total_order_value,
            COALESCE(SUM(vi.delivered_amount), 0) as total_delivered_value,
            -- Rating metrics
            COUNT(vr.*) as total_ratings,
            ROUND(AVG(vr.quality_score), 1) as average_quality_score,
            ROUND(AVG(vr.delivery_timeliness_score), 1) as average_delivery_score,
            ROUND(AVG(vr.communication_score), 1) as average_communication_score,
            ROUND(AVG(vr.overall_satisfaction), 1) as average_overall_satisfaction,
            -- Calculate overall letter grade
            CASE 
                WHEN AVG(vr.composite_score) >= 9 THEN 'A'
                WHEN AVG(vr.composite_score) >= 8 THEN 'B'
                WHEN AVG(vr.composite_score) >= 7 THEN 'C'
                WHEN AVG(vr.composite_score) >= 6 THEN 'D'
                WHEN AVG(vr.composite_score) < 6 THEN 'F'
                ELSE NULL
            END as overall_letter_grade,
            -- Issue tracking
            COUNT(CASE WHEN vi.issue_reported = true THEN 1 END) as total_issues_reported,
            COUNT(CASE WHEN vi.issue_reported = true AND vi.issue_resolved = true THEN 1 END) as issues_resolved,
            ROUND(AVG(CASE WHEN vi.issue_resolution_time_hours IS NOT NULL THEN vi.issue_resolution_time_hours END), 1) as average_resolution_time_hours,
            -- Compliance rates
            ROUND(AVG(CASE WHEN vi.temperature_compliant THEN 100 ELSE 0 END), 2) as temperature_compliance_rate,
            ROUND(AVG(CASE WHEN vi.documentation_complete THEN 100 ELSE 0 END), 2) as documentation_completeness_rate
        FROM vendors v
        LEFT JOIN vendor_interactions vi ON (
            v.id = vi.vendor_id 
            AND vi.created_at::DATE BETWEEN v_start_date AND v_end_date
        )
        LEFT JOIN vendor_ratings vr ON vi.id = vr.vendor_interaction_id
        WHERE v.id = v_vendor_record.vendor_id
        GROUP BY v.id
        
        ON CONFLICT (vendor_id, calculation_period)
        DO UPDATE SET
            period_start_date = EXCLUDED.period_start_date,
            period_end_date = EXCLUDED.period_end_date,
            total_interactions = EXCLUDED.total_interactions,
            completed_deliveries = EXCLUDED.completed_deliveries,
            on_time_deliveries = EXCLUDED.on_time_deliveries,
            late_deliveries = EXCLUDED.late_deliveries,
            failed_deliveries = EXCLUDED.failed_deliveries,
            total_order_value = EXCLUDED.total_order_value,
            total_delivered_value = EXCLUDED.total_delivered_value,
            total_ratings = EXCLUDED.total_ratings,
            average_quality_score = EXCLUDED.average_quality_score,
            average_delivery_score = EXCLUDED.average_delivery_score,
            average_communication_score = EXCLUDED.average_communication_score,
            average_overall_satisfaction = EXCLUDED.average_overall_satisfaction,
            overall_letter_grade = EXCLUDED.overall_letter_grade,
            total_issues_reported = EXCLUDED.total_issues_reported,
            issues_resolved = EXCLUDED.issues_resolved,
            average_resolution_time_hours = EXCLUDED.average_resolution_time_hours,
            temperature_compliance_rate = EXCLUDED.temperature_compliance_rate,
            documentation_completeness_rate = EXCLUDED.documentation_completeness_rate,
            last_updated = NOW();
    END LOOP;
END;
$$;

-- Grant execute permission to service role and authenticated users
GRANT EXECUTE ON FUNCTION calculate_vendor_metrics(UUID, VARCHAR) TO service_role;
GRANT EXECUTE ON FUNCTION calculate_vendor_metrics(UUID, VARCHAR) TO authenticated;

-- Create function to check for performance alerts
CREATE OR REPLACE FUNCTION check_vendor_performance_alerts(p_vendor_id UUID DEFAULT NULL)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_vendor_record RECORD;
    v_metrics_record RECORD;
BEGIN
    -- Loop through vendors to check
    FOR v_vendor_record IN 
        SELECT v.id as vendor_id, v.name as vendor_name
        FROM vendors v
        WHERE (p_vendor_id IS NULL OR v.id = p_vendor_id)
    LOOP
        -- Get latest 30-day metrics
        SELECT * INTO v_metrics_record
        FROM vendor_metrics 
        WHERE vendor_id = v_vendor_record.vendor_id 
        AND calculation_period = 'last_30_days'
        ORDER BY calculated_at DESC 
        LIMIT 1;
        
        IF v_metrics_record IS NOT NULL THEN
            -- Check completion rate alert (< 75%)
            IF v_metrics_record.completion_rate < 75 THEN
                INSERT INTO vendor_performance_alerts (
                    vendor_id, alert_type, severity, title, description,
                    triggered_by_metric, threshold_value, actual_value
                ) VALUES (
                    v_vendor_record.vendor_id,
                    'performance_decline',
                    CASE WHEN v_metrics_record.completion_rate < 50 THEN 'critical' ELSE 'high' END,
                    'Low Completion Rate - ' || v_vendor_record.vendor_name,
                    'Vendor completion rate has fallen below acceptable threshold.',
                    'completion_rate',
                    75,
                    v_metrics_record.completion_rate
                ) ON CONFLICT DO NOTHING;
            END IF;

            -- Check on-time delivery alert (< 80%)
            IF v_metrics_record.on_time_delivery_rate < 80 THEN
                INSERT INTO vendor_performance_alerts (
                    vendor_id, alert_type, severity, title, description,
                    triggered_by_metric, threshold_value, actual_value
                ) VALUES (
                    v_vendor_record.vendor_id,
                    'late_delivery',
                    CASE WHEN v_metrics_record.on_time_delivery_rate < 60 THEN 'critical' ELSE 'medium' END,
                    'Poor On-Time Delivery - ' || v_vendor_record.vendor_name,
                    'Vendor on-time delivery rate has fallen below acceptable threshold.',
                    'on_time_delivery_rate',
                    80,
                    v_metrics_record.on_time_delivery_rate
                ) ON CONFLICT DO NOTHING;
            END IF;

            -- Check quality score alert (< 7.0)
            IF v_metrics_record.average_quality_score < 7.0 AND v_metrics_record.average_quality_score > 0 THEN
                INSERT INTO vendor_performance_alerts (
                    vendor_id, alert_type, severity, title, description,
                    triggered_by_metric, threshold_value, actual_value
                ) VALUES (
                    v_vendor_record.vendor_id,
                    'quality_issue',
                    CASE WHEN v_metrics_record.average_quality_score < 5 THEN 'critical' ELSE 'medium' END,
                    'Low Quality Score - ' || v_vendor_record.vendor_name,
                    'Vendor quality ratings have fallen below acceptable threshold.',
                    'average_quality_score',
                    7.0,
                    v_metrics_record.average_quality_score
                ) ON CONFLICT DO NOTHING;
            END IF;

            -- Check temperature compliance alert (< 90%)
            IF v_metrics_record.temperature_compliance_rate < 90 AND v_metrics_record.temperature_compliance_rate > 0 THEN
                INSERT INTO vendor_performance_alerts (
                    vendor_id, alert_type, severity, title, description,
                    triggered_by_metric, threshold_value, actual_value
                ) VALUES (
                    v_vendor_record.vendor_id,
                    'compliance_violation',
                    'high',
                    'Temperature Compliance Issue - ' || v_vendor_record.vendor_name,
                    'Vendor temperature compliance has fallen below food safety standards.',
                    'temperature_compliance_rate',
                    90,
                    v_metrics_record.temperature_compliance_rate
                ) ON CONFLICT DO NOTHING;
            END IF;
        END IF;
    END LOOP;
END;
$$;

-- Grant execute permission to service role and authenticated users
GRANT EXECUTE ON FUNCTION check_vendor_performance_alerts(UUID) TO service_role;
GRANT EXECUTE ON FUNCTION check_vendor_performance_alerts(UUID) TO authenticated;