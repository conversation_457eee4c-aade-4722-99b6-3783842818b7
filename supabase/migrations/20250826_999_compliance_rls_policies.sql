-- ROW LEVEL SECURITY POLICIES FOR HACCP & COMPL<PERSON>NCE SYSTEM
-- Ensures multi-tenant security and proper access controls for compliance data

-- ============================================================================
-- ENABLE RLS ON ALL COMPLIANCE TABLES
-- ============================================================================

ALTER TABLE hazard_analysis ENABLE ROW LEVEL SECURITY;
ALTER TABLE critical_control_points ENABLE ROW LEVEL SECURITY;
ALTER TABLE ccp_monitoring_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE corrective_actions ENABLE ROW LEVEL SECURITY;
ALTER TABLE verification_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE equipment_calibrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE haccp_plan_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE environmental_monitoring ENABLE ROW LEVEL SECURITY;
ALTER TABLE compliance_alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE vessels ENABLE ROW LEVEL SECURITY;
ALTER TABLE fishing_areas ENABLE ROW LEVEL SECURITY;
ALTER TABLE certifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE genetic_analysis ENABLE ROW LEVEL SECURITY;
ALTER TABLE simp_documentation ENABLE ROW LEVEL SECURITY;
ALTER TABLE fda_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE fda_request_lots ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_allergens ENABLE ROW LEVEL SECURITY;
ALTER TABLE allergen_testing ENABLE ROW LEVEL SECURITY;
ALTER TABLE shelf_life_parameters ENABLE ROW LEVEL SECURITY;
ALTER TABLE lot_shelf_life ENABLE ROW LEVEL SECURITY;
ALTER TABLE recalls ENABLE ROW LEVEL SECURITY;
ALTER TABLE recall_lots ENABLE ROW LEVEL SECURITY;
ALTER TABLE recall_notifications ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- HACCP SYSTEM POLICIES
-- ============================================================================

-- Hazard Analysis: Users can view all, but only admins can modify
CREATE POLICY "Allow view hazard analysis" ON hazard_analysis FOR SELECT USING (true);
CREATE POLICY "Allow insert hazard analysis" ON hazard_analysis FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Allow update hazard analysis" ON hazard_analysis FOR UPDATE USING (auth.role() = 'authenticated');
CREATE POLICY "Allow delete hazard analysis" ON hazard_analysis FOR DELETE USING (auth.role() = 'authenticated');

-- Critical Control Points: Users can view all, but only admins can modify
CREATE POLICY "Allow view ccps" ON critical_control_points FOR SELECT USING (true);
CREATE POLICY "Allow insert ccps" ON critical_control_points FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Allow update ccps" ON critical_control_points FOR UPDATE USING (auth.role() = 'authenticated');
CREATE POLICY "Allow delete ccps" ON critical_control_points FOR DELETE USING (auth.role() = 'authenticated');

-- CCP Monitoring Logs: All authenticated users can create and view
CREATE POLICY "Allow view ccp monitoring logs" ON ccp_monitoring_logs FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Allow insert ccp monitoring logs" ON ccp_monitoring_logs FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Allow update ccp monitoring logs" ON ccp_monitoring_logs FOR UPDATE USING (auth.role() = 'authenticated');

-- Corrective Actions: All authenticated users can create and view
CREATE POLICY "Allow view corrective actions" ON corrective_actions FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Allow insert corrective actions" ON corrective_actions FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Allow update corrective actions" ON corrective_actions FOR UPDATE USING (auth.role() = 'authenticated');

-- Verification Activities: All authenticated users can view and update
CREATE POLICY "Allow view verification activities" ON verification_activities FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Allow insert verification activities" ON verification_activities FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Allow update verification activities" ON verification_activities FOR UPDATE USING (auth.role() = 'authenticated');

-- Equipment Calibrations: All authenticated users can create and view
CREATE POLICY "Allow view equipment calibrations" ON equipment_calibrations FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Allow insert equipment calibrations" ON equipment_calibrations FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Allow update equipment calibrations" ON equipment_calibrations FOR UPDATE USING (auth.role() = 'authenticated');

-- HACCP Plan Templates: Read-only for users, admins can modify
CREATE POLICY "Allow view haccp templates" ON haccp_plan_templates FOR SELECT USING (true);
CREATE POLICY "Allow insert haccp templates" ON haccp_plan_templates FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Allow update haccp templates" ON haccp_plan_templates FOR UPDATE USING (auth.role() = 'authenticated');

-- Environmental Monitoring: All authenticated users can create and view
CREATE POLICY "Allow view environmental monitoring" ON environmental_monitoring FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Allow insert environmental monitoring" ON environmental_monitoring FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Compliance Alerts: All authenticated users can view and acknowledge
CREATE POLICY "Allow view compliance alerts" ON compliance_alerts FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Allow update compliance alerts" ON compliance_alerts FOR UPDATE USING (auth.role() = 'authenticated');

-- ============================================================================
-- TRACEABILITY SYSTEM POLICIES
-- ============================================================================

-- Vessels: All authenticated users can view, admins can modify
CREATE POLICY "Allow view vessels" ON vessels FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Allow insert vessels" ON vessels FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Allow update vessels" ON vessels FOR UPDATE USING (auth.role() = 'authenticated');

-- Fishing Areas: Read-only for all users
CREATE POLICY "Allow view fishing areas" ON fishing_areas FOR SELECT USING (true);
CREATE POLICY "Allow insert fishing areas" ON fishing_areas FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Certifications: All authenticated users can view and manage
CREATE POLICY "Allow view certifications" ON certifications FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Allow insert certifications" ON certifications FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Allow update certifications" ON certifications FOR UPDATE USING (auth.role() = 'authenticated');

-- Genetic Analysis: All authenticated users can view and create
CREATE POLICY "Allow view genetic analysis" ON genetic_analysis FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Allow insert genetic analysis" ON genetic_analysis FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Allow update genetic analysis" ON genetic_analysis FOR UPDATE USING (auth.role() = 'authenticated');

-- SIMP Documentation: All authenticated users can view and create
CREATE POLICY "Allow view simp documentation" ON simp_documentation FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Allow insert simp documentation" ON simp_documentation FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Allow update simp documentation" ON simp_documentation FOR UPDATE USING (auth.role() = 'authenticated');

-- FDA Requests: All authenticated users can view and respond
CREATE POLICY "Allow view fda requests" ON fda_requests FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Allow insert fda requests" ON fda_requests FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Allow update fda requests" ON fda_requests FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "Allow view fda request lots" ON fda_request_lots FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Allow insert fda request lots" ON fda_request_lots FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Allow update fda request lots" ON fda_request_lots FOR UPDATE USING (auth.role() = 'authenticated');

-- ============================================================================
-- FOOD SAFETY & QUALITY POLICIES
-- ============================================================================

-- Product Allergens: All authenticated users can view and manage
CREATE POLICY "Allow view product allergens" ON product_allergens FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Allow insert product allergens" ON product_allergens FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Allow update product allergens" ON product_allergens FOR UPDATE USING (auth.role() = 'authenticated');

-- Allergen Testing: All authenticated users can view and create
CREATE POLICY "Allow view allergen testing" ON allergen_testing FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Allow insert allergen testing" ON allergen_testing FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Allow update allergen testing" ON allergen_testing FOR UPDATE USING (auth.role() = 'authenticated');

-- Shelf Life Parameters: All authenticated users can view, admins can modify
CREATE POLICY "Allow view shelf life parameters" ON shelf_life_parameters FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Allow insert shelf life parameters" ON shelf_life_parameters FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Allow update shelf life parameters" ON shelf_life_parameters FOR UPDATE USING (auth.role() = 'authenticated');

-- Lot Shelf Life: All authenticated users can view and manage
CREATE POLICY "Allow view lot shelf life" ON lot_shelf_life FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Allow insert lot shelf life" ON lot_shelf_life FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Allow update lot shelf life" ON lot_shelf_life FOR UPDATE USING (auth.role() = 'authenticated');

-- ============================================================================
-- RECALL MANAGEMENT POLICIES
-- ============================================================================

-- Recalls: All authenticated users can view, admins can manage
CREATE POLICY "Allow view recalls" ON recalls FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Allow insert recalls" ON recalls FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Allow update recalls" ON recalls FOR UPDATE USING (auth.role() = 'authenticated');

-- Recall Lots: All authenticated users can view and update
CREATE POLICY "Allow view recall lots" ON recall_lots FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Allow insert recall lots" ON recall_lots FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Allow update recall lots" ON recall_lots FOR UPDATE USING (auth.role() = 'authenticated');

-- Recall Notifications: All authenticated users can view and manage
CREATE POLICY "Allow view recall notifications" ON recall_notifications FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Allow insert recall notifications" ON recall_notifications FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Allow update recall notifications" ON recall_notifications FOR UPDATE USING (auth.role() = 'authenticated');

-- ============================================================================
-- COMPLIANCE REPORTING FUNCTIONS
-- ============================================================================

-- Function to generate FDA 24-hour compliance report
CREATE OR REPLACE FUNCTION generate_fda_24_hour_report(
  request_tlcs text[],
  request_date_start date DEFAULT NULL,
  request_date_end date DEFAULT NULL
) RETURNS jsonb AS $$
DECLARE
  report_data jsonb;
  total_lots integer;
  compliant_lots integer;
BEGIN
  -- Generate comprehensive FDA compliance report
  WITH lot_compliance AS (
    SELECT 
      l.tlc,
      l.id as lot_id,
      p.name as product_name,
      p.scientific_name,
      COUNT(te.id) as total_events,
      COUNT(CASE WHEN te.gdst_kde_version IS NOT NULL THEN 1 END) as compliant_events,
      COALESCE(
        json_agg(
          json_build_object(
            'event_type', te.event_type,
            'event_time', te.event_time,
            'actor', te.actor_partner_id,
            'location', te.actor_location_id,
            'vessel', te.vessel_id,
            'reference_doc', te.reference_doc
          ) ORDER BY te.event_time
        ) FILTER (WHERE te.id IS NOT NULL),
        '[]'::json
      ) as events
    FROM lots l
    JOIN "Products" p ON p.id = l.product_id
    LEFT JOIN event_lots el ON el.lot_id = l.id
    LEFT JOIN traceability_events te ON te.id = el.event_id
    WHERE (
      request_tlcs IS NULL OR 
      l.tlc = ANY(request_tlcs)
    )
    AND (
      request_date_start IS NULL OR 
      l.created_at >= request_date_start::timestamptz
    )
    AND (
      request_date_end IS NULL OR 
      l.created_at <= (request_date_end + interval '1 day')::timestamptz
    )
    GROUP BY l.tlc, l.id, p.name, p.scientific_name
  )
  SELECT 
    json_build_object(
      'report_metadata', json_build_object(
        'generated_at', now(),
        'request_parameters', json_build_object(
          'tlcs', request_tlcs,
          'date_range', json_build_object(
            'start', request_date_start,
            'end', request_date_end
          )
        ),
        'compliance_summary', json_build_object(
          'total_lots', COUNT(*),
          'fully_traceable_lots', COUNT(CASE WHEN total_events > 0 THEN 1 END),
          'compliance_percentage', CASE 
            WHEN COUNT(*) > 0 THEN 
              ROUND((COUNT(CASE WHEN total_events > 0 THEN 1 END)::numeric / COUNT(*)::numeric) * 100, 2)
            ELSE 0 
          END
        )
      ),
      'lots', json_agg(
        json_build_object(
          'tlc', tlc,
          'product_name', product_name,
          'scientific_name', scientific_name,
          'total_events', total_events,
          'compliant_events', compliant_events,
          'events', events,
          'fda_compliant', total_events > 0
        )
      )
    )
  INTO report_data
  FROM lot_compliance;

  RETURN COALESCE(report_data, '{"error": "No data found"}'::jsonb);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get compliance dashboard metrics
CREATE OR REPLACE FUNCTION get_compliance_metrics()
RETURNS jsonb AS $$
DECLARE
  metrics jsonb;
BEGIN
  WITH dashboard_metrics AS (
    SELECT 
      (SELECT COUNT(*) FROM critical_control_points WHERE is_active = true) as active_ccps,
      (SELECT COUNT(*) FROM ccp_monitoring_logs 
       WHERE monitoring_timestamp >= now() - interval '30 days' 
       AND within_critical_limits = false) as recent_deviations,
      (SELECT COUNT(*) FROM corrective_actions 
       WHERE verification_completed = false) as pending_corrective_actions,
      (SELECT COUNT(*) FROM verification_activities 
       WHERE status = 'overdue') as overdue_verifications,
      (SELECT COUNT(*) FROM equipment_calibrations 
       WHERE next_calibration_due <= now()) as calibrations_due,
      (SELECT COUNT(*) FROM compliance_alerts 
       WHERE resolved = false) as unresolved_alerts,
      (SELECT COUNT(*) FROM lots WHERE status = 'active') as active_lots,
      (SELECT COUNT(*) FROM traceability_events 
       WHERE event_time >= now() - interval '7 days') as recent_events
  )
  SELECT json_build_object(
    'haccp_metrics', json_build_object(
      'active_ccps', active_ccps,
      'recent_deviations', recent_deviations,
      'pending_corrective_actions', pending_corrective_actions,
      'overdue_verifications', overdue_verifications,
      'calibrations_due', calibrations_due
    ),
    'traceability_metrics', json_build_object(
      'active_lots', active_lots,
      'recent_events', recent_events
    ),
    'alert_metrics', json_build_object(
      'unresolved_alerts', unresolved_alerts
    ),
    'compliance_score', CASE 
      WHEN active_ccps > 0 THEN 
        GREATEST(0, 100 - (recent_deviations * 10) - (pending_corrective_actions * 5) - (overdue_verifications * 15))
      ELSE 100 
    END,
    'last_updated', now()
  )
  INTO metrics
  FROM dashboard_metrics;

  RETURN metrics;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION generate_fda_24_hour_report TO authenticated;
GRANT EXECUTE ON FUNCTION get_compliance_metrics TO authenticated;
GRANT EXECUTE ON FUNCTION validate_gdst_kdes TO authenticated;

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON FUNCTION generate_fda_24_hour_report IS 'Generates comprehensive FDA 24-hour rule compliance report for specified lots or date range';
COMMENT ON FUNCTION get_compliance_metrics IS 'Returns real-time compliance dashboard metrics for HACCP and traceability systems';
COMMENT ON FUNCTION validate_gdst_kdes IS 'Validates GDST Key Data Elements completeness for traceability events';