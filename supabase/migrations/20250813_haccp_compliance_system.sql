-- COMPREHENSIVE HACCP COMPLIANCE SYSTEM
-- Implements HACCP Seven Principles with seafood-specific Critical Control Points
-- Supports FDA FSMA 204, GDST 1.2, and seafood HACCP requirements

-- ============================================================================
-- HAZARD ANALYSIS & CRITICAL CONTROL POINTS (HACCP)
-- ============================================================================

-- Hazard Analysis (Principle 1)
CREATE TABLE IF NOT EXISTS hazard_analysis (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id uuid REFERENCES "Products"(id) ON DELETE CASCADE,
  process_step text NOT NULL,
  hazard_type text NOT NULL CHECK (hazard_type IN ('biological', 'chemical', 'physical')),
  hazard_description text NOT NULL,
  severity text NOT NULL CHECK (severity IN ('low', 'medium', 'high')),
  likelihood text NOT NULL CHECK (likelihood IN ('low', 'medium', 'high')),
  risk_level text GENERATED ALWAYS AS (
    CASE 
      WHEN severity = 'high' OR likelihood = 'high' THEN 'high'
      WHEN severity = 'medium' AND likelihood = 'medium' THEN 'medium'
      ELSE 'low'
    END
  ) STORED,
  preventive_measures text[],
  is_ccp boolean NOT NULL DEFAULT false,
  justification text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Critical Control Points (Principle 2)
CREATE TABLE IF NOT EXISTS critical_control_points (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  hazard_analysis_id uuid REFERENCES hazard_analysis(id) ON DELETE CASCADE,
  product_id uuid REFERENCES "Products"(id) ON DELETE CASCADE,
  ccp_number text NOT NULL,
  ccp_name text NOT NULL,
  process_step text NOT NULL,
  hazard_controlled text NOT NULL,
  critical_limits jsonb NOT NULL, -- {min_temp: -2, max_temp: 2, ph_min: 4.6, etc}
  monitoring_frequency text NOT NULL, -- 'continuous', 'hourly', 'per_batch', etc
  monitoring_method text NOT NULL,
  monitoring_equipment text,
  responsible_person text NOT NULL,
  record_keeping_requirements text NOT NULL,
  verification_frequency text NOT NULL,
  is_active boolean NOT NULL DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(product_id, ccp_number)
);

-- CCP Monitoring Logs (Principle 4)
CREATE TABLE IF NOT EXISTS ccp_monitoring_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  ccp_id uuid NOT NULL REFERENCES critical_control_points(id) ON DELETE CASCADE,
  lot_id uuid REFERENCES lots(id) ON DELETE SET NULL,
  batch_number text,
  monitoring_timestamp timestamptz NOT NULL DEFAULT now(),
  measurements jsonb NOT NULL, -- {temperature: 1.5, ph: 6.2, time: 30, etc}
  within_critical_limits boolean NOT NULL,
  deviation_details text,
  monitored_by text NOT NULL,
  monitoring_equipment_id text,
  notes text,
  requires_corrective_action boolean GENERATED ALWAYS AS (NOT within_critical_limits) STORED,
  created_at timestamptz DEFAULT now()
);

-- Corrective Actions (Principle 5)
CREATE TABLE IF NOT EXISTS corrective_actions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  monitoring_log_id uuid NOT NULL REFERENCES ccp_monitoring_logs(id) ON DELETE CASCADE,
  ccp_id uuid NOT NULL REFERENCES critical_control_points(id) ON DELETE CASCADE,
  action_type text NOT NULL CHECK (action_type IN ('immediate', 'systematic', 'preventive')),
  action_description text NOT NULL,
  product_disposition text NOT NULL CHECK (product_disposition IN ('approved', 'rework', 'hold', 'reject', 'destroyed')),
  root_cause_analysis text,
  preventive_measures text[],
  performed_by text NOT NULL,
  action_timestamp timestamptz NOT NULL DEFAULT now(),
  verification_required boolean NOT NULL DEFAULT true,
  verification_completed boolean NOT NULL DEFAULT false,
  verification_by text,
  verification_timestamp timestamptz,
  effectiveness_verified boolean,
  created_at timestamptz DEFAULT now()
);

-- Verification Activities (Principle 6)
CREATE TABLE IF NOT EXISTS verification_activities (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  ccp_id uuid REFERENCES critical_control_points(id) ON DELETE CASCADE,
  verification_type text NOT NULL CHECK (verification_type IN ('calibration', 'review', 'audit', 'validation', 'testing')),
  activity_description text NOT NULL,
  scheduled_frequency text NOT NULL,
  last_performed timestamptz,
  next_due timestamptz,
  performed_by text,
  results jsonb,
  findings text,
  action_required boolean NOT NULL DEFAULT false,
  follow_up_actions text[],
  status text NOT NULL DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'in_progress', 'completed', 'overdue')),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Equipment Calibration Records
CREATE TABLE IF NOT EXISTS equipment_calibrations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  equipment_id text NOT NULL,
  equipment_name text NOT NULL,
  equipment_type text NOT NULL, -- thermometer, pH meter, scale, etc
  calibration_standard text NOT NULL,
  calibration_method text NOT NULL,
  performed_by text NOT NULL,
  calibration_date timestamptz NOT NULL DEFAULT now(),
  next_calibration_due timestamptz NOT NULL,
  before_readings jsonb,
  after_readings jsonb,
  adjustment_made boolean NOT NULL DEFAULT false,
  calibration_passed boolean NOT NULL,
  certificate_number text,
  notes text,
  created_at timestamptz DEFAULT now()
);

-- ============================================================================
-- SEAFOOD-SPECIFIC HACCP TEMPLATES
-- ============================================================================

-- Pre-defined seafood HACCP plans
CREATE TABLE IF NOT EXISTS haccp_plan_templates (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  species_category text NOT NULL, -- finfish, crustacean, molluscan_shellfish, smoked_fish
  product_category text NOT NULL, -- fresh, frozen, cooked, smoked, breaded
  template_name text NOT NULL,
  description text,
  hazards jsonb NOT NULL, -- predefined hazards for this category
  recommended_ccps jsonb NOT NULL, -- suggested CCPs with limits
  monitoring_procedures jsonb NOT NULL,
  regulatory_references text[],
  is_active boolean NOT NULL DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(species_category, product_category, template_name)
);

-- ============================================================================
-- TEMPERATURE & ENVIRONMENTAL MONITORING
-- ============================================================================

-- Environmental monitoring (cold storage, processing areas)
CREATE TABLE IF NOT EXISTS environmental_monitoring (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  location_id uuid REFERENCES locations(id) ON DELETE CASCADE,
  zone_name text NOT NULL, -- cold_storage, processing_floor, receiving_dock
  sensor_id text NOT NULL,
  measurement_type text NOT NULL CHECK (measurement_type IN ('temperature', 'humidity', 'pressure')),
  measurement_value numeric NOT NULL,
  measurement_unit text NOT NULL,
  measurement_timestamp timestamptz NOT NULL DEFAULT now(),
  critical_limit_min numeric,
  critical_limit_max numeric,
  within_limits boolean GENERATED ALWAYS AS (
    CASE 
      WHEN critical_limit_min IS NOT NULL AND measurement_value < critical_limit_min THEN false
      WHEN critical_limit_max IS NOT NULL AND measurement_value > critical_limit_max THEN false
      ELSE true
    END
  ) STORED,
  alert_triggered boolean NOT NULL DEFAULT false,
  created_at timestamptz DEFAULT now()
);

-- ============================================================================
-- COMPLIANCE ALERTS & NOTIFICATIONS
-- ============================================================================

-- Real-time compliance alerts
CREATE TABLE IF NOT EXISTS compliance_alerts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  alert_type text NOT NULL CHECK (alert_type IN ('ccp_deviation', 'temperature_excursion', 'calibration_due', 'verification_overdue', 'document_expiring')),
  severity text NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  title text NOT NULL,
  description text NOT NULL,
  source_table text NOT NULL,
  source_id uuid NOT NULL,
  triggered_at timestamptz NOT NULL DEFAULT now(),
  acknowledged boolean NOT NULL DEFAULT false,
  acknowledged_by text,
  acknowledged_at timestamptz,
  resolved boolean NOT NULL DEFAULT false,
  resolved_by text,
  resolved_at timestamptz,
  resolution_notes text,
  notification_sent boolean NOT NULL DEFAULT false,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamptz DEFAULT now()
);

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Critical monitoring indexes
CREATE INDEX IF NOT EXISTS idx_ccp_monitoring_logs_ccp_timestamp ON ccp_monitoring_logs(ccp_id, monitoring_timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_ccp_monitoring_logs_within_limits ON ccp_monitoring_logs(within_critical_limits, monitoring_timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_corrective_actions_verification ON corrective_actions(verification_required, verification_completed);
CREATE INDEX IF NOT EXISTS idx_environmental_monitoring_location_timestamp ON environmental_monitoring(location_id, measurement_timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_environmental_monitoring_within_limits ON environmental_monitoring(within_limits, measurement_timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_compliance_alerts_unresolved ON compliance_alerts(resolved, severity, triggered_at DESC);
CREATE INDEX IF NOT EXISTS idx_verification_activities_due ON verification_activities(status, next_due);

-- ============================================================================
-- AUTOMATED ALERT TRIGGERS
-- ============================================================================

-- Function to create compliance alerts
CREATE OR REPLACE FUNCTION create_compliance_alert(
  p_alert_type text,
  p_severity text,
  p_title text,
  p_description text,
  p_source_table text,
  p_source_id uuid,
  p_metadata jsonb DEFAULT '{}'::jsonb
) RETURNS uuid AS $$
DECLARE
  alert_id uuid;
BEGIN
  INSERT INTO compliance_alerts (
    alert_type, severity, title, description, 
    source_table, source_id, metadata
  ) VALUES (
    p_alert_type, p_severity, p_title, p_description,
    p_source_table, p_source_id, p_metadata
  ) RETURNING id INTO alert_id;
  
  RETURN alert_id;
END;
$$ LANGUAGE plpgsql;

-- Trigger for CCP monitoring deviations
CREATE OR REPLACE FUNCTION trigger_ccp_deviation_alert()
RETURNS TRIGGER AS $$
BEGIN
  IF NOT NEW.within_critical_limits THEN
    PERFORM create_compliance_alert(
      'ccp_deviation',
      'critical',
      'Critical Control Point Deviation',
      format('CCP %s exceeded critical limits. Immediate corrective action required.', 
        (SELECT ccp_name FROM critical_control_points WHERE id = NEW.ccp_id)),
      'ccp_monitoring_logs',
      NEW.id,
      jsonb_build_object(
        'ccp_id', NEW.ccp_id,
        'measurements', NEW.measurements,
        'monitored_by', NEW.monitored_by
      )
    );
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trg_ccp_monitoring_alert ON ccp_monitoring_logs;
CREATE TRIGGER trg_ccp_monitoring_alert
  AFTER INSERT ON ccp_monitoring_logs
  FOR EACH ROW
  EXECUTE FUNCTION trigger_ccp_deviation_alert();

-- Trigger for environmental monitoring alerts
CREATE OR REPLACE FUNCTION trigger_environmental_alert()
RETURNS TRIGGER AS $$
BEGIN
  IF NOT NEW.within_limits AND NOT NEW.alert_triggered THEN
    PERFORM create_compliance_alert(
      'temperature_excursion',
      CASE 
        WHEN NEW.measurement_type = 'temperature' THEN 'high'
        ELSE 'medium'
      END,
      format('%s Alert: %s', initcap(NEW.measurement_type), NEW.zone_name),
      format('%s %s %s is outside acceptable limits (%s-%s %s)',
        NEW.zone_name, NEW.measurement_type, NEW.measurement_value,
        COALESCE(NEW.critical_limit_min::text, 'N/A'),
        COALESCE(NEW.critical_limit_max::text, 'N/A'),
        NEW.measurement_unit
      ),
      'environmental_monitoring',
      NEW.id,
      jsonb_build_object(
        'location_id', NEW.location_id,
        'sensor_id', NEW.sensor_id,
        'measurement_value', NEW.measurement_value,
        'critical_limits', jsonb_build_object(
          'min', NEW.critical_limit_min,
          'max', NEW.critical_limit_max
        )
      )
    );
    
    UPDATE environmental_monitoring 
    SET alert_triggered = true 
    WHERE id = NEW.id;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trg_environmental_monitoring_alert ON environmental_monitoring;
CREATE TRIGGER trg_environmental_monitoring_alert
  AFTER INSERT ON environmental_monitoring
  FOR EACH ROW
  EXECUTE FUNCTION trigger_environmental_alert();

-- ============================================================================
-- UPDATED AT TRIGGERS
-- ============================================================================

DROP TRIGGER IF EXISTS update_hazard_analysis_updated_at ON hazard_analysis;
CREATE TRIGGER update_hazard_analysis_updated_at
  BEFORE UPDATE ON hazard_analysis
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_critical_control_points_updated_at ON critical_control_points;
CREATE TRIGGER update_critical_control_points_updated_at
  BEFORE UPDATE ON critical_control_points
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_verification_activities_updated_at ON verification_activities;
CREATE TRIGGER update_verification_activities_updated_at
  BEFORE UPDATE ON verification_activities
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_haccp_plan_templates_updated_at ON haccp_plan_templates;
CREATE TRIGGER update_haccp_plan_templates_updated_at
  BEFORE UPDATE ON haccp_plan_templates
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- SEED DATA: SEAFOOD HACCP TEMPLATES
-- ============================================================================

-- Insert common seafood HACCP templates
INSERT INTO haccp_plan_templates (species_category, product_category, template_name, description, hazards, recommended_ccps, monitoring_procedures, regulatory_references) VALUES

-- Fresh Finfish Template
('finfish', 'fresh', 'Fresh Finfish HACCP Plan', 'Standard HACCP plan for fresh finfish products', 
 '[
   {"type": "biological", "hazard": "Histamine formation", "species": ["tuna", "salmon", "mackerel"], "severity": "high"},
   {"type": "biological", "hazard": "Pathogenic bacteria", "severity": "medium"},
   {"type": "chemical", "hazard": "Cleaning chemical residues", "severity": "medium"},
   {"type": "physical", "hazard": "Foreign objects", "severity": "low"}
 ]'::jsonb,
 '[
   {"ccp": "CCP-1", "step": "Receiving", "hazard": "Histamine formation", "critical_limits": {"temperature_max": 4.4, "time_limit_hours": 4}},
   {"ccp": "CCP-2", "step": "Cold Storage", "hazard": "Bacterial growth", "critical_limits": {"temperature_max": 2.0, "temperature_min": -1.0}}
 ]'::jsonb,
 '[
   {"ccp": "CCP-1", "monitoring": "Temperature at receiving", "frequency": "every_delivery", "method": "calibrated_thermometer"},
   {"ccp": "CCP-2", "monitoring": "Cold storage temperature", "frequency": "continuous", "method": "data_logger"}
 ]'::jsonb,
 ARRAY['21 CFR 123', 'FDA Fish and Fishery Products Hazards and Controls Guidance']
),

-- Frozen Seafood Template
('finfish', 'frozen', 'Frozen Seafood HACCP Plan', 'HACCP plan for frozen seafood products',
 '[
   {"type": "biological", "hazard": "Pathogenic bacteria survival", "severity": "medium"},
   {"type": "chemical", "hazard": "Sodium metabisulfite", "severity": "medium"},
   {"type": "physical", "hazard": "Ice crystals and foreign objects", "severity": "low"}
 ]'::jsonb,
 '[
   {"ccp": "CCP-1", "step": "Freezing", "hazard": "Pathogen survival", "critical_limits": {"temperature_max": -18.0}},
   {"ccp": "CCP-2", "step": "Frozen Storage", "hazard": "Quality degradation", "critical_limits": {"temperature_max": -18.0, "temperature_min": -23.0}}
 ]'::jsonb,
 '[
   {"ccp": "CCP-1", "monitoring": "Freezing temperature", "frequency": "continuous", "method": "data_logger"},
   {"ccp": "CCP-2", "monitoring": "Storage temperature", "frequency": "continuous", "method": "data_logger"}
 ]'::jsonb,
 ARRAY['21 CFR 123', 'CODEX STAN 190-1995']
),

-- Molluscan Shellfish Template
('molluscan_shellfish', 'fresh', 'Molluscan Shellfish HACCP Plan', 'HACCP plan for molluscan shellfish',
 '[
   {"type": "biological", "hazard": "Vibrio vulnificus", "severity": "high"},
   {"type": "biological", "hazard": "Norovirus", "severity": "high"},
   {"type": "chemical", "hazard": "Biotoxins (PSP, DSP, ASP)", "severity": "high"},
   {"type": "chemical", "hazard": "Heavy metals", "severity": "medium"}
 ]'::jsonb,
 '[
   {"ccp": "CCP-1", "step": "Receiving", "hazard": "Vibrio vulnificus", "critical_limits": {"temperature_max": 7.2, "source_water_approved": true}},
   {"ccp": "CCP-2", "step": "Shell Stock Identification", "hazard": "Biotoxins", "critical_limits": {"harvest_area_approved": true, "harvest_date_valid": true}}
 ]'::jsonb,
 '[
   {"ccp": "CCP-1", "monitoring": "Temperature and harvest documentation", "frequency": "every_delivery", "method": "thermometer_and_tag_verification"},
   {"ccp": "CCP-2", "monitoring": "Shell stock tags", "frequency": "every_delivery", "method": "tag_verification"}
 ]'::jsonb,
 ARRAY['21 CFR 123.28', 'National Shellfish Sanitation Program Guide']
),

-- Smoked Fish Template
('finfish', 'smoked', 'Smoked Fish HACCP Plan', 'HACCP plan for smoked fish products',
 '[
   {"type": "biological", "hazard": "Clostridium botulinum", "severity": "high"},
   {"type": "biological", "hazard": "Listeria monocytogenes", "severity": "high"},
   {"type": "chemical", "hazard": "Sodium nitrite", "severity": "medium"},
   {"type": "physical", "hazard": "Foreign objects", "severity": "low"}
 ]'::jsonb,
 '[
   {"ccp": "CCP-1", "step": "Salting/Brining", "hazard": "C. botulinum", "critical_limits": {"salt_concentration_min": 3.5, "ph_max": 6.0}},
   {"ccp": "CCP-2", "step": "Smoking", "hazard": "Pathogen survival", "critical_limits": {"internal_temp_min": 63.0, "time_min_minutes": 30}},
   {"ccp": "CCP-3", "step": "Cooling", "hazard": "Pathogen growth", "critical_limits": {"cooling_time_max_hours": 6, "final_temp_max": 4.0}}
 ]'::jsonb,
 '[
   {"ccp": "CCP-1", "monitoring": "Salt concentration and pH", "frequency": "every_batch", "method": "refractometer_and_ph_meter"},
   {"ccp": "CCP-2", "monitoring": "Internal temperature and time", "frequency": "continuous", "method": "probe_thermometer"},
   {"ccp": "CCP-3", "monitoring": "Product temperature", "frequency": "every_batch", "method": "probe_thermometer"}
 ]'::jsonb,
 ARRAY['21 CFR 123', 'FDA Guidance for Clostridium botulinum in ROP Fish']
)

ON CONFLICT (species_category, product_category, template_name) DO NOTHING;

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE hazard_analysis IS 'HACCP Principle 1: Systematic hazard analysis for products and processes';
COMMENT ON TABLE critical_control_points IS 'HACCP Principle 2: Critical Control Points with defined critical limits';
COMMENT ON TABLE ccp_monitoring_logs IS 'HACCP Principle 4: Monitoring procedures and logs for CCPs';
COMMENT ON TABLE corrective_actions IS 'HACCP Principle 5: Corrective actions for CCP deviations';
COMMENT ON TABLE verification_activities IS 'HACCP Principle 6: Verification activities and procedures';
COMMENT ON TABLE equipment_calibrations IS 'HACCP Principle 7: Record keeping for equipment calibration';
COMMENT ON TABLE environmental_monitoring IS 'Continuous environmental monitoring for temperature, humidity, and other parameters';
COMMENT ON TABLE compliance_alerts IS 'Real-time alerts for compliance deviations requiring immediate attention';
COMMENT ON TABLE haccp_plan_templates IS 'Pre-configured HACCP plans for common seafood product categories';