# Supabase Directory

This directory contains all Supabase-related configuration, database migrations, and schema definitions for the Pacific Cloud Seafoods Manager application.

## Overview

Supabase serves as the backend-as-a-service platform providing PostgreSQL database, authentication, real-time subscriptions, and file storage. This directory manages the database schema, migrations, and Supabase-specific configurations.

## Directory Structure

### `/migrations` - Database Migrations
Contains SQL migration files that define and evolve the database schema over time.

### `/Database Tables` - Schema Documentation
CSV files documenting the database structure and table relationships.

### Configuration Files
- `config.toml` - Supabase CLI configuration
- `.gitignore` - Git ignore rules for Supabase-specific files

### `/.temp` - Temporary Files
Temporary files created by Supabase CLI (not version controlled).

## Database Schema Overview

### Core Tables

#### `inventory_events`
**Purpose**: Central table for all inventory tracking events including voice-captured data.

**Key Features**:
- Comprehensive event tracking with timestamps
- Voice-specific columns for audio processing
- Audit trail integration
- GDST traceability compliance
- Row Level Security (RLS) policies

**Voice Extensions**:
```sql
-- Voice-specific columns added to inventory_events
audio_path TEXT,
transcription TEXT,
confidence_score DECIMAL(3,2),
processing_status TEXT DEFAULT 'pending',
voice_metadata JSONB
```

#### `voice_event_audit`
**Purpose**: Audit trail for voice event changes and processing history.

**Key Features**:
- Complete change tracking
- User attribution
- Timestamp tracking
- Old/new value comparison
- Compliance reporting

#### `products`
**Purpose**: Product catalog with detailed specifications and traceability information.

**Key Features**:
- Product specifications and attributes
- Category management
- Supplier relationships
- Compliance certifications
- Nutritional information

#### `vendors`
**Purpose**: Vendor management with performance tracking and report cards.

**Key Features**:
- Vendor profiles and contact information
- Performance metrics and scoring
- Compliance tracking
- Communication history
- Integration with vendor APIs

#### `customers`
**Purpose**: Customer relationship management and order tracking.

**Key Features**:
- Customer profiles and preferences
- Order history and patterns
- Communication tracking
- Compliance requirements
- Delivery preferences

### Compliance Tables

#### `haccp_events`
**Purpose**: HACCP (Hazard Analysis Critical Control Points) compliance tracking.

**Key Features**:
- Critical control point monitoring
- Temperature and safety logging
- Corrective action tracking
- Compliance reporting
- Audit trail maintenance

#### `compliance_events`
**Purpose**: General regulatory compliance tracking beyond HACCP.

**Key Features**:
- FDA compliance monitoring
- Traceability requirements
- Documentation management
- Inspection tracking
- Violation reporting

### Traceability Tables

#### `traceability_events`
**Purpose**: GDST (Global Dialogue on Seafood Traceability) compliant tracking.

**Key Features**:
- Supply chain visibility
- Catch/harvest information
- Processing history
- Chain of custody
- Sustainability certifications

## Migration History

### Recent Migrations

#### `20250815_001_voice_event_management_schema.sql`
**Purpose**: Comprehensive voice event management system implementation.

**Changes**:
- Added voice-specific columns to `inventory_events`
- Created `voice_event_audit` table
- Implemented RLS policies for voice data
- Added indexes for performance optimization
- Created functions for voice event processing

#### `20250814_001_vendor_report_card_schema.sql`
**Purpose**: Vendor performance tracking and report card system.

**Changes**:
- Created vendor performance tables
- Implemented scoring algorithms
- Added automated metrics collection
- Created reporting views
- Implemented RLS policies

#### `20250813_enhanced_traceability_gdst.sql`
**Purpose**: Enhanced traceability system compliant with GDST standards.

**Changes**:
- Implemented GDST data model
- Added traceability event tracking
- Created supply chain visibility features
- Added sustainability tracking
- Implemented compliance reporting

### Migration Management

#### Running Migrations
```bash
# Apply all pending migrations
npm run db:migrate

# Apply all migrations including seed data
npm run db:migrate:all

# Reset database and apply all migrations
npm run db:reset
```

#### Creating New Migrations
```bash
# Create a new migration file
supabase migration new migration_name

# Generate migration from schema changes
supabase db diff --schema public > supabase/migrations/new_migration.sql
```

## Row Level Security (RLS)

### Security Model
All tables implement RLS policies to ensure data access control:

#### User-Based Access
```sql
-- Example RLS policy for inventory_events
CREATE POLICY "Users can view their own events" ON inventory_events
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own events" ON inventory_events
  FOR INSERT WITH CHECK (auth.uid() = user_id);
```

#### Role-Based Access
```sql
-- Role-based access for managers
CREATE POLICY "Managers can view all events" ON inventory_events
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM user_roles 
      WHERE user_id = auth.uid() 
      AND role IN ('manager', 'admin')
    )
  );
```

### Voice Event Security
Special security considerations for voice data:
- Audio files stored in secure Supabase Storage buckets
- Signed URLs for temporary access
- Automatic cleanup of expired audio files
- Audit trail for all voice data access

## Real-Time Subscriptions

### Subscription Configuration
Real-time updates for critical data changes:

```typescript
// Voice event real-time updates
const subscription = supabase
  .channel('voice-events')
  .on('postgres_changes', {
    event: '*',
    schema: 'public',
    table: 'inventory_events',
    filter: 'transcription=not.is.null'
  }, (payload) => {
    // Handle voice event changes
  })
  .subscribe();
```

### Performance Optimization
- Selective subscriptions to reduce bandwidth
- Filtered subscriptions for relevant data only
- Connection pooling for multiple subscriptions
- Automatic reconnection handling

## Storage Configuration

### Audio File Storage
Supabase Storage buckets for voice recordings:

#### Bucket Configuration
```sql
-- Create audio storage bucket
INSERT INTO storage.buckets (id, name, public) 
VALUES ('voice-recordings', 'voice-recordings', false);

-- RLS policies for audio files
CREATE POLICY "Users can upload their own audio" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'voice-recordings' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );
```

#### File Management
- Automatic file compression
- Signed URL generation for secure access
- Lifecycle policies for file cleanup
- Backup and recovery procedures

## Performance Optimization

### Indexing Strategy
Comprehensive indexing for query performance:

```sql
-- Voice event performance indexes
CREATE INDEX idx_inventory_events_voice_search 
ON inventory_events USING gin(to_tsvector('english', transcription))
WHERE transcription IS NOT NULL;

CREATE INDEX idx_inventory_events_confidence 
ON inventory_events (confidence_score DESC)
WHERE confidence_score IS NOT NULL;

CREATE INDEX idx_inventory_events_processing_status 
ON inventory_events (processing_status, created_at DESC)
WHERE processing_status IS NOT NULL;
```

### Query Optimization
- Materialized views for complex aggregations
- Partial indexes for filtered queries
- Connection pooling configuration
- Query plan analysis and optimization

## Backup and Recovery

### Automated Backups
Supabase provides automated daily backups with point-in-time recovery.

### Manual Backup Procedures
```bash
# Export database schema
supabase db dump --schema-only > schema_backup.sql

# Export data
supabase db dump --data-only > data_backup.sql

# Full backup
supabase db dump > full_backup.sql
```

### Recovery Procedures
- Point-in-time recovery for data corruption
- Schema rollback procedures
- Data validation after recovery
- Testing recovery procedures

## Development Workflow

### Local Development
```bash
# Start local Supabase instance
supabase start

# Apply migrations to local instance
supabase db reset

# Generate TypeScript types
supabase gen types typescript > src/types/supabase.ts
```

### Testing
```bash
# Run database tests
supabase test db

# Test migrations
supabase db reset --debug
```

### Deployment
```bash
# Deploy to staging
supabase db push --project-ref staging-project-id

# Deploy to production
supabase db push --project-ref production-project-id
```

## Monitoring and Maintenance

### Performance Monitoring
- Query performance tracking
- Connection pool monitoring
- Storage usage monitoring
- Real-time subscription metrics

### Maintenance Tasks
- Regular index maintenance
- Statistics updates
- Vacuum operations
- Log rotation

### Health Checks
```sql
-- Database health check queries
SELECT schemaname, tablename, n_tup_ins, n_tup_upd, n_tup_del 
FROM pg_stat_user_tables 
ORDER BY n_tup_ins + n_tup_upd + n_tup_del DESC;

-- Voice event processing health
SELECT processing_status, COUNT(*) 
FROM inventory_events 
WHERE transcription IS NOT NULL 
GROUP BY processing_status;
```

## Security Best Practices

### Access Control
- Principle of least privilege
- Regular access reviews
- Role-based permissions
- API key rotation

### Data Protection
- Encryption at rest and in transit
- PII data handling
- Audit trail maintenance
- Compliance reporting

### Vulnerability Management
- Regular security updates
- Dependency scanning
- Penetration testing
- Security incident response

## Future Enhancements

- Advanced analytics and reporting tables
- Machine learning integration for voice processing
- Enhanced real-time collaboration features
- Advanced compliance automation
- Integration with external systems
- Performance optimization initiatives