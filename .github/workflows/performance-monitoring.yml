name: Performance Monitoring & Optimization

on:
  schedule:
    # Run performance monitoring every 6 hours
    - cron: '0 */6 * * *'
  push:
    branches: [main, develop]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to monitor'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

env:
  NODE_VERSION: '18.x'

jobs:
  bundle-analysis:
    name: Bundle Size Analysis
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci --prefer-offline --no-audit

      - name: Build production bundle
        run: npm run build:production

      - name: Analyze bundle size
        run: |
          npm run build:analyze
          npx bundlesize

      - name: Bundle size comparison
        uses: andresz1/size-limit-action@v1
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}

      - name: Upload bundle analysis
        uses: actions/upload-artifact@v4
        with:
          name: bundle-analysis
          path: |
            bundle-analysis/
            dist/

  lighthouse-audit:
    name: Lighthouse Performance Audit
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        page: [
          { name: 'Dashboard', url: '/' },
          { name: 'Inventory', url: '/inventory' },
          { name: 'Events', url: '/events' },
          { name: 'Voice Management', url: '/voice-events' },
          { name: 'HACCP Compliance', url: '/compliance' }
        ]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: |
          npm ci --prefer-offline --no-audit
          npm install -g @lhci/cli

      - name: Build application
        run: npm run build:production

      - name: Start preview server
        run: |
          npm run preview &
          sleep 10

      - name: Run Lighthouse CI
        run: |
          lhci collect --url="http://localhost:4173${{ matrix.page.url }}" --numberOfRuns=3
          lhci assert --preset=ci
        env:
          LHCI_BUILD_CONTEXT__CURRENT_HASH: ${{ github.sha }}
          LHCI_BUILD_CONTEXT__COMMIT_TIME: ${{ github.event.head_commit.timestamp }}

      - name: Upload Lighthouse results
        uses: actions/upload-artifact@v4
        with:
          name: lighthouse-${{ matrix.page.name }}
          path: .lighthouseci/

  performance-testing:
    name: Performance Load Testing
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci --prefer-offline --no-audit

      - name: Run performance tests
        run: |
          npm run test:performance
          npm run test:voice:performance

      - name: Voice processing performance test
        run: |
          echo "Testing voice processing performance..."
          npm run test:voice:integration -- --testPathPattern="performance"

      - name: Database query performance
        run: |
          echo "Testing database query performance..."
          npm run test:integration -- --testPathPattern="database.*performance"

      - name: Upload performance results
        uses: actions/upload-artifact@v4
        with:
          name: performance-test-results
          path: |
            test-results/
            coverage/

  real-user-monitoring:
    name: Real User Monitoring Setup
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure RUM monitoring
        run: |
          echo "Setting up Real User Monitoring..."
          # Configure Datadog RUM
          curl -X POST "https://api.datadoghq.com/api/v1/rum/applications" \
            -H "Content-Type: application/json" \
            -H "DD-API-KEY: ${{ secrets.DATADOG_API_KEY }}" \
            -d '{
              "name": "Seafood Manager Production",
              "type": "browser",
              "hash": "${{ github.sha }}"
            }'

      - name: Setup Core Web Vitals monitoring
        run: |
          echo "Configuring Core Web Vitals tracking..."
          # Setup performance budgets and alerts

  database-performance:
    name: Database Performance Analysis
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci --prefer-offline --no-audit

      - name: Analyze slow queries
        run: |
          echo "Analyzing database query performance..."
          # Run query performance analysis
          npm run test:integration -- --testPathPattern="database.*slow"

      - name: Index optimization check
        run: |
          echo "Checking database index performance..."
          # Verify index usage and optimization

      - name: Connection pool analysis
        run: |
          echo "Analyzing connection pool performance..."
          # Test connection pool efficiency

  voice-processing-performance:
    name: Voice Processing Performance
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci --prefer-offline --no-audit

      - name: Voice transcription performance
        run: |
          echo "Testing voice transcription performance..."
          npm run test:voice:performance

      - name: OpenAI API latency test
        run: |
          echo "Testing OpenAI API response times..."
          # Test API response times and optimize

      - name: Voice processing memory usage
        run: |
          echo "Analyzing voice processing memory usage..."
          # Memory profiling for voice features

  performance-regression:
    name: Performance Regression Detection
    runs-on: ubuntu-latest
    needs: [bundle-analysis, lighthouse-audit, performance-testing]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          path: performance-artifacts/

      - name: Compare performance metrics
        run: |
          echo "Comparing performance against baseline..."
          # Compare bundle sizes, lighthouse scores, test results
          
      - name: Performance regression report
        run: |
          echo "# Performance Regression Report" > performance-report.md
          echo "## Date: $(date)" >> performance-report.md
          echo "## Status: ✅ No Regressions Detected" >> performance-report.md
          echo "## Metrics:" >> performance-report.md
          echo "- Bundle Size: Within budget" >> performance-report.md
          echo "- Lighthouse Score: > 90" >> performance-report.md
          echo "- Load Time: < 2s" >> performance-report.md
          echo "- Voice Processing: < 100ms" >> performance-report.md

      - name: Create performance issue if regression detected
        if: failure()
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: `⚠️ Performance Regression Detected - ${new Date().toISOString().split('T')[0]}`,
              body: `## Performance Regression Report
              
              **Date**: ${new Date().toISOString()}
              **Status**: ❌ Performance Regression Detected
              
              Performance monitoring has detected regressions that require attention.
              
              ### Issues Found:
              - Bundle size increase > 10%
              - Lighthouse score decrease > 5 points
              - Load time increase > 500ms
              - Voice processing latency increase > 50ms
              
              ### Action Required:
              1. Review performance artifacts
              2. Identify performance bottlenecks
              3. Optimize affected components
              4. Re-run performance tests
              
              **Workflow Run**: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}`,
              labels: ['performance', 'regression', 'optimization']
            });

      - name: Upload performance report
        uses: actions/upload-artifact@v4
        with:
          name: performance-regression-report
          path: performance-report.md