name: CI

on:
  push:
    branches: [ main, dev ]
  pull_request:
    branches: [ main, dev ]

permissions: read-all

jobs:
  lint:
    name: <PERSON><PERSON>
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 20.x
          cache: npm
      - name: Install dependencies
        run: npm ci
      - name: Run ESLint
        run: npm run lint

  typecheck:
    name: Type Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 20.x
          cache: npm
      - name: Install dependencies
        run: npm ci
      - name: Run TypeScript type checks
        run: npx tsc --noEmit

  build:
    name: Build
    runs-on: ubuntu-latest
    needs: [ lint, typecheck ]
    env:
      CI: true
      VITE_SUPABASE_URL: ${{ secrets.VITE_SUPABASE_URL }}
      VITE_SUPABASE_ANON_KEY: ${{ secrets.VITE_SUPABASE_ANON_KEY }}
      VITE_SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.VITE_SUPABASE_SERVICE_ROLE_KEY }}
      VITE_OPENAI_API_KEY: ${{ secrets.VITE_OPENAI_API_KEY }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 20.x
          cache: npm
      - name: Install dependencies
        run: npm ci
      - name: Build
        run: npm run build
