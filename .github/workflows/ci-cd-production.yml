name: Seafood Manager CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'
  PNPM_VERSION: '8'

jobs:
  # Quality Gates Job
  quality-gates:
    name: Code Quality & Security
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci --frozen-lockfile

    - name: TypeScript type checking
      run: npm run type-check

    - name: ESLint code quality
      run: npm run lint

    - name: Security audit
      run: npm audit --audit-level=moderate

    - name: Dependency vulnerability scan
      run: |
        npm install -g audit-ci
        audit-ci --moderate

    - name: Bundle size analysis
      run: |
        npm run build
        npx bundlesize
      env:
        BUNDLESIZE_GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  # Testing Job
  test-suite:
    name: Comprehensive Testing
    runs-on: ubuntu-latest
    needs: quality-gates
    
    strategy:
      matrix:
        test-type: [unit, integration, e2e]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci --frozen-lockfile

    - name: Setup test environment
      run: |
        cp .env.example .env.test
        echo "VITE_SUPABASE_URL=${{ secrets.SUPABASE_TEST_URL }}" >> .env.test
        echo "VITE_SUPABASE_ANON_KEY=${{ secrets.SUPABASE_TEST_ANON_KEY }}" >> .env.test

    - name: Run unit tests
      if: matrix.test-type == 'unit'
      run: npm run test:coverage
      
    - name: Run integration tests  
      if: matrix.test-type == 'integration'
      run: npm run test -- --run integration

    - name: Install Playwright browsers
      if: matrix.test-type == 'e2e'
      run: npx playwright install --with-deps

    - name: Run E2E tests
      if: matrix.test-type == 'e2e'
      run: npm run test:e2e
      env:
        VITE_SUPABASE_URL: ${{ secrets.SUPABASE_TEST_URL }}
        VITE_SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_TEST_ANON_KEY }}

    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results-${{ matrix.test-type }}
        path: |
          coverage/
          test-results/
          playwright-report/

  # Performance Benchmarking
  performance-benchmark:
    name: Performance Benchmarks
    runs-on: ubuntu-latest
    needs: quality-gates
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci --frozen-lockfile

    - name: Build production bundle
      run: npm run build
      env:
        VITE_SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        VITE_SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}

    - name: Analyze bundle size
      run: |
        npx vite-bundle-analyzer dist --open=false --format=json > bundle-analysis.json

    - name: Performance audit with Lighthouse CI
      run: |
        npm install -g @lhci/cli@0.12.x
        lhci autorun
      env:
        LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

    - name: Check performance budgets
      run: |
        node scripts/check-performance-budgets.js

    - name: Upload performance artifacts
      uses: actions/upload-artifact@v4
      with:
        name: performance-results
        path: |
          bundle-analysis.json
          .lighthouseci/

  # Security Scanning
  security-scan:
    name: Security Analysis
    runs-on: ubuntu-latest
    needs: quality-gates
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v3
      with:
        sarif_file: 'trivy-results.sarif'

    - name: Snyk security scan
      uses: snyk/actions/node@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        args: --severity-threshold=high

  # Build and Deploy Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [test-suite, performance-benchmark, security-scan]
    if: github.ref == 'refs/heads/develop'
    
    environment:
      name: staging
      url: https://seafood-manager-staging.vercel.app
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci --frozen-lockfile

    - name: Build production bundle
      run: npm run build
      env:
        VITE_SUPABASE_URL: ${{ secrets.SUPABASE_STAGING_URL }}
        VITE_SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_STAGING_ANON_KEY }}
        VITE_OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY_STAGING }}

    - name: Deploy to Vercel Staging
      uses: vercel/action@v1
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-args: '--prod'
        vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
        vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}

    - name: Run staging smoke tests
      run: |
        npx playwright test --config=playwright.staging.config.ts
      env:
        STAGING_URL: https://seafood-manager-staging.vercel.app

  # Build and Deploy Production
  deploy-production:
    name: Deploy to Production  
    runs-on: ubuntu-latest
    needs: [test-suite, performance-benchmark, security-scan]
    if: github.ref == 'refs/heads/main'
    
    environment:
      name: production
      url: https://seafood-manager.vercel.app
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci --frozen-lockfile

    - name: Build production bundle
      run: npm run build
      env:
        VITE_SUPABASE_URL: ${{ secrets.SUPABASE_PRODUCTION_URL }}
        VITE_SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_PRODUCTION_ANON_KEY }}
        VITE_OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY_PRODUCTION }}

    - name: Deploy to Vercel Production
      uses: vercel/action@v1
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-args: '--prod'
        vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
        vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}

    - name: Run production health checks
      run: |
        npx playwright test --config=playwright.production.config.ts
      env:
        PRODUCTION_URL: https://seafood-manager.vercel.app

    - name: Notify deployment success
      uses: 8398a7/action-slack@v3
      with:
        status: success
        text: 'Seafood Manager successfully deployed to production!'
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # Monitoring Setup Post-Deploy
  setup-monitoring:
    name: Configure Production Monitoring
    runs-on: ubuntu-latest
    needs: deploy-production
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Configure DataDog monitoring
      run: |
        curl -X POST "https://api.datadoghq.com/api/v1/synthetics/tests" \
        -H "Content-Type: application/json" \
        -H "DD-API-KEY: ${{ secrets.DATADOG_API_KEY }}" \
        -H "DD-APPLICATION-KEY: ${{ secrets.DATADOG_APP_KEY }}" \
        -d '{
          "name": "Seafood Manager Health Check",
          "type": "api",
          "subtype": "http",
          "config": {
            "request": {
              "method": "GET",
              "url": "https://seafood-manager.vercel.app/health"
            },
            "assertions": [
              {
                "type": "statusCode",
                "operator": "is",
                "target": 200
              },
              {
                "type": "responseTime",
                "operator": "lessThan", 
                "target": 3000
              }
            ]
          },
          "locations": ["aws:us-east-1", "aws:eu-west-1"],
          "options": {
            "tick_every": 300
          }
        }'

    - name: Setup error tracking alerts
      run: |
        # Configure Sentry alerts via API
        curl -X POST "https://sentry.io/api/0/projects/${{ secrets.SENTRY_ORG }}/${{ secrets.SENTRY_PROJECT }}/rules/" \
        -H "Authorization: Bearer ${{ secrets.SENTRY_AUTH_TOKEN }}" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "High Error Rate Alert",
          "conditions": [
            {
              "id": "sentry.rules.conditions.event_frequency.EventFrequencyCondition",
              "value": 100,
              "interval": "1m"
            }
          ],
          "actions": [
            {
              "id": "sentry.rules.actions.notify_event_service.SlackNotifyServiceAction",
              "workspace": "${{ secrets.SLACK_WORKSPACE_ID }}",
              "channel": "#alerts"
            }
          ]
        }'