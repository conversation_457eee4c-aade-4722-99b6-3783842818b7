# Comprehensive CI/CD workflow for voice processing system testing
# Includes unit tests, integration tests, E2E tests, performance testing, and quality gates

name: Voice Processing System Tests

on:
  push:
    branches: [main, develop]
    paths:
      - 'src/components/voice/**'
      - 'src/services/*Voice*'
      - 'src/modules/voice-event-storage/**'
      - 'src/hooks/useVoice*'
      - 'src/test/**'
      - 'package.json'
      - 'vitest.config.ts'
      - 'playwright.config.ts'
  pull_request:
    branches: [main, develop]
    paths:
      - 'src/components/voice/**'
      - 'src/services/*Voice*'
      - 'src/modules/voice-event-storage/**'
      - 'src/hooks/useVoice*'
      - 'src/test/**'
      - 'package.json'
      - 'vitest.config.ts'
      - 'playwright.config.ts'

env:
  NODE_VERSION: '18'
  CI: true
  VITE_SUPABASE_URL: ${{ secrets.TEST_SUPABASE_URL || 'https://test.supabase.co' }}
  VITE_SUPABASE_ANON_KEY: ${{ secrets.TEST_SUPABASE_ANON_KEY || 'test-anon-key' }}
  VITE_OPENAI_API_KEY: ${{ secrets.TEST_OPENAI_API_KEY || 'test-openai-key' }}

jobs:
  # Pre-test setup and validation
  setup:
    name: Setup and Validation
    runs-on: ubuntu-latest
    outputs:
      cache-key: ${{ steps.cache-key.outputs.key }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Generate cache key
        id: cache-key
        run: echo "key=voice-tests-${{ hashFiles('package-lock.json', 'src/test/**/*', 'vitest.config.ts') }}" >> $GITHUB_OUTPUT

      - name: Install dependencies
        run: npm ci

      - name: Validate test configuration
        run: |
          npm run type-check
          node -e "
            const config = require('./src/test/voice-test-suite.config.ts');
            console.log('Test configuration validation passed');
            console.log('Environment:', config.TEST_ENV);
            console.log('Thresholds:', Object.keys(config.PERFORMANCE_THRESHOLDS));
          "

      - name: Cache test setup
        uses: actions/cache@v3
        with:
          path: |
            node_modules
            .npm
          key: ${{ steps.cache-key.outputs.key }}

  # Unit tests for voice processing components
  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    needs: setup
    strategy:
      matrix:
        test-group: ['voice-services', 'voice-components', 'voice-hooks']
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Restore cache
        uses: actions/cache@v3
        with:
          path: |
            node_modules
            .npm
          key: ${{ needs.setup.outputs.cache-key }}

      - name: Run unit tests
        run: |
          case "${{ matrix.test-group }}" in
            "voice-services")
              npm run test:unit -- --testPathPattern="services.*[Vv]oice.*test"
              ;;
            "voice-components")
              npm run test:unit -- --testPathPattern="components.*voice.*test"
              ;;
            "voice-hooks")
              npm run test:unit -- --testPathPattern="hooks.*[Vv]oice.*test"
              ;;
          esac

      - name: Generate coverage report
        run: npm run test:coverage -- --testPathPattern="${{ matrix.test-group }}"

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          flags: unit-tests-${{ matrix.test-group }}
          name: unit-coverage-${{ matrix.test-group }}

      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: unit-test-results-${{ matrix.test-group }}
          path: |
            test-results/unit/
            coverage/

  # Integration tests for voice-to-database workflows
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: setup
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_seafood_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Restore cache
        uses: actions/cache@v3
        with:
          path: |
            node_modules
            .npm
          key: ${{ needs.setup.outputs.cache-key }}

      - name: Setup test database
        run: |
          npm run db:test:setup
          npm run db:migrate:test

      - name: Run integration tests
        env:
          TEST_DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_seafood_db
        run: npm run test:integration

      - name: Run voice-database integration tests
        env:
          TEST_DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_seafood_db
        run: npm test -- src/test/integration/voice-database-comprehensive.test.ts

      - name: Generate integration test report
        run: |
          npm run test:integration -- --reporter=json > test-results/integration-results.json
          npm run test:integration -- --reporter=html

      - name: Upload integration test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: integration-test-results
          path: |
            test-results/integration/
            coverage/integration/

  # Performance tests for voice processing latency
  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: setup
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Restore cache
        uses: actions/cache@v3
        with:
          path: |
            node_modules
            .npm
          key: ${{ needs.setup.outputs.cache-key }}

      - name: Run performance tests
        run: npm run test:performance

      - name: Run voice processing performance tests
        run: npm test -- src/test/performance/voice-processing-performance.test.ts

      - name: Generate performance report
        run: |
          node scripts/generate-performance-report.js
          echo "## Performance Test Results" >> $GITHUB_STEP_SUMMARY
          cat test-results/performance-summary.md >> $GITHUB_STEP_SUMMARY

      - name: Check performance thresholds
        run: |
          node -e "
            const results = require('./test-results/performance-results.json');
            const config = require('./src/test/voice-test-suite.config.ts');
            
            for (const metric of results.metrics) {
              const threshold = config.PERFORMANCE_THRESHOLDS.VOICE_PROCESSING[metric.name + '_MAX_MS'];
              if (threshold && metric.value > threshold) {
                console.error(\`Performance threshold exceeded: \${metric.name} = \${metric.value}ms > \${threshold}ms\`);
                process.exit(1);
              }
            }
            console.log('All performance thresholds passed');
          "

      - name: Upload performance results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: performance-test-results
          path: |
            test-results/performance/
            performance-profiles/

  # End-to-End tests with Playwright
  e2e-tests:
    name: E2E Tests
    runs-on: ubuntu-latest
    needs: setup
    strategy:
      matrix:
        browser: [chromium, firefox, webkit]
        shard: [1/3, 2/3, 3/3]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Restore cache
        uses: actions/cache@v3
        with:
          path: |
            node_modules
            .npm
          key: ${{ needs.setup.outputs.cache-key }}

      - name: Install Playwright browsers
        run: npx playwright install --with-deps ${{ matrix.browser }}

      - name: Build application
        run: npm run build

      - name: Start test server
        run: |
          npm run preview &
          npx wait-on http://localhost:4173

      - name: Run E2E tests
        env:
          BROWSER: ${{ matrix.browser }}
        run: |
          npx playwright test src/test/e2e/voice-system-comprehensive.spec.ts \
            --project=${{ matrix.browser }} \
            --shard=${{ matrix.shard }}

      - name: Upload E2E test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: e2e-results-${{ matrix.browser }}-${{ strategy.job-index }}
          path: |
            test-results/e2e/
            playwright-report/

      - name: Upload trace files
        uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: playwright-traces-${{ matrix.browser }}-${{ strategy.job-index }}
          path: test-results/traces/

  # Cross-browser compatibility tests
  cross-browser-tests:
    name: Cross-Browser Tests
    runs-on: ubuntu-latest
    needs: setup
    strategy:
      matrix:
        browser: [chromium, firefox, webkit]
        device: [desktop, mobile, tablet]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Restore cache
        uses: actions/cache@v3
        with:
          path: |
            node_modules
            .npm
          key: ${{ needs.setup.outputs.cache-key }}

      - name: Install Playwright browsers
        run: npx playwright install --with-deps ${{ matrix.browser }}

      - name: Build application
        run: npm run build

      - name: Start test server
        run: |
          npm run preview &
          npx wait-on http://localhost:4173

      - name: Run cross-browser tests
        env:
          BROWSER: ${{ matrix.browser }}
          DEVICE: ${{ matrix.device }}
        run: |
          npx playwright test e2e/voice-browser-compatibility.spec.ts \
            --project=${{ matrix.browser }} \
            --grep="${{ matrix.device }}"

      - name: Upload compatibility test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: compatibility-results-${{ matrix.browser }}-${{ matrix.device }}
          path: test-results/compatibility/

  # Voice processing accuracy tests
  voice-accuracy-tests:
    name: Voice Accuracy Tests
    runs-on: ubuntu-latest
    needs: setup
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Restore cache
        uses: actions/cache@v3
        with:
          path: |
            node_modules
            .npm
          key: ${{ needs.setup.outputs.cache-key }}

      - name: Run voice transcription accuracy tests
        run: npm test -- src/test/voice/voice-transcription-accuracy.test.ts

      - name: Run voice user acceptance tests
        run: npm test -- src/test/voice/voice-user-acceptance.test.ts

      - name: Run voice regression tests
        run: npm test -- src/test/voice/voice-regression.test.ts

      - name: Generate accuracy report
        run: |
          node scripts/generate-accuracy-report.js
          echo "## Voice Accuracy Test Results" >> $GITHUB_STEP_SUMMARY
          cat test-results/voice-accuracy-summary.md >> $GITHUB_STEP_SUMMARY

      - name: Upload voice accuracy results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: voice-accuracy-results
          path: |
            test-results/voice-accuracy/
            voice-samples/

  # Quality gates and final validation
  quality-gates:
    name: Quality Gates
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, performance-tests, e2e-tests, voice-accuracy-tests]
    if: always()
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download all test artifacts
        uses: actions/download-artifact@v3
        with:
          path: all-test-results/

      - name: Consolidate test results
        run: |
          mkdir -p consolidated-results
          find all-test-results -name "*.json" -exec cp {} consolidated-results/ \;
          find all-test-results -name "coverage.json" -exec cp {} consolidated-results/coverage-{}.json \;

      - name: Calculate overall test metrics
        run: |
          node -e "
            const fs = require('fs');
            const path = require('path');
            
            const resultsDir = './consolidated-results';
            let totalTests = 0;
            let passedTests = 0;
            let coverage = 0;
            let coverageFiles = 0;
            
            fs.readdirSync(resultsDir).forEach(file => {
              if (file.endsWith('.json')) {
                const data = JSON.parse(fs.readFileSync(path.join(resultsDir, file)));
                if (data.numTotalTests) {
                  totalTests += data.numTotalTests;
                  passedTests += data.numPassedTests;
                }
                if (data.coverageMap && data.total) {
                  coverage += data.total.lines.pct || 0;
                  coverageFiles++;
                }
              }
            });
            
            const successRate = passedTests / totalTests;
            const avgCoverage = coverage / Math.max(coverageFiles, 1);
            
            console.log(\`Overall test success rate: \${(successRate * 100).toFixed(1)}%\`);
            console.log(\`Average test coverage: \${avgCoverage.toFixed(1)}%\`);
            
            const summary = {
              success_rate: successRate,
              coverage: avgCoverage,
              total_tests: totalTests,
              passed_tests: passedTests
            };
            
            fs.writeFileSync('./test-summary.json', JSON.stringify(summary, null, 2));
          "

      - name: Validate quality gates
        run: |
          node -e "
            const summary = require('./test-summary.json');
            const config = require('./src/test/voice-test-suite.config.ts');
            
            const minSuccessRate = config.PERFORMANCE_THRESHOLDS.QUALITY.E2E_TEST_SUCCESS_RATE_MIN;
            const minCoverage = config.PERFORMANCE_THRESHOLDS.QUALITY.UNIT_TEST_COVERAGE_MIN;
            
            console.log('Quality Gates Validation:');
            console.log(\`Success Rate: \${(summary.success_rate * 100).toFixed(1)}% (min: \${(minSuccessRate * 100)}%)\`);
            console.log(\`Coverage: \${summary.coverage.toFixed(1)}% (min: \${minCoverage}%)\`);
            
            if (summary.success_rate < minSuccessRate) {
              console.error('FAIL: Test success rate below threshold');
              process.exit(1);
            }
            
            if (summary.coverage < minCoverage) {
              console.error('FAIL: Test coverage below threshold');
              process.exit(1);
            }
            
            console.log('✓ All quality gates passed');
          "

      - name: Generate final test report
        run: |
          echo "# Voice Processing System Test Report" > TEST_REPORT.md
          echo "" >> TEST_REPORT.md
          echo "## Summary" >> TEST_REPORT.md
          cat test-summary.json | jq -r '"**Total Tests:** " + (.total_tests | tostring) + "  "' >> TEST_REPORT.md
          cat test-summary.json | jq -r '"**Passed Tests:** " + (.passed_tests | tostring) + "  "' >> TEST_REPORT.md
          cat test-summary.json | jq -r '"**Success Rate:** " + ((.success_rate * 100) | tostring | split(".")[0]) + "%  "' >> TEST_REPORT.md
          cat test-summary.json | jq -r '"**Coverage:** " + (.coverage | tostring | split(".")[0]) + "%  "' >> TEST_REPORT.md
          echo "" >> TEST_REPORT.md
          echo "## Test Results by Category" >> TEST_REPORT.md
          echo "- ✅ Unit Tests" >> TEST_REPORT.md
          echo "- ✅ Integration Tests" >> TEST_REPORT.md
          echo "- ✅ Performance Tests" >> TEST_REPORT.md
          echo "- ✅ E2E Tests" >> TEST_REPORT.md
          echo "- ✅ Voice Accuracy Tests" >> TEST_REPORT.md
          
          cat TEST_REPORT.md >> $GITHUB_STEP_SUMMARY

      - name: Upload consolidated results
        uses: actions/upload-artifact@v3
        with:
          name: consolidated-test-results
          path: |
            consolidated-results/
            TEST_REPORT.md
            test-summary.json

  # Deployment readiness check
  deployment-readiness:
    name: Deployment Readiness
    runs-on: ubuntu-latest
    needs: quality-gates
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download consolidated results
        uses: actions/download-artifact@v3
        with:
          name: consolidated-test-results
          path: ./

      - name: Check deployment readiness
        run: |
          node -e "
            const summary = require('./test-summary.json');
            const minSuccessRate = 0.95; // 95% for production deployment
            const minCoverage = 80; // 80% for production deployment
            
            console.log('Deployment Readiness Check:');
            console.log(\`Success Rate: \${(summary.success_rate * 100).toFixed(1)}%\`);
            console.log(\`Coverage: \${summary.coverage.toFixed(1)}%\`);
            
            if (summary.success_rate >= minSuccessRate && summary.coverage >= minCoverage) {
              console.log('✅ Ready for deployment');
              process.exit(0);
            } else {
              console.log('❌ Not ready for deployment');
              process.exit(1);
            }
          "

      - name: Create deployment tag
        if: success()
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git tag -a "voice-system-tested-$(date +%Y%m%d-%H%M%S)" -m "Voice system passed all quality gates"
          git push origin --tags