name: CI/CD Production Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

env:
  NODE_VERSION: '18.x'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Code Quality and Security Analysis
  quality-security:
    name: Code Quality & Security Analysis
    runs-on: ubuntu-latest
    permissions:
      contents: read
      security-events: write
      actions: read
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: |
          npm ci --prefer-offline --no-audit
          npm run type-check

      - name: Run code quality checks
        run: |
          npm run quality:check
          npm run performance:check

      - name: Security audit
        run: |
          npm audit --audit-level moderate
          npx audit-ci --moderate

      - name: CodeQL Analysis
        uses: github/codeql-action/init@v3
        with:
          languages: javascript

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3

      - name: Upload code quality results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: code-quality-results
          path: |
            eslint-report.json
            type-check-results.txt

  # Comprehensive Testing Suite
  test-suite:
    name: Comprehensive Testing
    runs-on: ubuntu-latest
    needs: quality-security
    
    strategy:
      matrix:
        test-type: [unit, integration, voice, performance]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci --prefer-offline --no-audit

      - name: Setup test environment
        run: |
          cp .env.example .env.test
          echo "NODE_ENV=test" >> .env.test

      - name: Run tests based on matrix
        run: |
          case "${{ matrix.test-type }}" in
            "unit")
              npm run test:unit -- --coverage --reporter=json --outputFile=coverage/unit-coverage.json
              ;;
            "integration")
              npm run test:integration -- --coverage --reporter=json --outputFile=coverage/integration-coverage.json
              ;;
            "voice")
              npm run test:voice:complete -- --coverage --reporter=json --outputFile=coverage/voice-coverage.json
              ;;
            "performance")
              npm run test:performance -- --reporter=json --outputFile=test-results/performance-results.json
              ;;
          esac

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-results-${{ matrix.test-type }}
          path: |
            coverage/
            test-results/

  # End-to-End Testing
  e2e-testing:
    name: End-to-End Testing
    runs-on: ubuntu-latest
    needs: test-suite
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci --prefer-offline --no-audit

      - name: Install Playwright browsers
        run: npx playwright install --with-deps

      - name: Build application
        run: npm run build
        env:
          NODE_ENV: test

      - name: Start application
        run: |
          npm run preview &
          sleep 10

      - name: Run E2E tests
        run: |
          npm run test:e2e
          npm run test:e2e:voice:browsers

      - name: Upload E2E results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: e2e-test-results
          path: |
            playwright-report/
            test-results/

  # Build and Performance Analysis
  build-analysis:
    name: Build & Performance Analysis
    runs-on: ubuntu-latest
    needs: quality-security
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci --prefer-offline --no-audit

      - name: Build for production
        run: |
          npm run build:production
          npm run build:analyze

      - name: Bundle size analysis
        run: |
          npx bundlesize
          echo "Build size analysis completed"

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: production-build
          path: |
            dist/
            bundle-analysis/

  # Security Scanning
  security-scan:
    name: Security Vulnerability Scan
    runs-on: ubuntu-latest
    needs: build-analysis
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

      - name: OWASP ZAP security scan
        if: github.ref == 'refs/heads/main'
        uses: zaproxy/action-full-scan@v0.10.0
        with:
          target: 'https://seafood-manager-staging.vercel.app'

  # Staging Deployment
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [test-suite, e2e-testing, build-analysis, security-scan]
    if: github.ref == 'refs/heads/develop' || github.event_name == 'workflow_dispatch'
    environment: staging
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install Vercel CLI
        run: npm install -g vercel@latest

      - name: Deploy to Vercel Staging
        run: |
          vercel pull --yes --environment=preview --token=${{ secrets.VERCEL_TOKEN }}
          vercel build --token=${{ secrets.VERCEL_TOKEN }}
          vercel deploy --prebuilt --token=${{ secrets.VERCEL_TOKEN }} > deployment-url.txt
        env:
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

      - name: Run Lighthouse CI
        run: |
          npm install -g @lhci/cli
          DEPLOYMENT_URL=$(cat deployment-url.txt)
          echo "LHCI_SERVER_URL=$DEPLOYMENT_URL" >> $GITHUB_ENV
          npm run performance:lighthouse

      - name: Comment PR with deployment info
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const deploymentUrl = fs.readFileSync('deployment-url.txt', 'utf8').trim();
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `## 🚀 Staging Deployment\n\n✅ Successfully deployed to: ${deploymentUrl}\n\n### Performance Report\nLighthouse CI results will be available shortly.`
            });

  # Production Deployment
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [test-suite, e2e-testing, build-analysis, security-scan]
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install Vercel CLI
        run: npm install -g vercel@latest

      - name: Deploy to Vercel Production
        run: |
          vercel pull --yes --environment=production --token=${{ secrets.VERCEL_TOKEN }}
          vercel build --prod --token=${{ secrets.VERCEL_TOKEN }}
          vercel deploy --prebuilt --prod --token=${{ secrets.VERCEL_TOKEN }} > deployment-url.txt
        env:
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

      - name: Database migration (Production)
        run: |
          echo "Running production database migrations"
          npm run db:migrate:all
        env:
          SUPABASE_URL: ${{ secrets.SUPABASE_PROD_URL }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_PROD_SERVICE_ROLE_KEY }}

      - name: Post-deployment health checks
        run: |
          DEPLOYMENT_URL=$(cat deployment-url.txt)
          echo "Running health checks on $DEPLOYMENT_URL"
          curl -f "$DEPLOYMENT_URL/health" || exit 1

      - name: Create release
        if: success()
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: v${{ github.run_number }}
          release_name: Production Release v${{ github.run_number }}
          body: |
            ## Production Deployment v${{ github.run_number }}
            
            ### Changes in this release:
            ${{ github.event.head_commit.message }}
            
            ### Deployment Details:
            - **Environment**: Production
            - **Commit**: ${{ github.sha }}
            - **Deployed at**: $(date)
            - **URL**: $(cat deployment-url.txt)

  # Monitoring Setup
  post-deploy-monitoring:
    name: Setup Monitoring & Alerts
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always() && (needs.deploy-staging.result == 'success' || needs.deploy-production.result == 'success')
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup monitoring
        run: |
          echo "Configuring monitoring and alerting"
          npm run monitoring:setup
        env:
          DATADOG_API_KEY: ${{ secrets.DATADOG_API_KEY }}
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}

      - name: Configure alerts
        run: |
          echo "Setting up production alerts"
          # Configure uptime monitoring, error rate alerts, performance budgets