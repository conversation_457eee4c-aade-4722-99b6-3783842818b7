// Test basic insert with current schema
import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

const supabaseUrl = process.env.VITE_SUPABASE_URL
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function testBasicInsert() {
  console.log('🧪 Testing Basic Insert with Current Schema')
  console.log('==========================================\n')

  try {
    // Test what we can insert with minimal data
    console.log('1. Testing minimal event insert...')
    
    const testEvent = {
      event_type: 'receiving',
      name: 'Test Salmon',
      quantity: 10,
      unit_price: 15.00,
      total_amount: 150.00,
      notes: 'Test event for debugging',
      metadata: { 
        unit: 'lbs',
        test: true
      }
    }

    const { data, error } = await supabase
      .from('inventory_events')
      .insert(testEvent)
      .select()

    if (error) {
      console.error('❌ Insert failed:', error.message)
      console.error('   Details:', error)
      
      // Try to understand the RLS policy
      console.log('\n2. Checking RLS policies...')
      const { data: user } = await supabase.auth.getUser()
      console.log('   Current user:', user?.user?.email || 'Anonymous')
      
    } else {
      console.log('✅ Insert successful!')
      console.log('   Event ID:', data[0].id)
      
      // Now test the EventsTable query
      console.log('\n3. Testing EventsTable query...')
      const { data: events, error: queryError } = await supabase
        .from('inventory_events')
        .select(`
          id,
          created_at,
          event_type,
          quantity,
          unit_price,
          total_amount,
          notes,
          metadata,
          product_id,
          name
        `)
        .order('created_at', { ascending: false })
        .limit(5)
      
      if (queryError) {
        console.error('❌ Query failed:', queryError.message)
      } else {
        console.log(`✅ Query successful! Found ${events.length} events`)
        events.forEach(event => {
          console.log(`   - ${event.event_type}: ${event.name} (${event.quantity} units)`)
        })
      }
    }

  } catch (error) {
    console.error('💥 Unexpected error:', error)
  }
}

testBasicInsert().then(() => {
  console.log('\n🏁 Test complete')
  process.exit(0)
}).catch(error => {
  console.error('\n💥 Test failed:', error)
  process.exit(1)
})