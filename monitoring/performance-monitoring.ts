// Production Performance Monitoring & Observability
// Comprehensive monitoring setup for Seafood Manager

export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, any> = new Map();
  private startTimes: Map<string, number> = new Map();

  static getInstance(): PerformanceMonitor {
    if (!this.instance) {
      this.instance = new PerformanceMonitor();
    }
    return this.instance;
  }

  // Core Web Vitals monitoring
  initWebVitalsMonitoring() {
    if (typeof window === 'undefined') return;

    // Largest Contentful Paint (LCP)
    new PerformanceObserver((entryList) => {
      for (const entry of entryList.getEntries()) {
        this.trackMetric('lcp', entry.startTime, {
          element: entry.element?.tagName,
          url: entry.url
        });
      }
    }).observe({ entryTypes: ['largest-contentful-paint'] });

    // First Input Delay (FID)
    new PerformanceObserver((entryList) => {
      for (const entry of entryList.getEntries()) {
        this.trackMetric('fid', entry.processingStart - entry.startTime, {
          name: entry.name,
          target: entry.target?.tagName
        });
      }
    }).observe({ entryTypes: ['first-input'] });

    // Cumulative Layout Shift (CLS)
    new PerformanceObserver((entryList) => {
      let clsValue = 0;
      for (const entry of entryList.getEntries()) {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
        }
      }
      if (clsValue > 0) {
        this.trackMetric('cls', clsValue);
      }
    }).observe({ entryTypes: ['layout-shift'] });
  }

  // Business-specific performance tracking
  startTimer(operation: string): void {
    this.startTimes.set(operation, performance.now());
  }

  endTimer(operation: string, metadata?: Record<string, any>): number {
    const startTime = this.startTimes.get(operation);
    if (!startTime) return 0;

    const duration = performance.now() - startTime;
    this.startTimes.delete(operation);
    
    this.trackMetric(operation, duration, metadata);
    return duration;
  }

  // Track custom metrics
  trackMetric(name: string, value: number, metadata?: Record<string, any>): void {
    const metric = {
      name,
      value,
      timestamp: Date.now(),
      metadata: metadata || {},
      url: window.location.pathname,
      userAgent: navigator.userAgent
    };

    this.metrics.set(`${name}_${Date.now()}`, metric);
    
    // Send to monitoring services
    this.sendToDataDog(metric);
    this.sendToSentry(metric);
  }

  // DataDog integration
  private sendToDataDog(metric: any): void {
    if (typeof window === 'undefined' || !window.DD_RUM) return;

    window.DD_RUM.addTiming(metric.name, metric.value);
    
    if (metric.metadata) {
      window.DD_RUM.addAttribute('custom_metadata', metric.metadata);
    }
  }

  // Sentry integration
  private sendToSentry(metric: any): void {
    if (typeof window === 'undefined' || !window.Sentry) return;

    window.Sentry.addBreadcrumb({
      category: 'performance',
      message: `${metric.name}: ${metric.value}ms`,
      level: 'info',
      data: metric.metadata
    });

    // Send performance data to Sentry
    window.Sentry.setMeasurement(metric.name, metric.value, 'millisecond');
  }

  // Get performance summary
  getPerformanceSummary(): Record<string, any> {
    const summary: Record<string, any> = {};
    
    for (const [key, metric] of this.metrics.entries()) {
      const metricName = metric.name;
      if (!summary[metricName]) {
        summary[metricName] = {
          count: 0,
          total: 0,
          avg: 0,
          min: Infinity,
          max: -Infinity
        };
      }
      
      summary[metricName].count++;
      summary[metricName].total += metric.value;
      summary[metricName].avg = summary[metricName].total / summary[metricName].count;
      summary[metricName].min = Math.min(summary[metricName].min, metric.value);
      summary[metricName].max = Math.max(summary[metricName].max, metric.value);
    }
    
    return summary;
  }
}

// Seafood-specific business metrics
export class SeafoodBusinessMetrics {
  private monitor = PerformanceMonitor.getInstance();

  // Voice processing performance
  trackVoiceProcessing(
    operation: 'transcription' | 'ai_processing' | 'command_execution',
    duration: number,
    metadata?: {
      accuracy?: number;
      confidence?: number;
      species?: string;
      success?: boolean;
    }
  ): void {
    this.monitor.trackMetric(`voice_${operation}`, duration, {
      ...metadata,
      business_function: 'voice_inventory'
    });
  }

  // Inventory operations performance
  trackInventoryOperation(
    operation: 'receiving' | 'sales' | 'adjustment' | 'disposal',
    duration: number,
    metadata?: {
      product_count?: number;
      batch_size?: number;
      success?: boolean;
    }
  ): void {
    this.monitor.trackMetric(`inventory_${operation}`, duration, {
      ...metadata,
      business_function: 'inventory_management'
    });
  }

  // CSV import performance
  trackCsvImport(
    stage: 'parsing' | 'validation' | 'processing' | 'saving',
    duration: number,
    metadata?: {
      row_count?: number;
      file_size?: number;
      success_rate?: number;
      errors?: number;
    }
  ): void {
    this.monitor.trackMetric(`csv_import_${stage}`, duration, {
      ...metadata,
      business_function: 'data_import'
    });
  }

  // HACCP compliance tracking
  trackHACCPOperation(
    operation: 'temperature_check' | 'compliance_validation' | 'report_generation',
    duration: number,
    metadata?: {
      compliance_status?: 'pass' | 'fail' | 'warning';
      temperature?: number;
      product_category?: string;
    }
  ): void {
    this.monitor.trackMetric(`haccp_${operation}`, duration, {
      ...metadata,
      business_function: 'compliance'
    });
  }

  // Database query performance
  trackDatabaseQuery(
    queryType: 'select' | 'insert' | 'update' | 'delete',
    table: string,
    duration: number,
    metadata?: {
      row_count?: number;
      uses_rls?: boolean;
      uses_index?: boolean;
    }
  ): void {
    this.monitor.trackMetric(`db_${queryType}_${table}`, duration, {
      ...metadata,
      business_function: 'database'
    });
  }
}

// Error tracking and alerting
export class ErrorTracker {
  // Track business-critical errors
  static trackBusinessError(
    errorType: 'voice_failure' | 'inventory_sync_failure' | 'compliance_violation' | 'data_corruption',
    error: Error,
    context?: Record<string, any>
  ): void {
    const errorData = {
      type: errorType,
      message: error.message,
      stack: error.stack,
      timestamp: Date.now(),
      context: context || {},
      url: window.location.href,
      userId: context?.userId
    };

    // Send to error tracking services
    if (window.Sentry) {
      window.Sentry.captureException(error, {
        tags: {
          error_type: errorType,
          business_critical: true
        },
        extra: context
      });
    }

    // Send critical alerts
    if (this.isCriticalError(errorType)) {
      this.sendCriticalAlert(errorData);
    }
  }

  private static isCriticalError(errorType: string): boolean {
    return [
      'compliance_violation',
      'data_corruption',
      'inventory_sync_failure'
    ].includes(errorType);
  }

  private static sendCriticalAlert(errorData: any): void {
    // Implementation would integrate with PagerDuty, Slack, etc.
    console.error('CRITICAL ERROR:', errorData);
    
    // Example Slack webhook notification
    if (process.env.VITE_SLACK_WEBHOOK_URL) {
      fetch(process.env.VITE_SLACK_WEBHOOK_URL, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          text: `🚨 CRITICAL ERROR in Seafood Manager: ${errorData.type}`,
          attachments: [{
            color: 'danger',
            fields: [
              { title: 'Error Type', value: errorData.type, short: true },
              { title: 'Message', value: errorData.message, short: false },
              { title: 'URL', value: errorData.url, short: true },
              { title: 'User ID', value: errorData.userId || 'Unknown', short: true }
            ]
          }]
        })
      }).catch(console.error);
    }
  }
}

// Performance budget enforcement
export class PerformanceBudget {
  private static readonly BUDGETS = {
    // Page load performance budgets (milliseconds)
    page_load: {
      dashboard: 3000,
      inventory: 2500,
      import: 4000,
      analytics: 3500
    },
    
    // API response budgets (milliseconds)
    api_response: {
      inventory_list: 500,
      product_search: 300,
      voice_processing: 2000,
      csv_import: 30000
    },
    
    // Bundle size budgets (bytes)
    bundle_size: {
      main_bundle: 1000000,      // 1MB
      vendor_chunks: 800000,     // 800KB
      css_bundle: 50000          // 50KB
    }
  };

  static checkBudget(metric: string, value: number, category: keyof typeof PerformanceBudget.BUDGETS): boolean {
    const budget = this.BUDGETS[category][metric as keyof typeof this.BUDGETS[typeof category]];
    
    if (!budget) return true;
    
    const isWithinBudget = value <= budget;
    
    if (!isWithinBudget) {
      console.warn(`Performance budget exceeded for ${metric}: ${value} > ${budget}`);
      
      // Track budget violations
      PerformanceMonitor.getInstance().trackMetric('budget_violation', value, {
        metric,
        budget,
        category,
        overrun_percentage: ((value - budget) / budget) * 100
      });
    }
    
    return isWithinBudget;
  }
}

// Initialize monitoring in production
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
  const monitor = PerformanceMonitor.getInstance();
  monitor.initWebVitalsMonitoring();
  
  // Initialize DataDog RUM
  if (process.env.VITE_DATADOG_CLIENT_TOKEN) {
    import('@datadog/browser-rum').then(({ datadogRum }) => {
      datadogRum.init({
        applicationId: process.env.VITE_DATADOG_APPLICATION_ID!,
        clientToken: process.env.VITE_DATADOG_CLIENT_TOKEN!,
        site: 'datadoghq.com',
        service: 'seafood-manager',
        env: process.env.NODE_ENV,
        version: process.env.VITE_APP_VERSION || '1.0.0',
        sessionSampleRate: 100,
        sessionReplaySampleRate: 20,
        trackUserInteractions: true,
        trackResources: true,
        trackLongTasks: true,
        defaultPrivacyLevel: 'mask-user-input'
      });
    });
  }
  
  // Initialize Sentry
  if (process.env.VITE_SENTRY_DSN) {
    import('@sentry/browser').then(Sentry => {
      Sentry.init({
        dsn: process.env.VITE_SENTRY_DSN,
        environment: process.env.NODE_ENV,
        tracesSampleRate: 0.1,
        beforeSend(event) {
          // Filter out non-critical errors in production
          if (event.level === 'warning') return null;
          return event;
        }
      });
    });
  }
}