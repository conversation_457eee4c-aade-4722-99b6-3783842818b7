# Monitoring Alerts Configuration for Seafood Manager
# Comprehensive alerting for production environment

alerts:
  # Performance Alerts
  - name: "High Page Load Time"
    type: "metric alert"
    query: "avg(last_5m):avg:browser.page.load_time{service:seafood-manager,env:production} > 3000"
    message: |
      🚨 **High Page Load Time Detected**
      
      The average page load time has exceeded 3 seconds for the last 5 minutes.
      This affects user experience and seafood inventory operations.
      
      **Current Value**: {{value}}ms
      **Threshold**: 3000ms
      **Page**: {{page.name}}
      
      **Immediate Actions**:
      - Check CDN performance
      - Review recent deployments
      - Monitor database response times
      
      @slack-alerts @pagerduty-seafood-team
    tags:
      - service:seafood-manager
      - alert-type:performance
      - severity:warning
    thresholds:
      critical: 5000
      warning: 3000
    
  - name: "Core Web Vitals Degradation"
    type: "metric alert"
    query: "avg(last_10m):avg:rum.largest_contentful_paint{service:seafood-manager,env:production} > 2500"
    message: |
      🚨 **Core Web Vitals Threshold Exceeded**
      
      Largest Contentful Paint (LCP) has exceeded the 2.5s threshold.
      This impacts SEO rankings and user experience.
      
      **Current LCP**: {{value}}ms
      **Threshold**: 2500ms
      
      **Check**:
      - Bundle size and code splitting
      - Image optimization
      - Server response times
      
      @slack-alerts @devops-team
    tags:
      - service:seafood-manager
      - alert-type:performance
      - severity:critical

  # Business Logic Alerts
  - name: "High Voice Processing Failure Rate"
    type: "metric alert"
    query: "avg(last_15m):sum:seafood.voice.processing{status:error,env:production}.as_rate() > 0.1"
    message: |
      🚨 **Voice Processing Failures**
      
      Voice processing failure rate has exceeded 10% over the last 15 minutes.
      This directly impacts seafood inventory data entry efficiency.
      
      **Failure Rate**: {{value}}%
      **Threshold**: 10%
      
      **Potential Causes**:
      - OpenAI API issues
      - Network connectivity problems
      - Audio quality issues
      - Species recognition model problems
      
      **Action Required**: Check OpenAI service status and review error logs
      
      @slack-alerts @voice-processing-team @on-call
    tags:
      - service:seafood-manager
      - alert-type:business-critical
      - severity:critical

  - name: "HACCP Compliance Violations"
    type: "metric alert"
    query: "sum(last_30m):sum:seafood.haccp.compliance{status:fail,env:production}.as_count() > 5"
    message: |
      🚨 **CRITICAL: HACCP Compliance Violations**
      
      Multiple HACCP compliance failures detected in the last 30 minutes.
      This is a CRITICAL food safety issue requiring immediate attention.
      
      **Failed Checks**: {{value}}
      **Threshold**: 5 failures
      
      **IMMEDIATE ACTIONS REQUIRED**:
      1. Review temperature monitoring systems
      2. Check equipment calibration
      3. Validate data entry procedures
      4. Contact quality assurance team
      
      **Compliance Officer**: @haccp-compliance-team
      **Emergency Contact**: @food-safety-emergency
      
      @slack-critical @pagerduty-compliance @sms-alerts
    tags:
      - service:seafood-manager
      - alert-type:compliance
      - severity:critical
      - escalation:immediate

  # Database Performance Alerts
  - name: "Slow Database Queries"
    type: "metric alert"
    query: "avg(last_10m):avg:supabase.query.duration{service:seafood-manager,env:production} > 500"
    message: |
      ⚠️ **Database Performance Degradation**
      
      Average database query time has exceeded 500ms threshold.
      This affects inventory operations and user experience.
      
      **Current Duration**: {{value}}ms
      **Threshold**: 500ms
      **Table**: {{table.name}}
      
      **Investigate**:
      - Check for missing indexes
      - Review RLS policy performance
      - Monitor connection pool usage
      - Check for long-running queries
      
      @slack-alerts @database-team
    tags:
      - service:seafood-manager
      - alert-type:database
      - severity:warning

  # CSV Import Alerts
  - name: "CSV Import Processing Failures"
    type: "metric alert"
    query: "avg(last_1h):sum:seafood.csv.import{status:error,env:production}.as_rate() > 0.2"
    message: |
      🚨 **CSV Import System Issues**
      
      CSV import failure rate has exceeded 20% in the last hour.
      This affects bulk inventory data processing capabilities.
      
      **Failure Rate**: {{value}}%
      **Threshold**: 20%
      
      **Common Issues**:
      - File format validation errors
      - Data mapping problems
      - Memory/timeout issues with large files
      - Database constraint violations
      
      @slack-alerts @data-processing-team
    tags:
      - service:seafood-manager
      - alert-type:data-processing
      - severity:warning

  # Infrastructure Alerts
  - name: "High Error Rate"
    type: "metric alert"
    query: "avg(last_5m):sum:rum.error_count{service:seafood-manager,env:production}.as_rate() > 0.05"
    message: |
      🚨 **High Application Error Rate**
      
      Application error rate has exceeded 5% threshold.
      
      **Error Rate**: {{value}}%
      **Threshold**: 5%
      
      **Actions**:
      - Check recent deployments
      - Review error logs in Sentry
      - Monitor third-party service status
      
      @slack-alerts @on-call
    tags:
      - service:seafood-manager
      - alert-type:reliability
      - severity:critical

  - name: "Memory Usage High"
    type: "metric alert"
    query: "avg(last_10m):avg:system.mem.pct_usable{service:seafood-manager,env:production} < 0.2"
    message: |
      ⚠️ **High Memory Usage**
      
      Available memory has dropped below 20%.
      
      **Available Memory**: {{value}}%
      **Threshold**: 20%
      
      **Investigate**:
      - Memory leaks in application
      - Large dataset processing
      - Container resource limits
      
      @slack-alerts @infrastructure-team
    tags:
      - service:seafood-manager
      - alert-type:infrastructure
      - severity:warning

  # Security Alerts
  - name: "Suspicious Login Activity"
    type: "log alert"
    query: "source:supabase status:error @message:\"Authentication failed\" | rate > 10"
    message: |
      🚨 **SECURITY ALERT: Suspicious Login Activity**
      
      Multiple authentication failures detected.
      Potential brute force attack or credential stuffing.
      
      **Failed Attempts**: {{value}} per minute
      **Threshold**: 10 per minute
      
      **Immediate Actions**:
      - Check source IP addresses
      - Review authentication logs
      - Consider temporary IP blocking
      - Notify security team
      
      @slack-security @security-team @on-call
    tags:
      - service:seafood-manager
      - alert-type:security
      - severity:critical

# Notification Channels
notification_channels:
  slack-alerts:
    type: slack
    webhook_url: "${SLACK_ALERTS_WEBHOOK}"
    channel: "#seafood-manager-alerts"
    
  slack-critical:
    type: slack
    webhook_url: "${SLACK_CRITICAL_WEBHOOK}"
    channel: "#critical-alerts"
    
  slack-security:
    type: slack
    webhook_url: "${SLACK_SECURITY_WEBHOOK}"
    channel: "#security-alerts"
    
  pagerduty-seafood-team:
    type: pagerduty
    service_key: "${PAGERDUTY_SEAFOOD_SERVICE_KEY}"
    
  pagerduty-compliance:
    type: pagerduty
    service_key: "${PAGERDUTY_COMPLIANCE_SERVICE_KEY}"
    escalation_policy: "HACCP_CRITICAL"
    
  sms-alerts:
    type: webhook
    url: "${SMS_WEBHOOK_URL}"
    method: POST

# Alert Schedules
schedules:
  business_hours:
    timezone: "America/Los_Angeles"
    days: ["monday", "tuesday", "wednesday", "thursday", "friday"]
    start_time: "08:00"
    end_time: "18:00"
    
  compliance_monitoring:
    timezone: "America/Los_Angeles"
    days: ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]
    start_time: "00:00"
    end_time: "23:59"
    description: "24/7 monitoring for food safety compliance"

# Escalation Policies
escalation_policies:
  - name: "Critical Production Issues"
    alerts: ["High Page Load Time", "High Error Rate", "High Voice Processing Failure Rate"]
    levels:
      - delay: 0
        targets: ["@on-call", "slack-alerts"]
      - delay: 300  # 5 minutes
        targets: ["@team-lead", "pagerduty-seafood-team"]
      - delay: 900  # 15 minutes
        targets: ["@engineering-manager", "sms-alerts"]
        
  - name: "HACCP Compliance Emergency"
    alerts: ["HACCP Compliance Violations"]
    levels:
      - delay: 0
        targets: ["@haccp-compliance-team", "slack-critical", "pagerduty-compliance"]
      - delay: 60   # 1 minute
        targets: ["@food-safety-emergency", "sms-alerts"]
      - delay: 180  # 3 minutes
        targets: ["@quality-assurance-director"]

# Maintenance Windows
maintenance_windows:
  - name: "Weekly Maintenance"
    schedule: "0 2 * * 0"  # Every Sunday at 2 AM
    duration: 120  # 2 hours
    alerts_suppressed: ["High Page Load Time", "Slow Database Queries"]
    
  - name: "Database Maintenance"
    schedule: "0 3 1 * *"  # First day of month at 3 AM
    duration: 240  # 4 hours
    alerts_suppressed: ["Slow Database Queries", "High Error Rate"]