{"title": "Seafood Manager - Production Monitoring Dashboard", "description": "Comprehensive monitoring for Pacific Cloud Seafoods Manager application", "layout_type": "ordered", "template_variables": [{"name": "env", "prefix": "env", "available_values": ["production", "staging", "development"], "default": "production"}, {"name": "service", "prefix": "service", "available_values": ["seafood-manager", "seafood-manager-api"], "default": "seafood-manager"}], "widgets": [{"id": 1, "definition": {"type": "timeseries", "requests": [{"q": "avg:browser.page.load_time{service:$service,env:$env} by {page}", "display_type": "line", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}}], "title": "<PERSON> Load Performance", "title_size": "16", "title_align": "left", "yaxis": {"label": "Load Time (ms)", "scale": "linear", "min": "auto", "max": "auto", "include_zero": true}, "legend": {"show_legend": true, "legend_size": "0"}}, "layout": {"x": 0, "y": 0, "width": 6, "height": 3}}, {"id": 2, "definition": {"type": "timeseries", "requests": [{"q": "avg:rum.largest_contentful_paint{service:$service,env:$env}", "display_type": "line", "style": {"palette": "orange", "line_type": "solid", "line_width": "normal"}}, {"q": "avg:rum.first_input_delay{service:$service,env:$env}", "display_type": "line", "style": {"palette": "purple", "line_type": "solid", "line_width": "normal"}}, {"q": "avg:rum.cumulative_layout_shift{service:$service,env:$env}", "display_type": "line", "style": {"palette": "green", "line_type": "solid", "line_width": "normal"}}], "title": "Core Web Vitals", "title_size": "16", "title_align": "left", "yaxis": {"label": "Time (ms) / Score", "scale": "linear"}, "markers": [{"value": "y = 2500", "display_type": "error dashed", "label": "LCP Threshold"}, {"value": "y = 100", "display_type": "warning dashed", "label": "FID Threshold"}]}, "layout": {"x": 6, "y": 0, "width": 6, "height": 3}}, {"id": 3, "definition": {"type": "timeseries", "requests": [{"q": "sum:seafood.inventory.operation{operation:receiving,env:$env}.as_count()", "display_type": "bars", "style": {"palette": "green"}}, {"q": "sum:seafood.inventory.operation{operation:sales,env:$env}.as_count()", "display_type": "bars", "style": {"palette": "blue"}}, {"q": "sum:seafood.inventory.operation{operation:disposal,env:$env}.as_count()", "display_type": "bars", "style": {"palette": "red"}}], "title": "Inventory Operations Volume", "title_size": "16", "title_align": "left", "yaxis": {"label": "Operations Count", "scale": "linear"}}, "layout": {"x": 0, "y": 3, "width": 6, "height": 3}}, {"id": 4, "definition": {"type": "timeseries", "requests": [{"q": "avg:seafood.voice.processing_time{env:$env} by {operation}", "display_type": "line", "style": {"palette": "purple", "line_type": "solid", "line_width": "normal"}}], "title": "Voice Processing Performance", "title_size": "16", "title_align": "left", "yaxis": {"label": "Processing Time (ms)", "scale": "linear"}, "markers": [{"value": "y = 2000", "display_type": "error dashed", "label": "Target Response Time"}]}, "layout": {"x": 6, "y": 3, "width": 6, "height": 3}}, {"id": 5, "definition": {"type": "timeseries", "requests": [{"q": "avg:supabase.query.duration{service:$service,env:$env} by {table}", "display_type": "line", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}}], "title": "Database Query Performance", "title_size": "16", "title_align": "left", "yaxis": {"label": "Query Duration (ms)", "scale": "linear"}, "markers": [{"value": "y = 100", "display_type": "warning dashed", "label": "Target Query Time"}]}, "layout": {"x": 0, "y": 6, "width": 6, "height": 3}}, {"id": 6, "definition": {"type": "timeseries", "requests": [{"q": "sum:seafood.csv.import{stage:parsing,env:$env}.as_count()", "display_type": "bars", "style": {"palette": "blue"}}, {"q": "sum:seafood.csv.import{stage:processing,env:$env}.as_count()", "display_type": "bars", "style": {"palette": "orange"}}, {"q": "sum:seafood.csv.import{stage:saving,env:$env}.as_count()", "display_type": "bars", "style": {"palette": "green"}}], "title": "CSV Import Processing", "title_size": "16", "title_align": "left", "yaxis": {"label": "Import Count", "scale": "linear"}}, "layout": {"x": 6, "y": 6, "width": 6, "height": 3}}, {"id": 7, "definition": {"type": "timeseries", "requests": [{"q": "sum:seafood.haccp.compliance{status:pass,env:$env}.as_count()", "display_type": "bars", "style": {"palette": "green"}}, {"q": "sum:seafood.haccp.compliance{status:fail,env:$env}.as_count()", "display_type": "bars", "style": {"palette": "red"}}, {"q": "sum:seafood.haccp.compliance{status:warning,env:$env}.as_count()", "display_type": "bars", "style": {"palette": "orange"}}], "title": "HACCP Compliance Status", "title_size": "16", "title_align": "left", "yaxis": {"label": "Compliance Checks", "scale": "linear"}}, "layout": {"x": 0, "y": 9, "width": 6, "height": 3}}, {"id": 8, "definition": {"type": "query_value", "requests": [{"q": "sum:rum.error_count{service:$service,env:$env}.as_count()", "aggregator": "sum"}], "title": "Error Rate (24h)", "title_size": "16", "title_align": "left", "autoscale": true, "precision": 0, "text_align": "center"}, "layout": {"x": 6, "y": 9, "width": 3, "height": 3}}, {"id": 9, "definition": {"type": "query_value", "requests": [{"q": "avg:rum.session.time_spent{service:$service,env:$env}", "aggregator": "avg"}], "title": "Avg Session Duration", "title_size": "16", "title_align": "left", "autoscale": true, "precision": 0, "text_align": "center"}, "layout": {"x": 9, "y": 9, "width": 3, "height": 3}}], "notify_list": [], "reflow_type": "fixed"}