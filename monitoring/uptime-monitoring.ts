// Uptime and Health Monitoring for Seafood Manager
// Comprehensive health checks and uptime monitoring

// Extend Window interface for Datadog RUM
declare global {
  interface Window {
    DD_RUM?: {
      addAttribute: (key: string, value: any) => void;
    };
  }
}

export interface HealthCheck {
  name: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime: number;
  message?: string;
  metadata?: Record<string, any>;
}

export interface UptimeMetrics {
  uptime: number;
  downtime: number;
  availability: number;
  mttr: number; // Mean Time To Recovery
  mtbf: number; // Mean Time Between Failures
}

export class HealthMonitor {
  private checks: Map<string, HealthCheck> = new Map();
  private uptimeData: Array<{ timestamp: number; status: boolean }> = [];
  private alertThresholds = {
    responseTime: 5000, // 5 seconds
    availability: 99.5,  // 99.5%
    errorRate: 5         // 5%
  };
  
  // Method to access alert thresholds for configuration
  getAlertThresholds() {
    return this.alertThresholds;
  }

  // Core system health checks
  async performHealthChecks(): Promise<HealthCheck[]> {
    const checks = await Promise.allSettled([
      this.checkSupabaseConnection(),
      this.checkOpenAIConnection(),
      this.checkDatabasePerformance(),
      this.checkVoiceProcessingHealth(),
      this.checkMemoryUsage(),
      this.checkCacheHealth(),
      this.checkBusinessCriticalFeatures()
    ]);

    const healthChecks = checks.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          name: `health_check_${index}`,
          status: 'unhealthy' as const,
          responseTime: 0,
          message: result.reason.message
        };
      }
    });

    // Store results for monitoring
    healthChecks.forEach(check => {
      this.checks.set(check.name, check);
    });

    return healthChecks;
  }

  // Supabase connection health
  private async checkSupabaseConnection(): Promise<HealthCheck> {
    const startTime = performance.now();
    
    try {
      const { createClient } = await import('@supabase/supabase-js');
      const supabase = createClient(
        process.env.VITE_SUPABASE_URL!,
        process.env.VITE_SUPABASE_ANON_KEY!
      );

      const { error } = await supabase
        .from('health_check')
        .select('count')
        .limit(1);

      const responseTime = performance.now() - startTime;

      if (error) {
        return {
          name: 'supabase_connection',
          status: 'unhealthy',
          responseTime,
          message: error.message
        };
      }

      return {
        name: 'supabase_connection',
        status: responseTime < 1000 ? 'healthy' : 'degraded',
        responseTime,
        metadata: { connection_pool: 'active' }
      };
    } catch (error) {
      return {
        name: 'supabase_connection',
        status: 'unhealthy',
        responseTime: performance.now() - startTime,
        message: (error as Error).message
      };
    }
  }

  // OpenAI API health
  private async checkOpenAIConnection(): Promise<HealthCheck> {
    const startTime = performance.now();
    
    try {
      // Simple API health check
      const response = await fetch('https://api.openai.com/v1/models', {
        headers: {
          'Authorization': `Bearer ${process.env.VITE_OPENAI_API_KEY}`,
        },
        signal: AbortSignal.timeout(5000)
      });

      const responseTime = performance.now() - startTime;

      return {
        name: 'openai_connection',
        status: response.ok ? 'healthy' : 'degraded',
        responseTime,
        metadata: { 
          status_code: response.status,
          rate_limit_remaining: response.headers.get('x-ratelimit-remaining')
        }
      };
    } catch (error) {
      return {
        name: 'openai_connection',
        status: 'unhealthy',
        responseTime: performance.now() - startTime,
        message: (error as Error).message
      };
    }
  }

  // Database performance health
  private async checkDatabasePerformance(): Promise<HealthCheck> {
    const startTime = performance.now();
    
    try {
      const { createClient } = await import('@supabase/supabase-js');
      const supabase = createClient(
        process.env.VITE_SUPABASE_URL!,
        process.env.VITE_SUPABASE_ANON_KEY!
      );

      // Test a representative query
      const { data, error } = await supabase
        .from('inventory_events')
        .select('id, event_type, created_at')
        .order('created_at', { ascending: false })
        .limit(10);

      const responseTime = performance.now() - startTime;

      if (error) {
        return {
          name: 'database_performance',
          status: 'unhealthy',
          responseTime,
          message: error.message
        };
      }

      return {
        name: 'database_performance',
        status: responseTime < 500 ? 'healthy' : 'degraded',
        responseTime,
        metadata: { 
          query_result_count: data?.length || 0,
          performance_tier: responseTime < 200 ? 'excellent' : responseTime < 500 ? 'good' : 'poor'
        }
      };
    } catch (error) {
      return {
        name: 'database_performance',
        status: 'unhealthy',
        responseTime: performance.now() - startTime,
        message: (error as Error).message
      };
    }
  }

  // Voice processing health
  private async checkVoiceProcessingHealth(): Promise<HealthCheck> {
    const startTime = performance.now();
    
    try {
      // Check if voice processing dependencies are available
      const hasWebSpeech = 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window;
      const hasMediaDevices = 'mediaDevices' in navigator;
      
      const responseTime = performance.now() - startTime;

      let status: 'healthy' | 'degraded' | 'unhealthy';
      if (hasWebSpeech && hasMediaDevices) {
        status = 'healthy';
      } else if (hasWebSpeech || hasMediaDevices) {
        status = 'degraded';
      } else {
        status = 'unhealthy';
      }

      return {
        name: 'voice_processing',
        status,
        responseTime,
        metadata: {
          web_speech_api: hasWebSpeech,
          media_devices_api: hasMediaDevices,
          browser_support: navigator.userAgent
        }
      };
    } catch (error) {
      return {
        name: 'voice_processing',
        status: 'unhealthy',
        responseTime: performance.now() - startTime,
        message: (error as Error).message
      };
    }
  }

  // Memory usage health
  private async checkMemoryUsage(): Promise<HealthCheck> {
    const startTime = performance.now();
    
    try {
      // @ts-ignore - performance.memory is available in Chrome
      const memory = (performance as any).memory;
      const responseTime = performance.now() - startTime;

      if (!memory) {
        return {
          name: 'memory_usage',
          status: 'degraded',
          responseTime,
          message: 'Memory API not available'
        };
      }

      const memoryUsageRatio = memory.usedJSHeapSize / memory.jsHeapSizeLimit;
      
      let status: 'healthy' | 'degraded' | 'unhealthy';
      if (memoryUsageRatio < 0.7) {
        status = 'healthy';
      } else if (memoryUsageRatio < 0.9) {
        status = 'degraded';
      } else {
        status = 'unhealthy';
      }

      return {
        name: 'memory_usage',
        status,
        responseTime,
        metadata: {
          used_memory_mb: Math.round(memory.usedJSHeapSize / 1024 / 1024),
          total_memory_mb: Math.round(memory.totalJSHeapSize / 1024 / 1024),
          memory_limit_mb: Math.round(memory.jsHeapSizeLimit / 1024 / 1024),
          usage_ratio: Math.round(memoryUsageRatio * 100)
        }
      };
    } catch (error) {
      return {
        name: 'memory_usage',
        status: 'unhealthy',
        responseTime: performance.now() - startTime,
        message: (error as Error).message
      };
    }
  }

  // Cache health (localStorage/sessionStorage)
  private async checkCacheHealth(): Promise<HealthCheck> {
    const startTime = performance.now();
    
    try {
      // Test localStorage and sessionStorage
      const testKey = '__health_check_test__';
      const testData = JSON.stringify({ timestamp: Date.now() });
      
      localStorage.setItem(testKey, testData);
      const localStorageWorks = localStorage.getItem(testKey) === testData;
      localStorage.removeItem(testKey);
      
      sessionStorage.setItem(testKey, testData);
      const sessionStorageWorks = sessionStorage.getItem(testKey) === testData;
      sessionStorage.removeItem(testKey);
      
      const responseTime = performance.now() - startTime;

      let status: 'healthy' | 'degraded' | 'unhealthy';
      if (localStorageWorks && sessionStorageWorks) {
        status = 'healthy';
      } else if (localStorageWorks || sessionStorageWorks) {
        status = 'degraded';
      } else {
        status = 'unhealthy';
      }

      return {
        name: 'cache_health',
        status,
        responseTime,
        metadata: {
          local_storage: localStorageWorks,
          session_storage: sessionStorageWorks,
          storage_quota: await this.getStorageQuota()
        }
      };
    } catch (error) {
      return {
        name: 'cache_health',
        status: 'unhealthy',
        responseTime: performance.now() - startTime,
        message: (error as Error).message
      };
    }
  }

  // Business critical features health
  private async checkBusinessCriticalFeatures(): Promise<HealthCheck> {
    const startTime = performance.now();
    
    try {
      // Check critical business functions
      const criticalFeatures = {
        inventory_management: await this.testInventoryFeature(),
        voice_commands: await this.testVoiceFeature(),
        haccp_compliance: await this.testHACCPFeature(),
        data_import: await this.testImportFeature()
      };
      
      const responseTime = performance.now() - startTime;
      const healthyFeatures = Object.values(criticalFeatures).filter(Boolean).length;
      const totalFeatures = Object.keys(criticalFeatures).length;
      
      let status: 'healthy' | 'degraded' | 'unhealthy';
      if (healthyFeatures === totalFeatures) {
        status = 'healthy';
      } else if (healthyFeatures >= totalFeatures * 0.8) {
        status = 'degraded';
      } else {
        status = 'unhealthy';
      }

      return {
        name: 'business_critical_features',
        status,
        responseTime,
        metadata: {
          ...criticalFeatures,
          health_ratio: `${healthyFeatures}/${totalFeatures}`
        }
      };
    } catch (error) {
      return {
        name: 'business_critical_features',
        status: 'unhealthy',
        responseTime: performance.now() - startTime,
        message: (error as Error).message
      };
    }
  }

  // Helper methods for feature testing
  private async testInventoryFeature(): Promise<boolean> {
    try {
      // Test if inventory API is responsive
      const { createClient } = await import('@supabase/supabase-js');
      const supabase = createClient(
        process.env.VITE_SUPABASE_URL!,
        process.env.VITE_SUPABASE_ANON_KEY!
      );
      
      const { error } = await supabase
        .from('inventory_events')
        .select('count')
        .limit(1);
        
      return !error;
    } catch {
      return false;
    }
  }

  private async testVoiceFeature(): Promise<boolean> {
    try {
      return 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window;
    } catch {
      return false;
    }
  }

  private async testHACCPFeature(): Promise<boolean> {
    try {
      const { createClient } = await import('@supabase/supabase-js');
      const supabase = createClient(
        process.env.VITE_SUPABASE_URL!,
        process.env.VITE_SUPABASE_ANON_KEY!
      );
      
      const { error } = await supabase
        .from('haccp_events')
        .select('count')
        .limit(1);
        
      return !error;
    } catch {
      return false;
    }
  }

  private async testImportFeature(): Promise<boolean> {
    try {
      // Test if File API is available
      return 'File' in window && 'FileReader' in window && 'FileList' in window;
    } catch {
      return false;
    }
  }

  // Storage quota check
  private async getStorageQuota(): Promise<{ used: number; quota: number }> {
    try {
      if ('storage' in navigator && 'estimate' in navigator.storage) {
        const estimate = await navigator.storage.estimate();
        return {
          used: estimate.usage || 0,
          quota: estimate.quota || 0
        };
      }
    } catch {
      // Fallback for older browsers
    }
    
    return { used: 0, quota: 0 };
  }

  // Generate health report
  async generateHealthReport(): Promise<{
    overall_status: 'healthy' | 'degraded' | 'unhealthy';
    checks: HealthCheck[];
    summary: {
      healthy_count: number;
      degraded_count: number;
      unhealthy_count: number;
      average_response_time: number;
    };
  }> {
    const checks = await this.performHealthChecks();
    
    const healthy = checks.filter(c => c.status === 'healthy').length;
    const degraded = checks.filter(c => c.status === 'degraded').length;
    const unhealthy = checks.filter(c => c.status === 'unhealthy').length;
    
    const avgResponseTime = checks.reduce((sum, c) => sum + c.responseTime, 0) / checks.length;
    
    let overall_status: 'healthy' | 'degraded' | 'unhealthy';
    if (unhealthy > 0) {
      overall_status = 'unhealthy';
    } else if (degraded > 0) {
      overall_status = 'degraded';
    } else {
      overall_status = 'healthy';
    }

    return {
      overall_status,
      checks,
      summary: {
        healthy_count: healthy,
        degraded_count: degraded,
        unhealthy_count: unhealthy,
        average_response_time: Math.round(avgResponseTime)
      }
    };
  }

  // Track uptime
  recordUptime(isHealthy: boolean): void {
    this.uptimeData.push({
      timestamp: Date.now(),
      status: isHealthy
    });

    // Keep only last 24 hours of data
    const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
    this.uptimeData = this.uptimeData.filter(entry => entry.timestamp > oneDayAgo);
  }

  // Calculate uptime metrics
  getUptimeMetrics(): UptimeMetrics {
    if (this.uptimeData.length === 0) {
      return { uptime: 0, downtime: 0, availability: 0, mttr: 0, mtbf: 0 };
    }

    const uptimeEntries = this.uptimeData.filter(entry => entry.status);
    const downtimeEntries = this.uptimeData.filter(entry => !entry.status);
    
    const uptime = uptimeEntries.length;
    const downtime = downtimeEntries.length;
    const availability = (uptime / this.uptimeData.length) * 100;

    return {
      uptime,
      downtime,
      availability: Math.round(availability * 100) / 100,
      mttr: this.calculateMTTR(),
      mtbf: this.calculateMTBF()
    };
  }

  private calculateMTTR(): number {
    // Mean Time To Recovery calculation
    let totalRecoveryTime = 0;
    let recoveryCount = 0;
    
    for (let i = 1; i < this.uptimeData.length; i++) {
      const current = this.uptimeData[i];
      const previous = this.uptimeData[i - 1];
      
      if (current.status && !previous.status) {
        // Recovery detected
        let downStart = previous.timestamp;
        for (let j = i - 2; j >= 0; j--) {
          if (this.uptimeData[j].status) break;
          downStart = this.uptimeData[j].timestamp;
        }
        
        totalRecoveryTime += current.timestamp - downStart;
        recoveryCount++;
      }
    }
    
    return recoveryCount > 0 ? totalRecoveryTime / recoveryCount : 0;
  }

  private calculateMTBF(): number {
    // Mean Time Between Failures calculation
    let totalUptime = 0;
    let failureCount = 0;
    
    for (let i = 1; i < this.uptimeData.length; i++) {
      const current = this.uptimeData[i];
      const previous = this.uptimeData[i - 1];
      
      if (!current.status && previous.status) {
        // Failure detected
        failureCount++;
      }
      
      if (current.status) {
        totalUptime += current.timestamp - previous.timestamp;
      }
    }
    
    return failureCount > 0 ? totalUptime / failureCount : 0;
  }
}

// Initialize health monitoring
export const healthMonitor = new HealthMonitor();

// Set up periodic health checks in production
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
  // Check health every 5 minutes
  setInterval(async () => {
    const report = await healthMonitor.generateHealthReport();
    healthMonitor.recordUptime(report.overall_status !== 'unhealthy');
    
    // Send health data to monitoring services
    if (window.DD_RUM) {
      window.DD_RUM.addAttribute('health_status', report.overall_status);
      window.DD_RUM.addAttribute('health_score', report.summary.healthy_count);
    }
    
    console.log('Health Check:', report.overall_status, report.summary);
  }, 5 * 60 * 1000);
}