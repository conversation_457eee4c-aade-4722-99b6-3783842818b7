# OpenAI Realtime Assistant Setup Guide

This guide explains how to set up and use the OpenAI Realtime Assistant with Supabase and Zep database integrations for seafood inventory management.

## Prerequisites

1. **OpenAI API Key** with access to GPT-4o Realtime API
2. **Supabase Project** with inventory database
3. **Zep API Key** for conversation memory (already configured)

## Installation

### 1. Install Dependencies

```bash
# Install Zep client library
npm install @getzep/zep-js

# Install additional audio dependencies if needed
npm install @types/web
```

### 2. Environment Variables

Ensure your `.env` file contains:

```env
VITE_OPENAI_API_KEY=your_openai_api_key_here
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

**Note:** The Zep API key is already configured in the code.

## Components Overview

### 1. RealtimeAssistantTools.ts
**Location:** `src/services/RealtimeAssistantTools.ts`

Provides 8 tool functions for the OpenAI Realtime Assistant:

#### Supabase Database Tools:
- **`create_inventory_event`** - Creates inventory events (receiving, sales, disposal, physical_count)
- **`search_products`** - Searches for products in the database
- **`get_inventory_status`** - Gets current inventory levels
- **`search_vendors`** - Finds vendors/suppliers
- **`search_customers`** - Finds customers
- **`get_recent_events`** - Retrieves recent inventory activity

#### Zep Memory Tools:
- **`store_conversation_memory`** - Stores conversation context and preferences
- **`retrieve_conversation_memory`** - Retrieves relevant conversation history

### 2. RealtimeVoiceAssistant.tsx
**Location:** `src/components/voice/RealtimeVoiceAssistant.tsx`

React component providing:
- WebSocket connection to OpenAI Realtime API
- Real-time audio input/output
- Tool function execution
- Connection status monitoring
- Activity logging

### 3. RealtimeAssistantDemo.tsx
**Location:** `src/pages/RealtimeAssistantDemo.tsx`

Demo page showcasing:
- Voice assistant interface
- Recent events display
- Error handling
- Usage examples
- Configuration overview

## Usage Instructions

### 1. Basic Setup

```tsx
import RealtimeVoiceAssistant from '../components/voice/RealtimeVoiceAssistant';

function MyComponent() {
  const handleEventCreated = (event) => {
    console.log('New inventory event:', event);
  };

  const handleError = (error) => {
    console.error('Assistant error:', error);
  };

  return (
    <RealtimeVoiceAssistant
      userId="your-user-id"
      onEventCreated={handleEventCreated}
      onError={handleError}
    />
  );
}
```

### 2. Voice Commands

The assistant understands natural seafood inventory language:

#### Receiving Events:
- "We received 50 pounds of Atlantic salmon from Pacific Seafoods"
- "Got 25 cases of Dungeness crab from Ocean Fresh"
- "Incoming shipment: 100 pounds of halibut fillets"

#### Sales Events:
- "Sold 30 pounds of rockfish to Harbor Restaurant"
- "Delivered 15 pounds of spot prawns to customer"
- "Shipped 2 cases of oysters to downtown market"

#### Inventory Queries:
- "What's our current salmon inventory?"
- "How much Dungeness crab do we have?"
- "Show me recent receiving events"

#### Physical Counts:
- "Physical count shows 75 pounds of halibut in freezer 2"
- "Counted 150 pounds of cod in cold storage"

### 3. Advanced Features

#### Memory & Context:
The assistant remembers:
- User preferences and patterns
- Conversation history
- Frequently used vendors/customers
- Common product variations

#### Error Handling:
- Automatic retry for failed operations
- Graceful degradation when services are unavailable
- Clear error messages with recovery suggestions

#### Real-time Processing:
- Sub-second voice recognition
- Immediate database updates
- Live status feedback
- Conversation flow management

## Tool Function Details

### create_inventory_event

Creates a new inventory event with voice metadata:

```json
{
  "event_type": "receiving|sale|disposal|physical_count",
  "product_name": "Atlantic Salmon Fillets",
  "quantity": 50,
  "unit": "lbs",
  "vendor_name": "Pacific Seafoods",
  "condition": "Excellent",
  "processing_method": "Fresh",
  "quality_grade": "Premium",
  "voice_confidence_score": 0.95,
  "raw_transcript": "We received fifty pounds of Atlantic salmon from Pacific Seafoods"
}
```

### search_products

Searches products with fuzzy matching:

```json
{
  "query": "salmon",
  "limit": 10
}
```

Returns matching products with current inventory levels.

### get_inventory_status

Gets comprehensive inventory information:

```json
{
  "product_name": "Atlantic Salmon"
}
```

Returns current stock, recent activity, and trends.

### Memory Functions

Store and retrieve conversation context:

```json
{
  "user_id": "seafood-manager-001",
  "session_id": "session_12345",
  "message": "User prefers receiving events with temperature data",
  "metadata": {
    "preferences": ["temperature_tracking", "vendor_verification"],
    "common_products": ["salmon", "crab", "halibut"]
  }
}
```

## Configuration Options

### Voice Detection Settings:
- **Threshold**: 0.5 (sensitivity)
- **Prefix Padding**: 300ms (before speech)
- **Silence Duration**: 200ms (after speech)

### Audio Processing:
- **Sample Rate**: 24kHz
- **Format**: 16-bit PCM
- **Echo Cancellation**: Enabled
- **Noise Suppression**: Enabled

### Tool Behavior:
- **Temperature**: 0.6 (creativity vs accuracy)
- **Tool Choice**: Auto (intelligent selection)
- **Confirmation**: Required for high-value transactions

## Troubleshooting

### Common Issues:

1. **Connection Fails**
   - Check OpenAI API key and billing
   - Verify WebSocket support in browser
   - Check network/firewall settings

2. **Microphone Not Working**
   - Grant microphone permissions
   - Check browser security settings
   - Verify HTTPS (required for microphone access)

3. **Database Errors**
   - Verify Supabase connection
   - Check RLS (Row Level Security) policies
   - Confirm user authentication

4. **Memory Issues**
   - Zep API key is pre-configured
   - Check internet connectivity
   - Verify session management

### Debug Mode:

Enable console logging for detailed debugging:

```tsx
<RealtimeVoiceAssistant
  userId="debug-user"
  onEventCreated={(event) => console.log('Event:', event)}
  onError={(error) => console.error('Error:', error)}
/>
```

## Security Considerations

1. **API Keys**: Never expose OpenAI API keys in client-side code
2. **Database Access**: Use proper RLS policies in Supabase
3. **Voice Data**: Audio is processed by OpenAI (review privacy policy)
4. **Memory Storage**: Conversation data stored in Zep (encrypted)

## Performance Tips

1. **WebSocket Management**: Properly close connections when unmounting
2. **Audio Processing**: Use appropriate buffer sizes for your use case
3. **Database Queries**: Implement proper indexing for search operations
4. **Memory Usage**: Limit conversation history length for better performance

## Integration Examples

### With Existing Inventory System:

```tsx
import { useInventoryContext } from '../contexts/InventoryContext';

function EnhancedInventoryPage() {
  const { addEvent, updateInventory } = useInventoryContext();

  const handleVoiceEvent = (event) => {
    addEvent(event);
    updateInventory(event.product_id, event.quantity);
  };

  return (
    <div>
      <InventoryDashboard />
      <RealtimeVoiceAssistant onEventCreated={handleVoiceEvent} />
    </div>
  );
}
```

### With Real-time Updates:

```tsx
import { useEffect } from 'react';
import { supabase } from '../lib/supabase';

function LiveInventoryManager() {
  useEffect(() => {
    const subscription = supabase
      .channel('inventory_events')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'inventory_events'
      }, (payload) => {
        if (payload.new.created_by_voice) {
          // Handle voice-created events
          console.log('Voice event created:', payload.new);
        }
      })
      .subscribe();

    return () => subscription.unsubscribe();
  }, []);

  return <RealtimeVoiceAssistant />;
}
```

This completes the comprehensive setup for the OpenAI Realtime Assistant with Supabase and Zep integration for seafood inventory management.