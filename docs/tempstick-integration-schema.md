# TempStick Sensor Integration Schema Documentation

## Overview

This document outlines the comprehensive database schema for TempStick sensor integration in the Seafood Manager application. The schema supports real-time temperature monitoring, HACCP compliance, and seamless integration with the existing inventory management system.

## Database Schema Architecture

### Core Tables

#### 1. `storage_areas`
Physical storage locations with temperature monitoring requirements.

**Key Features:**
- Supports various storage types (walk-in cooler/freezer, reach-in units, dry storage, etc.)
- HACCP Critical Control Point (CCP) designation
- Configurable temperature and humidity thresholds
- Alert escalation settings
- Compliance monitoring configuration

**Important Fields:**
- `haccp_required`: Flags areas requiring HACCP monitoring
- `haccp_ccp_number`: Critical Control Point designation
- `temp_min/max_fahrenheit/celsius`: Safe temperature ranges
- `alert_threshold_minutes`: Time before alerting on violations
- `monitoring_frequency_minutes`: How often to check readings

#### 2. `sensors` (TempStick devices)
TempStick sensor metadata and configuration.

**Key Features:**
- TempStick API integration fields (`sensor_id`, `device_name`)
- Real-time connection status monitoring
- Battery and signal strength tracking
- Calibration settings and maintenance scheduling
- Custom alert thresholds (override storage area defaults)

**Important Fields:**
- `sensor_id`: TempStick device ID from API
- `connection_status`: 'online', 'offline', 'maintenance', 'error'
- `battery_level`: 0-100 percentage
- `custom_temp_min/max_*`: Override thresholds for specific sensors

#### 3. `temperature_readings`
Time-series temperature and humidity data.

**Key Features:**
- High-frequency temperature data storage
- Automatic violation detection via triggers
- Data quality indicators
- TempStick API synchronization tracking
- Performance-optimized indexes for time-series queries

**Important Fields:**
- `temp_celsius/fahrenheit`: Temperature readings in both units
- `humidity`: Relative humidity percentage
- `within_safe_range`: Auto-calculated compliance flag
- `temp_violation/humidity_violation`: Specific violation flags
- `sync_status`: API synchronization status

#### 4. `temperature_alerts`
Temperature violations and alert management.

**Key Features:**
- Comprehensive alert classification system
- Escalation and notification tracking
- HACCP compliance impact assessment
- Resolution workflow management
- Auto-resolution capabilities

**Important Fields:**
- `alert_type`: 'temp_high', 'temp_low', 'sensor_offline', etc.
- `severity`: 'info', 'warning', 'critical', 'emergency'
- `haccp_violation`: Flags regulatory compliance impact
- `product_safety_risk`: Risk assessment level
- `escalated/escalation_level`: Alert escalation tracking

### Integration Tables

#### 5. `product_storage_requirements`
Product-specific temperature and storage requirements.

**Purpose:** Links products to their specific storage requirements for HACCP compliance.

**Key Features:**
- Product-specific temperature ranges
- Cold chain criticality flags
- HACCP monitoring requirements
- Preferred/prohibited storage areas
- Shelf life impact calculations

#### 6. `temperature_events`
Temperature verification events linked to inventory operations.

**Purpose:** Tracks temperature checks during inventory events (receiving, storage, sales).

**Key Features:**
- Links temperature readings to inventory transactions
- Compliance assessment at transaction time
- Quality impact evaluation
- Corrective action tracking
- HACCP documentation

### Enhanced Inventory Integration

#### Modified `inventory_events` Table
The existing inventory_events table has been enhanced with temperature fields:

```sql
-- New temperature monitoring fields
storage_area_id UUID REFERENCES storage_areas(id),
temp_at_event_celsius DECIMAL(6,2),
temp_at_event_fahrenheit DECIMAL(6,2),
humidity_at_event DECIMAL(5,2),
temp_compliant BOOLEAN DEFAULT true,
temp_sensor_id UUID REFERENCES sensors(id),
temp_reading_id UUID REFERENCES temperature_readings(id),
cold_chain_maintained BOOLEAN DEFAULT true,
temp_violation_notes TEXT
```

## Database Views

### 1. `sensor_status_dashboard`
Real-time dashboard view of all sensor statuses and latest readings.

**Use Case:** Main sensor monitoring dashboard

**Key Data:**
- Current sensor status and battery levels
- Latest temperature readings
- Active alert counts
- Storage area information

### 2. `haccp_compliance_dashboard`
HACCP compliance summary for areas requiring monitoring.

**Use Case:** Regulatory compliance monitoring

**Key Data:**
- Temperature violation percentages
- Sensor availability status
- Inventory temperature compliance
- Overall compliance status ratings

### 3. `inventory_with_temperature`
Inventory events enhanced with real-time temperature monitoring data.

**Use Case:** Integrated inventory and temperature management

**Key Data:**
- Inventory transactions with temperature context
- Product storage requirement compliance
- Cold chain maintenance status
- Temperature compliance status

## Security and Performance

### Row Level Security (RLS)
All tables implement comprehensive RLS policies ensuring complete user data isolation:
- Users can only access their own data
- Policies cover all CRUD operations
- Foreign key relationships respect user boundaries

### Performance Optimization
- **Time-series indexes** on temperature_readings for efficient querying
- **Composite indexes** for dashboard queries
- **Partial indexes** for active alerts and violations
- **LATERAL joins** in views for optimal performance

### Real-time Capabilities
- **Real-time subscriptions** enabled for dashboard updates
- **Automatic triggers** for violation detection
- **Background functions** for alert processing

## Database Functions and Triggers

### 1. `check_temperature_violation()`
Automatically evaluates temperature readings against safe ranges.

**Triggered on:** INSERT/UPDATE to temperature_readings
**Purpose:** Sets violation flags based on storage area or sensor thresholds

### 2. `check_inventory_temperature_compliance()`
Checks temperature compliance during inventory events.

**Triggered on:** INSERT/UPDATE to inventory_events
**Purpose:** Links inventory transactions to current temperature conditions

### 3. `update_updated_at_column()`
Maintains updated_at timestamps across all tables.

## TempStick API Integration Points

### Device Management
- **Sensor registration:** Map TempStick device IDs to internal sensor records
- **Status monitoring:** Track online/offline status and battery levels
- **Firmware updates:** Monitor device firmware versions

### Data Synchronization
- **Reading import:** Pull temperature data from TempStick API
- **Data validation:** Verify reading quality and detect gaps
- **Conflict resolution:** Handle duplicate or conflicting readings

### Alert Integration
- **Threshold configuration:** Sync alert thresholds with TempStick devices
- **Alert correlation:** Match TempStick alerts with internal alert system
- **Notification routing:** Coordinate with existing notification systems

## HACCP Compliance Features

### Critical Control Points (CCPs)
- **CCP designation:** Link storage areas to specific HACCP control points
- **Monitoring procedures:** Automated temperature monitoring per HACCP requirements
- **Corrective actions:** Track corrective actions for temperature violations
- **Verification records:** Maintain verification documentation

### Documentation and Reporting
- **Temperature logs:** Comprehensive temperature history for audits
- **Compliance reports:** Automated HACCP compliance reporting
- **Violation tracking:** Detailed violation history and resolution
- **Audit trails:** Complete audit trail for all temperature-related activities

## Sample Usage Patterns

### 1. Real-time Dashboard Query
```sql
SELECT * FROM sensor_status_dashboard 
WHERE user_id = current_user_id()
ORDER BY critical_alerts_count DESC, active_alerts_count DESC;
```

### 2. HACCP Compliance Check
```sql
SELECT * FROM haccp_compliance_dashboard 
WHERE compliance_status != 'compliant' 
  AND user_id = current_user_id();
```

### 3. Inventory with Temperature Context
```sql
SELECT * FROM inventory_with_temperature 
WHERE temperature_compliance_status != 'compliant'
  AND occurred_at > (NOW() - INTERVAL '7 days')
ORDER BY occurred_at DESC;
```

### 4. Temperature Trend Analysis
```sql
SELECT 
  recorded_at,
  temp_fahrenheit,
  within_safe_range,
  storage_area_name
FROM temperature_readings tr
JOIN sensors s ON s.id = tr.sensor_id
JOIN storage_areas sa ON sa.id = s.storage_area_id
WHERE tr.user_id = current_user_id()
  AND tr.recorded_at > (NOW() - INTERVAL '24 hours')
ORDER BY recorded_at DESC;
```

## Migration Files

1. **20250825_001_tempstick_sensor_integration_schema.sql**
   - Core schema creation
   - Tables, indexes, RLS policies
   - Database functions and triggers
   - Dashboard views

2. **20250825_002_tempstick_sample_data.sql**
   - Sample storage areas and sensors
   - Realistic temperature readings
   - Sample alerts for testing

3. **20250825_003_inventory_temperature_integration.sql**
   - Inventory system integration
   - Product storage requirements
   - Temperature event tracking
   - Enhanced compliance monitoring

## TypeScript Integration

The schema is fully typed in `src/types/schema.ts` with interfaces for:
- Core entities (StorageArea, TempStickSensor, TemperatureReading, TemperatureAlert)
- Integration types (InventoryEventWithTemperature, ProductStorageRequirements)
- Dashboard views (SensorStatusDashboard, HACCPComplianceDashboard)
- API types (TempStickApiReading, TemperatureDataRequest/Response)

## Next Steps

1. **Implement TempStick API service** (`lib/tempstick-service.ts`)
2. **Create sensor management UI** (`components/sensors/`)
3. **Build temperature monitoring dashboard** 
4. **Integrate with existing HACCP components**
5. **Add real-time alert notifications**
6. **Implement automated reporting**

## Performance Considerations

- **Index strategy:** Optimized for time-series queries and dashboard views
- **Data retention:** Configurable retention periods per storage area
- **Archival strategy:** Consider archiving old temperature data
- **Query optimization:** Use materialized views for heavy dashboard queries
- **Connection pooling:** Optimize database connections for real-time updates

This schema provides a robust foundation for comprehensive temperature monitoring with full integration into the existing seafood inventory management system while maintaining HACCP compliance and supporting real-time operations.