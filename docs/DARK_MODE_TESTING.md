# Dark Mode Testing Suite Documentation

This document describes the comprehensive automated testing suite for the dark mode functionality in the Seafood Manager application.

## Overview

The dark mode implementation includes:
- **ThemeContext** with light, dark, and system theme options
- **Theme toggle button** in the Sidebar component  
- **Theme-aware styling** throughout sensor components (SensorDashboard, SensorTile, ExportControls)
- **Persistent theme storage** using localStorage
- **System preference detection** for automatic theme switching

## Test Architecture

### Unit Tests (`src/__tests__/ThemeContext.test.tsx`)

**Scope**: Tests the core ThemeContext provider, useTheme hook, and useThemeAwareStyles hook

**Key Test Areas**:
- ThemeProvider initialization with different default themes
- Theme persistence using localStorage with error handling
- System theme detection and media query listening
- DOM updates (CSS classes and custom properties)
- Theme state management and transitions
- Hook error handling and SSR compatibility

**Coverage**: 
- ✅ Context provider initialization
- ✅ Theme state management  
- ✅ System preference detection
- ✅ LocalStorage persistence and error handling
- ✅ CSS custom properties updates
- ✅ Hook utilities and error boundaries

### End-to-End Tests

#### 1. Core Functionality (`e2e/dark-mode-functionality.spec.ts`)

**Scope**: Tests the complete theme switching workflow in a real browser environment

**Test Scenarios**:
```typescript
// Theme Context Provider Initialization
- Should initialize with system theme by default
- Should provide theme state through context  
- Should handle missing localStorage gracefully

// Theme Toggle Button Behavior
- Should cycle through light -> dark -> system -> light
- Should update theme icons correctly (Sun/Moon/Monitor)
- Should maintain functionality when sidebar is collapsed

// System Theme Detection  
- Should respect system dark mode preference
- Should respect system light mode preference
- Should update when system theme changes dynamically

// Theme Persistence
- Should save theme preference to localStorage
- Should restore theme preference on reload  
- Should handle corrupted localStorage data
- Should persist theme across navigation

// CSS Custom Properties Updates
- Should update chart properties for dark/light themes
- Should maintain proper values for --chart-background, --chart-text, etc.
```

#### 2. Visual Styling Validation (`e2e/theme-visual-styling.spec.ts`)

**Scope**: Tests visual consistency and styling application across themes

**Test Scenarios**:
```typescript
// Light Mode Visual Validation
- Should apply light mode styling to sidebar and content
- Should display correct Sun icon
- Should use appropriate background/text colors

// Dark Mode Visual Validation  
- Should apply dark CSS class to HTML element
- Should display correct Moon icon
- Should use dark color schemes consistently

// System Mode Visual Validation
- Should reflect browser's preferred color scheme
- Should display Monitor icon for system mode

// Theme Transition Smoothness
- Should smoothly transition between themes
- Should maintain layout during transitions

// Accessibility in Different Themes
- Should maintain adequate contrast ratios
- Should work across different browsers consistently
```

#### 3. Sensor Components Integration (`e2e/sensor-components-theme-aware.spec.ts`)

**Scope**: Tests theme-aware rendering in sensor-specific components

**Test Scenarios**:
```typescript
// SensorDashboard Theme Integration
- Should render with proper light/dark theme styles
- Should update styling when theme changes dynamically

// SensorTile Theme Integration  
- Should render sensor status indicators with theme-appropriate colors
- Should maintain functionality across theme changes

// ExportControls Theme Integration
- Should render export controls with appropriate styling
- Should maintain functionality in both themes

// Temperature Chart Theme Integration
- Should apply theme-aware chart styling via CSS custom properties
- Should update chart colors when theme changes

// HACCP Compliance Color Indicators
- Should render status indicators correctly in both themes
- Should maintain color coding for compliance/violation/warning states

// Responsive Design
- Should maintain theme styling on mobile/tablet viewports
```

## Test Data and Mocks

### LocalStorage Mocking
```typescript
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(), 
  removeItem: vi.fn(),
  clear: vi.fn(),
};
```

### Media Query Mocking
```typescript
const mockMatchMedia = vi.fn((query) => ({
  matches: query === '(prefers-color-scheme: dark)',
  media: query,
  addListener: vi.fn(),
  removeListener: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
}));
```

### CSS Custom Properties Testing
The tests validate these CSS custom properties are properly set:
- `--chart-background`: `#ffffff` (light) / `#1f2937` (dark)
- `--chart-text`: `#374151` (light) / `#f3f4f6` (dark)  
- `--chart-grid`: `#e5e7eb` (light) / `#374151` (dark)
- `--chart-border`: `#d1d5db` (light) / `#4b5563` (dark)

## Running the Tests

### Quick Test Execution
```bash
# Run all dark mode tests
./scripts/run-theme-tests.sh

# Run specific test suites
npm test -- ThemeContext.test.tsx
npx playwright test e2e/dark-mode-functionality.spec.ts
npx playwright test e2e/theme-visual-styling.spec.ts
npx playwright test e2e/sensor-components-theme-aware.spec.ts
```

### Individual Test Commands
```bash
# Unit tests only
npm test -- src/__tests__/ThemeContext.test.tsx --reporter=verbose

# E2E tests only  
npx playwright test --grep "Dark Mode"

# Visual tests with screenshots
npx playwright test e2e/theme-visual-styling.spec.ts --headed

# Cross-browser testing
npx playwright test --project=chromium --project=firefox --project=webkit
```

## Test Configuration

### Vitest Setup (`vitest.config.ts`)
- **Environment**: happy-dom for DOM testing
- **Setup Files**: `src/__tests__/setup.ts` for global mocks
- **Coverage**: Configured for theme-related files

### Playwright Setup (`playwright.config.ts`)  
- **Base URL**: http://localhost:5177
- **Projects**: Chrome, Firefox, Safari, Mobile Chrome, Mobile Safari
- **Screenshots**: On failure for visual validation
- **Video**: Retained on failure for debugging

## Validation Checklist

The test suite validates these requirements:

### ✅ Core Functionality
- [x] ThemeContext provider properly initializes and provides theme state
- [x] Theme toggle button cycles through light -> dark -> system -> light  
- [x] System theme detection works correctly based on browser preferences
- [x] Theme persistence across browser sessions using localStorage

### ✅ Visual Styling
- [x] Theme-aware styling applies correctly in both light and dark modes
- [x] All sensor components render properly with theme-aware styles
- [x] CSS custom properties for charts update correctly with theme changes

### ✅ Error Handling & Edge Cases  
- [x] Graceful handling of localStorage errors
- [x] Proper fallback for corrupted theme data
- [x] SSR compatibility
- [x] System preference change detection

### ✅ Cross-Browser & Responsive
- [x] Consistent behavior across Chrome, Firefox, Safari
- [x] Mobile and tablet viewport compatibility
- [x] Accessibility compliance in both themes

## Debugging Failed Tests

### Common Issues and Solutions

**1. Theme Button Not Found**
```bash
# Ensure data-testid is added to Sidebar component
# Check: <nav data-testid="sidebar">
```

**2. CSS Custom Properties Not Updating**
```bash
# Check ThemeContext updates document.documentElement.style
# Verify CSS property names match expectations
```

**3. LocalStorage Tests Failing**
```bash
# Ensure localStorage mock is properly configured
# Check error handling in ThemeContext
```

**4. System Theme Detection Issues**  
```bash
# Verify matchMedia mock setup
# Check media query listener registration/cleanup
```

### Screenshot Comparison
Visual tests generate screenshots for comparison:
- `light-mode-theme-button.png`
- `dark-mode-theme-button.png`  
- `sensor-dashboard-light-mode.png`
- `sensor-dashboard-dark-mode.png`

## Extending the Test Suite

### Adding New Theme-Aware Components
1. Add component-specific test scenarios to `sensor-components-theme-aware.spec.ts`
2. Test both light and dark mode styling
3. Verify theme transition behavior
4. Add visual screenshot validation

### Adding New Theme Options
1. Update unit tests in `ThemeContext.test.tsx`
2. Add new theme cycle tests in E2E suite
3. Update CSS custom properties validation
4. Test system preference integration

### Performance Testing
Consider adding performance tests for:
- Theme switching speed
- Memory usage during theme changes  
- Re-render optimization with React.memo

## CI/CD Integration

The test suite is designed to run in CI environments:

```yaml
# Example GitHub Actions integration
- name: Run Dark Mode Tests
  run: |
    npm ci
    npx playwright install
    ./scripts/run-theme-tests.sh
```

Success criteria for automated builds:
- All unit tests pass
- All E2E tests pass across browsers
- Visual regression tests show no unexpected changes
- Coverage thresholds met for theme-related code

---

This comprehensive test suite ensures the dark mode implementation is robust, user-friendly, and maintainable across different environments and use cases.