# Voice Event Management - Database Schema Extensions

## Overview

This document describes the database schema extensions required for the Voice Event Management system. These extensions add voice processing capabilities to the existing `inventory_events` table and create an audit trail system for tracking changes.

## Schema Changes

### Extended inventory_events Table

The following columns have been added to the existing `inventory_events` table:

| Column | Type | Description |
|--------|------|-------------|
| `voice_confidence_score` | DECIMAL(3,2) | Confidence score (0.0-1.0) from voice processing |
| `voice_confidence_breakdown` | JSONB | Detailed confidence breakdown by component |
| `raw_transcript` | TEXT | Original transcript from voice-to-text processing |
| `audio_recording_url` | TEXT | URL to stored audio recording for verification |
| `created_by_voice` | BOOLEAN | Flag indicating if event was created via voice |

### New event_audit_trail Table

A new table to track all changes to inventory events:

```sql
CREATE TABLE event_audit_trail (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_id UUID NOT NULL REFERENCES inventory_events(id) ON DELETE CASCADE,
    field_name TEXT NOT NULL,
    old_value JSONB,
    new_value JSONB,
    changed_by UUID,
    changed_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    change_reason TEXT,
    change_source TEXT DEFAULT 'manual',
    session_id TEXT
);
```

## Voice Confidence Breakdown Structure

The `voice_confidence_breakdown` JSONB field contains:

```json
{
  "overall": 0.85,
  "product_match": 0.90,
  "quantity_extraction": 0.80,
  "vendor_match": 0.85
}
```

## Indexes

The following indexes have been created for optimal performance:

- `idx_inventory_events_voice_created` - For filtering voice-created events
- `idx_inventory_events_voice_confidence` - For confidence score filtering
- `idx_inventory_events_voice_low_confidence` - For quality review queue
- `idx_inventory_events_audio_url` - For audio recording management
- `idx_event_audit_trail_event_id` - For audit trail queries
- `idx_event_audit_trail_changed_by` - For user activity tracking
- `idx_event_audit_trail_changed_at` - For chronological queries

## Constraints

### Voice Confidence Validation
Ensures that when a confidence score is present, the breakdown is also present with required fields:

```sql
CHECK (
    (voice_confidence_score IS NULL AND voice_confidence_breakdown IS NULL) OR
    (voice_confidence_score IS NOT NULL AND voice_confidence_breakdown IS NOT NULL AND
     voice_confidence_breakdown ? 'overall' AND
     voice_confidence_breakdown ? 'product_match' AND
     voice_confidence_breakdown ? 'quantity_extraction')
)
```

### Voice Fields Consistency
Ensures that voice-specific fields are only present when `created_by_voice` is true:

```sql
CHECK (
    (created_by_voice = FALSE AND voice_confidence_score IS NULL AND raw_transcript IS NULL) OR
    (created_by_voice = TRUE)
)
```

## Migration Files

1. **Primary Migration**: `supabase/migrations/20250815_001_voice_event_management_schema.sql`
   - Complete migration with triggers and helper functions
   - Includes RLS policies and permissions

2. **Standalone Script**: `scripts/apply-voice-event-schema.sql`
   - Simplified version that can be run independently
   - Focuses on core schema changes only

## Usage Examples

### Query Voice Events
```sql
SELECT * FROM inventory_events 
WHERE created_by_voice = TRUE 
ORDER BY created_at DESC;
```

### Query Low Confidence Events (Quality Review)
```sql
SELECT * FROM inventory_events 
WHERE created_by_voice = TRUE 
  AND voice_confidence_score < 0.7
ORDER BY created_at ASC;
```

### Query Audit Trail for an Event
```sql
SELECT * FROM event_audit_trail 
WHERE event_id = 'your-event-id'
ORDER BY changed_at DESC;
```

## TypeScript Interfaces

The corresponding TypeScript interfaces have been added to `src/types/schema.ts`:

- `VoiceEvent` - Complete voice event structure
- `EventAudit` - Audit trail entry structure
- `VoiceEventFilters` - Filtering options for queries
- `VoiceEventResult` - Voice processing result structure
- `VoiceEventData` - Input data for voice processing

## Next Steps

After applying the schema changes:

1. Implement the `VoiceEventService` class for database operations
2. Create the voice event processing components
3. Build the UI components for event display and editing
4. Set up audio recording and storage integration
5. Implement the quality assurance and review system

## Verification

After applying the migration, verify the changes with:

```sql
-- Check new columns exist
SELECT column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name = 'inventory_events' 
  AND column_name LIKE '%voice%';

-- Check audit trail table exists
SELECT * FROM information_schema.tables 
WHERE table_name = 'event_audit_trail';

-- Check indexes were created
SELECT indexname FROM pg_indexes 
WHERE tablename = 'inventory_events' 
  AND indexname LIKE '%voice%';
```