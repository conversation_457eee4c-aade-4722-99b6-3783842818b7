# TempStick Dashboard User Guide

## 🌡️ Overview

Your TempStick temperature monitoring dashboard is now fully operational and displaying real-time data from your 3 TempStick sensors:

1. **Downstairs Walk in Freezer** (Sensor ID: 2550380)
2. **Upright White alaskan fish freezer** (Sensor ID: 2298510) 
3. **East coast chest freezer** (Sensor ID: 2301797)

## 🚀 How to Access the Dashboard

### 1. Navigate to Temperature Monitoring
- Open your application at `http://localhost:5177`
- In the sidebar, click **"Temperature"** to expand the menu
- Click **"Dashboard"** to open the main temperature monitoring dashboard

### 2. Alternative Access Routes
- Direct URL: `http://localhost:5177` → Navigate to "Temperature Monitoring"
- Sidebar: **Temperature → Dashboard**

## 📊 Dashboard Features

### Real-Time Data Display
- **Live sensor readings** updated every 30 seconds
- **Temperature in both Celsius and Fahrenheit**
- **Humidity levels** for each sensor
- **Battery status** and signal strength indicators
- **Online/offline status** for each sensor

### Summary Cards
- **Total Sensors**: Shows count of all configured sensors
- **Online Sensors**: Number of currently active sensors
- **Active Alerts**: Count of temperature violations or issues
- **Average Temperature**: Real-time average across all sensors

### Interactive Filters
- **Time Range**: 1 hour, 6 hours, 24 hours, 7 days, 30 days
- **Sensor Selection**: Filter by specific sensors
- **Temperature Range**: Filter by temperature categories:
  - Freezer (-30°F to 0°F)
  - Refrigerator (32°F to 40°F) 
  - Room Temperature (60°F to 80°F)
  - Critical Alerts
- **Show/Hide Offline Sensors**: Toggle offline sensor visibility
- **Alerts Only**: Show only sensors with active alerts

### Data Source Controls
- **TempStick Data Source Selector**: Switch between Real API, Mock Data, or Auto mode
- **System Status Indicators**: API connection, auto-refresh status, data source
- **Manual Refresh**: Force immediate data update
- **Auto-refresh Toggle**: Enable/disable automatic updates

### Temperature Trend Charts
- **Interactive line charts** showing temperature trends over time
- **Multiple sensor overlay** for comparison
- **Zoom and pan capabilities**
- **Hover tooltips** with detailed readings

## 🔧 Current Sensor Data

Your sensors are currently reporting:

### Downstairs Walk in Freezer
- **Temperature**: -21.84°C (-7.3°F)
- **Humidity**: 79.86%
- **Status**: Online
- **Battery**: 100%
- **Network**: Fresh-Basement WiFi

### Upright White alaskan fish freezer  
- **Temperature**: -29.55°C (-21.2°F)
- **Humidity**: 53.37%
- **Status**: Online
- **Battery**: 100%
- **Network**: FRESH WiFi

### East coast chest freezer
- **Temperature**: -29.39°C (-20.9°F) 
- **Humidity**: 59.95%
- **Status**: Online
- **Battery**: 100%
- **Network**: FRESH WiFi

## 🎯 Next Steps

### Immediate Actions
1. **Explore the dashboard** - Navigate through different time ranges and filters
2. **Test the refresh functionality** - Use manual refresh and toggle auto-refresh
3. **Monitor temperature trends** - Watch the real-time charts update

### Future Enhancements
1. **Set up temperature alerts** - Configure thresholds for each storage area
2. **Add more sensors** - Expand monitoring to additional storage areas
3. **Export data** - Download temperature logs for HACCP compliance
4. **Mobile access** - Dashboard is fully responsive for mobile monitoring

## 🚨 Troubleshooting

### If sensors show as offline:
1. Check WiFi connectivity for the sensors
2. Verify TempStick API key is valid
3. Restart the CORS proxy server if needed

### If data is not updating:
1. Check the "System Status" indicators
2. Use "Refresh Now" button
3. Verify auto-refresh is enabled

### If charts are not displaying:
1. Select a longer time range (24h or 7d)
2. Ensure sensors are selected in filters
3. Check that sensors have recent readings

## 📞 Support

The dashboard is fully functional and ready for production use. All TempStick sensors are connected and reporting accurate temperature data for your seafood storage monitoring needs.
