-- Enable required extensions
create extension if not exists "uuid-ossp";
create extension if not exists "pgcrypto";

-- Create setup_database function
create or replace function setup_database()
returns void as $$
declare
  policy_exists boolean;
  type_exists boolean;
begin
  -- Check if type exists before creating
  select exists (
    select 1 from pg_type 
    where typname = 'products' 
    and typnamespace = (select oid from pg_namespace where nspname = 'public')
  ) into type_exists;

  -- Create tables if they don't exist
  create table if not exists public.categories (
    id uuid default uuid_generate_v4() primary key,
    name text unique not null,
    rls_enabled boolean default false,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,
    updated_at timestamp with time zone default timezone('utc'::text, now()) not null
  );

  create type condition_type AS ENUM ('fresh', 'frozen', 'other');

  create table if not exists public.vendors (
    id uuid default uuid_generate_v4() primary key,
    name text not null,
    contact_name text,
    email text,
    phone text,
    address text,
    status text default 'active',
    payment_terms text,
    credit_limit numeric(10,2),
    metadata jsonb default '{}'::jsonb,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,
    updated_at timestamp with time zone default timezone('utc'::text, now()) not null
  );

  create table if not exists public.customers (
    id uuid default uuid_generate_v4() primary key,
    name text not null,
    contact_name text,
    email text,
    phone text,
    address text,
    channel_type text not null,
    customer_source text,
    status text default 'active',
    payment_terms text,
    credit_limit numeric(10,2),
    metadata jsonb default '{}'::jsonb,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,
    updated_at timestamp with time zone default timezone('utc'::text, now()) not null
  );

  create table if not exists public.products (
    id uuid default uuid_generate_v4() primary key,
    name text not null,
    category_id uuid references public.categories(id),
    amount numeric(10,2) not null default 0,
    condition condition_type not null,
    other_condition text,
    price numeric(10,2),
    supplier_id uuid references public.vendors(id),
    expiry_date timestamp with time zone,
    supplier text,
    species_details jsonb default '{}'::jsonb,
    notes text,
    rls_enabled boolean default false,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,
    updated_at timestamp with time zone default timezone('utc'::text, now()) not null
  );

  create table if not exists public.events (
    id uuid default uuid_generate_v4() primary key,
    event_type text not null,
    product_id uuid references public.products(id),
    vendor_id uuid references public.vendors(id),
    customer_id uuid references public.customers(id),
    quantity numeric(10,2),
    unit text,
    temperature numeric(5,2),
    quality_status text,
    notes text,
    metadata jsonb default '{}'::jsonb,
    gdst_data jsonb default '{}'::jsonb,
    created_by uuid references auth.users(id),
    created_at timestamp with time zone default timezone('utc'::text, now()) not null
  );

  -- Enable RLS
  alter table if exists public.vendors enable row level security;
  alter table if exists public.customers enable row level security;
  alter table if exists public.products enable row level security;
  alter table if exists public.events enable row level security;

  -- Drop existing policies
  drop policy if exists "Enable read access for authenticated users" on public.vendors;
  drop policy if exists "Enable insert access for authenticated users" on public.vendors;
  drop policy if exists "Enable update access for authenticated users" on public.vendors;
  drop policy if exists "Enable delete access for authenticated users" on public.vendors;

  drop policy if exists "Enable read access for authenticated users" on public.customers;
  drop policy if exists "Enable insert access for authenticated users" on public.customers;
  drop policy if exists "Enable update access for authenticated users" on public.customers;
  drop policy if exists "Enable delete access for authenticated users" on public.customers;

  drop policy if exists "Enable read access for authenticated users" on public.products;
  drop policy if exists "Enable insert access for authenticated users" on public.products;
  drop policy if exists "Enable update access for authenticated users" on public.products;
  drop policy if exists "Enable delete access for authenticated users" on public.products;

  drop policy if exists "Enable read access for authenticated users" on public.events;
  drop policy if exists "Enable insert access for authenticated users" on public.events;
  drop policy if exists "Enable update access for authenticated users" on public.events;
  drop policy if exists "Enable delete access for authenticated users" on public.events;

  -- Create new policies
  -- Vendors policies
  create policy "Enable read access for authenticated users"
    on public.vendors for select
    to authenticated
    using (true);

  create policy "Enable insert access for authenticated users"
    on public.vendors for insert
    to authenticated
    with check (true);

  create policy "Enable update access for authenticated users"
    on public.vendors for update
    to authenticated
    using (true);

  create policy "Enable delete access for authenticated users"
    on public.vendors for delete
    to authenticated
    using (true);

  -- Customers policies
  create policy "Enable read access for authenticated users"
    on public.customers for select
    to authenticated
    using (true);

  create policy "Enable insert access for authenticated users"
    on public.customers for insert
    to authenticated
    with check (true);

  create policy "Enable update access for authenticated users"
    on public.customers for update
    to authenticated
    using (true);

  create policy "Enable delete access for authenticated users"
    on public.customers for delete
    to authenticated
    using (true);

  -- Products policies
  create policy "Enable read access for authenticated users"
    on public.products for select
    to authenticated
    using (true);

  create policy "Enable insert access for authenticated users"
    on public.products for insert
    to authenticated
    with check (true);

  create policy "Enable update access for authenticated users"
    on public.products for update
    to authenticated
    using (true);

  create policy "Enable delete access for authenticated users"
    on public.products for delete
    to authenticated
    using (true);

  -- Events policies
  create policy "Enable read access for authenticated users"
    on public.events for select
    to authenticated
    using (true);

  create policy "Enable insert access for authenticated users"
    on public.events for insert
    to authenticated
    with check (true);

  create policy "Enable update access for authenticated users"
    on public.events for update
    to authenticated
    using (true);

  create policy "Enable delete access for authenticated users"
    on public.events for delete
    to authenticated
    using (true);

  -- Grant necessary permissions
  grant usage on schema public to authenticated;
  grant all privileges on all tables in schema public to authenticated;
  grant all privileges on all sequences in schema public to authenticated;

end;
$$ language plpgsql security definer;

-- Execute the setup function
select setup_database();
