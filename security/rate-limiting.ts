// Rate Limiting and Security Infrastructure for Seafood Manager
// Comprehensive rate limiting and abuse prevention

export interface RateLimit {
  windowMs: number;
  maxRequests: number;
  message: string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

export interface SecurityConfig {
  rateLimits: {
    api: RateLimit;
    auth: RateLimit;
    voice: RateLimit;
    import: RateLimit;
    search: RateLimit;
  };
  security: {
    maxFileSize: number;
    allowedFileTypes: string[];
    maxConcurrentUploads: number;
    sessionTimeout: number;
  };
}

export class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  private blockedIPs: Set<string> = new Set();
  
  constructor(private config: RateLimit) {}

  // Check if request is within rate limit
  isAllowed(identifier: string): boolean {
    const now = Date.now();
    const windowStart = now - this.config.windowMs;
    
    // Get existing requests for this identifier
    let requestTimes = this.requests.get(identifier) || [];
    
    // Remove old requests outside the window
    requestTimes = requestTimes.filter(time => time > windowStart);
    
    // Check if limit exceeded
    if (requestTimes.length >= this.config.maxRequests) {
      this.logRateLimitViolation(identifier);
      return false;
    }
    
    // Add current request
    requestTimes.push(now);
    this.requests.set(identifier, requestTimes);
    
    return true;
  }

  // Get remaining requests in window
  getRemainingRequests(identifier: string): number {
    const now = Date.now();
    const windowStart = now - this.config.windowMs;
    
    const requestTimes = this.requests.get(identifier) || [];
    const recentRequests = requestTimes.filter(time => time > windowStart);
    
    return Math.max(0, this.config.maxRequests - recentRequests.length);
  }

  // Get time until window resets
  getResetTime(identifier: string): number {
    const requestTimes = this.requests.get(identifier) || [];
    if (requestTimes.length === 0) return 0;
    
    const oldestRequest = Math.min(...requestTimes);
    const resetTime = oldestRequest + this.config.windowMs;
    
    return Math.max(0, resetTime - Date.now());
  }

  // Block IP address
  blockIP(ip: string, duration: number = 24 * 60 * 60 * 1000): void {
    this.blockedIPs.add(ip);
    
    setTimeout(() => {
      this.blockedIPs.delete(ip);
    }, duration);
    
    console.warn(`IP ${ip} blocked for ${duration}ms due to rate limit violations`);
  }

  // Check if IP is blocked
  isBlocked(ip: string): boolean {
    return this.blockedIPs.has(ip);
  }

  private logRateLimitViolation(identifier: string): void {
    console.warn(`Rate limit exceeded for identifier: ${identifier}`);
    
    // Track violations
    if (typeof window !== 'undefined' && window.Sentry) {
      window.Sentry.captureMessage('Rate limit exceeded', {
        level: 'warning',
        tags: { identifier, type: 'rate_limit' },
        extra: { config: this.config }
      });
    }
  }
}

// Production security configuration
export const SECURITY_CONFIG: SecurityConfig = {
  rateLimits: {
    api: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 1000,
      message: 'Too many API requests, please try again later'
    },
    auth: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 5,
      message: 'Too many authentication attempts, please try again later'
    },
    voice: {
      windowMs: 1 * 60 * 1000, // 1 minute
      maxRequests: 30,
      message: 'Voice processing rate limit exceeded'
    },
    import: {
      windowMs: 60 * 60 * 1000, // 1 hour
      maxRequests: 10,
      message: 'Import rate limit exceeded, please wait before uploading more files'
    },
    search: {
      windowMs: 1 * 60 * 1000, // 1 minute
      maxRequests: 100,
      message: 'Search rate limit exceeded'
    }
  },
  security: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedFileTypes: [
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/json'
    ],
    maxConcurrentUploads: 3,
    sessionTimeout: 8 * 60 * 60 * 1000 // 8 hours
  }
};

// Rate limiter instances
export const rateLimiters = {
  api: new RateLimiter(SECURITY_CONFIG.rateLimits.api),
  auth: new RateLimiter(SECURITY_CONFIG.rateLimits.auth),
  voice: new RateLimiter(SECURITY_CONFIG.rateLimits.voice),
  import: new RateLimiter(SECURITY_CONFIG.rateLimits.import),
  search: new RateLimiter(SECURITY_CONFIG.rateLimits.search)
};

// Request tracking for analytics
export class SecurityAnalytics {
  private violations: Array<{
    type: string;
    identifier: string;
    timestamp: number;
    metadata?: Record<string, any>;
  }> = [];

  trackViolation(
    type: 'rate_limit' | 'file_size' | 'file_type' | 'concurrent_limit' | 'session_timeout',
    identifier: string,
    metadata?: Record<string, any>
  ): void {
    const violation = {
      type,
      identifier,
      timestamp: Date.now(),
      metadata: metadata || {}
    };

    this.violations.push(violation);
    
    // Keep only last 1000 violations
    if (this.violations.length > 1000) {
      this.violations = this.violations.slice(-1000);
    }

    // Send to monitoring services
    this.reportToMonitoring(violation);
  }

  getViolationStats(): {
    total: number;
    byType: Record<string, number>;
    recent: number; // Last hour
  } {
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    const recentViolations = this.violations.filter(v => v.timestamp > oneHourAgo);
    
    const byType: Record<string, number> = {};
    this.violations.forEach(v => {
      byType[v.type] = (byType[v.type] || 0) + 1;
    });

    return {
      total: this.violations.length,
      byType,
      recent: recentViolations.length
    };
  }

  private reportToMonitoring(violation: any): void {
    console.warn('Security violation:', violation);

    // Send to DataDog
    if (typeof window !== 'undefined' && window.DD_RUM) {
      window.DD_RUM.addAttribute('security_violation', violation.type);
      window.DD_RUM.addAttribute('violation_identifier', violation.identifier);
    }

    // Send to Sentry
    if (typeof window !== 'undefined' && window.Sentry) {
      window.Sentry.captureMessage('Security violation detected', {
        level: 'warning',
        tags: { violation_type: violation.type },
        extra: violation
      });
    }
  }
}

// Global security analytics instance
export const securityAnalytics = new SecurityAnalytics();

// File upload security
export class FileUploadSecurity {
  static validateFile(file: File): { valid: boolean; error?: string } {
    // Check file size
    if (file.size > SECURITY_CONFIG.security.maxFileSize) {
      securityAnalytics.trackViolation('file_size', 'unknown', {
        file_size: file.size,
        max_size: SECURITY_CONFIG.security.maxFileSize
      });
      
      return {
        valid: false,
        error: `File size exceeds maximum allowed size of ${SECURITY_CONFIG.security.maxFileSize / 1024 / 1024}MB`
      };
    }

    // Check file type
    if (!SECURITY_CONFIG.security.allowedFileTypes.includes(file.type)) {
      securityAnalytics.trackViolation('file_type', 'unknown', {
        file_type: file.type,
        allowed_types: SECURITY_CONFIG.security.allowedFileTypes
      });
      
      return {
        valid: false,
        error: `File type ${file.type} is not allowed`
      };
    }

    // Check file name for suspicious patterns
    const suspiciousPatterns = [
      /\.exe$/i,
      /\.scr$/i,
      /\.bat$/i,
      /\.cmd$/i,
      /\.com$/i,
      /\.pif$/i,
      /\.vbs$/i,
      /\.js$/i,
      /\.jar$/i,
      /\.php$/i
    ];

    if (suspiciousPatterns.some(pattern => pattern.test(file.name))) {
      securityAnalytics.trackViolation('file_type', 'unknown', {
        file_name: file.name,
        reason: 'suspicious_extension'
      });
      
      return {
        valid: false,
        error: 'File type not allowed for security reasons'
      };
    }

    return { valid: true };
  }

  static sanitizeFileName(fileName: string): string {
    // Remove dangerous characters and patterns
    return fileName
      .replace(/[^a-zA-Z0-9.-]/g, '_')
      .replace(/\.+/g, '.')
      .replace(/^\./, '')
      .substring(0, 255);
  }
}

// Session security
export class SessionSecurity {
  private static readonly SESSION_KEY = 'seafood_manager_session';
  
  static createSession(userId: string): void {
    const session = {
      userId,
      createdAt: Date.now(),
      lastActivity: Date.now(),
      csrfToken: this.generateCSRFToken()
    };

    try {
      localStorage.setItem(this.SESSION_KEY, JSON.stringify(session));
    } catch (error) {
      console.error('Failed to create session:', error);
    }
  }

  static validateSession(): boolean {
    try {
      const sessionData = localStorage.getItem(this.SESSION_KEY);
      if (!sessionData) return false;

      const session = JSON.parse(sessionData);
      const now = Date.now();
      
      // Check session timeout
      if (now - session.lastActivity > SECURITY_CONFIG.security.sessionTimeout) {
        this.destroySession();
        securityAnalytics.trackViolation('session_timeout', session.userId);
        return false;
      }

      // Update last activity
      session.lastActivity = now;
      localStorage.setItem(this.SESSION_KEY, JSON.stringify(session));
      
      return true;
    } catch (error) {
      console.error('Session validation failed:', error);
      this.destroySession();
      return false;
    }
  }

  static getCSRFToken(): string | null {
    try {
      const sessionData = localStorage.getItem(this.SESSION_KEY);
      if (!sessionData) return null;

      const session = JSON.parse(sessionData);
      return session.csrfToken || null;
    } catch {
      return null;
    }
  }

  static destroySession(): void {
    try {
      localStorage.removeItem(this.SESSION_KEY);
    } catch (error) {
      console.error('Failed to destroy session:', error);
    }
  }

  private static generateCSRFToken(): string {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }
}

// Content Security Policy helper
export class CSPManager {
  static generateNonce(): string {
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    return btoa(String.fromCharCode(...array));
  }

  static buildCSPHeader(nonce?: string): string {
    const cspDirectives = [
      "default-src 'self'",
      `script-src 'self' 'unsafe-inline' ${nonce ? `'nonce-${nonce}'` : ''} https://cdn.openai.com https://api.openai.com`,
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "img-src 'self' data: https: blob:",
      "connect-src 'self' https://*.supabase.co https://api.openai.com wss://*.supabase.co",
      "media-src 'self' blob:",
      "worker-src 'self' blob:",
      "font-src 'self' https://fonts.gstatic.com",
      "frame-ancestors 'none'",
      "base-uri 'self'",
      "form-action 'self'",
      "upgrade-insecure-requests"
    ];

    return cspDirectives.join('; ');
  }

  static reportCSPViolation(violation: any): void {
    console.warn('CSP Violation:', violation);
    
    securityAnalytics.trackViolation('csp_violation', 'unknown', {
      blocked_uri: violation.blockedURI,
      violated_directive: violation.violatedDirective,
      document_uri: violation.documentURI
    });
  }
}

// Initialize security monitoring
if (typeof window !== 'undefined') {
  // Listen for CSP violations
  document.addEventListener('securitypolicyviolation', CSPManager.reportCSPViolation);
  
  // Validate session on page load
  if (!SessionSecurity.validateSession() && window.location.pathname !== '/login') {
    console.warn('Invalid session detected, redirecting to login');
    // Handle redirect to login page
  }

  // Set up periodic session validation
  setInterval(() => {
    if (!SessionSecurity.validateSession()) {
      console.warn('Session expired, user needs to re-authenticate');
      // Handle session expiration
    }
  }, 5 * 60 * 1000); // Check every 5 minutes
}