// HACCP Compliance and Audit Logging for Seafood Manager
// Tamper-proof logging system for regulatory compliance

export interface ComplianceEvent {
  id: string;
  timestamp: string;
  event_type: 'access' | 'modification' | 'deletion' | 'export' | 'import' | 'temperature' | 'critical_control_point';
  user_id: string;
  user_email?: string;
  resource_type: 'inventory' | 'product' | 'batch' | 'report' | 'user' | 'system';
  resource_id?: string;
  action: string;
  details: Record<string, any>;
  ip_address: string;
  user_agent: string;
  session_id: string;
  compliance_category: 'HACCP' | 'GDST' | 'FDA' | 'USDA' | 'GENERAL';
  risk_level: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  integrity_hash: string;
}

export interface AuditTrail {
  events: ComplianceEvent[];
  metadata: {
    total_events: number;
    date_range: { start: string; end: string };
    integrity_verified: boolean;
    generated_at: string;
  };
}

export class ComplianceLogger {
  private static instance: ComplianceLogger;
  private eventQueue: ComplianceEvent[] = [];
  private hashChain: string[] = [];
  private isProcessing = false;

  static getInstance(): ComplianceLogger {
    if (!this.instance) {
      this.instance = new ComplianceLogger();
    }
    return this.instance;
  }

  // Log compliance-critical events
  async logEvent(
    eventType: ComplianceEvent['event_type'],
    userId: string,
    resourceType: ComplianceEvent['resource_type'],
    action: string,
    details: Record<string, any>,
    options: {
      resourceId?: string;
      userEmail?: string;
      complianceCategory?: ComplianceEvent['compliance_category'];
      riskLevel?: ComplianceEvent['risk_level'];
    } = {}
  ): Promise<void> {
    const event: ComplianceEvent = {
      id: this.generateEventId(),
      timestamp: new Date().toISOString(),
      event_type: eventType,
      user_id: userId,
      user_email: options.userEmail,
      resource_type: resourceType,
      resource_id: options.resourceId,
      action,
      details: this.sanitizeDetails(details),
      ip_address: await this.getClientIP(),
      user_agent: navigator.userAgent,
      session_id: this.getSessionId(),
      compliance_category: options.complianceCategory || 'GENERAL',
      risk_level: options.riskLevel || this.calculateRiskLevel(eventType, action),
      integrity_hash: '' // Will be calculated
    };

    // Calculate integrity hash
    event.integrity_hash = await this.calculateIntegrityHash(event);

    // Add to queue
    this.eventQueue.push(event);

    // Process queue if not already processing
    if (!this.isProcessing) {
      this.processEventQueue();
    }

    // Send critical events immediately
    if (event.risk_level === 'CRITICAL') {
      await this.sendCriticalAlert(event);
    }
  }

  // Process queued events in batches
  private async processEventQueue(): Promise<void> {
    if (this.isProcessing || this.eventQueue.length === 0) return;

    this.isProcessing = true;

    try {
      const batchSize = 10;
      const batch = this.eventQueue.splice(0, batchSize);

      // Store events in Supabase with integrity verification
      await this.storeEventBatch(batch);

      // Update hash chain
      this.updateHashChain(batch);

      // Send to external audit systems
      await this.forwardToAuditSystems(batch);

    } catch (error) {
      console.error('Failed to process compliance events:', error);
      
      // Re-add failed events to front of queue
      this.eventQueue.unshift(...this.eventQueue.splice(0, 0));
      
      // Alert on compliance logging failure
      await this.sendComplianceSystemAlert(error as Error);
    } finally {
      this.isProcessing = false;

      // Process remaining events
      if (this.eventQueue.length > 0) {
        setTimeout(() => this.processEventQueue(), 1000);
      }
    }
  }

  // Store events in Supabase with RLS protection
  private async storeEventBatch(events: ComplianceEvent[]): Promise<void> {
    try {
      const { createClient } = await import('@supabase/supabase-js');
      const supabase = createClient(
        process.env.VITE_SUPABASE_URL!,
        process.env.VITE_SUPABASE_SERVICE_ROLE_KEY! // Use service role for audit logging
      );

      const { error } = await supabase
        .from('compliance_audit_log')
        .insert(events.map(event => ({
          ...event,
          created_at: event.timestamp
        })));

      if (error) {
        throw new Error(`Database storage failed: ${error.message}`);
      }

      console.log(`Stored ${events.length} compliance events`);
    } catch (error) {
      console.error('Failed to store compliance events:', error);
      throw error;
    }
  }

  // Generate tamper-proof hash chain
  private updateHashChain(events: ComplianceEvent[]): void {
    const lastHash = this.hashChain[this.hashChain.length - 1] || '0';
    
    events.forEach(event => {
      const hashInput = lastHash + JSON.stringify(event);
      const hash = this.sha256(hashInput);
      this.hashChain.push(hash);
    });

    // Keep only last 1000 hashes in memory
    if (this.hashChain.length > 1000) {
      this.hashChain = this.hashChain.slice(-1000);
    }
  }

  // Calculate integrity hash for event
  private async calculateIntegrityHash(event: Omit<ComplianceEvent, 'integrity_hash'>): Promise<string> {
    const eventString = JSON.stringify(event, Object.keys(event).sort());
    return this.sha256(eventString);
  }

  // Simple SHA-256 implementation
  private sha256(message: string): string {
    // In production, use Web Crypto API
    if (typeof crypto !== 'undefined' && crypto.subtle) {
      // This is async, but for simplicity using sync version
      // In real implementation, make this properly async
    }
    
    // Fallback simple hash (for demo - use proper crypto in production)
    let hash = 0;
    for (let i = 0; i < message.length; i++) {
      const char = message.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(16);
  }

  // Forward events to external audit systems
  private async forwardToAuditSystems(events: ComplianceEvent[]): Promise<void> {
    const forwardingPromises = [];

    // Send to external SIEM
    if (process.env.VITE_SIEM_ENDPOINT) {
      forwardingPromises.push(this.forwardToSIEM(events));
    }

    // Send to compliance monitoring service
    if (process.env.VITE_COMPLIANCE_WEBHOOK) {
      forwardingPromises.push(this.forwardToComplianceService(events));
    }

    // Send high-risk events to security team
    const highRiskEvents = events.filter(e => ['HIGH', 'CRITICAL'].includes(e.risk_level));
    if (highRiskEvents.length > 0) {
      forwardingPromises.push(this.alertSecurityTeam(highRiskEvents));
    }

    await Promise.allSettled(forwardingPromises);
  }

  private async forwardToSIEM(events: ComplianceEvent[]): Promise<void> {
    try {
      await fetch(process.env.VITE_SIEM_ENDPOINT!, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.VITE_SIEM_API_KEY}`
        },
        body: JSON.stringify({
          source: 'seafood-manager',
          events: events.map(event => ({
            ...event,
            '@timestamp': event.timestamp,
            'event.category': 'compliance',
            'event.type': event.event_type,
            'user.id': event.user_id,
            'source.ip': event.ip_address
          }))
        })
      });
    } catch (error) {
      console.error('Failed to forward to SIEM:', error);
    }
  }

  private async forwardToComplianceService(events: ComplianceEvent[]): Promise<void> {
    try {
      await fetch(process.env.VITE_COMPLIANCE_WEBHOOK!, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          application: 'seafood-manager',
          timestamp: new Date().toISOString(),
          events
        })
      });
    } catch (error) {
      console.error('Failed to forward to compliance service:', error);
    }
  }

  private async alertSecurityTeam(events: ComplianceEvent[]): Promise<void> {
    try {
      if (process.env.VITE_SECURITY_ALERT_WEBHOOK) {
        await fetch(process.env.VITE_SECURITY_ALERT_WEBHOOK, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            text: `🚨 High-risk compliance events detected in Seafood Manager`,
            attachments: [{
              color: 'danger',
              title: `${events.length} high-risk compliance events`,
              fields: events.map(event => ({
                title: `${event.event_type} - ${event.risk_level}`,
                value: `User: ${event.user_id}\nAction: ${event.action}\nResource: ${event.resource_type}:${event.resource_id}`,
                short: true
              }))
            }]
          })
        });
      }
    } catch (error) {
      console.error('Failed to alert security team:', error);
    }
  }

  // Critical event immediate alerting
  private async sendCriticalAlert(event: ComplianceEvent): Promise<void> {
    console.error('CRITICAL COMPLIANCE EVENT:', event);

    // Immediate notification for critical events
    const alertPromises = [];

    // Send to monitoring systems
    if (typeof window !== 'undefined' && window.Sentry) {
      window.Sentry.captureMessage('Critical compliance event', {
        level: 'error',
        tags: { compliance: true, critical: true },
        extra: event
      });
    }

    // Send immediate alert
    if (process.env.VITE_CRITICAL_ALERT_WEBHOOK) {
      alertPromises.push(
        fetch(process.env.VITE_CRITICAL_ALERT_WEBHOOK, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            text: `🚨 CRITICAL COMPLIANCE EVENT`,
            attachments: [{
              color: 'danger',
              title: 'Critical compliance violation detected',
              fields: [
                { title: 'Event Type', value: event.event_type, short: true },
                { title: 'User', value: event.user_id, short: true },
                { title: 'Action', value: event.action, short: false },
                { title: 'Risk Level', value: event.risk_level, short: true },
                { title: 'Timestamp', value: event.timestamp, short: true }
              ],
              text: `Immediate investigation required for compliance event in ${event.resource_type}`
            }]
          })
        })
      );
    }

    await Promise.allSettled(alertPromises);
  }

  // Compliance system failure alerting
  private async sendComplianceSystemAlert(error: Error): Promise<void> {
    console.error('COMPLIANCE SYSTEM FAILURE:', error);

    try {
      if (process.env.VITE_SYSTEM_ALERT_WEBHOOK) {
        await fetch(process.env.VITE_SYSTEM_ALERT_WEBHOOK, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            text: `🔥 COMPLIANCE LOGGING SYSTEM FAILURE`,
            attachments: [{
              color: 'danger',
              title: 'Compliance audit logging system has failed',
              text: `Error: ${error.message}\n\nThis is a critical system failure that may impact regulatory compliance. Immediate attention required.`,
              fields: [
                { title: 'Error', value: error.message, short: false },
                { title: 'Stack', value: error.stack?.substring(0, 500) || 'No stack trace', short: false },
                { title: 'Time', value: new Date().toISOString(), short: true }
              ]
            }]
          })
        });
      }
    } catch (alertError) {
      console.error('Failed to send compliance system alert:', alertError);
    }
  }

  // Generate audit report
  async generateAuditReport(
    startDate: string,
    endDate: string,
    filters?: {
      userId?: string;
      eventType?: string;
      complianceCategory?: string;
      riskLevel?: string;
    }
  ): Promise<AuditTrail> {
    try {
      const { createClient } = await import('@supabase/supabase-js');
      const supabase = createClient(
        process.env.VITE_SUPABASE_URL!,
        process.env.VITE_SUPABASE_SERVICE_ROLE_KEY!
      );

      let query = supabase
        .from('compliance_audit_log')
        .select('*')
        .gte('timestamp', startDate)
        .lte('timestamp', endDate)
        .order('timestamp', { ascending: true });

      // Apply filters
      if (filters?.userId) {
        query = query.eq('user_id', filters.userId);
      }
      if (filters?.eventType) {
        query = query.eq('event_type', filters.eventType);
      }
      if (filters?.complianceCategory) {
        query = query.eq('compliance_category', filters.complianceCategory);
      }
      if (filters?.riskLevel) {
        query = query.eq('risk_level', filters.riskLevel);
      }

      const { data: events, error } = await query;

      if (error) {
        throw new Error(`Failed to generate audit report: ${error.message}`);
      }

      // Verify integrity of events
      const integrityVerified = await this.verifyEventIntegrity(events || []);

      return {
        events: events || [],
        metadata: {
          total_events: events?.length || 0,
          date_range: { start: startDate, end: endDate },
          integrity_verified: integrityVerified,
          generated_at: new Date().toISOString()
        }
      };
    } catch (error) {
      console.error('Failed to generate audit report:', error);
      throw error;
    }
  }

  // Verify integrity of audit events
  private async verifyEventIntegrity(events: ComplianceEvent[]): Promise<boolean> {
    try {
      for (const event of events) {
        const calculatedHash = await this.calculateIntegrityHash({
          ...event,
          integrity_hash: undefined
        } as any);

        if (calculatedHash !== event.integrity_hash) {
          console.error('Integrity check failed for event:', event.id);
          return false;
        }
      }
      return true;
    } catch (error) {
      console.error('Integrity verification failed:', error);
      return false;
    }
  }

  // Helper methods
  private generateEventId(): string {
    return `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async getClientIP(): Promise<string> {
    try {
      // In production, this would be provided by the server
      return 'client-ip-not-available';
    } catch {
      return 'unknown';
    }
  }

  private getSessionId(): string {
    try {
      const sessionData = localStorage.getItem('seafood_manager_session');
      if (sessionData) {
        const session = JSON.parse(sessionData);
        return session.sessionId || 'unknown';
      }
    } catch {
      // Fallback
    }
    return 'unknown';
  }

  private calculateRiskLevel(
    eventType: ComplianceEvent['event_type'],
    action: string
  ): ComplianceEvent['risk_level'] {
    // Critical actions
    if (action.includes('delete') || action.includes('destroy') || eventType === 'critical_control_point') {
      return 'CRITICAL';
    }

    // High risk actions
    if (action.includes('modify') || action.includes('export') || eventType === 'temperature') {
      return 'HIGH';
    }

    // Medium risk actions
    if (action.includes('create') || action.includes('import') || eventType === 'modification') {
      return 'MEDIUM';
    }

    return 'LOW';
  }

  private sanitizeDetails(details: Record<string, any>): Record<string, any> {
    const sanitized = { ...details };
    
    // Remove sensitive information
    const sensitiveKeys = ['password', 'token', 'key', 'secret', 'credit_card'];
    sensitiveKeys.forEach(key => {
      if (key in sanitized) {
        sanitized[key] = '[REDACTED]';
      }
    });

    return sanitized;
  }
}

// Global compliance logger instance
export const complianceLogger = ComplianceLogger.getInstance();

// Helper functions for common compliance events
export const logComplianceEvent = {
  // Inventory operations
  inventoryAccess: (userId: string, inventoryId: string, action: string) =>
    complianceLogger.logEvent('access', userId, 'inventory', action, { inventory_id: inventoryId }),

  inventoryModification: (userId: string, inventoryId: string, changes: Record<string, any>) =>
    complianceLogger.logEvent('modification', userId, 'inventory', 'modify', { inventory_id: inventoryId, changes }),

  // Temperature monitoring
  temperatureCheck: (userId: string, temperature: number, location: string, product: string) =>
    complianceLogger.logEvent('temperature', userId, 'system', 'temperature_check', {
      temperature,
      location,
      product,
      compliance_required: true
    }, { complianceCategory: 'HACCP', riskLevel: 'HIGH' }),

  // Data export/import
  dataExport: (userId: string, dataType: string, recordCount: number) =>
    complianceLogger.logEvent('export', userId, 'system', 'data_export', {
      data_type: dataType,
      record_count: recordCount
    }, { complianceCategory: 'GDST', riskLevel: 'MEDIUM' }),

  dataImport: (userId: string, dataType: string, recordCount: number, source: string) =>
    complianceLogger.logEvent('import', userId, 'system', 'data_import', {
      data_type: dataType,
      record_count: recordCount,
      source
    }, { complianceCategory: 'GDST', riskLevel: 'MEDIUM' }),

  // Critical control points
  ccpViolation: (userId: string, ccpType: string, violation: string, corrective_action: string) =>
    complianceLogger.logEvent('critical_control_point', userId, 'system', 'ccp_violation', {
      ccp_type: ccpType,
      violation,
      corrective_action,
      requires_investigation: true
    }, { complianceCategory: 'HACCP', riskLevel: 'CRITICAL' }),

  // User actions
  userLogin: (userId: string, success: boolean, ipAddress?: string) =>
    complianceLogger.logEvent('access', userId, 'user', success ? 'login_success' : 'login_failure', {
      success,
      ip_address: ipAddress
    }),

  userLogout: (userId: string) =>
    complianceLogger.logEvent('access', userId, 'user', 'logout', {})
};