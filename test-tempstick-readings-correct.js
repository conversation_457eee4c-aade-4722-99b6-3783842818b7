#!/usr/bin/env node

/**
 * Test Correct TempStick Readings Endpoint
 */

import fetch from 'node-fetch';
import 'dotenv/config';

const API_KEY = process.env.VITE_TEMPSTICK_API_KEY;
const BASE_URL = 'https://tempstickapi.com/api/v1';

async function testCorrectReadings() {
  console.log('🧪 Testing Correct TempStick Readings Endpoint...\n');
  
  const sensorId = '2550380'; // Downstairs Walk in Freezer
  const url = `${BASE_URL}/sensor/${sensorId}/readings`;
  
  console.log(`🌐 Making request to: ${url}`);
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'X-API-KEY': API_KEY,
        'Accept': 'application/json',
        'User-Agent': 'SeafoodManager/1.0'
      }
    });
    
    console.log(`📡 Response status: ${response.status} ${response.statusText}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }
    
    const data = await response.json();
    console.log(`✅ Response received:`, JSON.stringify(data, null, 2));
    
  } catch (error) {
    console.error(`❌ Request failed:`, error.message);
  }
}

testCorrectReadings().catch(console.error);