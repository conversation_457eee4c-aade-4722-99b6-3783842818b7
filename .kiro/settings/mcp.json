{"mcpServers": {"byterover-mcp": {"serverUrl": "https://mcp.byterover.dev/mcp?machineId=1f08235b-6edf-6110-9fd9-f79d2ff4b2f2", "disabled": false, "autoApprove": ["byterover-retrieve-knowledge", "byterover-store-knowledge"]}, "context7": {"command": "uvx", "args": ["context7"], "env": {"FASTMCP_LOG_LEVEL": "ERROR"}, "disabled": false, "autoApprove": ["context7-search", "context7-store", "context7-retrieve"]}, "playwright": {"command": "uvx", "args": ["mcp-server-playwright"], "env": {"FASTMCP_LOG_LEVEL": "ERROR"}, "disabled": false, "autoApprove": ["playwright_screenshot", "playwright_click", "playwright_fill", "playwright_navigate"]}, "github": {"command": "uvx", "args": ["mcp-server-github"], "env": {"FASTMCP_LOG_LEVEL": "ERROR"}, "disabled": false, "autoApprove": ["github_search_repositories", "github_get_repository", "github_list_issues", "github_get_issue"]}}}