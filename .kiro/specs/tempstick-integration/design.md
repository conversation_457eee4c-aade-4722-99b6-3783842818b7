# Design Document

## Overview

The TempStick integration system provides comprehensive temperature monitoring capabilities for seafood storage facilities. It integrates wireless TempStick sensors with the existing Seafood Manager application to deliver real-time monitoring, HACCP compliance tracking, automated alerting, and comprehensive reporting.

The system builds upon the existing database schema and service layer while adding new UI components for sensor management, dashboard visualization, and alert handling. It leverages the TempStick API for sensor data synchronization and provides seamless integration with the existing inventory management system.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[TempStick Sensors] --> B[TempStick API]
    B --> C[TempStick Service]
    C --> D[Supabase Database]
    D --> E[Real-time Subscriptions]
    E --> F[Dashboard UI]
    F --> G[Alert System]
    G --> H[Notification Service]
    
    I[Background Sync] --> C
    J[HACCP Compliance] --> D
    K[Reporting Engine] --> D
    L[Mobile App] --> F
```

### Component Architecture

The system consists of five main architectural layers:

1. **Data Synchronization Layer**: TempStick API integration and data sync
2. **Database Layer**: Temperature data storage and HACCP compliance tracking
3. **Business Logic Layer**: Alert processing, compliance checking, and reporting
4. **UI Layer**: Dashboard, sensor management, and mobile interfaces
5. **Integration Layer**: Inventory system integration and external exports

### Technology Stack Integration

- **Sensors**: TempStick wireless temperature/humidity sensors
- **API Integration**: TempStick REST API with OAuth authentication
- **Database**: PostgreSQL with Supabase, time-series optimized tables
- **Real-time Updates**: Supabase real-time subscriptions
- **Frontend**: React/TypeScript with responsive design
- **Background Jobs**: Scheduled sync and alert processing
- **Notifications**: Email, SMS, and push notification services
- **Reporting**: PDF generation, Excel exports, Google Sheets integration

## Components and Interfaces

### 1. TempStick Service (Enhanced)

**Purpose**: Handles all TempStick API integration and data synchronization

**Key Methods**:
```typescript
interface TempStickService {
  // Sensor Management
  syncSensors(): Promise<void>
  registerSensor(sensorId: string, config: SensorConfig): Promise<Sensor>
  updateSensorConfig(sensorId: string, config: Partial<SensorConfig>): Promise<void>
  
  // Data Synchronization
  syncAllTemperatureReadings(): Promise<SyncResult>
  syncSensorReadings(sensorId: string): Promise<SyncResult>
  scheduledSync(): Promise<void>
  
  // Real-time Monitoring
  getCurrentReadings(): Promise<TemperatureReading[]>
  getReadingsForDateRange(start: Date, end: Date, sensorIds?: string[]): Promise<TemperatureReading[]>
  
  // Alert Management
  getActiveAlerts(): Promise<TemperatureAlert[]>
  resolveAlert(alertId: string, resolution: AlertResolution): Promise<void>
  checkAlertConditions(sensorId: string, readings: TemperatureReading[]): Promise<void>
}
```

**Integration**: Extends existing service with enhanced error handling, retry logic, and performance optimization.

### 2. Temperature Dashboard Component

**Purpose**: Main dashboard for real-time temperature monitoring

**Key Features**:
- Real-time temperature display with auto-refresh
- Sensor status indicators and health monitoring
- Interactive temperature trend charts
- Alert summary and quick actions
- Storage area overview with compliance status

```typescript
interface TemperatureDashboardProps {
  timeRange: TimeRange
  selectedSensors?: string[]
  autoRefresh: boolean
  onAlertAction: (alertId: string, action: AlertAction) => void
  onSensorSelect: (sensorId: string) => void
}
```

### 3. Sensor Management Component

**Purpose**: Configuration and management of TempStick sensors

**Key Features**:
- Sensor discovery and registration
- Configuration of thresholds and alert settings
- Calibration tracking and maintenance scheduling
- Storage area assignment and mapping
- Battery and connectivity monitoring

```typescript
interface SensorManagementProps {
  sensors: Sensor[]
  storageAreas: StorageArea[]
  onSensorUpdate: (sensorId: string, config: SensorConfig) => Promise<void>
  onSensorDelete: (sensorId: string) => Promise<void>
  onCalibrationSchedule: (sensorId: string, date: Date) => Promise<void>
}
```

### 4. Alert Management System

**Purpose**: Handles temperature alerts and notifications

**Key Features**:
- Real-time alert generation and processing
- Escalation rules and notification routing
- Alert acknowledgment and resolution tracking
- HACCP violation documentation
- Corrective action workflow

```typescript
interface AlertManagementSystem {
  processAlert(alert: TemperatureAlert): Promise<void>
  escalateAlert(alertId: string): Promise<void>
  sendNotifications(alert: TemperatureAlert, recipients: string[]): Promise<void>
  documentCorrectiveAction(alertId: string, action: CorrectiveAction): Promise<void>
  generateAlertReport(dateRange: DateRange): Promise<AlertReport>
}
```

### 5. Reporting and Export System

**Purpose**: Generates compliance reports and exports data

**Key Features**:
- HACCP compliance reports with temperature logs
- Automated report scheduling and email delivery
- Multi-format exports (PDF, Excel, CSV)
- Google Sheets integration for real-time data
- Regulatory reporting templates

```typescript
interface ReportingSystem {
  generateHACCPReport(params: HACCPReportParams): Promise<HACCPReport>
  generateTemperatureLog(params: TemperatureLogParams): Promise<TemperatureLog>
  scheduleReport(config: ReportScheduleConfig): Promise<void>
  exportToGoogleSheets(config: GoogleSheetsConfig): Promise<void>
  exportToExcel(data: TemperatureData[], format: ExcelFormat): Promise<Buffer>
}
```

## Data Models

### Enhanced Database Schema

The system uses the existing comprehensive database schema with the following key tables:

#### Storage Areas
```typescript
interface StorageArea {
  id: string
  name: string
  area_type: 'freezer' | 'refrigerator' | 'dry_storage' | 'processing' | 'receiving'
  required_temp_min: number | null
  required_temp_max: number | null
  haccp_control_point: boolean
  alert_threshold_minutes: number
  monitoring_frequency_minutes: number
  description?: string
  created_at: string
}
```

#### Sensors (TempStick Integration)
```typescript
interface Sensor {
  id: string
  tempstick_sensor_id: string
  name: string
  location: string
  sensor_type: 'temperature_humidity' | 'temperature_only'
  installation_date?: string
  calibration_date?: string
  next_calibration_date?: string
  temp_min_threshold: number | null
  temp_max_threshold: number | null
  humidity_min_threshold?: number | null
  humidity_max_threshold?: number | null
  storage_area_id: string | null
  active: boolean
  battery_level?: number
  signal_strength?: number
  last_reading_at?: string
  connection_status: 'online' | 'offline' | 'maintenance' | 'error'
  created_at: string
}
```

#### Temperature Readings (Time-Series Data)
```typescript
interface TemperatureReading {
  id: string
  sensor_id: string
  temperature_celsius: number
  temperature_fahrenheit: number
  humidity?: number
  reading_timestamp: string
  within_safe_range: boolean
  temp_violation: boolean
  humidity_violation: boolean
  sync_status: 'synced' | 'pending' | 'failed'
  data_quality_score: number
  created_at: string
}
```

#### Temperature Alerts
```typescript
interface TemperatureAlert {
  id: string
  sensor_id: string
  alert_type: 'temp_high' | 'temp_low' | 'humidity_high' | 'humidity_low' | 'sensor_offline' | 'haccp_violation'
  severity: 'info' | 'warning' | 'critical' | 'emergency'
  temperature?: number
  humidity?: number
  alert_timestamp: string
  resolved_timestamp?: string
  acknowledged_at?: string
  acknowledged_by?: string
  haccp_violation: boolean
  product_safety_risk: 'none' | 'low' | 'medium' | 'high' | 'critical'
  escalated: boolean
  escalation_level: number
  notification_sent: boolean
  corrective_actions?: CorrectiveAction[]
  notes?: string
  created_at: string
}
```

### Integration Models

#### Inventory Event Temperature Integration
```typescript
interface InventoryEventWithTemperature extends InventoryEvent {
  storage_area_id?: string
  temp_at_event_celsius?: number
  temp_at_event_fahrenheit?: number
  humidity_at_event?: number
  temp_compliant: boolean
  temp_sensor_id?: string
  temp_reading_id?: string
  cold_chain_maintained: boolean
  temp_violation_notes?: string
}
```

#### Product Storage Requirements
```typescript
interface ProductStorageRequirement {
  id: string
  product_category: string
  product_name?: string
  min_temperature_celsius: number
  max_temperature_celsius: number
  max_humidity?: number
  storage_duration_hours?: number
  haccp_critical: boolean
  cold_chain_required: boolean
  notes?: string
  created_at: string
}
```

## Error Handling

### Temperature Monitoring Errors

1. **Sensor Connectivity Issues**: Automatic retry with exponential backoff
2. **Data Synchronization Failures**: Queue failed syncs for retry
3. **Alert Processing Errors**: Fallback notification methods
4. **API Rate Limiting**: Intelligent request throttling and queuing

### Error Recovery Strategies

```typescript
interface ErrorRecoveryStrategy {
  retrySensorSync(sensorId: string, maxRetries: number): Promise<SyncResult>
  handleApiRateLimit(retryAfter: number): Promise<void>
  fallbackAlertNotification(alert: TemperatureAlert): Promise<void>
  queueFailedSync(syncData: SyncData): Promise<void>
  processQueuedSyncs(): Promise<SyncResult[]>
}
```

### Data Quality Assurance

- **Reading Validation**: Detect and flag anomalous temperature readings
- **Sensor Health Monitoring**: Track sensor performance and data quality
- **Gap Detection**: Identify and alert on missing data periods
- **Calibration Tracking**: Monitor sensor accuracy and schedule maintenance

## Testing Strategy

### Unit Testing

1. **TempStick Service**: Test API integration, data transformation, and error handling
2. **Alert Processing**: Test alert generation, escalation, and notification logic
3. **Database Operations**: Test CRUD operations, triggers, and data integrity
4. **UI Components**: Test rendering, user interactions, and real-time updates

### Integration Testing

1. **End-to-End Sync**: Test complete sensor-to-dashboard data flow
2. **Alert Workflow**: Test alert generation through resolution
3. **Real-time Updates**: Test dashboard updates with live data
4. **Cross-browser Compatibility**: Test on multiple browsers and devices

### Performance Testing

1. **High-Frequency Data**: Test with rapid temperature reading ingestion
2. **Large Dataset Queries**: Test dashboard performance with historical data
3. **Concurrent Users**: Test multi-user dashboard access
4. **Mobile Performance**: Test responsive design and mobile interactions

### HACCP Compliance Testing

1. **Regulatory Requirements**: Verify compliance with FDA and HACCP standards
2. **Audit Trail Integrity**: Test complete audit trail maintenance
3. **Report Accuracy**: Verify report calculations and compliance metrics
4. **Data Retention**: Test long-term data storage and retrieval

### Test Data Strategy

```typescript
interface TemperatureTestScenario {
  description: string
  sensorConfig: SensorConfig
  temperatureReadings: TemperatureReading[]
  expectedAlerts: TemperatureAlert[]
  expectedCompliance: boolean
  testCategory: 'normal_operation' | 'violation_detection' | 'sensor_failure' | 'data_quality'
}
```

### Automated Testing Pipeline

1. **Sensor Simulation**: Mock TempStick API responses for consistent testing
2. **Database Migration Tests**: Ensure schema changes maintain data integrity
3. **Alert Processing Tests**: Automated testing of alert generation and escalation
4. **Performance Regression Tests**: Monitor dashboard and query performance
5. **Mobile Compatibility Tests**: Automated testing across device types

The testing strategy ensures reliable temperature monitoring, accurate HACCP compliance tracking, and robust system performance under various operational conditions.