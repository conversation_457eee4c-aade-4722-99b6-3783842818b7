# Design Document

## Overview

The voice event management system enhances the existing voice processing capabilities by providing a complete workflow for voice-to-database event creation, display, and editing. This system builds upon the current VoiceProcessor and voice processing API to create a seamless user experience for managing seafood inventory events through voice commands.

The system integrates with the existing `events` and `inventory_events` tables, leveraging the current voice processing infrastructure while adding new UI components for event management and editing capabilities.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[Voice Input] --> B[Voice Processor]
    B --> C[Event Parser]
    C --> D[Database Storage]
    D --> E[Event Display UI]
    E --> F[Event Editor]
    F --> D
    
    G[Audio Confirmation] --> A
    H[Real-time Updates] --> E
    I[Audit Trail] --> D
```

### Component Architecture

The system consists of four main architectural layers:

1. **Voice Processing Layer**: Existing VoiceProcessor with enhanced event creation capabilities
2. **Data Layer**: Enhanced database operations for event management
3. **UI Layer**: New components for event display and editing
4. **Integration Layer**: Connections to existing seafood management systems

### Technology Stack Integration

- **Frontend**: React/TypeScript components integrated with existing UI framework
- **Voice Processing**: Existing OpenAI Whisper + GPT-4 pipeline
- **Database**: PostgreSQL with Supabase, using existing `events` and `inventory_events` tables
- **Real-time Updates**: Supabase real-time subscriptions
- **State Management**: React hooks with local state management

## Components and Interfaces

### 1. Enhanced Voice Event Processor

**Purpose**: Extends existing VoiceProcessor to handle event creation workflow

**Key Methods**:
```typescript
interface EnhancedVoiceEventProcessor {
  processVoiceEvent(audioBlob: Blob): Promise<VoiceEventResult>
  saveEventToDatabase(eventData: VoiceEventData): Promise<DatabaseEvent>
  provideAudioConfirmation(eventData: VoiceEventData): Promise<void>
}

interface VoiceEventResult {
  eventData: VoiceEventData
  confidence: number
  requiresConfirmation: boolean
  audioConfirmation?: string
}
```

**Integration**: Builds upon existing `VoiceProcessor` class, adding database persistence and confirmation features.

### 2. Voice Event Display Component

**Purpose**: Shows voice-added events in a dedicated interface

**Key Features**:
- Real-time event list with filtering and search
- Confidence score indicators
- Audio playback for original recordings
- Quick edit access

```typescript
interface VoiceEventDisplayProps {
  events: VoiceEvent[]
  onEventEdit: (eventId: string) => void
  onEventDelete: (eventId: string) => void
  filters: EventFilters
  onFiltersChange: (filters: EventFilters) => void
}
```

### 3. Voice Event Editor Component

**Purpose**: Provides editing capabilities for voice-added events

**Key Features**:
- Form-based editing with validation
- Confidence score display
- Original audio playback
- Change tracking and audit trail

```typescript
interface VoiceEventEditorProps {
  event: VoiceEvent
  onSave: (updatedEvent: VoiceEvent) => Promise<void>
  onCancel: () => void
  readOnly?: boolean
}
```

### 4. Voice Event Database Service

**Purpose**: Handles all database operations for voice events

**Key Methods**:
```typescript
interface VoiceEventService {
  createVoiceEvent(eventData: VoiceEventData): Promise<VoiceEvent>
  getVoiceEvents(filters?: EventFilters): Promise<VoiceEvent[]>
  updateVoiceEvent(eventId: string, updates: Partial<VoiceEvent>): Promise<VoiceEvent>
  deleteVoiceEvent(eventId: string): Promise<void>
  getEventAuditTrail(eventId: string): Promise<EventAudit[]>
}
```

## Data Models

### Enhanced Voice Event Model

```typescript
interface VoiceEvent {
  id: string
  event_type: 'receiving' | 'disposal' | 'physical_count' | 'sale'
  product_name: string
  quantity: number
  unit: 'lbs' | 'kg' | 'cases' | 'units'
  vendor_name?: string
  customer_name?: string
  condition?: 'Excellent' | 'Good' | 'Fair' | 'Poor' | 'Damaged'
  event_date: string
  occurred_at: string
  temperature?: number
  temperature_unit?: 'fahrenheit' | 'celsius'
  processing_method?: string
  quality_grade?: string
  market_form?: string
  notes?: string
  
  // Voice-specific fields
  voice_confidence_score: number
  voice_confidence_breakdown: {
    product_match: number
    quantity_extraction: number
    vendor_match: number
    overall: number
  }
  raw_transcript: string
  audio_recording_url?: string
  created_by_voice: boolean
  
  // Metadata
  created_at: string
  updated_at: string
  created_by: string
  last_modified_by?: string
}
```

### Event Audit Trail Model

```typescript
interface EventAudit {
  id: string
  event_id: string
  field_name: string
  old_value: any
  new_value: any
  changed_by: string
  changed_at: string
  change_reason?: string
}
```

### Database Schema Extensions

The system will extend the existing `events` table with voice-specific columns:

```sql
-- Add voice-specific columns to existing events table
ALTER TABLE events ADD COLUMN IF NOT EXISTS voice_confidence_score DECIMAL(3,2);
ALTER TABLE events ADD COLUMN IF NOT EXISTS voice_confidence_breakdown JSONB;
ALTER TABLE events ADD COLUMN IF NOT EXISTS raw_transcript TEXT;
ALTER TABLE events ADD COLUMN IF NOT EXISTS audio_recording_url TEXT;
ALTER TABLE events ADD COLUMN IF NOT EXISTS created_by_voice BOOLEAN DEFAULT FALSE;

-- Create audit trail table for event changes
CREATE TABLE IF NOT EXISTS event_audit_trail (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  event_id UUID REFERENCES events(id) ON DELETE CASCADE,
  field_name TEXT NOT NULL,
  old_value JSONB,
  new_value JSONB,
  changed_by UUID REFERENCES auth.users(id),
  changed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  change_reason TEXT
);
```

## Error Handling

### Voice Processing Errors

1. **Low Confidence Scores**: Events with confidence < 0.7 are flagged for review
2. **Audio Processing Failures**: Fallback to text input with error notification
3. **Database Conflicts**: Optimistic locking with conflict resolution UI
4. **Network Issues**: Offline queue with sync when connection restored

### Error Recovery Strategies

```typescript
interface ErrorRecoveryStrategy {
  retryVoiceProcessing(audioBlob: Blob, maxRetries: number): Promise<VoiceEventResult>
  fallbackToTextInput(transcript: string): Promise<VoiceEventResult>
  queueOfflineEvent(eventData: VoiceEventData): Promise<void>
  syncOfflineEvents(): Promise<SyncResult[]>
}
```

### User Feedback System

- **Audio Confirmations**: "Successfully added 25 pounds of Dungeness Crab from Pacific Seafoods"
- **Visual Indicators**: Confidence score badges, processing status indicators
- **Error Messages**: Clear, actionable error descriptions with retry options

## Testing Strategy

### Unit Testing

1. **Voice Processing Logic**: Test event parsing accuracy with various voice inputs
2. **Database Operations**: Test CRUD operations and data integrity
3. **UI Components**: Test rendering, user interactions, and state management
4. **Error Handling**: Test error scenarios and recovery mechanisms

### Integration Testing

1. **Voice-to-Database Flow**: End-to-end testing of voice input to database storage
2. **Real-time Updates**: Test event display updates when new events are created
3. **Edit Workflow**: Test event editing and audit trail creation
4. **Cross-browser Compatibility**: Test voice processing across different browsers

### Performance Testing

1. **Voice Processing Latency**: Ensure sub-2-second processing times
2. **Database Query Performance**: Optimize event retrieval with proper indexing
3. **Real-time Update Performance**: Test with high-frequency event creation
4. **Audio Storage**: Test audio file upload and retrieval performance

### User Acceptance Testing

1. **Voice Recognition Accuracy**: Test with various accents and speaking styles
2. **Workflow Efficiency**: Measure time savings compared to manual entry
3. **Error Recovery**: Test user experience during error scenarios
4. **Mobile Compatibility**: Test voice processing on mobile devices

### Test Data Strategy

```typescript
interface VoiceTestCase {
  description: string
  audioInput: Blob | string // Audio file or transcript
  expectedOutput: Partial<VoiceEvent>
  confidenceThreshold: number
  testCategory: 'accuracy' | 'performance' | 'error_handling'
}
```

### Automated Testing Pipeline

1. **Voice Processing Regression Tests**: Automated tests with pre-recorded audio samples
2. **Database Migration Tests**: Ensure schema changes don't break existing functionality
3. **API Integration Tests**: Test voice processing API endpoints
4. **UI Component Tests**: Automated testing of React components

The testing strategy ensures high reliability and accuracy of voice event processing while maintaining good user experience and system performance.