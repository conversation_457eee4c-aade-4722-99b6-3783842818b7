import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const serviceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !serviceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, serviceKey);

async function createTable(tableName, createSQL) {
  console.log(`📄 Creating table: ${tableName}`);
  
  try {
    // First check if table exists
    const { data: tables, error: checkError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', tableName);
    
    if (!checkError && tables && tables.length > 0) {
      console.log(`   ⚠️  Table ${tableName} already exists, skipping...`);
      return;
    }
    
    // Use a basic SQL execution approach
    const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec_sql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': serviceKey,
        'Authorization': `Bearer ${serviceKey}`
      },
      body: JSON.stringify({ sql: createSQL })
    });
    
    if (!response.ok) {
      console.log(`   ❌ HTTP Error: ${response.status} ${response.statusText}`);
      const errorText = await response.text();
      console.log(`   Error details: ${errorText}`);
    } else {
      console.log(`   ✅ Table ${tableName} created successfully`);
    }
  } catch (err) {
    console.error(`   ❌ Error creating ${tableName}: ${err.message}`);
  }
}

async function main() {
  console.log('🌡️ Creating Essential TempStick Tables');
  console.log('======================================');
  
  // Create basic sensors table
  await createTable('sensors', `
    CREATE TABLE IF NOT EXISTS sensors (
      id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
      tempstick_sensor_id VARCHAR(255) NOT NULL UNIQUE,
      name VARCHAR(255) NOT NULL,
      location VARCHAR(255),
      sensor_type VARCHAR(50) DEFAULT 'temperature_humidity',
      temp_min_threshold DECIMAL(5,2),
      temp_max_threshold DECIMAL(5,2),
      active BOOLEAN DEFAULT true,
      created_at TIMESTAMPTZ DEFAULT NOW()
    );
  `);
  
  // Create basic temperature_readings table
  await createTable('temperature_readings', `
    CREATE TABLE IF NOT EXISTS temperature_readings (
      id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
      sensor_id UUID REFERENCES sensors(id) ON DELETE CASCADE,
      temperature DECIMAL(5,2) NOT NULL,
      humidity DECIMAL(5,2),
      reading_timestamp TIMESTAMPTZ NOT NULL,
      alert_triggered BOOLEAN DEFAULT false,
      created_at TIMESTAMPTZ DEFAULT NOW()
    );
  `);
  
  // Create basic temperature_alerts table
  await createTable('temperature_alerts', `
    CREATE TABLE IF NOT EXISTS temperature_alerts (
      id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
      sensor_id UUID REFERENCES sensors(id) ON DELETE CASCADE,
      alert_type VARCHAR(50) NOT NULL,
      temperature DECIMAL(5,2),
      humidity DECIMAL(5,2),
      alert_timestamp TIMESTAMPTZ NOT NULL,
      severity_level VARCHAR(20) DEFAULT 'medium',
      alert_status VARCHAR(20) DEFAULT 'active',
      created_at TIMESTAMPTZ DEFAULT NOW()
    );
  `);
  
  console.log('✅ Basic table creation complete!');
  console.log('🌡️ TempStick integration should now work with database');
}

main().catch(console.error);