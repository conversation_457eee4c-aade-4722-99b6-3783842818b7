// Simple test to verify TempStick integration is working
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testTempStickIntegration() {
  console.log('🌡️ Testing TempStick Integration');
  console.log('================================');

  try {
    // Test 1: Check if we can connect to Supabase
    console.log('\n1. Testing Supabase connection...');
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError) {
      console.log('   ⚠️ Auth check failed (expected for testing):', authError.message);
    } else if (user) {
      console.log('   ✅ User authenticated:', user.email);
    } else {
      console.log('   ✅ Supabase connection working (no user authenticated)');
    }

    // Test 2: Check if TempStick tables exist
    console.log('\n2. Testing TempStick tables...');
    
    const tables = ['sensors', 'storage_areas', 'temperature_readings', 'temperature_alerts'];
    const tableResults = {};
    
    for (const table of tables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('*')
          .limit(1);
        
        if (error) {
          if (error.message.includes('does not exist')) {
            tableResults[table] = 'missing';
          } else {
            tableResults[table] = 'error';
          }
        } else {
          tableResults[table] = 'exists';
        }
      } catch (err) {
        tableResults[table] = 'error';
      }
    }
    
    // Display results
    for (const [table, status] of Object.entries(tableResults)) {
      if (status === 'exists') {
        console.log(`   ✅ ${table} table exists`);
      } else if (status === 'missing') {
        console.log(`   ❌ ${table} table missing`);
      } else {
        console.log(`   ⚠️ ${table} table error`);
      }
    }
    
    const missingTables = Object.entries(tableResults).filter(([_, status]) => status === 'missing');
    
    if (missingTables.length > 0) {
      console.log('\n💡 Missing tables detected. The dashboard will use mock data.');
      console.log('   This is expected for development when database tables haven\'t been created yet.');
    } else {
      console.log('\n✅ All TempStick tables are available!');
    }

    // Test 3: Check environment variables
    console.log('\n3. Testing environment configuration...');
    const useMockData = process.env.VITE_USE_MOCK_TEMPSTICK_DATA;
    console.log(`   Mock data enabled: ${useMockData === 'true' ? '✅ Yes' : '❌ No'}`);
    
    if (missingTables.length > 0 && useMockData !== 'true') {
      console.log('   💡 Consider setting VITE_USE_MOCK_TEMPSTICK_DATA=true in .env');
    }

    console.log('\n🎉 TempStick integration test completed!');
    console.log('\n📋 Next steps:');
    console.log('   1. The dashboard should work with mock data if tables are missing');
    console.log('   2. Navigate to: http://localhost:5177/temperature');
    console.log('   3. Or test page: http://localhost:5177/test-tempstick');
    console.log('   4. Check browser console for any errors');
    
    if (missingTables.length > 0) {
      console.log('\n🔧 To create database tables:');
      console.log('   1. Use Supabase dashboard SQL editor');
      console.log('   2. Run the SQL from create-tempstick-views.sql');
      console.log('   3. Or use the migration scripts in the migrations/ folder');
    }
    
  } catch (error) {
    console.error('💥 Test failed:', error);
  }
}

testTempStickIntegration();