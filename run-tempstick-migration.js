#!/usr/bin/env node

/**
 * Direct TempStick Migration Runner
 * Manually executes the TempStick schema using Supabase client
 */

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function createTempStickTables() {
  console.log('🌡️  Creating TempStick tables...\n');
  
  // Create storage_areas table
  try {
    console.log('📦 Creating storage_areas table...');
    const { error: storageError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS storage_areas (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
          name VARCHAR(255) NOT NULL,
          description TEXT,
          area_code VARCHAR(50),
          area_type VARCHAR(50) NOT NULL CHECK (area_type IN (
            'walk_in_cooler', 'walk_in_freezer', 'reach_in_cooler', 'reach_in_freezer',
            'dry_storage', 'processing_area', 'shipping_dock', 'receiving_area',
            'blast_chiller', 'ice_storage', 'custom'
          )),
          temp_min_celsius DECIMAL(5,2),
          temp_max_celsius DECIMAL(5,2),
          temp_min_fahrenheit DECIMAL(5,2),
          temp_max_fahrenheit DECIMAL(5,2),
          temp_unit VARCHAR(10) DEFAULT 'fahrenheit' CHECK (temp_unit IN ('celsius', 'fahrenheit')),
          haccp_required BOOLEAN DEFAULT false,
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMPTZ DEFAULT NOW(),
          updated_at TIMESTAMPTZ DEFAULT NOW(),
          UNIQUE(user_id, area_code)
        );
      `
    });
    
    if (storageError) {
      console.log(`   ⚠️  ${storageError.message}`);
    } else {
      console.log('   ✅ storage_areas table created');
    }
  } catch (err) {
    console.log(`   ❌ Error: ${err.message}`);
  }
  
  // Create sensors table
  try {
    console.log('📡 Creating sensors table...');
    const { error: sensorsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS sensors (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
          storage_area_id UUID REFERENCES storage_areas(id) ON DELETE CASCADE,
          sensor_id VARCHAR(100) NOT NULL,
          device_name VARCHAR(255) NOT NULL,
          device_type VARCHAR(100) DEFAULT 'TempStick',
          name VARCHAR(255) NOT NULL,
          description TEXT,
          is_online BOOLEAN DEFAULT true,
          last_seen_at TIMESTAMPTZ,
          connection_status VARCHAR(50) DEFAULT 'online' CHECK (connection_status IN (
            'online', 'offline', 'maintenance', 'error'
          )),
          battery_level INTEGER,
          temp_offset_celsius DECIMAL(4,2) DEFAULT 0.0,
          temp_offset_fahrenheit DECIMAL(4,2) DEFAULT 0.0,
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMPTZ DEFAULT NOW(),
          updated_at TIMESTAMPTZ DEFAULT NOW(),
          UNIQUE(user_id, sensor_id)
        );
      `
    });
    
    if (sensorsError) {
      console.log(`   ⚠️  ${sensorsError.message}`);
    } else {
      console.log('   ✅ sensors table created');
    }
  } catch (err) {
    console.log(`   ❌ Error: ${err.message}`);
  }
  
  // Create temperature_readings table
  try {
    console.log('🌡️  Creating temperature_readings table...');
    const { error: readingsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS temperature_readings (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
          sensor_id UUID REFERENCES sensors(id) ON DELETE CASCADE NOT NULL,
          storage_area_id UUID REFERENCES storage_areas(id) ON DELETE CASCADE,
          recorded_at TIMESTAMPTZ NOT NULL,
          temp_celsius DECIMAL(6,2) NOT NULL,
          temp_fahrenheit DECIMAL(6,2) NOT NULL,
          humidity DECIMAL(5,2),
          within_safe_range BOOLEAN NOT NULL DEFAULT true,
          temp_violation BOOLEAN DEFAULT false,
          humidity_violation BOOLEAN DEFAULT false,
          reading_quality VARCHAR(20) DEFAULT 'good' CHECK (reading_quality IN (
            'good', 'acceptable', 'poor', 'error'
          )),
          signal_strength INTEGER,
          battery_level INTEGER,
          data_source VARCHAR(50) DEFAULT 'tempstick_api' CHECK (data_source IN (
            'tempstick_api', 'manual_entry', 'calibration', 'estimated'
          )),
          created_at TIMESTAMPTZ DEFAULT NOW()
        );
      `
    });
    
    if (readingsError) {
      console.log(`   ⚠️  ${readingsError.message}`);
    } else {
      console.log('   ✅ temperature_readings table created');
    }
  } catch (err) {
    console.log(`   ❌ Error: ${err.message}`);
  }
  
  // Create temperature_alerts table
  try {
    console.log('🚨 Creating temperature_alerts table...');
    const { error: alertsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS temperature_alerts (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
          sensor_id UUID REFERENCES sensors(id) ON DELETE CASCADE NOT NULL,
          storage_area_id UUID REFERENCES storage_areas(id) ON DELETE CASCADE,
          alert_type VARCHAR(50) NOT NULL CHECK (alert_type IN (
            'temp_high', 'temp_low', 'humidity_high', 'humidity_low',
            'sensor_offline', 'battery_low', 'calibration_due', 'maintenance_due',
            'data_gap', 'system_error'
          )),
          severity VARCHAR(20) NOT NULL CHECK (severity IN (
            'info', 'warning', 'critical', 'emergency'
          )),
          alert_status VARCHAR(20) DEFAULT 'active' CHECK (alert_status IN (
            'active', 'acknowledged', 'investigating', 'resolved', 'dismissed'
          )),
          title VARCHAR(255) NOT NULL,
          message TEXT NOT NULL,
          threshold_value DECIMAL(8,2),
          actual_value DECIMAL(8,2),
          first_detected_at TIMESTAMPTZ NOT NULL,
          last_detected_at TIMESTAMPTZ,
          acknowledged_at TIMESTAMPTZ,
          acknowledged_by UUID REFERENCES auth.users(id),
          resolved_at TIMESTAMPTZ,
          resolved_by UUID REFERENCES auth.users(id),
          haccp_violation BOOLEAN DEFAULT false,
          created_at TIMESTAMPTZ DEFAULT NOW(),
          updated_at TIMESTAMPTZ DEFAULT NOW()
        );
      `
    });
    
    if (alertsError) {
      console.log(`   ⚠️  ${alertsError.message}`);
    } else {
      console.log('   ✅ temperature_alerts table created');
    }
  } catch (err) {
    console.log(`   ❌ Error: ${err.message}`);
  }
  
  console.log('\n🏁 Table creation complete!');
}

async function createIndexes() {
  console.log('\n📊 Creating indexes for performance...\n');
  
  const indexes = [
    'CREATE INDEX IF NOT EXISTS idx_storage_areas_user_id ON storage_areas(user_id)',
    'CREATE INDEX IF NOT EXISTS idx_sensors_user_id ON sensors(user_id)',
    'CREATE INDEX IF NOT EXISTS idx_sensors_storage_area_id ON sensors(storage_area_id)',
    'CREATE INDEX IF NOT EXISTS idx_temperature_readings_user_id ON temperature_readings(user_id)',
    'CREATE INDEX IF NOT EXISTS idx_temperature_readings_sensor_id_time ON temperature_readings(sensor_id, recorded_at DESC)',
    'CREATE INDEX IF NOT EXISTS idx_temperature_readings_recorded_at ON temperature_readings(recorded_at DESC)',
    'CREATE INDEX IF NOT EXISTS idx_temperature_alerts_user_id ON temperature_alerts(user_id)',
    'CREATE INDEX IF NOT EXISTS idx_temperature_alerts_sensor_id ON temperature_alerts(sensor_id)',
    'CREATE INDEX IF NOT EXISTS idx_temperature_alerts_status ON temperature_alerts(alert_status) WHERE alert_status = \'active\''
  ];
  
  for (let i = 0; i < indexes.length; i++) {
    try {
      console.log(`   Creating index ${i + 1}/${indexes.length}...`);
      const { error } = await supabase.rpc('exec_sql', { sql: indexes[i] });
      
      if (error) {
        console.log(`   ⚠️  ${error.message}`);
      } else {
        console.log('   ✅ Index created');
      }
    } catch (err) {
      console.log(`   ❌ Error: ${err.message}`);
    }
  }
}

async function enableRLS() {
  console.log('\n🔒 Enabling Row Level Security...\n');
  
  const tables = ['storage_areas', 'sensors', 'temperature_readings', 'temperature_alerts'];
  
  for (const table of tables) {
    try {
      console.log(`   Enabling RLS on ${table}...`);
      const { error } = await supabase.rpc('exec_sql', { 
        sql: `ALTER TABLE ${table} ENABLE ROW LEVEL SECURITY;` 
      });
      
      if (error) {
        console.log(`   ⚠️  ${error.message}`);
      } else {
        console.log('   ✅ RLS enabled');
      }
    } catch (err) {
      console.log(`   ❌ Error: ${err.message}`);
    }
  }
}

async function createRLSPolicies() {
  console.log('\n🛡️  Creating RLS policies...\n');
  
  const policies = [
    {
      table: 'storage_areas',
      policies: [
        `CREATE POLICY "Users can view their own storage areas" ON storage_areas FOR SELECT USING (auth.uid() = user_id)`,
        `CREATE POLICY "Users can create their own storage areas" ON storage_areas FOR INSERT WITH CHECK (auth.uid() = user_id)`,
        `CREATE POLICY "Users can update their own storage areas" ON storage_areas FOR UPDATE USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id)`,
        `CREATE POLICY "Users can delete their own storage areas" ON storage_areas FOR DELETE USING (auth.uid() = user_id)`
      ]
    },
    {
      table: 'sensors',
      policies: [
        `CREATE POLICY "Users can view their own sensors" ON sensors FOR SELECT USING (auth.uid() = user_id)`,
        `CREATE POLICY "Users can create their own sensors" ON sensors FOR INSERT WITH CHECK (auth.uid() = user_id)`,
        `CREATE POLICY "Users can update their own sensors" ON sensors FOR UPDATE USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id)`,
        `CREATE POLICY "Users can delete their own sensors" ON sensors FOR DELETE USING (auth.uid() = user_id)`
      ]
    }
  ];
  
  for (const { table, policies: tablePolicies } of policies) {
    console.log(`   Creating policies for ${table}...`);
    
    for (const policy of tablePolicies) {
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: policy });
        
        if (error && !error.message.includes('already exists')) {
          console.log(`   ⚠️  ${error.message}`);
        } else {
          console.log('   ✅ Policy created');
        }
      } catch (err) {
        console.log(`   ❌ Error: ${err.message}`);
      }
    }
  }
}

async function testTables() {
  console.log('\n🧪 Testing table access...\n');
  
  const tables = ['storage_areas', 'sensors', 'temperature_readings', 'temperature_alerts'];
  
  for (const table of tables) {
    try {
      console.log(`   Testing ${table}...`);
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1);
      
      if (error) {
        console.log(`   ❌ ${error.message}`);
      } else {
        console.log(`   ✅ Accessible (${data?.length || 0} rows)`);
      }
    } catch (err) {
      console.log(`   ❌ Error: ${err.message}`);
    }
  }
}

async function main() {
  console.log('🌡️  TempStick Migration Runner');
  console.log('==============================\n');
  
  try {
    await createTempStickTables();
    await createIndexes();
    await enableRLS();
    await createRLSPolicies();
    await testTables();
    
    console.log('\n✅ Migration complete!');
    console.log('\n🎯 Next steps:');
    console.log('   1. Test the tables in your application');
    console.log('   2. Add sample data for testing');
    console.log('   3. Implement TempStick API integration');
    
  } catch (err) {
    console.error('❌ Migration failed:', err);
    process.exit(1);
  }
}

main();