// Simple script to create TempStick tables
import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

dotenv.config()

const supabaseUrl = process.env.VITE_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function createTempStickTables() {
  console.log('🌡️ Creating TempStick Database Tables')
  console.log('====================================')

  try {
    // Create storage_areas table
    console.log('📊 Creating storage_areas table...')
    const { error: storageError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS storage_areas (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
          name VARCHAR(255) NOT NULL,
          description TEXT,
          area_type VARCHAR(50) NOT NULL,
          haccp_required BOOLEAN DEFAULT false,
          temp_min_fahrenheit DECIMAL(5,2),
          temp_max_fahrenheit DECIMAL(5,2),
          temp_min_celsius DECIMAL(5,2),
          temp_max_celsius DECIMAL(5,2),
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMPTZ DEFAULT NOW(),
          updated_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        ALTER TABLE storage_areas ENABLE ROW LEVEL SECURITY;
        
        CREATE POLICY "Users can manage their own storage areas" 
          ON storage_areas FOR ALL 
          USING (auth.uid() = user_id) 
          WITH CHECK (auth.uid() = user_id);
      `
    })
    
    if (storageError) {
      console.log('   ❌ Error creating storage_areas:', storageError.message)
    } else {
      console.log('   ✅ storage_areas table created')
    }

    // Create sensors table
    console.log('🌡️ Creating sensors table...')
    const { error: sensorsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS sensors (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
          storage_area_id UUID REFERENCES storage_areas(id) ON DELETE CASCADE,
          sensor_id VARCHAR(100) NOT NULL,
          name VARCHAR(255) NOT NULL,
          is_online BOOLEAN DEFAULT true,
          battery_level INTEGER,
          temp_min_threshold DECIMAL(5,2),
          temp_max_threshold DECIMAL(5,2),
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMPTZ DEFAULT NOW(),
          updated_at TIMESTAMPTZ DEFAULT NOW(),
          UNIQUE(user_id, sensor_id)
        );
        
        ALTER TABLE sensors ENABLE ROW LEVEL SECURITY;
        
        CREATE POLICY "Users can manage their own sensors" 
          ON sensors FOR ALL 
          USING (auth.uid() = user_id) 
          WITH CHECK (auth.uid() = user_id);
      `
    })
    
    if (sensorsError) {
      console.log('   ❌ Error creating sensors:', sensorsError.message)
    } else {
      console.log('   ✅ sensors table created')
    }

    // Create temperature_readings table
    console.log('📊 Creating temperature_readings table...')
    const { error: readingsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS temperature_readings (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
          sensor_id UUID REFERENCES sensors(id) ON DELETE CASCADE NOT NULL,
          storage_area_id UUID REFERENCES storage_areas(id) ON DELETE CASCADE,
          recorded_at TIMESTAMPTZ NOT NULL,
          temp_celsius DECIMAL(6,2) NOT NULL,
          temp_fahrenheit DECIMAL(6,2) NOT NULL,
          humidity DECIMAL(5,2),
          within_safe_range BOOLEAN DEFAULT true,
          temp_violation BOOLEAN DEFAULT false,
          created_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        ALTER TABLE temperature_readings ENABLE ROW LEVEL SECURITY;
        
        CREATE POLICY "Users can manage their own temperature readings" 
          ON temperature_readings FOR ALL 
          USING (auth.uid() = user_id) 
          WITH CHECK (auth.uid() = user_id);
          
        CREATE INDEX IF NOT EXISTS idx_temperature_readings_sensor_time 
          ON temperature_readings(sensor_id, recorded_at DESC);
      `
    })
    
    if (readingsError) {
      console.log('   ❌ Error creating temperature_readings:', readingsError.message)
    } else {
      console.log('   ✅ temperature_readings table created')
    }

    // Create temperature_alerts table
    console.log('🚨 Creating temperature_alerts table...')
    const { error: alertsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS temperature_alerts (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
          sensor_id UUID REFERENCES sensors(id) ON DELETE CASCADE NOT NULL,
          storage_area_id UUID REFERENCES storage_areas(id) ON DELETE CASCADE,
          alert_type VARCHAR(50) NOT NULL,
          severity VARCHAR(20) NOT NULL,
          alert_status VARCHAR(20) DEFAULT 'active',
          title VARCHAR(255) NOT NULL,
          message TEXT NOT NULL,
          temperature DECIMAL(6,2),
          first_detected_at TIMESTAMPTZ NOT NULL,
          resolved_at TIMESTAMPTZ,
          created_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        ALTER TABLE temperature_alerts ENABLE ROW LEVEL SECURITY;
        
        CREATE POLICY "Users can manage their own temperature alerts" 
          ON temperature_alerts FOR ALL 
          USING (auth.uid() = user_id) 
          WITH CHECK (auth.uid() = user_id);
      `
    })
    
    if (alertsError) {
      console.log('   ❌ Error creating temperature_alerts:', alertsError.message)
    } else {
      console.log('   ✅ temperature_alerts table created')
    }

    console.log('\n✅ TempStick tables created successfully!')
    
  } catch (error) {
    console.error('💥 Error creating tables:', error)
  }
}

createTempStickTables().then(() => {
  console.log('🏁 Table creation complete')
}).catch(err => {
  console.error('💥 Error:', err)
})