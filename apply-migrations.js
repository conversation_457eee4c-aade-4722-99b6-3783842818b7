import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const serviceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !serviceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Required: VITE_SUPABASE_URL, VITE_SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, serviceKey);

async function applyMigration(filePath) {
  console.log(`📄 Applying migration: ${filePath}`);
  
  try {
    const sql = readFileSync(filePath, 'utf8');
    
    // Split SQL into individual statements (basic approach)
    const statements = sql
      .split(';')
      .map(s => s.trim())
      .filter(s => s.length > 0 && !s.startsWith('--'));
    
    for (const statement of statements) {
      if (statement.trim()) {
        console.log(`   Executing: ${statement.substring(0, 80)}...`);
        const { error } = await supabase.rpc('exec', { statement });
        
        if (error) {
          // Try direct query for DDL statements
          const { error: directError } = await supabase
            .from('_temp_migration')
            .select('*')
            .limit(0);
            
          console.log(`   ⚠️  RPC failed, trying direct execution...`);
          // This won't work for DDL but let's see the error
          console.log(`   Error: ${error.message}`);
        } else {
          console.log(`   ✅ Success`);
        }
      }
    }
  } catch (err) {
    console.error(`   ❌ Error reading file: ${err.message}`);
  }
}

async function main() {
  console.log('🌡️ Applying TempStick Database Migrations');
  console.log('==========================================');
  
  const migrationFiles = [
    './supabase/migrations/20250825_001_tempstick_sensor_integration_schema.sql',
    './supabase/migrations/20250825_002_tempstick_sample_data.sql',
    './supabase/migrations/20250825_003_inventory_temperature_integration.sql'
  ];
  
  for (const file of migrationFiles) {
    await applyMigration(file);
    console.log('');
  }
  
  console.log('✅ Migration application complete!');
}

main().catch(console.error);