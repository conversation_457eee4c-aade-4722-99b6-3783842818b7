
// Script to analyze the Supabase schema, create migrations and generate TypeScript definitions
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_KEY = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

// Main function
async function analyzeSchema() {
  console.log('Analyzing Supabase schema...');
  
  try {
    // Get all tables in public schema
    const { data: tables, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public');
    
    if (error) {
      throw new Error(`Failed to fetch tables: ${error.message}`);
    }
    
    const tableNames = tables.map(t => t.table_name);
    console.log(`Found ${tableNames.length} tables: ${tableNames.join(', ')}`);
    
    // Check for vendors/suppliers
    const hasVendors = tableNames.includes('vendors');
    const hasSuppliers = tableNames.includes('suppliers');
    
    console.log(`Vendors table exists: ${hasVendors}, Suppliers table exists: ${hasSuppliers}`);
    
    // Check for required tables
    const requiredTables = [
      'cogs',
      'fulfillments',
      'haccp_logs',
      'haccp_audits',
      'inventory_transactions',
      'platform_integrations',
      'sync_logs',
      'gdst_traceability'
    ];
    
    const missingTables = requiredTables.filter(table => !tableNames.includes(table));
    
    console.log(`Missing tables: ${missingTables.join(', ')}`);
    
    // For each table, get column information
    const tableSchema = {};
    
    for (const tableName of tableNames) {
      const { data: columns, error } = await supabase
        .from('information_schema.columns')
        .select('column_name, data_type, is_nullable, column_default')
        .eq('table_schema', 'public')
        .eq('table_name', tableName);
      
      if (error) {
        console.error(`Error fetching columns for ${tableName}:`, error);
        continue;
      }
      
      tableSchema[tableName] = columns;
      console.log(`Table ${tableName} has ${columns.length} columns`);
    }
    
    // Generate migration SQL
    console.log('Generating migration SQL...');
    let migrationSql = '';
    
    // 1. Rename vendors to suppliers if needed
    if (hasVendors && !hasSuppliers) {
      migrationSql += `
-- Rename vendors to suppliers
ALTER TABLE vendors RENAME TO suppliers;
      `;
      
      // Update references
      for (const tableName of tableNames) {
        for (const column of tableSchema[tableName] || []) {
          if (column.column_name === 'vendor_id') {
            migrationSql += `
-- Update reference in ${tableName}
ALTER TABLE ${tableName} RENAME COLUMN vendor_id TO supplier_id;
            `;
          }
        }
      }
    }
    
    // 2. Create missing tables
    for (const missingTable of missingTables) {
      migrationSql += `
-- Create ${missingTable} table
`;
      
      switch (missingTable) {
        case 'cogs':
          migrationSql += `
CREATE TABLE IF NOT EXISTS cogs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  batch_id UUID NOT NULL,
  raw_product_cost DECIMAL(10, 2) NOT NULL,
  shipping_cost DECIMAL(10, 2) DEFAULT 0,
  handling_cost DECIMAL(10, 2) DEFAULT 0,
  processing_cost DECIMAL(10, 2) DEFAULT 0,
  packaging_cost DECIMAL(10, 2) DEFAULT 0,
  labor_cost DECIMAL(10, 2) DEFAULT 0,
  other_costs DECIMAL(10, 2) DEFAULT 0,
  total_cost DECIMAL(10, 2) GENERATED ALWAYS AS (
    raw_product_cost + shipping_cost + handling_cost + 
    processing_cost + packaging_cost + labor_cost + other_costs
  ) STORED,
  cost_per_unit DECIMAL(10, 2),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
`;
          break;
        
        case 'fulfillments':
          migrationSql += `
CREATE TABLE IF NOT EXISTS fulfillments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_number VARCHAR(50) NOT NULL,
  square_transaction_id VARCHAR(100),
  dbp_order_id VARCHAR(100),
  batch_id UUID,
  product_id UUID NOT NULL,
  customer_id UUID,
  quantity DECIMAL(10, 2) NOT NULL,
  unit_price DECIMAL(10, 2) NOT NULL,
  total_price DECIMAL(10, 2) NOT NULL,
  fulfillment_date TIMESTAMP WITH TIME ZONE NOT NULL,
  fulfillment_status VARCHAR(50) NOT NULL CHECK (
    fulfillment_status IN ('pending', 'processing', 'shipped', 'delivered', 'cancelled')
  ),
  shipping_method VARCHAR(100),
  tracking_number VARCHAR(100),
  notes TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
`;
          break;
        
        case 'haccp_logs':
          migrationSql += `
CREATE TABLE IF NOT EXISTS haccp_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  batch_id UUID,
  log_type VARCHAR(50) NOT NULL CHECK (
    log_type IN ('receiving', 'processing', 'storage', 'shipping', 'temperature', 'sanitation', 'other')
  ),
  step_name VARCHAR(100) NOT NULL,
  critical_limit VARCHAR(255),
  monitoring_procedure TEXT,
  corrective_action TEXT,
  verification_procedure TEXT,
  record_keeping TEXT,
  temperature DECIMAL(5, 2),
  humidity DECIMAL(5, 2),
  ph_value DECIMAL(5, 2),
  compliance_status VARCHAR(50) NOT NULL CHECK (
    compliance_status IN ('compliant', 'non-compliant', 'pending', 'corrected')
  ),
  inspector_name VARCHAR(100),
  details TEXT,
  log_date TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
`;
          break;
          
        case 'haccp_audits':
          migrationSql += `
CREATE TABLE IF NOT EXISTS haccp_audits (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  audit_date TIMESTAMP WITH TIME ZONE NOT NULL,
  auditor_name VARCHAR(100) NOT NULL,
  audit_type VARCHAR(50) NOT NULL CHECK (
    audit_type IN ('internal', 'external', 'regulatory', 'certification')
  ),
  findings TEXT,
  non_conformities TEXT,
  corrective_actions TEXT,
  compliance_status VARCHAR(50) NOT NULL CHECK (
    compliance_status IN ('compliant', 'minor-non-compliant', 'major-non-compliant', 'critical-non-compliant')
  ),
  next_audit_date TIMESTAMP WITH TIME ZONE,
  attachments TEXT[],
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
`;
          break;
        
        case 'inventory_transactions':
          migrationSql += `
CREATE TABLE IF NOT EXISTS inventory_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID NOT NULL,
  batch_id UUID,
  transaction_type VARCHAR(50) NOT NULL CHECK (
    transaction_type IN ('receiving', 'sale', 'adjustment', 'transfer', 'disposal', 'return')
  ),
  quantity DECIMAL(10, 2) NOT NULL,
  unit_cost DECIMAL(10, 2),
  total_cost DECIMAL(10, 2),
  previous_quantity DECIMAL(10, 2) NOT NULL,
  new_quantity DECIMAL(10, 2) NOT NULL,
  location VARCHAR(100),
  reference_id UUID,
  reference_type VARCHAR(50),
  performed_by VARCHAR(100),
  notes TEXT,
  transaction_date TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
`;
          break;
        
        case 'platform_integrations':
          migrationSql += `
CREATE TABLE IF NOT EXISTS platform_integrations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  platform_name VARCHAR(50) NOT NULL CHECK (
    platform_name IN ('square', 'dbp', 'shopify', 'woocommerce', 'other')
  ),
  api_key TEXT,
  api_secret TEXT,
  access_token TEXT,
  refresh_token TEXT,
  token_expiry TIMESTAMP WITH TIME ZONE,
  webhook_url TEXT,
  webhook_secret TEXT,
  last_sync_time TIMESTAMP WITH TIME ZONE,
  sync_interval_minutes INTEGER DEFAULT 60,
  status VARCHAR(20) DEFAULT 'active',
  config JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
`;
          break;
        
        case 'sync_logs':
          migrationSql += `
CREATE TABLE IF NOT EXISTS sync_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  platform_id UUID,
  sync_start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  sync_end_time TIMESTAMP WITH TIME ZONE,
  sync_type VARCHAR(50) NOT NULL CHECK (
    sync_type IN ('orders', 'inventory', 'products', 'customers', 'fulfillments', 'full')
  ),
  sync_direction VARCHAR(20) NOT NULL CHECK (
    sync_direction IN ('import', 'export', 'bidirectional')
  ),
  items_processed INTEGER DEFAULT 0,
  items_created INTEGER DEFAULT 0,
  items_updated INTEGER DEFAULT 0,
  items_deleted INTEGER DEFAULT 0,
  items_failed INTEGER DEFAULT 0,
  status VARCHAR(20) NOT NULL CHECK (
    status IN ('pending', 'running', 'completed', 'failed', 'partially_completed')
  ),
  error_details JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
`;
          break;
          
        case 'gdst_traceability':
          migrationSql += `
CREATE TABLE IF NOT EXISTS gdst_traceability (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  batch_id UUID,
  product_id UUID,
  event_type VARCHAR(50) NOT NULL CHECK (
    event_type IN ('fishing', 'landing', 'transshipment', 'processing', 'aggregation', 'disaggregation', 'shipping', 'receiving')
  ),
  event_time TIMESTAMP WITH TIME ZONE NOT NULL,
  location_id VARCHAR(100),
  location_name VARCHAR(100),
  vessel_name VARCHAR(100),
  vessel_id VARCHAR(50),
  vessel_flag_state VARCHAR(3),
  catch_area VARCHAR(20),
  fishing_gear VARCHAR(20),
  production_method VARCHAR(2),
  first_freeze_date TIMESTAMP WITH TIME ZONE,
  business_step VARCHAR(50),
  disposition VARCHAR(50),
  gdst_data JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
`;
          break;
      }
      
      // Add indices for the table
      migrationSql += `
-- Create indices for ${missingTable}
`;
      
      switch (missingTable) {
        case 'cogs':
          migrationSql += `
CREATE INDEX idx_cogs_batch ON cogs(batch_id);
`;
          break;
        
        case 'fulfillments':
          migrationSql += `
CREATE INDEX idx_fulfillments_batch ON fulfillments(batch_id);
CREATE INDEX idx_fulfillments_product ON fulfillments(product_id);
CREATE INDEX idx_fulfillments_customer ON fulfillments(customer_id);
CREATE INDEX idx_fulfillments_order_number ON fulfillments(order_number);
`;
          break;
        
        case 'haccp_logs':
          migrationSql += `
CREATE INDEX idx_haccp_logs_batch ON haccp_logs(batch_id);
CREATE INDEX idx_haccp_logs_date ON haccp_logs(log_date);
`;
          break;
        
        case 'haccp_audits':
          migrationSql += `
CREATE INDEX idx_haccp_audits_date ON haccp_audits(audit_date);
`;
          break;
        
        case 'inventory_transactions':
          migrationSql += `
CREATE INDEX idx_inventory_transactions_product ON inventory_transactions(product_id);
CREATE INDEX idx_inventory_transactions_batch ON inventory_transactions(batch_id);
CREATE INDEX idx_inventory_transactions_date ON inventory_transactions(transaction_date);
`;
          break;
        
        case 'platform_integrations':
          migrationSql += `
CREATE INDEX idx_platform_integrations_platform ON platform_integrations(platform_name);
`;
          break;
        
        case 'sync_logs':
          migrationSql += `
CREATE INDEX idx_sync_logs_platform ON sync_logs(platform_id);
CREATE INDEX idx_sync_logs_time ON sync_logs(sync_start_time);
CREATE INDEX idx_sync_logs_status ON sync_logs(status);
`;
          break;
        
        case 'gdst_traceability':
          migrationSql += `
CREATE INDEX idx_gdst_batch ON gdst_traceability(batch_id);
CREATE INDEX idx_gdst_product ON gdst_traceability(product_id);
CREATE INDEX idx_gdst_event_type ON gdst_traceability(event_type);
CREATE INDEX idx_gdst_event_time ON gdst_traceability(event_time);
`;
          break;
      }
    }
    
    // Create triggers for new tables
    migrationSql += `
-- Create updated_at triggers for new tables
`;
    
    for (const missingTable of missingTables) {
      if (missingTable !== 'gdst_traceability' && missingTable !== 'sync_logs') {
        migrationSql += `
CREATE TRIGGER update_${missingTable.replace(/\_/g, '_')}_updated_at
  BEFORE UPDATE ON ${missingTable}
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();
`;
      }
    }
    
    // Write migration SQL to file
    const migrationDir = path.join(__dirname, 'migrations');
    if (!fs.existsSync(migrationDir)) {
      fs.mkdirSync(migrationDir, { recursive: true });
    }
    
    const migrationFilePath = path.join(migrationDir, `${Date.now()}_update_schema.sql`);
    fs.writeFileSync(migrationFilePath, migrationSql);
    
    console.log(`Migration SQL written to ${migrationFilePath}`);
    
    // Generate TypeScript interfaces
    console.log('Generating TypeScript interfaces...');
    
    let tsInterfaces = `// Generated TypeScript interfaces for Supabase schema
// Generated on ${new Date().toISOString()}

`;
    
    const typeMapping = {
      'uuid': 'string',
      'character varying': 'string',
      'varchar': 'string',
      'text': 'string',
      'integer': 'number',
      'smallint': 'number',
      'bigint': 'number',
      'decimal': 'number',
      'numeric': 'number',
      'real': 'number',
      'double precision': 'number',
      'boolean': 'boolean',
      'date': 'string', // or Date in runtime
      'timestamp with time zone': 'string', // or Date in runtime
      'timestamp without time zone': 'string', // or Date in runtime
      'jsonb': 'Record<string, unknown>',
      'json': 'Record<string, unknown>',
      'text[]': 'string[]'
    };
    
    // Add existing tables
    for (const tableName of tableNames) {
      // Convert snake_case to PascalCase
      const interfaceName = tableName
        .split('_')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join('');
      
      tsInterfaces += `export interface ${interfaceName} {\n`;
      
      for (const column of tableSchema[tableName] || []) {
        const { column_name, data_type, is_nullable } = column;
        const tsType = typeMapping[data_type] || 'any';
        const nullable = is_nullable === 'YES' ? '?' : '';
        
        tsInterfaces += `  ${column_name}${nullable}: ${tsType};\n`;
      }
      
      tsInterfaces += `}\n\n`;
    }
    
    // Add interfaces for missing tables
    for (const missingTable of missingTables) {
      // Will be created based on migration definitions
      const interfaceName = missingTable
        .split('_')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join('');
      
      tsInterfaces += `export interface ${interfaceName} {\n`;
      
      switch (missingTable) {
        case 'cogs':
          tsInterfaces += `  id?: string;\n`;
          tsInterfaces += `  batch_id: string;\n`;
          tsInterfaces += `  raw_product_cost: number;\n`;
          tsInterfaces += `  shipping_cost?: number;\n`;
          tsInterfaces += `  handling_cost?: number;\n`;
          tsInterfaces += `  processing_cost?: number;\n`;
          tsInterfaces += `  packaging_cost?: number;\n`;
          tsInterfaces += `  labor_cost?: number;\n`;
          tsInterfaces += `  other_costs?: number;\n`;
          tsInterfaces += `  total_cost?: number;\n`;
          tsInterfaces += `  cost_per_unit?: number;\n`;
          tsInterfaces += `  notes?: string;\n`;
          tsInterfaces += `  created_at?: string;\n`;
          tsInterfaces += `  updated_at?: string;\n`;
          break;
          
        case 'fulfillments':
          tsInterfaces += `  id?: string;\n`;
          tsInterfaces += `  order_number: string;\n`;
          tsInterfaces += `  square_transaction_id?: string;\n`;
          tsInterfaces += `  dbp_order_id?: string;\n`;
          tsInterfaces += `  batch_id?: string;\n`;
          tsInterfaces += `  product_id: string;\n`;
          tsInterfaces += `  customer_id?: string;\n`;
          tsInterfaces += `  quantity: number;\n`;
          tsInterfaces += `  unit_price: number;\n`;
          tsInterfaces += `  total_price: number;\n`;
          tsInterfaces += `  fulfillment_date: string;\n`;
          tsInterfaces += `  fulfillment_status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';\n`;
          tsInterfaces += `  shipping_method?: string;\n`;
          tsInterfaces += `  tracking_number?: string;\n`;
          tsInterfaces += `  notes?: string;\n`;
          tsInterfaces += `  metadata?: Record<string, unknown>;\n`;
          tsInterfaces += `  created_at?: string;\n`;
          tsInterfaces += `  updated_at?: string;\n`;
          break;
        
        // Add the rest of interfaces for other tables similarly
        // ...
      }
      
      tsInterfaces += `}\n\n`;
    }
    
    // Write TypeScript interfaces to file
    const typesDir = path.join(__dirname, 'src', 'types');
    if (!fs.existsSync(typesDir)) {
      fs.mkdirSync(typesDir, { recursive: true });
    }
    
    const typesFilePath = path.join(typesDir, 'schema.ts');
    fs.writeFileSync(typesFilePath, tsInterfaces);
    
    console.log(`TypeScript interfaces written to ${typesFilePath}`);
    
  } catch (error) {
    console.error('Error analyzing schema:', error);
  }
}

// Run the analysis
analyzeSchema();
