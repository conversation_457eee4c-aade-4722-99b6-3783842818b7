#!/usr/bin/env node
/**
 * Create or update a confirmed Supabase auth user for local testing.
 * Usage:
 *   node scripts/create-test-user.mjs --email=<EMAIL> --password=TempPass123!
 *
 * Requirements:
 *   - Environment variables available (from shell or .env):
 *       VITE_SUPABASE_URL
 *       VITE_SUPABASE_SERVICE_ROLE_KEY
 *   - Uses service role key (server-side only!). Do not expose in client.
 */

import { createClient } from '@supabase/supabase-js'
import fs from 'fs'
import path from 'path'
import crypto from 'crypto'

function loadEnvFromDotEnv() {
  try {
    const envPath = path.resolve(process.cwd(), '.env')
    if (!fs.existsSync(envPath)) return
    const text = fs.readFileSync(envPath, 'utf8')
    for (const raw of text.split('\n')) {
      const line = raw.trim()
      if (!line || line.startsWith('#')) continue
      const idx = line.indexOf('=')
      if (idx === -1) continue
      const key = line.slice(0, idx).trim()
      let val = line.slice(idx + 1).trim()
      if ((val.startsWith('"') && val.endsWith('"')) || (val.startsWith("'") && val.endsWith("'"))) {
        val = val.slice(1, -1)
      }
      if (!process.env[key]) process.env[key] = val
    }
  } catch (_) {
    // best-effort .env load
  }
}

loadEnvFromDotEnv()

const SUPABASE_URL = process.env.VITE_SUPABASE_URL
const SERVICE_ROLE = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY

if (!SUPABASE_URL || !SERVICE_ROLE) {
  console.error('[create-test-user] Missing VITE_SUPABASE_URL or VITE_SUPABASE_SERVICE_ROLE_KEY in environment or .env')
  process.exit(1)
}

const supabase = createClient(SUPABASE_URL, SERVICE_ROLE)

function parseArgs(argv) {
  const out = {}
  for (const arg of argv.slice(2)) {
    if (arg.startsWith('--')) {
      const eq = arg.indexOf('=')
      if (eq !== -1) {
        out[arg.slice(2, eq)] = arg.slice(eq + 1)
      } else {
        out[arg.slice(2)] = true
      }
    }
  }
  return out
}

const args = parseArgs(process.argv)
const email = args.email || '<EMAIL>'
const password = args.password || crypto.randomBytes(12).toString('base64').replace(/[^a-zA-Z0-9]/g, '').slice(0, 16)

async function ensureUser() {
  // Try to create the user confirmed
  const { data, error } = await supabase.auth.admin.createUser({
    email,
    password,
    email_confirm: true,
    app_metadata: { role: 'tester' },
    user_metadata: { name: 'Local Tester' },
  })

  if (!error) {
    return { user: data.user, created: true, password }
  }

  // If already exists, try to locate and update password
  const alreadyExists = /already|registered|exists/i.test(error.message || '')
  if (!alreadyExists) {
    throw new Error(`[create-test-user] Failed to create user: ${error.message || 'Unknown error'}`)
  }

  const list = await supabase.auth.admin.listUsers({ page: 1, perPage: 1000 })
  const existing = list.data?.users?.find(u => (u.email || '').toLowerCase() === email.toLowerCase())
  if (!existing) {
    throw new Error('[create-test-user] User reported as existing but could not be found via listUsers.')
  }

  const upd = await supabase.auth.admin.updateUserById(existing.id, { password })
  if (upd.error) {
    throw new Error(`[create-test-user] Failed to update existing user password: ${upd.error.message}`)
  }

  return { user: upd.data.user, created: false, password }
}

try {
  const result = await ensureUser()
  const summary = {
    email,
    password: result.password,
    id: result.user?.id || null,
    status: result.created ? 'created' : 'updated-password',
    url: SUPABASE_URL,
  }
  console.log(JSON.stringify(summary, null, 2))
} catch (e) {
  console.error(String(e?.message || e))
  process.exit(1)
}
