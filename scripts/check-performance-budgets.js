#!/usr/bin/env node

// Performance Budget Checker Script
// Validates build artifacts against performance budgets

import { readFileSync, existsSync } from 'fs';
import { resolve } from 'path';
import { gzipSync } from 'zlib';

const PERFORMANCE_BUDGETS = {
  // Aggressive bundle size budgets (bytes) - optimized for performance
  'main-bundle': 400000,        // 400KB max for main bundle (was 1MB)
  'vendor-chunks': 600000,      // 600KB max for vendor chunks (was 800KB)
  'css-bundle': 40000,          // 40KB max for CSS (was 50KB)
  'individual-chunk': 300000,   // 300KB max for any individual chunk (was 500KB)
  
  // Asset budgets - more strict
  'total-assets': 1500000,      // 1.5MB total for all assets (was 2MB)
  'image-assets': 300000,       // 300KB for images (was 500KB)
  
  // Performance metrics budgets
  'build-time': 45000,          // 45 seconds max build time (was 60s)
  'bundle-count': 15,           // Max 15 chunks (was 20)
  
  // New budgets for specific optimization
  'react-core-chunk': 150000,   // 150KB for React core
  'voice-processing': 250000,   // 250KB for voice features
  'data-heavy': 350000,         // 350KB for data processing (Papa/XLSX)
  'charts': 200000,             // 200KB for chart library
  'ui-extended': 100000,        // 100KB for extended UI components
};

class PerformanceBudgetChecker {
  constructor() {
    this.violations = [];
    this.distPath = resolve(process.cwd(), 'dist');
  }

  async checkBudgets() {
    console.log('🔍 Checking performance budgets...\n');

    if (!existsSync(this.distPath)) {
      throw new Error('Build artifacts not found. Run `npm run build` first.');
    }

    // Check bundle sizes
    await this.checkBundleSizes();
    
    // Check asset sizes
    await this.checkAssetSizes();
    
    // Check chunk count
    await this.checkChunkCount();
    
    // Generate report
    this.generateReport();
    
    // Exit with error code if violations found
    if (this.violations.length > 0) {
      process.exit(1);
    }
  }

  async checkBundleSizes() {
    const { globSync } = await import('glob');
    
    // Check main bundle
    const jsFiles = globSync('dist/assets/*.js');
    const cssFiles = globSync('dist/assets/*.css');
    
    let mainBundleSize = 0;
    let vendorBundleSize = 0;
    let cssBundleSize = 0;
    
    // Analyze JS bundles
    for (const file of jsFiles) {
      const content = readFileSync(file);
      const gzippedSize = gzipSync(content).length;
      const originalSize = content.length;
      
      console.log(`📦 ${file}: ${this.formatBytes(originalSize)} (${this.formatBytes(gzippedSize)} gzipped)`);
      
      // Classify bundle type
      if (file.includes('vendor') || file.includes('chunk')) {
        vendorBundleSize += gzippedSize;
      } else {
        mainBundleSize += gzippedSize;
      }
      
      // Check individual chunk size
      this.checkBudget('individual-chunk', gzippedSize, file);
    }
    
    // Analyze CSS bundles
    for (const file of cssFiles) {
      const content = readFileSync(file);
      const gzippedSize = gzipSync(content).length;
      const originalSize = content.length;
      
      console.log(`🎨 ${file}: ${this.formatBytes(originalSize)} (${this.formatBytes(gzippedSize)} gzipped)`);
      cssBundleSize += gzippedSize;
    }
    
    // Check budget compliance
    this.checkBudget('main-bundle', mainBundleSize, 'Main JS Bundle');
    this.checkBudget('vendor-chunks', vendorBundleSize, 'Vendor Chunks');
    this.checkBudget('css-bundle', cssBundleSize, 'CSS Bundle');
    
    console.log('\n📊 Bundle Size Summary:');
    console.log(`  Main Bundle: ${this.formatBytes(mainBundleSize)}`);
    console.log(`  Vendor Chunks: ${this.formatBytes(vendorBundleSize)}`);
    console.log(`  CSS Bundle: ${this.formatBytes(cssBundleSize)}`);
    console.log(`  Total: ${this.formatBytes(mainBundleSize + vendorBundleSize + cssBundleSize)}\n`);
  }

  async checkAssetSizes() {
    const { globSync } = await import('glob');
    
    const allAssets = globSync('dist/**/*', { nodir: true });
    let totalAssetSize = 0;
    let imageAssetSize = 0;
    
    for (const file of allAssets) {
      const content = readFileSync(file);
      const size = content.length;
      totalAssetSize += size;
      
      // Check if it's an image asset
      if (/\.(png|jpg|jpeg|gif|svg|webp|ico)$/i.test(file)) {
        imageAssetSize += size;
      }
    }
    
    this.checkBudget('total-assets', totalAssetSize, 'Total Assets');
    this.checkBudget('image-assets', imageAssetSize, 'Image Assets');
    
    console.log(`📁 Total Assets: ${this.formatBytes(totalAssetSize)}`);
    console.log(`🖼️  Image Assets: ${this.formatBytes(imageAssetSize)}\n`);
  }

  async checkChunkCount() {
    const { globSync } = await import('glob');
    
    const jsFiles = globSync('dist/assets/*.js');
    const chunkCount = jsFiles.length;
    
    this.checkBudget('bundle-count', chunkCount, 'Bundle Count');
    
    console.log(`📈 Total Chunks: ${chunkCount}\n`);
  }

  checkBudget(budgetKey, actualValue, description) {
    const budget = PERFORMANCE_BUDGETS[budgetKey];
    if (!budget) return;
    
    const isWithinBudget = actualValue <= budget;
    const percentage = ((actualValue / budget) * 100).toFixed(1);
    
    if (isWithinBudget) {
      console.log(`✅ ${description}: ${this.formatValue(actualValue, budgetKey)} (${percentage}% of budget)`);
    } else {
      const violation = {
        budget: budgetKey,
        description,
        actual: actualValue,
        budget: budget,
        overage: actualValue - budget,
        percentage: percentage
      };
      
      this.violations.push(violation);
      console.log(`❌ ${description}: ${this.formatValue(actualValue, budgetKey)} (${percentage}% of budget - OVER BUDGET)`);
    }
  }

  formatValue(value, budgetKey) {
    if (budgetKey === 'bundle-count' || budgetKey === 'build-time') {
      return value.toString();
    }
    return this.formatBytes(value);
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  generateReport() {
    console.log('\n🎯 Performance Budget Report\n');
    console.log('='.repeat(50));
    
    if (this.violations.length === 0) {
      console.log('✅ All performance budgets are within limits!');
      console.log('🚀 Ready for production deployment.\n');
      return;
    }
    
    console.log(`❌ Found ${this.violations.length} budget violation(s):\n`);
    
    for (const violation of this.violations) {
      console.log(`🚨 ${violation.description}`);
      console.log(`   Budget: ${this.formatValue(violation.budget, violation.budget)}`);
      console.log(`   Actual: ${this.formatValue(violation.actual, violation.budget)}`);
      console.log(`   Overage: ${this.formatValue(violation.overage, violation.budget)} (${(((violation.actual - violation.budget) / violation.budget) * 100).toFixed(1)}%)`);
      console.log('');
    }
    
    console.log('🔧 Suggestions to fix budget violations:');
    this.generateSuggestions();
  }

  generateSuggestions() {
    const hasMainBundleViolation = this.violations.some(v => v.budget === 'main-bundle');
    const hasVendorViolation = this.violations.some(v => v.budget === 'vendor-chunks');
    const hasChunkCountViolation = this.violations.some(v => v.budget === 'bundle-count');
    
    if (hasMainBundleViolation) {
      console.log('   • Implement code splitting for routes');
      console.log('   • Use dynamic imports for heavy components');
      console.log('   • Remove unused dependencies');
    }
    
    if (hasVendorViolation) {
      console.log('   • Split vendor chunks further by usage frequency');
      console.log('   • Use CDN for common libraries');
      console.log('   • Consider smaller alternatives for heavy dependencies');
    }
    
    if (hasChunkCountViolation) {
      console.log('   • Consolidate related chunks');
      console.log('   • Adjust chunk splitting strategy');
    }
    
    console.log('   • Enable tree shaking for all dependencies');
    console.log('   • Use webpack-bundle-analyzer to identify large modules');
    console.log('   • Implement lazy loading for non-critical features\n');
  }
}

// Run the budget checker
const checker = new PerformanceBudgetChecker();
checker.checkBudgets().catch(error => {
  console.error('❌ Error checking performance budgets:', error.message);
  process.exit(1);
});