# Scripts Directory

This directory contains utility scripts, automation tools, and maintenance scripts for the Pacific Cloud Seafoods Manager application.

## Overview

The scripts directory provides essential automation and utility scripts for development, testing, deployment, and maintenance tasks. These scripts help streamline workflows, ensure consistency, and automate repetitive tasks across the development lifecycle.

## Script Categories

### Database Scripts

#### Data Seeding Scripts
- `seed-categories.mjs` - Seeds product categories into the database
- `seed-products.mjs` - Populates the product catalog with initial data

**Usage**:
```bash
npm run seed:categories
npm run seed:products
```

**Features**:
- Validates data before insertion
- Handles duplicate entries gracefully
- Provides progress feedback
- Supports rollback on errors
- Logs all operations for audit

#### Database Maintenance Scripts
- `check_tables.js` - Validates database table structure and integrity
- `db-updates.js` - Applies incremental database updates
- `update_schema.js` - Updates database schema with new changes
- `query_db_structure.js` - Analyzes and reports database structure
- `read_tables.js` / `read_tables.ts` - Reads and validates table data
- `read_tables_live.ts` - Live database monitoring and validation

**Key Features**:
- Table structure validation
- Data integrity checks
- Performance analysis
- Index optimization recommendations
- Constraint validation
- Foreign key relationship verification

#### Migration Scripts
- `migrate-production.sh` - Production database migration script
- `apply-voice-event-schema.sql` - Applies voice event schema changes

**Safety Features**:
- Pre-migration backups
- Rollback procedures
- Validation checks
- Progress monitoring
- Error handling and recovery

### Testing Scripts

#### Vendor System Testing
- `test-vendor-system.sh` - Comprehensive vendor system testing suite

**Test Coverage**:
- Unit tests for vendor components
- API integration tests
- Performance benchmarks
- Accessibility compliance tests
- End-to-end workflow tests

**Usage**:
```bash
npm run test:vendor
./scripts/test-vendor-system.sh
```

**Features**:
- Parallel test execution
- Detailed reporting
- Performance metrics
- Coverage analysis
- Automated test data setup and cleanup

### Performance Scripts

#### Performance Monitoring
- `check-performance-budgets.js` - Validates application performance against defined budgets
- `setup-monitoring.js` - Configures monitoring and alerting systems

**Performance Budgets**:
- Bundle size limits
- Load time thresholds
- Memory usage limits
- API response time targets
- Database query performance

**Monitoring Setup**:
- Application performance monitoring (APM)
- Error tracking configuration
- Custom metrics collection
- Alert threshold configuration
- Dashboard setup

#### Analysis Scripts
- `analyze-schema.js` - Analyzes database schema for optimization opportunities

**Analysis Features**:
- Query performance analysis
- Index usage statistics
- Table size and growth analysis
- Relationship complexity assessment
- Optimization recommendations

### Development Utilities

#### Code Quality Scripts
Scripts for maintaining code quality and consistency:

**Features**:
- Code formatting validation
- Linting and style checking
- Type checking automation
- Import organization
- Dead code detection

#### Build and Deployment
Scripts for build optimization and deployment automation:

**Build Scripts**:
- Bundle analysis and optimization
- Asset compression and minification
- Environment-specific builds
- Cache busting and versioning

**Deployment Scripts**:
- Pre-deployment validation
- Environment configuration
- Health checks
- Rollback procedures

## Script Architecture

### Common Patterns

#### Error Handling
All scripts implement consistent error handling:

```javascript
#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables
config();

// Error handling wrapper
async function withErrorHandling(operation, description) {
  try {
    console.log(`Starting: ${description}`);
    const result = await operation();
    console.log(`✅ Completed: ${description}`);
    return result;
  } catch (error) {
    console.error(`❌ Failed: ${description}`);
    console.error('Error:', error.message);
    process.exit(1);
  }
}

// Main execution
async function main() {
  await withErrorHandling(async () => {
    // Script logic here
  }, 'Script operation');
}

main();
```

#### Configuration Management
Scripts use environment-based configuration:

```javascript
const config = {
  database: {
    url: process.env.SUPABASE_URL,
    key: process.env.SUPABASE_SERVICE_ROLE_KEY
  },
  environment: process.env.NODE_ENV || 'development',
  verbose: process.env.VERBOSE === 'true'
};

// Validate required configuration
function validateConfig() {
  const required = ['SUPABASE_URL', 'SUPABASE_SERVICE_ROLE_KEY'];
  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    console.error('Missing required environment variables:', missing);
    process.exit(1);
  }
}
```

#### Logging and Progress
Consistent logging across all scripts:

```javascript
class Logger {
  constructor(verbose = false) {
    this.verbose = verbose;
  }
  
  info(message, data = {}) {
    console.log(`ℹ️  ${message}`, this.verbose ? data : '');
  }
  
  success(message, data = {}) {
    console.log(`✅ ${message}`, this.verbose ? data : '');
  }
  
  error(message, error = null) {
    console.error(`❌ ${message}`);
    if (error && this.verbose) {
      console.error(error);
    }
  }
  
  progress(current, total, operation) {
    const percentage = Math.round((current / total) * 100);
    console.log(`📊 ${operation}: ${current}/${total} (${percentage}%)`);
  }
}
```

### Database Connection Management
Reusable database connection utilities:

```javascript
import { createClient } from '@supabase/supabase-js';

class DatabaseManager {
  constructor() {
    this.client = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );
  }
  
  async testConnection() {
    try {
      const { data, error } = await this.client
        .from('products')
        .select('count')
        .limit(1);
        
      if (error) throw error;
      return true;
    } catch (error) {
      throw new Error(`Database connection failed: ${error.message}`);
    }
  }
  
  async executeQuery(query, params = {}) {
    const { data, error } = await this.client.rpc(query, params);
    if (error) throw error;
    return data;
  }
}
```

## Security Considerations

### Environment Variables
Scripts handle sensitive data securely:

```javascript
// Never log sensitive environment variables
const sensitiveKeys = ['API_KEY', 'SECRET', 'PASSWORD', 'TOKEN'];

function logConfig(config) {
  const safeConfig = { ...config };
  
  Object.keys(safeConfig).forEach(key => {
    if (sensitiveKeys.some(sensitive => key.includes(sensitive))) {
      safeConfig[key] = '***REDACTED***';
    }
  });
  
  console.log('Configuration:', safeConfig);
}
```

### Input Validation
All scripts validate inputs and parameters:

```javascript
import { z } from 'zod';

const ScriptArgsSchema = z.object({
  environment: z.enum(['development', 'staging', 'production']),
  dryRun: z.boolean().default(false),
  verbose: z.boolean().default(false)
});

function validateArgs(args) {
  try {
    return ScriptArgsSchema.parse(args);
  } catch (error) {
    console.error('Invalid arguments:', error.errors);
    process.exit(1);
  }
}
```

### Safe Operations
Scripts implement safety checks for destructive operations:

```javascript
async function safeDelete(table, conditions, options = {}) {
  const { dryRun = false, confirm = false } = options;
  
  // Count records that would be affected
  const { count } = await supabase
    .from(table)
    .select('*', { count: 'exact', head: true })
    .match(conditions);
  
  console.log(`Would delete ${count} records from ${table}`);
  
  if (!confirm && !dryRun) {
    console.log('Use --confirm flag to proceed with deletion');
    return;
  }
  
  if (dryRun) {
    console.log('Dry run - no records deleted');
    return;
  }
  
  // Proceed with deletion
  const { error } = await supabase
    .from(table)
    .delete()
    .match(conditions);
    
  if (error) throw error;
  console.log(`Successfully deleted ${count} records`);
}
```

## Testing Scripts

### Script Testing Framework
Scripts include their own testing utilities:

```javascript
// test-script-utils.js
export class ScriptTester {
  constructor() {
    this.tests = [];
    this.results = [];
  }
  
  test(name, testFn) {
    this.tests.push({ name, testFn });
  }
  
  async run() {
    console.log(`Running ${this.tests.length} tests...`);
    
    for (const { name, testFn } of this.tests) {
      try {
        await testFn();
        this.results.push({ name, status: 'passed' });
        console.log(`✅ ${name}`);
      } catch (error) {
        this.results.push({ name, status: 'failed', error });
        console.log(`❌ ${name}: ${error.message}`);
      }
    }
    
    this.printSummary();
  }
  
  printSummary() {
    const passed = this.results.filter(r => r.status === 'passed').length;
    const failed = this.results.filter(r => r.status === 'failed').length;
    
    console.log(`\nTest Summary: ${passed} passed, ${failed} failed`);
  }
}
```

### Integration Testing
Scripts for testing system integration:

```javascript
// integration-test.js
import { ScriptTester } from './test-script-utils.js';

const tester = new ScriptTester();

tester.test('Database connection', async () => {
  const db = new DatabaseManager();
  await db.testConnection();
});

tester.test('API endpoints', async () => {
  const response = await fetch('/api/health');
  if (!response.ok) throw new Error('API health check failed');
});

tester.test('Voice processing', async () => {
  // Test voice processing pipeline
});

await tester.run();
```

## Deployment Scripts

### Production Deployment
Comprehensive production deployment automation:

```bash
#!/bin/bash
# deploy-production.sh

set -e  # Exit on any error

echo "🚀 Starting production deployment..."

# Pre-deployment checks
echo "📋 Running pre-deployment checks..."
npm run quality:check
npm run test:all
npm run performance:check

# Database migrations
echo "🗄️  Running database migrations..."
npm run db:migrate

# Build application
echo "🔨 Building application..."
npm run build:production

# Deploy to Vercel
echo "☁️  Deploying to Vercel..."
vercel --prod

# Post-deployment verification
echo "✅ Running post-deployment verification..."
./scripts/verify-deployment.sh

echo "🎉 Deployment completed successfully!"
```

### Environment Management
Scripts for managing different environments:

```javascript
// manage-environment.js
const environments = {
  development: {
    database: 'dev-database',
    apiUrl: 'http://localhost:3000',
    features: ['debug', 'hot-reload']
  },
  staging: {
    database: 'staging-database',
    apiUrl: 'https://staging.example.com',
    features: ['performance-monitoring']
  },
  production: {
    database: 'prod-database',
    apiUrl: 'https://app.example.com',
    features: ['analytics', 'error-tracking']
  }
};

function deployToEnvironment(env) {
  const config = environments[env];
  if (!config) {
    throw new Error(`Unknown environment: ${env}`);
  }
  
  // Set environment variables
  process.env.NODE_ENV = env;
  process.env.DATABASE_URL = config.database;
  process.env.API_URL = config.apiUrl;
  
  // Deploy with environment-specific configuration
  console.log(`Deploying to ${env} environment...`);
}
```

## Maintenance and Monitoring

### Health Check Scripts
Regular system health monitoring:

```javascript
// health-check.js
async function checkSystemHealth() {
  const checks = [
    { name: 'Database', check: checkDatabase },
    { name: 'API', check: checkAPI },
    { name: 'Storage', check: checkStorage },
    { name: 'Voice Processing', check: checkVoiceProcessing }
  ];
  
  const results = [];
  
  for (const { name, check } of checks) {
    try {
      const result = await check();
      results.push({ name, status: 'healthy', ...result });
    } catch (error) {
      results.push({ name, status: 'unhealthy', error: error.message });
    }
  }
  
  return results;
}
```

### Cleanup Scripts
Automated cleanup and maintenance:

```javascript
// cleanup.js
async function performCleanup() {
  const tasks = [
    { name: 'Clean expired audio files', task: cleanExpiredAudio },
    { name: 'Archive old events', task: archiveOldEvents },
    { name: 'Update statistics', task: updateStatistics },
    { name: 'Optimize database', task: optimizeDatabase }
  ];
  
  for (const { name, task } of tasks) {
    console.log(`Starting: ${name}`);
    await task();
    console.log(`Completed: ${name}`);
  }
}
```

## Usage Guidelines

### Running Scripts
Standard patterns for script execution:

```bash
# Development environment
NODE_ENV=development node scripts/script-name.js

# With verbose logging
VERBOSE=true node scripts/script-name.js

# Dry run mode
node scripts/script-name.js --dry-run

# Production environment (with confirmation)
NODE_ENV=production node scripts/script-name.js --confirm
```

### Script Development
Guidelines for creating new scripts:

1. **Use consistent error handling patterns**
2. **Implement dry-run mode for destructive operations**
3. **Add verbose logging options**
4. **Include progress indicators for long-running operations**
5. **Validate all inputs and environment variables**
6. **Provide clear usage instructions**
7. **Include rollback procedures where applicable**

### Best Practices
- Test scripts in development before production use
- Use version control for all scripts
- Document script purposes and usage
- Implement proper error handling and logging
- Use environment variables for configuration
- Include safety checks for destructive operations
- Provide rollback mechanisms where needed