// Node-only script to seed sample products using Supabase service role key
// Usage: node scripts/seed-products.mjs

import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

const url = (process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL || '').trim();
const serviceKey = (
  process.env.SUPABASE_SERVICE_ROLE_KEY ||
  process.env.VITE_SUPABASE_SERVICE_ROLE_KEY ||
  ''
).trim();

if (!url || !serviceKey) {
  console.error('[seed-products] Missing SUPABASE URL or SERVICE ROLE KEY');
  console.error('Ensure SUPABASE_SERVICE_ROLE_KEY (recommended) or VITE_SUPABASE_SERVICE_ROLE_KEY is set in .env');
  process.exit(1);
}

const supabase = createClient(url, serviceKey);

const SAMPLE_PRODUCTS = [
  { name: 'Atlantic Salmon' },
  { name: 'Dungeness Crab' },
  { name: '<PERSON><PERSON>' },
  { name: 'Pacific Halibut' },
  { name: '<PERSON>' },
  { name: 'Chinook Salmon' },
  { name: 'Yellowfin Tuna' },
  { name: 'Pacific Cod' },
  { name: 'Sockeye Salmon' },
  { name: 'Albacore Tuna' }
];

(async () => {
  try {
    console.log('[seed-products] Checking existing products...');
    const { data: existing, error: selectErr } = await supabase
      .from('Products')
      .select('id, name');

    if (selectErr) {
      console.error('[seed-products] Failed to read products:', selectErr);
      process.exit(1);
    }

    console.log(`[seed-products] Found ${existing?.length || 0} existing products`);

    const existingNames = new Set((existing || []).map((p) => p.name));
    const missing = SAMPLE_PRODUCTS.filter((p) => !existingNames.has(p.name));

    if (missing.length === 0) {
      console.log('[seed-products] All sample products already present. Nothing to do.');
      process.exit(0);
    }

    console.log(`[seed-products] Inserting ${missing.length} missing products:`, missing.map(p => p.name).join(', '));

    const { error: insertErr } = await supabase
      .from('Products')
      .insert(missing);

    if (insertErr) {
      console.error('[seed-products] Insert error:', insertErr);
      process.exit(1);
    }

    console.log(`[seed-products] Successfully inserted ${missing.length} products`);
    process.exit(0);
  } catch (e) {
    console.error('[seed-products] Unexpected error:', e);
    process.exit(1);
  }
})();