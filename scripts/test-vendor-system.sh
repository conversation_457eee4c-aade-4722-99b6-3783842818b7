#!/bin/bash

# Vendor Report Card System - Test Runner Script
# Comprehensive test execution for all vendor system components

set -e

echo "🧪 Starting Vendor Report Card System Test Suite"
echo "=================================================="

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test execution results
UNIT_TESTS_PASSED=false
INTEGRATION_TESTS_PASSED=false
API_TESTS_PASSED=false
PERFORMANCE_TESTS_PASSED=false
ACCESSIBILITY_TESTS_PASSED=false
E2E_TESTS_PASSED=false

# Function to run unit tests
run_unit_tests() {
    print_status "Running Unit Tests for Vendor Components..."
    
    if npm test -- --run --reporter=verbose --testPathPattern="vendor.*component.*test"; then
        print_success "Unit tests passed ✅"
        UNIT_TESTS_PASSED=true
    else
        print_error "Unit tests failed ❌"
        return 1
    fi
}

# Function to run API tests
run_api_tests() {
    print_status "Running API Layer Tests..."
    
    if npm test -- --run --reporter=verbose --testPathPattern="vendor-api.test"; then
        print_success "API tests passed ✅"
        API_TESTS_PASSED=true
    else
        print_error "API tests failed ❌"
        return 1
    fi
}

# Function to run integration tests
run_integration_tests() {
    print_status "Running Integration Tests..."
    
    if npm test -- --run --reporter=verbose --testPathPattern="vendor.*integration.*test"; then
        print_success "Integration tests passed ✅"
        INTEGRATION_TESTS_PASSED=true
    else
        print_error "Integration tests failed ❌"
        return 1
    fi
}

# Function to run performance tests
run_performance_tests() {
    print_status "Running Performance Tests..."
    
    if npm test -- --run --reporter=verbose --testPathPattern="vendor.*performance.*test"; then
        print_success "Performance tests passed ✅"
        PERFORMANCE_TESTS_PASSED=true
    else
        print_error "Performance tests failed ❌"
        return 1
    fi
}

# Function to run accessibility tests
run_accessibility_tests() {
    print_status "Running Accessibility Tests..."
    
    if npm test -- --run --reporter=verbose --testPathPattern="vendor.*accessibility.*test"; then
        print_success "Accessibility tests passed ✅"
        ACCESSIBILITY_TESTS_PASSED=true
    else
        print_error "Accessibility tests failed ❌"
        return 1
    fi
}

# Function to run E2E tests
run_e2e_tests() {
    print_status "Running End-to-End Tests..."
    
    if npx playwright test vendor-performance-workflow.spec.ts; then
        print_success "E2E tests passed ✅"
        E2E_TESTS_PASSED=true
    else
        print_error "E2E tests failed ❌"
        return 1
    fi
}

# Function to generate coverage report
generate_coverage() {
    print_status "Generating Test Coverage Report..."
    
    if npm test -- --run --coverage --testPathPattern="vendor.*test"; then
        print_success "Coverage report generated ✅"
        echo "📊 Coverage report available at: coverage/index.html"
    else
        print_warning "Coverage generation failed, but tests may have passed"
    fi
}

# Function to run all tests
run_all_tests() {
    local failed_tests=()
    
    echo ""
    echo "🚀 Running All Vendor System Tests..."
    echo "====================================="
    
    # Run each test suite and track failures
    run_unit_tests || failed_tests+=("Unit Tests")
    echo ""
    
    run_api_tests || failed_tests+=("API Tests")
    echo ""
    
    run_integration_tests || failed_tests+=("Integration Tests")
    echo ""
    
    run_performance_tests || failed_tests+=("Performance Tests")
    echo ""
    
    run_accessibility_tests || failed_tests+=("Accessibility Tests")
    echo ""
    
    # Only run E2E if other tests pass
    if [ ${#failed_tests[@]} -eq 0 ]; then
        run_e2e_tests || failed_tests+=("E2E Tests")
        echo ""
    else
        print_warning "Skipping E2E tests due to previous failures"
    fi
    
    # Generate coverage report regardless of test outcomes
    generate_coverage
    echo ""
    
    # Print summary
    print_test_summary "${failed_tests[@]}"
}

# Function to print test summary
print_test_summary() {
    local failed_tests=("$@")
    
    echo ""
    echo "📋 Test Summary"
    echo "==============="
    
    # Test results
    echo "Unit Tests:        $([ "$UNIT_TESTS_PASSED" = true ] && echo "✅ PASSED" || echo "❌ FAILED")"
    echo "API Tests:         $([ "$API_TESTS_PASSED" = true ] && echo "✅ PASSED" || echo "❌ FAILED")"
    echo "Integration Tests: $([ "$INTEGRATION_TESTS_PASSED" = true ] && echo "✅ PASSED" || echo "❌ FAILED")"
    echo "Performance Tests: $([ "$PERFORMANCE_TESTS_PASSED" = true ] && echo "✅ PASSED" || echo "❌ FAILED")"
    echo "Accessibility Tests: $([ "$ACCESSIBILITY_TESTS_PASSED" = true ] && echo "✅ PASSED" || echo "❌ FAILED")"
    echo "E2E Tests:         $([ "$E2E_TESTS_PASSED" = true ] && echo "✅ PASSED" || echo "❌ FAILED")"
    
    echo ""
    
    if [ ${#failed_tests[@]} -eq 0 ]; then
        print_success "🎉 All vendor system tests passed!"
        echo ""
        echo "✅ System is ready for deployment"
        echo "📊 Check coverage/index.html for detailed coverage report"
        echo "📋 See VENDOR_TESTING_STRATEGY.md for test documentation"
        return 0
    else
        print_error "❌ Some tests failed:"
        for test in "${failed_tests[@]}"; do
            echo "   - $test"
        done
        echo ""
        echo "🔧 Please fix failing tests before deployment"
        return 1
    fi
}

# Function to show usage
show_usage() {
    echo "Vendor Report Card System - Test Runner"
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  all            Run all test suites (default)"
    echo "  unit           Run unit tests only"
    echo "  api            Run API tests only"
    echo "  integration    Run integration tests only"
    echo "  performance    Run performance tests only"
    echo "  accessibility  Run accessibility tests only"
    echo "  e2e            Run E2E tests only"
    echo "  coverage       Generate coverage report"
    echo "  help           Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                    # Run all tests"
    echo "  $0 unit               # Run only unit tests"
    echo "  $0 coverage           # Generate coverage report"
}

# Main execution logic
main() {
    local command="${1:-all}"
    
    case "$command" in
        "all")
            run_all_tests
            ;;
        "unit")
            run_unit_tests
            ;;
        "api")
            run_api_tests
            ;;
        "integration")
            run_integration_tests
            ;;
        "performance")
            run_performance_tests
            ;;
        "accessibility")
            run_accessibility_tests
            ;;
        "e2e")
            run_e2e_tests
            ;;
        "coverage")
            generate_coverage
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            print_error "Unknown command: $command"
            echo ""
            show_usage
            exit 1
            ;;
    esac
}

# Trap to ensure cleanup on exit
cleanup() {
    print_status "Cleaning up test environment..."
    # Add any cleanup logic here if needed
}

trap cleanup EXIT

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if npm is available
    if ! command -v npm &> /dev/null; then
        print_error "npm is required but not installed"
        exit 1
    fi
    
    # Check if node_modules exists
    if [ ! -d "node_modules" ]; then
        print_warning "node_modules not found, running npm install..."
        npm install
    fi
    
    # Check if Playwright is installed for E2E tests
    if ! command -v npx playwright &> /dev/null; then
        print_warning "Playwright not found, installing..."
        npx playwright install
    fi
    
    print_success "Prerequisites check completed"
}

# Run prerequisites check
check_prerequisites

# Execute main function with all arguments
main "$@"