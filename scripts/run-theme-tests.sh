#!/bin/bash

# Dark Mode Testing Suite Runner
# Runs comprehensive tests for dark mode functionality

set -e

echo "🌙 Dark Mode Testing Suite"
echo "=========================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
  echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
  echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
  echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

# Check if dependencies are installed
print_status "Checking dependencies..."

if ! command -v npm &> /dev/null; then
  print_error "npm is required but not installed"
  exit 1
fi

if ! npm list @playwright/test &> /dev/null; then
  print_warning "Playwright not found, installing..."
  npm install
fi

# Install Playwright browsers if needed
print_status "Ensuring Playwright browsers are installed..."
npx playwright install

# Run unit tests first
print_status "Running ThemeContext unit tests..."
if npm run test -- src/__tests__/ThemeContext.test.tsx --reporter=verbose; then
  print_success "Unit tests passed!"
else
  print_error "Unit tests failed!"
  exit 1
fi

# Start the development server in background
print_status "Starting development server..."
npm run dev &
SERVER_PID=$!

# Function to cleanup server on exit
cleanup() {
  print_status "Cleaning up..."
  if kill -0 $SERVER_PID 2>/dev/null; then
    kill $SERVER_PID
    wait $SERVER_PID 2>/dev/null || true
  fi
}
trap cleanup EXIT

# Wait for server to start
print_status "Waiting for server to start..."
timeout=30
count=0
while [ $count -lt $timeout ]; do
  if curl -f http://localhost:5177 >/dev/null 2>&1; then
    break
  fi
  sleep 1
  count=$((count + 1))
done

if [ $count -eq $timeout ]; then
  print_error "Server failed to start within ${timeout} seconds"
  exit 1
fi

print_success "Server started successfully"

# Run E2E tests
print_status "Running dark mode E2E tests..."

# Run specific dark mode test suites
TEST_SUITES=(
  "e2e/dark-mode-functionality.spec.ts"
  "e2e/theme-visual-styling.spec.ts"
  "e2e/sensor-components-theme-aware.spec.ts"
)

FAILED_TESTS=()

for test_suite in "${TEST_SUITES[@]}"; do
  print_status "Running ${test_suite}..."
  
  if npx playwright test "$test_suite" --reporter=list; then
    print_success "✓ ${test_suite} passed"
  else
    print_error "✗ ${test_suite} failed"
    FAILED_TESTS+=("$test_suite")
  fi
done

# Summary
echo ""
echo "========================================="
echo "🌙 Dark Mode Test Results Summary"
echo "========================================="

if [ ${#FAILED_TESTS[@]} -eq 0 ]; then
  print_success "All tests passed! 🎉"
  
  # Generate test report
  print_status "Generating test report..."
  npx playwright show-report
  
  echo ""
  print_success "Dark mode implementation is fully functional!"
  echo ""
  echo "✅ ThemeContext provider properly initializes and provides theme state"
  echo "✅ Theme toggle button cycles through light -> dark -> system -> light"  
  echo "✅ System theme detection works correctly based on browser preferences"
  echo "✅ Theme persistence across browser sessions using localStorage"
  echo "✅ Theme-aware styling applies correctly in both light and dark modes"
  echo "✅ All sensor components render properly with theme-aware styles"
  echo "✅ CSS custom properties for charts update correctly with theme changes"
  
  exit 0
else
  print_error "Some tests failed:"
  for failed_test in "${FAILED_TESTS[@]}"; do
    echo "  - $failed_test"
  done
  exit 1
fi