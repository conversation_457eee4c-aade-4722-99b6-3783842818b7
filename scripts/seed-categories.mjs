// Node-only script to seed required categories using Supabase service role key
// Usage: node scripts/seed-categories.mjs

import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

const url = (process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL || '').trim();
const serviceKey = (
  process.env.SUPABASE_SERVICE_ROLE_KEY ||
  process.env.VITE_SUPABASE_SERVICE_ROLE_KEY ||
  ''
).trim();

if (!url || !serviceKey) {
  console.error('[seed-categories] Missing SUPABASE URL or SERVICE ROLE KEY');
  console.error('Ensure SUPABASE_SERVICE_ROLE_KEY (recommended) or VITE_SUPABASE_SERVICE_ROLE_KEY is set in .env');
  process.exit(1);
}

const supabase = createClient(url, serviceKey);

const REQUIRED = ['Receiving', 'Disposal', 'Physical Count', 'Re-processing'];

(async () => {
  try {
    console.log('[seed-categories] Checking existing categories...');
    const { data: existing, error: selectErr } = await supabase
      .from('categories')
      .select('id, name');

    if (selectErr) {
      console.error('[seed-categories] Failed to read categories:', selectErr);
      process.exit(1);
    }

    const existingNames = new Set((existing || []).map((c) => c.name));
    const missing = REQUIRED.filter((n) => !existingNames.has(n));

    if (missing.length === 0) {
      console.log('[seed-categories] All required categories already present. Nothing to do.');
      process.exit(0);
    }

    console.log('[seed-categories] Inserting missing categories:', missing.join(', '));

    const toInsert = missing.map((name) => ({ name }));
    const { error: insertErr } = await supabase
      .from('categories')
      .insert(toInsert);

    if (insertErr) {
      console.error('[seed-categories] Insert error:', insertErr);
      process.exit(1);
    }

    console.log('[seed-categories] Successfully inserted categories:', missing.join(', '));
    process.exit(0);
  } catch (e) {
    console.error('[seed-categories] Unexpected error:', e);
    process.exit(1);
  }
})();
