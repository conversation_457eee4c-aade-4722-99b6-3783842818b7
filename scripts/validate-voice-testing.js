#!/usr/bin/env node

/**
 * Voice Testing Validation Script
 * Validates that all voice testing components are properly configured and functional
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🎤 Voice Testing System Validation\n');

// Configuration
const REQUIRED_FILES = [
  'src/test/unit/voice-processing-components.test.ts',
  'src/test/integration/voice-database-comprehensive.test.ts',
  'src/test/performance/voice-processing-performance.test.ts',
  'src/test/e2e/voice-system-comprehensive.spec.ts',
  'src/test/mocks/voice-mocks.ts',
  'src/test/mocks/openai-mocks.ts',
  'src/test/mocks/supabase-mocks.ts',
  'src/test/voice-test-suite.config.ts',
  '.github/workflows/voice-testing-ci.yml'
];

const REQUIRED_SCRIPTS = [
  'test:voice:unit',
  'test:voice:integration',
  'test:voice:performance',
  'test:voice:all',
  'test:e2e:voice',
  'test:voice:complete',
  'test:ci:voice'
];

const VALIDATION_CHECKS = {
  files: 0,
  scripts: 0,
  dependencies: 0,
  configuration: 0,
  total: 4
};

let hasErrors = false;

// Helper functions
function checkExists(filePath) {
  return fs.existsSync(path.resolve(filePath));
}

function readJsonFile(filePath) {
  try {
    return JSON.parse(fs.readFileSync(path.resolve(filePath), 'utf8'));
  } catch (error) {
    console.error(`❌ Failed to read ${filePath}:`, error.message);
    return null;
  }
}

function logSuccess(message) {
  console.log(`✅ ${message}`);
}

function logError(message) {
  console.error(`❌ ${message}`);
  hasErrors = true;
}

function logWarning(message) {
  console.warn(`⚠️  ${message}`);
}

// Validation 1: Check required files
console.log('1. 📁 Checking required test files...');
REQUIRED_FILES.forEach(file => {
  if (checkExists(file)) {
    logSuccess(`Found: ${file}`);
  } else {
    logError(`Missing: ${file}`);
  }
});

if (!hasErrors) {
  VALIDATION_CHECKS.files = 1;
  logSuccess('All required test files are present\n');
} else {
  console.log();
}

// Validation 2: Check package.json scripts
console.log('2. 📜 Checking package.json test scripts...');
const packageJson = readJsonFile('package.json');
if (packageJson && packageJson.scripts) {
  REQUIRED_SCRIPTS.forEach(script => {
    if (packageJson.scripts[script]) {
      logSuccess(`Found script: ${script}`);
    } else {
      logError(`Missing script: ${script}`);
    }
  });
  
  if (!hasErrors) {
    VALIDATION_CHECKS.scripts = 1;
    logSuccess('All required test scripts are present\n');
  } else {
    console.log();
  }
} else {
  logError('Could not read package.json or scripts section missing\n');
}

// Validation 3: Check dependencies
console.log('3. 📦 Checking testing dependencies...');
const requiredDeps = [
  '@testing-library/react',
  '@testing-library/jest-dom',
  '@testing-library/user-event',
  'vitest',
  '@vitest/ui',
  '@vitest/coverage-v8',
  '@playwright/test',
  'msw',
  'happy-dom'
];

if (packageJson && packageJson.devDependencies) {
  requiredDeps.forEach(dep => {
    if (packageJson.devDependencies[dep] || (packageJson.dependencies && packageJson.dependencies[dep])) {
      logSuccess(`Found dependency: ${dep}`);
    } else {
      logError(`Missing dependency: ${dep}`);
    }
  });
  
  if (!hasErrors) {
    VALIDATION_CHECKS.dependencies = 1;
    logSuccess('All required testing dependencies are present\n');
  } else {
    console.log();
  }
} else {
  logError('Could not read package.json dependencies\n');
}

// Validation 4: Check configuration files
console.log('4. ⚙️  Checking configuration files...');
const configFiles = [
  { file: 'vitest.config.ts', description: 'Vitest configuration' },
  { file: 'playwright.config.ts', description: 'Playwright configuration' },
  { file: 'src/test/setup.ts', description: 'Test setup file' }
];

configFiles.forEach(({ file, description }) => {
  if (checkExists(file)) {
    logSuccess(`Found: ${description} (${file})`);
  } else {
    logError(`Missing: ${description} (${file})`);
  }
});

// Check vitest config specifically
if (checkExists('vitest.config.ts')) {
  try {
    const vitestConfig = fs.readFileSync('vitest.config.ts', 'utf8');
    if (vitestConfig.includes('coverage') && vitestConfig.includes('setupFiles')) {
      logSuccess('Vitest configuration includes coverage and setup files');
    } else {
      logWarning('Vitest configuration may be incomplete (missing coverage or setup)');
    }
  } catch (error) {
    logWarning('Could not validate vitest configuration content');
  }
}

if (!hasErrors) {
  VALIDATION_CHECKS.configuration = 1;
  logSuccess('All configuration files are present\n');
} else {
  console.log();
}

// Test execution validation (optional - only if all files are present)
console.log('5. 🧪 Testing system validation...');
if (VALIDATION_CHECKS.files && VALIDATION_CHECKS.scripts && VALIDATION_CHECKS.dependencies) {
  try {
    console.log('Running type check...');
    execSync('npm run type-check', { stdio: 'pipe' });
    logSuccess('TypeScript compilation successful');
    
    console.log('Testing voice mock imports...');
    // Skip dynamic import test for now as it requires TypeScript compilation
    logSuccess('Voice test configuration syntax check skipped (requires TS compilation)');
    
    console.log('Validating test file syntax...');
    // Basic syntax validation by attempting to parse the files
    const testFiles = [
      'src/test/unit/voice-processing-components.test.ts',
      'src/test/integration/voice-database-comprehensive.test.ts'
    ].filter(checkExists);
    
    testFiles.forEach(file => {
      const content = fs.readFileSync(file, 'utf8');
      if (content.includes('describe(') && content.includes('it(') && content.includes('expect(')) {
        logSuccess(`Test file structure valid: ${file}`);
      } else {
        logWarning(`Test file may have structural issues: ${file}`);
      }
    });
    
  } catch (error) {
    logWarning(`Test validation encountered issues: ${error.message}`);
  }
} else {
  logWarning('Skipping test execution validation due to missing requirements');
}

// Final summary
console.log('\n🎯 Validation Summary');
console.log('===================');

const passedChecks = Object.values(VALIDATION_CHECKS).reduce((sum, val) => sum + val, 0);
console.log(`Passed: ${passedChecks}/${VALIDATION_CHECKS.total} validation checks`);

if (VALIDATION_CHECKS.files) logSuccess('Required test files');
else logError('Required test files');

if (VALIDATION_CHECKS.scripts) logSuccess('Package.json scripts');
else logError('Package.json scripts');

if (VALIDATION_CHECKS.dependencies) logSuccess('Testing dependencies');
else logError('Testing dependencies');

if (VALIDATION_CHECKS.configuration) logSuccess('Configuration files');
else logError('Configuration files');

console.log('\n📋 Next Steps:');
if (passedChecks === VALIDATION_CHECKS.total) {
  console.log('🎉 Voice testing system is ready!');
  console.log('');
  console.log('You can now run:');
  console.log('• npm run test:voice:unit        - Unit tests for voice components');
  console.log('• npm run test:voice:integration - Integration tests for voice workflows');
  console.log('• npm run test:voice:performance - Performance tests for voice processing');
  console.log('• npm run test:voice:all         - All voice unit/integration/performance tests');
  console.log('• npm run test:e2e:voice         - End-to-end voice system tests');
  console.log('• npm run test:voice:complete    - Complete voice testing suite');
  console.log('');
  console.log('For CI/CD:');
  console.log('• npm run test:ci:voice          - Voice tests for CI pipeline');
  console.log('• The GitHub Actions workflow will run automatically on PR/push');
} else {
  console.log('⚠️  Voice testing system needs setup completion:');
  
  if (!VALIDATION_CHECKS.files) {
    console.log('  1. Ensure all test files are created');
  }
  if (!VALIDATION_CHECKS.scripts) {
    console.log('  2. Add missing scripts to package.json');
  }
  if (!VALIDATION_CHECKS.dependencies) {
    console.log('  3. Install missing testing dependencies');
  }
  if (!VALIDATION_CHECKS.configuration) {
    console.log('  4. Create missing configuration files');
  }
  
  console.log('');
  console.log('Run this script again after addressing the issues.');
}

console.log('\n📖 Documentation:');
console.log('• See src/test/VOICE_TESTING_README.md for comprehensive testing guide');
console.log('• Check .github/workflows/voice-testing-ci.yml for CI/CD setup');
console.log('• Review src/test/voice-test-suite.config.ts for configuration options');

// Exit with appropriate code
process.exit(hasErrors ? 1 : 0);