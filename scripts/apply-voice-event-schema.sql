-- ================================================================
-- VOICE EVENT MANAGEMENT SYSTEM - DATABASE SCHEMA EXTENSIONS
-- ================================================================
-- This script can be run independently to add voice event management
-- capabilities to an existing inventory_events table
-- ================================================================

BEGIN;

-- ================================================================
-- EXTEND INVENTORY_EVENTS TABLE WITH VOICE-SPECIFIC COLUMNS
-- ================================================================

-- Add voice processing confidence score (0.0 to 1.0)
ALTER TABLE inventory_events 
ADD COLUMN IF NOT EXISTS voice_confidence_score DECIMAL(3,2) 
CHECK (voice_confidence_score >= 0.0 AND voice_confidence_score <= 1.0);

-- Add detailed confidence breakdown as JSONB
ALTER TABLE inventory_events 
ADD COLUMN IF NOT EXISTS voice_confidence_breakdown JSONB;

-- Add raw transcript from voice processing
ALTER TABLE inventory_events 
ADD COLUMN IF NOT EXISTS raw_transcript TEXT;

-- Add URL to stored audio recording
ALTER TABLE inventory_events 
ADD COLUMN IF NOT EXISTS audio_recording_url TEXT;

-- Add flag to indicate if event was created via voice
ALTER TABLE inventory_events 
ADD COLUMN IF NOT EXISTS created_by_voice BOOLEAN DEFAULT FALSE;

-- Add vendor_id for voice events (if not already exists)
ALTER TABLE inventory_events 
ADD COLUMN IF NOT EXISTS vendor_id UUID;

-- Add batch_number for voice events (if not already exists)
ALTER TABLE inventory_events 
ADD COLUMN IF NOT EXISTS batch_number TEXT;

-- Add condition_on_receipt for voice events (if not already exists)
ALTER TABLE inventory_events 
ADD COLUMN IF NOT EXISTS condition_on_receipt TEXT;

-- Add temperature_at_receipt for voice events (if not already exists)
ALTER TABLE inventory_events 
ADD COLUMN IF NOT EXISTS temperature_at_receipt NUMERIC;

-- Add occurred_at for voice events (if not already exists)
ALTER TABLE inventory_events 
ADD COLUMN IF NOT EXISTS occurred_at TIMESTAMPTZ;

-- ================================================================
-- CREATE EVENT AUDIT TRAIL TABLE
-- ================================================================

CREATE TABLE IF NOT EXISTS event_audit_trail (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_id UUID NOT NULL REFERENCES inventory_events(id) ON DELETE CASCADE,
    field_name TEXT NOT NULL,
    old_value JSONB,
    new_value JSONB,
    changed_by UUID,
    changed_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    change_reason TEXT,
    
    -- Metadata for tracking change context
    change_source TEXT DEFAULT 'manual', -- 'manual', 'voice', 'api', 'system'
    session_id TEXT, -- For tracking related changes in same session
    
    CONSTRAINT valid_change_source CHECK (change_source IN ('manual', 'voice', 'api', 'system'))
);

-- ================================================================
-- CREATE INDEXES FOR EFFICIENT QUERYING
-- ================================================================

-- Index for voice events filtering
CREATE INDEX IF NOT EXISTS idx_inventory_events_voice_created 
ON inventory_events(created_by_voice) 
WHERE created_by_voice = TRUE;

-- Index for confidence score filtering (for quality review)
CREATE INDEX IF NOT EXISTS idx_inventory_events_voice_confidence 
ON inventory_events(voice_confidence_score) 
WHERE voice_confidence_score IS NOT NULL;

-- Composite index for voice events with low confidence (quality review queue)
CREATE INDEX IF NOT EXISTS idx_inventory_events_voice_low_confidence 
ON inventory_events(created_by_voice, voice_confidence_score) 
WHERE created_by_voice = TRUE AND voice_confidence_score < 0.7;

-- Index for audio recordings (for cleanup and retrieval)
CREATE INDEX IF NOT EXISTS idx_inventory_events_audio_url 
ON inventory_events(audio_recording_url) 
WHERE audio_recording_url IS NOT NULL;

-- Indexes for audit trail table
CREATE INDEX IF NOT EXISTS idx_event_audit_trail_event_id 
ON event_audit_trail(event_id);

CREATE INDEX IF NOT EXISTS idx_event_audit_trail_changed_by 
ON event_audit_trail(changed_by);

CREATE INDEX IF NOT EXISTS idx_event_audit_trail_changed_at 
ON event_audit_trail(changed_at);

-- ================================================================
-- ADD VALIDATION CONSTRAINTS
-- ================================================================

-- Add constraint to ensure voice confidence breakdown has required fields when confidence score is present
ALTER TABLE inventory_events 
ADD CONSTRAINT IF NOT EXISTS voice_confidence_breakdown_structure 
CHECK (
    (voice_confidence_score IS NULL AND voice_confidence_breakdown IS NULL) OR
    (voice_confidence_score IS NOT NULL AND voice_confidence_breakdown IS NOT NULL AND
     voice_confidence_breakdown ? 'overall' AND
     voice_confidence_breakdown ? 'product_match' AND
     voice_confidence_breakdown ? 'quantity_extraction')
);

-- Add constraint to ensure created_by_voice is true when voice fields are present
ALTER TABLE inventory_events 
ADD CONSTRAINT IF NOT EXISTS voice_fields_consistency 
CHECK (
    (created_by_voice = FALSE AND voice_confidence_score IS NULL AND raw_transcript IS NULL) OR
    (created_by_voice = TRUE)
);

-- ================================================================
-- ADD COMMENTS FOR DOCUMENTATION
-- ================================================================

COMMENT ON COLUMN inventory_events.voice_confidence_score IS 'Confidence score (0.0-1.0) from voice processing system';
COMMENT ON COLUMN inventory_events.voice_confidence_breakdown IS 'Detailed confidence breakdown by component (product_match, quantity_extraction, etc.)';
COMMENT ON COLUMN inventory_events.raw_transcript IS 'Original transcript from voice-to-text processing';
COMMENT ON COLUMN inventory_events.audio_recording_url IS 'URL to stored audio recording for review and verification';
COMMENT ON COLUMN inventory_events.created_by_voice IS 'Flag indicating if this event was created through voice processing';

COMMENT ON TABLE event_audit_trail IS 'Audit trail for tracking changes to inventory events, especially voice-created ones';

COMMIT;

-- ================================================================
-- VERIFICATION QUERIES
-- ================================================================

-- Verify the schema extensions were applied
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'inventory_events' 
    AND column_name IN (
        'voice_confidence_score', 
        'voice_confidence_breakdown', 
        'raw_transcript', 
        'audio_recording_url', 
        'created_by_voice'
    )
ORDER BY column_name;

-- Verify audit trail table was created
SELECT table_name, column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'event_audit_trail'
ORDER BY ordinal_position;

-- Verify indexes were created
SELECT indexname, indexdef 
FROM pg_indexes 
WHERE tablename IN ('inventory_events', 'event_audit_trail')
    AND indexname LIKE '%voice%'
ORDER BY indexname;