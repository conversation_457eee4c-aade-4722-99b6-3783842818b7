#!/usr/bin/env node

/**
 * Advanced Bundle Performance Analyzer
 * Provides detailed analysis of bundle composition, optimization opportunities,
 * and performance recommendations for the Seafood Manager application
 */

import { readFileSync, existsSync, statSync, readdirSync } from 'fs';
import { resolve, basename } from 'path';
import { gzipSync } from 'zlib';

class BundlePerformanceAnalyzer {
  constructor() {
    this.distPath = resolve(process.cwd(), 'dist');
    this.analysis = {
      bundles: [],
      totalSize: 0,
      gzippedSize: 0,
      recommendations: [],
      optimizations: [],
      performance: {}
    };
  }

  async analyze() {
    console.log('🔍 Analyzing bundle performance...\n');

    if (!existsSync(this.distPath)) {
      throw new Error('Build artifacts not found. Run `npm run build:production` first.');
    }

    // Analyze bundle composition
    await this.analyzeBundleComposition();
    
    // Analyze optimization opportunities
    await this.analyzeOptimizationOpportunities();
    
    // Generate performance recommendations
    this.generateRecommendations();
    
    // Generate detailed report
    this.generateReport();
    
    return this.analysis;
  }

  async analyzeBundleComposition() {
    const { globSync } = await import('glob');
    
    const jsFiles = globSync('dist/assets/*.js');
    const cssFiles = globSync('dist/assets/*.css');
    
    console.log('📦 Bundle Composition Analysis\n');
    
    // Analyze JavaScript bundles
    for (const file of jsFiles) {
      const analysis = await this.analyzeBundle(file, 'js');
      this.analysis.bundles.push(analysis);
      this.analysis.totalSize += analysis.originalSize;
      this.analysis.gzippedSize += analysis.gzippedSize;
    }
    
    // Analyze CSS bundles
    for (const file of cssFiles) {
      const analysis = await this.analyzeBundle(file, 'css');
      this.analysis.bundles.push(analysis);
      this.analysis.totalSize += analysis.originalSize;
      this.analysis.gzippedSize += analysis.gzippedSize;
    }
    
    // Sort bundles by size (largest first)
    this.analysis.bundles.sort((a, b) => b.gzippedSize - a.gzippedSize);
  }

  async analyzeBundle(filePath, type) {
    const content = readFileSync(filePath);
    const originalSize = content.length;
    const gzippedSize = gzipSync(content).length;
    const compressionRatio = ((originalSize - gzippedSize) / originalSize * 100).toFixed(1);
    
    const fileName = basename(filePath);
    const bundleType = this.identifyBundleType(fileName);
    
    const analysis = {
      fileName,
      filePath,
      type,
      bundleType,
      originalSize,
      gzippedSize,
      compressionRatio: parseFloat(compressionRatio),
      isOverBudget: false,
      budgetUsage: 0,
      recommendations: []
    };

    // Check against budgets
    this.checkBundleBudgets(analysis);
    
    // Analyze bundle content
    if (type === 'js') {
      this.analyzeJavaScriptBundle(analysis, content.toString());
    }

    console.log(`${this.getBundleEmoji(bundleType)} ${fileName}`);
    console.log(`   Size: ${this.formatBytes(originalSize)} → ${this.formatBytes(gzippedSize)} (${compressionRatio}% compression)`);
    console.log(`   Type: ${bundleType} ${analysis.isOverBudget ? '❌ OVER BUDGET' : '✅'}`);
    console.log();

    return analysis;
  }

  identifyBundleType(fileName) {
    const typeMap = {
      'react-core': 'React Core',
      'voice-processing': 'Voice Processing',
      'data-heavy': 'Data Processing', 
      'charts': 'Charts & Analytics',
      'supabase-core': 'Supabase Core',
      'ui-core': 'UI Components',
      'forms': 'Form Handling',
      'compliance': 'HACCP Compliance',
      'import-system': 'Import System',
      'vendor-management': 'Vendor Management',
      'analytics': 'Analytics',
      'main': 'Main Application',
      'index': 'Main Application'
    };

    for (const [key, name] of Object.entries(typeMap)) {
      if (fileName.includes(key)) {
        return name;
      }
    }

    if (fileName.includes('vendor')) {
      return 'Third-party Libraries';
    }

    if (fileName.endsWith('.css')) {
      return 'Styles';
    }

    return 'Application Code';
  }

  getBundleEmoji(bundleType) {
    const emojiMap = {
      'React Core': '⚛️',
      'Voice Processing': '🎤',
      'Data Processing': '📊',
      'Charts & Analytics': '📈',
      'Supabase Core': '🗄️',
      'UI Components': '🎨',
      'Form Handling': '📝',
      'HACCP Compliance': '🧪',
      'Import System': '📥',
      'Vendor Management': '🏭',
      'Analytics': '📊',
      'Main Application': '🏠',
      'Third-party Libraries': '📚',
      'Styles': '💄'
    };

    return emojiMap[bundleType] || '📦';
  }

  checkBundleBudgets(analysis) {
    const budgets = {
      'React Core': 150000,
      'Voice Processing': 250000,
      'Data Processing': 350000,
      'Charts & Analytics': 200000,
      'Main Application': 400000,
      'Third-party Libraries': 600000,
      'Styles': 40000
    };

    const budget = budgets[analysis.bundleType];
    if (budget) {
      analysis.budgetUsage = (analysis.gzippedSize / budget * 100).toFixed(1);
      analysis.isOverBudget = analysis.gzippedSize > budget;
      
      if (analysis.isOverBudget) {
        analysis.recommendations.push({
          type: 'budget',
          severity: 'high',
          message: `Bundle is ${this.formatBytes(analysis.gzippedSize - budget)} over budget`,
          suggestion: this.getBudgetSuggestion(analysis.bundleType)
        });
      }
    }
  }

  getBudgetSuggestion(bundleType) {
    const suggestions = {
      'Voice Processing': 'Consider lazy loading advanced voice features and splitting OpenAI integration',
      'Data Processing': 'Implement streaming for CSV processing and lazy load XLSX functionality',
      'Charts & Analytics': 'Use dynamic imports for chart components and consider lighter chart library',
      'Main Application': 'Implement route-based code splitting and lazy load non-critical features',
      'Third-party Libraries': 'Audit dependencies for unused exports and consider CDN loading'
    };

    return suggestions[bundleType] || 'Consider code splitting and lazy loading for this bundle type';
  }

  analyzeJavaScriptBundle(analysis, content) {
    // Detect potential optimization opportunities in JS content
    const checks = [
      {
        pattern: /console\.(log|warn|error|debug)/g,
        type: 'console-logs',
        message: 'Contains console.log statements that should be removed in production'
      },
      {
        pattern: /sourceMappingURL=/g,
        type: 'source-maps',
        message: 'Contains source map references (should be disabled in production)'
      },
      {
        pattern: /\/\*[\s\S]*?\*\/|\/\/.*$/gm,
        type: 'comments',
        message: 'Contains comments that could be minified further'
      },
      {
        pattern: /import\s*\{\s*[\s\S]*?\}\s*from\s*['"][^'"]*['"]/g,
        type: 'imports',
        message: 'May have unused imports that could be tree-shaken'
      }
    ];

    for (const check of checks) {
      const matches = content.match(check.pattern);
      if (matches && matches.length > 0) {
        analysis.recommendations.push({
          type: check.type,
          severity: 'medium',
          message: check.message,
          count: matches.length
        });
      }
    }

    // Check for heavy dependencies
    const heavyDeps = [
      { name: 'moment', alternative: 'date-fns (already used)', savings: '~200KB' },
      { name: 'lodash', alternative: 'Native ES6 methods or lodash-es', savings: '~70KB' },
      { name: 'axios', alternative: 'fetch API or lightweight HTTP client', savings: '~50KB' },
      { name: 'jquery', alternative: 'Native DOM methods', savings: '~90KB' }
    ];

    for (const dep of heavyDeps) {
      if (content.includes(dep.name)) {
        analysis.recommendations.push({
          type: 'heavy-dependency',
          severity: 'medium',
          message: `Contains ${dep.name} - consider ${dep.alternative}`,
          potentialSavings: dep.savings
        });
      }
    }
  }

  analyzeOptimizationOpportunities() {
    console.log('🎯 Optimization Opportunities\n');

    // Analyze chunk distribution
    this.analyzeChunkDistribution();
    
    // Check for duplicate code
    this.checkForDuplicateCode();
    
    // Analyze compression efficiency
    this.analyzeCompressionEfficiency();
  }

  analyzeChunkDistribution() {
    const jsBundles = this.analysis.bundles.filter(b => b.type === 'js');
    const totalJSSize = jsBundles.reduce((sum, b) => sum + b.gzippedSize, 0);
    
    console.log('📊 Chunk Distribution Analysis:');
    
    for (const bundle of jsBundles) {
      const percentage = (bundle.gzippedSize / totalJSSize * 100).toFixed(1);
      console.log(`   ${bundle.bundleType}: ${percentage}% (${this.formatBytes(bundle.gzippedSize)})`);
      
      // Flag bundles that are disproportionately large
      if (parseFloat(percentage) > 25) {
        this.analysis.optimizations.push({
          type: 'large-chunk',
          bundle: bundle.fileName,
          message: `${bundle.bundleType} represents ${percentage}% of total JS - consider splitting`,
          priority: 'high'
        });
      }
    }
    console.log();
  }

  checkForDuplicateCode() {
    // This would require more sophisticated analysis
    // For now, provide general recommendations based on bundle patterns
    
    const vendorBundles = this.analysis.bundles.filter(b => 
      b.bundleType === 'Third-party Libraries' || b.fileName.includes('vendor')
    );
    
    if (vendorBundles.length > 3) {
      this.analysis.optimizations.push({
        type: 'vendor-splitting',
        message: `${vendorBundles.length} vendor bundles detected - consider consolidating common dependencies`,
        priority: 'medium'
      });
    }
  }

  analyzeCompressionEfficiency() {
    console.log('🗜️  Compression Efficiency Analysis:');
    
    const avgCompression = this.analysis.bundles.reduce((sum, b) => sum + b.compressionRatio, 0) / this.analysis.bundles.length;
    console.log(`   Average compression ratio: ${avgCompression.toFixed(1)}%`);
    
    // Find bundles with poor compression
    const poorCompression = this.analysis.bundles.filter(b => b.compressionRatio < 60);
    
    if (poorCompression.length > 0) {
      console.log('   📋 Bundles with poor compression (<60%):');
      for (const bundle of poorCompression) {
        console.log(`      ${bundle.fileName}: ${bundle.compressionRatio}%`);
        
        this.analysis.optimizations.push({
          type: 'poor-compression',
          bundle: bundle.fileName,
          message: `Poor compression ratio (${bundle.compressionRatio}%) - may contain redundant code`,
          priority: 'medium'
        });
      }
    }
    console.log();
  }

  generateRecommendations() {
    console.log('💡 Performance Recommendations\n');
    
    // Priority 1: Critical optimizations
    const criticalOptimizations = [
      {
        title: 'Implement Advanced Code Splitting',
        description: 'Split voice processing and data import features into separate lazy-loaded chunks',
        impact: 'High - Reduce initial bundle size by ~40%',
        effort: 'Medium',
        steps: [
          'Use React.lazy() for voice components',
          'Implement route-based code splitting',
          'Lazy load OpenAI and Papa Parse libraries',
          'Add intelligent preloading for critical features'
        ]
      },
      {
        title: 'Optimize Heavy Dependencies',
        description: 'Replace or optimize the heaviest third-party libraries',
        impact: 'High - Save 200-300KB compressed',
        effort: 'Medium',
        steps: [
          'Replace heavy chart library with lighter alternative',
          'Use date-fns tree shaking (already implemented)',
          'Optimize Supabase imports to use only needed modules',
          'Consider CDN loading for common libraries'
        ]
      }
    ];

    // Priority 2: Medium impact optimizations
    const mediumOptimizations = [
      {
        title: 'Enable Advanced Tree Shaking',
        description: 'Ensure all dependencies support tree shaking and unused code is eliminated',
        impact: 'Medium - Save 100-150KB compressed',
        effort: 'Low',
        steps: [
          'Audit imports for unused exports',
          'Use named imports instead of default imports',
          'Enable sideEffects: false in package.json for eligible packages',
          'Configure Vite for aggressive dead code elimination'
        ]
      },
      {
        title: 'Implement Service Worker Caching',
        description: 'Cache bundles and assets for repeat visits',
        impact: 'High - Improve repeat visit performance by 80%+',
        effort: 'Medium',
        steps: [
          'Implement Workbox service worker',
          'Cache JavaScript and CSS bundles',
          'Implement cache-first strategy for static assets',
          'Add cache invalidation for new deployments'
        ]
      }
    ];

    this.analysis.recommendations = [...criticalOptimizations, ...mediumOptimizations];
    
    for (const [index, rec] of this.analysis.recommendations.entries()) {
      console.log(`${index + 1}. 🎯 ${rec.title}`);
      console.log(`   ${rec.description}`);
      console.log(`   Impact: ${rec.impact}`);
      console.log(`   Effort: ${rec.effort}`);
      console.log('   Steps:');
      for (const step of rec.steps) {
        console.log(`     • ${step}`);
      }
      console.log();
    }
  }

  generateReport() {
    console.log('\n🎯 Bundle Performance Report\n');
    console.log('='.repeat(60));
    
    // Overall statistics
    console.log('📊 Overall Statistics:');
    console.log(`   Total bundles: ${this.analysis.bundles.length}`);
    console.log(`   Total size: ${this.formatBytes(this.analysis.totalSize)} (${this.formatBytes(this.analysis.gzippedSize)} compressed)`);
    console.log(`   Average compression: ${((this.analysis.totalSize - this.analysis.gzippedSize) / this.analysis.totalSize * 100).toFixed(1)}%`);
    
    // Budget status
    const overBudgetBundles = this.analysis.bundles.filter(b => b.isOverBudget);
    console.log(`   Budget compliance: ${overBudgetBundles.length === 0 ? '✅' : '❌'} (${overBudgetBundles.length} over budget)`);
    console.log();
    
    // Top 5 largest bundles
    console.log('🏆 Largest Bundles:');
    const topBundles = this.analysis.bundles.slice(0, 5);
    for (const [index, bundle] of topBundles.entries()) {
      console.log(`   ${index + 1}. ${bundle.fileName} (${this.formatBytes(bundle.gzippedSize)})`);
    }
    console.log();
    
    // Critical issues
    const criticalIssues = this.analysis.optimizations.filter(o => o.priority === 'high');
    if (criticalIssues.length > 0) {
      console.log('🚨 Critical Issues:');
      for (const issue of criticalIssues) {
        console.log(`   • ${issue.message}`);
      }
      console.log();
    }
    
    // Success metrics to track
    console.log('📈 Success Metrics to Track:');
    console.log('   • First Contentful Paint (FCP) < 1.5s');
    console.log('   • Largest Contentful Paint (LCP) < 2.5s');  
    console.log('   • Time to Interactive (TTI) < 3.5s');
    console.log('   • Main bundle < 400KB compressed');
    console.log('   • Total bundle size < 1.5MB compressed');
    console.log();
    
    // Next steps
    console.log('🚀 Recommended Next Steps:');
    console.log('   1. Implement voice component lazy loading');
    console.log('   2. Split data processing into separate chunk');
    console.log('   3. Add route-based code splitting');
    console.log('   4. Implement intelligent preloading');
    console.log('   5. Add performance monitoring to CI/CD');
    console.log();
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// CLI execution
if (import.meta.url === `file://${process.argv[1]}`) {
  const analyzer = new BundlePerformanceAnalyzer();
  
  analyzer.analyze()
    .then(() => {
      console.log('✅ Bundle analysis complete!');
      console.log('\n📋 To implement optimizations:');
      console.log('   npm run build:production  # Build with new optimizations');
      console.log('   npm run performance:check # Verify improvements');
    })
    .catch(error => {
      console.error('❌ Error analyzing bundles:', error.message);
      process.exit(1);
    });
}

export default BundlePerformanceAnalyzer;