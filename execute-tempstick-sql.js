#!/usr/bin/env node

// Direct execution of TempStick schema SQL
import { createClient } from '@supabase/supabase-js'
import { readFileSync } from 'fs'
import dotenv from 'dotenv'

dotenv.config()

const supabaseUrl = process.env.VITE_SUPABASE_URL
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing environment variables')
  console.error('   Need VITE_SUPABASE_URL and VITE_SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function executeTempStickSQL() {
  console.log('🌡️  Executing TempStick SQL Schema')
  console.log('=====================================')
  
  try {
    // Read the schema SQL file
    const schemaSQL = readFileSync('./supabase/migrations/20250825_001_tempstick_sensor_integration_schema.sql', 'utf8')
    
    console.log(`📄 Loaded schema SQL (${schemaSQL.length} characters)`)
    console.log('⚡ Executing SQL...')
    
    // Execute the raw SQL
    const { data, error } = await supabase.rpc('exec', { sql: schemaSQL })
    
    if (error) {
      console.error('❌ SQL execution failed:', error)
      
      // Try alternative approach - execute via raw query
      console.log('⚡ Trying alternative execution method...')
      const { data: altData, error: altError } = await supabase
        .from('__temp_sql_execution__')
        .select('*')
        .or('false')  // This will fail gracefully
        
      console.log('⚠️  Direct SQL execution not available via Supabase client')
      console.log('📝 Manual execution required in Supabase SQL Editor')
      
      return false
    } else {
      console.log('✅ Schema SQL executed successfully!')
      return true
    }
  } catch (err) {
    console.error('❌ Execution error:', err.message)
    return false
  }
}

// Run verification after execution attempt
async function verifyTables() {
  console.log('\n🔍 Verifying table creation...')
  
  const tables = ['storage_areas', 'sensors', 'temperature_readings', 'temperature_alerts']
  let successCount = 0
  
  for (const table of tables) {
    try {
      const { data, error } = await supabase.from(table).select('*').limit(1)
      if (error) {
        console.log(`   ❌ ${table}: ${error.message}`)
      } else {
        console.log(`   ✅ ${table}: Created successfully`)
        successCount++
      }
    } catch (err) {
      console.log(`   ❌ ${table}: ${err.message}`)
    }
  }
  
  console.log(`\n📊 Results: ${successCount}/${tables.length} tables created`)
  
  if (successCount === 0) {
    console.log('\n🔧 Manual SQL Execution Required:')
    console.log('   1. Open your Supabase project dashboard')
    console.log('   2. Go to SQL Editor')
    console.log('   3. Copy the contents of tempstick-manual-sql.sql')
    console.log('   4. Paste and run the SQL')
  }
  
  return successCount === tables.length
}

async function main() {
  const success = await executeTempStickSQL()
  const verified = await verifyTables()
  
  if (verified) {
    console.log('\n🎉 TempStick schema applied successfully!')
    console.log('✅ Ready to proceed with API integration')
  } else {
    console.log('\n⚠️  Schema execution needs manual intervention')
    console.log('📋 Next step: Execute tempstick-manual-sql.sql in Supabase dashboard')
  }
}

main().catch(console.error)