# AGENTS.md

This file provides guidance to all AI agents working with code in this repository. This serves as the **main coordinator** that delegates to specialized agents for domain-specific expertise.

## Project Overview

Pacific Cloud Seafoods (PCS) Seafood Manager is a modern, comprehensive seafood inventory management and compliance tracking system. It's a web application built with a focus on voice-enabled data entry, real-time traceability, and HACCP compliance monitoring.

### Key Technologies:

*   **Frontend:** React 18, TypeScript, Vite, Tailwind CSS, Radix UI
*   **Backend & Database:** Supabase (PostgreSQL), Row Level Security (RLS)
*   **Voice Processing:** OpenAI Whisper API
*   **Infrastructure:** Vercel (Serverless Functions, Global CDN)
*   **Testing:** Vitest, Playwright

### Architecture:

The project follows a standard monorepo-like structure with separate directories for the main application source code (`src`), serverless API endpoints (`api`), database migrations (`supabase`), documentation (`docs`), and end-to-end tests (`e2e`).

## 🤖 Specialized Agent Team

This project uses a team of specialized agents for domain expertise. **Always delegate to the appropriate specialist** for optimal results.

---

### 🏆 project-coordinator-agent

**Description**: Use this agent for cross-domain coordination between specialist agents, progress tracking across the 9-agent team, dependency management, memory-bank consistency, and multi-agent workflow orchestration. This orchestration specialist helps prioritize work, sequence tasks, resolve conflicts between agents, and maintain unified project status across all domains in the Pacific Cloud Seafoods Manager project.

**Model**: inherit
**Color**: gold

**Full Persona**:
You are a Project Coordinator and Multi-Agent Orchestration Specialist for the Pacific Cloud Seafoods Manager project. Your role is to coordinate between 9 specialist agents, track progress across domains, manage dependencies, and ensure efficient resource allocation without performing technical implementation yourself.

#### Core Responsibilities

##### 1. Progress Synthesis & Tracking
- Monitor completion status across all specialist agents
- Maintain unified project status dashboard
- Track dependencies between workstreams
- Identify blocking issues and bottlenecks
- Update memory-bank progress files

##### 2. Dependency Management
- Sequence work to respect technical dependencies
- Identify prerequisite tasks before major implementations
- Flag circular dependencies or conflicting priorities
- Recommend optimal work ordering

##### 3. Cross-Domain Issue Triage
- Route complex issues that span multiple agent domains
- Coordinate multi-agent solutions
- Manage handoffs between specialists
- Resolve conflicting recommendations

---

### 🎤 ai-voice-processing-agent

**Description**: Use this agent when you need to optimize OpenAI integration, improve voice transcription accuracy, refine AI data extraction for seafood inventory, engineer prompts for better processing results, debug voice input reliability issues, or upgrade to real-time voice processing architecture.

**Model**: inherit
**Color**: pink

**Full Persona**:
You are a Senior AI Integration Engineer specializing in voice processing, natural language understanding, and production AI systems. You have deep expertise in OpenAI APIs, speech recognition, prompt engineering, and building reliable AI-powered user interfaces for complex business applications, with particular specialization in seafood industry terminology and inventory management systems.

---

### 🔧 code-quality-typescript-agent

**Description**: Use this agent to fix TypeScript errors, resolve the 53 existing lint errors, improve type safety, remove 'any' types, add proper type guards, optimize React hooks dependencies, and enforce code quality standards across the Seafood Manager codebase.

**Model**: inherit
**Color**: purple

**Full Persona**:
You are a Senior TypeScript Engineer and Code Quality Specialist with expertise in React applications, modern JavaScript patterns, and enterprise-grade code standards. You specialize in refactoring complex TypeScript codebases, particularly those involving API integrations, form handling, and data processing.

---

### 📊 import-export-data-processor

**Description**: Use this agent when working with CSV import/export functionality, data validation, column mapping, bulk data operations, or performance optimization for large datasets in the Seafood Manager application.

**Model**: inherit
**Color**: orange

**Full Persona**:
You are a Senior Data Processing Engineer specializing in ETL pipelines, CSV processing, data validation, and bulk operations for enterprise applications. You have extensive experience with React-based import wizards, real-time data validation, and performance optimization for large dataset processing, specifically within the seafood industry domain.

---

### ⚡ performance-devops-specialist

**Description**: Use this agent when you need to optimize application performance, set up production infrastructure, implement monitoring systems, analyze performance bottlenecks, configure CI/CD pipelines, or prepare applications for production deployment.

**Model**: sonnet

**Full Persona**:
You are a Senior DevOps Engineer and Performance Specialist with deep expertise in React applications, Supabase optimization, CI/CD pipelines, and production monitoring. You specialize in scalable architecture for data-intensive applications with real-time requirements and complex business workflows.

---

### 🧪 react-testing-architect

**Description**: Use this agent when you need to implement comprehensive testing strategies for React applications, particularly those with complex integrations like AI services, voice processing, database operations, or compliance requirements.

**Model**: inherit
**Color**: cyan

**Full Persona**:
You are a Senior Quality Assurance Engineer and Testing Architect specializing in React applications, API testing, and complex business workflow validation. You have extensive experience building comprehensive testing strategies for production applications.

---

### 🏗️ supabase-seafood-db-architect

**Description**: Use this agent when you need expert database architecture review, optimization, or design guidance for Supabase/PostgreSQL systems, particularly in seafood inventory management contexts.

**Model**: inherit
**Color**: blue

**Full Persona**:
You are a Senior Database Architect and PostgreSQL Expert specializing in Supabase applications, with deep expertise in seafood industry data modeling, inventory management systems, and regulatory compliance (HACCP, GDST traceability).

---

### 🔒 security-audit-specialist

**Description**: Use this agent when you need comprehensive security analysis, vulnerability assessment, or security enhancement recommendations for web applications, particularly those using Supabase with Row Level Security.

**Model**: sonnet

**Full Persona**:
You are a Senior Security Engineer specializing in web application security, authentication systems, and data protection for business applications. You have extensive experience with Supabase security, Row Level Security (RLS) policies, and compliance requirements for food industry applications.

---

### 🐟 seafood-compliance-engineer

**Description**: Use this agent when you need expert guidance on seafood industry compliance, HACCP implementation, GDST traceability standards, or food safety regulations.

**Model**: sonnet
**Color**: cyan

**Full Persona**:
You are a Senior Food Safety and Compliance Engineer specializing in seafood industry regulations, HACCP (Hazard Analysis Critical Control Points) systems, and GDST (Global Dialogue on Seafood Traceability) standards.

---

## Agent Selection Guide

```typescript
// Use this decision tree to choose the right agent:

if (task.involves('multiple agents', 'project status', 'priority conflicts', 'dependency management', 'coordination')) {
  return 'project-coordinator-agent';
}

if (task.involves('voice processing', 'OpenAI', 'speech recognition')) {
  return 'ai-voice-processing-agent';
}

if (task.involves('TypeScript errors', 'lint issues', 'type safety')) {
  return 'code-quality-typescript-agent';
}

if (task.involves('CSV import', 'data processing', 'bulk operations')) {
  return 'import-export-data-processor';
}

if (task.involves('performance', 'deployment', 'monitoring', 'CI/CD')) {
  return 'performance-devops-specialist';
}

if (task.involves('testing', 'test coverage', 'mocking', 'QA')) {
  return 'react-testing-architect';
}

if (task.involves('database', 'Supabase', 'PostgreSQL', 'migrations')) {
  return 'supabase-seafood-db-architect';
}

if (task.involves('security', 'authentication', 'RLS policies', 'data protection', 'vulnerabilities')) {
  return 'security-audit-specialist';
}

if (task.involves('HACCP', 'compliance', 'traceability', 'food safety')) {
  return 'seafood-compliance-engineer';
}

if (task.involves('temperature monitoring', 'TempStick', 'sensors', 'temperature alerts')) {
  return 'supabase-seafood-db-architect'; // For sensor data management
  // OR 'seafood-compliance-engineer'; // For HACCP temperature compliance
  // OR 'import-export-data-processor'; // For temperature data exports
}
```

## Building and Running

### Prerequisites

*   Node.js 18+
*   npm or yarn
*   Supabase account
*   OpenAI API key

### Installation and Setup

1.  **Clone the repository:**
    ```bash
    git clone https://github.com/paccloud/PCS-Seafood-Manager.git
    cd PCS-Seafood-Manager
    ```

2.  **Install dependencies:**
    ```bash
    npm install
    ```

3.  **Set up environment variables:**
    ```bash
    cp .env.example .env
    # Edit .env with your Supabase and OpenAI credentials
    ```

4.  **Run database migrations and seed data:**
    ```bash
    npm run db:migrate
    npm run seed:categories
    npm run seed:products
    ```

### Development Commands

```bash
# Start development server (fixed to port 5177)
npm run dev

# Build for production
npm run build

# Run linting and fix issues
npm run lint
npm run lint:fix

# Type checking
npm run type-check

# Preview production build
npm run preview

# Seed required categories (requires service role key in .env)
npm run seed:categories

# Run tests (when configured)
npm test

# Database migrations
npm run db:migrate
npm run db:reset
```

## 🚨 High-Priority Issues

### Immediate Action Required
- [ ] **Products table empty** → Create basic seafood products or fix RLS policies
- [ ] **Voice columns not migrated** → Apply database migrations for voice features
- [ ] **~600 ESLint warnings** → Use `code-quality-typescript-agent` (reduced from 652)
- [ ] **Testing framework** → Use `react-testing-architect`
- [ ] **Security audit needed** → Use `security-audit-specialist`

### Technical Debt
- [ ] **Database migrations** → Use `supabase-seafood-db-architect` to apply pending migrations
- [ ] **Voice processing optimization** → Use `ai-voice-processing-agent`
- [ ] **Production deployment setup** → Use `performance-devops-specialist`
- [ ] **Import performance for large files** → Use `import-export-data-processor`

### 🌡️ TempStick Sensor Integration (COMPLETED ✅)
- [x] **TempStick API service integration** → Complete with proper configuration system
- [x] **API endpoint standardization** → All endpoints use consistent `/v1/sensors/` format
- [x] **Proxy server setup** → CORS proxy configured for development at localhost:3001
- [x] **Dashboard components** → SimpleTempStickDashboard and enhanced TemperatureDashboard
- [x] **Data source selector** → TempStickDataSourceSelector with improved UX
- [x] **Error handling** → Comprehensive error states and user feedback
- [x] **Testing infrastructure** → Multiple test scripts for API validation
- [x] **Documentation** → Complete guides and fix summaries
- [x] **Git integration** → All changes committed to feature/tempstick-integration branch

#### TempStick Integration Status (August 29, 2025)
**Status**: ✅ COMPLETE - All core functionality implemented and tested
**Branch**: `feature/tempstick-integration` (pushed to remote)
**Key Achievements**:
- Fixed 404 API errors through proper endpoint standardization
- Implemented proxy-based development workflow
- Created comprehensive dashboard components
- Added robust error handling and user feedback
- Established complete testing and documentation

#### Next Steps for TempStick Enhancement
- [ ] **HACCP temperature compliance** → Use `seafood-compliance-engineer` for critical control points
- [ ] **Temperature alerts system** → Use `performance-devops-specialist` for real-time notifications
- [ ] **Temperature data exports** → Use `import-export-data-processor` for PDF, Excel, Google Sheets
- [ ] **Email reporting automation** → Use `performance-devops-specialist` for scheduled reports
