import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import { securityHeadersPlugin } from './src/lib/security/security-headers';

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  const isProd = mode === 'production';
  
  return {
    plugins: [
      react({
        babel: {
          plugins: [
            '@babel/plugin-transform-runtime'
          ]
        }
      }),
      securityHeadersPlugin()
    ],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },
    define: {
      'process.env': env,
    },
    server: {
      port: 5177,
      strictPort: true,
      host: true,
      proxy: {
        '/api/tempstick': {
          target: 'https://tempstickapi.com',
          changeOrigin: true,
          rewrite: (path) => {
            const rewritten = path.replace(/^\/api\/tempstick/, '/api/v1');
            console.log(`[Proxy] Rewriting: ${path} -> ${rewritten}`);
            return rewritten;
          },
          secure: true,
          headers: {
            'User-Agent': 'Mozilla/5.0 (compatible; SeafoodManager/1.0)',
            'Accept': 'application/json',
            'Accept-Encoding': 'gzip'
          },
          configure: (proxy, _options) => {
            proxy.on('error', (err, _req, _res) => {
              console.error('[Proxy Error]', err);
            });
            proxy.on('proxyReq', (proxyReq, req, _res) => {
              console.log('[Proxy Request]', req.method, req.url, '->', proxyReq.path);
              // Log headers being sent
              const headers = proxyReq.getHeaders();
              console.log('[Proxy Headers]', headers);
            });
            proxy.on('proxyRes', (proxyRes, req, _res) => {
              console.log('[Proxy Response]', proxyRes.statusCode, req.url);
            });
          }
        }
      }
    },
    preview: {
      port: 5177,
      strictPort: true,
      host: true
    },
    optimizeDeps: {
      include: ['regenerator-runtime/runtime']
    },
    build: isProd ? {
      target: ['es2020', 'chrome80', 'safari13'],
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: true,
          drop_debugger: true,
          pure_funcs: ['console.log', 'console.info', 'console.debug', 'console.warn'],
          passes: 3,
          pure_getters: true,
          unsafe_comps: true,
          unsafe_math: true,
          unsafe_methods: true
        },
        mangle: {
          safari10: true,
          properties: {
            regex: /^_/
          }
        },
        format: {
          comments: false
        }
      },
      rollupOptions: {
        output: {
          manualChunks: (id) => {
            // Only split chunks that actually exist and are large enough
            if (id.includes('node_modules')) {
              // Major vendor chunks
              if (id.includes('@supabase')) {
                return 'supabase';
              }
              if (id.includes('react') || id.includes('react-dom')) {
                return 'react';
              }
              if (id.includes('@radix-ui')) {
                return 'ui-components';
              }
              if (id.includes('date-fns') || id.includes('react-calendar')) {
                return 'date-utils';
              }
              if (id.includes('zod') || id.includes('react-hook-form')) {
                return 'forms';
              }
              if (id.includes('lucide-react')) {
                return 'icons';
              }
              // Only create separate chunks for libraries that are actually large
              if (id.includes('lodash') || id.includes('ramda') || id.includes('axios')) {
                return 'vendor-utils';
              }
              return 'vendor';
            }
            
            // Only split our code if chunks would be meaningful
            if (id.includes('/components/voice/') || id.includes('/services/') && id.includes('Voice')) {
              return 'voice-features';
            }
            if (id.includes('/components/import/') && !id.includes('voice')) {
              return 'import-features';  
            }
            if (id.includes('/components/haccp/') && !id.includes('voice')) {
              return 'haccp-features';
            }
            
            // Keep everything else in main chunk for now
            return undefined;
          },
          chunkFileNames: '[name]-[hash].js'
        }
      },
      chunkSizeWarningLimit: 300,
      assetsInlineLimit: 4096,
      cssCodeSplit: true
    } : undefined
  };
});