// Add meaningful test events
import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

const supabaseUrl = process.env.VITE_SUPABASE_URL
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function addTestEvents() {
  console.log('📊 Adding Meaningful Test Events')
  console.log('================================\n')

  const testEvents = [
    {
      event_type: 'receiving',
      name: 'Wild Alaskan Salmon',
      quantity: 50,
      unit_price: 12.50,
      total_amount: 625.00,
      notes: 'Fresh catch from Bristol Bay - Grade A quality',
      metadata: { 
        unit: 'lbs',
        batch_number: 'AK-SAL-001',
        supplier: 'Alaska Fresh Seafood',
        temperature: '32°F',
        condition: 'excellent'
      }
    },
    {
      event_type: 'receiving',
      name: 'Dungeness Crab',
      quantity: 25,
      unit_price: 18.00,
      total_amount: 450.00,
      notes: 'Live crab delivery - premium size',
      metadata: { 
        unit: 'lbs',
        batch_number: 'DC-LIVE-002',
        supplier: 'Pacific Coast Crab Co.',
        condition: 'live',
        size: 'large'
      }
    },
    {
      event_type: 'sale',
      name: 'Wild Alaskan Salmon',
      quantity: -15,
      unit_price: 18.00,
      total_amount: 270.00,
      notes: 'Sold to Ocean View Restaurant',
      metadata: { 
        unit: 'lbs',
        customer: 'Ocean View Restaurant',
        order_number: 'OVR-001',
        delivery_date: new Date().toISOString()
      }
    },
    {
      event_type: 'physical_count',
      name: 'Pacific Cod',
      quantity: 30,
      unit_price: 8.50,
      total_amount: 255.00,
      notes: 'Weekly inventory count',
      metadata: { 
        unit: 'lbs',
        count_type: 'weekly',
        counter: 'staff',
        variance: 0
      }
    },
    {
      event_type: 'disposal',
      name: 'Day-old Halibut',
      quantity: -3,
      unit_price: 0,
      total_amount: 0,
      notes: 'Past sell-by date, disposed per HACCP protocol',
      metadata: { 
        unit: 'lbs',
        reason: 'expired',
        disposal_method: 'compost',
        authorization: 'manager'
      }
    }
  ]

  try {
    console.log('Adding test events...')
    const { data, error } = await supabase
      .from('inventory_events')
      .insert(testEvents)
      .select()

    if (error) {
      console.error('❌ Insert failed:', error.message)
      return
    }

    console.log(`✅ Added ${data.length} test events`)

    // Verify all events
    console.log('\n📋 All events in database:')
    const { data: allEvents, error: queryError } = await supabase
      .from('inventory_events')
      .select(`
        id,
        event_type,
        name,
        quantity,
        total_amount,
        notes,
        created_at
      `)
      .order('created_at', { ascending: false })

    if (queryError) {
      console.error('❌ Query failed:', queryError.message)
      return
    }

    allEvents.forEach((event, index) => {
      const total = event.total_amount ? `$${event.total_amount}` : '$0'
      console.log(`${index + 1}. ${event.event_type.toUpperCase()}: ${event.name || 'Unnamed'} (${event.quantity} units) - ${total}`)
      if (event.notes) {
        console.log(`   Notes: ${event.notes.substring(0, 60)}${event.notes.length > 60 ? '...' : ''}`)
      }
    })

    console.log(`\n✅ Total events: ${allEvents.length}`)
    console.log('🎉 Test data ready! EventsTable should now display events.')

  } catch (error) {
    console.error('💥 Error:', error)
  }
}

addTestEvents().then(() => {
  console.log('\n🏁 Test events setup complete')
  process.exit(0)
}).catch(error => {
  console.error('\n💥 Setup failed:', error)
  process.exit(1)
})