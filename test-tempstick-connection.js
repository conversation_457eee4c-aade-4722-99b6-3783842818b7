#!/usr/bin/env node

/**
 * Test TempStick API Connection
 * 
 * This script tests direct connectivity to the TempStick API
 * to verify API key, endpoints, and data structure.
 */

import fetch from 'node-fetch';
import 'dotenv/config';

const API_KEY = process.env.VITE_TEMPSTICK_API_KEY;
const BASE_URL = 'https://tempstickapi.com/api/v1';

if (!API_KEY) {
  console.error('❌ VITE_TEMPSTICK_API_KEY environment variable is required');
  process.exit(1);
}

/**
 * Make a TempStick API request
 */
async function makeApiRequest(endpoint) {
  const url = `${BASE_URL}${endpoint}`;
  console.log(`🌐 Making request to: ${url}`);
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'X-API-KEY': API_KEY,
        'Accept': 'application/json',
        'User-Agent': 'SeafoodManager/1.0'
      }
    });
    
    console.log(`📡 Response status: ${response.status} ${response.statusText}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }
    
    const data = await response.json();
    console.log(`✅ Response received:`, JSON.stringify(data, null, 2));
    return data;
    
  } catch (error) {
    console.error(`❌ Request failed:`, error.message);
    throw error;
  }
}

/**
 * Main test function
 */
async function testTempStickApi() {
  console.log('🧪 Testing TempStick API Connection...\n');
  
  try {
    // Test 1: Get all sensors
    console.log('📊 Test 1: Get all sensors');
    console.log('═'.repeat(40));
    const sensorsResponse = await makeApiRequest('/sensors/all');
    
    if (sensorsResponse.success && sensorsResponse.sensors) {
      console.log(`🎉 Found ${sensorsResponse.sensors.length} sensors`);
      
      // Test 2: Get readings for first sensor if available
      if (sensorsResponse.sensors.length > 0) {
        const firstSensor = sensorsResponse.sensors[0];
        console.log(`\n📈 Test 2: Get readings for sensor: ${firstSensor.sensor_name} (${firstSensor.sensor_id})`);
        console.log('═'.repeat(40));
        
        const readingsResponse = await makeApiRequest(`/readings/${firstSensor.sensor_id}/10`);
        
        if (readingsResponse.success && readingsResponse.readings) {
          console.log(`🎉 Retrieved ${readingsResponse.readings.length} readings`);
          if (readingsResponse.readings.length > 0) {
            const latestReading = readingsResponse.readings[0];
            console.log(`📊 Latest reading:`, {
              timestamp: latestReading.timestamp,
              temperature: latestReading.temperature,
              humidity: latestReading.humidity,
              signal: latestReading.signal
            });
          }
        } else {
          console.log(`⚠️ No readings found for sensor ${firstSensor.sensor_id}`);
        }
      }
      
    } else {
      console.log('⚠️ No sensors found or unexpected response structure');
    }
    
    // Test 3: Get user info
    console.log(`\n👤 Test 3: Get user info`);
    console.log('═'.repeat(40));
    const userResponse = await makeApiRequest('/user');
    
    if (userResponse.success && userResponse.user) {
      console.log(`🎉 User: ${userResponse.user.full_name} (${userResponse.user.email})`);
    }
    
    console.log('\n✅ All tests completed successfully!');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run the tests
testTempStickApi().catch(console.error);