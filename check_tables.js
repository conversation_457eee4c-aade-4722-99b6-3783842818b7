require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkTables() {
  try {
    console.log('Checking existing tables in Supabase...');
    
    // Query to get existing tables
    const { data, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public');
    
    if (error) {
      console.error('Error fetching tables:', error);
      
      // Try a different approach
      console.log('Trying to query a known table directly...');
      
      const knownTables = ['products', 'customers', 'vendors', 'batches', 'events', 'inventory'];
      
      for (const table of knownTables) {
        const { data: tableData, error: tableError } = await supabase
          .from(table)
          .select('*')
          .limit(1);
        
        if (!tableError) {
          console.log(`✓ Found table: ${table}`);
        } else {
          console.log(`✗ Table not found or error: ${table}`);
        }
      }
      
      return;
    }
    
    console.log('Tables found:', data.map(row => row.table_name).join(', '));
    
    // Now check for the tables we need to add
    const requiredTables = [
      'cogs', 
      'fulfillments', 
      'haccp_logs', 
      'haccp_audits', 
      'inventory_transactions',
      'platform_integrations',
      'sync_logs',
      'gdst_traceability'
    ];
    
    console.log('\nChecking for required tables:');
    
    for (const tableName of requiredTables) {
      const exists = data.some(row => row.table_name === tableName);
      console.log(`${exists ? '✓' : '✗'} ${tableName}`);
    }
    
    // Check if vendors need to be renamed to suppliers
    const vendorsExists = data.some(row => row.table_name === 'vendors');
    const suppliersExists = data.some(row => row.table_name === 'suppliers');
    
    console.log(`\nVendors table exists: ${vendorsExists ? 'Yes' : 'No'}`);
    console.log(`Suppliers table exists: ${suppliersExists ? 'Yes' : 'No'}`);
    
    if (vendorsExists && !suppliersExists) {
      console.log('Need to rename "vendors" to "suppliers"');
    }
    
  } catch (error) {
    console.error('Error checking tables:', error);
  }
}

checkTables();
