#!/usr/bin/env node

/**
 * TempStick API Connectivity Test
 * Tests the TempStick service layer and API connectivity
 */

import { tempStickService } from './src/lib/tempstick-service.ts'
import dotenv from 'dotenv'

dotenv.config()

async function testTempStickAPI() {
  console.log('🌡️  TempStick API Connectivity Test')
  console.log('===================================')
  
  try {
    // Check if we have API key configured
    const apiKey = process.env.VITE_TEMPSTICK_API_KEY
    if (!apiKey) {
      console.log('❌ VITE_TEMPSTICK_API_KEY not configured')
      console.log('Please set your TempStick API key in the .env file')
      process.exit(1)
    } else {
      console.log('🔑 API key configured, testing real API...')
    }
    
    console.log('\n📊 Test 1: Get Sensors')
    console.log('----------------------')
    
    const sensors = await tempStickService.getSensors()
    console.log(`   ✅ Found ${sensors.length} sensors`)
    
    if (sensors.length > 0) {
      const sensor = sensors[0]
      console.log(`   📍 Sample sensor: ${sensor.name} (${sensor.device_name})`)
      console.log(`   🔋 Battery: ${sensor.battery_level || 'N/A'}%`)
      console.log(`   📡 Status: ${sensor.connection_status}`)
      
      console.log('\n🌡️  Test 2: Get Latest Readings')
      console.log('--------------------------------')
      
      try {
        const readings = await tempStickService.getLatestReadings(sensor.sensor_id, 5)
        console.log(`   ✅ Retrieved ${readings.length} readings`)
        
        if (readings.length > 0) {
          const latest = readings[0]
          console.log(`   📊 Latest: ${latest.temp_fahrenheit}°F, ${latest.humidity}% humidity`)
          console.log(`   ⏰ Time: ${latest.recorded_at}`)
        }
      } catch (readingError) {
        console.log(`   ⚠️  Readings test: ${readingError.message}`)
      }
      
      console.log('\n🔍 Test 3: Health Check')
      console.log('-----------------------')
      
      try {
        const health = await tempStickService.getSensorHealth(sensor.sensor_id)
        console.log(`   ✅ Health status: ${health.status}`)
        console.log(`   📡 Last seen: ${health.last_seen || 'Unknown'}`)
        console.log(`   🔋 Battery: ${health.battery_level || 'N/A'}%`)
      } catch (healthError) {
        console.log(`   ⚠️  Health check: ${healthError.message}`)
      }
    }
    
    console.log('\n🔄 Test 4: Service Configuration')
    console.log('--------------------------------')
    
    const config = tempStickService.getConfiguration()
    console.log(`   📊 Data source: ${config.dataSource}`)
    console.log(`   🔄 Rate limit: ${config.rateLimit?.requestsPerMinute}/min`)
    console.log(`   ⏱️  Retry attempts: ${config.retry?.maxAttempts}`)
    
    console.log('\n🧪 Test 5: Mock Data Fallback')
    console.log('-----------------------------')
    
    // Test fallback to mock data
    tempStickService.useAutoMode()
    const mockSensors = await tempStickService.getSensors()
    console.log(`   ✅ Auto mode sensors: ${mockSensors.length}`)
    console.log(`   🎯 Mock data working: ${mockSensors.length > 0 ? 'Yes' : 'No'}`)
    
    return true
    
  } catch (error) {
    console.error('❌ API test failed:', error)
    return false
  }
}

async function testRateLimiting() {
  console.log('\n⏱️  Test 6: Rate Limiting')
  console.log('------------------------')
  
  try {
    const startTime = Date.now()
    
    // Make multiple rapid requests to test rate limiting
    const promises = []
    for (let i = 0; i < 3; i++) {
      promises.push(tempStickService.getSensors())
    }
    
    const results = await Promise.allSettled(promises)
    const successCount = results.filter(r => r.status === 'fulfilled').length
    const elapsed = Date.now() - startTime
    
    console.log(`   📊 Requests: ${successCount}/${results.length} succeeded`)
    console.log(`   ⏱️  Time taken: ${elapsed}ms`)
    console.log(`   🛡️  Rate limiting: ${elapsed > 1000 ? 'Active' : 'Inactive'}`)
    
  } catch (error) {
    console.log(`   ⚠️  Rate limit test: ${error.message}`)
  }
}

async function main() {
  const success = await testTempStickAPI()
  await testRateLimiting()
  
  console.log('\n🎯 Test Summary')
  console.log('===============')
  
  if (success) {
    console.log('✅ TempStick service layer is working')
    console.log('✅ API connectivity tested successfully')
    console.log('✅ Mock data fallback operational')
    console.log('\n🚀 Ready for dashboard integration!')
  } else {
    console.log('❌ TempStick service layer needs attention')
    console.log('⚠️  Check API configuration and service code')
  }
  
  console.log('\n📋 Next Steps:')
  console.log('   1. ✅ TempStick API connectivity - TESTED')
  console.log('   2. ⏳ Validate service layer operations')
  console.log('   3. ⏳ Test dashboard real-time updates')
  console.log('   4. ⏳ Database tables (manual creation needed)')
}

main().catch(console.error)