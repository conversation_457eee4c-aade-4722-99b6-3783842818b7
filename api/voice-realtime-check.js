// OpenAI Realtime API availability check endpoint
export default async function handler(req, res) {
  // CORS headers
  res.setHeader('Access-Control-Allow-Origin', process.env.FRONTEND_URL || 'http://localhost:5177');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Check if OpenAI API key is configured
    if (!process.env.OPENAI_API_KEY) {
      return res.json({
        supported: false,
        reason: 'OpenAI API key not configured',
        fallback_available: true
      });
    }

    // For now, return false to use the proven Whisper fallback
    // The Realtime API is still in beta and may have availability issues
    return res.json({
      supported: false,
      reason: 'Using proven Whisper + GPT pipeline for reliability',
      fallback_available: true,
      features: {
        whisper_transcription: true,
        gpt_processing: true,
        browser_speech_recognition: true,
        sub_second_latency: false, // Will be ~2-3 seconds with current implementation
        real_time_streaming: false
      }
    });

    // Future implementation when Realtime API is stable:
    /*
    // Test connection to OpenAI Realtime API
    const testResponse = await fetch('https://api.openai.com/v1/realtime/sessions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
        'Content-Type': 'application/json',
        'OpenAI-Beta': 'realtime=v1'
      },
      body: JSON.stringify({
        model: 'gpt-4o-realtime-preview-2024-10-01',
        modalities: ['text'],
        instructions: 'Test connection'
      })
    });

    if (testResponse.ok) {
      return res.json({
        supported: true,
        features: {
          real_time_streaming: true,
          sub_300ms_latency: true,
          voice_activity_detection: true,
          natural_interruption: true,
          direct_audio_processing: true
        }
      });
    } else {
      return res.json({
        supported: false,
        reason: 'Realtime API not accessible',
        fallback_available: true
      });
    }
    */

  } catch (error) {
    console.error('Realtime API check failed:', error);
    
    return res.json({
      supported: false,
      reason: 'Connection test failed',
      fallback_available: true,
      error: error.message
    });
  }
}