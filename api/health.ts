// Health Check API Endpoint for Seafood Manager
// Vercel serverless function to provide health status

import { VercelRequest, VercelResponse } from '@vercel/node';

interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  version: string;
  environment: string;
  uptime: number;
  checks: {
    database: boolean;
    cache: boolean;
    external_apis: boolean;
  };
  performance: {
    response_time: number;
    memory_usage?: number;
  };
}

export default async function handler(req: VercelRequest, res: VercelResponse) {
  const startTime = Date.now();
  
  try {
    // Set CORS headers for monitoring tools
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    
    if (req.method === 'OPTIONS') {
      return res.status(200).end();
    }
    
    if (req.method !== 'GET') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Perform health checks
    const healthStatus = await performHealthChecks();
    const responseTime = Date.now() - startTime;
    
    healthStatus.performance.response_time = responseTime;
    
    // Determine overall status
    const { database, cache, external_apis } = healthStatus.checks;
    let status: 'healthy' | 'degraded' | 'unhealthy';
    
    if (database && cache && external_apis) {
      status = 'healthy';
    } else if (database) {
      status = 'degraded';
    } else {
      status = 'unhealthy';
    }
    
    healthStatus.status = status;
    
    // Set appropriate status code
    const statusCode = status === 'healthy' ? 200 : status === 'degraded' ? 202 : 503;
    
    return res.status(statusCode).json(healthStatus);
    
  } catch (error) {
    console.error('Health check failed:', error);
    
    return res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      version: process.env.VITE_APP_VERSION || '1.0.0',
      environment: process.env.NODE_ENV || 'production',
      uptime: process.uptime(),
      checks: {
        database: false,
        cache: false,
        external_apis: false
      },
      performance: {
        response_time: Date.now() - startTime
      },
      error: (error as Error).message
    });
  }
}

async function performHealthChecks(): Promise<HealthStatus> {
  const checks = await Promise.allSettled([
    checkDatabaseHealth(),
    checkCacheHealth(),
    checkExternalAPIs()
  ]);
  
  return {
    status: 'healthy', // Will be overridden
    timestamp: new Date().toISOString(),
    version: process.env.VITE_APP_VERSION || '1.0.0',
    environment: process.env.NODE_ENV || 'production',
    uptime: process.uptime(),
    checks: {
      database: checks[0].status === 'fulfilled' && checks[0].value,
      cache: checks[1].status === 'fulfilled' && checks[1].value,
      external_apis: checks[2].status === 'fulfilled' && checks[2].value
    },
    performance: {
      response_time: 0, // Will be set later
      memory_usage: process.memoryUsage().heapUsed / 1024 / 1024 // MB
    }
  };
}

async function checkDatabaseHealth(): Promise<boolean> {
  try {
    if (!process.env.VITE_SUPABASE_URL || !process.env.VITE_SUPABASE_ANON_KEY) {
      return false;
    }
    
    // Dynamic import to avoid bundling issues
    const { createClient } = await import('@supabase/supabase-js');
    const supabase = createClient(
      process.env.VITE_SUPABASE_URL,
      process.env.VITE_SUPABASE_ANON_KEY
    );
    
    // Simple ping query with timeout
    const { error } = await Promise.race([
      supabase.from('inventory_events').select('count').limit(1),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Database timeout')), 5000)
      )
    ]) as any;
    
    return !error;
  } catch (error) {
    console.error('Database health check failed:', error);
    return false;
  }
}

async function checkCacheHealth(): Promise<boolean> {
  try {
    // Since this is a serverless function, we can't check browser cache
    // Instead, check if we can store and retrieve data in memory
    const testData = { timestamp: Date.now() };
    const serialized = JSON.stringify(testData);
    const parsed = JSON.parse(serialized);
    
    return parsed.timestamp === testData.timestamp;
  } catch (error) {
    console.error('Cache health check failed:', error);
    return false;
  }
}

async function checkExternalAPIs(): Promise<boolean> {
  try {
    // Check OpenAI API health
    if (!process.env.VITE_OPENAI_API_KEY) {
      return false;
    }
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);
    
    const response = await fetch('https://api.openai.com/v1/models', {
      headers: {
        'Authorization': `Bearer ${process.env.VITE_OPENAI_API_KEY}`,
      },
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    return response.ok;
  } catch (error) {
    console.error('External API health check failed:', error);
    return false;
  }
}