// Secure server-side voice processing endpoint
import OpenAI from 'openai';

// Initialize OpenAI with server-side API key (secure)
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY, // Server-side only
});

// Comprehensive seafood terminology database for enhanced recognition
const SEAFOOD_DATABASE = {
  finfish: [
    // Salmon varieties
    'Atlantic Salmon', 'Pacific King Salmon', 'Coho Salmon', 'Sockeye Salmon', 'Chinook Salmon', 'Pink Salmon', 'Chum Salmon',
    // Tuna species
    'Bluefin Tuna', 'Yellowfin Tuna', 'Albacore Tuna', 'Skipjack Tuna', 'Bigeye Tuna',
    // Cod family
    'Atlantic Cod', 'Pacific Cod', 'Lingcod', 'Black Cod', 'Sablefish',
    // Flatfish
    'Pacific Halibut', 'Atlantic Halibut', 'Dover Sole', 'Petrale Sole', 'English Sole', 'Flounder',
    // Other popular finfish
    'Sea Bass', 'Striped Bass', 'Red Snapper', 'Grouper', '<PERSON><PERSON>', 'Swordfish', 'Rockfish'
  ],
  shellfish: [
    // Oyster varieties
    'Pacific Oysters', 'Eastern Oysters', 'Kumamoto Oysters', 'Belon Oysters', 'Blue Point Oysters',
    // Clam types
    'Manila Clams', 'Littleneck Clams', 'Razor Clams', 'Geoduck Clams', 'Steamer Clams',
    // Mussel species
    'Blue Mussels', 'Mediterranean Mussels', 'Green Mussels',
    // Scallops
    'Sea Scallops', 'Bay Scallops', 'Diver Scallops'
  ],
  crustaceans: [
    // Lobster types
    'Maine Lobster', 'Spiny Lobster', 'Rock Lobster', 'Langostino',
    // Crab species
    'Dungeness Crab', 'King Crab', 'Snow Crab', 'Blue Crab', 'Jonah Crab', 'Stone Crab',
    // Shrimp/Prawn varieties
    'Tiger Prawns', 'Spot Prawns', 'White Shrimp', 'Pink Shrimp', 'Rock Shrimp'
  ],
  specialty: [
    'Caviar', 'Salmon Roe', 'Sea Urchin', 'Uni', 'Octopus', 'Squid', 'Cuttlefish', 'Abalone'
  ]
};

// Enhanced voice recognition errors and corrections
const VOICE_CORRECTIONS = {
  // Crab corrections
  'dangerous grab': 'Dungeness Crab',
  'dangerous crab': 'Dungeness Crab',
  'king grab': 'King Crab',
  'snow grab': 'Snow Crab',
  'blue grab': 'Blue Crab',
  
  // Salmon corrections
  'silver salmon': 'Coho Salmon',
  'king salmon': 'Chinook Salmon',
  'red salmon': 'Sockeye Salmon',
  
  // Sole corrections
  'dover soul': 'Dover Sole',
  'petrel sole': 'Petrale Sole',
  'english soul': 'English Sole',
  
  // Scallop corrections
  'see scallops': 'Sea Scallops',
  'bay scallops': 'Bay Scallops',
  'diver scallops': 'Diver Scallops',
  
  // Processing method corrections
  'age and g': 'H&G',
  'head and gutted': 'H&G',
  'i q f': 'IQF',
  'individually quick frozen': 'IQF',
  'previously frozen': 'Previously Frozen',
  
  // Vendor corrections
  'forty ninth state': '49th State Seafoods',
  'forty-ninth state': '49th State Seafoods',
  'forty nine state': '49th State Seafoods',
  'pacific seafood': 'Pacific Seafoods',
  'pac seafoods': 'Pacific Seafoods',
  'ocean fresh seafood': 'Ocean Fresh Seafoods',
  'trident seafood': 'Trident Seafoods',
  
  // Other corrections
  'alaskan pollock': 'Alaska Pollock',
  'pacific rock cod': 'Pacific Rockfish',
  'maine lobster tales': 'Maine Lobster Tails',
  'little neck clams': 'Littleneck Clams',
  'prince edward island mussels': 'PEI Mussels'
};

// Enhanced prompt with seafood-specific context
const SEAFOOD_SYSTEM_PROMPT = `You are a seafood inventory processing expert. Extract inventory information from voice input with high accuracy for seafood terminology.

SEAFOOD CATEGORIES AND PRODUCTS:
Finfish: ${SEAFOOD_DATABASE.finfish.join(', ')}
Shellfish: ${SEAFOOD_DATABASE.shellfish.join(', ')}
Crustaceans: ${SEAFOOD_DATABASE.crustaceans.join(', ')}
Specialty: ${SEAFOOD_DATABASE.specialty.join(', ')}

VOICE RECOGNITION CORRECTIONS:
Apply these common corrections:
${Object.entries(VOICE_CORRECTIONS).map(([wrong, right]) => `"${wrong}" → "${right}"`).join('\n')}

EXTRACTION RULES:
1. Product identification: Match against seafood database, apply voice corrections
2. Category assignment: Must be exactly "Finfish", "Shellfish", "Crustaceans", or "Specialty"
3. Quantity extraction: Include numbers with units (lbs, kg, units, cases)
4. Price extraction: Identify per-unit pricing
5. Vendor/Origin: Extract supplier or geographic origin
6. Storage requirements: Temperature, handling instructions
7. Quality indicators: Grade, freshness, processing method

CONFIDENCE SCORING:
- High (0.9+): Exact match with database, clear quantities/prices
- Medium (0.7-0.89): Fuzzy match, some missing data
- Low (0.5-0.69): Basic text extraction, limited matching

Return valid JSON with confidence score and processing metadata.`;

export default async function handler(req, res) {
  // Security headers
  res.setHeader('Access-Control-Allow-Origin', process.env.FRONTEND_URL || 'http://localhost:5177');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { transcript, options = {} } = req.body;

    if (!transcript || typeof transcript !== 'string') {
      return res.status(400).json({ error: 'Invalid transcript provided' });
    }

    // Apply voice corrections before processing
    let correctedTranscript = transcript.toLowerCase();
    Object.entries(VOICE_CORRECTIONS).forEach(([wrong, right]) => {
      correctedTranscript = correctedTranscript.replace(new RegExp(wrong, 'gi'), right);
    });

    const startTime = Date.now();

    // Enhanced OpenAI processing with seafood context
    const completion = await openai.chat.completions.create({
      messages: [
        {
          role: 'system',
          content: SEAFOOD_SYSTEM_PROMPT
        },
        {
          role: 'user', 
          content: `Process this seafood inventory voice input: "${correctedTranscript}"`
        }
      ],
      model: options.model || 'gpt-3.5-turbo',
      temperature: 0.2, // Lower temperature for more consistent extraction
      response_format: { type: 'json_object' }
    });

    const processingTime = Date.now() - startTime;
    const result = completion.choices[0]?.message?.content;

    if (!result) {
      throw new Error('No response from OpenAI');
    }

    let parsedData;
    try {
      parsedData = JSON.parse(result);
    } catch (parseError) {
      // Fallback to basic processing if JSON parsing fails
      return res.json(basicSeafoodProcessing(correctedTranscript, processingTime));
    }

    // Add comprehensive metadata
    const processedData = {
      ...parsedData,
      metadata: {
        processing_method: 'ai_enhanced',
        original_text: transcript,
        corrected_text: correctedTranscript,
        confidence_score: parsedData.confidence_score || 0.8,
        processing_time_ms: processingTime,
        model: options.model || 'gpt-3.5-turbo',
        voice_corrections_applied: Object.keys(VOICE_CORRECTIONS).some(wrong => 
          transcript.toLowerCase().includes(wrong)
        ),
        timestamp: new Date().toISOString()
      }
    };

    return res.json(processedData);

  } catch (error) {
    console.error('Voice processing error:', error);
    
    // Enhanced fallback processing for errors
    if (req.body?.transcript) {
      return res.json(basicSeafoodProcessing(req.body.transcript, 0, error.message));
    }

    return res.status(500).json({ 
      error: 'Voice processing failed',
      fallback: true,
      details: error.message
    });
  }
}

// Enhanced fallback processing with seafood specialization
function basicSeafoodProcessing(text, processingTime = 0, errorMessage = null) {
  const data = {};
  const lowerText = text.toLowerCase();

  // Enhanced seafood product detection
  let detectedProduct = null;
  let detectedCategory = null;

  // Check all seafood categories for matches
  for (const [category, products] of Object.entries(SEAFOOD_DATABASE)) {
    for (const product of products) {
      if (lowerText.includes(product.toLowerCase())) {
        detectedProduct = product;
        detectedCategory = category.charAt(0).toUpperCase() + category.slice(1);
        break;
      }
    }
    if (detectedProduct) break;
  }

  // Apply voice corrections
  Object.entries(VOICE_CORRECTIONS).forEach(([wrong, right]) => {
    if (lowerText.includes(wrong)) {
      detectedProduct = right;
      // Determine category for corrected product
      for (const [category, products] of Object.entries(SEAFOOD_DATABASE)) {
        if (products.includes(right)) {
          detectedCategory = category.charAt(0).toUpperCase() + category.slice(1);
          break;
        }
      }
    }
  });

  if (detectedProduct) {
    data.product = detectedProduct;
    data.category = detectedCategory;
    data.subCategory = detectedProduct;
  }

  // Enhanced quantity extraction
  const quantityPatterns = [
    /(\d+(?:\.\d+)?)\s*(kg|kilograms?|pounds?|lbs?|units?|pieces?|pcs|cases?)/gi,
    /quantity(?:\s+of)?\s+(\d+(?:\.\d+)?)/gi,
    /(\d+(?:\.\d+)?)\s*(?:units?|boxes?|cases?)/gi
  ];

  for (const pattern of quantityPatterns) {
    const match = pattern.exec(text);
    if (match) {
      data.quantity = parseFloat(match[1]);
      if (match[2]) {
        const unitMap = {
          'kg': 'kg', 'kilogram': 'kg', 'kilograms': 'kg',
          'pound': 'lbs', 'pounds': 'lbs', 'lbs': 'lbs', 'lb': 'lbs',
          'piece': 'units', 'pieces': 'units', 'pcs': 'units', 'units': 'units',
          'case': 'cases', 'cases': 'cases'
        };
        data.unit = unitMap[match[2].toLowerCase()] || 'lbs';
      }
      break;
    }
  }

  // Enhanced price extraction
  const pricePatterns = [
    /\$?(\d+(?:\.\d{2})?)\s*(?:per\s+(?:kg|pound|lb|unit|case))?/gi,
    /(?:price|cost)(?:\s+of)?\s+\$?(\d+(?:\.\d{2})?)/gi,
    /at\s+\$?(\d+(?:\.\d{2})?)/gi
  ];

  for (const pattern of pricePatterns) {
    const match = pattern.exec(text);
    if (match) {
      data.price = parseFloat(match[1]);
      break;
    }
  }

  // Enhanced vendor/origin extraction
  const vendorPatterns = [
    /from\s+([a-zA-Z\s&.-]+?)(?:\s+(?:at|for|received|delivered))/gi,
    /vendor\s+([a-zA-Z\s&.-]+)/gi,
    /supplied\s+by\s+([a-zA-Z\s&.-]+)/gi
  ];

  for (const pattern of vendorPatterns) {
    const match = pattern.exec(text);
    if (match) {
      data.vendor = match[1].trim();
      break;
    }
  }

  // Calculate confidence score based on extracted data
  let confidence = 0.4; // Base score for basic processing
  if (detectedProduct) confidence += 0.2;
  if (data.quantity) confidence += 0.1;
  if (data.price) confidence += 0.1;
  if (data.vendor) confidence += 0.1;

  return {
    ...data,
    metadata: {
      processing_method: 'basic_enhanced',
      original_text: text,
      confidence_score: confidence,
      processing_time_ms: processingTime,
      seafood_database_match: !!detectedProduct,
      voice_correction_applied: Object.keys(VOICE_CORRECTIONS).some(wrong => 
        lowerText.includes(wrong)
      ),
      error_reason: errorMessage,
      timestamp: new Date().toISOString()
    }
  };
}