# API Directory

This directory contains serverless API endpoints for the Pacific Cloud Seafoods Manager application, providing voice processing capabilities and external integrations.

## Overview

The API directory houses serverless functions that handle computationally intensive operations, external API integrations, and specialized processing that's better suited for server-side execution. These endpoints are designed to work with Vercel's serverless function platform.

## API Endpoints

### Voice Processing Endpoints

#### `voice-process.js`
**Purpose**: Main voice processing endpoint for speech-to-text conversion and voice event creation.

**Functionality**:
- Receives audio blob data from client applications
- Processes audio through OpenAI Whisper API
- Performs confidence scoring and quality assessment
- Creates voice events in the database
- Returns processed transcription and metadata

**Request Format**:
```javascript
POST /api/voice-process
Content-Type: multipart/form-data

{
  audio: Blob,           // Audio file data
  metadata: {
    user_id: string,
    event_type: string,
    timestamp: string,
    location?: string
  }
}
```

**Response Format**:
```javascript
{
  success: boolean,
  data: {
    transcription: string,
    confidence_score: number,
    processing_time: number,
    event_id: string,
    audio_path?: string
  },
  error?: string
}
```

**Key Features**:
- Audio format validation and conversion
- OpenAI Whisper integration for speech-to-text
- Confidence scoring algorithm
- Database persistence with audit trail
- Error handling and retry logic
- Performance monitoring and logging

#### `voice-command-extract.js`
**Purpose**: Extracts structured commands and data from voice transcriptions.

**Functionality**:
- Parses natural language voice commands
- Extracts inventory data (quantities, products, locations)
- Validates extracted data against business rules
- Returns structured data for inventory operations

**Request Format**:
```javascript
POST /api/voice-command-extract
Content-Type: application/json

{
  transcription: string,
  context?: {
    current_location: string,
    user_role: string,
    recent_products: string[]
  }
}
```

**Response Format**:
```javascript
{
  success: boolean,
  data: {
    command_type: 'inventory_update' | 'product_lookup' | 'batch_tracking',
    extracted_data: {
      product_name?: string,
      quantity?: number,
      location?: string,
      batch_number?: string,
      action: 'add' | 'remove' | 'move' | 'count'
    },
    confidence: number,
    suggestions?: string[]
  },
  error?: string
}
```

**Key Features**:
- Natural language processing for command extraction
- Context-aware parsing using conversation history
- Business rule validation
- Fuzzy matching for product names and locations
- Confidence scoring for extracted data
- Suggestion engine for ambiguous commands

#### `voice-realtime-check.js`
**Purpose**: Real-time voice processing status and health monitoring.

**Functionality**:
- Checks voice processing service health
- Monitors API quotas and rate limits
- Provides real-time processing status updates
- Returns system performance metrics

**Request Format**:
```javascript
GET /api/voice-realtime-check?event_id={event_id}
```

**Response Format**:
```javascript
{
  success: boolean,
  data: {
    service_status: 'healthy' | 'degraded' | 'down',
    processing_queue_length: number,
    average_processing_time: number,
    api_quota_remaining: number,
    event_status?: {
      id: string,
      status: 'pending' | 'processing' | 'completed' | 'failed',
      progress?: number
    }
  },
  timestamp: string
}
```

**Key Features**:
- Real-time service health monitoring
- Processing queue management
- API quota tracking
- Event-specific status updates
- Performance metrics collection
- Alerting for service degradation

## Architecture Patterns

### Serverless Function Design
All API endpoints follow serverless best practices:

```javascript
export default async function handler(req, res) {
  // CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  try {
    // Input validation
    const validatedInput = validateInput(req.body);
    
    // Business logic
    const result = await processRequest(validatedInput);
    
    // Response formatting
    return res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    // Error handling
    console.error('API Error:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
}
```

### Error Handling Strategy
Consistent error handling across all endpoints:

```javascript
class APIError extends Error {
  constructor(message, statusCode = 500, code = 'INTERNAL_ERROR') {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
  }
}

// Usage
throw new APIError('Invalid audio format', 400, 'INVALID_AUDIO');
```

### Input Validation
All endpoints use Zod schemas for input validation:

```javascript
import { z } from 'zod';

const VoiceProcessSchema = z.object({
  audio: z.instanceof(Blob),
  metadata: z.object({
    user_id: z.string().uuid(),
    event_type: z.string(),
    timestamp: z.string().datetime()
  })
});
```

## External Integrations

### OpenAI Whisper API
Integration with OpenAI for speech-to-text processing:

```javascript
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

async function transcribeAudio(audioBuffer) {
  const response = await openai.audio.transcriptions.create({
    file: audioBuffer,
    model: 'whisper-1',
    language: 'en',
    response_format: 'verbose_json'
  });
  
  return {
    transcription: response.text,
    confidence: calculateConfidence(response),
    duration: response.duration
  };
}
```

### Supabase Integration
Database operations using Supabase client:

```javascript
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function createVoiceEvent(eventData) {
  const { data, error } = await supabase
    .from('inventory_events')
    .insert(eventData)
    .select()
    .single();
    
  if (error) throw new APIError('Database error', 500, 'DB_ERROR');
  return data;
}
```

## Performance Optimization

### Caching Strategy
Implement caching for frequently accessed data:

```javascript
// In-memory cache for session data
const cache = new Map();

function getCachedData(key, ttl = 300000) { // 5 minutes TTL
  const cached = cache.get(key);
  if (cached && Date.now() - cached.timestamp < ttl) {
    return cached.data;
  }
  return null;
}
```

### Connection Pooling
Efficient database connection management:

```javascript
// Reuse Supabase client across function invocations
let supabaseClient;

function getSupabaseClient() {
  if (!supabaseClient) {
    supabaseClient = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );
  }
  return supabaseClient;
}
```

### Rate Limiting
Implement rate limiting to prevent abuse:

```javascript
const rateLimiter = new Map();

function checkRateLimit(userId, limit = 100, window = 3600000) { // 100 requests per hour
  const now = Date.now();
  const userRequests = rateLimiter.get(userId) || [];
  
  // Remove old requests outside the window
  const validRequests = userRequests.filter(time => now - time < window);
  
  if (validRequests.length >= limit) {
    throw new APIError('Rate limit exceeded', 429, 'RATE_LIMIT');
  }
  
  validRequests.push(now);
  rateLimiter.set(userId, validRequests);
}
```

## Security Implementation

### Authentication
Verify user authentication for protected endpoints:

```javascript
async function verifyAuth(req) {
  const token = req.headers.authorization?.replace('Bearer ', '');
  if (!token) {
    throw new APIError('Authentication required', 401, 'AUTH_REQUIRED');
  }
  
  const { data: user, error } = await supabase.auth.getUser(token);
  if (error || !user) {
    throw new APIError('Invalid token', 401, 'INVALID_TOKEN');
  }
  
  return user;
}
```

### Input Sanitization
Sanitize all inputs to prevent injection attacks:

```javascript
function sanitizeInput(input) {
  if (typeof input === 'string') {
    return input.trim().replace(/[<>]/g, '');
  }
  return input;
}
```

### CORS Configuration
Proper CORS setup for cross-origin requests:

```javascript
function setCORSHeaders(res) {
  res.setHeader('Access-Control-Allow-Origin', process.env.ALLOWED_ORIGINS || '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Access-Control-Max-Age', '86400'); // 24 hours
}
```

## Monitoring and Logging

### Structured Logging
Implement structured logging for better observability:

```javascript
function log(level, message, metadata = {}) {
  const logEntry = {
    timestamp: new Date().toISOString(),
    level,
    message,
    ...metadata,
    requestId: req.headers['x-request-id'] || 'unknown'
  };
  
  console.log(JSON.stringify(logEntry));
}
```

### Performance Monitoring
Track API performance metrics:

```javascript
async function withMetrics(operation, metadata = {}) {
  const startTime = Date.now();
  
  try {
    const result = await operation();
    const duration = Date.now() - startTime;
    
    log('info', 'Operation completed', {
      ...metadata,
      duration,
      success: true
    });
    
    return result;
  } catch (error) {
    const duration = Date.now() - startTime;
    
    log('error', 'Operation failed', {
      ...metadata,
      duration,
      error: error.message,
      success: false
    });
    
    throw error;
  }
}
```

## Testing Strategy

### Unit Testing
Test individual functions and utilities:

```javascript
// voice-process.test.js
import { describe, it, expect, vi } from 'vitest';
import { processVoiceEvent } from './voice-process.js';

describe('Voice Processing', () => {
  it('should process valid audio input', async () => {
    const mockAudio = new Blob(['audio data'], { type: 'audio/wav' });
    const result = await processVoiceEvent(mockAudio, { user_id: 'test' });
    
    expect(result.success).toBe(true);
    expect(result.data.transcription).toBeDefined();
  });
});
```

### Integration Testing
Test API endpoints with real requests:

```javascript
// api.integration.test.js
import { describe, it, expect } from 'vitest';

describe('Voice API Integration', () => {
  it('should process voice command', async () => {
    const response = await fetch('/api/voice-process', {
      method: 'POST',
      body: formData
    });
    
    const result = await response.json();
    expect(result.success).toBe(true);
  });
});
```

## Deployment Configuration

### Environment Variables
Required environment variables for API functions:

```bash
# OpenAI Configuration
OPENAI_API_KEY=sk-...

# Supabase Configuration
SUPABASE_URL=https://...
SUPABASE_SERVICE_ROLE_KEY=eyJ...

# Application Configuration
ALLOWED_ORIGINS=https://yourdomain.com
NODE_ENV=production
```

### Vercel Configuration
`vercel.json` configuration for API routes:

```json
{
  "functions": {
    "api/voice-process.js": {
      "maxDuration": 30
    },
    "api/voice-command-extract.js": {
      "maxDuration": 10
    }
  }
}
```

## Future Enhancements

- Advanced voice processing with custom models
- Real-time streaming voice processing
- Multi-language support
- Voice biometric authentication
- Advanced analytics and reporting APIs
- Webhook endpoints for external integrations
- GraphQL API layer
- Advanced caching with Redis
- Message queue integration for async processing