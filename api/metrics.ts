// Metrics API Endpoint for Seafood Manager
// Provides performance and business metrics for monitoring

import { VercelRequest, VercelResponse } from '@vercel/node';

interface MetricsResponse {
  timestamp: string;
  application: {
    name: string;
    version: string;
    environment: string;
    uptime: number;
  };
  performance: {
    response_times: {
      avg: number;
      p95: number;
      p99: number;
    };
    memory_usage: {
      used: number;
      total: number;
      heap_used: number;
    };
    cpu_usage?: number;
  };
  business_metrics: {
    active_users: number;
    daily_transactions: number;
    voice_commands_processed: number;
    compliance_checks: number;
    error_rate: number;
  };
  system_health: {
    database_connections: number;
    cache_hit_rate: number;
    api_success_rate: number;
  };
}

export default async function handler(req: VercelRequest, res: VercelResponse) {
  try {
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    
    if (req.method === 'OPTIONS') {
      return res.status(200).end();
    }
    
    if (req.method !== 'GET') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Authentication check for metrics endpoint
    const authHeader = req.headers.authorization;
    if (!authHeader || !isValidMetricsToken(authHeader)) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const metrics = await collectMetrics();
    
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Content-Type', 'application/json');
    
    return res.status(200).json(metrics);
    
  } catch (error) {
    console.error('Metrics collection failed:', error);
    
    return res.status(500).json({
      error: 'Failed to collect metrics',
      message: (error as Error).message,
      timestamp: new Date().toISOString()
    });
  }
}

function isValidMetricsToken(authHeader: string): boolean {
  const token = authHeader.replace('Bearer ', '');
  const validToken = process.env.METRICS_API_TOKEN;
  
  return validToken && token === validToken;
}

async function collectMetrics(): Promise<MetricsResponse> {
  const memoryUsage = process.memoryUsage();
  
  return {
    timestamp: new Date().toISOString(),
    application: {
      name: 'seafood-manager',
      version: process.env.VITE_APP_VERSION || '1.0.0',
      environment: process.env.NODE_ENV || 'production',
      uptime: process.uptime()
    },
    performance: {
      response_times: await getResponseTimeMetrics(),
      memory_usage: {
        used: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
        total: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
        heap_used: Math.round(memoryUsage.heapUsed / 1024 / 1024) // MB
      },
      cpu_usage: await getCPUUsage()
    },
    business_metrics: await getBusinessMetrics(),
    system_health: await getSystemHealthMetrics()
  };
}

async function getResponseTimeMetrics(): Promise<{ avg: number; p95: number; p99: number }> {
  // In a real implementation, this would pull from a metrics store
  // For now, return mock data that would come from DataDog/Sentry
  return {
    avg: 250,
    p95: 500,
    p99: 1000
  };
}

async function getCPUUsage(): Promise<number> {
  try {
    // CPU usage calculation for serverless is complex
    // Return a reasonable estimate or undefined
    return 15; // Mock 15% CPU usage
  } catch {
    return 0;
  }
}

async function getBusinessMetrics(): Promise<{
  active_users: number;
  daily_transactions: number;
  voice_commands_processed: number;
  compliance_checks: number;
  error_rate: number;
}> {
  try {
    // Query Supabase for business metrics
    const { createClient } = await import('@supabase/supabase-js');
    const supabase = createClient(
      process.env.VITE_SUPABASE_URL!,
      process.env.VITE_SUPABASE_SERVICE_ROLE_KEY! // Use service role for metrics
    );

    const today = new Date().toISOString().split('T')[0];
    
    // Get daily transactions (inventory events)
    const { count: dailyTransactions } = await supabase
      .from('inventory_events')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', `${today}T00:00:00.000Z`)
      .lt('created_at', `${today}T23:59:59.999Z`);

    // Get voice commands processed today
    const { count: voiceCommands } = await supabase
      .from('voice_events')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', `${today}T00:00:00.000Z`)
      .lt('created_at', `${today}T23:59:59.999Z`);

    // Get HACCP compliance checks today
    const { count: complianceChecks } = await supabase
      .from('haccp_events')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', `${today}T00:00:00.000Z`)
      .lt('created_at', `${today}T23:59:59.999Z`);

    // Calculate error rate (simplified)
    const errorRate = await calculateErrorRate();

    return {
      active_users: 0, // Would need session tracking
      daily_transactions: dailyTransactions || 0,
      voice_commands_processed: voiceCommands || 0,
      compliance_checks: complianceChecks || 0,
      error_rate: errorRate
    };
  } catch (error) {
    console.error('Failed to collect business metrics:', error);
    return {
      active_users: 0,
      daily_transactions: 0,
      voice_commands_processed: 0,
      compliance_checks: 0,
      error_rate: 0
    };
  }
}

async function getSystemHealthMetrics(): Promise<{
  database_connections: number;
  cache_hit_rate: number;
  api_success_rate: number;
}> {
  try {
    // Database connection check
    const dbConnections = await checkDatabaseConnections();
    
    return {
      database_connections: dbConnections,
      cache_hit_rate: 95, // Mock cache hit rate
      api_success_rate: 99.9 // Mock API success rate
    };
  } catch (error) {
    console.error('Failed to collect system health metrics:', error);
    return {
      database_connections: 0,
      cache_hit_rate: 0,
      api_success_rate: 0
    };
  }
}

async function checkDatabaseConnections(): Promise<number> {
  try {
    const { createClient } = await import('@supabase/supabase-js');
    const supabase = createClient(
      process.env.VITE_SUPABASE_URL!,
      process.env.VITE_SUPABASE_SERVICE_ROLE_KEY!
    );

    // Test connection
    const { error } = await supabase.from('inventory_events').select('count').limit(1);
    return error ? 0 : 1;
  } catch {
    return 0;
  }
}

async function calculateErrorRate(): Promise<number> {
  try {
    // In a real implementation, this would calculate error rate from logs
    // For now, return a mock low error rate
    return 0.1; // 0.1% error rate
  } catch {
    return 0;
  }
}

// Prometheus-style metrics format (optional alternative endpoint)
export async function prometheusMetrics(req: VercelRequest, res: VercelResponse) {
  try {
    const metrics = await collectMetrics();
    
    const prometheusFormat = `
# HELP seafood_manager_uptime Application uptime in seconds
# TYPE seafood_manager_uptime counter
seafood_manager_uptime ${metrics.application.uptime}

# HELP seafood_manager_memory_usage Memory usage in MB
# TYPE seafood_manager_memory_usage gauge
seafood_manager_memory_usage ${metrics.performance.memory_usage.used}

# HELP seafood_manager_daily_transactions Daily transaction count
# TYPE seafood_manager_daily_transactions counter
seafood_manager_daily_transactions ${metrics.business_metrics.daily_transactions}

# HELP seafood_manager_voice_commands Voice commands processed today
# TYPE seafood_manager_voice_commands counter
seafood_manager_voice_commands ${metrics.business_metrics.voice_commands_processed}

# HELP seafood_manager_compliance_checks HACCP compliance checks today
# TYPE seafood_manager_compliance_checks counter
seafood_manager_compliance_checks ${metrics.business_metrics.compliance_checks}

# HELP seafood_manager_error_rate Application error rate percentage
# TYPE seafood_manager_error_rate gauge
seafood_manager_error_rate ${metrics.business_metrics.error_rate}

# HELP seafood_manager_response_time_avg Average response time in milliseconds
# TYPE seafood_manager_response_time_avg gauge
seafood_manager_response_time_avg ${metrics.performance.response_times.avg}
    `.trim();

    res.setHeader('Content-Type', 'text/plain');
    return res.status(200).send(prometheusFormat);
  } catch (error) {
    return res.status(500).send('# Error collecting metrics');
  }
}