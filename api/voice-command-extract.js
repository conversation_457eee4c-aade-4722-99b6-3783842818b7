// Voice command extraction endpoint for structured seafood command processing
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Enhanced seafood command extraction prompt
const COMMAND_EXTRACTION_PROMPT = `You are a seafood inventory command processor. Extract structured data from voice commands for seafood operations.

SUPPORTED ACTIONS:
1. create_event (receiving, disposal, sale, physical_count)
2. query_inventory (check stock levels)
3. navigate (view different sections)

SEAFOOD PRODUCTS DATABASE:
- Finfish: Atlantic Salmon, Pacific King Salmon, Coho Salmon, Sockeye Salmon, Bluefin Tuna, Yellowfin Tuna, Atlantic Cod, Pacific Cod, Pacific Halibut, Dover Sole, Sea Bass, Red Snapper, Mahi Mahi
- Shellfish: Pacific Oysters, Eastern Oysters, Blue Point Oysters, Manila Clams, Littleneck Clams, Razor Clams, Blue Mussels, Sea Scallops, Bay Scallops
- Crustaceans: Maine Lobster, <PERSON><PERSON><PERSON> Crab, <PERSON>rab, <PERSON>, <PERSON>rab, Tiger Prawns, Spot Prawns, White Shrimp

COMMAND PATTERNS:
- Receiving: "Add receiving 50 pounds cod from Ocean Fresh" | "Received 25 kg salmon condition excellent"
- Disposal: "Dispose 10 pounds expired cod" | "Log disposal 5 cases damaged salmon"
- Sales: "Sale 30 pounds halibut to Restaurant ABC at $15 per pound"
- Physical Count: "Physical count 75 pounds cod in freezer"
- Inventory Query: "How much cod do we have?" | "What's our salmon inventory?"
- Navigation: "Show me recent events" | "View inventory dashboard"

EXTRACTION RULES:
1. Match seafood products to database (fuzzy matching allowed)
2. Extract quantities with units (lbs, kg, cases, units)
3. Identify vendors (from/by) and customers (to/for)
4. Detect conditions (excellent, good, fair, poor, damaged)
5. Extract prices ($X per unit, at $X, for $X)
6. Calculate confidence scores (0.0-1.0)

OUTPUT FORMAT (JSON):
{
  "action_type": "create_event|query_inventory|navigate",
  "event_type": "receiving|disposal|sale|physical_count",
  "product_name": "Standardized Product Name",
  "quantity": 50.0,
  "unit": "lbs|kg|cases|units",
  "vendor_name": "Vendor Name",
  "customer_name": "Customer Name", 
  "price": 15.50,
  "condition": "Excellent|Good|Fair|Poor|Damaged",
  "notes": "Additional context",
  "confidence_score": 0.85
}

Return ONLY valid JSON. Use exact product names from database. Apply voice corrections (e.g., "dangerous grab" → "Dungeness Crab").`;

export default async function handler(req, res) {
  // CORS headers
  res.setHeader('Access-Control-Allow-Origin', process.env.FRONTEND_URL || 'http://localhost:5177');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { command } = req.body;

    if (!command || typeof command !== 'string') {
      return res.status(400).json({ error: 'Command is required' });
    }

    const startTime = Date.now();

    // Apply common voice corrections before processing
    const voiceCorrections = {
      'dangerous grab': 'Dungeness Crab',
      'dangerous crab': 'Dungeness Crab',
      'king grab': 'King Crab',
      'dover soul': 'Dover Sole',
      'petrel sole': 'Petrale Sole',
      'blue point': 'Blue Point Oysters',
      'tiger prawns': 'Tiger Prawns',
      'spot prawns': 'Spot Prawns'
    };

    let correctedCommand = command.toLowerCase();
    Object.entries(voiceCorrections).forEach(([wrong, right]) => {
      correctedCommand = correctedCommand.replace(new RegExp(wrong, 'gi'), right);
    });

    // Process with OpenAI
    const completion = await openai.chat.completions.create({
      messages: [
        {
          role: 'system',
          content: COMMAND_EXTRACTION_PROMPT
        },
        {
          role: 'user',
          content: `Extract structured data from this seafood command: "${correctedCommand}"`
        }
      ],
      model: 'gpt-3.5-turbo',
      temperature: 0.1, // Low temperature for consistent extraction
      response_format: { type: 'json_object' }
    });

    const processingTime = Date.now() - startTime;
    const result = completion.choices[0]?.message?.content;

    if (!result) {
      throw new Error('No response from OpenAI');
    }

    let extractedData;
    try {
      extractedData = JSON.parse(result);
    } catch (parseError) {
      // Fallback to pattern-based extraction
      return res.json(fallbackExtraction(correctedCommand, processingTime));
    }

    // Add metadata
    const response = {
      ...extractedData,
      metadata: {
        processing_method: 'ai_extraction',
        original_command: command,
        corrected_command: correctedCommand,
        processing_time_ms: processingTime,
        voice_corrections_applied: Object.keys(voiceCorrections).some(wrong => 
          command.toLowerCase().includes(wrong)
        ),
        model: 'gpt-3.5-turbo',
        timestamp: new Date().toISOString()
      }
    };

    return res.json(response);

  } catch (error) {
    console.error('Command extraction error:', error);
    
    // Enhanced fallback processing
    if (req.body?.command) {
      return res.json(fallbackExtraction(req.body.command, 0, error.message));
    }

    return res.status(500).json({ 
      error: 'Command extraction failed',
      fallback: true,
      details: error.message
    });
  }
}

// Enhanced fallback extraction with pattern matching
function fallbackExtraction(command, processingTime = 0, errorMessage = null) {
  const lowerCommand = command.toLowerCase();
  
  // Initialize response structure
  const response = {
    action_type: 'unknown',
    confidence_score: 0.4,
    metadata: {
      processing_method: 'pattern_fallback',
      original_command: command,
      processing_time_ms: processingTime,
      error_reason: errorMessage,
      timestamp: new Date().toISOString()
    }
  };

  // Seafood product detection
  const seafoodTerms = {
    'cod': 'Atlantic Cod',
    'salmon': 'Atlantic Salmon',
    'halibut': 'Pacific Halibut',
    'tuna': 'Yellowfin Tuna',
    'dungeness': 'Dungeness Crab',
    'king crab': 'King Crab',
    'snow crab': 'Snow Crab',
    'blue crab': 'Blue Crab',
    'oysters': 'Pacific Oysters',
    'clams': 'Manila Clams',
    'mussels': 'Blue Mussels',
    'scallops': 'Sea Scallops',
    'shrimp': 'White Shrimp',
    'prawns': 'Tiger Prawns',
    'lobster': 'Maine Lobster'
  };

  // Find product
  for (const [term, productName] of Object.entries(seafoodTerms)) {
    if (lowerCommand.includes(term)) {
      response.product_name = productName;
      response.confidence_score += 0.2;
      break;
    }
  }

  // Extract quantity and unit
  const quantityMatch = lowerCommand.match(/(\d+(?:\.\d+)?)\s*(pounds?|lbs?|kg|kilograms?|cases?|units?)/);
  if (quantityMatch) {
    response.quantity = parseFloat(quantityMatch[1]);
    const unit = quantityMatch[2].toLowerCase();
    if (unit.includes('pound') || unit.includes('lb')) response.unit = 'lbs';
    else if (unit.includes('kg') || unit.includes('kilogram')) response.unit = 'kg';
    else if (unit.includes('case')) response.unit = 'cases';
    else response.unit = 'units';
    response.confidence_score += 0.2;
  }

  // Determine action type
  if (lowerCommand.includes('receive') || lowerCommand.includes('add') || lowerCommand.includes('receiving')) {
    response.action_type = 'create_event';
    response.event_type = 'receiving';
    response.confidence_score += 0.2;
    
    // Extract vendor
    const vendorMatch = lowerCommand.match(/from\s+([a-zA-Z\s&.-]+?)(?:\s+(?:at|for|condition|$))/);
    if (vendorMatch) {
      response.vendor_name = vendorMatch[1].trim();
      response.confidence_score += 0.1;
    }
  } else if (lowerCommand.includes('dispose') || lowerCommand.includes('disposal')) {
    response.action_type = 'create_event';
    response.event_type = 'disposal';
    response.confidence_score += 0.2;
  } else if (lowerCommand.includes('sale') || lowerCommand.includes('sold') || lowerCommand.includes('sell')) {
    response.action_type = 'create_event';
    response.event_type = 'sale';
    response.confidence_score += 0.2;
    
    // Extract customer
    const customerMatch = lowerCommand.match(/to\s+([a-zA-Z\s&.-]+?)(?:\s+(?:at|for|$))/);
    if (customerMatch) {
      response.customer_name = customerMatch[1].trim();
      response.confidence_score += 0.1;
    }
  } else if (lowerCommand.includes('count') || lowerCommand.includes('physical')) {
    response.action_type = 'create_event';
    response.event_type = 'physical_count';
    response.confidence_score += 0.2;
  } else if (lowerCommand.includes('how much') || lowerCommand.includes('inventory') || lowerCommand.includes('stock')) {
    response.action_type = 'query_inventory';
    response.confidence_score += 0.3;
  } else if (lowerCommand.includes('show') || lowerCommand.includes('view') || lowerCommand.includes('display')) {
    response.action_type = 'navigate';
    response.confidence_score += 0.2;
    
    if (lowerCommand.includes('event')) response.notes = 'Events';
    else if (lowerCommand.includes('inventory')) response.notes = 'Inventory';
    else if (lowerCommand.includes('dashboard')) response.notes = 'Dashboard';
  }

  // Extract price
  const priceMatch = lowerCommand.match(/\$?(\d+(?:\.\d{2})?)\s*(?:per\s+(?:pound|lb|unit|case))?/);
  if (priceMatch) {
    response.price = parseFloat(priceMatch[1]);
    response.confidence_score += 0.1;
  }

  // Extract condition
  const conditions = ['excellent', 'good', 'fair', 'poor', 'damaged'];
  for (const condition of conditions) {
    if (lowerCommand.includes(condition)) {
      response.condition = condition.charAt(0).toUpperCase() + condition.slice(1);
      response.confidence_score += 0.05;
      break;
    }
  }

  return response;
}