// Apply voice event migration manually
import { createClient } from '@supabase/supabase-js'
import { readFileSync } from 'fs'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

const supabaseUrl = process.env.VITE_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables')
  console.log('Need VITE_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function applyMigration() {
  console.log('🚀 Applying Voice Event Migration')
  console.log('==================================\n')

  try {
    // Read the migration file
    const migrationSQL = readFileSync('./supabase/migrations/20250815_001_voice_event_management_schema.sql', 'utf8')
    
    console.log('📄 Migration SQL loaded, length:', migrationSQL.length)
    
    // Execute the migration
    console.log('⚡ Executing migration...')
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: migrationSQL
    })
    
    if (error) {
      console.error('❌ Migration failed:', error)
      return
    }
    
    console.log('✅ Migration applied successfully!')
    
    // Test that the new columns exist
    console.log('\n🔍 Verifying migration...')
    const { data: testData, error: testError } = await supabase
      .from('inventory_events')
      .select('voice_confidence_score, created_by_voice')
      .limit(1)
    
    if (testError) {
      console.error('❌ Verification failed:', testError)
    } else {
      console.log('✅ Voice columns accessible after migration')
    }
    
  } catch (error) {
    console.error('💥 Error:', error)
  }
}

applyMigration().then(() => {
  console.log('\n🏁 Migration complete')
  process.exit(0)
}).catch(error => {
  console.error('\n💥 Migration failed:', error)
  process.exit(1)
})