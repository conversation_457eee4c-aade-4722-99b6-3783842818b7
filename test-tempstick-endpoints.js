#!/usr/bin/env node

/**
 * Test Different TempStick API Endpoint Formats
 */

import fetch from 'node-fetch';
import 'dotenv/config';

const API_KEY = process.env.VITE_TEMPSTICK_API_KEY;
const BASE_URL = 'https://tempstickapi.com/api/v1';

async function testEndpoint(endpoint, description) {
  const url = `${BASE_URL}${endpoint}`;
  console.log(`\n🧪 ${description}`);
  console.log(`🌐 ${url}`);
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'X-API-KEY': API_KEY,
        'Accept': 'application/json',
        'User-Agent': 'SeafoodManager/1.0'
      }
    });
    
    console.log(`📡 Status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Success! Keys:`, Object.keys(data));
      if (data.data && data.data.items) {
        console.log(`📊 Items count: ${data.data.items.length}`);
      }
    } else {
      console.log(`❌ Failed`);
    }
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  }
}

async function testReadingEndpoints() {
  console.log('🧪 Testing TempStick API Endpoint Formats...');
  
  const sensorId = '2550380'; // Downstairs Walk in Freezer
  
  // Test different endpoint formats
  await testEndpoint('/sensors/all', 'Get all sensors');
  await testEndpoint(`/sensor/${sensorId}`, 'Get single sensor');
  await testEndpoint(`/readings/${sensorId}/10`, 'Readings format 1');
  await testEndpoint(`/sensor/${sensorId}/readings`, 'Readings format 2');  
  await testEndpoint(`/sensor/${sensorId}/data`, 'Readings format 3');
  await testEndpoint(`/data/${sensorId}`, 'Readings format 4');
  await testEndpoint(`/sensors/${sensorId}/readings`, 'Readings format 5');
  
  // Test with query parameters
  await testEndpoint(`/readings?sensor_id=${sensorId}&limit=10`, 'Readings with query params');
}

testReadingEndpoints().catch(console.error);