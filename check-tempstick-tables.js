// Check TempStick Tables Status
import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

dotenv.config()

const supabaseUrl = process.env.VITE_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function checkTempStickTables() {
  console.log('🔍 Checking TempStick Database Tables Status')
  console.log('============================================\n')

  const tables = [
    'storage_areas',
    'sensors', 
    'temperature_readings',
    'temperature_alerts'
  ]

  const views = [
    'sensor_status_dashboard',
    'haccp_compliance_dashboard'
  ]

  // Check tables
  console.log('📊 Checking Tables:')
  for (const table of tables) {
    try {
      const { data, error } = await supabase.from(table).select('*').limit(1)
      if (error) {
        console.log(`   ❌ ${table}: ${error.message}`)
      } else {
        console.log(`   ✅ ${table}: Accessible`)
      }
    } catch (err) {
      console.log(`   ❌ ${table}: ${err.message}`)
    }
  }

  // Check views
  console.log('\n👀 Checking Views:')
  for (const view of views) {
    try {
      const { data, error } = await supabase.from(view).select('*').limit(1)
      if (error) {
        console.log(`   ❌ ${view}: ${error.message}`)
      } else {
        console.log(`   ✅ ${view}: Accessible`)
      }
    } catch (err) {
      console.log(`   ❌ ${view}: ${err.message}`)
    }
  }

  // Check sample data if tables exist
  console.log('\n📦 Checking Sample Data:')
  try {
    const { data: areas, error: areasError } = await supabase.from('storage_areas').select('*')
    const { data: sensors, error: sensorsError } = await supabase.from('sensors').select('*')
    
    if (!areasError && !sensorsError) {
      console.log(`   📦 Storage Areas: ${areas?.length || 0} records`)
      console.log(`   🌡️  Sensors: ${sensors?.length || 0} records`)
    } else {
      console.log('   ❌ Unable to check sample data (tables may not exist)')
    }
  } catch (err) {
    console.log('   ❌ Unable to check sample data:', err.message)
  }
}

checkTempStickTables().then(() => {
  console.log('\n🏁 Table check complete')
}).catch(err => {
  console.error('💥 Error:', err)
})