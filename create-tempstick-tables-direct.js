#!/usr/bin/env node

/**
 * Direct TempStick Table Creation Script
 * Executes SQL statements individually to avoid migration conflicts
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

dotenv.config()

const supabaseUrl = process.env.VITE_SUPABASE_URL
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing environment variables')
  console.error('   Need VITE_SUPABASE_URL and VITE_SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

// Use service role key for administrative access
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: { persistSession: false }
})

async function createTempStickTables() {
  console.log('🌡️  Testing TempStick Table Access')
  console.log('==================================')
  
  const tables = ['storage_areas', 'sensors', 'temperature_readings', 'temperature_alerts']
  let successCount = 0
  
  try {
    // Test table access first
    console.log('\n🔍 Testing table access...')
    for (const table of tables) {
      try {
        const { data, error } = await supabase.from(table).select('*').limit(1)
        if (error) {
          console.log(`   ❌ ${table}: ${error.message}`)
        } else {
          console.log(`   ✅ ${table}: Accessible (${data?.length || 0} rows)`)
          successCount++
        }
      } catch (err) {
        console.log(`   ❌ ${table}: ${err.message}`)
      }
    }
    
    console.log(`\n📊 Results: ${successCount}/${tables.length} tables accessible`)
    
    if (successCount === 0) {
      console.log('\n⚠️  No TempStick tables found')
      console.log('🔧 Manual creation required:')
      console.log('   1. Open Supabase dashboard')
      console.log('   2. Go to SQL Editor')
      console.log('   3. Execute tempstick-manual-sql.sql')
      console.log('   4. Or use the migration file: 20250827143811_create_tempstick_tables_manual.sql')
    } else if (successCount < tables.length) {
      console.log('\n⚠️  Some tables missing - check SQL for errors')
    } else {
      console.log('\n🎉 All TempStick tables are ready!')
      console.log('✅ Ready to test API connectivity')
    }
    
    return successCount === tables.length
    
  } catch (error) {
    console.error('❌ Script execution failed:', error)
    return false
  }
}

async function main() {
  const success = await createTempStickTables()
  
  if (success) {
    console.log('\n🚀 Next Steps:')
    console.log('   1. Test TempStick API connectivity')
    console.log('   2. Validate service layer operations') 
    console.log('   3. Test dashboard real-time updates')
  } else {
    console.log('\n📋 Required Action:')
    console.log('   • Execute SQL manually in Supabase dashboard')
    console.log('   • File: tempstick-manual-sql.sql')
  }
}

main().catch(console.error)