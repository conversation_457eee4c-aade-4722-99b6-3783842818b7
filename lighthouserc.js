// Lighthouse CI Configuration for Performance Monitoring
module.exports = {
  ci: {
    collect: {
      url: [
        'http://localhost:5177',
        'http://localhost:5177/inventory',
        'http://localhost:5177/products',
        'http://localhost:5177/dashboard',
        'http://localhost:5177/import',
        'http://localhost:5177/analytics'
      ],
      startServerCommand: 'npm run preview',
      startServerReadyPattern: 'Local:',
      startServerTimeout: 30000,
      numberOfRuns: 3,
      settings: {
        chromeFlags: '--no-sandbox --disable-dev-shm-usage'
      }
    },
    assert: {
      // Performance budgets based on Seafood Manager requirements
      assertions: {
        // Core Web Vitals
        'categories:performance': ['error', {minScore: 0.8}],
        'categories:accessibility': ['error', {minScore: 0.9}],
        'categories:best-practices': ['error', {minScore: 0.9}],
        'categories:seo': ['error', {minScore: 0.8}],
        
        // Performance metrics specific to seafood industry workflows
        'metrics:first-contentful-paint': ['error', {maxNumericValue: 2000}],
        'metrics:largest-contentful-paint': ['error', {maxNumericValue: 3000}],
        'metrics:cumulative-layout-shift': ['error', {maxNumericValue: 0.1}],
        'metrics:first-input-delay': ['error', {maxNumericValue: 100}],
        
        // Resource optimization
        'metrics:total-blocking-time': ['error', {maxNumericValue: 300}],
        'metrics:speed-index': ['error', {maxNumericValue: 3000}],
        
        // Network performance
        'metrics:server-response-time': ['error', {maxNumericValue: 500}],
        
        // Bundle size audits
        'audits:unused-javascript': ['error', {maxNumericValue: 500000}], // 500KB max unused JS
        'audits:modern-image-formats': 'error',
        'audits:uses-optimized-images': 'error',
        'audits:efficient-animated-content': 'error',
        
        // Critical rendering path
        'audits:render-blocking-resources': 'error',
        'audits:unused-css-rules': ['error', {maxNumericValue: 50000}], // 50KB max unused CSS
        
        // Seafood Manager specific performance requirements
        'audits:bootup-time': ['error', {maxNumericValue: 4000}], // Fast app initialization
        'audits:mainthread-work-breakdown': ['error', {maxNumericValue: 3000}], // Responsive UI
        
        // Progressive Web App features
        'audits:viewport': 'error',
        'audits:without-javascript': 'off', // React app requires JS
        
        // Security and best practices for food safety compliance
        'audits:is-on-https': 'error',
        'audits:uses-http2': 'warn',
        'audits:no-vulnerable-libraries': 'error'
      }
    },
    upload: {
      target: 'temporary-public-storage'
    }
  }
};