<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TempStick Direct API Test</title>
    <style>
        body {
            font-family: monospace;
            padding: 20px;
            background: #1a1a1a;
            color: #0f0;
        }
        button {
            background: #333;
            color: #0f0;
            border: 1px solid #0f0;
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #444;
        }
        pre {
            background: #000;
            padding: 10px;
            border: 1px solid #333;
            overflow-x: auto;
        }
        .error {
            color: #f00;
        }
        .success {
            color: #0f0;
        }
    </style>
</head>
<body>
    <h1>TempStick Direct API Test (No Proxy)</h1>
    <div>
        <button onclick="testEndpoint('https://tempstickapi.com/api/v1/sensors')">Test /api/v1/sensors</button>
        <button onclick="testEndpoint('https://tempstickapi.com/api/sensors')">Test /api/sensors</button>
        <button onclick="testEndpoint('https://tempstickapi.com/api/v1/sensor/all')">Test /api/v1/sensor/all</button>
        <button onclick="testEndpoint('https://tempstickapi.com/api/v1/account')">Test /api/v1/account</button>
        <button onclick="testEndpoint('https://tempstickapi.com/api/v1/devices')">Test /api/v1/devices</button>
    </div>
    <pre id="output"></pre>

    <script>
        const API_KEY = '03e99232a2794e2ea07fcd8f06ad356f35132396f02133c07a';
        
        async function testEndpoint(url) {
            const output = document.getElementById('output');
            output.innerHTML = `Testing: ${url}\n\n`;
            
            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${API_KEY}`,
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-API-KEY': API_KEY
                    }
                });
                
                const statusClass = response.ok ? 'success' : 'error';
                output.innerHTML += `<span class="${statusClass}">Status: ${response.status} ${response.statusText}</span>\n`;
                output.innerHTML += `Headers:\n${JSON.stringify(Object.fromEntries(response.headers), null, 2)}\n\n`;
                
                const text = await response.text();
                try {
                    const json = JSON.parse(text);
                    output.innerHTML += `Response:\n${JSON.stringify(json, null, 2)}`;
                } catch {
                    output.innerHTML += `Response (text):\n${text}`;
                }
            } catch (error) {
                output.innerHTML += `<span class="error">Error: ${error.message}</span>`;
            }
        }
    </script>
</body>
</html>
